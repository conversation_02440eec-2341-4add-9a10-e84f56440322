import { useEffect, useState } from 'react';
import AsyncSelect from 'react-select/async';
import useDebounceValue from '../../../hooks/useDebounceValue';

const customStyles = {
  option: (provided, state) => ({
    ...provided,
    display: 'flex',
    alignItems: 'center',
    padding: '12px 16px',
    position: 'relative',
    overflow: 'visible',
    backgroundColor: state.isSelected
      ? '#3b82f6'
      : state.isFocused
        ? '#f1f5f9'
        : 'white',
    color: state.isSelected ? 'white' : '#374151',
    borderRadius: '6px',
    margin: '2px 4px',
    cursor: 'pointer',
    transition: 'all 0.15s ease',
    '&:hover': {
      backgroundColor: state.isSelected ? '#3b82f6' : '#f1f5f9',
    },
  }),
  control: (provided, state) => ({
    ...provided,
    height: '100%',
    width: '100%',
    border: '1px solid #d1d5db',
    borderRadius: '8px',
    boxShadow: state.isFocused ? '0 0 0 2px rgba(59, 130, 246, 0.1)' : 'none',
    borderColor: state.isFocused ? '#3b82f6' : '#d1d5db',
    '&:hover': {
      borderColor: '#9ca3af',
    },
  }),
  menu: (provided) => ({
    ...provided,
    zIndex: 999,
    position: 'absolute',
    borderRadius: '10px',
    border: 'none',
    boxShadow:
      '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    overflow: 'hidden',
  }),
  menuList: (provided) => ({
    ...provided,
    padding: '6px',
    maxHeight: '250px',
  }),
  placeholder: (provided) => ({
    ...provided,
    color: '#9ca3af',
  }),
  dropdownIndicator: (provided, state) => ({
    ...provided,
    color: '#6b7280',
    transition: 'transform 0.2s ease',
    transform: state.selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0deg)',
  }),
  indicatorSeparator: () => ({
    display: 'none',
  }),
};

const AsyncSelector = ({
  placeholder = 'Select an option',
  onChange,
  selectedValue,
  fetchFxn,
  size = 'md', // 'sm', 'md', 'lg'
  // isAssembly = false,
}) => {
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [options, setOptions] = useState([]);
  const [val, setVal] = useState('');
  const [totalPages, setTotalPages] = useState(0);

  // Size configurations
  const sizeStyles = {
    sm: {
      control: { minHeight: '36px' },
      option: { padding: '8px 12px' },
      image: { width: '28px', height: '28px' },
      hoverImage: { width: '120px', height: '120px' },
      text: { fontSize: '14px' },
    },
    md: {
      control: { minHeight: '44px' },
      option: { padding: '12px 16px' },
      image: { width: '32px', height: '32px' },
      hoverImage: { width: '144px', height: '144px' },
      text: { fontSize: '16px' },
    },
    lg: {
      control: { minHeight: '52px' },
      option: { padding: '16px 20px' },
      image: { width: '40px', height: '40px' },
      hoverImage: { width: '160px', height: '160px' },
      text: { fontSize: '18px' },
    },
  };

  const currentSizeStyle = sizeStyles[size];

  const enhancedStyles = {
    ...customStyles,
    control: (provided, state) => ({
      ...customStyles.control(provided, state),
      ...currentSizeStyle.control,
    }),
    option: (provided, state) => ({
      ...customStyles.option(provided, state),
      ...currentSizeStyle.option,
    }),
  };

  const fetchOptions = async (inputValue) => {
    try {
      setIsLoading(true);
      const response = await fetchFxn({
        page,
        limit: 10,
        searchTerm: inputValue,
      }).unwrap();

      const fetchedOptions =
        response.results.map((option) => ({
          value: option._id,
          label: option.name + ' (' + option.workOrderId + ')',
          image: option.files?.[0]?.data || '',
          allImages: option.files?.map((file) => file.data) || [],
        })) || [];

      setTotalPages(response.totalPages);
      setIsLoading(false);

      return fetchedOptions;
    } catch (error) {
      setIsLoading(false);
      return [];
    }
  };

  const loadPaginatedOptions = async (inputValue, callback) => {
    if (isLoading) return; // Prevent duplicate fetches

    let fetchedOptions;

    if (inputValue) {
      setPage(1);
      fetchedOptions = await fetchOptions(inputValue);
      setOptions(fetchedOptions);
      //   setAllDropdownOptions(fetchedOptions);
    } else {
      if (page > totalPages) {
        return;
      }
      fetchedOptions = await fetchOptions('');
      setOptions((prevOptions) => [...prevOptions, ...fetchedOptions]);
      setPage((prev) => prev + 1);
      //   setAllDropdownOptions(options);
    }
    if (callback && typeof callback === 'function') {
      callback(fetchedOptions);
    }
  };

  const formatOptionLabel = ({ label, image }) => (
    <div className="flex items-center">
      {image && (
        <div className="group z-[9999]">
          <img
            src={image}
            alt={label}
            className="object-cover rounded-md mr-3 z-[9999]"
            style={currentSizeStyle.image}
          />

          <div className="absolute left-[4.5rem] transition-all duration-300 ease-in-out transform scale-0 opacity-0 group-hover:scale-110 group-hover:opacity-100 z-[9999] pointer-events-none">
            <img
              src={image}
              alt={label}
              className="object-cover rounded-lg shadow-xl z-[1000]"
              style={currentSizeStyle.hoverImage}
            />
          </div>
        </div>
      )}

      {!image && (
        <div className="bg-gray-200 rounded-md flex flex-col items-center justify-center transition-colors duration-200 overflow-hidden mr-3">
          <span
            className="text-gray-500 flex items-center justify-center font-medium"
            style={{
              ...currentSizeStyle.image,
              fontSize: `${parseInt(currentSizeStyle.image.width) * 0.4}px`,
            }}
          >
            {label?.slice(0, 1).toUpperCase()}
          </span>
        </div>
      )}
      <span className="text-gray-700" style={currentSizeStyle.text}>
        {label}
      </span>
    </div>
  );

  useEffect(() => {
    const loadInitialOptions = async () => {
      const initialOptions = await fetchOptions('');
      setOptions(initialOptions);
      setPage(1);
    };

    loadInitialOptions();
  }, []); // eslint-disable-line

  let debouncedVal = useDebounceValue(val, 500);

  useEffect(() => {
    loadPaginatedOptions(debouncedVal, () => {});
  }, [debouncedVal]); // eslint-disable-line

  return (
    <div className="relative">
      <AsyncSelect
        cacheOptions
        value={selectedValue}
        isLoading={isLoading}
        loadOptions={loadPaginatedOptions}
        onChange={(e) => {
          let files = options?.find(
            (item) => item?.value === e?.value
          )?.allImages;
          onChange(e, files);
        }}
        styles={enhancedStyles}
        formatOptionLabel={formatOptionLabel}
        placeholder={placeholder}
        onInputChange={(inputValue) => {
          setVal(inputValue);
        }}
        onMenuScrollToBottom={() => loadPaginatedOptions('', () => {})}
        defaultOptions={options?.filter(
          (item, index) => options?.indexOf(item) === index
        )}
      />
    </div>
  );
};

export default AsyncSelector;
