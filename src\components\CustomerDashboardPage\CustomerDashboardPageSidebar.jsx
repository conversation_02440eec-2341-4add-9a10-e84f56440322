import { mqtt5 } from 'aws-iot-device-sdk-v2';
import { useEffect, useState } from 'react';
import { IoLocationSharp } from 'react-icons/io5';
import { useOutletContext } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  convertMinsSecsStrToSecs,
  convertSecsToHrsAndMins,
  convertSecsToMinsAndSecs,
} from '../../helperFunction';
import { useResetMaintenanceMutation } from '../../slices/iotDeviceApiSlice';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import RightSidebar from '../global/components/RightSidebar';

const FIELDS = [
  'IDLECURRENTCT1',
  'IDLECURRENTCT2',
  'IDLECURRENTCT3',
  'ENGAGECURRENTCT1',
  'ENGAGECURRENTCT2',
  'ENGAGECURRENTCT3',
  'PRODUCTIONTIME',
];

function CustomerDashboardPageSidebar({
  openSideBar,
  setOpenSideBar,
  data,
  handleActions,
  setOpenMapSelector,
  mapLocation,
  setMapLocation,
}) {
  const [thresholds, setThresholds] = useState({
    maintenanceThreshold: '',
    dailyThreshold: '',
    costPerHour: '',
    overloadThreshold: '',
  });
  const [ctData, setCtData] = useState({
    IDLECURRENTCT1: '',
    IDLECURRENTCT2: '',
    IDLECURRENTCT3: '',
    ENGAGECURRENTCT1: '',
    ENGAGECURRENTCT2: '',
    ENGAGECURRENTCT3: '',
    PRODUCTIONTIME: '',
  });

  const { publishMessage, iotClient, iotDeviceData } = useOutletContext();

  const [resetMaintenance] = useResetMaintenanceMutation();

  useEffect(() => {
    setThresholds({
      maintenanceThreshold: data?.device?.maintenanceThreshold
        ? convertSecsToHrsAndMins(data?.device?.maintenanceThreshold)
        : '',
      dailyThreshold: data?.device?.dailyThreshold
        ? convertSecsToHrsAndMins(data?.device?.dailyThreshold)
        : '',
      costPerHour: data?.device?.costPerHour || '',
      overloadThreshold: data?.device?.overloadThreshold || '',
    });
    if (data?.device?.location?.lat && data?.device?.location?.lng)
      setMapLocation({
        lat: data?.device?.location?.lat,
        lng: data?.device?.location?.lng,
      });
  }, [
    data?.device?.maintenanceThreshold,
    data?.device?.dailyThreshold,
    data?.device?.costPerHour,
    data?.device?.overloadThreshold,
    data?.device?.location?.lat,
    data?.device?.location?.lng,
    setMapLocation,
  ]);

  useEffect(() => {
    if (data?.device?.deviceId && iotClient?.subscribe) {
      FIELDS.forEach((i) => {
        iotClient.subscribe({
          subscriptions: [
            {
              qos: mqtt5.QoS.AtLeastOnce,
              topicFilter: `/${data?.device?.deviceId}/${i}`,
            },
          ],
        });
      });
    }
  }, [data?.device?.deviceId, iotClient]);

  useEffect(() => {
    if (iotDeviceData && data?.device?.deviceId) {
      FIELDS?.forEach((field) => {
        const value = iotDeviceData?.[`/${data?.device?.deviceId}/${field}`];
        if (value)
          setCtData((prev) => ({
            ...prev,
            [field]:
              field === 'PRODUCTIONTIME'
                ? convertSecsToMinsAndSecs(+value)
                : value,
          }));
      });
    }
  }, [iotDeviceData, data?.device?.deviceId]);

  const inputChange = (e) => {
    const { name, value } = e.target;

    const check = value.split(':');

    const hasLetter = value.match(/[a-zA-Z]/);

    const isDaily = name === 'dailyThreshold';

    if (hasLetter) {
      toast.error('Cannot contain letter', {
        toastId: 'letter',
      });
      return;
    } else if (value?.length > 5 && isDaily) {
      toast.error('Invalid format, It should be HH:MM', {
        toastId: 'format',
      });
      return;
    } else if (+check[0] > 23 && isDaily) {
      toast.error('Hours cannot be greater than 23', { toastId: 'hour' });
      return;
    } else if (+check[1] > 59) {
      toast.error('Minutes cannot be greater than 59', { toastId: 'minute' });
      return;
    }
    setThresholds((prev) => ({ ...prev, [name]: value }));
  };

  const ctInputChange = (e) => {
    const { name, value } = e.target;

    const check = value.split(':');

    const hasLetter = value.match(/[a-zA-Z]/);

    if (hasLetter) {
      toast.error('Cannot contain letter', {
        toastId: 'letter',
      });
      return;
    } else if (+check[1] > 59) {
      toast.error('Seconds cannot be greater than 59', { toastId: 'seconds' });
      return;
    }

    setCtData((prev) => ({ ...prev, [name]: value }));
  };

  const sendData = () => {
    for (const i in ctData) {
      if (ctData?.[i]) {
        publishMessage(
          `/${data?.device?.deviceId}/${i}`,
          i === 'PRODUCTIONTIME'
            ? convertMinsSecsStrToSecs(ctData?.[i]).toString()
            : ctData?.[i],
          true
        );
      }
    }
  };

  const onResetMaintenance = async () => {
    const check = await customConfirm(
      `Are you sure you want to reset maintenace?`
    );
    if (!check) return;
    const res = await resetMaintenance({ id: data?.device?._id }).unwrap();
    if (res) {
      publishMessage(`/${data?.device?.deviceId}/MAINTENANCEHOURS`, '0', true);
      toast.success('Maintenance reset successful');
    }
  };

  return (
    <RightSidebar openSideBar={openSideBar} setOpenSideBar={setOpenSideBar}>
      <h3>{data?.machineName}</h3>

      <div className="grid grid-cols-2 mt-2 gap-3 ">
        <p className="my-auto col-span-2">Location</p>
        <div className="my-auto flex gap-5 col-span-2">
          <Input
            placeholder="lat"
            value={mapLocation?.lat}
            onChange={(e) =>
              setMapLocation((prev) => ({ ...prev, lat: +e.target.value }))
            }
          />
          <Input
            placeholder="long"
            value={mapLocation?.lng}
            onChange={(e) =>
              setMapLocation((prev) => ({ ...prev, lng: +e.target.value }))
            }
          />
          <button
            type="button"
            className="border border-gray-300 px-2 rounded-md flex items-center"
            onClick={() => setOpenMapSelector(true)}
          >
            <IoLocationSharp className="h-8 w-8" />
          </button>
        </div>
        <p className="my-auto">Device ID</p>
        <p className="my-auto">{data?.device?.deviceId}</p>
        <p className="my-auto">Daily Threshold</p>
        <Input
          name={'dailyThreshold'}
          value={thresholds?.dailyThreshold}
          onChange={inputChange}
        />
        <p className="my-auto">Maintenance Threshold</p>
        <Input
          name={'maintenanceThreshold'}
          value={thresholds?.maintenanceThreshold}
          onChange={inputChange}
        />
        <p className="my-auto">Overload Threshold</p>
        <Input
          type="number"
          name={'overloadThreshold'}
          value={thresholds?.overloadThreshold}
          onChange={inputChange}
        />
        <p className="my-auto">Cost Per Hour</p>
        <Input
          type="number"
          name={'costPerHour'}
          value={thresholds?.costPerHour}
          onChange={inputChange}
        />
      </div>

      <Button
        className={'w-full my-5'}
        onClick={() => handleActions(data?.device, thresholds, 'threshold')}
      >
        Update
      </Button>

      <div className="grid grid-cols-2 mt-2 gap-3 ">
        <p className="my-auto">Idle Current CT1</p>
        <Input
          type="number"
          name={'IDLECURRENTCT1'}
          value={ctData?.IDLECURRENTCT1}
          onChange={ctInputChange}
        />
        <p className="my-auto">Idle Current CT2</p>
        <Input
          type="number"
          name={'IDLECURRENTCT2'}
          value={ctData?.IDLECURRENTCT2}
          onChange={ctInputChange}
        />
        <p className="my-auto">Idle Current CT3</p>
        <Input
          type="number"
          name={'IDLECURRENTCT3'}
          value={ctData?.IDLECURRENTCT3}
          onChange={ctInputChange}
        />
        <p className="my-auto">Engage Current CT1</p>
        <Input
          type="number"
          name={'ENGAGECURRENTCT1'}
          value={ctData?.ENGAGECURRENTCT1}
          onChange={ctInputChange}
        />
        <p className="my-auto">Engage Current CT2</p>
        <Input
          type="number"
          name={'ENGAGECURRENTCT2'}
          value={ctData?.ENGAGECURRENTCT2}
          onChange={ctInputChange}
        />
        <p className="my-auto">Engage Current CT3</p>

        <Input
          type="number"
          name={'ENGAGECURRENTCT3'}
          value={ctData?.ENGAGECURRENTCT3}
          onChange={ctInputChange}
        />
        <p className="my-auto">Production Time</p>
        <Input
          name={'PRODUCTIONTIME'}
          value={ctData?.PRODUCTIONTIME}
          onChange={ctInputChange}
        />
      </div>

      <Button className={'w-full my-5'} onClick={sendData}>
        Set
      </Button>

      <Button className={'w-full my-5'} onClick={onResetMaintenance}>
        Reset Maintenance
      </Button>
    </RightSidebar>
  );
}

export default CustomerDashboardPageSidebar;
