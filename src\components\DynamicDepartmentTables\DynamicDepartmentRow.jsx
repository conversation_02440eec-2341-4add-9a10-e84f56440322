import { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { customConfirm } from '../../utils/customConfirm';

import { Dropdown } from 'antd';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Table from '../global/components/Table';
import Attachments from './Attachments';
import AudioAttachments from './AudioAttachments';
import FormManagementFillModal from './FormManagementFillModal';
import FormManagementModal from './FormManagementModal';

import { ArrowUpTrayIcon } from '@heroicons/react/24/outline';
import { FaLock } from 'react-icons/fa';
import { FaCircleCheck } from 'react-icons/fa6';
import { IoSaveOutline } from 'react-icons/io5';
import { MdDeleteOutline } from 'react-icons/md';

// import { Select, Tooltip } from 'antd';
import { getLocalDateTime } from '../../helperFunction';
import { useGetBomsForWoOptionsQuery } from '../../slices/assemblyBomApiSlice';
import { useGetAllAssetsForOptionsQuery } from '../../slices/assetApiSlice';
import { useGetPosForOptionsQuery } from '../../slices/createPoApiSlice';
import {
  useAddDepartmentRowMutation,
  useDeleteDepartmentRowMutation,
  useEditDepartmentRowMutation,
} from '../../slices/departmentRowApiSlice';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import LinkModal from './LinkModal';
import TableConfigModal from './TableConfigModal';

const DynamicDepartmentRow = ({
  columns,
  data,
  row,
  deleteRow,
  idx,
  setSteps,
  willFlash,
}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { data: posForOptions } = useGetPosForOptionsQuery();
  const [deleteDepRow] = useDeleteDepartmentRowMutation();

  const [rowData, setRowData] = useState(row);
  const [mediaModal, setMediaModal] = useState(false);
  const [openTableModals, setOpenTableModals] = useState([]);
  const [audioModal, setAudioModal] = useState(false);
  const [createFormModal, setCreateFormModal] = useState(false);
  const [fillFormModal, setFillFormModal] = useState(false);
  const [linkModal, setLinkModal] = useState(false);
  const [deletedMedia, setDeletedMedia] = useState([]);
  const [deletedAudio, setDeletedAudio] = useState([]);
  const [deletedForms, setDeletedForms] = useState([]);

  const { data: boms } = useGetBomsForWoOptionsQuery();
  const { data: assets } = useGetAllAssetsForOptionsQuery();
  const [addDepartmentRow] = useAddDepartmentRowMutation();
  const [editDepartmentRow] = useEditDepartmentRowMutation();

  const changeHandler = (column, value, isMedia) => {
    if (isMedia) {
      setRowData((prev) => ({
        ...prev,
        [column?.name]: {
          value: [...(prev?.[column?.name]?.value || []), value],
          type: column?.type,
        },
      }));
    } else {
      setRowData((prev) => ({
        ...prev,
        [column?.name]: {
          value,
          type: column?.type,
        },
      }));
    }
  };

  const handleView = (link, id) => {
    navigate(`${link}?id=${id}&depEdit=true&depPath=${location.pathname}`);
  };

  const handleWorkOrderView = ({ key }) => {
    handleView('/jobs/workorder', key);
  };
  const toggleModal = (index, state) => {
    setOpenTableModals((prev) => {
      const newModals = [...prev];
      newModals[index] = state;
      return newModals;
    });
  };

  const saveRow = async () => {
    for (const column of columns) {
      if (column?.isMandatory && !rowData?.[column?.name]?.value) {
        toast.error(`${column?.name} is required`);
        return;
      }
    }
    if (rowData?.isUpdate) {
      let id = rowData?.isUpdate;
      let transformedRowData = rowData;
      delete transformedRowData.isUpdate;
      for (let i of Object.keys(transformedRowData)) {
        if (transformedRowData?.[i]?.type === 'media') {
          let mediaArray = [];
          for (let j of transformedRowData?.[i]?.value) {
            if (j?.data) mediaArray?.push(j);
          }
          transformedRowData = {
            ...transformedRowData,
            [i]: {
              type: 'media',
              value: mediaArray,
            },
          };
        }
      }
      const editedRow = await editDepartmentRow({
        id,
        data: {
          row: transformedRowData,
          deletedMedia,
          deletedForms,
          deletedAudio,
        },
      }).unwrap();
      if (editedRow) {
        toast.success('Department Row Updated');
        const kanban = searchParams.get('kanban') === 'true';
        const orderId = searchParams.get('orderId');
        const navigateParams = {
          department: searchParams.get('department'),
          id: editedRow?._id,
          refType: searchParams.get('refType'),
          page: searchParams.get('page'),
          taskId: searchParams.get('taskId'),
          orderId,
          index: searchParams.get('index'),
        };

        const filteredParams = Object.fromEntries(
          Object.entries(navigateParams).filter(([_, value]) => value !== null)
        );

        const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;

        if (kanban) {
          navigate(navigateStr);
        }
      }
    } else {
      let row = {
        department: data?.department?._id,
        departmentNav: data?.departmentNav?._id,
        departmentChildNav: data?.departmentChildNav?._id,
        departmentColumn: data?._id,
        data: rowData,
      };
      const newRow = await addDepartmentRow({
        data: row,
      }).unwrap();
      if (newRow) {
        toast.success('Department Row Added');
        const kanban = searchParams.get('kanban') === 'true';
        const orderId = searchParams.get('orderId');
        const navigateParams = {
          department: searchParams.get('department'),
          id: newRow?._id,
          refType: searchParams.get('refType'),
          page: searchParams.get('page'),
          taskId: searchParams.get('taskId'),
          orderId,
          index: searchParams.get('index'),
        };

        const filteredParams = Object.fromEntries(
          Object.entries(navigateParams).filter(([_, value]) => value !== null)
        );

        const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;
        if (kanban) {
          navigate(navigateStr);
        }
      }
    }
  };

  const handleRowDelete = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to delete Row?',
      'delete'
    );
    if (!confirm) return;
    if (row?.isUpdate) {
      const res = await deleteDepRow({ id: row?.isUpdate }).unwrap();

      if (!res?.error) {
        toast.success('Deleted row successfully', { toastId: 'success' });
        deleteRow(row?.isUpdate, false);
      } else {
        toast.error(`${res?.error}`);
      }
    } else {
      deleteRow(idx, true);
      toast.success('Deleted row successfully', { toastId: 'success' });
    }
  };
  const dynamicDepartmentHandler = (column, index) => {
    let userId = JSON.parse(localStorage.getItem('user'))?.user?._id;
    let columnUsers = column?.users;
    let isVisible = true;
    if (column?.users?.length !== 0) {
      isVisible = columnUsers?.includes(userId);
    }
    const formCreateChangeHandler = (value) => {
      changeHandler(
        {
          ...column,
          type: `${column?.type}-${column?.formType}`,
        },
        value
      );
    };
    const formFillChangeHandler = (value) => {
      changeHandler(
        {
          ...column,
          type: `${column?.type}-${column?.formType}`,
        },
        value
      );
    };
    const linkChangeHandler = (val) => {
      changeHandler(column, val);
    };
    const audioChangeHandler = (value) => {
      changeHandler(column, value);
    };
    if (isVisible) {
      let items = [];
      let selectedItems = [];
      if (column?.type === 'work order') {
        let rowVal = rowData?.[column?.name]?.value;
        let ids = Array.isArray(rowVal)
          ? rowVal?.map((elem) => (elem?.value ? elem?.value : elem))
          : [];
        if (posForOptions) {
          for (let i of posForOptions) {
            if (ids?.includes(i?._id)) {
              selectedItems?.push({
                label: i?.name,
                value: i?._id,
              });
              items?.push({
                label: i?.name,
                key: i?._id,
              });
            }
          }
        }
      }
      switch (column?.type) {
        case 'table':
          return (
            <Table.Td>
              {openTableModals?.[index] && (
                <TableConfigModal
                  openModal={openTableModals?.[index]}
                  setOpenModal={(state) => toggleModal(index, state)}
                  tableConfig={column?.tableConfig}
                  changeHandler={changeHandler}
                  column={column}
                  rowData={rowData}
                />
              )}
              <div className="flex gap-x-2 items-center">
                <Button onClick={() => toggleModal(index, true)}>View</Button>
                <FaCircleCheck
                  className={`${rowData?.[column?.name]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
                />
              </div>
            </Table.Td>
          );
        case 'number':
          return (
            <Table.Td>
              <Input
                value={rowData?.[column?.name]?.value}
                type="number"
                placeholder="Enter a number"
                className="w-full min-w-[10rem]"
                inputClassname="!h-[30px]"
                onChange={(e) => {
                  changeHandler(column, e.target.value);
                }}
              />
            </Table.Td>
          );
        case 'string':
          return (
            <Table.Td>
              <Input
                value={rowData?.[column?.name]?.value}
                placeholder="Enter a string"
                className="w-full min-w-[10rem]"
                inputClassname="!h-[30px]"
                onChange={(e) => {
                  changeHandler(column, e.target.value);
                }}
              />
            </Table.Td>
          );
        case 'checkbox':
          return (
            <Table.Td>
              <Input
                checked={rowData?.[column?.name]?.value}
                type="checkbox"
                className="w-[1.3rem] m-auto"
                onChange={(e) => {
                  changeHandler(column, e.target.checked);
                }}
              />
            </Table.Td>
          );

        case 'select':
          return (
            <Table.Td>
              <Select
                className="!min-w-[10rem]"
                value={rowData?.[column?.name]?.value}
                onChange={(e) => {
                  changeHandler(column, e.target.value);
                }}
                options={column?.values?.map((elem) => ({
                  label: elem,
                  value: elem,
                }))}
              />
            </Table.Td>
          );
        case 'multiCheckbox':
          return (
            <Table.Td>
              <MultiSelect
                className="!min-w-[10rem]"
                value={rowData?.[column?.name]?.value}
                onChange={(e) => {
                  const updatedValues = e.target.value;
                  changeHandler(column, updatedValues);
                }}
                options={posForOptions
                  ?.map((elem) => ({
                    label: elem?.name,
                    value: elem?._id,
                  }))
                  ?.reverse()}
              />
            </Table.Td>
          );
        case 'dropdown':
          return (
            <Table.Td>
              <MultiSelect
                className="!min-w-[10rem]"
                value={rowData?.[column?.name]?.value || []}
                onChange={(e) => {
                  changeHandler(column, e.target.value);
                }}
                options={column?.values?.map((elem) => ({
                  label: elem,
                  value: elem,
                }))}
              />
            </Table.Td>
          );
        case 'media':
          return (
            <Table.Td>
              <Attachments
                mediaModal={mediaModal}
                setMediaModal={setMediaModal}
                changeHandler={changeHandler}
                column={column}
                rowData={rowData}
                setDeletedMedia={setDeletedMedia}
                deletedMedia={deletedMedia}
              />
              <div className="flex items-center justify-center gap-2 !min-w-[10rem]">
                <Button
                  className={`!w-fit`}
                  onClick={() => {
                    setMediaModal(true);
                  }}
                >
                  Add Media
                  <ArrowUpTrayIcon className="!w-[16px] !h-[16px]" />
                  {/* Add Media */}
                </Button>
                <FaCircleCheck
                  className={`${rowData?.[column?.name]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
                />
              </div>
            </Table.Td>
          );
        case 'bom':
          return (
            <Table.Td>
              <MultiSelect
                className="!min-w-[10rem]"
                value={rowData?.[column?.name]?.value || []}
                onChange={(e) => {
                  changeHandler(column, e.target.value);
                }}
                options={boms
                  ?.map((elem) => ({
                    label: elem?.name,
                    value: elem?._id,
                  }))
                  ?.reverse()}
              />
            </Table.Td>
          );
        case 'assets':
          return (
            <Table.Td>
              <MultiSelect
                className="!min-w-[10rem]"
                value={selectedItems}
                onChange={(e) => {
                  changeHandler(column, e.target.value);
                }}
                options={assets
                  ?.map((elem) => ({
                    label: elem?.name,
                    value: elem?._id,
                  }))
                  ?.reverse()}
              />
            </Table.Td>
          );
        case 'date':
          return (
            <Table.Td>
              <Input
                type="date"
                value={rowData?.[column?.name]?.value}
                className="w-full !min-w-[10rem]"
                inputClassname="!h-[30px]"
                onChange={(e) => {
                  changeHandler(column, e.target.value);
                }}
              />
            </Table.Td>
          );
        case 'forms':
          return (
            <Table.Td>
              <FormManagementFillModal
                open={fillFormModal}
                setOpen={setFillFormModal}
                changeHandler={formFillChangeHandler}
                value={rowData?.[column?.name]?.value}
                form={column?.form}
              />
              <div className="flex items-center justify-center gap-2 !min-w-[10rem]">
                <Button
                  className="min-w-[8rem]"
                  onClick={() => setFillFormModal(true)}
                >
                  Fill Form
                </Button>
                <FaCircleCheck
                  className={`${rowData?.[column?.name]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
                />
              </div>
            </Table.Td>
          );
        case 'form management':
          if (column?.formType === 'create') {
            return (
              <Table.Td>
                <FormManagementModal
                  formType="create"
                  open={createFormModal}
                  setOpen={setCreateFormModal}
                  changeHandler={formCreateChangeHandler}
                  column={column}
                  value={rowData?.[column?.name]?.value}
                  deletedForms={deletedForms}
                  setDeletedForms={setDeletedForms}
                />
                <div className="flex items-center justify-center gap-2 !min-w-[10rem]">
                  <Button
                    className="min-w-[8rem]"
                    onClick={() => setCreateFormModal(true)}
                  >
                    Create Form
                  </Button>
                  <FaCircleCheck
                    className={`${rowData?.[column?.name]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
                  />
                </div>
              </Table.Td>
            );
          } else {
            return (
              <Table.Td>
                <FormManagementFillModal
                  open={fillFormModal}
                  setOpen={setFillFormModal}
                  changeHandler={formFillChangeHandler}
                  value={rowData?.[column?.name]?.value}
                />
                <div className="flex items-center justify-center gap-2 !min-w-[10rem]">
                  <Button
                    className="min-w-[8rem]"
                    onClick={() => setFillFormModal(true)}
                  >
                    Fill Form
                  </Button>
                  <FaCircleCheck
                    className={`${rowData?.[column?.name]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
                  />
                </div>
              </Table.Td>
            );
          }
        case 'hyperlink':
          return (
            <Table.Td>
              <LinkModal
                open={linkModal}
                setOpen={setLinkModal}
                value={rowData?.[column?.name]?.value}
                changeHandler={linkChangeHandler}
              />
              <div className="flex items-center justify-center gap-2 !min-w-[10rem]">
                <Button
                  className="min-w-[8rem]"
                  onClick={() => setLinkModal(true)}
                >
                  Add Links
                </Button>
                <FaCircleCheck
                  className={`${rowData?.[column?.name]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
                />
              </div>
            </Table.Td>
          );
        case 'audio':
          return (
            <Table.Td>
              <AudioAttachments
                mediaModal={audioModal}
                setMediaModal={setAudioModal}
                changeHandler={audioChangeHandler}
                column={column}
                rowData={rowData}
                setDeletedMedia={setDeletedAudio}
                deletedMedia={deletedAudio}
              />
              <div className="flex items-center justify-center gap-2 !min-w-[10rem]">
                <Button
                  className="!min-w-[8rem]"
                  onClick={() => {
                    setAudioModal(true);
                  }}
                >
                  Add Audio
                </Button>
                <FaCircleCheck
                  className={`${rowData?.[column?.name]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'}  text-xl`}
                />
              </div>
            </Table.Td>
          );
        case 'work order':
          return (
            <Table.Td>
              <div className="w-full flex items-center gap-2">
                <MultiSelect
                  className="!min-w-[10rem]"
                  value={selectedItems}
                  onChange={(e) => {
                    changeHandler(column, e.target.value);
                  }}
                  options={posForOptions
                    ?.map((elem) => ({
                      label: elem?.name,
                      value: elem?._id,
                    }))
                    ?.reverse()}
                />
                {/* <Select
                  value={selectedItems}
                  isMulti
                  name="workOrders"
                  onChange={(e) => {
                    changeHandler(column, e.target.value);
                  }}
                  options={posForOptions
                    ?.map((elem) => ({
                      label: elem?.name,
                      value: elem?._id,
                    }))
                    ?.reverse()}
                  className="basic-multi-select"
                  classNamePrefix="select"
                /> */}
                {/* <Select
                  mode="multiple"
                  allowClear
                  listItemHeight={32} // Add this to control individual option height
                  listHeight={256} // Add this to control dropdown menu height
                  menuItemSelectedIcon={null}
                  popupClassName="custom-select-dropdown"
                  className="!min-w-[10rem]"
                  placeholder="Please select"
                  value={selectedItems}
                  onChange={(e) => {
                    changeHandler(column, e);
                  }}
                  options={posForOptions
                    ?.map((elem) => ({
                      label: elem?.name,
                      value: elem?._id,
                    }))
                    ?.reverse()}
                  maxTagCount={'responsive'}
                  maxTagPlaceholder={(omittedValues) => (
                    <Tooltip
                      overlayStyle={{
                        pointerEvents: 'none',
                      }}
                      title={omittedValues.map(({ label }) => label).join(', ')}
                    >
                      <span>+{omittedValues?.length}</span>
                    </Tooltip>
                  )}
                /> */}
                <Dropdown menu={{ items, onClick: handleWorkOrderView }}>
                  <Button
                    disabled={
                      rowData?.[column?.name]?.value?.length > 0 ? false : true
                    }
                  >
                    View
                  </Button>
                </Dropdown>
                {/* {rowData?.[column?.name]?.value?.length > 0 && (
                  <Button
                    onClick={()=>{
                      handleView()
                    }}
                  >
                    View
                  </Button>
                )} */}
              </div>
            </Table.Td>
          );
      }
    } else {
      return (
        <Table.Td className="!bg-slate-200">
          <FaLock className="text-xl text-slate-400 m-auto" />
        </Table.Td>
      );
    }
  };

  return (
    <>
      <Table.Td
        className={`${willFlash ? 'border-l-4 border-solid border-blue-500' : ''}`}
      >
        {idx + 1}
      </Table.Td>
      <Table.Td
        className="hover:cursor-pointer text-center hover:underline min-w-[50px] font-medium !text-blue-400"
        onClick={() => {
          if (row?.taskId?.taskId) {
            setSteps(row?.taskId?.taskId);
          } else {
            setSteps(searchParams?.get('taskID'));
          }
        }}
      >
        {idx === 0 && searchParams.get('taskID')
          ? searchParams.get('taskID')
          : row?.taskId?.customTaskId
            ? `${row?.taskId?.customTaskId}(${row?.taskId?.taskId})`
            : row?.taskId?.taskId || '-'}
      </Table.Td>
      <Table.Td className={'!w-32'}>
        {row?.createdAt ? (
          <div className="flex flex-col">
            <p>
              {getLocalDateTime(row?.createdAt).split(',')[0]} {/* Date */}
            </p>
            <p className="whitespace-nowrap">
              {getLocalDateTime(row?.createdAt).split(',')[1]} {/* Time */}
            </p>
          </div>
        ) : (
          <div className="flex flex-col">
            <p>
              {getLocalDateTime(Date.now()).split(',')[0]} {/* Date */}
            </p>
            <p className="whitespace-nowrap">
              {getLocalDateTime(Date.now()).split(',')[1]} {/* Time */}
            </p>
          </div>
        )}
      </Table.Td>
      {columns?.map((column, index) => {
        return <>{dynamicDepartmentHandler(column, index)}</>;
      })}
      <Table.Td>
        <IoSaveOutline
          className="text-2xl text-slate-500 font-semibold m-auto cursor-pointer"
          onClick={saveRow}
        />
      </Table.Td>
      <Table.Td>
        <MdDeleteOutline
          className="text-3xl text-red-500 font-semibold m-auto cursor-pointer"
          onClick={handleRowDelete}
        />
      </Table.Td>
    </>
  );
};

export default DynamicDepartmentRow;
