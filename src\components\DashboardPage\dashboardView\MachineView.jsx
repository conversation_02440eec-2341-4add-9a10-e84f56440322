import { useContext, useEffect, useState } from 'react';
import { useLazyGetDeviceDataForDashoardOEEQuery } from '../../../slices/deviceDataApiSlice';
import { Store } from '../../../store/Store';
import Layout from '../Layout';
import OEE from './progressBar/OEE';
import WorkerEfficiency from './progressBar/WorkerEfficiency';

const CuProjectTab = ({ cuProject, selectOptions, values, deviceDatas }) => {
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  return (
    <div className="w-full p-2 py-3 rounded-new bg-gray-primary mb-8 last:mb-0">
      <p className="text-[1.25rem] px-3 font-medium mb-4 flex justify-between">
        {`Batch ${cuProject?.batchInfo?.batchNo} :`}

        <span>{cuProject?.subProcessData?.process}</span>
      </p>
      <div className="w-full">
        {cuProject?.machineAndOperator?.map((mao) => {
          const machine = selectOptions?.project?.machines?.find(
            (mac) => mac._id === mao?.machine._id
          );

          const operator = mao?.operator?.findLast((op) => op)?.user;

          const lessThanTwoFields = [];
          const lessThanFourFields = [];

          const devices = machine?.devices?.map((device) => {
            const filtered = device?.deviceType?.fields?.filter((field) =>
              ['formula', 'value'].includes(field?.type)
            );
            const nosOfFields = filtered?.length;

            if (nosOfFields < 2) {
              if (lessThanTwoFields?.length < 2) {
                lessThanTwoFields.push({ ...device, nosOfFields });
              }
            } else if (nosOfFields < 4) {
              if (lessThanFourFields?.length < 1) {
                lessThanFourFields.push({ ...device, nosOfFields });
              }
            }

            return { ...device, nosOfFields };
          });

          let devicesToDisplayBefore = [];

          if (lessThanTwoFields?.length > 0) {
            devicesToDisplayBefore = lessThanTwoFields;
          } else if (lessThanFourFields?.length === 1) {
            devicesToDisplayBefore = lessThanFourFields;
          }

          const isActive = mao?.status === 'active';

          const lastValues =
            cuProject?.lastValues?.[mao?.machine?.machineId] || {};

          return (
            <div
              key={mao._id}
              className="w-full bg-white rounded-new mb-3 last:mb-0 px-2 py-3"
            >
              <p className="text-[0.94rem] font-medium mb-2 w-full flex items-center gap-x-2">
                {`${machine?.machineName}`}
                <span
                  className={`capitalize text-white px-2 rounded-full ${
                    machine?.status === 'active'
                      ? 'bg-green-primary'
                      : machine?.status === 'pause'
                      ? 'bg-yellow-500'
                      : 'bg-red-primary'
                  }`}
                >
                  {' '}
                  {machine?.status}
                </span>
                {/* <ColoredCircle status={machine?.status} /> */}
              </p>
              <div className="w-full grid grid-cols-4 gap-3">
                {/* first two blocks for devices */}
                {devicesToDisplayBefore?.map((device) => (
                  <Layout
                    key={device._id}
                    device={device}
                    values={isActive ? values : lastValues}
                  />
                ))}

                {/* workers details */}
                <div className="col-start-3 col-span-2 w-full text-[0.88rem] font-medium flex flex-col">
                  <section className="grid grid-cols-2 gap-x-2">
                    <section className="relative w-full h-full">
                      <section className="w-1/2 max-w-[60px] pt-1.5 mx-auto">
                        <WorkerEfficiency
                          mao={mao}
                          cuProject={cuProject}
                          defaultParam={defaultParam}
                          selectOptions={selectOptions}
                          deviceDatas={deviceDatas}
                          values={isActive ? values : lastValues}
                        />
                      </section>
                      <p className="text-center mt-1">Worker Efficiency</p>
                      <p className="absolute bottom-full left-1/2 -translate-x-1/2 text-center w-full h-fit">
                        Worker Id :{' '}
                        <span className="text-blue-primary">
                          {operator?.employeeId}
                        </span>
                      </p>
                    </section>
                    <section className="relative w-full h-full">
                      <section className="w-1/2 max-w-[60px] pt-1.5 mx-auto">
                        <OEE
                          mao={mao}
                          defaultParam={defaultParam}
                          selectOptions={selectOptions}
                          deviceDatas={deviceDatas}
                          values={isActive ? values : lastValues}
                        />
                      </section>
                      <p className="text-center mt-1">OEE</p>
                      <p className="absolute bottom-full left-1/2 -translate-x-1/2 text-center w-full h-fit">
                        Name :{' '}
                        <span className="text-blue-primary">
                          {operator?.name}
                        </span>
                      </p>
                    </section>
                  </section>
                </div>

                {/* return remaining devices */}
                {devices?.map((device) => {
                  // if 2 devices with fields less than equal to 2 then return null for matched device id
                  const exists1 = lessThanTwoFields.find(
                    (item) => item._id === device._id
                  );
                  if (lessThanTwoFields?.length > 0 && exists1) return null;

                  // if 1 device with fields greater than 2 and less than equal to 4 then return null for matched device id
                  const exists2 = lessThanFourFields.find(
                    (item) => item._id === device._id
                  );
                  if (lessThanFourFields?.length === 1 && exists2) return null;

                  return (
                    <Layout
                      key={device._id}
                      device={device}
                      values={isActive ? values : lastValues}
                    />
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const MachineView = ({ selectOptions, values, searchOptions }) => {
  const [searchResult, setSearchResult] = useState([]);
  const [deviceDatas, setDeviceDatas] = useState([]);

  const [getDeviceDataForDashoardOEE] =
    useLazyGetDeviceDataForDashoardOEEQuery();

  useEffect(() => {
    if (selectOptions?.cuProjects?.length > 0) {
      let devices = [];
      selectOptions?.cuProjects?.forEach((cuProject) => {
        cuProject?.machineAndOperator?.forEach((mao) => {
          devices.push(...(mao?.machine?.devices.map((i) => i._id) || []));
        });
      });

      const start = new Date(new Date().setHours(0, 0, 0, 0)).toISOString();

      (async () => {
        const res = await getDeviceDataForDashoardOEE(
          {
            query: {
              devices: devices?.join(','),
              start,
            },
          },
          false
        ).unwrap();

        if (res) {
          setDeviceDatas(res?.deviceDatas);
        }
      })();
    }
  }, [selectOptions?.cuProjects, getDeviceDataForDashoardOEE]);

  useEffect(() => {
    if (searchOptions && selectOptions?.cuProjects) {
      const { search, option } = searchOptions;
      const { cuProjects } = selectOptions;
      if (search) {
        // search by batch
        if (option === 'batch') {
          const temp = cuProjects.filter((cuProject) =>
            `batch ${cuProject.batchInfo.batchNo}`.includes(
              search?.toLowerCase()
            )
          );

          setSearchResult(temp);
        }

        // serach by machine
        if (option === 'machine') {
          const temp = cuProjects.filter((cuProject) => {
            let check = false;
            cuProject?.machineAndOperator?.forEach((mao) => {
              check = mao?.machine?.machineName
                ?.toLowerCase()
                ?.includes(search?.toLowerCase());
            });
            return check;
          });
          setSearchResult(temp);
        }

        // serach by worker
        if (option === 'worker') {
          const temp = cuProjects.filter((cuProject) => {
            let check = false;
            cuProject?.machineAndOperator?.forEach((mao) => {
              mao?.operator?.forEach((optr) => {
                check = optr?.user?.name
                  ?.toLowerCase()
                  ?.includes(search?.toLowerCase());
              });
            });
            return check;
          });
          setSearchResult(temp);
        }
      } else {
        setSearchResult(cuProjects);
      }
    }
  }, [selectOptions, searchOptions]);

  return (
    <div className="mt-5 w-full rounded-new py-3 px-5">
      <p className="font-bold mb-3">{selectOptions?.mqtt?.process}</p>
      {searchResult?.length > 0 ? (
        <>
          {searchResult?.map((cuProject) => (
            <CuProjectTab
              key={cuProject._id}
              cuProject={cuProject}
              selectOptions={selectOptions}
              values={values}
              deviceDatas={deviceDatas}
            />
          ))}
        </>
      ) : (
        <p className="text-center">No results found</p>
      )}
    </div>
  );
};
export default MachineView;
