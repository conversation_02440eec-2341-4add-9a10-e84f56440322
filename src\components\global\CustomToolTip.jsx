import { Tooltip as ReactTooltip } from 'react-tooltip';

const CustomToolTip = ({
  tooltipId,
  content,
  place = 'top',
  effect = 'solid',
  className = '',
  children,
  ...rest
}) => {
  const { title, ...filteredRest } = rest; // eslint-disable-line

  return (
    <div className="relative inline-block">
      <div
        data-tooltip-id={tooltipId}
        data-tooltip-content={content}
        className="cursor-pointer"
        title=""
      >
        {children}
      </div>
      <ReactTooltip
        id={tooltipId}
        place={place}
        effect={effect}
        className={`bg-black !text-[12px] text-white !px-[10px]  !py-[5px] rounded shadow-md ${className} !z-[99999]`}
        {...filteredRest}
      />
    </div>
  );
};

export default CustomToolTip;
