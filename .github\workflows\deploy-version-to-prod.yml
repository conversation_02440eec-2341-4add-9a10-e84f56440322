name: Upload the version build to prod server
on:
  push:
    branches:
      - 'main-*'
jobs:
  build-and-upload:
    runs-on: ubuntu-22.04
    env:
      PRIVATE_KEY: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
      HOST1: ${{ secrets.HOST1 }}
      HOST2: ${{ secrets.HOST2 }}
      HOST3: ${{ secrets.HOST3 }}
      HOST4: ${{ secrets.HOST4 }}
      HOST5: ${{ secrets.HOST5 }}
      USER_NAME: ${{ secrets.USER_NAME }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ github.ref_name }}-prod-optiwise-npm-cache-${{ hashFiles('**/package-lock.json') }}
      - name: Install dependencies
        run: npm ci
      - name: Build Code
        run: npm run build
      - name: Compress dist
        run: zip -r ${{ github.ref_name }}.zip dist/
      - name: Generate key
        run: echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
      - name: Upload files
        run: |
          scp -o StrictHostKeyChecking=no -i private_key ./${{ github.ref_name }}.zip ${USER_NAME}@${HOST1}:
          echo "Uploaded on instance 1"
          scp -o StrictHostKeyChecking=no -i private_key ./${{ github.ref_name }}.zip ${USER_NAME}@${HOST2}:
          echo "Uploaded on instance 2"
          scp -o StrictHostKeyChecking=no -i private_key ./${{ github.ref_name }}.zip ${USER_NAME}@${HOST3}:
          echo "Uploaded on instance 3"
          scp -o StrictHostKeyChecking=no -i private_key ./${{ github.ref_name }}.zip ${USER_NAME}@${HOST4}:
          echo "Uploaded on instance 4"
          scp -o StrictHostKeyChecking=no -i private_key ./${{ github.ref_name }}.zip ${USER_NAME}@${HOST5}:
          echo "Uploaded on instance 5"
      - name: Replace file on ec2
        run: |
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST1} '
              # Now we have got the access of EC2 and we will start the deploy .
              sudo mkdir -p code/versions/${{ github.ref_name }} &&
              cd code/versions/${{ github.ref_name }} &&
              sudo rm -rf dist/ &&
              cd /home/<USER>
              sudo unzip ${{ github.ref_name }}.zip &&
              sudo mv dist/ code/versions/${{ github.ref_name }} &&
              sudo rm ${{ github.ref_name }}.zip
              '
          echo "Replaced on instance 1"
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST2} '
              # Now we have got the access of EC2 and we will start the deploy .
              sudo mkdir -p code/versions/${{ github.ref_name }} &&
              cd code/versions/${{ github.ref_name }} &&
              sudo rm -rf dist/ &&
              cd /home/<USER>
              sudo unzip ${{ github.ref_name }}.zip &&
              sudo mv dist/ code/versions/${{ github.ref_name }} &&
              sudo rm ${{ github.ref_name }}.zip
              '
          echo "Replaced on instance 2"
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST3} '
              # Now we have got the access of EC2 and we will start the deploy .
              sudo mkdir -p code/versions/${{ github.ref_name }} &&
              cd code/versions/${{ github.ref_name }} &&
              sudo rm -rf dist/ &&
              cd /home/<USER>
              sudo unzip ${{ github.ref_name }}.zip &&
              sudo mv dist/ code/versions/${{ github.ref_name }} &&
              sudo rm ${{ github.ref_name }}.zip
              '
          echo "Replaced on instance 3"
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST4} '
              # Now we have got the access of EC2 and we will start the deploy .
              sudo mkdir -p code/versions/${{ github.ref_name }} &&
              cd code/versions/${{ github.ref_name }} &&
              sudo rm -rf dist/ &&
              cd /home/<USER>
              sudo unzip ${{ github.ref_name }}.zip &&
              sudo mv dist/ code/versions/${{ github.ref_name }} &&
              sudo rm ${{ github.ref_name }}.zip
              '
          echo "Replaced on instance 4"
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOST5} '
              # Now we have got the access of EC2 and we will start the deploy .
              sudo mkdir -p code/versions/${{ github.ref_name }} &&
              cd code/versions/${{ github.ref_name }} &&
              sudo rm -rf dist/ &&
              cd /home/<USER>
              sudo unzip ${{ github.ref_name }}.zip &&
              sudo mv dist/ code/versions/${{ github.ref_name }} &&
              sudo rm ${{ github.ref_name }}.zip
              '
          echo "Replaced on instance 5"
      - name: Remove key
        run: rm private_key
