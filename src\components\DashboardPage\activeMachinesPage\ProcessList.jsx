const ProcessList = ({ projects, setSelectOptions }) => {
  return (
    <div className="w-full">
      <p className="font-bold text-[1.25rem] mb-3">Active Rooms</p>
      {projects?.map((project) => {
        return (
          <div
            key={project._id}
            onClick={() =>
              setSelectOptions({
                project: project._id,
                process: project.projectId,
              })
            }
            className="w-full aspect-[65/24] border-l-4 border-red-primary flex gap-x-3 px-4 items-center bg-white rounded-new mb-3 last:mb-0 cursor-pointer"
          >
            <p className="text-[1.13rem] font-bold mb-1">
              {project?.projectId?.process}
            </p>
            <section className="w-2/5  aspect-square"></section>
          </div>
        );
      })}
    </div>
  );
};

export default ProcessList;
