import {
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';

const Pagination = ({
  page = 1,
  limit = 1,
  totalPages = 0,
  totalResults = 0,
  setPage,
  setLimit,
  setCsvData,
}) => {
  const [inputPage, setInputPage] = useState(page);

  useEffect(() => {
    setInputPage(page);
  }, [page]);

  const handleLimitChange = (event) => {
    const newLimit = parseInt(event.target.value, 10);
    setLimit(newLimit);
    setPage(1);
  };

  const handleInputPageChange = (event) => {
    const inputValue = event.target.value;
    setInputPage(inputValue);
  };

  const handlePageSubmit = () => {
    const newPage = parseInt(inputPage, 10);
    if (!isNaN(newPage) && newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    } else {
      setInputPage(page);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handlePageSubmit();
    }
  };

  const goToPage = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
      if (setCsvData) {
        setCsvData([]);
      }
    }
  };

  return (
    <div className="w-full py-6 border-b border-x rounded-b-xl flex flex-col md:flex-row justify-between items-center bg-white px-4 text-gray-700 text-sm">
      <div className="flex items-center gap-x-3 mb-4 md:mb-0">
        <button
          className={`p-2 rounded-full transition-colors ${
            page === 1 || totalResults === 0
              ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
          onClick={() => goToPage(1)}
          disabled={page === 1 || totalResults === 0}
        >
          <ChevronDoubleLeftIcon className="h-4 w-4" />
        </button>
        <button
          className={`p-2 rounded-full transition-colors ${
            page === 1 || totalResults === 0
              ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
          onClick={() => goToPage(page - 1)}
          disabled={page === 1 || totalResults === 0}
        >
          <ChevronLeftIcon className="h-4 w-4" />
        </button>
        <div className="flex items-center">
          <input
            type="text"
            value={inputPage}
            onChange={handleInputPageChange}
            onKeyDown={handleKeyPress}
            onBlur={handlePageSubmit}
            className="w-12 px-2 py-1 text-center border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <span className="ml-2">of {totalPages}</span>
        </div>
        <button
          className={`p-2 rounded-full transition-colors ${
            page === totalPages || totalResults === 0
              ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
          onClick={() => goToPage(page + 1)}
          disabled={page === totalPages || totalResults === 0}
        >
          <ChevronRightIcon className="h-4 w-4" />
        </button>
        <button
          className={`p-2 rounded-full transition-colors ${
            page === totalPages || totalResults === 0
              ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
          onClick={() => goToPage(totalPages)}
          disabled={page === totalPages || totalResults === 0}
        >
          <ChevronDoubleRightIcon className="h-4 w-4" />
        </button>
      </div>
      <div className="flex items-center gap-x-4">
        <div className="flex items-center">
          <label htmlFor="limit" className="mr-2">
            Items per page:
          </label>
          <select
            id="limit"
            value={limit}
            onChange={handleLimitChange}
            className="bg-white border rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
        </div>
        <div className="text-gray-600">
          Showing {(page - 1) * limit + 1} -{' '}
          {Math.min(page * limit, totalResults)} of {totalResults} items
        </div>
      </div>
    </div>
  );
};

export default Pagination;
