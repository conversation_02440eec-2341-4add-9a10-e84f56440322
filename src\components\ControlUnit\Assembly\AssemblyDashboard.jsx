import { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ReactComponent as Preview } from '../../../assets/svgs/preview.svg';
import { calculate } from '../../../calculateString';
import {
  useLazyGetCuProjectByIdQuery,
  useUpdateCuProjectAssemblyMutation,
  useUpdateProcessGoalDataMutation,
} from '../../../slices/CuProjectNewApiSlice';
import { apiSlice } from '../../../slices/apiSlice';
import { useEditWorkOrderMutation } from '../../../slices/createPoApiSlice';
import { useUpdateCuNotificationMutation } from '../../../slices/cuNotificationApiSlice';
import { useEditGoalsTableMutation } from '../../../slices/goalsTableapiSlice';
import { useCreateNotificationMutation } from '../../../slices/notificationApiSlice';
import { Store } from '../../../store/Store';
import { DEFAULT_MULTIPLIER_VALUE } from '../../../utils/Constant';
import {
  updateNextProcesses,
  updateWoItemProgress,
} from '../../../utils/updateNextProcess';
import { useUpdateWoItemProgressMutation } from '../../../slices/woItemProgressApiSlice';
import PreviewPopUp from '../../PreviewPop';
import Button from '../../global/components/Button';
import PdfViewer from '../../global/components/PdfViewer';
import AdditionalFieldRender from '../AdditionalFieldRender';
import DragAndDrop from '../DragAndDrop';
import ActionPopup from '../Inhouse/ActionPopup';
import PopupWrapper from '../PopupWrapper';
import Steps from './Steps';

const AssemblyDashboard = ({
  activeTile,
  cuprocess,
  allEmployees,
  woId,
  getAllPo,
  itemForJob,
}) => {
  const dispatch = useDispatch();

  const [formDetail, setFormDetail] = useState({});
  const [stepDetails, setStepDetail] = useState([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [activeForm, setActiveForm] = useState({});
  const [formData, setFormData] = useState({});
  const [isOpen, setIsOpen] = useState(false);
  const [isStop, setIsStop] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [referenceData, setReferenceData] = useState([]);
  const [cuProject, setCuProject] = useState({});
  const [samplingBatchSize, setSamplingBatchSize] = useState('');
  const [locationName, setLocationName] = useState('');
  const [pdfisOpen, setpdfIsOpen] = useState(false);
  const [unit, setUnit] = useState(1);
  const [selectedEmployee, setSelectedEmployee] = useState('');

  const [updateProgress] = useUpdateWoItemProgressMutation();
  const [updateCuProjectAssembly] = useUpdateCuProjectAssemblyMutation();
  const [getCuProjectById] = useLazyGetCuProjectByIdQuery();
  const [updateCuNotification] = useUpdateCuNotificationMutation();
  const [editGoalsTable] = useEditGoalsTableMutation();
  const [createNotification] = useCreateNotificationMutation();
  const [editPo] = useEditWorkOrderMutation();
  const [updateProcessGoalData] = useUpdateProcessGoalDataMutation();

  const navigate = useNavigate();

  const {
    defaults,
    mqttClient,
    refreshParams: { refreshUrl },
  } = useContext(Store);

  const defaultNoti = defaults?.defaultParam?.notificationDefaults;

  useEffect(() => {
    if (activeTile) {
      (async () => {
        const res = await getCuProjectById({ id: activeTile }).unwrap();
        if (res) {
          setCuProject(res?.cuProject);
        }
      })();
    }
  }, [getCuProjectById, refresh, activeTile]);

  const poId = cuProject?.project?.createPo;

  const prevEmployee = allEmployees?.find(
    (user) => user._id === cuProject?.assemblyData?.operator
  );

  useEffect(() => {
    if (cuProject?._id) {
      const currentIndexQC = cuprocess?.findIndex(
        (item) => item?._id === cuProject?.flowId
      );
      const currentProcess = cuprocess?.[currentIndexQC]?.mqtt;
      const linkedForms = cuProject?.project?.linkedAssemblyForms;
      const currForm = linkedForms?.find(
        (item) => item.processId?._id === currentProcess
      );

      let recievedFormData = cuProject?.assemblyData?.formData || {};

      let currUnit = Object.keys(recievedFormData)?.length || 1;

      if (
        currForm?.formId?.steps?.length === recievedFormData?.[currUnit]?.length
      ) {
        setReferenceData([]);

        setUnit(currUnit + 1);
      } else {
        setUnit(currUnit);
        setReferenceData(recievedFormData?.[currUnit] || []);
      }
      //Setting up formDetails for further use
      setFormDetail(currForm);
      setStepDetail(currForm?.formId?.steps);
    }
  }, [cuProject]); // eslint-disable-line

  useEffect(() => {
    setActiveIndex(referenceData?.length || 0);
    setActiveForm(formDetail?.formId?.steps[referenceData?.length || 0]);
  }, [activeTile, formDetail?.formId, referenceData]);

  useEffect(() => {
    setFormData({});
    setActiveForm(formDetail?.formId?.steps[activeIndex]);
  }, [activeIndex]); // eslint-disable-line

  const handleChange = (e) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      value: value,
      stepName: name,
    }));
  };

  const changeHandler = (e, fieldName) => {
    for (let i in e) {
      const fr = new FileReader();
      if (i === 'length') return;
      let name = e[i].name;
      let type = e[i].type;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;

        setFormData((prev) => ({
          ...prev,
          media: [
            ...(prev?.[fieldName]?.media || []),
            {
              name: name,
              type: type,
              data: url,
            },
          ],
        }));
      });
    }
  };

  const removeImgHandler = (url) => {
    setFormData((prev) => ({
      ...prev,
      media: prev?.media?.filter((item) => item.data !== url),
    }));
  };

  const handleStepDone = async () => {
    if (
      activeForm?.isMediaMandatory &&
      (formData?.media?.length === 0 ||
        JSON.stringify(formData) === '{}' ||
        !formData?.media)
    ) {
      toast.error('Please add media to proceed!');
      return;
    }
    let finalformData = {
      ...formData,
      isComplete: true,
      stepNo: Number(activeIndex) + 1,
      stepName: activeForm?.stepName,
      stepSubmissionTime: Date.now(),
    };

    let fData = {
      id: activeTile,
      formData: finalformData,
      unit: unit,
    };

    const update = await updateCuProjectAssembly({ data: fData }).unwrap();

    if (update) {
      setFormData({});
      setRefresh((prev) => !prev);
      toast.success('Added Successfully');
      // Update acitivity time for respective workOrder
      await editPo({
        id: woId,
        data: { latestActivityTime: Date.now(), activity: true },
      }).unwrap();
    }
  };

  const handleStopSubmit = async () => {
    const fData = {
      date: Date.now(),
      id: activeTile,
      stopLocation: locationName,
      samplingBatchSize: samplingBatchSize,
    };

    let tempData = {};

    // if (selectedEmployee === '') {
    //   toast.error(`Select operator to stop!`);
    //   return;
    // }

    if (samplingBatchSize === '') {
      toast.error('Batch size cannot be empty!');
      return;
    } else if (
      Number(samplingBatchSize) <=
      (+cuProject?.batchInfo?.['newBatchSize'] ||
        Number(cuProject?.batchInfo['Batch Size']))
    ) {
      tempData = {
        ['Batch Size']: samplingBatchSize,
      };
    } else {
      toast.error(
        `Entered batch size cannot surpass the current batch size (${
          +cuProject?.batchInfo?.['newBatchSize'] ||
          +cuProject?.batchInfo['Batch Size']
        })`
      );
      return;
    }

    try {
      const update = await updateCuProjectAssembly({
        data: fData,
        type: 'stop',
      }).unwrap();
      if (update) {
        // Update acitivity time for respective workOrder
        await editPo({
          id: woId,
          data: { latestActivityTime: Date.now(), activity: true },
        }).unwrap();

        let nextProcess = null;
        const flowid = cuProject?.flowId;
        const processArray = cuProject?.productionFlow?.processes;
        const currentIndex = processArray?.findIndex(
          (item) => item?._id === flowid
        );

        if (currentIndex === processArray?.length - 1) {
          nextProcess = null;
        } else if (
          processArray?.[currentIndex + 1]?.processCategory === 'Outsource'
        ) {
          nextProcess = null;
        } else {
          nextProcess = processArray?.[currentIndex + 1]?.mqtt;
        }

        if (nextProcess) {
          const finaldata = {
            batchNo: cuProject.batchInfo.batchNo,
            createInputId: cuProject.project._id,
            index: currentIndex,
            action: 'stop',
          };

          await updateCuNotification({ data: finaldata }).unwrap();
        }

        if (defaultNoti?.Stop) {
          const response = await createNotification({
            data: {
              createdAt: new Date(),
              cuProject: cuProject?._id,
              type: 'stop',
            },
          }).unwrap();

          if (response) {
            if (mqttClient?.publish)
              mqttClient?.publish(refreshUrl, `ACTION_CHANGE`);
          }
        }

        if (
          processArray[currentIndex + 1]?.processCategory === 'Outsource' &&
          currentIndex !== processArray.length - 1
        ) {
          const response = await createNotification({
            data: {
              createdAt: new Date(),
              cuProject: cuProject._id,
              type: 'outsource',
            },
          }).unwrap();

          if (response) {
            if (mqttClient?.publish)
              mqttClient?.publish(refreshUrl, `OUT_SOURCE`);
          }
        }

        if (mqttClient?.publish)
          mqttClient?.publish(
            '/IP/REFRESH/IOT/CONTROLUNIT/APP',
            `REFRESH_PREV_TILE`
          );

        const newData = update?.cuProject;
        const allGoalsTables = newData?.project?.goalsTable;
        const productionFlow = newData?.productionFlow;
        const goalsTable = allGoalsTables?.find(
          (gt) => gt.flowId === newData?.flowId
        );

        const currentFlowIdIdx = productionFlow?.processes?.findIndex(
          (pro) => pro._id === newData?.flowId
        );

        const { multiplier = DEFAULT_MULTIPLIER_VALUE } = goalsTable;

        if (multiplier?.startsWith('*')) {
          tempData['Batch Size'] = calculate(
            `${tempData['Batch Size']}${multiplier.replace('*', '/')}`
          );
        } else if (multiplier?.startsWith('/')) {
          tempData['Batch Size'] = calculate(
            `${tempData['Batch Size']}${multiplier.replace('/', '*')}`
          );
        }

        tempData['Batch Size'] = Math.floor(tempData?.['Batch Size']);

        // for process that is multiprocess
        if (newData?.isMultiProcess) {
          // modify tableData from updated cuProject
          const tableData = goalsTable?.tableData?.map((table) => {
            // run for every subprocess subprocess
            updateNextProcesses(
              productionFlow,
              newData,
              tempData,
              currentFlowIdIdx,
              allGoalsTables,
              editGoalsTable
            );

            // modify the data of batch in goals table for stopped cu project
            if (table?.batchNo === newData?.batchInfo?.batchNo) {
              // variable to store the index of subprocess process that is stopped
              let stoppedIdx;
              return {
                ...table,
                status:
                  +newData?.subProcessIndex ===
                  table?.subProcessData?.length - 1
                    ? 'complete'
                    : table?.status,
                subProcessData: table?.subProcessData?.map((item, iIdx) => {
                  if (iIdx === newData?.subProcessIndex) {
                    // update the stopped sub process status
                    stoppedIdx = iIdx;
                    return { ...item, status: 'complete' };
                  }

                  // update batch size of all the sub processes after stopped sub process
                  if (iIdx >= stoppedIdx && stoppedIdx >= 0) {
                    return {
                      ...item,
                      ['Batch Size']:
                        tempData?.['Batch Size'] || item?.['Batch Size'],
                      newBatchSize: calculate(
                        `${tempData?.['Batch Size'] || item?.['Batch Size']}${
                          goalsTable?.multiplier || DEFAULT_MULTIPLIER_VALUE
                        }`
                      ),
                    };
                  }

                  return item;
                }),
              };
            }
            return table;
          });

          if (tableData.length > 0)
            await editGoalsTable({
              id: goalsTable._id,
              data: {
                tableData,
              },
            }).unwrap();
        } else {
          // for process that is not multiprocess

          const tableData = goalsTable?.tableData?.map((table) => {
            // modify the data of batch in goals table for stopped cu project
            if (table?.batchNo === newData?.batchInfo?.batchNo) {
              return {
                ...table,
                status: 'complete',
                outputSize: tempData?.['Batch Size'],
              };
            }
            return table;
          });

          if (tableData?.length > 0)
            await editGoalsTable({
              id: goalsTable._id,
              data: {
                tableData,
              },
            }).unwrap();

          updateNextProcesses(
            productionFlow,
            newData,
            tempData,
            currentFlowIdIdx,
            allGoalsTables,
            editGoalsTable
          );
        }

        // Update PROGRESS
        // if current process is the last process then update woItemProgress
        if (currentFlowIdIdx === productionFlow?.processes?.length - 1) {
          await updateWoItemProgress(
            updateProgress,
            {
              indexId: itemForJob?._id,
              workOrder: woId,
            },
            {
              producedQuantity: tempData?.['Batch Size'] || 0,
              cuProject: newData?._id,
            }
          );
        }

        tempData['Batch Size'] = calculate(
          `${tempData['Batch Size']}${multiplier}`
        );

        await updateProcessGoalData({
          id: cuProject.processGoalData._id,
          data: {
            dataOnStop: {
              machineAndOperatorId: selectedEmployee,
              data: tempData,
            },
          },
        }).unwrap();

        setIsStop(false);
        setSelectedEmployee('');
        setCuProject({});
      }

      getAllPo();
      dispatch(apiSlice.util.invalidateTags(['CuProject', 'CuProjectBatch']));
    } catch (err) {
      toast.error(err?.response?.data?.message || err?.message, {
        position: 'top-right',
        toastId: err?.response?.data?.message || err?.message,
      });
    }
  };

  return (
    <div>
      <div className="flex justify-end items-center gap-5 mt-5">
        <div className="">
          <Button
            color="brown"
            className="text-[14px] sm:text-[12px]"
            width=" "
            onClick={() => {
              navigate('/assembly', { state: poId });
            }}
          >
            Assembly
          </Button>
        </div>
        <Preview
          className=" sm:w-[3.2%] w-[5%] h-[5%] sm:h-[4%]  "
          onClick={() => setpdfIsOpen(true)}
        />
      </div>

      {cuProject?.mqtt?.additionalFields?.length > 0 && (
        <div className="w-full mb-2 ">
          <AdditionalFieldRender
            fields={cuProject?.mqtt?.additionalFields || []}
            activeTile={activeTile}
            saveDisabled={!cuProject?.additionalFields ? false : true}
          />
        </div>
      )}

      <div className="bg-white p-6  w-full lg:h-full md:h-[110%] flex">
        {pdfisOpen && (
          <div className="z-[100] fixed top-0 left-0 h-screen w-screen flex justify-center items-center bg-black/20">
            <div className="relative w-[95%] h-[95%]">
              <PdfViewer
                file={stepDetails?.[activeIndex]?.attachments?.[0]?.data}
                name={stepDetails?.[activeIndex]?.attachments?.[0]?.name}
                closeClick={() => setpdfIsOpen(false)}
              />
            </div>
          </div>
        )}
        {isStop && (
          <PopupWrapper setOpenPopup={() => setIsStop(false)}>
            <ActionPopup
              popupFor="stop"
              setOperatorPopup={setIsStop}
              userBatchSizeInput={true}
              samplingBatchSize={samplingBatchSize}
              setSamplingBatchSize={setSamplingBatchSize}
              setLocationName={setLocationName}
              locationName={locationName}
              allEmployees={allEmployees}
              selectedEmployee={selectedEmployee}
              setSelectedEmployee={setSelectedEmployee}
              prevEmployee={prevEmployee}
              submitFunction={handleStopSubmit}
              itemForJob={itemForJob}
              cuProject={cuProject}
            />
          </PopupWrapper>
        )}

        {isOpen && (
          <PreviewPopUp
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            postion={'sm:top-[13%] sm:right-[12%] right-[14.5%] top-[23%]'}
          >
            <div className="px-8 mt-[6%] ">
              <h3 className="mb-1  text-lg">Uploaded Images </h3>
              <div className="flex w-full gap-x-5 overflow-y-scroll">
                {formData?.media?.map((item, uIdx) => (
                  <section key={uIdx} className="p-2 border rounded-md w-fit">
                    <img
                      src={item?.data}
                      alt=""
                      className="hover:cursor-pointer min-w-[180px] max-w-[250px]"
                    />

                    <section className="flex justify-between items-center text-sm mt-2">
                      <span className="px-2">{item?.name}</span>
                      <button
                        onClick={() => removeImgHandler(item?.data)}
                        className="outline-none text-red-primary hover:text-white hover:bg-red-primary rounded px-2 py-1"
                      >
                        Remove
                      </button>
                    </section>
                  </section>
                ))}
              </div>
            </div>
          </PreviewPopUp>
        )}

        {/* Side Step Tile  START */}
        <div className="mt-[4%] sm:mr-0 mr-4">
          <div className="space-y-6 sm:w-72 w-[120%]">
            {/* Mapping over steps */}
            {stepDetails?.map((item, idx) => {
              let status = '';
              if (activeIndex === idx) {
                status = 'active';
              } else if (
                referenceData?.find(
                  (item) => Number(item?.stepNo) === Number(idx) + 1
                )
              ) {
                status = 'completed';
              } else {
                status = '';
              }

              return (
                <Steps
                  key={idx}
                  index={idx}
                  isSequential={formDetail?.formId?.isSequential}
                  setActiveIndex={setActiveIndex}
                  setActiveForm={setActiveForm}
                  item={item}
                  name={item.stepName}
                  type={status} //active, ''
                  stepNo={Number(item.stepNumber)}
                />
              );
            })}
          </div>
        </div>
        {/* Side Step Tile  END */}

        {/* form generation START */}
        <div className="mt-[4%] sm:px-6 px-1 sm:w-full w-[85%]">
          <div className="flex justify-center sm:w-[80%] mx-auto">
            {unit <=
            (cuProject?.batchInfo?.['newBatchSize'] ||
              cuProject?.batchInfo?.['Batch Size']) ? (
              <h3 className="  text-green-500 tracking-tight font-semibold  text-xl  w-full flex justify-center items-center leading-5 text-nowrap ">
                Unit {unit}&nbsp; of &nbsp;
                {cuProject?.batchInfo?.['newBatchSize'] ||
                  cuProject?.batchInfo?.['Batch Size']}{' '}
                is ongoing
              </h3>
            ) : null}
          </div>
          <div className="flex justify-center  sm:w-[80%] w-[85%] mx-auto  mt-1">
            {unit >
            (cuProject?.batchInfo?.['newBatchSize'] ||
              cuProject?.batchInfo?.['Batch Size']) ? (
              <h2 className=" sm:text-xl flex text-green-500 tracking-tight sm:font-semibold">
                Assembly Completed!!!
              </h2>
            ) : (
              <p className=" text-sm flex justify-center text-center text-gray-500 tracking-tight leading-4">
                {activeForm?.description}
              </p>
            )}
          </div>

          <hr className="sm:w-[90%] w-[92%] h-1 mx-auto my-2   bg-gray-300 border-0 rounded " />

          <div className=" sm:w-[90%] w-[80%] ">
            {/* Description START */}
            <div className="sm:mb-4 sm:w-[90%] mt-10 sm:ml-7 ">
              <label
                htmlFor="description"
                className="block text-gray-700 font-bold mb-2"
              >
                Remarks :
              </label>
              <textarea
                id="description"
                name={activeForm?.stepName}
                value={formData?.value || ''}
                onChange={(e) => handleChange(e)}
                className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight ml-4 focus:outline-none focus:shadow-outline"
                rows="3"
                placeholder="Enter your remark"
              />
            </div>

            {/* Description END */}

            {/* Media START */}
            <div className="sm:w-[90%] w-ful">
              <label className="block text-gray-700 font-bold mb-2 sm:ml-7">
                Media :
              </label>
              <div className="flex justify-end ">
                <DragAndDrop
                  className="h-10  text-xs  w-[50%] mr-[10%] "
                  accept="image/png, image/jpeg"
                  fileType="JPG/PNG"
                  onChange={(e) => changeHandler(e, activeForm?.stepName)}
                  multiple
                />
                <Preview
                  className=" sm:w-[4.5%] sm:h-[4%] w-[7%] mt-3 "
                  onClick={() => setIsOpen(!isOpen)}
                />
              </div>
            </div>

            {/* Media Ends */}

            <div className="w-full flex justify-evenly sm:mt-[33%] mt-[28%] sm:ml-4 ml-7">
              <Button
                className="py-2 "
                color="blue"
                width=""
                name=""
                value=""
                type="button"
                disabled={
                  referenceData?.find(
                    (item) => item?.stepName === activeForm?.stepName
                  ) ||
                  referenceData?.length === stepDetails?.length ||
                  unit >
                    (cuProject?.batchInfo?.['newBatchSize'] ||
                      cuProject?.batchInfo?.['Batch Size'])
                }
                onClick={(e) => {
                  handleStepDone(e);
                }}
              >
                Done
              </Button>

              <Button
                className="py-2"
                color="red"
                width=" sm:w-[21%] w-[30%]"
                name="Submit"
                value="Submit"
                type="button"
                onClick={() => setIsStop((prev) => !prev)}
              >
                Stop
              </Button>
            </div>
          </div>
        </div>

        {/* form generation END */}
      </div>
    </div>
  );
};

export default AssemblyDashboard;
