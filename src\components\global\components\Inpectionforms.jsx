import Button from './Button';
import { useMediaQuery } from 'react-responsive';
import { mobileWidth } from '../../../helperFunction';

const Inpectionforms = ({ children, onClose, type }) => {
  const isMobile = useMediaQuery({ query: mobileWidth });
  return (
    <div className="!relative w-full h-[90vh] overflow-y-scroll">
      {!type?.startsWith('stockInpage') && isMobile && (
        <Button
          className="!bg-white border border-gray-500 !text-black  !mt-1 ml-3"
          onClick={() => onClose()}
        >
          Back
        </Button>
      )}

      {children}
    </div>
  );
};

export default Inpectionforms;
