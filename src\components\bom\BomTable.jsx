import { useContext, useState } from 'react';
import Table from '../global/components/Table';
import { Store } from '../../store/Store';
import BomRow from './BomRow';
import { INITIAL_MODAL_FOR } from './bomConstants';
import { useGetAllPartsForOptionsQuery } from '../../slices/partApiSlice';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice';
import { useGetAllTemplatesForOptionsQuery } from '../../slices/templateApiSlice';
import RawMaterialModal from './RawMaterialModal';
import ManageMedia from '../global/components/ManageMedia';
import ChangeProductionFlow from './ChangeProductionFlow';

export default function BomTable({
  boms,
  setBoms,
  allProducts = [],
  setDeletedMedias,
  setDeletedBoms,
}) {
  const [modalFor, setModalFor] = useState(INITIAL_MODAL_FOR);
  const { defaults } = useContext(Store);

  const { data: allParts = [] } = useGetAllPartsForOptionsQuery();
  const { data: dropdowns } = useGetDropdownsQuery();
  const { data: allTemplates = [] } = useGetAllTemplatesForOptionsQuery();

  const uoms =
    dropdowns?.dropdowns?.find((d) => d?.name === 'uom')?.values || [];

  return (
    <>
      {modalFor?.for === 'pf' && (
        <ChangeProductionFlow
          modalFor={modalFor}
          setModalfor={setModalFor}
          bom={boms?.find((bom) => bom?.rowId === modalFor?.rowId)}
          setBoms={setBoms}
        />
      )}

      {modalFor?.for === 'rm' && (
        <RawMaterialModal
          modalFor={modalFor}
          setModalfor={setModalFor}
          allParts={allParts}
          bom={boms?.find((bom) => bom?.rowId === modalFor?.rowId)}
          setBoms={setBoms}
        />
      )}
      {modalFor?.for === 'media' && (
        <ManageMedia
          open={modalFor?.open}
          setOpen={() => setModalFor(INITIAL_MODAL_FOR)}
          medias={
            boms?.find((bom) => bom?.rowId === modalFor?.rowId)?.media || []
          }
          onSubmit={(attachments, deleted) => {
            setDeletedMedias((prev) => [
              ...(prev || []),
              ...(deleted || []).filter(Boolean),
            ]);

            setBoms((prev) =>
              prev?.map((b) => {
                if (b.rowId === modalFor?.rowId) {
                  return {
                    ...b,
                    isUpdated: true,
                    media: attachments,
                  };
                }
                return b;
              })
            );
            setModalFor(INITIAL_MODAL_FOR);
          }}
        />
      )}
      <div className="w-full overflow-x-auto">
        <Table>
          <Table.Head>
            <Table.Row>
              <Table.Th>#</Table.Th>
              <Table.Th>Category</Table.Th>
              <Table.Th>Item Name</Table.Th>
              <Table.Th>Uom</Table.Th>
              <Table.Th>Units</Table.Th>
              <Table.Th>Template</Table.Th>
              {defaults?.defaultParam?.bomColumns?.map((el, index) => (
                <Table.Th key={index}>{el?.title}</Table.Th>
              ))}
              <Table.Th>Media</Table.Th>
              <Table.Th>RM</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {boms
              .filter((bom) => bom.variantIndex === 1)
              .map((bom) => (
                <BomRow
                  key={bom?._id || bom?.tempId}
                  row={bom}
                  boms={boms}
                  setBoms={setBoms}
                  variantIndex={1}
                  allProducts={allProducts}
                  allParts={allParts}
                  uoms={uoms}
                  allTemplates={allTemplates}
                  setModalFor={setModalFor}
                  setDeletedBoms={setDeletedBoms}
                  setDeletedMedias={setDeletedMedias}
                />
              ))}
          </Table.Body>
        </Table>
      </div>
    </>
  );
}
