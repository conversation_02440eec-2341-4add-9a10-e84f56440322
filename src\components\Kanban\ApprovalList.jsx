import { memo, useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import ApprovalCard from './ApprovalCard';

const ITEM_HEIGHT = 200;

const ApprovalList = memo(({
  items,
  hasNextPage,
  isNextPageLoading,
  loadNextPage,
  selectedCards,
  onCardSelect,
  setShowSidebar,
  setSidebarData,
  setSidebarDataType,
  setSelectedTabForPrint,
  setDataToPrint,
  setShowEmailModal,
  setDataForMail,
  height = 600
}) => {
  const itemCount = useMemo(() => {
    return hasNextPage ? items.length + 1 : items.length;
  }, [items.length, hasNextPage]);

  const isItemLoaded = useCallback((index) => {
    return !!items[index];
  }, [items]);

  const Row = memo(({ index, style }) => {
    const item = items[index];
    if (!item) {
      return (
        <div style={style} className="flex items-center justify-center">
          <div className="animate-pulse bg-gray-200 rounded-lg h-32 w-full mx-4"></div>
        </div>
      );
    }

    const selectedTab = item._category || 'unknown';

    return (
      <div style={style} className="px-4">
        <ApprovalCard
          key={item._id}
          selectedTab={selectedTab}
          data={item}
          setShowSidebar={setShowSidebar}
          setSidebarData={setSidebarData}
          setSidebarDataType={setSidebarDataType}
          setSelectedTabForPrint={setSelectedTabForPrint}
          setDataToPrint={setDataToPrint}
          setShowEmailModal={setShowEmailModal}
          setDataForMail={setDataForMail}
          isSelected={selectedCards.has(item._id)}
          onSelect={onCardSelect}
        />
      </div>
    );
  });

  Row.displayName = 'ApprovalRow';

  return (
    <InfiniteLoader
      isItemLoaded={isItemLoaded}
      itemCount={itemCount}
      loadMoreItems={loadNextPage}
    >
      {({ onItemsRendered, ref }) => (
        <List
          ref={ref}
          height={height}
          itemCount={itemCount}
          itemSize={ITEM_HEIGHT}
          onItemsRendered={onItemsRendered}
          overscanCount={5}
        >
          {Row}
        </List>
      )}
    </InfiniteLoader>
  );
});

ApprovalList.displayName = 'ApprovalList';

export default ApprovalList;
