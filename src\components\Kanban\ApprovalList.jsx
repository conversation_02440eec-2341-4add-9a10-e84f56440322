import { memo, useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import ApprovalCard from './ApprovalCard';

const ITEM_HEIGHT = 280;

const ApprovalList = memo(({
  items,
  hasNextPage,
  loadNextPage,
  selectedCards,
  onCardSelect,
  setShowSidebar,
  setSidebarData,
  setSidebarDataType,
  setSelectedTabForPrint,
  setDataToPrint,
  setShowEmailModal,
  setDataForMail,
}) => {
  const itemCount = useMemo(() => {
    return hasNextPage ? items.length + 1 : items.length;
  }, [items.length, hasNextPage]);

  const isItemLoaded = useCallback((index) => {
    return !!items[index];
  }, [items]);

  const Row = memo(({ index, style }) => {
    const item = items[index];
    if (!item) {
      return (
        <div style={style} className="flex items-center justify-center px-4 py-2">
          <div className="bg-white border border-slate-200 rounded-xl w-full h-64 flex items-center justify-center">
            <div className="flex flex-col items-center gap-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-500">Loading more approvals...</span>
            </div>
          </div>
        </div>
      );
    }

    const selectedTab = item._category || 'unknown';

    return (
      <div style={style} className="px-4">
        <ApprovalCard
          key={item._id}
          selectedTab={selectedTab}
          data={item}
          setShowSidebar={setShowSidebar}
          setSidebarData={setSidebarData}
          setSidebarDataType={setSidebarDataType}
          setSelectedTabForPrint={setSelectedTabForPrint}
          setDataToPrint={setDataToPrint}
          setShowEmailModal={setShowEmailModal}
          setDataForMail={setDataForMail}
          isSelected={selectedCards.has(item._id)}
          onSelect={onCardSelect}
        />
      </div>
    );
  });

  Row.displayName = 'ApprovalRow';

  return (
    <InfiniteLoader
      isItemLoaded={isItemLoaded}
      itemCount={itemCount}
      loadMoreItems={loadNextPage}
    >
      {({ onItemsRendered, ref }) => (
        <List
          ref={ref}
          height={1000}
          itemCount={itemCount}
          itemSize={ITEM_HEIGHT}
          onItemsRendered={onItemsRendered}
          overscanCount={5}
        >
          {Row}
        </List>
      )}
    </InfiniteLoader>
  );
});

ApprovalList.displayName = 'ApprovalList';

export default ApprovalList;
