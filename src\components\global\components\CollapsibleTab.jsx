import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

const CollapsibleTab = ({
  defaultOpen = false,
  children,
  title,
  className,
}) => {
  const [expand, setExpand] = useState(defaultOpen);
  return (
    <div className={className}>
      <section
        onClick={() => setExpand((prev) => !prev)}
        className={`w-full flex justify-between items-center px-5 py-1 border-black/20 cursor-pointer ${
          expand ? 'border-y' : 'border-t'
        }`}
      >
        <span className="text-lg text-black font-semibold tracking-wide">
          {title}
        </span>
        {expand ? (
          <ChevronUpIcon className="h-4 w-4" />
        ) : (
          <ChevronDownIcon className="h-4 w-4" />
        )}
      </section>
      <section
        className={`${expand ? 'block border-b border-black/20' : 'hidden'}`}
      >
        {children}
      </section>
    </div>
  );
};

export default CollapsibleTab;
