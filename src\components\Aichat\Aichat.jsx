import { use<PERSON><PERSON>back, useContext, useEffect, useRef, useState } from 'react';
import { HiSpark<PERSON> } from 'react-icons/hi2';
import {
  IoIosCamera,
  IoIosDesktop,
  IoIosRefresh,
  IoIosSend,
  IoIosSwap,
  IoMdAlarm,
  IoMdArrowRoundBack,
  IoMdAttach,
  IoMdCamera,
  IoMdCart,
  IoMdCash,
  IoMdClipboard,
  IoMdCube,
  IoMdListBox,
  IoMdMic,
  IoMdMicOff,
  IoMdPersonAdd,
  IoMdStats,
  IoMdVolumeHigh,
  IoMdVolumeOff,
} from 'react-icons/io';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import Webcam from 'react-webcam';
import { handlePdf } from '../../helperFunction';
import {
  useSendImageMutation,
  useSendMessageMutation,
} from '../../slices/ai/chatbotApislice';
import { apiSlice } from '../../slices/apiSlice';
import { useLazyGetMediaByIdQuery } from '../../slices/mediaSlice';
import { useGetAllEmployeesQuery } from '../../slices/userApiSlice';
import { Store } from '../../store/Store';
import Button from '../global/components/Button';
import DragAndDrop from '../global/components/DragAndDrop';
import PopoverModal from '../global/components/PopoverModal';
import Select from '../global/components/Select';
import Textarea from '../global/components/Textarea';
import TruncateString from '../global/TruncateString';
import HistorySidebar from '../Kanban/HistorySidebar';
import { Input } from '../v2';

const refactorMessage = (message) => {
  let newMessage = message.split(
    /@nav|@createTable|@revalidate|@timer|@user|@btn/
  )?.[0];
  if (message.includes('@table')) {
    newMessage = message?.split(/@table/)?.[0] + message?.split(/@table/)?.[1];
  }
  return newMessage;
};

const Aichat = () => {
  const dispatch = useDispatch();
  const [reminderData, setReminderData] = useState({
    date: new Date().toISOString()?.split('T')[0],
    time: '',
  });
  const [sessionId, setSessionId] = useState(Date.now());
  const [reminderRemarks, setReminderRemarks] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUserName, setSelectedUserName] = useState('');

  const [inputValue, setInputValue] = useState('');
  const [recognition, setRecognition] = useState(null);
  const startListening = () => {
    if (isRecording) {
      recognition.stop();
      setIsRecording(false);
    } else {
      if (!recognition) {
        const SpeechRecognition =
          window.SpeechRecognition || window.webkitSpeechRecognition;
        const newRecognition = new SpeechRecognition();
        newRecognition.continuous = true;
        newRecognition.interimResults = false;
        newRecognition.lang = 'en-US';

        newRecognition.onresult = (event) => {
          const transcript =
            event.results[event.results.length - 1][0].transcript;
          setInputValue((prev) => prev + ' ' + transcript);
        };

        setRecognition(newRecognition);
        newRecognition.start();
        setIsRecording(true);
      } else {
        recognition.start();
        setIsRecording(true);
      }
    }
  };

  /**
   * The state variable `showModal` is a boolean that controls the visibility of
   * a modal dialog. When `true`, the modal is visible, and when `false`, it is
   * hidden.
   * @type {boolean}
   */
  const [showModal, setShowModal] = useState(false);
  const [temp, setTemp] = useState('');
  const [timer, setTimer] = useState(false);
  const [users, setUsers] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [fieldNames, setFieldNames] = useState([]);

  const [messages, setMessages] = useState([]);
  /**
   * `file` is a state variable that represents the current file being
   * uploaded or viewed. When the user uploads a file, it contains the
   * data of the file. When the user views a file, it contains the metadata
   * of the file. The file object has the following structure:
   ** @type {{ data: string; fileName: string; fileType: string; }}
   * **/
  const [file, setFile] = useState(null);

  /* 
  The state variable `facingMode` is used to set the camera facing mode.
  Possible values are:
  - 'user': the camera is facing the user, typically the front camera.
  - 'environment': the camera is facing the environment, typically the rear camera.
  This state variable is used to control the facing mode of the camera.
  It is initially set to 'user'.
  */
  const [facingMode, setFacingMode] = useState('user');

  /**
   * `isOpenCam` is a state variable that controls whether the camera modal is open or not.
   * When `true`, the camera modal is open and when `false`, it is closed.
   * @type {boolean}
   */
  const [isOpenCam, setIsOpenCam] = useState(false);
  const [isRecording, setIsRecording] = useState(false);

  const [sendMessage, { isLoading }] = useSendMessageMutation();
  const ref = useRef(null);
  const inputRef = useRef(null);
  const loaderRef = useRef(null);

  const webcamRef = useRef(null);

  const capture = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();

    let data = {
      fileName: 'captured-image.jpeg',
      fileType: 'image/jpeg',
      data: imageSrc,
    };
    setFile(data);
  }, [webcamRef]);
  /**
   * A function that toggles the facing mode between 'user' and 'environment'.
   *
   * @param None
   * @return None
   */
  const toggleFacingMode = () => {
    const newFacingMode = facingMode === 'user' ? 'environment' : 'user';
    setFacingMode(newFacingMode);
  };

  const [getMediaById] = useLazyGetMediaByIdQuery();
  const { data: employees = [] } = useGetAllEmployeesQuery();
  const filteredEmployees = employees.filter((employee) =>
    employee.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  const [sendImage, { isLoading: isLoadingImage }] = useSendImageMutation();
  const [historySidebar, setHistorySidebar] = useState({
    open: false,
    steps: [],
  });
  const GetMedia = async (temp) => {
    let arr = temp.split(',');
    return await getMediaById({ id: arr[1] }, false).unwrap();
  };
  const [isSpeaking, setIsSpeaking] = useState(false);

  const handleSpeak = (text) => {
    if ('speechSynthesis' in window) {
      if (speechSynthesis.speaking) {
        speechSynthesis.cancel();
        setIsSpeaking(false);
        return;
      }
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.onend = () => setIsSpeaking(false);
      speechSynthesis.speak(utterance);
      setIsSpeaking(true);
    } else {
      alert('Speech synthesis is not supported in your browser.');
    }
  };

  const resetState = () => {
    setMessages([]);
    setTimer(false);
    setUsers(false);
    setReminderData({
      date: new Date().toISOString()?.split('T')[0],
      time: '',
    });
    setReminderRemarks('');
    setSessionId(Date.now());
    setShowButtons(true);
    setFormValues([]);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const messageText = e.target.value || e.target.elements.sendingText.value;
    if (messageText.trim() !== '' && !isRecording) {
      handleSendMessage(messageText);
      if (e.target.value) {
        e.target.value = '';
      } else {
        e.target.reset();
      }
    }
  };

  const handleTableSubmit = () => {
    let messageToSend = '';
    for (const [key, value] of Object.entries(formValues || {}).reverse()) {
      messageToSend += `${key.replaceAll(' ', '_')}  :  ${value}  , \n`;
    }
    sendMessage({ data: { message: messageToSend, sessionId } })
      .unwrap()
      .then((res) => {
        setTemp(res.chatbotData);
        if (res.chatbotData.includes('@nav')) {
          renderSideBar(res.chatbotData.split('@nav')[1]);
        }

        if (res.chatbotData.includes('@timer')) {
          setTimer(true);
        }
        if (res.chatbotData.includes('@user')) {
          setUsers(true);
        }
        if (res.chatbotData.includes('@revalidate:')) {
          const tags = res?.chatbotData
            ?.split('@revalidate:')?.[1]
            ?.split(', ');
          dispatch(apiSlice.util.invalidateTags(tags));
        }
        if (res.chatbotData.includes('@pdf:')) {
          const pdfUrl = res?.chatbotData?.split('@pdf:')[1];
          handlePdf(null, null, null, pdfUrl);
        }
        if (res.chatbotData.includes('@createTable')) {
          setFieldNames(
            res?.chatbotData?.split('@createTable')[1].trim().split(',')
          );
        }
        if (res.chatbotData.includes('@clearData ')) {
          setFieldNames([]);
          res?.chatbotData?.replaceAll('@clearData ', '');
        }
        setMessages((prevMessages) => [
          ...prevMessages,
          {
            name: 'bot',
            message: res.chatbotData.includes('W@3n')
              ? res.chatbotData.replace(
                  res.chatbotData,
                  'Your file is ready to view'
                )
              : res.chatbotData,
          },
        ]);
        if (temp.includes('W@3n')) {
          GetMedia(temp).then((item2) => setFile(item2));
        }
      });
    setFormValues([]);
  };
  const [formValues, setFormValues] = useState(
    fieldNames.reduce((acc, field) => {
      const [fieldName] = field.split(':').map((str) => str.trim());
      acc[fieldName] = '';
      return acc;
    }, {})
  );
  const createTable = (midx) => {
    const handleChange = (e, fieldName) => {
      setFormValues({
        ...formValues,
        [fieldName]: e.target.value,
      });
    };

    return (
      <form className="z-[20] w-full max-w-md mx-auto bg-white rounded-lg overflow-hidden md:max-w-lg lg:max-w-xl p-4">
        <div className="flex flex-col space-y-4">
          {fieldNames.map((field, index) => {
            const [fieldName, fieldType, fieldValue] = field
              .split(':')
              .map((str) => str.trim());
            const fieldValues = fieldValue?.split(';');
            return (
              <div
                key={index}
                className="w-full flex items-center justify-between space-x-4"
              >
                <label className="text-gray-700 font-medium w-1/3">
                  {fieldName}
                </label>
                {fieldType === 'select' ? (
                  <div className="w-2/3">
                    <Select
                      menuPosition={'fixed'}
                      className={'!z-[52]'}
                      styles={{
                        control: (provided) => {
                          return {
                            ...provided,
                            width: '100%',
                            border: '1px solid #E5E7EB',
                          };
                        },
                        menuList: (provided) => {
                          return {
                            ...provided,
                          };
                        },
                      }}
                      value={formValues[fieldName]}
                      options={fieldValues?.slice(1)?.map((el, idx) => ({
                        value: idx,
                        label: el,
                      }))}
                      onChange={(e) => handleChange(e, fieldName)}
                      placeholder={fieldValues[0]}
                    />
                  </div>
                ) : (
                  <input
                    type={fieldType || 'text'}
                    placeholder={`Enter ${fieldName} [${fieldType || 'text'}] `}
                    className="p-2 border rounded-md w-2/3"
                    value={formValues[fieldName]}
                    onChange={(e) => handleChange(e, fieldName)}
                  />
                )}
              </div>
            );
          })}

          <div className="flex space-x-4">
            <button
              type="button"
              className="mt-4 bg-blue-500 text-white py-2 rounded-md w-1/2"
              onClick={handleTableSubmit}
              disabled={messages.length - 1 !== midx}
            >
              Submit
            </button>
            <button
              type="button"
              className="mt-4 bg-gray-500 text-white py-2 rounded-md w-1/2"
              onClick={() => alert('Form cancelled')}
              disabled={messages.length - 1 !== midx}
            >
              Cancel
            </button>
          </div>
        </div>
      </form>
    );
  };

  const handleSendMessage = async (text) => {
    if (text !== '') {
      setShowButtons(false);
      setMessages((prevMessages) => [
        ...prevMessages,
        { name: 'User', message: text },
      ]);

      sendMessage({ data: { message: text, sessionId } })
        .unwrap()
        .then((res) => {
          setTemp(res.chatbotData);
          if (res.chatbotData.includes('@nav')) {
            renderSideBar(res.chatbotData.split('@nav')[1]);
          }

          if (res.chatbotData.includes('@timer')) {
            setTimer(true);
          }
          if (res.chatbotData.includes('@user')) {
            setUsers(true);
          }
          if (res.chatbotData.includes('@revalidate:')) {
            const tags = res.chatbotData
              ?.split('@revalidate:')?.[1]
              ?.split(', ');
            dispatch(apiSlice.util.invalidateTags(tags));
          }
          if (res.chatbotData.includes('@pdf:')) {
            const pdfUrl = res.chatbotData.split('@pdf:')[1];
            handlePdf(null, null, null, pdfUrl);
          }
          if (res.chatbotData.includes('@createTable')) {
            setFieldNames(
              res.chatbotData.split('@createTable')[1].trim().split(',')
            );
          }
          if (res.chatbotData.includes('@clearData ')) {
            setFieldNames([]);
            res.chatbotData.replace('@clearData ', '');
          }
          setMessages((prevMessages) => [
            ...prevMessages,
            {
              name: 'bot',
              message: res.chatbotData.includes('W@3n')
                ? res.chatbotData.replace(
                    res.chatbotData,
                    'Your file is ready to view'
                  )
                : res.chatbotData,
            },
          ]);
          if (temp.includes('W@3n')) {
            GetMedia(temp).then((item2) => setFile(item2));
          }
        });
      setSearchQuery('');
      setSelectedUsers([]);
      setInputValue('');
    }
  };

  const navigate = useNavigate();
  const handleNavigation = (url) => {
    navigate(url);
  };
  useEffect(() => {
    if (isLoading || messages.length) {
      ref.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
      });
      loaderRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
    if (inputRef.current) {
      inputRef.current.focus();
    }
    if (messages.length > 0 && inputRef.current) {
      inputRef.current.focus();
    }
  }, [messages.length, isLoading]);

  /**
   * Handles the change event for a given input element for media upload and updates the file state.
   *
   * @param {Object} e - The event object containing the input element data.
   */
  const changeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name;
      let ftype = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;

        let data = {
          fileName: fname,
          fileType: ftype,
          data: url,
        };
        setFile(data);
      });
    }
  };

  const handleImageSubmit = () => {
    sendImage(file)
      .unwrap()
      .then(() => {
        setMessages((prevMessages) => [
          ...prevMessages,
          {
            name: 'bot',
            message: `Customer has been created. \nl1nk:/settings/config/customer:Customer Tab:l1nk.`,
          },
        ]);
        setFile(null);
      })
      .catch(() => {
        setMessages((prevMessages) => [
          ...prevMessages,
          {
            name: 'bot',
            message: `An error occured! Customer could not be created.`,
          },
        ]);
      });
  };
  const buttonValue = (value) => {
    handleSendMessage(value);
  };

  const renderTable = (data, header, footer) => {
    const tableData = data.split('&&').map((row) => {
      const [key, ...values] = row.split(':');
      const value = values.join(':').trim();
      return { key: key.trim(), value };
    });

    return (
      <div className="w-full max-w-md mx-auto bg-white rounded-lg overflow-hidden md:max-w-lg lg:max-w-xl">
        <div className="px-4 py-3 md:px-6 md:py-4">
          <h2 className="text-sm font-semibold text-gray-800 text-center md:text-left">
            {header}
          </h2>
        </div>
        <hr />
        <div className="flex flex-col divide-y divide-gray-200">
          {tableData.map((row, index) => (
            <div
              key={index}
              className="flex flex-wrap justify-between items-center px-4 py-3 md:px-6 md:py-4 group relative"
            >
              <span className="text-gray-600 font-medium w-full md:w-1/2 text-sm">
                {row.key}
              </span>
              <span className="text-gray-800 font-semibold w-full md:w-1/2 text-sm text-right md:text-left relative">
                <TruncateString length={15}>{row.value}</TruncateString>
              </span>
            </div>
          ))}
        </div>
        <div className="px-4 py-3 md:px-6 md:py-4">
          <h2 className="text-sm font-semibold text-gray-800 text-center md:text-left">
            {refactorMessage(footer)}
          </h2>
        </div>
      </div>
    );
  };

  const renderMessageWithLink = (message) => {
    const hrefText = message.split(':')[2];
    return (
      <span>
        {message
          .replace(`${hrefText}:l1nk.`, ' ')
          .split(' ')
          .map((word, idx) =>
            word.includes('l1nk:') ? (
              <span
                key={idx}
                onClick={() => handleNavigation(word.split(':')[1])}
                style={{
                  color: 'blue',
                  textDecoration: 'underline',
                  cursor: 'pointer',
                }}
              >
                {hrefText}{' '}
              </span>
            ) : (
              <span key={idx}>{word} </span>
            )
          )}
      </span>
    );
  };

  const renderSideBar = (obj) => {
    setHistorySidebar((prevState) => ({
      open: true,
      steps: [...prevState.steps],
      orderId: obj.trim(),
    }));
  };

  const [isMinimized, _setIsMinimized] = useState(false);
  const [showButtons, setShowButtons] = useState(true);
  const messagesEndRef = useRef(null);
  const { state: { user } = {} } = useContext(Store);
  const handleButtonClick = (message) => {
    handleSendMessage(message);
    setShowButtons(false);
  };

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const handleUpdateReminder = () => {
    sendMessage({
      data: { message: `${reminderData.date},${reminderData.time}`, sessionId },
    })
      .unwrap()
      .then(() => {
        if (reminderRemarks !== '') {
          sendMessage({ data: { message: 'Yes', sessionId } })
            .unwrap()
            .then(() => {
              sendMessage({ data: { message: reminderRemarks, sessionId } })
                .unwrap()
                .then((res) => {
                  if (res.chatbotData.includes('@revalidate:')) {
                    const tags = res.chatbotData
                      ?.split('@revalidate:')?.[1]
                      ?.split(', ');
                    dispatch(apiSlice.util.invalidateTags(tags));
                  }
                  setMessages((prevMessages) => [
                    ...prevMessages,
                    { name: 'bot', message: res.chatbotData },
                  ]);
                });
            });
        } else {
          sendMessage({ data: { message: 'No', sessionId } })
            .unwrap()
            .then((res) => {
              if (res.chatbotData.includes('@revalidate:')) {
                const tags = res.chatbotData
                  ?.split('@revalidate:')?.[1]
                  ?.split(', ');
                dispatch(apiSlice.util.invalidateTags(tags));
              }
              setMessages((prevMessages) => [
                ...prevMessages,
                { name: 'bot', message: res.chatbotData },
              ]);
            });
        }
      });
  };

  return (
    <div className="flex flex-col h-full w-full mx-auto">
      <HistorySidebar sidebar={historySidebar} setSidebar={setHistorySidebar} />

      <div className="flex-1 flex flex-col h-full w-full transition-all duration-300">
        <div className="flex justify-between items-center  bg-[#F1F0FA]  flex-shrink-0">
          <div className="flex items-center space-x-2 ml-auto">
            <IoIosRefresh
              onClick={resetState}
              className="h-6 w-6 text-[#000000] cursor-pointer"
            />
            {/* isMinimized ? (
              <FaMaximize
                className="h-6 w-6 text-[#000000] cursor-pointer"
                onClick={() => setIsMinimized(!isMinimized)}
              />
            ) : (
              <FaMinimize
                className="h-6 w-6 text-[#000000] cursor-pointer"
                onClick={() => setIsMinimized(!isMinimized)}
              />
            ) */}
            {/* <ImCross className="h-6 w-6 text-[#000000] cursor-pointer" />*/}
          </div>
        </div>
        <div className="flex-grow flex bg-[#F1F0FA] flex-col overflow-hidden">
          {!isMinimized && showButtons && (
            <div className="flex flex-col items-center justify-center  bg-[#F1F0FA]">
              <div className="w-full max-w-3xl mx-auto text-left py-10">
                <div className="text-[30px] font-semibold bg-gradient-to-r from-[#000000] via-[#B65398] to-[#604ADC] text-left text-transparent bg-clip-text">
                  Hi there,{' '}
                  <span className="bg-gradient-to-r from-[#000000] via-[#B65398] to-[#604ADC] text-transparent bg-clip-text">
                    {user.name}
                  </span>
                </div>
                <div className="text-[30px] font-semibold text-left mb-1 bg-gradient-to-r from-[#000000] via-[#B65398] to-[#604ADC] text-transparent bg-clip-text">
                  What would you like to know?
                </div>
                <div className="text-[#898989] w-[400px] text-left text-[16px]">
                  Use one of the most common prompts below or use your own to
                  begin
                </div>
              </div>
              <div className="mb-2 w-full max-w-3xl">
                <div className="grid grid-cols-4 gap-y-2 mb-2 w-full max-w-2xl ">
                  <div
                    className="p-2 w-[150px] bg-white border-[#CDCDCD] border rounded-md flex flex-col items-start justify-center text-left text-[#898989] hover:bg-[#FFFFFF] cursor-pointer"
                    onClick={() => handleButtonClick('Work Order')}
                  >
                    <div className="mb-4 font-semibold text-[#000000] text-[14px]">
                      Work Order
                    </div>
                    <IoMdClipboard className="text-xl" />
                  </div>

                  <div
                    className="p-2 w-[150px] border bg-white border-[#CDCDCD] rounded-md flex flex-col items-start justify-center text-left text-[#898989] hover:bg-[#FFFFFF] cursor-pointer"
                    onClick={() => handleButtonClick('Inventory')}
                  >
                    <div className="mb-4 font-semibold text-[#000000] text-[14px]">
                      Inventory
                    </div>
                    <IoMdCube className="text-xl" />
                  </div>
                  <div
                    className="p-2 w-[150px] border bg-white border-[#CDCDCD] rounded-md flex flex-col items-start justify-center text-left text-[#898989] hover:bg-[#FFFFFF] cursor-pointer"
                    onClick={() => handleButtonClick('Sales')}
                  >
                    <div className="mb-4 font-semibold text-[#000000] text-[14px]">
                      Sales
                    </div>
                    <IoMdCart className="text-xl" />
                  </div>
                  <div
                    className="p-2 w-[150px] border bg-white border-[#CDCDCD] rounded-md flex flex-col items-start justify-center text-left text-[#898989] hover:bg-[#FFFFFF] cursor-pointer"
                    onClick={() => handleButtonClick('Purchase')}
                  >
                    <div className="mb-4 font-semibold text-[#000000] text-[14px]">
                      Purchase
                    </div>
                    <IoMdCash className="text-xl" />
                  </div>
                  <div
                    className="p-2 w-[150px] border bg-white border-[#CDCDCD] rounded-md flex flex-col items-start justify-center text-left text-[#898989] hover:bg-[#FFFFFF] cursor-pointer"
                    onClick={() => handleButtonClick('Insight')}
                  >
                    <div className="mb-4 font-semibold text-[#000000] text-[14px]">
                      Insight
                    </div>
                    <IoMdStats className="text-xl" />
                  </div>
                  <div
                    className="p-2 w-[150px] border bg-white border-[#CDCDCD] rounded-md flex flex-col items-start justify-center text-left text-[#898989] hover:bg-[#FFFFFF] cursor-pointer"
                    onClick={() => handleButtonClick('System')}
                  >
                    <div className="mb-4 font-semibold text-[#000000] text-[14px]">
                      System
                    </div>
                    <IoIosDesktop className="text-xl" />
                  </div>
                  <div
                    className="p-2 w-[150px] border bg-white border-[#CDCDCD] rounded-md flex flex-col items-start justify-center text-left text-[#898989] hover:bg-[#FFFFFF] cursor-pointer"
                    onClick={() => handleButtonClick('Task Summary')}
                  >
                    <div className="mb-4 font-semibold text-[#000000] text-[14px]">
                      Task Summary
                    </div>
                    <IoMdListBox className="text-xl" />
                  </div>
                </div>
              </div>
            </div>
          )}
          <div className="flex-grow w-full overflow-y-auto p-4 bg-[#F1F0FA] flex flex-col items-center">
            <div className="w-full max-w-3xl mx-auto">
              {messages.map((message, mIdx) => (
                <div
                  key={mIdx}
                  className={`px-2 flex ${message.name === 'bot' ? 'justify-start' : 'justify-end'} mb-4`}
                >
                  <div className="flex flex-col gap-2 max-w-2xl">
                    <div className="flex">
                      {message.name === 'bot' && (
                        <div className="rounded-full mt-auto mr-1">
                          <HiSparkles size={20} color="#ff8e09" />
                        </div>
                      )}
                      <div>
                        {message.name === 'bot' && mIdx > 0 && (
                          <div className="flex flex-row">
                            <p
                              className={`underline text-blue-primary cursor-pointer text-sm flex ${messages.length - 1 === mIdx ? '' : 'pointer-events-none'}`}
                              onClick={resetState}
                            >
                              Go Back{' '}
                              <IoMdArrowRoundBack
                                size={10}
                                className="ml-1 mt-1"
                              />
                            </p>
                          </div>
                        )}
                        <div
                          key={mIdx}
                          className={`p-2 rounded-3xl flex max-w-2xl ${message.name === 'bot' ? 'bg-[#FFFFFF] text-[#000000]' : 'bg-[#005EEC] text-white'}`}
                        >
                          {message.name === 'bot' &&
                          message.message.includes('@table') ? (
                            renderTable(
                              message.message.split('@table')[1],
                              message.message.slice(
                                0,
                                message.message.indexOf('@table') !== -1
                                  ? message.message.indexOf('@table')
                                  : message.message.length
                              ),
                              message.message.slice(
                                message.message.lastIndexOf('@table') + 6
                              )
                            )
                          ) : message.name === 'bot' &&
                            message.message.includes('@createTable') ? (
                            createTable(mIdx)
                          ) : message.name === 'bot' &&
                            message.message.includes('@nav') ? (
                            <p
                              className={`whitespace-pre-line text-[18px] p-2 font-sans`}
                            >
                              {message.message.slice(
                                0,
                                message.message.indexOf('@nav') !== -1
                                  ? message.message.indexOf('@nav')
                                  : message.message.length
                              )}
                            </p>
                          ) : message.name === 'bot' &&
                            message.message.includes('l1nk') ? (
                            <p
                              className={`whitespace-pre-line text-[18px] p-2 font-sans`}
                            >
                              {renderMessageWithLink(
                                refactorMessage(message.message)
                              )}
                            </p>
                          ) : (
                            <p
                              className={`whitespace-pre-line text-[18px] p-2 font-sans`}
                            >
                              {message.name === 'bot' &&
                              message.message.includes('@btn')
                                ? message.message.slice(
                                    0,
                                    message.message.indexOf('@btn') !== -1
                                      ? message.message.indexOf('@btn')
                                      : message.message.length
                                  )
                                : message.name === 'bot' &&
                                    message.message.includes('@tile')
                                  ? message.message.slice(
                                      0,
                                      message.message.indexOf('@tile') !== -1
                                        ? message.message.indexOf('@tile')
                                        : message.message.length
                                    )
                                  : refactorMessage(message.message)}
                            </p>
                          )}
                          {temp.includes('W@3n') ? (
                            <div>
                              {message.message.includes(
                                'Your file is ready to view'
                              ) ? (
                                <Button onClick={() => setShowModal(true)}>
                                  View
                                </Button>
                              ) : null}
                            </div>
                          ) : null}
                          {message.name === 'bot' &&
                            message.message.includes('@timer') &&
                            timer && (
                              <div className="px-1 flex justify-start">
                                <div
                                  className="inset-0 flex items-center justify-center z-100 bg-gray-900 bg-opacity-50"
                                  onClick={() => setTimer(false)}
                                >
                                  <div
                                    className="relative w-[420px] bg-white shadow-md"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    {' '}
                                    <div className="bg-[#F2F1FB] p-4 rounded-t-lg flex text-center justify-center items-center">
                                      <IoMdAlarm className="mr-2" />
                                      <div className="text-lg font-semibold">
                                        Set Reminder
                                      </div>
                                    </div>
                                    <div className="flex flex-col items-start w-full">
                                      <div className="w-full">
                                        <div className="flex items-center gap-4 w-full m-4">
                                          <div className="flex flex-col w-auto">
                                            <label className="mb-1 text-sm font-medium">
                                              Select Date
                                            </label>
                                            <div className="flex items-center border rounded-md p-2">
                                              <Input
                                                type="date"
                                                onChange={(e) =>
                                                  setReminderData((prev) => ({
                                                    ...prev,
                                                    date: e.target.value,
                                                  }))
                                                }
                                                value={reminderData.date}
                                                className="w-full border-none"
                                              />
                                            </div>
                                          </div>

                                          <div className="flex flex-col w-auto">
                                            <label className="mb-1 text-sm font-medium">
                                              Select Time
                                            </label>
                                            <div className="flex items-center border rounded-md p-2">
                                              <Input
                                                type="time"
                                                onChange={(e) =>
                                                  setReminderData((prev) => ({
                                                    ...prev,
                                                    time: e.target.value,
                                                  }))
                                                }
                                                value={reminderData.time}
                                                className="w-full border-none"
                                              />
                                            </div>
                                          </div>
                                        </div>
                                        <div className="flex flex-col mx-4">
                                          <label className="mb-1 text-sm font-medium">
                                            Remarks
                                          </label>
                                          <Textarea
                                            placeholder="Write your remarks..."
                                            value={reminderRemarks}
                                            onChange={(e) =>
                                              setReminderRemarks(e.target.value)
                                            }
                                            className="w-full border rounded-md p-2"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                    <div className="flex items-center m-2 pl-2 justify-end">
                                      <Button
                                        className="mr-2 !bg-white !text-[#56555c]"
                                        disabled={mIdx !== messages?.length - 1}
                                        onClick={() =>
                                          handleSendMessage('Cancel')
                                        }
                                      >
                                        CANCEL
                                      </Button>
                                      <Button
                                        className="!py-1 text-[#005eec] bg-white"
                                        isLoading={isLoading}
                                        onClick={handleUpdateReminder}
                                      >
                                        OK
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          {message.name === 'bot' &&
                            message.message.includes('@user') &&
                            users && (
                              <div className="px-1 flex justify-start">
                                <div className="inset-0 flex items-center justify-center z-50 bg-gray-900 bg-opacity-50">
                                  <div
                                    onClick={(e) => e.stopPropagation()}
                                    className="flex flex-col justify-around relative w-[420px] bg-white shadow-md"
                                  >
                                    <div className="w-full flex flex-col">
                                      <div className="bg-[#F2F1FB] p-4 rounded-t-lg flex text-center justify-center items-center">
                                        <IoMdPersonAdd className="mr-2" />
                                        <div className="text-lg font-semibold">
                                          Assign Users
                                        </div>
                                      </div>
                                      <div className="p-4">
                                        <input
                                          type="text"
                                          className="w-full p-2 border outline-none rounded-md"
                                          placeholder="Search Users"
                                          value={searchQuery}
                                          onChange={(e) =>
                                            setSearchQuery(e.target.value)
                                          }
                                        />
                                      </div>
                                      {selectedUserName && (
                                        <div className="p-4">
                                          <div>
                                            Selected user: {selectedUserName}
                                          </div>
                                        </div>
                                      )}
                                      {searchQuery && (
                                        <div className="p-4 max-h-60 overflow-y-auto">
                                          {filteredEmployees.map((employee) => (
                                            <div
                                              key={employee._id}
                                              className={`p-2 border-b border-gray-200 cursor-pointer ${selectedUsers.includes(employee) ? 'text-red-500' : 'bg-lightblue-100'}`}
                                              onClick={() => {
                                                setSelectedUsers([employee]);
                                                setSearchQuery(employee.name);
                                                setSelectedUserName(
                                                  employee.name
                                                );
                                              }}
                                            >
                                              {employee.name}
                                            </div>
                                          ))}
                                        </div>
                                      )}
                                      <div
                                        className={`flex items-center m-2 pl-2 justify-end transition-all duration-300`}
                                      >
                                        <Button
                                          className={`mr-2 !bg-white !text-[#56555c]`}
                                          disabled={
                                            mIdx !== messages?.length - 1
                                          }
                                          onClick={() => {
                                            setSelectedUserName('');
                                            handleSendMessage('Cancel');
                                          }}
                                        >
                                          CANCEL
                                        </Button>
                                        <Button
                                          className="!py-1 !text-[#005eec] !bg-white"
                                          isLoading={isLoading}
                                          onClick={() =>
                                            handleSendMessage(
                                              selectedUsers?.[0]?.name
                                            )
                                          }
                                        >
                                          OK
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          {message.name === 'bot' && (
                            <div className="flex items-end cursor-pointer justify-end rounded-full mt-auto mr-1">
                              {isSpeaking ? (
                                <IoMdVolumeOff
                                  size={20}
                                  color="red"
                                  onClick={() =>
                                    handleSpeak(
                                      refactorMessage(messages?.[mIdx]?.message)
                                    )
                                  }
                                />
                              ) : (
                                <IoMdVolumeHigh
                                  size={20}
                                  color="#ff8e09"
                                  onClick={() =>
                                    handleSpeak(
                                      refactorMessage(messages?.[mIdx]?.message)
                                    )
                                  }
                                />
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    {message.message.includes('@btn') ? (
                      <div
                        className={`grid grid-cols-4 gap-2 justify-end max-w-2xl ml-6`}
                      >
                        {message.message
                          .split('@btn')[1]
                          .trim()
                          .split(',')
                          .map((btnText, index) => (
                            <button
                              key={index}
                              title={btnText.trim()}
                              disabled={mIdx !== messages?.length - 1}
                              className="px-2 py-1 rounded-xl text-[#005EEC] hover:bg-[#FFFFFF] text-[16px] border w-full"
                              onClick={() => buttonValue(btnText.trim())}
                            >
                              {btnText.trim()}
                            </button>
                          ))}
                      </div>
                    ) : null}
                    {message.message.includes('@tile') ? (
                      <div
                        className={`grid grid-cols-4 gap-2 justify-end max-w-2xl ml-6`}
                      >
                        {message.message
                          .split('@tile')[1]
                          .trim()
                          .split(',')
                          .map((btnText, index) => (
                            <button
                              key={index}
                              title={btnText.trim()}
                              disabled={mIdx !== messages?.length - 1}
                              className="px-2 py-1 rounded-lg text-[#005EEC] hover:bg-[#FFFFFF] text-[16px] border w-full"
                              onClick={() => buttonValue(btnText.trim())}
                            >
                              {btnText.trim()}
                            </button>
                          ))}
                      </div>
                    ) : null}
                  </div>
                  <div ref={ref} />
                </div>
              ))}
              {isLoading ? (
                <span className="chatloader" ref={loaderRef}></span>
              ) : null}
              <div ref={messagesEndRef} />
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="w-full flex items-center justify-center mx-auto bg-[#F1F0FA] p-4">
              <div className="w-full max-w-3xl bg-white p-4 rounded-lg shadow-sm flex items-center justify-center">
                <textarea
                  ref={inputRef}
                  id="sendingText"
                  type="text"
                  placeholder="Ask whatever you want...."
                  className="flex-1 p-1 border-none rounded-md bg-transparent resize-none focus:outline-none"
                  name="message"
                  value={inputValue}
                  autoComplete="off"
                  disabled={isLoading || isLoadingImage}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e);
                    }
                  }}
                />

                {showModal && (
                  <PopoverModal
                    title={'Upload Customer Card'}
                    description={'Uploaded Files'}
                    file={file}
                    showModal={showModal}
                    isLoading={isLoading}
                    isLoadingImage={isLoadingImage}
                    onCloseModal={() => {
                      setShowModal(false);
                      setFile(null);
                      setIsOpenCam(false);
                    }}
                    onSubmit={() => {
                      if (file === null) {
                        toast.error('Cannot submit with empty file', {
                          theme: 'colored',
                          position: 'top-right',
                          toastId: 'Empty File',
                        });
                        return;
                      }
                      handleImageSubmit();
                      setShowModal(false);
                      setIsOpenCam(false);
                    }}
                    canSubmit={false}
                  >
                    {() => (
                      <>
                        <div className=" flex w-full">
                          <DragAndDrop
                            className="h-10  text-xs  w-[50%]  "
                            accept="image/png, image/jpeg"
                            onChange={(e) => changeHandler(e)}
                            multiple={true}
                          />
                          <IoIosCamera
                            className="mt-3"
                            size={45}
                            onClick={() => setIsOpenCam((prev) => !prev)}
                          />
                        </div>

                        {isOpenCam && (
                          <div className=" bg-slate-200/55 shadow-md rounded-lg overflow-hidden mt-3 mx-auto w-[50%] p-2">
                            <div className="relative aspect-w-16 aspect-h-9">
                              <Webcam
                                audio={false}
                                ref={webcamRef}
                                screenshotFormat="image/jpeg"
                                width={640}
                                height={480}
                                className="object-cover"
                                videoConstraints={{ facingMode }}
                              />
                            </div>
                            <div className="flex justify-around my-2">
                              <IoMdCamera size={45} onClick={capture} />

                              <IoIosSwap size={45} onClick={toggleFacingMode} />
                            </div>
                          </div>
                        )}

                        {file && (
                          <div className=" mt-3 mx-auto">
                            <img
                              src={file?.media?.data || file?.data}
                              alt={file?.media?.name || file?.fileName}
                              className="w-[50%] rounded-lg h-[50%] mx-auto"
                            />
                            <div className="flex justify-between w-[50%] mx-auto">
                              <p>{file.fileName}</p>
                              <p
                                className="text-red-600 hover:underline cursor-pointer"
                                onClick={() => setFile(null)}
                              >
                                Remove
                              </p>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </PopoverModal>
                )}

                <div className="flex items-center ml-4 space-x-4">
                  <button
                    type="button"
                    className="flex items-center space-x-1 p-2 rounded-full"
                    style={{ backgroundColor: isRecording ? 'red' : '#005EEC' }}
                    onClick={startListening}
                  >
                    {isRecording ? (
                      <IoMdMicOff color="white" className="text-xl" />
                    ) : (
                      <IoMdMic color="white" className="text-xl" />
                    )}
                  </button>

                  <button
                    disabled={isLoading || isLoadingImage}
                    className="flex items-center space-x-1 bg-[#005EEC] text-white p-2 rounded-full hover:bg-purple-600"
                  >
                    <IoMdAttach
                      onClick={() => {
                        setFile(null);
                        setShowModal(true);
                      }}
                      className="text-xl"
                    />
                  </button>
                  <button
                    disabled={isLoading || isLoadingImage}
                    className="flex items-center justify-center bg-[#005EEC] text-white p-2 rounded-full hover:bg-purple-600"
                  >
                    <IoIosSend type="submit" className="text-xl" />
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Aichat;
