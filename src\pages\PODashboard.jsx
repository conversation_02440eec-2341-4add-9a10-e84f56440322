import { LoadingOutlined } from '@ant-design/icons';
import { Button, Input, Spin, Tooltip } from 'antd';
import DOMPurify from 'dompurify';
import he from 'he';
import { htmlToText } from 'html-to-text';
import { Archive, ArrowDown, ArrowUp, Trash } from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import HistorySidebar from '../components/Kanban/HistorySidebar';
import POTilesInfo from '../components/POTilesInfo';
import HeaderReorder from '../components/Pipeline/HeaderReorder';
import { FilterIcon, FilterV2 } from '../components/global/components/FilterV2';
import Header from '../components/global/components/Header';
import { InfoTooltip } from '../components/global/components/InfoTooltip';
import Pagination from '../components/global/components/Pagination';
import RightSidebar from '../components/global/components/RightSidebar';
import Table from '../components/global/components/Table';
import TablePopup from '../components/global/components/TablePopup';
import PoStatusModal from '../components/po-dashboard/PoStatusModal';
import PoWhatsappShareModal from '../components/po-dashboard/PoWhatsappShareModal';
import PODashboardSidebar from '../components/po-dashboard/po-dashboard-sidebar';
import MediaModal from '../components/v3/global/components/MediaModal';
import SendMail from '../components/v3/global/components/SendMail';
import {
  addCommaToAmount,
  getLocalDate,
  getLocalDateTime,
  handlePdf,
  mobileWidth,
} from '../helperFunction';
import useDebounceValue from '../hooks/useDebounceValue';
import { useUpdateStatusByFieldNameMutation } from '../slices/defaultsApiSlice';
import { useGetHeaderByPageQuery } from '../slices/headerReorderApiSlice';
import { useLazyGetPdfQuery } from '../slices/pdfApiSlice';
import {
  useArchivePurchaseOrderMutation,
  useDeleteManyPurchaseOrderMutation,
  useDeletePurchaseOrderMutation,
  useGetPoFilterOptionsQuery,
  useLazyGetPurchaseOrderQuery,
  useSendPurchaseOrderMutation,
  useUpdatePurchaseOrderStatusMutation,
} from '../slices/purchaseOrderApiSlice';
import { useSendPoOrQuotationMessageMutation } from '../slices/whatsappConfigurationApiSlice';
import { Store } from '../store/Store';
import {
  DEFAULT_PO_HEADER,
  DEFAULT_PO_PRODUCT_DETAILS_HEADER,
  PAGINATION_LIMIT,
} from '../utils/Constant';
import WithSelectAll from '../utils/HOC/WithSelectAll';
import { customConfirm } from '../utils/customConfirm';
import { toCapitalize } from '../utils/toCapitalize';

const colors = {
  PENDING: 'bg-yellow-200 text-yellow-600',
  REJECTED: 'bg-red-200 text-red-600',
  APPROVED: 'bg-[#DCF0DD] text-[#0F6A2E]',
  COMPLETED: 'bg-green-200 text-green-600',
  'EXCESS MATERIAL': 'bg-indigo-200 text-indigo-600',
  'PARTIALLY COMPLETED': 'bg-orange-200 text-orange-700',
};
const defaultStatuses = [
  {
    label: 'Pending',
    value: 'pending',
  },
  {
    label: 'Rejected',
    value: 'rejected',
  },
  {
    label: 'Approved',
    value: 'approved',
  },
  {
    label: 'Completed',
    value: 'completed',
  },
  {
    label: 'Excess Material',
    value: 'excess material',
  },
  {
    label: 'Partially Completed',
    value: 'partially completed',
  },
];

const PODashboard = ({
  handleCheckBoxChange,
  handleSelectAll,
  selectAll,
  checkedRows,
  setCheckedRows,
  rows,
  setRows,
}) => {
  const { state, defaults } = useContext(Store);
  const { user } = state;
  // for responsive view
  const isMobile = useMediaQuery({ query: mobileWidth });
  const poStatusFieldName = 'purchaseOrderCustomStatus';

  const [readMore, setReadMore] = useState(false);
  const navigate = useNavigate();
  let [searchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [historySidebar, setHistorySidebar] = useState({
    open: false,
    steps: [],
    orderId: '',
  });
  const [updatePoStatusIntoDefaults] = useUpdateStatusByFieldNameMutation();
  const debounceSearch = useDebounceValue(searchQuery || '');
  const [getPOData, { data: poData, isLoading: isLoadingPOData }] =
    useLazyGetPurchaseOrderQuery();
  const [DeletePurchaseOrder] = useDeletePurchaseOrderMutation();
  const [deleteManyPurchaseOrder] = useDeleteManyPurchaseOrderMutation();
  const [archivePurchaseOrder] = useArchivePurchaseOrderMutation();
  const [ShowSidebar, setShowSidebar] = useState(false);
  const [field, setField] = useState('createdAt');
  const [type, setType] = useState('desc');
  const [Page, setPage] = useState(1);
  const [Limit, setLimit] = useState(PAGINATION_LIMIT);
  const [TotalPages, setTotalPages] = useState(0);
  const [TotalResults, setTotalResults] = useState(0);
  const [SelectedPo, setSelectedPo] = useState(null);
  const [ShowEmailModal, setShowEmailModal] = useState(false);
  const [SendingMail, setSendingMail] = useState(false);
  const [isDeleteVisible, setIsDeleteVisible] = useState(false);
  const [isArchiveVisible, setIsArchiveVisible] = useState(false);
  const [isOpenHeaders, setIsOpenHeaders] = useState(false);
  const [allHeader, setAllHeader] = useState([]);
  const [mailData, setMailData] = useState({
    receiver: '',
    body: '',
    subject: '',
    input: {},
    attachments: [],
  });
  const [showTable, setShowTable] = useState(false);
  const activeHeaderFromLocal = 'activePOHeader';
  const { data: headerInfo } = useGetHeaderByPageQuery({
    headerFor: 'activePOHeader',
  });

  const [activeHeader, setActiveHeader] = useState(
    headerInfo?.headers || DEFAULT_PO_HEADER
  );

  const [clickedRow, setClickedRow] = useState('');
  const [openShareModal, setOpenShareModal] = useState(false);
  const User = JSON.parse(localStorage.getItem('user'));
  const [shareNumber, setShareNumber] = useState('');
  const [selectedOption, setSelectedOption] = useState('company');
  // const [shareFile, setShareFile] = useState({});
  const [sendToWhatsApp] = useSendPoOrQuotationMessageMutation();
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  // const [editPOdatas] =  useEditPoMutation();
  const [updatePurchaseOrderStatus] = useUpdatePurchaseOrderStatusMutation();
  const [sendMail, { isLoading }] = useSendPurchaseOrderMutation(); // eslint-disable-line

  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const { data: filterOptions } = useGetPoFilterOptionsQuery(
    {},
    { refetchOnMountOrArgChange: true }
  );
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [statusToCreate, setStatusToCreate] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [filters, setFilters] = useState([]);
  const [showFilters, setShowFilters] = useState(true);
  const [showTimeline, setShowTimeline] = useState(false);

  const MOBILE_VIEW_HEADERS = ['DATE', 'PO ID', 'PO STATUS'];
  // const DESKTOP_VIEW_HEADERS = [
  //   '',
  //   'DATE',
  //   'TASK ID',
  //   'PO ID',
  //   'DELIVERY DATE',
  //   'PO STATUS',
  //   'AMOUNT',
  // ];

  const filterConfig = [
    {
      key: 'date',
      label: 'Date',
      type: 'date',
      path: 'createdAt',
    },
    {
      key: 'poId',
      label: 'PO ID',
      type: 'multiSelect',
      path: 'poID',
      options: filterOptions?.poId || [],
    },
    {
      key: 'status',
      label: 'Status',
      path: 'poStatus',
      type: 'multiSelect',
      options: filterOptions?.poStatus || [],
    },
    {
      key: 'deliveryDate',
      label: 'Delivery Date',
      type: 'date',
      path: 'deliveryDate',
    },
    {
      key: 'vendor',
      label: 'Vendor',
      path: 'vendor',
      type: 'multiSelect',
      isObjectId: true,
      options: filterOptions?.vendor || [],
    },
  ];

  useEffect(() => {
    if (searchParams.get('kanban') === 'true') {
      navigate(
        `/purchase/po/createpurchaseorderv2/new?with_indent=false&kanban=${searchParams.get(
          'kanban'
        )}&department=${searchParams.get('department')}&page=${searchParams.get(
          'page'
        )}&refType=${searchParams.get('refType')}&orderId=${searchParams.get(
          'orderId'
        )}&index=${searchParams.get('index')}`
      );
    }
  }, []); //eslint-disable-line

  useEffect(() => {
    getPOData({
      page: Page,
      limit: Limit,
      debounceSearch,
      type,
      field,
      filters,
    });
  }, [getPOData, Page, Limit, field, type, debounceSearch, filters]);

  useEffect(() => {
    if (isMobile) {
      setActiveHeader(
        MOBILE_VIEW_HEADERS.map((item) => {
          return {
            headerName: item,
            key: item.toLocaleLowerCase(),
            isAdditional: false,
          };
        })
      );
    } else {
      setActiveHeader(headerInfo?.headers || DEFAULT_PO_HEADER);
    }
  }, [isMobile, headerInfo]); // eslint-disable-line

  useEffect(() => {
    if (rows) {
      let allHead = [];
      rows.forEach((row) => {
        if (row?.additionalFields?.templateData?.length > 0) {
          let temp = row?.additionalFields?.templateData;
          temp.forEach((item) => {
            if (item?.fieldType === 'Table') return;

            const key = item?.fieldName;
            if (!allHead.some((item) => item.key === key)) {
              allHead.push({
                headerName: item?.fieldName?.toUpperCase(),
                key: item?.fieldName,
                isAdditional: true,
                type: item?.fieldType,
              });
            }
          });
        }
      });
      setAllHeader([...DEFAULT_PO_HEADER, ...allHead]);
    }
  }, [rows, activeHeader]);

  useEffect(() => {
    if (checkedRows?.length > 0) {
      setIsDeleteVisible(true);
      setIsArchiveVisible(true);
    } else {
      setIsDeleteVisible(false);
      setIsArchiveVisible(false);
    }
  }, [checkedRows]);

  useEffect(() => {
    if (poData?.allPurchaseOrders?.results) {
      setRows(poData?.allPurchaseOrders?.results);
    } else {
      setRows([]);
    }
    setTotalPages(poData?.allPurchaseOrders?.totalPages);
    setTotalResults(poData?.allPurchaseOrders?.totalResults);
  }, [poData, setRows, type, field]);

  const handleDeleteAll = async () => {
    const confirm = await customConfirm(
      "Are you sure you want to delete these PO's?",
      'delete'
    );
    if (!confirm) return;

    const ids = checkedRows.map((item) => item._id);
    const res = await deleteManyPurchaseOrder({ ids });
    if (res?.data?.message) {
      if (res.data.message.msg1 || res.data.message.msg2) {
        toast.error(res.data.message.msg1);
        toast.success(res.data.message.msg2);
      } else {
        toast.success(res.data.message);
      }
    }

    setCheckedRows([]);
  };

  const handleDelete = async (id) => {
    const confirm = await customConfirm(
      'Are you sure you want to delete this purchase order?',
      'delete'
    );
    if (!confirm) return;
    const data = await DeletePurchaseOrder({ id }).unwrap();
    if (data?.message) {
      toast.success(data?.message);
      setShowSidebar(false);
      // getPOData({ page: Page, limit: Limit });
    }
  };
  const handleArchiveAll = async () => {
    const confirm = await customConfirm(
      "Are you sure you want to archive these PO's?",
      'success'
    );
    if (!confirm) return;
    try {
      const ids = checkedRows.map((item) => item._id);
      const res = await archivePurchaseOrder({ ids });
      if (res) {
        toast.success("PO's archived successfully");
        setCheckedRows([]);
      }
    } catch (err) {
      toast.error(err?.response?.data?.message || err.message, {
        theme: 'colored',
        position: 'top-right',
      });
    }
  };

  const handleApprovePO = async (id) => {
    const confirm = await customConfirm(
      "Are you sure you want to approve this PO? You won't be able to edit/delete approved PO."
    );
    if (!confirm) return;
    const data = await updatePurchaseOrderStatus({
      poStatus: 'Approved',
      approvedby: User?.user?.name,
      id,
    });
    if (!data?.error) {
      toast.success(data?.data?.updated?.message);
      setShowSidebar(false);
      setSelectedPo(null);
    }
  };
  const handelsetSidebarData = () => {
    setShowSidebar(true);
  };

  const handleSendmail = async () => {
    setSendingMail(true);
    const fd = new FormData();
    fd.append('id', SelectedPo?._id);
    fd.append('receiver', mailData?.receiver);
    fd.append('subject', mailData?.subject);
    fd.append('body', mailData?.body);
    fd.append(
      'activeHeader',
      localStorage.getItem('activePOProductDetailsHeader') ||
        JSON.stringify(DEFAULT_PO_PRODUCT_DETAILS_HEADER)
    );
    if (mailData?.attachments?.length !== 0) {
      mailData.attachments.forEach((file, index) => {
        // Convert the file object to a JSON string
        fd.append(`attachments[${index}][data]`, file.data);
        fd.append(`attachments[${index}][name]`, file.name);
        fd.append(`attachments[${index}][type]`, file.type);
      });
    }
    await sendMail(fd)
      .unwrap()
      .finally(() => {
        setSendingMail(false);
      });
    toast.success('Mail Sent Successfully');
    setShowEmailModal(false);
    setMailData({
      receiver: '',
      body: '',
      subject: '',
      input: {},
      attachments: [],
    });
  };

  const setSteps = (taskId) => {
    let tasks = state?.allTiles;
    let chosenTask = tasks?.find((elem) => elem?.taskId === taskId);
    if (chosenTask) {
      setHistorySidebar({
        open: true,
        steps: chosenTask?.steps,
        orderId: chosenTask?._id,
      });
    }
  };
  const [isShared, setIsShared] = useState(false);

  const handleShare = async () => {
    try {
      setIsShared(true);
      const data = {
        phoneNumber: shareNumber,
        templateId: selectedTemplate?._id,
        type: 'purchaseOrder',
        id: SelectedPo?._id,
        prefixId: SelectedPo?.poID,
        companyProfile: SelectedPo?.selectedDetails?.companyProfileId || '',
        activeHeader:
          localStorage.getItem('activePOProductDetailsHeader') ||
          JSON.stringify(DEFAULT_PO_PRODUCT_DETAILS_HEADER),
      };
      const res = await sendToWhatsApp(data).unwrap();
      if (res) {
        setOpenShareModal(false);
        setShareNumber('');
        setSelectedOption('company');
        toast.success('Shared Successfully');
      }
    } catch (err) {
      setIsShared(false);
      toast(err);
    } finally {
      setIsShared(false);
    }
  };

  const handleOptionChange = (option) => {
    setSelectedOption(option);
    if (option === 'userManual') {
      setShareNumber('');
    }
  };

  const getStatusColor = (status) => {
    if (colors[status]) {
      return colors[status];
    }
    return 'bg-fuchsia-200 text-fuchsia-600';
  };

  const getPOStatus = () => {
    const allCustomStatuses = defaults?.defaultParam?.[poStatusFieldName];
    return [...defaultStatuses, ...(allCustomStatuses || [])];
  };

  const handleCreateStatus = async () => {
    if (!statusToCreate) {
      toast.error('Please select a status');
      return;
    }
    const allStatuses = getPOStatus();
    const alreadyExists = allStatuses?.find(
      (status) => status?.label === statusToCreate
    );
    if (alreadyExists) {
      toast.error('Status already exists');
      return;
    }
    const body = {
      fieldName: poStatusFieldName,
      value: [
        ...(allStatuses || []),
        { label: statusToCreate.trim(), value: statusToCreate.trim() },
      ],
    };
    const res = await updatePoStatusIntoDefaults(body);
    if (!res?.error) {
      toast.success('Status created successfully');
      setStatusToCreate('');
    }
  };
  const handleDeleteCustomStatus = async (statusToDelete) => {
    const allStatuses = getPOStatus()?.filter(
      (status) => status.label !== statusToDelete.label
    );
    const body = {
      fieldName: poStatusFieldName,
      value: [...(allStatuses || [])],
    };
    const res = await updatePoStatusIntoDefaults(body);
    if (!res?.error) {
      toast.success('Status deleted successfully');
      setSelectedStatus('');
    }
  };

  return (
    <div>
      {isMobile && clickedRow && (
        <TablePopup
          isEdit={false}
          onDownload={() => handlePdf(getPdf, clickedRow?._id, 'purchaseOrder')}
          downloading={isFetchingPdf}
          onBack={() => setClickedRow(null)}
        >
          <div className="space-y-4 !text-[12px]">
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">TASK ID</label>
              <p>
                {clickedRow?.taskId?.customTaskId
                  ? `${clickedRow?.taskId?.customTaskId}(${clickedRow?.taskId?.taskId})`
                  : clickedRow?.taskId?.taskId
                    ? clickedRow?.taskId?.taskId
                    : '-'}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">PURCHASE ORDER DATE</label>
              <p>{getLocalDateTime(clickedRow?.createdAt)}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">PURCHASE ORDER ID</label>
              <p>{clickedRow?.poID}</p>
            </div>

            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">DELIVERY DATE</label>
              <p>{getLocalDateTime(clickedRow?.deliveryDate)}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">PURCHASE ORDER STATUS</label>
              <p>
                {!defaultParam?.projectDefaults?.disableApprovals ? (
                  <span
                    className={`${
                      colors[
                        clickedRow?.poStatus?.charAt(0).toUpperCase() +
                          clickedRow?.poStatus?.slice(1)
                      ]
                    } px-3 py-1 rounded-md font-medium whitespace-nowrap`}
                  >
                    {clickedRow?.poStatus?.charAt(0).toUpperCase() +
                      clickedRow?.poStatus?.slice(1)}
                  </span>
                ) : (
                  <span>-</span>
                )}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">AMOUNT</label>
              <p>{clickedRow?.total}</p>
            </div>
          </div>
        </TablePopup>
      )}
      {isOpenHeaders && (
        <HeaderReorder
          activeHeaderFromLocal={activeHeaderFromLocal}
          setIsOpenHeaders={setIsOpenHeaders}
          activeHeader={activeHeader}
          setActiveHeader={setActiveHeader}
          allHeader={allHeader}
        />
      )}
      <HistorySidebar sidebar={historySidebar} setSidebar={setHistorySidebar} />
      {ShowEmailModal && (
        <SendMail
          title={'Send PO Mail'}
          mailData={mailData}
          setMailData={setMailData}
          SendingMail={SendingMail}
          handleSendmail={handleSendmail}
          setShowEmailModal={setShowEmailModal}
        />
      )}

      <PoWhatsappShareModal
        openModal={openShareModal}
        onCloseModal={() => {
          setOpenShareModal(false);
          setShareNumber('');
          setSelectedOption('company');
        }}
        setSelectedTemplate={setSelectedTemplate}
        setShareNumber={setShareNumber}
        shareNumber={shareNumber}
        selectedOption={selectedOption}
        handleOptionChange={handleOptionChange}
        isShared={isShared}
        SelectedPo={SelectedPo}
        selectedTemplate={selectedTemplate}
        handleShare={handleShare}
      />

      <PoStatusModal
        openModal={openStatusModal}
        setOpenModal={setOpenStatusModal}
        statusToCreate={statusToCreate}
        setStatusToCreate={setStatusToCreate}
        handleCreateStatus={handleCreateStatus}
        getPOStatus={getPOStatus}
        selectedStatus={selectedStatus}
        setSelectedStatus={setSelectedStatus}
        handleDeleteCustomStatus={handleDeleteCustomStatus}
        defaultStatuses={defaultStatuses}
        clickedRow={clickedRow}
      />
      <RightSidebar
        openSideBar={ShowSidebar}
        setOpenSideBar={setShowSidebar}
        scale={736}
        onClose={() => {
          setShowSidebar(false);
          setSelectedPo(null);
          setShowTable(false);
          setTimeout(() => {
            setShowTimeline(false);
          }, 1000);
        }}
        title="PO Details"
      >
        <PODashboardSidebar
          selectedPo={SelectedPo}
          handleDelete={handleDelete}
          setShowEmailModal={setShowEmailModal}
          setShowSidebar={setShowSidebar}
          handleApprovePO={handleApprovePO}
          setOpenShareModal={setOpenShareModal}
          setReadMore={setReadMore}
          showTable={showTable}
          setShowTable={setShowTable}
          showTimeline={showTimeline}
          setShowTimeline={setShowTimeline}
        />
      </RightSidebar>

      {readMore && (
        <MediaModal
          FormData={SelectedPo?.files}
          isView={true}
          setShowModal={setReadMore}
          ShowModal={readMore}
        />
      )}

      <div className="flex">
        <div className="flex flex-col">
          <div className="flex gap-[5px] items-center !min-w-[35rem]">
            <Header
              title="Purchase Dashboard"
              description=""
              infoTitle="Welcome to Purchase Order Dashboard"
              infoDesc="The Purchase Dashboard is your go-to platform for streamlined procurement management.
               This user-friendly interface offers a comprehensive overview of purchasing processes, including order tracking
               , vendor relations, budget monitoring, and customizable approval workflows. Access historical data, and maintain
                a centralized document repository for enhanced analysis and compliance."
              paras={[
                'The Purchase Order page simplifies procurement processes. Quickly create, review, and track purchase orders to enhance procurement efficiency with our user-friendly Purchase Order page. Efficiently manage the smooth workflow from order creation to delivery.',
              ]}
              classNames="mb-[0px]"
            />
          </div>
        </div>
      </div>
      <POTilesInfo isMobile={isMobile} className="mt-[-12px]" />

      <div className="flex justify-between items-center w-full bg-white px-4 py-2">
        <div className="w-1/4">
          <Input.Search
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search"
          />
        </div>
        <div className="flex items-center gap-x-2">
          <FilterIcon
            showFilters={showFilters}
            setShowFilters={setShowFilters}
          />
          {user?.archivePurchaseOrderManagement && (
            <Tooltip title="Archive">
              <Archive
                className={`h-5 w-5 ${isArchiveVisible ? 'text-blue-500  cursor-pointer ' : 'text-gray-400 cursor-not-allowed'}`}
                onClick={handleArchiveAll}
              />
            </Tooltip>
          )}
          {!isMobile && (
            <Tooltip title="Delete">
              <Trash
                onClick={isDeleteVisible ? handleDeleteAll : undefined}
                className={`h-5 w-5 ${!isDeleteVisible ? 'text-gray-400' : 'text-red-500'}`}
              />
            </Tooltip>
          )}
          <Button
            size="small"
            type="primary"
            onClick={() =>
              navigate(
                '/purchase/po/createpurchaseorderv2/new?with_indent=false'
              )
            }
          >
            + Create Order
          </Button>
        </div>
      </div>

      <FilterV2
        showFilters={showFilters}
        config={filterConfig}
        setFilters={setFilters}
      />
      <div>
        <Table>
          <Table.Head>
            <Table.Row>
              {!isMobile && (
                <Table.Th>
                  {checkedRows.length > 0 ? (
                    <div>
                      <input
                        type="checkbox"
                        className="mr-2"
                        checked={selectAll}
                        onChange={(e) => handleSelectAll(e)}
                      />
                      Select All
                    </div>
                  ) : (
                    ''
                  )}
                </Table.Th>
              )}
              {activeHeader?.map((el) => {
                const isHide =
                  isMobile && !MOBILE_VIEW_HEADERS?.includes(el?.headerName);
                if (el?.headerName === 'DATE') {
                  return (
                    <Table.Th key={el?.headerName}>
                      <div className="flex">
                        <div>Date</div>
                        {type === 'aesc' && field === 'createdAt' ? (
                          <ArrowUp
                            cursor={'pointer'}
                            size={15}
                            onClick={() => {
                              setField('createdAt');
                              setType('desc');
                            }}
                          />
                        ) : (
                          <ArrowDown
                            cursor={'pointer'}
                            size={15}
                            onClick={() => {
                              setField('createdAt');
                              setType('aesc');
                            }}
                          />
                        )}
                      </div>
                    </Table.Th>
                  );
                }
                if (el?.headerName === 'TASK ID' && !isMobile) {
                  return (
                    <Table.Th key={el?.headerName}>{el?.headerName}</Table.Th>
                  );
                }

                return (
                  !isHide && (
                    <Table.Th key={el?.headerName}>{el?.headerName}</Table.Th>
                  )
                );
              })}
              {!isMobile && (
                <Table.Options
                  className={'!p-2 border-b-2'}
                  onRearrange={() => {
                    setIsOpenHeaders(true);
                  }}
                />
              )}
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {isLoadingPOData ? (
              <Table.Row>
                <Table.Td colSpan={activeHeader?.length + 2}>
                  <div className="flex justify-center items-center">
                    <Spin size="large" indicator={<LoadingOutlined spin />} />
                  </div>
                </Table.Td>
              </Table.Row>
            ) : (
              rows?.map((item, idxx) => (
                <Table.Row
                  key={idxx}
                  isClickable={true}
                  onClick={() => {
                    setSelectedPo(item);
                  }}
                >
                  {!isMobile && (
                    <Table.Td>
                      <input
                        type="checkbox"
                        onChange={(event) => handleCheckBoxChange(event, item)}
                        checked={checkedRows.includes(item)}
                      />
                    </Table.Td>
                  )}

                  {/* commented rows */}
                  {/* <Table.Td
                  onClick={() => {
                    handelsetSidebarData(item);
                  }}
                >
                  <Button className="!bg-transparent !p-0 !text-blue-500 underline underline-offset-1">
                    GRN ({item?.grn.length})
                  </Button>
                </Table.Td> */}
                  {/* <Table.Td
                  onClick={() => {
                    handelsetSidebarData(item);
                  }}
                >
                  <Button className="!bg-transparent !p-0 !text-blue-500 underline underline-offset-1">
                    Indent (+{item?.indentLink?.length})
                  </Button>
                </Table.Td> */}
                  {/* <Table.Td
                  onClick={() => {
                    handelsetSidebarData(item);
                  }}
                >
                  {item?.vendor?.name ? (
                    item?.vendor?.name?.length > 0 ? (
                      item?.vendor?.name?.length <= 13 ? (
                        item?.vendor?.name
                      ) : (
                        '-'
                      )
                    ) : (
                      '-'
                    )
                  ) : (
                    '-'
                  )}
                </Table.Td> */}

                  {/* commented rows above  */}

                  {/* rearranged columns */}

                  {activeHeader?.map((el, index) => {
                    if (el?.isAdditional) {
                      if (el?.type === 'MultiSelect') {
                        let val =
                          item?.additionalFields?.templateData
                            ?.find((e) => e.fieldName === el?.key)
                            ?.fieldValue?.map((e) => e?.value)
                            ?.join(', ') || '-';
                        return (
                          <Table.Td
                            key={index}
                            onClick={(e) => {
                              handelsetSidebarData(item);
                              e.stopPropagation();
                            }}
                          >
                            {val}
                          </Table.Td>
                        );
                      } else if (el?.type === 'Description') {
                        let val =
                          item?.additionalFields?.templateData?.find(
                            (e) => e.fieldName === el?.key
                          )?.fieldValue || '-';
                        const decodedHTML = he.decode(val || '');
                        const sanitizedHTML = DOMPurify.sanitize(decodedHTML);
                        const completeText = htmlToText(sanitizedHTML, {
                          wordwrap: false,
                        });

                        const oneLineText = completeText
                          .replace(/(\r\n|\n|\r)/gm, '')
                          .replace(/\s+/g, '');

                        return (
                          <Table.Td
                            key={index}
                            onClick={(e) => {
                              handelsetSidebarData(item);
                              e.stopPropagation();
                            }}
                          >
                            <Tooltip text={sanitizedHTML} isText={false}>
                              {oneLineText.length > 10
                                ? oneLineText.slice(0, 10) + '...'
                                : oneLineText}
                            </Tooltip>
                          </Table.Td>
                        );
                      } else {
                        return (
                          <Table.Td
                            key={index}
                            className={'!text-nowrap'}
                            onClick={(e) => {
                              handelsetSidebarData(item);
                              e.stopPropagation();
                            }}
                          >
                            {item?.additionalFields?.templateData?.find(
                              (e) => e.fieldName === el?.key
                            )?.fieldValue || '-'}
                          </Table.Td>
                        );
                      }
                    } else {
                      switch (el?.key) {
                        case 'date':
                          return (
                            <Table.Td
                              onClick={() => {
                                if (isMobile) {
                                  setClickedRow(item);
                                } else {
                                  handelsetSidebarData(item);
                                }
                              }}
                            >
                              {getLocalDate(item.createdAt)}
                            </Table.Td>
                          );
                        case 'task id':
                          return (
                            !isMobile && (
                              <Table.Td
                                className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                                onClick={() => {
                                  setSteps(item?.taskId?.taskId);
                                }}
                              >
                                {(item?.taskId?.customTaskId
                                  ? `${item?.taskId?.customTaskId}(${item?.taskId?.taskId})`
                                  : item?.taskId?.taskId) || '-'}
                              </Table.Td>
                            )
                          );

                        case 'vendor':
                          return (
                            <Table.Td
                              onClick={() => {
                                if (isMobile) {
                                  setClickedRow(item);
                                } else {
                                  handelsetSidebarData(item);
                                }
                              }}
                            >
                              {item?.vendor?.name || '-'}
                            </Table.Td>
                          );

                        case 'po id':
                          return (
                            <>
                              <Table.Td
                                className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                                onClick={() => {
                                  if (isMobile) {
                                    setClickedRow(item);
                                  } else {
                                    handelsetSidebarData(item);
                                  }
                                }}
                              >
                                {item.poID}
                              </Table.Td>
                            </>
                          );
                        case 'delivery date':
                          return (
                            <>
                              {!isMobile && (
                                <Table.Td
                                  onClick={() => {
                                    handelsetSidebarData(item);
                                  }}
                                >
                                  {item?.deliveryDate
                                    ? getLocalDate(item?.deliveryDate)
                                    : 'NA'}
                                </Table.Td>
                              )}
                            </>
                          );
                        case 'po status':
                          return (
                            <>
                              <Table.Td
                                onClick={() => {
                                  if (isMobile) {
                                    setClickedRow(item);
                                  } else {
                                    setClickedRow(item);
                                    setOpenStatusModal(true);
                                  }
                                }}
                              >
                                {!defaults?.defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                                  'purchaseOrder'
                                ) ? (
                                  <div className="flex items-center">
                                    <span
                                      className={`${getStatusColor(
                                        item?.poStatus?.toUpperCase()?.trim()
                                      )} px-3 py-1 rounded-full font-medium whitespace-nowrap`}
                                    >
                                      {toCapitalize(item.poStatus)}
                                    </span>
                                    {item?.poStatus?.toLowerCase() ===
                                      'rejected' &&
                                      [...(item?.statusTimeline || [])].sort(
                                        (a, b) =>
                                          new Date(b.timestamp).getTime() -
                                          new Date(a.timestamp).getTime()
                                      )?.[0]?.remark && (
                                        <InfoTooltip
                                          id="quote status"
                                          position="top"
                                          className="ml-2"
                                        >
                                          {
                                            [
                                              ...(item?.statusTimeline || []),
                                            ].sort(
                                              (a, b) =>
                                                new Date(
                                                  b.timestamp
                                                ).getTime() -
                                                new Date(a.timestamp).getTime()
                                            )?.[0]?.remark
                                          }
                                        </InfoTooltip>
                                      )}
                                  </div>
                                ) : (
                                  <span>-</span>
                                )}
                              </Table.Td>
                            </>
                          );
                        case 'amount':
                          return (
                            <>
                              {!isMobile && (
                                <Table.Td
                                  onClick={() => {
                                    handelsetSidebarData(item);
                                  }}
                                >
                                  {addCommaToAmount(item.total)}
                                </Table.Td>
                              )}
                            </>
                          );

                        default:
                          return null;
                      }
                    }
                  })}
                  {!isMobile && <Table.Td></Table.Td>}
                </Table.Row>
              ))
            )}
          </Table.Body>
        </Table>
        <Pagination
          limit={Limit}
          page={Page}
          setLimit={setLimit}
          setPage={setPage}
          totalPages={TotalPages}
          totalResults={TotalResults}
        />
      </div>
    </div>
  );
};

export default WithSelectAll(PODashboard);
