export function disableReactDevTools() {
  if (typeof window._REACT_DEVTOOLS_GLOBAL_HOOK_ === 'object') {
    for (const prop in window._REACT_DEVTOOLS_GLOBAL_HOOK_) {
      if (prop === 'renderers') {
        window._REACT_DEVTOOLS_GLOBAL_HOOK_[prop] = new Map();
      } else {
        window._REACT_DEVTOOLS_GLOBAL_HOOK_[prop] =
          typeof window._REACT_DEVTOOLS_GLOBAL_HOOK_[prop] === 'function'
            ? () => {}
            : null;
      }
    }
  }
}
