import { openDB } from 'idb';

const DB_NAME = 'Optiwise-DB';
const DB_VERSION = 1;

export const STORE_NAMES = { autosaveForm: 'autosaveForm' };

export const initDB = async () => {
  return openDB(DB_NAME, DB_VERSION, {
    upgrade(db) {
      Object.values(STORE_NAMES).forEach((sName) => {
        if (!db.objectStoreNames.contains(sName)) {
          db.createObjectStore(sName);
        }
      });
    },
  });
};

export const saveToDB = async (storeKey, formKey, data) => {
  const db = await initDB();
  await db.put(STORE_NAMES[storeKey], data, formKey);
};

export const fetchFromDB = async (storeKey, formKey) => {
  const db = await initDB();
  return await db.get(STORE_NAMES[storeKey], formKey);
};

export const deleteFormDB = async (storeKey, formKey) => {
  const db = await initDB();
  await db.delete(STORE_NAMES[storeKey], formKey);
};
