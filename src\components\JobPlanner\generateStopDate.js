import {
  addMinutesToDate,
  checkIfHoliday,
  convertMinsToHrs,
  generateDateStringProduction,
  generateDateTimeStringProduction,
  generateValidDate,
} from '../../helperFunction';
import plannerPrompt from '../../utils/plannerPrompt';

/**
 * Function to generate stopdate
 *
 * @param {string} value start date
 * @param {object} item batch details
 * @param {object} defaultParam object from store
 * @param {object[]} applicableDowntimes applicable downtime array
 * @returns stopdate
 */
export const genStopDate = (value, item, defaultParam, applicableDowntimes) => {
  const {
    'Batch Size': bSize,
    newBatchSize,
    itemsPerHour,
    intervalTime: batchIntervalTime,
    intervalDuration: batchIntervalDuration,
    qtyCount: batchQtyCount,
    qtyDuration: batchQtyDuration,
    setupTime = 0,
  } = item;

  const batchSize = newBatchSize || bSize;

  const { holidays, customShiftTimings } = defaultParam;

  let startDate = new Date(value);
  let stopDate = null;

  // total mins required for process to complete
  let totalMinsRequired = batchSize / (itemsPerHour / 60);

  totalMinsRequired = totalMinsRequired > 0 ? totalMinsRequired + setupTime : 0;

  if (!isFinite(totalMinsRequired) || isNaN(totalMinsRequired)) {
    return;
  }

  let counter = 0;
  let shiftStartTime;
  let shiftStopTime;

  let totalDurationInMins = totalMinsRequired;

  let expectedPlannedDownTime = 0;

  // keep looping till totalMinsRequired is <= 0
  while (totalMinsRequired > 0) {
    // current date for reference genereated according to selected start date
    const currDate = new Date(
      new Date(new Date(value).setDate(startDate.getDate() + counter)).setHours(
        0,
        0,
        0,
        0
      )
    );
    // initial shift details
    let shiftStart = defaultParam?.shiftStart?.split(':');
    let shiftStop = defaultParam?.shiftStop?.split(':');
    const customShift = customShiftTimings?.find(
      (cst) => cst.date === generateDateStringProduction(currDate)
    );

    // if custom shift exists change initial shift details
    if (customShift) {
      shiftStart = customShift?.start?.split(':');
      shiftStop = customShift?.stop?.split(':');
    }

    // shift date and time
    if (counter === 0 && startDate.getHours() < 12) {
      // sets shift time to previous day if start date in between 0-12 AM
      shiftStartTime = new Date(
        new Date(new Date(startDate).setDate(startDate.getDate() - 1)).setHours(
          +shiftStart[0],
          +shiftStart[1],
          0,
          0
        )
      );
    } else {
      shiftStartTime = new Date(
        new Date(new Date(generateDateStringProduction(currDate))).setHours(
          +shiftStart[0],
          +shiftStart[1],
          0,
          0
        )
      );
    }
    shiftStopTime = new Date(
      new Date(new Date(generateDateStringProduction(currDate))).setHours(
        +shiftStop[0],
        +shiftStop[1],
        0,
        0
      )
    );

    /**
     * if shift start is greater than shift stop then use next days as date for shift stop time
     */
    if (shiftStartTime > shiftStopTime) {
      shiftStopTime = new Date(
        new Date(shiftStopTime).setDate(shiftStopTime?.getDate() + 1)
      );
    }

    /**
     * for first iteration in loop use the startdate as shift start for further calculation
     * otherwise use whaterver the daily shift start is
     */
    const startTimeOfTheDay = counter === 0 ? startDate : shiftStartTime;

    // total shift duration without any downtime
    let totalShiftDuration = (shiftStopTime - startTimeOfTheDay) / 60000;

    const newStopDate = new Date(
      new Date(startTimeOfTheDay).setHours(
        startTimeOfTheDay.getHours(),
        startTimeOfTheDay.getMinutes() + totalMinsRequired,
        0,
        0
      )
    );

    const stopTimeOfTheDay =
      totalShiftDuration > totalMinsRequired ? newStopDate : shiftStopTime;

    // only run following condition
    if (!checkIfHoliday(currDate, holidays)) {
      // timed downtimes section
      const timedDowntimes = applicableDowntimes?.filter(
        (dt) => dt.type === 'time'
      );

      // array to store all time downtimes for priority check in interval and quantity downtimes check
      let timedDowntimesArrForCheck = [];

      let minsForRef = 0;

      // run for each timmed breaktime
      timedDowntimes.forEach((tdt) => {
        const breakTimes = tdt?.time?.split(':');
        let breakStart = null;
        if (+breakTimes[0] < 12) {
          // if break is between 0-12 AM set start date equivalent to that of shift stop
          breakStart = new Date(
            new Date(shiftStopTime).setHours(
              +breakTimes[0],
              +breakTimes[1],
              0,
              0
            )
          );
        } else {
          breakStart = new Date(
            new Date(shiftStartTime).setHours(
              +breakTimes[0],
              +breakTimes[1],
              0,
              0
            )
          );
        }

        // calculate breakstop by adding duration to break start
        const breakStop = new Date(
          new Date(breakStart).setHours(
            breakStart.getHours(),
            breakStart.getMinutes() + +tdt?.duration,
            0,
            0
          )
        );

        timedDowntimesArrForCheck.push({
          breakStart,
          breakStop,
          duration: +tdt?.duration,
        });

        // if break time is in between shift then substract from total shift duration
        if (startTimeOfTheDay <= breakStart && stopTimeOfTheDay >= breakStop) {
          totalShiftDuration = totalShiftDuration - +tdt?.duration;
          minsForRef += +tdt?.duration;
          expectedPlannedDownTime += +tdt?.duration;
        }
      });

      // interval downtimes section
      const intervalDowntime = applicableDowntimes?.find(
        (dt) => dt.type === 'interval'
      );

      if (intervalDowntime) {
        const {
          duration: durationIDT,
          interval: intervalIDT,
          priority,
        } = intervalDowntime;

        // get batch interval data or planned dt data
        const interval = batchIntervalTime || intervalIDT;
        const duration = batchIntervalDuration || durationIDT;

        // calculate total shift duration for interval downtime calculation
        const totalShiftDurationForIDT =
          (shiftStopTime - startTimeOfTheDay) / 60000;

        let tempStartTime = new Date(startTimeOfTheDay);
        let tempStopTime = new Date(stopTimeOfTheDay);

        const minsForDt = +interval + +duration;
        const repetation = Math.floor(totalShiftDurationForIDT / minsForDt);

        [...Array(repetation)].forEach(() => {
          if (tempStartTime > tempStopTime) return;

          const breakStart = new Date(
            new Date(tempStartTime).setHours(
              tempStartTime.getHours(),
              tempStartTime.getMinutes() + +interval,
              0,
              0
            )
          );

          const breakStop = new Date(
            new Date(tempStartTime).setHours(
              tempStartTime.getHours(),
              tempStartTime.getMinutes() + minsForDt,
              0,
              0
            )
          );

          tempStartTime = breakStop;

          tempStopTime = new Date(
            new Date(tempStopTime).setHours(
              tempStopTime.getHours(),
              tempStopTime.getMinutes() + duration,
              0,
              0
            )
          );

          const isConflict = timedDowntimesArrForCheck?.find(
            (elem) =>
              elem.breakStart <= breakStart && elem.breakStop >= breakStart
          );

          if (
            startTimeOfTheDay <= breakStart &&
            shiftStopTime >= breakStop &&
            totalMinsRequired > +interval
          ) {
            if (!isConflict) {
              totalShiftDuration = totalShiftDuration - +duration;
              minsForRef += +duration;
              expectedPlannedDownTime += +duration;
            } else if (priority !== 'skip') {
              totalShiftDuration = totalShiftDuration - +duration;
              minsForRef += +duration;
              expectedPlannedDownTime += +duration;
            }
          }
        });
      }

      // quantity downtimes section
      const quantityDowntime = applicableDowntimes?.find(
        (dt) => dt.type === 'quantity'
      );

      if (quantityDowntime && batchSize > batchQtyCount) {
        const {
          quantity: quantityQDT,
          duration: durationQDT,
          priority,
        } = quantityDowntime;
        const quantity = +batchQtyCount || +quantityQDT;
        const duration = +batchQtyDuration || +durationQDT;

        expectedPlannedDownTime = expectedPlannedDownTime + duration;

        const repetation = Math.floor(batchSize / quantity);

        let tempStartTime = new Date(startTimeOfTheDay);
        let tempStopTime = new Date(stopTimeOfTheDay);

        [...Array(repetation)].forEach(() => {
          if (tempStartTime > tempStopTime) return;

          const minsRequiredForQuantity = quantity / (itemsPerHour / 60);

          const breakStart = new Date(
            new Date(tempStartTime).setHours(
              tempStartTime.getHours(),
              tempStartTime.getMinutes() + +minsRequiredForQuantity,
              0,
              0
            )
          );

          const breakStop = new Date(
            new Date(tempStartTime).setHours(
              tempStartTime.getHours(),
              tempStartTime.getMinutes() + minsRequiredForQuantity + duration,
              0,
              0
            )
          );

          tempStartTime = breakStop;

          tempStopTime = new Date(
            new Date(tempStopTime).setHours(
              tempStopTime.getHours(),
              tempStopTime.getMinutes() + duration,
              0,
              0
            )
          );

          const isConflict = timedDowntimesArrForCheck?.find(
            (elem) =>
              elem.breakStart <= breakStart && elem.breakStop >= breakStart
          );

          if (startTimeOfTheDay <= breakStart && shiftStopTime >= breakStop) {
            if (!isConflict) {
              totalShiftDuration = totalShiftDuration - +duration;
              minsForRef += +duration;
              expectedPlannedDownTime += +duration;
            } else if (priority !== 'skip') {
              totalShiftDuration = totalShiftDuration - +duration;
              minsForRef += +duration;
              expectedPlannedDownTime += +duration;
            }
          }
        });
      }

      if (totalMinsRequired > 0 && totalShiftDuration > totalMinsRequired) {
        let newStopDate;
        if (counter === 0) {
          newStopDate = new Date(
            new Date(startTimeOfTheDay).setHours(
              startTimeOfTheDay.getHours(),
              startTimeOfTheDay.getMinutes() + (totalMinsRequired + minsForRef),
              0,
              0
            )
          );
        } else {
          newStopDate = new Date(
            new Date(startTimeOfTheDay).setHours(
              startTimeOfTheDay.getHours(),
              startTimeOfTheDay.getMinutes() + totalMinsRequired,
              0,
              0
            )
          );
        }

        const stopDateCheck = timedDowntimesArrForCheck.find(
          (elem) =>
            elem.breakStart <= newStopDate && elem.breakStop >= newStopDate
        );

        if (stopDateCheck) {
          const diff = (newStopDate - stopDateCheck?.breakStart) / 60000;

          newStopDate = new Date(
            new Date(stopDateCheck.breakStop).setHours(
              stopDateCheck.breakStop.getHours(),
              stopDateCheck.breakStop.getMinutes() + diff,
              0,
              0
            )
          );
        }

        stopDate = newStopDate;
      }

      totalDurationInMins += minsForRef;
      totalMinsRequired = totalMinsRequired - totalShiftDuration;
    }
    // increment counter to change the date
    counter += 1;
  }

  const duration = convertMinsToHrs(totalDurationInMins);

  return {
    startDate,
    stopDate,
    duration,
    expectedPlannedDownTime,
    durationInMins: totalDurationInMins,
  };
};

/**
 * Function to generate stopdate of the batch
 *
 * @param {string} value start date of batch
 * @param {object} batch batch details object
 * @param {object} defaultParam default param object from Store
 * @param {object} goalsTable goalstable object to modify
 * @param {boolean} isMultiProcess boolean value to determine if process is multiprocess or not
 * @param {object[]} applicableDowntime array of all applicable downtime
 * @returns modified goalstable data
 */
export const generateStopDateInhouse = (
  value,
  batch,
  defaultParam,
  goalsTable,
  isMultiProcess,
  applicableDowntime
) => {
  let newApplicableDowntime = [];
  if (applicableDowntime) {
    newApplicableDowntime = applicableDowntime;
  } else {
    const appdtsIds = goalsTable?.applicableDowntime?.map((i) => i.value);

    newApplicableDowntime = defaultParam?.plannedDowntimes?.filter((i) =>
      appdtsIds?.includes(i._id)
    );
  }

  if (isMultiProcess) {
    let newGoalsTable = goalsTable;

    let newValue = value;
    let startD = '';
    let stopD = '';
    let totalDuration = 0;

    batch?.subProcessData?.forEach((subProcess, sIdx) => {
      const {
        startDate,
        stopDate,
        duration,
        expectedPlannedDownTime,
        durationInMins,
      } = genStopDate(
        newValue,
        subProcess,
        defaultParam,
        newApplicableDowntime
      );

      if (sIdx === 0) startD = startDate;
      stopD = stopDate;

      const [hours, minutes] = duration?.split(':');

      totalDuration += durationInMins;

      const [proHours, proMinutes] =
        convertMinsToHrs(totalDuration)?.split(':');

      newGoalsTable = {
        ...newGoalsTable,
        tableData: newGoalsTable.tableData?.map((item) => {
          if (+item?.batchNo === +batch?.batchNo) {
            return {
              ...item,
              startDate: value,
              stopDate: generateDateTimeStringProduction(stopDate),
              duration: `${proHours}h ${proMinutes}m`,
              subProcessData: item?.subProcessData?.map((sub, sIndex) => {
                if (sIndex === sIdx) {
                  return {
                    ...sub,
                    startDate: generateDateTimeStringProduction(startDate),
                    stopDate: generateDateTimeStringProduction(stopDate),
                    duration: `${hours}h ${minutes}m`,
                    expectedPlannedDownTime,
                  };
                  // return { ...sub, startDate: +value };
                }
                return sub;
              }),
            };
          }
          return item;
        }),
      };

      let newStopDate = new Date(
        new Date(stopDate).setHours(
          stopDate.getHours(),
          stopDate.getMinutes() + +(subProcess?.changeOverTime || 0),
          0,
          0
        )
      );

      const { shiftStart, shiftStop, holidays, customShiftTimings } =
        defaultParam;

      let start = shiftStart.split(':');
      let stop = shiftStop.split(':');

      const customShift = customShiftTimings?.find(
        (cst) => cst.date === generateDateStringProduction(newStopDate)
      );

      if (customShift) {
        start = customShift?.stop.split(':');
        stop = customShift?.stop?.split(':');
      }

      const checkStartDate = new Date(
        new Date(newStopDate).setHours(+start[0], +start[1], 0, 0)
      );

      const checkStopDate = new Date(
        new Date(newStopDate).setHours(+stop[0], +stop[1], 0, 0)
      );

      if (checkStopDate < newStopDate && newStopDate < checkStartDate) {
        let counter = 0;
        let flag = true;

        while (flag) {
          const checkDate = new Date(
            new Date(newStopDate).setDate(newStopDate.getDate() + counter)
          );
          const isHoliday = checkIfHoliday(checkDate, holidays);

          if (!isHoliday) {
            let newStart = defaultParam?.shiftStart?.split(':');
            const customShift = customShiftTimings?.find(
              (cst) => cst.date === generateDateStringProduction(checkDate)
            );

            if (customShift) {
              newStart = customShift?.start?.split(':');
            }

            newStopDate = new Date(
              new Date(checkDate).setHours(+newStart[0], +newStart[1], 0, 0)
            );
            flag = false;
          }
          counter += 1;
        }
      }

      newValue = generateDateTimeStringProduction(newStopDate);
    });

    return [
      newGoalsTable,
      {
        startDate: generateDateTimeStringProduction(startD),
        stopDate: generateDateTimeStringProduction(stopD),
      },
    ];
  } else {
    const { startDate, stopDate, duration, expectedPlannedDownTime } =
      genStopDate(value, batch, defaultParam, newApplicableDowntime);

    const [hours, minutes] = duration?.split(':');

    return [
      {
        ...goalsTable,
        tableData: goalsTable.tableData?.map((item) => {
          if (+item?.batchNo === +batch?.batchNo) {
            return {
              ...item,
              startDate: value,
              stopDate: generateDateTimeStringProduction(stopDate),
              duration: `${hours}h ${minutes}m`,
              expectedPlannedDownTime: expectedPlannedDownTime,
            };
          }
          return item;
        }),
      },
      {
        startDate: generateDateTimeStringProduction(startDate),
        stopDate: generateDateTimeStringProduction(stopDate),
      },
    ];
  }
};

/**
 * Function to generate stopdates for all process in a paticular batch
 *
 * @param {object[]} processes array of all processes in production flow
 * @param {object[]} batchData array of all batch details of particular batch
 * @param {object[]} goalsTable array of all goalstable
 * @param {object} defaultParam default param object from Store
 * @param {boolean} isSequential boolean value to to determine if process is sequential or parallel
 * @returns array of modified goals tables
 */
export const generateStopDateForAll = async (
  value,
  processes,
  batchData,
  goalsTable,
  defaultParam,
  isSequential
) => {
  // varilate to store return data
  let arrToReturn = [];
  // variable to store next process date
  let refDateForNextProcess = '';
  // process table data to be displayed inside the promt
  let processTable = [];

  // loop over all process to genrate stop date
  for (let i = 0; i < processes?.length; i++) {
    const process = processes?.[i];
    const currBatch = batchData?.[i];
    const currGoalsTable = goalsTable?.find((gt) => gt.flowId === process._id);

    const isProcessOutSource = process?.processCategory === 'Outsource';

    // stop date generation for inhouse process
    if (process.processCategory === 'Inhouse') {
      const [tempGt, dates] = generateStopDateInhouse(
        i === 0 ? value : refDateForNextProcess,
        currBatch,
        defaultParam,
        currGoalsTable,
        process?.isMultiProcess
      );

      let bufTime = 0;
      let iph = 1;

      // get buffer time(bufTime) and items per hour(iph) according to process
      if (process?.isMultiProcess) {
        bufTime =
          currBatch?.subProcessData?.[currBatch?.subProcessData?.length - 1]
            ?.bufferTime || 0;
        iph =
          currBatch?.subProcessData?.[currBatch?.subProcessData?.length - 1]
            ?.itemsPerHour || 0;
      } else {
        bufTime = currBatch?.bufferTime || 0;
        iph = currBatch?.itemsPerHour || 0;
      }

      // calculate cycle time using items per hour
      const cycleTime = Math.round(60 / iph);

      // add buffer time and cycle time to stopdate genrated from generateStopDateInhouse function
      let newDate = addMinutesToDate(
        isSequential || isProcessOutSource ? dates?.stopDate : dates?.startDate,
        bufTime + cycleTime
      );

      // check if date from addMinutesToDate is not in between downtime or out of shift time and return a valid date time for next process
      const generatedDate = generateValidDate(
        newDate,
        defaultParam,
        currGoalsTable?.applicableDowntime
      );

      // set start date for next process
      refDateForNextProcess = generateDateTimeStringProduction(generatedDate);

      processTable.push({ ...dates, name: process.processName });

      arrToReturn.push(tempGt);
    } else {
      // for processes other than inhouse
      const [startDate, stopDate] = await plannerPrompt(
        `Please enter dates for ${process?.processName}`,
        i === 0 ? value : refDateForNextProcess,
        null, // argument in case we need to suggest stop date for user
        processTable
      );

      // modify goals table using value from promt given by the user.
      let tempGt = {
        ...currGoalsTable,
        tableData: currGoalsTable?.tableData?.map((tdt) => {
          if (tdt?.batchNo === currBatch?.batchNo) {
            return { ...tdt, startDate, stopDate };
          }
          return tdt;
        }),
      };

      // add buffer time to stopdate genrated from generateStopDateInhouse function
      let newDate = addMinutesToDate(
        isSequential || isProcessOutSource ? stopDate : startDate,
        currBatch?.bufferTime || 0
      );

      // check if date from addMinutesToDate is not in between downtime or out of shift time and return a valid date time for next process
      const generatedDate = generateValidDate(
        newDate,
        defaultParam,
        currGoalsTable?.applicableDowntime
      );

      // set start date for next process
      refDateForNextProcess = generateDateTimeStringProduction(generatedDate);

      processTable.push({ startDate, stopDate, name: process.processName });

      arrToReturn.push(tempGt);
    }
  }

  return arrToReturn;
};

// checks if items per hour exists in all inhouse processes
export const itemsPerHourCheck = (batchData, processes) => {
  return batchData?.every((item, idx) => {
    const process = processes[idx];

    if (process.processCategory !== 'Inhouse') {
      return true;
    } else {
      if (process?.isMultiProcess) {
        return item?.subProcessData?.every((elem) => !!elem.itemsPerHour);
      } else {
        return !!item.itemsPerHour;
      }
    }
  });
};

export const generateMachineStopDate = (
  data,
  value,
  mac,
  customMachineTimes = [],
  holidays
) => {
  const { target, speed } = data;
  const { defaultStartTime, defaultStopTime } = mac;

  let totalMinsRequired = target / (speed / 60);

  let counter = 0;
  let machineStartTime;
  let machineStopTime;

  const startDate = new Date(value);

  let stopDate;

  // keep looping till totalMinsRequired is <= 0
  while (totalMinsRequired > 0) {
    // current date for reference genereated according to selected start date
    const currDate = new Date(
      new Date(new Date(value).setDate(startDate.getDate() + counter)).setHours(
        0,
        0,
        0,
        0
      )
    );
    // initial machine details
    let machineStart = defaultStartTime?.split(':');
    let machineStop = defaultStopTime?.split(':');
    const customTime = customMachineTimes?.find(
      (cst) => cst.date === generateDateStringProduction(currDate)
    );

    // if custom machine exists change initial machine details
    if (customTime) {
      machineStart = customTime?.startTime?.split(':');
      machineStop = customTime?.stopTime?.split(':');
    }

    // machine date and time
    if (counter === 0 && startDate.getHours() < 12) {
      // sets machine time to previous day if start date in between 0-12 AM
      machineStartTime = new Date(
        new Date(new Date(startDate).setDate(startDate.getDate() - 1)).setHours(
          +machineStart[0],
          +machineStart[1],
          0,
          0
        )
      );
    } else {
      machineStartTime = new Date(
        new Date(new Date(generateDateStringProduction(currDate))).setHours(
          +machineStart[0],
          +machineStart[1],
          0,
          0
        )
      );
    }
    machineStopTime = new Date(
      new Date(new Date(generateDateStringProduction(currDate))).setHours(
        +machineStop[0],
        +machineStop[1],
        0,
        0
      )
    );

    /**
     * if machine start is greater than machine stop then use next days as date for machine stop time
     */
    if (machineStartTime > machineStopTime) {
      machineStopTime = new Date(
        new Date(machineStopTime).setDate(machineStopTime?.getDate() + 1)
      );
    }

    /**
     * for first iteration in loop use the startdate as machine start for further calculation
     * otherwise use whaterver the daily machine start is
     */
    const startTimeOfTheDay = counter === 0 ? startDate : machineStartTime;

    // console.log(startTimeOfTheDay);

    if (!checkIfHoliday(currDate, holidays)) {
      // total machine duration without any downtime
      let totalMachineDuration = (machineStopTime - startTimeOfTheDay) / 60000;

      if (totalMinsRequired > 0 && totalMachineDuration > totalMinsRequired) {
        let newStopDate;
        if (counter === 0) {
          newStopDate = new Date(
            new Date(startTimeOfTheDay).setHours(
              startTimeOfTheDay.getHours(),
              startTimeOfTheDay.getMinutes() + totalMinsRequired,
              0,
              0
            )
          );
        } else {
          newStopDate = new Date(
            new Date(startTimeOfTheDay).setHours(
              startTimeOfTheDay.getHours(),
              startTimeOfTheDay.getMinutes() + totalMinsRequired,
              0,
              0
            )
          );
        }

        stopDate = newStopDate;
      }

      totalMinsRequired -= totalMachineDuration;
    }

    counter += 1;
  }

  return {
    startDate: value,
    stopDate: generateDateTimeStringProduction(stopDate),
  };
};
