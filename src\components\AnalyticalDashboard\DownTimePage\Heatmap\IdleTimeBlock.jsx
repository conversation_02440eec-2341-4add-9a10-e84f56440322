import { useState } from 'react';

const IdleTimeBlock = ({ data, kpiParam }) => {
  const [hovered, setHovered] = useState(false);
  const date = new Date(data?.data?.createdAt);

  const hrs = date?.getHours();
  const mins = date.getMinutes();

  return (
    <>
      {hovered && (
        <div
          className="absolute shadow-basic bg-red-primary -translate-x-full text-white rounded-xl bottom-[110%]"
          style={{
            left: data.left || 0,
            width: data.width > 150 ? data.width : 150,
          }}
        >
          <h4 className="px-5 py-1 border-b text-center">
            {data?.tag?.name || 'Idle Time'}
          </h4>
          <div className="flex flex-col text-sm px-5 py-2 gap-y-1 gap-x-2">
            <span>
              Time:{' '}
              {`${+hrs < 10 ? '0' : ''}${hrs}:${+mins < 10 ? '0' : ''}${mins}`}
            </span>
            <span>Duration: {+data?.data?.[kpiParam?.toUpperCase()]}</span>
          </div>
        </div>
      )}
      <section
        className="absolute h-full bg-red-primary -translate-x-full hover:cursor-pointer hover:shadow-basic hover:z-[100]"
        style={{
          left: data.left || 0,
          width: data.width || 0,
        }}
        onMouseOver={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      ></section>
    </>
  );
};

export default IdleTimeBlock;
