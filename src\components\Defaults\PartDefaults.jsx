import Input from '../global/components/Input';

function PartDefaults({ defaults, setDefaults }) {
  //   const [partIdLabel, setPartIdLabel] = useState(
  //     defaults?.projectDefaults?.projectIdentifier ?? ''
  //   );

  return (
    <div className="h-vh bg-white">
      <h3 className="text-gray-subHeading">Inventory Defaults:</h3>
      <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-x-5 gap-y-5  w-full">
        <div>
          <h5 className="mb-2 text-gray-label">Part Id Label:</h5>
          <Input
            value={defaults?.inventoryIdDefaults?.partDefaults}
            onChange={(e) => {
              // setDefaults((prev) => {
              //   return { ...prev, partDefaults: e.target.value };
              // });
              setDefaults((prev) => ({
                ...prev,
                inventoryIdDefaults: {
                  ...prev.inventoryIdDefaults,
                  partDefaults: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">Product Id Label:</h5>

          <Input
            value={defaults?.inventoryIdDefaults?.productDefaults}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryIdDefaults: {
                  ...prev.inventoryIdDefaults,
                  productDefaults: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">SubAssembly Id Label:</h5>

          <Input
            value={defaults?.inventoryIdDefaults?.subAssemblyDefaults}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryIdDefaults: {
                  ...prev.inventoryIdDefaults,
                  subAssemblyDefaults: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">Vendor Id Label:</h5>

          <Input
            value={defaults?.inventoryIdDefaults?.vendorDefaults}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryIdDefaults: {
                  ...prev.inventoryIdDefaults,
                  vendorDefaults: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">Store Id Label:</h5>

          <Input
            value={defaults?.inventoryIdDefaults?.storeDefaults}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryIdDefaults: {
                  ...prev.inventoryIdDefaults,
                  storeDefaults: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">DropDown Id Label:</h5>

          <Input
            value={defaults?.inventoryIdDefaults?.dropDownDefaults}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryIdDefaults: {
                  ...prev.inventoryIdDefaults,
                  dropDownDefaults: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">OutPage Custom Id Label:</h5>

          <Input
            value={defaults?.inventoryIdDefaults?.outPageCustomId}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryIdDefaults: {
                  ...prev.inventoryIdDefaults,
                  outPageCustomId: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-1 text-gray-label">QR Validation:</h5>
          <input
            type="checkbox"
            checked={defaults?.qrValidation}
            className="mt-4 ml-10"
            onChange={(e) => {
              setDefaults((prev) => {
                return { ...prev, qrValidation: e.target.checked };
              });
            }}
          />
        </div>
        <div>
          <h5 className="mb-1 text-gray-label">FIFO/LIFO Validation:</h5>
          <input
            type="checkbox"
            checked={defaults?.fifo_lifo_validation}
            className="mt-4 ml-10"
            onChange={(e) => {
              setDefaults((prev) => {
                return { ...prev, fifo_lifo_validation: e.target.checked };
              });
            }}
          />
        </div>
        <div>
          <h5 className="mb-1 text-gray-label">Check Attendance:</h5>
          <input
            type="checkbox"
            checked={defaults?.checkAttendance}
            className="mt-4 ml-10"
            onChange={(e) => {
              setDefaults((prev) => {
                return { ...prev, checkAttendance: e.target.checked };
              });
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default PartDefaults;
