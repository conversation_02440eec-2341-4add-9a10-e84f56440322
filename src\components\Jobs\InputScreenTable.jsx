import { useNavigate } from 'react-router-dom';
import HideIcon from '../../assets/images/hide.png';
import { useDeleteInputScreenMutation } from '../../slices/inputScreenApiSlice';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import Table from '../global/components/Table';

const InputScreenTable = ({
  setHideInputScreensTable,
  inputScreens,
  isFromTemplate = false,
}) => {
  const navigate = useNavigate();

  const [deleteInputScreen] = useDeleteInputScreenMutation();

  // function to edit the input screen

  //Don't remove this comment
  // const handleEdit = (data) => {
  //   navigate(
  //     `/settings/create/jobtemplate/edit-${data.id}${isFromTemplate ? '?fromTemplate=true' : ''}`
  //   );
  // };

  // function to copy the input screen
  const handleCopy = (data) => {
    navigate(
      `/settings/create/jobtemplate/copy-${data.id}${isFromTemplate ? '?fromTemplate=true' : ''}`
    );
  };

  // function to delete the input screen
  const handleDelete = async (id, name) => {
    if (
      await customConfirm(`Are you sure you want to delete ${name}?`, 'delete')
    ) {
      await deleteInputScreen({ id }).unwrap();
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-5 px-2">
        <h2 className="font-bold">Job Instructions </h2>
        {!isFromTemplate && (
          <Button
            onClick={() => setHideInputScreensTable(true)}
            size="sm"
            className="!px-4"
          >
            <img
              src={HideIcon}
              alt="Hide Icon"
              className="w-5 h-5 object-contain relative"
            />
            Hide templates
          </Button>
        )}
      </div>
      <Table>
        <Table.Head>
          <Table.Row>
            <Table.Th>#</Table.Th>
            <Table.Th>Name</Table.Th>
            <Table.Th></Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {inputScreens?.map((screen, idx) => (
            <Table.Row key={screen.id}>
              <Table.Td>{idx + 1}</Table.Td>
              <Table.Td>{screen.name}</Table.Td>
              <Table.Options
                className={'bg-white'}
                // onEdit={() => handleEdit(screen)}
                onCopy={() => handleCopy(screen)}
                onDelete={() => handleDelete(screen.id, screen.name)}
              />
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
};

export default InputScreenTable;
