import {
  DashboardOutlined,
  SecurityScanOutlined,
  ShopOutlined,
  TableOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Col,
  Divider,
  Form,
  Input,
  Modal,
  Row,
  Space,
  Typography,
} from 'antd';
import { useState } from 'react';
import {
  generateDateString,
  unCamelCaseString,
} from '../../../../helperFunction';
import MultiSelect from '../MultiSelect';
import SelectV2 from '../SelectV2';
import ColumnAccess from './ColumnAccess';
import StoreAccess from './StoreAccess';

const { Title, Text } = Typography;

const UserModal = ({
  openModal,
  title,
  onClose,
  onSubmit,
  userRole,
  inputData,
  inputChangeHandler,
  profiles,
  isEdit,
  filteredDepartments,
  roles,
  isSuperuser,
  setShowMore,
  showMore,
  setInputData,
  mqttData,
  allSlugs,
  APPROVAL_ACCESS,
  selectedDepartments,
  getAllColumns,
}) => {
  const [form] = Form.useForm();
  const [selectedTab, setSelectedTab] = useState('General');
  const [loading, setLoading] = useState(false);

  const tabs = [
    { name: 'General', id: 1, icon: <UserOutlined /> },
    { name: 'Page Access', id: 2, icon: <SecurityScanOutlined /> },
    { name: 'Kanban Info', id: 3, icon: <DashboardOutlined /> },
    { name: 'Column Access', id: 4, icon: <TableOutlined /> },
    { name: 'Store Access', id: 5, icon: <ShopOutlined /> },
  ];

  const handleTabChange = (tabName) => setSelectedTab(tabName);

  const handleValuesChange = (_, allValues) => {
    setInputData((prev) => ({ ...prev, ...allValues }));
  };

  const handleSelectAll = (checked) => {
    setInputData((prev) => ({
      ...prev,
      pageAccess: checked ? allSlugs : [],
      approvalAccess: checked
        ? [...APPROVAL_ACCESS.map((i) => i.slug), 'isEditableKanban']
        : [],
      isKanbanAccess: checked,
    }));
  };

  const handleCheckboxChange = (key, checked, field) => {
    setInputData((prev) => ({
      ...prev,
      [field]: checked
        ? [...(prev[field] || []), key]
        : prev[field].filter((i) => i !== key),
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onSubmit(e);
    } finally {
      setLoading(false);
    }
  };

  const renderGeneralTab = () => (
    <div className="space-y-6">
      <div className="mb-6">
        <Title level={4} className="!mb-2">
          Basic Information
        </Title>
        <Text type="secondary">Configure user details and authentication</Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={inputData}
        onValuesChange={handleValuesChange}
      >
        <Row gutter={[24, 16]}>
          {userRole === 'superuser' && (
            <Col span={24}>
              <Form.Item label="Select Profile" name="profileId">
                <SelectV2
                  id="profileId"
                  value={inputData.profileId || ''}
                  onChange={inputChangeHandler}
                  name="profileId"
                  placeholder="Select Profile ID"
                  options={profiles.map((pro) => ({
                    label: pro.name,
                    value: pro._id,
                    disabled: !!pro?.assigned,
                  }))}
                />
              </Form.Item>
            </Col>
          )}

          <Col xs={24} md={12}>
            <Form.Item label="Employee ID" name="employeeId">
              <Input placeholder="Enter employee ID" />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              label="Full Name"
              name="name"
              rules={[{ required: true, message: 'Name is required' }]}
            >
              <Input placeholder="Enter full name" />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              label="Email Address"
              name="email"
              rules={[
                {
                  required: true,
                  type: 'email',
                  message: 'Valid email is required',
                },
              ]}
            >
              <Input placeholder="Enter email address" />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item label="Mobile Number" name="mobile">
              <Input placeholder="Enter mobile number" />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              label="Role"
              name="role"
              rules={[{ required: true, message: 'Role is required' }]}
            >
              <SelectV2
                options={roles
                  ?.filter((r) => r !== 'superuser')
                  .map((role) => ({ label: role, value: role }))}
                placeholder="Select role"
                value={inputData.role || ''}
                onChange={inputChangeHandler}
                name="role"
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <MultiSelect
              options={mqttData.map((mqtt) => ({
                label: mqtt.process,
                value: mqtt._id,
              }))}
              placeholder="Select processes"
              name="processAccess"
              value={inputData?.processAccess || []}
              onChange={inputChangeHandler}
            />
          </Col>

          {isEdit && (
            <Col xs={24} md={12}>
              <Form.Item
                label={isSuperuser ? 'Master Password' : 'Current Password'}
                name={isSuperuser ? 'masterpassword' : 'currpassword'}
              >
                <Input.Password placeholder="Enter current password" />
              </Form.Item>
            </Col>
          )}

          <Col xs={24} md={12}>
            <Form.Item
              label="New Password"
              name="password"
              rules={[
                {
                  required: true,
                  min: 8,
                  message: 'Password must be at least 8 characters',
                },
              ]}
            >
              <Input.Password placeholder="Enter password" />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              label="Confirm Password"
              name="confirm"
              rules={[
                { required: true, message: 'Confirm password is required' },
              ]}
            >
              <Input.Password placeholder="Confirm password" />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item label="RFID" name="rfid">
              <Input placeholder="Enter RFID number" />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item label="RFID Expiry" name="rfidExpiryDate">
              <Input
                type="date"
                value={
                  isEdit
                    ? generateDateString(inputData.rfidExpiryDate)
                    : undefined
                }
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <div className="flex items-center justify-between mb-4">
          <Text
            className="text-blue-600 cursor-pointer hover:text-blue-800"
            onClick={() => setShowMore((prev) => !prev)}
          >
            {showMore
              ? '− Hide Additional Details'
              : '+ Show Additional Details'}
          </Text>
        </div>

        {showMore && (
          <Row gutter={[24, 16]} className="p-4 bg-gray-50 rounded-lg">
            <Col xs={24} md={8}>
              <Form.Item label="Designation" name="designation">
                <Input placeholder="Enter designation" />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item label="Contractor Name" name="contractorName">
                <Input placeholder="Enter contractor name" />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item label="Hourly Rate" name="workPerWages">
                <Input type="number" placeholder="Enter hourly rate" />
              </Form.Item>
            </Col>
          </Row>
        )}
      </Form>
    </div>
  );

  const renderPageAccessTab = () => (
    <div className="space-y-6">
      <div className="mb-6">
        <Title level={4} className="!mb-2">
          Access Permissions
        </Title>
        <Text type="secondary">
          Configure department access and page permissions
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={inputData}
        onValuesChange={handleValuesChange}
      >
        <div className="mb-6">
          <MultiSelect
            id="departments"
            value={inputData.departments || []}
            onChange={inputChangeHandler}
            options={filteredDepartments?.map((opt) => ({
              label: opt.name,
              value: opt._id,
            }))}
            name="departments"
            required
            placeholder="Select departments"
          />
        </div>

        <div className="flex justify-end items-center mb-6">
          <Checkbox
            checked={
              inputData?.pageAccess?.length === allSlugs?.length &&
              inputData?.approvalAccess?.includes('isEditableKanban') &&
              inputData?.isKanbanAccess
            }
            onChange={(e) => handleSelectAll(e.target.checked)}
          >
            Select All
          </Checkbox>
        </div>

        <div className="space-y-4">
          {selectedDepartments?.map((dep) => (
            <div
              key={dep._id}
              className="border border-gray-200 rounded-lg p-4"
            >
              <Title level={5} className="!mb-3 text-gray-800">
                {dep.name}
              </Title>
              {dep?.navs?.map((nav) => (
                <div key={nav._id} className="mb-4">
                  <Text strong className="text-gray-700 mb-2 block">
                    {nav.name}
                  </Text>
                  <Row gutter={[16, 8]}>
                    {nav?.childNavs?.map((cnav) => (
                      <Col xs={24} sm={12} md={8} key={cnav._id}>
                        <Checkbox
                          checked={inputData?.pageAccess?.includes(cnav.cslug)}
                          onChange={(e) =>
                            handleCheckboxChange(
                              cnav.cslug,
                              e.target.checked,
                              'pageAccess'
                            )
                          }
                        >
                          {cnav.cname}
                        </Checkbox>
                      </Col>
                    ))}
                  </Row>
                </div>
              ))}
            </div>
          ))}

          <div className="border border-orange-200 rounded-lg p-4 bg-orange-50">
            <Title level={5} className="!mb-3 text-orange-800">
              Approval Access
            </Title>
            <Row gutter={[16, 8]}>
              {APPROVAL_ACCESS?.filter((i) =>
                inputData?.pageAccess?.includes(i.slug)
              ).map((item) => (
                <Col xs={24} sm={12} md={8} key={item.slug}>
                  <Checkbox
                    checked={inputData?.approvalAccess?.includes(item.slug)}
                    onChange={(e) =>
                      handleCheckboxChange(
                        item.slug,
                        e.target.checked,
                        'approvalAccess'
                      )
                    }
                  >
                    {unCamelCaseString(item.value)}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          </div>

          <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
            <Title level={5} className="!mb-3 text-blue-800">
              Kanban Access
            </Title>
            <Checkbox
              checked={inputData?.isKanbanAccess}
              onChange={(e) =>
                setInputData((prev) => ({
                  ...prev,
                  isKanbanAccess: e.target.checked,
                }))
              }
            >
              Enable Kanban Access
            </Checkbox>
          </div>

          <div className="border border-red-200 rounded-lg p-4 bg-red-50">
            <Title level={5} className="!mb-3 text-red-800">
              Edit/Delete Permissions
            </Title>
            <Row gutter={[16, 8]}>
              {[
                'deletePendingStockIn',
                'archivePartAccess',
                'canDeleteInpage',
                'canEditQuotation',
                'canEditSalesOrder',
                'canEditIndent',
                'canEditPurchaseOrder',
                'canEditRequestForQuotation',
                'canDeleteQuotation',
                'canDeleteSalesOrder',
                'canDeleteIndent',
                'canDeleteRequestForQuotation',
                'canDeletePurchaseOrder',
              ].map((key) => (
                <Col xs={24} sm={12} md={8} key={key}>
                  <Checkbox
                    checked={inputData[key]}
                    onChange={(e) =>
                      setInputData((prev) => ({
                        ...prev,
                        [key]: e.target.checked,
                      }))
                    }
                  >
                    {unCamelCaseString(key)}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          </div>

          {/* edit permissions for approved documents */}
          <div className="border border-green-200 rounded-lg p-4 bg-green-50">
            <Title level={5} className="!mb-3 text-green-800">
              Edit Permissions for Approved Documents
            </Title>
            <Row gutter={[16, 8]}>
              {[
                'canEditApprovedQuotation',
                'canEditApprovedSalesOrder',
                'canEditApprovedPurchaseOrder',
                'canEditApprovedIndent',
                'canEditApprovedRequestForQuotation',
              ].map((key) => (
                <Col xs={24} sm={12} md={8} key={key}>
                  <Checkbox
                    checked={inputData[key]}
                    onChange={(e) =>
                      setInputData((prev) => ({
                        ...prev,
                        [key]: e.target.checked,
                      }))
                    }
                  >
                    {unCamelCaseString(key)}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          </div>

          <Row gutter={16}>
            <Col xs={24} md={12}>
              <div className="border border-green-200 rounded-lg p-4 bg-green-50">
                <Title level={5} className="!mb-3 text-green-800">
                  Sales Order Archive
                </Title>
                <Checkbox
                  checked={inputData?.archiveSalesOrderManagement}
                  onChange={(e) =>
                    setInputData((prev) => ({
                      ...prev,
                      archiveSalesOrderManagement: e.target.checked,
                    }))
                  }
                >
                  Enable Archive Access
                </Checkbox>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="border border-purple-200 rounded-lg p-4 bg-purple-50">
                <Title level={5} className="!mb-3 text-purple-800">
                  Purchase Order Archive
                </Title>
                <Checkbox
                  checked={inputData?.archivePurchaseOrderManagement}
                  onChange={(e) =>
                    setInputData((prev) => ({
                      ...prev,
                      archivePurchaseOrderManagement: e.target.checked,
                    }))
                  }
                >
                  Enable Archive Access
                </Checkbox>
              </div>
            </Col>
          </Row>
        </div>
      </Form>
    </div>
  );

  const renderKanbanInfoTab = () => (
    <div className="space-y-6">
      <div className="mb-6">
        <Title level={4} className="!mb-2">
          Kanban Information
        </Title>
        <Text type="secondary">
          Select kanban information categories for this user
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={inputData}
        onValuesChange={handleValuesChange}
      >
        <MultiSelect
          id="kanbanInfo"
          value={inputData.kanbanFilter || []}
          options={getAllColumns()}
          placeholder="Select kanban information categories"
          name="kanbanFilter"
          onChange={inputChangeHandler}
          required
        />

        {inputData.kanbanFilter?.length > 0 && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <Title level={5} className="!mb-3">
              Selected Categories
            </Title>
            <div className="flex flex-wrap gap-2">
              {inputData.kanbanFilter?.map((item, idx) => (
                <span
                  key={idx}
                  className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                >
                  {item}
                </span>
              ))}
            </div>
          </div>
        )}
      </Form>
    </div>
  );

  return (
    <Modal
      open={openModal}
      onCancel={onClose}
      title={<span className="text-lg font-semibold">{title}</span>}
      footer={
        <Space>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            type="primary"
            onClick={(e) => handleSubmit(e)}
            loading={loading}
          >
            {isEdit ? 'Update' : 'Create'}
          </Button>
        </Space>
      }
      width={1200}
      centered
      styles={{
        body: {
          maxHeight: `calc(100vh - 150px)`,
          overflowY: 'auto',
        },
      }}
    >
      <div className="flex">
        {/* Sidebar */}
        <div className="w-52 bg-gray-50 border-r border-gray-200 p-4">
          <div className="space-y-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.name)}
                className={`w-full flex items-center gap-3 px-3 py-1 rounded-lg text-left transition-colors ${
                  selectedTab === tab.name
                    ? 'bg-blue-100 text-gray-700 hover:bg-blue-100 border-r-2 border-blue-600'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <span className="text-lg">{tab.icon}</span>
                <span className="font-medium">{tab.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 max-h-[600px] overflow-y-auto">
          <Form form={form} onFinish={handleSubmit}>
            {selectedTab === 'General' && renderGeneralTab()}
            {selectedTab === 'Page Access' && renderPageAccessTab()}
            {selectedTab === 'Kanban Info' && renderKanbanInfoTab()}
            {selectedTab === 'Column Access' && (
              <ColumnAccess setInputData={setInputData} inputData={inputData} />
            )}
            {selectedTab === 'Store Access' && (
              <StoreAccess setInputData={setInputData} inputData={inputData} />
            )}
          </Form>
        </div>
      </div>
    </Modal>
  );
};

export default UserModal;
