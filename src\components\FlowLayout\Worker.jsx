import { useContext, useEffect, useState } from 'react';
import { ReactComponent as BlankProfile } from '../../assets/svgs/blankProfile.svg';
import {
  calculateValueForViews,
  convertToHrsAndMins,
  getMinutesPassed,
  handlePercentDeicmalValue,
} from '../../helperFunction';
import { MqttContext } from '../../mqtt/DashboardMqttContext';
import { Store } from '../../store/Store';
import ProgressBar from '../global/components/ProgressBar';
import Directions from './Directions';

const getMinutesToMinus = (tempStart, tempStop, idleDatas) => {
  let minsToMinus = 0;

  idleDatas?.forEach((data) => {
    const stopDate = new Date(data?.data?.createdAt);

    const startDate = new Date(
      new Date(data?.data?.createdAt).setHours(
        stopDate.getHours(),
        stopDate?.getMinutes() - +data?.data?.IDLE,
        stopDate?.getSeconds(),
        stopDate.getMilliseconds()
      )
    );

    if (tempStart <= startDate && tempStop >= stopDate) {
      minsToMinus = minsToMinus + +data?.data?.IDLE;
    }
  });

  return minsToMinus;
};

export default function Errors({
  isMobile,
  isTablet,
  data,
  cuProjects,
  nextCuProjects,
  processGoalViews,
  deviceDatas,
  isReversed,
  isLastInRow,
  isLastItem,
}) {
  const [realtimeBatchSize, setRealtimeBatchSize] = useState(0);
  const [newValues, setNewValues] = useState({});
  const [haltData, setHaltData] = useState({ hrs: 0, mins: 0, status: false });
  const [operatorData, setOperatorData] = useState([]);

  const { values } = useContext(MqttContext);
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const cuProject = cuProjects.findLast((i) => i);

  const nextFlowCuPro = nextCuProjects?.findLast((i) => i);

  const isComplete = cuProject?.status === 'complete';

  const batchSizeParam = processGoalViews
    ?.find((item) => item.project.projectId === cuProject?.mqtt?._id)
    ?.parameters?.find((item) => item.name === 'Batch Size');

  const machineList = cuProject?.machineAndOperator?.map(
    (mao) => mao?.machine?.machineId
  );

  const batchData = cuProject?.isMultiProcess
    ? cuProject?.subProcessData
    : cuProject?.batchInfo;

  useEffect(() => {
    if (cuProject?.stopTime && nextFlowCuPro?.startTime) {
      const cuStop = new Date(cuProject?.stopTime);
      const nextCuStart = new Date(nextFlowCuPro?.startTime);

      if (nextCuStart > cuStop) {
        const totalMinutesPassed = getMinutesPassed(
          nextCuStart,
          cuStop,
          defaultParam,
          true
        );

        const [hrs, mins] = convertToHrsAndMins(totalMinutesPassed / 60);
        setHaltData({ hrs, mins, status: true });
      } else {
        setHaltData({ hrs: 0, mins: 0, status: false });
      }
    } else {
      setHaltData({ hrs: 0, mins: 0, status: false });
    }
  }, [cuProject, nextFlowCuPro, defaultParam]);

  useEffect(() => {
    if (batchSizeParam && machineList && newValues) {
      const val = calculateValueForViews(
        batchSizeParam?.formula,
        newValues,
        machineList
      );

      setRealtimeBatchSize(val || 0);
    }
  }, [batchSizeParam, machineList, newValues]);

  useEffect(() => {
    if (cuProject && values) {
      cuProject?.machineAndOperator?.forEach((mao) => {
        const isActive = mao?.status === 'active';

        const machine = mao.machine;

        machine?.devices?.forEach((device) => {
          device?.assignedDevice?.forEach((item) => {
            if (isActive) {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]: values?.[item?.deviceId] || {},
              }));
            } else {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]:
                  cuProject?.lastValues?.[machine?.machineId]?.[
                    item?.deviceId
                  ] || {},
              }));
            }
          });
        });
      });
    }
  }, [cuProject, values]);

  useEffect(() => {
    const idleDatas = deviceDatas?.filter(
      (item) =>
        item?.cuProject === cuProject?._id &&
        item.type === 'kpi' &&
        item.data.hasOwnProperty('IDLE') // eslint-disable-line
    );
    setOperatorData([]);
    if (cuProject) {
      const temp = [];
      const allOps = [];
      const allMacs = [];
      cuProject?.machineAndOperator?.forEach((mao) => {
        allMacs.push({ ...mao?.machine, maoId: mao._id });
        mao?.operator?.forEach((op) => {
          if (!allOps?.find((i) => i._id === op.user._id)) {
            allOps.push(op.user);
          }
        });

        const startOp = mao?.operator?.find((op) => op.action === 'start');
        const stopOp = mao?.operator?.find((op) => op.action === 'stop');
        const pauseOps = mao?.operator?.filter((op) => op.action === 'pause');
        const resumeOps = mao?.operator?.filter((op) => op.action === 'resume');
        const firstPauseOp = pauseOps?.[0];
        const lastResumeOp = resumeOps?.findLast((i) => i);

        if (resumeOps?.length > 0) {
          const timeBetweenStartPause =
            getMinutesPassed(
              new Date(firstPauseOp?.time),
              new Date(startOp?.time),
              defaultParam,
              true
            ) -
            getMinutesToMinus(
              new Date(startOp?.time),
              new Date(firstPauseOp?.time),
              idleDatas?.filter((data) => data?.machine === mao?.machine?._id)
            );

          temp.push({
            machine: mao.machine,
            user: startOp.user,
            time: timeBetweenStartPause,
          });

          resumeOps?.forEach((op, oIdx) => {
            if (oIdx + 1 === resumeOps?.length) return;
            const timeBetweenResumePause =
              getMinutesPassed(
                new Date(pauseOps?.[oIdx + 1]?.time),
                new Date(op?.time),
                defaultParam,
                true
              ) -
              getMinutesToMinus(
                new Date(op?.time),
                new Date(pauseOps?.[oIdx + 1]?.time),
                idleDatas?.filter((data) => data?.machine === mao?.machine?._id)
              );

            temp.push({
              machine: mao.machine,
              user: startOp.user,
              time: timeBetweenResumePause,
            });
          });

          if (lastResumeOp) {
            const timeBetweenResumeStop =
              getMinutesPassed(
                stopOp ? new Date(stopOp?.time) : new Date(),
                new Date(lastResumeOp?.time),
                defaultParam,
                true
              ) -
              getMinutesToMinus(
                new Date(lastResumeOp?.time),
                stopOp ? new Date(stopOp?.time) : new Date(),
                idleDatas?.filter((data) => data?.machine === mao?.machine?._id)
              );

            temp.push({
              machine: mao.machine,
              user: startOp.user,
              time: timeBetweenResumeStop,
            });
          }
        } else {
          const tempStart = new Date(startOp?.time);
          const tempStop = stopOp?.time
            ? new Date(stopOp?.time)
            : pauseOps?.length === 1 && resumeOps?.length === 0
              ? new Date(firstPauseOp?.time)
              : new Date();

          const minsToMinus = getMinutesToMinus(
            tempStart,
            tempStop,
            idleDatas?.filter((data) => data?.machine === mao?.machine?._id)
          );

          const timeBetweenStartStop = getMinutesPassed(
            tempStop,
            tempStart,
            defaultParam,
            true
          );

          temp.push({
            machine: mao.machine,
            user: startOp.user,
            time: timeBetweenStartStop - minsToMinus,
          });
        }
      });

      allMacs?.forEach((mac) => {
        allOps?.forEach((op) => {
          const test = temp.filter(
            (item) => item.user._id === op._id && item.machine._id === mac._id
          );

          if (test?.length > 0) {
            const errorMessages = cuProject?.errorMessages?.filter(
              (em) =>
                em.machineAndOperatorId === mac.maoId &&
                em.operator._id === op._id
            );

            const completeTime = test.reduce(
              (acc, curVal) => acc + +curVal?.time,
              0
            );

            const data = {
              user: op,
              machine: mac,
              time: convertToHrsAndMins(completeTime / 60),
              errors: errorMessages?.length || 0,
            };

            setOperatorData((prev) => [...prev, data]);
          }
        });
      });
    }
  }, [cuProject, defaultParam, deviceDatas]);

  const progressPercent = (realtimeBatchSize / batchData?.['Batch Size']) * 100;

  let progress =
    handlePercentDeicmalValue(progressPercent > 100 ? 100 : progressPercent) ||
    0;

  return (
    <>
      <div
        className={`relative rounded-xl w-[250px] h-[330px] shadow-lg bg-white ${
          !cuProject ? 'flex flex-col gap-2 justify-center items-center' : ''
        }`}
      >
        {cuProject ? (
          <div className="w-full h-full flex flex-col">
            <div className="w-full flex justify-between items-center px-5 py-1 border-b">
              <section className="w-[80%]">
                <p className="mt-1 text-[12px] font-semibold">
                  {data?.processName}
                </p>
                <ProgressBar
                  progress={progress}
                  max={100}
                  progressColor={'#77DD77'}
                />
              </section>
              <section className="flex w-[15%] items-end">
                <p className="text-xl">{progress}</p>
                <span className="text-sm mb-0.5">%</span>
              </section>
            </div>

            <div className="overflow-y-scroll no-scrollbar h-full">
              {operatorData?.map((item, iIdx) => (
                <section
                  key={iIdx}
                  className="w-full flex items-center py-1 px-3 border-b gap-x-3"
                >
                  <span className="w-1/5 aspect-square bg-gray-200 rounded-full overflow-hidden">
                    <BlankProfile />
                  </span>
                  <section className="flex flex-col text-[10px] text-[#36434D] w-4/5">
                    <p className="font-semibold mb-1">
                      {item?.user.name}({item?.user?.employeeId})
                    </p>
                    <section className="w-full flex justify-between items-center">
                      <section className="flex flex-col justify-center items-center">
                        <p className="font-semibold">{`${item?.time?.[0]}h ${item?.time?.[1]}m`}</p>
                        <p className="text-[9px]">Uptime</p>
                      </section>
                      <section className="flex flex-col justify-center items-center">
                        <p className="font-semibold">{item?.errors}</p>
                        <p className="text-[9px]">Pauses</p>
                      </section>
                      <section className="flex flex-col justify-center items-center">
                        <p className="font-semibold">
                          {item?.machine?.machineName}
                        </p>
                        <p className="text-[9px]">Machine</p>
                      </section>
                    </section>
                  </section>
                </section>
              ))}
            </div>
          </div>
        ) : (
          <>
            <p>No project started</p>
            <p>{data?.processName}</p>
          </>
        )}
        {!isLastItem && (
          <Directions
            isComplete={isComplete}
            scrap={batchData?.['Batch Size'] - realtimeBatchSize}
            isReversed={isReversed}
            isLastInRow={isLastInRow}
            haltData={haltData}
            isMobile={isMobile}
            isTablet={isTablet}
          />
        )}
      </div>
    </>
  );
}
