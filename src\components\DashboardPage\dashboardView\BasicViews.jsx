import { useState, useEffect } from 'react';
import { calculateValueForViews } from '../../../helperFunction';

const BasicViews = ({ selectOptions, values }) => {
  const [newValues, setNewValues] = useState({});

  useEffect(() => {
    if (selectOptions && values) {
      selectOptions?.cuProjects?.forEach((cuProject) => {
        cuProject?.machineAndOperator?.forEach((mao) => {
          const isActive = mao?.status === 'active';

          const machine = selectOptions?.project?.machines?.find(
            (machine) => machine._id === mao?.machine._id
          );

          machine?.devices?.forEach((device) => {
            device?.assignedDevice?.forEach((item) => {
              if (isActive) {
                setNewValues((prev) => ({
                  ...prev,
                  [item?.deviceId]: values?.[item?.deviceId] || {},
                }));
              } else {
                setNewValues((prev) => ({
                  ...prev,
                  [item?.deviceId]:
                    cuProject?.lastValues?.[machine?.machineId]?.[
                      item?.deviceId
                    ] || {},
                }));
              }
            });
          });
        });
      });
    }
  }, [values, selectOptions]);

  return (
    <div className="w-full aspect-[664/90] flex bg-white rounded-new overflow-x-scroll no-scrollbar px-3 pt-1 items-center justify-between">
      {selectOptions?.project?.basicView?.fields?.map((field) => {
        const calculatedValue = calculateValueForViews(
          field.formula,
          newValues,
          selectOptions?.machineList?.map((mac) => mac?.id)
        );

        return (
          <div
            key={field._id}
            className="h-full min-w-[100px] flex flex-col justify-evenly items-center"
          >
            <p className="text-[1.25rem] font-bold">
              {Math.floor(calculatedValue)}
            </p>
            <p className="text-[0.94rem] font-medium">{field.name}</p>
          </div>
        );
      })}
    </div>
  );
};
export default BasicViews;
