import { DEFAULT_MULTIPLIER_VALUE } from '../../utils/Constant';
import Input from '../global/components/Input';
import Table from '../global/components/Table';

function TemplateMultiplier({
  productionFlow = {},
  templateDetails,
  setTemplateDetails,
}) {
  const { processes } = productionFlow;

  const handleMultiplierChange = (e, flow) => {
    const regex = new RegExp(/^[*/]\d+/);
    const pass = regex.test(e.target.value);
    if (pass) {
      document
        .getElementById(`input${flow._id}`)
        ?.classList.remove('!bg-red-200');
    } else {
      document.getElementById(`input${flow._id}`)?.classList.add('!bg-red-200');
    }

    setTemplateDetails((prev) => ({
      ...prev,
      multiplier: { ...(prev?.multiplier || {}), [flow?._id]: e.target.value },
    }));
  };

  return (
    <div>
      <Table>
        <Table.Head>
          <Table.Row>
            <Table.Th>#</Table.Th>
            <Table.Th>Process</Table.Th>
            <Table.Th>Multiplier</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {processes?.map((flow, fIdx) => (
            <Table.Row key={flow._id}>
              <Table.Td>{fIdx + 1}</Table.Td>
              <Table.Td>{flow.processName}</Table.Td>
              <Table.Td>
                <Input
                  id={`input${flow._id}`}
                  value={
                    templateDetails?.multiplier?.[flow._id] ||
                    DEFAULT_MULTIPLIER_VALUE
                  }
                  onChange={(e) => handleMultiplierChange(e, flow)}
                />
              </Table.Td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
}

export default TemplateMultiplier;
