import { createObjects } from '../../helperFunction';

export const setDefaultfilledValue = (inspectionFormRef) => {
  if (!inspectionFormRef) return;
  const defaultObj = {};

  Object.keys(inspectionFormRef).forEach((key) => {
    const el = inspectionFormRef[key];
    switch (inspectionFormRef[key].fieldType.toString()) {
      case 'Date':
        defaultObj[key] = {
          fieldType: el.fieldType,
          date: '',
          filledValue: '',
          checkCondition: '',
        };
        break;

      case 'Check':
        defaultObj[key] = {
          filledValue: false,
          fieldType: el.fieldType,
        };
        break;

      case 'MultiCheckbox':
        defaultObj[key] = {
          fieldType: inspectionFormRef[key].fieldType,
          labelArray: el.labelArray || [],
          filledValue: [],
        };

        break;
      case 'Range':
        defaultObj[key] = {
          fieldType: el.fieldType,
          filledValue: '',
          min: 0,
          max: 0,
        };
        break;

      case 'Min-Max':
        defaultObj[key] = {
          fieldType: el.fieldType,
          filledValue: '',
          filledMinfilledValue: 0,
          filledMaxfilledValue: 0,
        };
        break;

      case 'Media':
        defaultObj[key] = {
          fieldType: el.fieldType,
          filledValue: [],
        };
        break;

      case 'Range Threshold':
        defaultObj[key] = {
          fieldType: el.fieldType,
          filledValue: '',
          average: 0,
          margin: 0,
        };
        break;

      case 'Table':
        defaultObj[key] = {
          type: el?.fieldType,
          columns: el?.tableOptions?.column || [],
          noOfRows: +el?.tableOptions?.rows || 0,
          newRowNo: 0,
          rows: +el?.tableOptions?.rows || 0,
          row:
            el?.tableOptions?.row ||
            Array.from({ length: el?.tableOptions?.rows }, () => ''),
          rowData: createObjects(
            +el?.tableOptions?.rows || 0,
            el?.tableOptions?.column || []
          ),
        };
        break;

      default:
        defaultObj[key] = {
          fieldType: el.fieldType,
          filledValue: '',
          options: el?.fieldOptions || [],
        };

        break;
    }
  });

  return defaultObj;
};
