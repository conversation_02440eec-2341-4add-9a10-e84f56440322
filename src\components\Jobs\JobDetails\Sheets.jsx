import useForm from '../../../hooks/useForm';

const Sheet = ({
  sheet,
  tabIdx,
  sheetIdx,
  setFormData,
  productionFlow,
  multiProcessTablesData,
  setMultiProcessTablesData,
  handleLinkFieldFormValChange,
  step,
  goalsData,
  goalsTables = [],
  setGoalsTables,
  masters = [],
  jobDetails = {},
  isEdit = false,
}) => {
  const master = masters?.find((item) => item.id === sheet?.masterId);

  const [renderInputForm, renderProcessSelectionForm] = useForm({
    sheet,
    tabIdx,
    sheetIdx,
    setFormData,
    productionFlow,
    multiProcessTablesData,
    setMultiProcessTablesData,
    handleLinkFieldFormValChange,
    goalsData,
    goalsTables,
    setGoalsTables,
    master,
    jobDetails,
    isEdit,
  });

  return (
    <div className="w-full overflow-x-hidden">
      {step === 0 && (
        <>
          <h3 className={`text-center p-2 mb-3 border-b`}>{sheet.sheetName}</h3>
          <section className="px-8">{renderInputForm()}</section>
        </>
      )}
      {step === 4 && (
        <>
          <h3 className={`text-center p-2 mb-3 border-b`}>{sheet.sheetName}</h3>
          <section className="px-8">{renderProcessSelectionForm()}</section>
        </>
      )}
    </div>
  );
};

const Sheets = ({
  tab,
  tabIdx,
  setFormData,
  productionFlow,
  multiProcessTablesData,
  setMultiProcessTablesData,
  handleLinkFieldFormValChange,
  step,
  goalsData,
  goalsTables,
  setGoalsTables,
  masters,
  jobDetails,
  isEdit,
}) => {
  return (
    <div className="w-full">
      {tab?.data?.map((sheet, sheetIdx) => (
        <Sheet
          key={sheetIdx}
          sheet={sheet}
          tabIdx={tabIdx}
          sheetIdx={sheetIdx}
          setFormData={setFormData}
          productionFlow={productionFlow}
          multiProcessTablesData={multiProcessTablesData}
          setMultiProcessTablesData={setMultiProcessTablesData}
          handleLinkFieldFormValChange={handleLinkFieldFormValChange}
          step={step}
          goalsData={goalsData}
          goalsTables={goalsTables}
          setGoalsTables={setGoalsTables}
          masters={masters}
          jobDetails={jobDetails}
          isEdit={isEdit}
        />
      ))}
    </div>
  );
};

export default Sheets;
