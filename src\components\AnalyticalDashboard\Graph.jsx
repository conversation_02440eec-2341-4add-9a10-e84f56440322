import {
  CategoryScale,
  Chart as ChartJS,
  Colors,
  LineElement,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js';
import { useEffect, useState } from 'react';
import { Line } from 'react-chartjs-2';

ChartJS.register(
  Colors,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip
  // Legend
);

const Graph = ({ deviceDatas, device, period, fields }) => {
  const [dates, setDates] = useState([]);
  const [lastDeviceDatas, setLastDeviceDatas] = useState({});

  useEffect(() => {
    // generates array of dates according to period

    setDates([]);
    const todayDate = new Date(new Date().setHours(0, 0, 0, 0));
    if (period === 'weekly') {
      for (let i = 6; i >= 0; i--) {
        const tempDate = new Date(new Date().setHours(0, 0, 0, 0));

        setDates((prev) => [
          ...prev,
          new Date(tempDate.setDate(todayDate.getDate() - i)),
        ]);
      }
    } else if (period === 'monthly') {
      for (let i = 27; i >= 0; i--) {
        const tempDate = new Date(new Date().setHours(0, 0, 0, 0));

        setDates((prev) => [
          ...prev,
          new Date(tempDate.setDate(todayDate.getDate() - i)),
        ]);
      }
    } else {
      [...Array(24)].forEach((i, idx) => {
        const tempDate = new Date(new Date().setHours(0, 0, 0, 0));
        if (idx % 2 === 0) {
          setDates((prev) => [
            ...prev,
            new Date(tempDate.setHours(idx, 0, 0, 0)),
          ]);
        }
      });
    }
  }, [period]);

  useEffect(() => {
    // gets last stored value from all device datas according to time stamps of dates and field for graph
    if (
      dates?.length > 0 &&
      deviceDatas?.length > 0 &&
      device &&
      fields?.length > 0
    ) {
      setLastDeviceDatas({});

      // run for each fields of devices
      fields.forEach((field) => {
        const deviceIdObj = device.assignedDevice[field.deviceNo - 1];
        const fieldName = `${field.name} (${deviceIdObj.deviceId})`;

        // creates entry with empty array in last device data object to prevent iteration error
        setLastDeviceDatas((prev) => ({ ...prev, [fieldName]: [] }));

        // runs for each date stamp
        dates.forEach((date) => {
          // finds last device data and stores in array inside object
          const temp = deviceDatas.findLast((item) => {
            const newDate = new Date(item.data.createdAt);
            if (period === 'weekly' || period === 'monthly') {
              return (
                newDate.getDate() === date.getDate() &&
                item.device === device._id &&
                item.deviceId === deviceIdObj.id &&
                Object.keys(item.data).includes(field.name.toUpperCase())
              );
            } else {
              return (
                newDate > date &&
                (newDate.getHours() === date.getHours() ||
                  newDate.getHours() === date.getHours() + 1) &&
                item.device === device._id &&
                item.deviceId === deviceIdObj.id &&
                Object.keys(item.data).includes(field.name.toUpperCase())
              );
            }
          });
          if (temp) {
            setLastDeviceDatas((prev) => ({
              ...prev,
              [fieldName]: [...(prev?.[fieldName] || []), temp],
            }));
          } else {
            setLastDeviceDatas((prev) => ({
              ...prev,
              [fieldName]: [...(prev?.[fieldName] || []), null],
            }));
          }
        });
      });
    }
  }, [dates, deviceDatas, device, fields, period]);

  const labels =
    period === 'monthly'
      ? ['Week 1', 'Week 2', 'Week 3', 'Week 4']
      : period === 'weekly'
      ? dates.map((date) => {
          const temp = date.toDateString();
          return temp.substring(0, temp.length - 5);
        })
      : dates.map((date) => date.toLocaleTimeString());

  let datasets = [];

  const keys = Object.keys(lastDeviceDatas);

  keys.forEach((key) => {
    const tempData = [];
    if (period === 'monthly') {
      labels.forEach((l, lIdx) => {
        tempData.push(
          lastDeviceDatas[key]
            .slice(7 * lIdx, 7 * lIdx + 7)
            .reduce((acc, curVal) => {
              if (curVal) {
                const tempField = Object.keys(curVal.data)[0];
                return +curVal.data?.[tempField] + acc;
              } else {
                return acc;
              }
            }, 0)
        );
      });
    }

    datasets = [
      ...datasets,
      {
        label: key,
        data:
          period === 'monthly'
            ? tempData
            : lastDeviceDatas[key].map((data) => {
                if (data) {
                  const tempField = Object.keys(data.data)[0];
                  return +data.data?.[tempField];
                }
                return 0;
              }),
      },
    ];
  });

  const data = {
    labels,
    datasets,
  };

  const options = {
    responsive: true,
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          display: false,
        },
      },
    },
  };

  return <Line options={options} data={data} />;
};

export default Graph;
