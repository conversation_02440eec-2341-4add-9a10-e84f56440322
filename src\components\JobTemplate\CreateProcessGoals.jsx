import { Disclosure } from '@headlessui/react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import cross from '../../assets/images/whiteCross.png';
import edit from '../../assets/images/whiteEdit.png';
import { ReactComponent as Doc } from '../../assets/svgs/documentprevious.svg';
import { useLazyGetAllMqttsQuery } from '../../slices/mqttslice';
import {
  useCreateProcessGoalMutation,
  useDeleteProcessGoalMutation,
  useUpdateProcessGoalMutation,
} from '../../slices/processGoalApiSlice';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import NewSelect from '../global/components/NewSelect';
import Select from '../global/components/Select';

const CreateProcessGoals = ({ processGoals, data }) => {
  const [selectProcess, setSelectProcess] = useState('');
  const [parameters, setParameters] = useState([]);
  const [name, setName] = useState('');
  const [reRender, setReRender] = useState(false);
  const [allFields, setAllFields] = useState([]);
  const [selectField, setSelectField] = useState('');
  const [editId, setEditId] = useState('');
  const [processSet, setProcessSet] = useState('');

  const [getAllMqtts, { data: processes = [] }] = useLazyGetAllMqttsQuery();

  const [createProcessGoal, { isLoading: isLoadingCreate }] =
    useCreateProcessGoalMutation();

  const [updateProcessGoal, { isLoading: isLoadingUpdate }] =
    useUpdateProcessGoalMutation();

  const [deleteProcessGoal] = useDeleteProcessGoalMutation();

  useEffect(() => {
    if (data) {
      setAllFields([]);
      data?.forEach((tab) => {
        tab?.data?.forEach((form) => {
          form?.sheetData?.data?.forEach((data) => {
            if (
              (data.Type === 'NUMERIC' || data.Type === 'NUMDROPDOWN') &&
              data['Horizontal Repetition'] === '1' &&
              data['Vertical Repetition'] === '1'
            )
              setAllFields((prev) => {
                if (prev.includes(data.Parameter)) {
                  return prev;
                }
                return [...prev, data.Parameter];
              });
          });
        });
      });
    }
  }, [data]);

  useEffect(() => {
    getAllMqtts({}, false);
  }, [reRender, getAllMqtts]);

  useEffect(() => {
    if (processGoals && processGoals.length > 0) {
      localStorage.setItem('processGoals', JSON.stringify(processGoals));
    }
  }, [processGoals]);

  useEffect(() => {
    if (selectProcess) {
      setParameters((prev) =>
        prev.map((param) => ({
          ...param,
          identifier: `${selectProcess}-${param.name}`,
        }))
      );
    }
  }, [selectProcess]);

  const addParamHandler = () => {
    if (!name || name === '') {
      toast.error('Please add Parameter name', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Please add Parameter name',
      });
      return;
    }
    if (!selectField || selectField === '') {
      toast.error('Please add Link Field', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Please add Link Field',
      });
      return;
    }
    setParameters((prev) => [
      ...prev,
      { name, identifier: `${selectProcess}-${name}`, field: selectField },
    ]);
    setName('');
    setSelectField('');
  };

  const addGoalHandler = async () => {
    if (!parameters || parameters === '') {
      toast.error('Please add atleast one record', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Please add atleast one record',
      });
      return;
    }
    const res = await createProcessGoal({
      data: {
        mqtt: selectProcess,
        parameters,
      },
    }).unwrap();
    if (res) {
      setProcessSet('');
      setSelectProcess('');
      setParameters([]);
      setReRender((prev) => !prev);
    }
  };

  const deleteHandler = async (goal) => {
    if (
      await customConfirm(
        `Are you sure you want to delete ${goal?.mqtt?.process}?`,
        'delete'
      )
    ) {
      try {
        const res = await deleteProcessGoal({ id: goal?._id }).unwrap();
        if (res) {
          setReRender((prev) => !prev);
        }
      } catch (err) {
        toast.error(err?.response?.data?.message || err.message, {
          theme: 'colored',
          position: 'top-right',
        });
      }
    } else {
      return;
    }
  };

  const updateHandler = async () => {
    const res = await updateProcessGoal({
      id: editId,
      data: {
        parameters,
      },
    }).unwrap();
    if (res) {
      setProcessSet('');
      setSelectProcess('');
      setParameters([]);
      setReRender((prev) => !prev);
      setEditId('');
    }
  };

  return (
    <div className="w-full bg-white text-gray-primary rounded-lg px-5">
      {/* process select */}
      {/* <div className="w-full border-b px-5 py-2">
        <div className="w-3/5 rounded-full shadow-basic bg-white my-3 pr-1.5 flex">
          <p
            htmlFor="selectProcess"
            className="bg-blue-primary inline-block py-1 w-2/5 text-center text-white rounded-full"
          >
            Add Process
          </p>
          <Select
            value={selectProcess}
            onChange={(e) => setSelectProcess(e.target.value)}
            options={processes?.map((process) => ({
              name: process.process,
              value: process._id,
              disabled: processGoals?.find((goal) => goal?.mqtt?._id === process?._id),
            }))}
            className="border-none shadow-none"
            placeholder="Please select a process"
          />
        </div>
      </div> */}

      <div className="flex w-full flex-col md:flex-row md:justify-between md:items-center">
        <div className="flex w-full md:w-1/4 flex-col mt-5">
          <label className="mb-1 font-semibold text-[#667085]">
            Select Process
            <span className="text-xl text-red-500 -mt-1">*</span>
          </label>
          <NewSelect
            value={selectProcess}
            onChange={(e) => setSelectProcess(e.target.value)}
            options={processes?.map((process) => ({
              name: process.process,
              value: process._id,
              disabled: processGoals?.find(
                (goal) => goal?.mqtt?._id === process?._id
              ),
            }))}
            // className="border-none shadow-none"
            placeholder="Please select a process"
          />
        </div>
        <Button
          className={'h-7 text-xs mt-4'}
          onClick={() => setProcessSet(selectProcess)}
        >
          Add Process
        </Button>
      </div>

      {processSet && (
        <>
          <Modal
            title={'Add Process'}
            description={''}
            svg={<Doc className="h-8 w-8" />}
            onCloseModal={setProcessSet}
            btnIsLoading={isLoadingCreate || isLoadingUpdate}
            onSubmit={!editId ? addGoalHandler : updateHandler}
            onAdd={{ label: 'Add', func: [addParamHandler], step: [0] }}
          >
            {() => {
              return (
                <>
                  <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
                    <div className="flex flex-col my-5">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Parameter Name
                        <span className="text-xl text-red-500 -mt-1 ">*</span>
                      </label>
                      <Input
                        type="text"
                        id="name"
                        list="defaultsParam"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        autoComplete="off"
                      />
                    </div>
                    <div className="flex flex-col my-5">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Link Field
                        <span className="text-xl text-red-500 -mt-1 ">*</span>
                      </label>
                      <Select
                        id="field"
                        value={selectField}
                        disabled={
                          name === 'Number of Batches' ||
                          name === 'Total Material'
                        }
                        onChange={(e) => setSelectField(e.target.value)}
                        options={allFields?.map((field) => ({
                          name: field,
                          value: field,
                        }))}
                      />
                    </div>
                    {/* <div className="flex mt-0 mb-10 justify-end w-full">
                      <button
                        type="button"
                        disabled={!name}
                        className={`text-center text-sm text-white rounded-[8px] w-[138px] h-[32px] bg-blue-primary hover:bg-blue-hover ${
                          !name
                            ? 'bg-blue-disabled hover:cursor-not-allowed'
                            : 'bg-blue-primary hover:bg-blue-hover'
                        }`}
                        onClick={addParamHandler}
                      >
                        Add Parameter
                      </button>
                    </div> */}
                  </div>
                  {parameters && parameters.length > 0 && (
                    <div className="overflow-scroll w-full">
                      <table className="leading-normal overflow-scroll w-full">
                        <thead>
                          <tr>
                            <th className="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                              #
                            </th>
                            <th className="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                              Parameter
                            </th>
                            <th className="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                              Linked Field
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {parameters.map((param, pIdx) => (
                            <tr key={pIdx}>
                              <td className="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                                <p className="text-gray-900 whitespace-no-wrap">
                                  {pIdx + 1}
                                </p>
                              </td>
                              <td className="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                                <p className="text-gray-900 whitespace-no-wrap">
                                  {param.name}
                                </p>
                              </td>
                              <td className="px-5 py-5 border-b border-gray-200 bg-white text-sm">
                                <p className="text-gray-900 whitespace-no-wrap">
                                  {param.field}
                                </p>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </>
              );
            }}
          </Modal>
        </>
      )}
      <Disclosure>
        {({ open }) => (
          <>
            <Disclosure.Button className="w-full md:w-1/4 mt-5">
              <div className="w-full flex justify-between items-center p-2 text-[#667085]  font-bold border-y">
                <h3>Saved Parameters</h3>
                {open ? (
                  <ChevronUpIcon className="h-4 w-4" />
                ) : (
                  <ChevronDownIcon className="h-4 w-4" />
                )}
              </div>
            </Disclosure.Button>
            <Disclosure.Panel className="bg-white grid grid-cols-1 md:grid-cols-4 gap-10 px-1 md:px-10 p-2 mt-4">
              {processGoals &&
                processGoals.map((goal, gIdx) => (
                  <div
                    key={gIdx}
                    className="w-full aspect-square rounded-xl bg-[#1364D4] hover:cursor-pointer p-4 text-white relative"
                  >
                    <p className="text-lg font-medium mb-[9px]">
                      {goal?.mqtt?.process}
                    </p>
                    {goal.parameters.map((param, pIdx) => (
                      <p key={pIdx} className="pb-1 text-sm">
                        {param.name}
                      </p>
                    ))}
                    <div className=" absolute right-0 p-3 flex flex-row top-0 gap-2">
                      <img
                        src={edit}
                        alt=""
                        className="w-3"
                        onClick={() => {
                          setEditId(goal._id);
                          setSelectProcess(goal?.mqtt?._id);
                          setParameters(goal?.parameters);
                        }}
                      />
                      <img
                        src={cross}
                        alt=""
                        className="w-3"
                        onClick={() => {
                          deleteHandler(goal);
                        }}
                      />
                    </div>
                  </div>
                ))}
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>
      <div className=""></div>
    </div>
  );
};

export default CreateProcessGoals;
