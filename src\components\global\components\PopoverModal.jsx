import {
  MinusCircleIcon,
  PlusCircleIcon,
  TruckIcon,
} from '@heroicons/react/24/outline';
import { Modal } from 'antd';
import { useState } from 'react';
import AddIcon from './../../../assets/images/add.png';
import BackIcon from './../../../assets/images/back.png';
import NextIcon from './../../../assets/images/next.png';
import SubmitIcon from './../../../assets/images/submit.png';
import Button from './Button';
const FormWrapper = ({
  children,
  isForm = true,
  step,
  pages,
  current,
  onSubmit,
  nextClickHandler,
}) => {
  if (isForm) {
    return (
      <form
        onSubmit={(e) => {
          e.preventDefault();
          if (step === pages?.length - 1 || pages?.length < 2) {
            onSubmit(e, current);
          } else {
            nextClickHandler();
          }
        }}
        className="w-full h-[82.5%]"
      >
        {children}
      </form>
    );
  } else {
    return <div className="w-full h-[82.5%]">{children}</div>;
  }
};
const PopoverModal = ({
  children,
  title,
  pages = [],
  onNextClick,
  onBackClick,
  onSubmit,
  onAdd,
  canSubmit = true,
  indexStartFromZero = true,
  isSubmitRequired = true,
  btnIsLoading = false,
  isBackButton = false,
  canAddValue = false,
  canRemoveValue = false,
  formData = [],
  setFormData,
  isChangeInAssembly = false,
  setValueCount,
  canDispatch = false,
  onDispatch,
  onCloseModal,
  showModal,
  file,
}) => {
  const [step, setStep] = useState(0);
  const current = { step, page: pages?.[step], setStep };
  const nextClickHandler = () => {
    if (onNextClick) {
      onNextClick({ ...current, setStep });
    } else {
      setStep((prev) => prev + 1);
    }
  };

  return (
    <>
      <Modal
        title={title}
        onCancel={onCloseModal}
        open={showModal}
        width={400}
        footer={null}
        style={{
          top: file ? '20%' : '60%',
          left: '35%',
        }}
      >
        <FormWrapper
          isForm={canSubmit}
          step={step}
          pages={pages}
          current={current}
          onSubmit={onSubmit}
          nextClickHandler={nextClickHandler}
        >
          <>
            <div className="w-full h-[85%] px-8 py-5 overflow-y-scroll  ">
              {children(current)}
            </div>
            <div
              className={`h-[15%] w-full flex flex-row items-center justify-between px-8 ${
                isSubmitRequired && 'border-t'
              }`}
            >
              {canAddValue && (
                <Button
                  onClick={() => {
                    const updatedValues = formData?.values
                      ? [...formData.values, '']
                      : [''];
                    setFormData((prev) => ({
                      ...prev,
                      values: updatedValues,
                    }));
                    setValueCount((prev) => {
                      return prev + 1;
                    });
                  }}
                  type="button"
                  // className={`text-center text-sm bg-blue-primary hover:bg-blue-hover text-white rounded-[8px] w-[200px] h-[32px]`}
                >
                  <PlusCircleIcon height={20} width={20} />
                </Button>
              )}

              {canRemoveValue && (
                <div className="justify-start flex relative">
                  <Button
                    onClick={() => {
                      if (formData?.values?.length > 0) {
                        const updatedValues = formData.values.slice(0, -1);
                        setFormData((prev) => ({
                          ...prev,
                          values: updatedValues,
                        }));
                      }
                      setValueCount((prev) => {
                        return prev - 1;
                      });
                    }}
                    type="button"
                    className={`ml-10 `}
                  >
                    <MinusCircleIcon height={20} width={20} />
                  </Button>
                </div>
              )}
              {onAdd && onAdd?.step?.includes(step) ? (
                <div className={`w-1/4 items-center flex justify-start`}>
                  <Button
                    type="button"
                    className={`rounded-[8px] w-[138px] h-[32px]`}
                    onClick={onAdd.func[indexStartFromZero ? step : step - 1]}
                    icon={
                      <img
                        src={AddIcon}
                        alt="Add Icon"
                        className="w-5 h-5 object-contain relative"
                      />
                    }
                  >
                    {onAdd.label}
                  </Button>
                </div>
              ) : null}

              <div
                className={`flex items-center gap-x-8 justify-end ${
                  onAdd && onAdd?.step?.includes(step) ? 'w-3/4' : 'w-full'
                }`}
              >
                {isBackButton || step > 0 ? (
                  <Button
                    type="button"
                    className="rounded-[8px] w-[138px] h-[32px]"
                    onClick={() => {
                      if (onBackClick) {
                        onBackClick({ ...current, setStep });
                      } else {
                        setStep((prev) => prev - 1);
                      }
                    }}
                    icon={
                      <img
                        src={BackIcon}
                        alt="Back Icon"
                        className="w-5 h-5 object-contain relative"
                      />
                    }
                  >
                    Back
                  </Button>
                ) : null}
                {canDispatch && (
                  <Button
                    onClick={() => {
                      onDispatch();
                    }}
                  >
                    <TruckIcon className="w-[1rem] h-[1rem]" />
                    Dispatch
                  </Button>
                )}

                {isSubmitRequired && (
                  <Button
                    isLoading={btnIsLoading}
                    type={canSubmit ? 'submit' : 'button'}
                    onClick={(e) => {
                      if (!canSubmit && onSubmit) {
                        onSubmit(e, current);
                      }
                    }}
                    className={
                      isChangeInAssembly
                        ? `rounded-[8px] w-[180px] h-[32px] bg-[#14BA6D] hover:bg-[#21d17f]`
                        : `
                      rounded-[8px] w-[138px] h-[32px] bg-green-600 hover:brightness-105 ${
                        step + 1 === pages?.length || pages?.length === 0
                          ? ''
                          : 'hidden'
                      }`
                    }
                    color=""
                    icon={
                      <img
                        src={SubmitIcon}
                        alt="Submit Icon"
                        className="w-5 h-5 object-contain relative"
                      />
                    }
                  >
                    {isChangeInAssembly ? 'Make Copy and Proceed' : 'Submit'}
                  </Button>
                )}

                {pages?.length !== 0 && (
                  <Button
                    type="button"
                    className={`rounded-[8px] w-[138px] h-[32px] ${
                      step + 1 !== pages?.length ? '' : 'hidden'
                    }`}
                    onClick={nextClickHandler}
                    icon={
                      <img
                        src={NextIcon}
                        alt="Next Icon"
                        className="w-5 h-5 object-contain relative"
                      />
                    }
                  >
                    Next
                  </Button>
                )}
              </div>
            </div>
          </>
        </FormWrapper>
      </Modal>
    </>
  );
};

export default PopoverModal;

// return (
//   <> <button
//     disabled={isLoading || isLoadingImage}
//     onClick={() => {
//       setFile(null);
//       togglePopover();
//     }}
//   >
//     <IoMdAttach
//       className="absolute right-12 top-1/2 -translate-y-1/2"
//       size={'20px'}
//       color="gray"
//     />
//   </button>
//     <Popover
//       trigger="click"
//       open={isPopoverVisible}
//       onClose={onCloseModal}
//       onOpenChange={togglePopover}
//       content={
//         <FormWrapper
//           isForm={canSubmit}
//           step={step}
//           pages={pages}
//           current={current}
//           onSubmit={onSubmit}
//           nextClickHandler={nextClickHandler}
//         >
//           <>
//             <div className="w-full h-[85%] px-8 py-5 overflow-y-scroll">
//               {children(current)}
//             </div>
//             <div
//               className={`h-[15%] w-full flex flex-row items-center justify-between px-8 ${isSubmitRequired && 'border-t'
//                 }`}
//             >
//               {canAddValue && (
//                 <Button
//                   onClick={() => {
//                     const updatedValues = formData?.values
//                       ? [...formData.values, '']
//                       : [''];
//                     setFormData((prev) => ({
//                       ...prev,
//                       values: updatedValues,
//                     }));
//                     setValueCount((prev) => {
//                       return prev + 1;
//                     });
//                   }}
//                   type="button"
//                 // className={`text-center text-sm bg-blue-primary hover:bg-blue-hover text-white rounded-[8px] w-[200px] h-[32px]`}
//                 >
//                   <PlusCircleIcon height={20} width={20} />
//                 </Button>
//               )}

//               {canRemoveValue && (
//                 <div className="justify-start flex relative">
//                   <Button
//                     onClick={() => {
//                       if (formData?.values?.length > 0) {
//                         const updatedValues = formData.values.slice(0, -1);
//                         setFormData((prev) => ({
//                           ...prev,
//                           values: updatedValues,
//                         }));
//                       }
//                       setValueCount((prev) => {
//                         return prev - 1;
//                       });
//                     }}
//                     type="button"
//                     className={`ml-10 `}
//                   >
//                     <MinusCircleIcon height={20} width={20} />
//                   </Button>
//                 </div>
//               )}
//               {onAdd && onAdd?.step?.includes(step) ? (
//                 <div className={`w-1/4 items-center flex justify-start`}>
//                   <Button
//                     type="button"
//                     className={`rounded-[8px] w-[138px] h-[32px]`}
//                     onClick={
//                       onAdd.func[indexStartFromZero ? step : step - 1]
//                     }
//                     icon={
//                       <img
//                         src={AddIcon}
//                         alt="Add Icon"
//                         className="w-5 h-5 object-contain relative"
//                       />
//                     }
//                   >
//                     {onAdd.label}
//                   </Button>
//                 </div>
//               ) : null}

//               <div
//                 className={`flex items-center gap-x-8 justify-end ${onAdd && onAdd?.step?.includes(step) ? 'w-3/4' : 'w-full'
//                   }`}
//               >
//                 {isBackButton || step > 0 ? (
//                   <Button
//                     type="button"
//                     // className={`${
//                     //   onAdd && onAdd?.step?.includes(step)
//                     //     ? 'w-[26%]'
//                     //     : 'w-1/5'
//                     // }`}
//                     className="rounded-[8px] w-[138px] h-[32px]"
//                     onClick={() => {
//                       if (onBackClick) {
//                         onBackClick({ ...current, setStep });
//                       } else {
//                         setStep((prev) => prev - 1);
//                       }
//                     }}
//                     icon={
//                       <img
//                         src={BackIcon}
//                         alt="Back Icon"
//                         className="w-5 h-5 object-contain relative"
//                       />
//                     }
//                   >
//                     Back
//                   </Button>
//                 ) : null}
//                 {canDispatch && (
//                   <Button
//                     onClick={() => {
//                       onDispatch();
//                     }}
//                   >
//                     <TruckIcon className="w-[1rem] h-[1rem]" />
//                     Dispatch
//                   </Button>
//                 )}

//                 {isSubmitRequired && (
//                   <Button
//                     isLoading={btnIsLoading}
//                     type={canSubmit ? 'submit' : 'button'}
//                     onClick={(e) => {
//                       if (!canSubmit && onSubmit) {
//                         onSubmit(e, current);
//                       }
//                     }}
//                     className={
//                       isChangeInAssembly
//                         ? `rounded-[8px] w-[180px] h-[32px] bg-[#14BA6D] hover:bg-[#21d17f]`
//                         : `
//                     rounded-[8px] w-[138px] h-[32px] bg-green-600 hover:brightness-105 ${step + 1 === pages?.length || pages?.length === 0
//                           ? ''
//                           : 'hidden'
//                         }`
//                     }
//                     color=""
//                     icon={
//                       <img
//                         src={SubmitIcon}
//                         alt="Submit Icon"
//                         className="w-5 h-5 object-contain relative"
//                       />
//                     }
//                   >
//                     {isChangeInAssembly
//                       ? 'Make Copy and Proceed'
//                       : 'Submit'}
//                   </Button>
//                 )}

//                 {pages?.length !== 0 && (
//                   <Button
//                     type="button"
//                     className={`rounded-[8px] w-[138px] h-[32px] ${step + 1 !== pages?.length ? '' : 'hidden'
//                       }`}
//                     onClick={nextClickHandler}
//                     icon={
//                       <img
//                         src={NextIcon}
//                         alt="Next Icon"
//                         className="w-5 h-5 object-contain relative"
//                       />
//                     }
//                   >
//                     Next
//                   </Button>
//                 )}
//               </div>
//             </div>
//           </>
//         </FormWrapper>

//       }
//     />
//   </>
// );
