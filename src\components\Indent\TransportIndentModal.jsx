import { useEffect, useState, useMemo, useCallback } from 'react';
import Modal from '../global/components/Modal';
import SelectV2 from '../global/components/SelectV2';
import { Button, Input, Switch, Card, Space, Typography, Tag } from 'antd';
import {
  PlusOutlined,
  TruckOutlined,
  EnvironmentOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { toast } from 'react-toastify';

import {
  useCreateTransportIndentMutation,
  useUpdateTransportIndentMutation,
} from '../../slices/transportIndentApiSlice';
import { useGetAllVendorsForOptionsQuery } from '../../slices/vendorApiSlice';
import { useGetCustomerOptionsQuery } from '../../slices/customerDataSlice';
import AddVendorModal from '../v3/global/components/AddVendorModal';
import CustomerModalFormData from '../global/components/CustomerModalFormData';

const { Title, Text } = Typography;

// ----- Constants -----
const baseOptions = (labels) => [
  { label: '+ Create New', value: 'create_new', icon: <PlusOutlined /> },
  ...labels.map((lbl) => ({ label: lbl, value: lbl })),
];

const FIELD_TYPES = ['Vendor', 'Customer', 'Factory', 'Other'];
const FROM_TO_OPTIONS = baseOptions(FIELD_TYPES);

const TRANSPORT_OPTIONS = baseOptions(['Local Pickup Van', 'Porter', 'Other']);
const STATUS_OPTIONS = baseOptions([
  'Created',
  'Dispatched',
  'On the way',
  'Delivered',
  'Other',
]);

const initialFields = {
  from: {
    type: '',
    vendor: null,
    vendorName: '',
    customer: null,
    customerName: '',
    address: '',
  },
  to: {
    type: '',
    vendor: null,
    vendorName: '',
    customer: null,
    customerName: '',
    address: '',
  },
  name: '',
  quantity: 0,
  recommendedModeOfTransport: {
    mode: '',
    vehicleNumber: '',
    contactDetails: '',
    trackingUrl: '',
  },
  status: '',
  remarks: '',
};

const MAX_OPTION_LENGTH = 20;

// --- Helper Functions ---
const findOption = (options, value) => options.find((el) => el.value === value);
const mapOptions = (list) =>
  Array.isArray(list)
    ? list.map((el) => ({ label: el?.name, value: el?._id }))
    : [];
function addToOptions(optionsArray, item) {
  if (!item || !item.value) return;
  // Prevent duplicate addition based on value
  if (!optionsArray.find((opt) => opt.value === item.value)) {
    optionsArray.push(item);
  }
}

function TransportIndentModal({ setOpen, data, startProcess }) {
  // --- State ---
  const [formData, setFormData] = useState(initialFields);
  const [showInputFor, setShowInputFor] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [quantityNA, setQuantityNA] = useState(false);
  const [errors, setErrors] = useState({});
  const [customVendors, setCustomVendors] = useState([]);
  const [customCustomers, setCustomCustomers] = useState([]);
  const [showVendorModal, setShowVendorModal] = useState(false);
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [ColumnsValue, setColumnsValue] = useState([]);
  const [newAddedVendorId, setNewAddedVendorId] = useState('');
  const [customerResponse, setCustomerResponse] = useState({});
  const [showModalFor, setShowModalFor] = useState('');

  // --- API Data ---
  const { data: allVendors } = useGetAllVendorsForOptionsQuery();
  const { data: allCustomers } = useGetCustomerOptionsQuery();

  // --- Mutations ---
  const [createTransportIndent, { isLoading }] =
    useCreateTransportIndentMutation();
  const [updateIndent] = useUpdateTransportIndentMutation();

  const isEditMode = !!data;

  // --- Memoized Options ---
  const vendorOptions = useMemo(
    () => [
      { label: '+ Create Tempoary', value: 'create_new' },
      { label: '+ Create New', value: 'create_original' },
      ...customVendors,
      ...mapOptions(allVendors),
    ],
    [customVendors, allVendors]
  );
  const customerOptions = useMemo(
    () => [
      { label: '+ Create Temporary', value: 'create_new' },
      { label: '+ Create Original', value: 'create_original' },
      ...customCustomers,
      ...(allCustomers?.customers || [])?.map((el) => ({
        label: el?.company_name,
        value: el?._id,
      })),
    ],
    [customCustomers, allCustomers]
  );

  const addInSetter = (options, item, setter) => {
    if (!item || !item.value) return;
    // Prevent duplicate addition based on value
    if (!options.find((opt) => opt.value === item.value)) {
      setter((prev) => {
        if (!prev.find((opt) => opt.value === item.value)) {
          return [...prev, item];
        }
        return prev;
      });
    }
  };

  // --- Effect: Prefill from edit mode ---
  useEffect(() => {
    if (!data) return;

    let filled = {
      ...data,
      from: { ...data.from },
      to: { ...data.to },
      recommendedModeOfTransport: { ...data.recommendedModeOfTransport },
    };

    // from & to type
    addToOptions(FROM_TO_OPTIONS, {
      label: filled.from?.type,
      value: filled.from?.type,
    });
    addToOptions(FROM_TO_OPTIONS, {
      label: filled.to?.type,
      value: filled.to?.type,
    });

    // transport mode
    addToOptions(TRANSPORT_OPTIONS, {
      label: filled.recommendedModeOfTransport?.mode,
      value: filled.recommendedModeOfTransport?.mode,
    });

    // status
    addToOptions(STATUS_OPTIONS, {
      label: filled.status,
      value: filled.status,
    });

    const vOptions = (allVendors || [])?.map(({ _id, name }) => ({
      label: name,
      value: _id,
    }));

    const cOptions = (allCustomers?.customers || []).map(({ _id, name }) => ({
      label: name,
      value: _id,
    }));

    if (!filled?.from?.vendor) {
      addInSetter(
        vOptions,
        {
          label: filled.from?.vendorName,
          value: filled.from?.vendorName,
        },
        setCustomVendors
      );
    }
    if (!filled?.to?.vendor) {
      addInSetter(
        vOptions,
        {
          label: filled.to?.vendorName,
          value: filled.to?.vendorName,
        },
        setCustomVendors
      );
    }
    if (!filled?.from?.customer) {
      addInSetter(
        cOptions,
        {
          label: filled.from?.customerName,
          value: filled.from?.customerName,
        },
        setCustomCustomers
      );
    }
    if (!filled?.to?.customer) {
      addInSetter(
        cOptions,
        {
          label: filled.to?.customerName,
          value: filled.to?.customerName,
        },
        setCustomCustomers
      );
    }

    if (!filled.from?.vendor) filled.from.vendor = filled.from?.vendorName;
    if (!filled.to?.vendor) filled.to.vendor = filled.to?.vendorName;
    if (!filled.from?.customer)
      filled.from.customer = filled.from?.customerName;
    if (!filled.to?.customer) filled.to.customer = filled.to?.customerName;

    setQuantityNA(filled.quantity === 'NA');
    delete filled._id;
    delete filled.__v;
    setFormData(filled);
  }, [allCustomers?.customers, allVendors, data]);

  useEffect(() => {
    if (customerResponse && showModalFor) {
      setFormData((prev) => ({
        ...prev,
        [showModalFor]: {
          ...prev?.[showModalFor],
          customer: customerResponse?._id,
          customerName: customerResponse?.company_name,
        },
      }));
    }
  }, [customerResponse, showModalFor]);

  useEffect(() => {
    if (newAddedVendorId && showModalFor && allVendors) {
      setFormData((prev) => ({
        ...prev,
        [showModalFor]: {
          ...prev?.[showModalFor],
          vendor: newAddedVendorId,
          vendorName: allVendors?.find((el) => el._id === newAddedVendorId)
            ?.name,
        },
      }));
    }
  }, [newAddedVendorId, showModalFor, allVendors]);
  // --- Handler: Form Input Changes ---
  const handleEntityChange = useCallback((name, value) => {
    setShowInputFor('');
    setErrors((prev) => ({ ...prev, [`${name}`]: '' }));

    if (value === 'create_new') {
      setShowInputFor(name);
      return;
    }
    setFormData((prev) => ({
      ...prev,
      [name]: {
        type: value,
        vendor: '',
        customer: '',
        vendorName: '',
        customerName: '',
      },
    }));
  }, []);

  // Generic text field change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (value?.length > MAX_OPTION_LENGTH) {
      toast.error(`Maximum ${MAX_OPTION_LENGTH} characters allowed`);
      return;
    }
    setErrors((prev) => ({ ...prev, [name]: '' }));
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // --- Handler: Add New Option Modal ---
  const addNewOption = useCallback(() => {
    if (!inputValue.trim()) return;
    const optionToAdd = { label: inputValue, value: inputValue };

    if (
      ['from', 'to', 'recommendedModeOfTransport', 'status'].includes(
        showInputFor
      )
    ) {
      if (showInputFor === 'from' || showInputFor === 'to') {
        addToOptions(FROM_TO_OPTIONS, optionToAdd);
      }
      if (showInputFor === 'status') {
        addToOptions(STATUS_OPTIONS, optionToAdd);
      }
      if (showInputFor === 'recommendedModeOfTransport') {
        addToOptions(TRANSPORT_OPTIONS, optionToAdd);
      }

      setFormData((prev) => ({
        ...prev,
        [showInputFor === 'recommendedModeOfTransport'
          ? 'recommendedModeOfTransport'
          : showInputFor]:
          showInputFor === 'recommendedModeOfTransport'
            ? { ...prev.recommendedModeOfTransport, mode: inputValue }
            : showInputFor === 'status'
              ? inputValue
              : { ...(prev[showInputFor] || {}), type: inputValue },
      }));
    } else if (showInputFor.includes('vendor')) {
      // from-vendor or to-vendor
      const fieldType = showInputFor.split('-')[0]; // 'from' or 'to'
      setCustomVendors((prev) => [...prev, optionToAdd]);
      setFormData((prev) => ({
        ...prev,
        [fieldType]: {
          ...prev[fieldType],
          vendor: inputValue,
          vendorName: inputValue,
          customer: '',
          customerName: '',
        },
      }));
    } else if (showInputFor.includes('customer')) {
      // from-customer or to-customer
      const fieldType = showInputFor.split('-')[0];
      setCustomCustomers((prev) => [...prev, optionToAdd]);
      setFormData((prev) => ({
        ...prev,
        [fieldType]: {
          ...prev[fieldType],
          customer: inputValue,
          customerName: inputValue,
          vendor: '',
          vendorName: '',
        },
      }));
    }

    setInputValue('');
    setShowInputFor('');
  }, [inputValue, showInputFor]);

  // --- Validation ---
  const validate = useCallback(() => {
    const e = {};
    if (!formData.from?.type) e.from = 'From Type is required';
    if (!formData.to?.type) e.to = 'To Type is required';
    if (!formData.name) e.name = 'Indent name is required';
    if (!quantityNA && (!formData.quantity || formData.quantity < 1))
      e.quantity = 'Quantity is required or mark as N/A';
    if (formData.from.type === 'Vendor' && !formData.from.vendor)
      e.from = 'Vendor is required in From';
    if (formData.to.type === 'Vendor' && !formData.to.vendor)
      e.to = 'Vendor is required in To';
    if (formData.from.type === 'Customer' && !formData.from.customer)
      e.from = 'Customer is required in From';
    if (formData.to.type === 'Customer' && !formData.to.customer)
      e.to = 'Customer is required in To';
    if (
      formData.from?.type !== 'Vendor' &&
      formData.from?.type !== 'Customer'
    ) {
      if (!formData.from.address?.trim())
        e.from_address = 'Source address is required';
    }
    if (formData.to?.type !== 'Vendor' && formData.to?.type !== 'Customer') {
      if (!formData.to.address?.trim())
        e.to_address = 'Destination address is required';
    }

    setErrors(e);
    return Object.keys(e).length === 0;
  }, [formData, quantityNA]);

  // --- Submission ---
  const handleSubmit = async () => {
    if (!validate()) {
      toast.error('Fill all required fields');
      return;
    }
    let payload = { ...formData };
    // Only keep valid vendor/customer ids (if Not Keep Null Values)
    const vOptions = allVendors?.map(({ _id, name }) => ({
      value: _id,
      label: name,
    }));
    const cOptions = allCustomers?.customers?.map(({ _id, name }) => ({
      value: _id,
      label: name,
    }));
    if (!findOption(vOptions, formData.from.vendor)) payload.from.vendor = null;
    if (!findOption(vOptions, formData.to.vendor)) payload.to.vendor = null;
    if (!findOption(cOptions, formData.from.customer))
      payload.from.customer = null;
    if (!findOption(cOptions, formData.to.customer)) payload.to.customer = null;
    if (isEditMode) {
      payload.isStarted = startProcess;
      const res = await updateIndent({ data: payload, id: data?._id });
      if (!res.error) {
        toast.success(
          `Transport Indent ${startProcess ? 'Started' : 'Updated'} Successfully`
        );
        resetModal();
      } else toast.error('Failed to update transport indent');
    } else {
      const res = await createTransportIndent(payload);
      if (!res.error) {
        toast.success('Transport Indent Created Successfully');
        resetModal();
      } else toast.error('Failed to create transport indent');
    }
  };

  function resetModal() {
    setFormData(initialFields);
    setShowInputFor('');
    setInputValue('');
    setQuantityNA(false);
    setErrors({});
    setOpen(false);
  }

  // --- Renderers ---
  function renderCustomOptionInput() {
    if (!showInputFor) return null;

    const getInputTitle = () => {
      if (showInputFor.includes('vendor')) {
        const field = showInputFor.split('-')[0];
        return `Add New Vendor for ${field.charAt(0).toUpperCase() + field.slice(1)} Location`;
      }
      if (showInputFor.includes('customer')) {
        const field = showInputFor.split('-')[0];
        return `Add New Customer for ${field.charAt(0).toUpperCase() + field.slice(1)} Location`;
      }
      if (showInputFor === 'from') {
        return `Add New Source Location`;
      }
      if (showInputFor === 'to') {
        return `Add New Destination Location`;
      }
      return `Add New ${showInputFor.replace(/([A-Z])/g, ' $1').toLowerCase()}`;
    };

    return (
      <Card
        size="small"
        className="bg-blue-50 border-blue-200 px-4 py-2 mb-4"
        title={
          <div className="flex items-center gap-2">
            <PlusOutlined className="text-blue-600" />
            <span className="text-sm">{getInputTitle()}</span>
          </div>
        }
      >
        <Space.Compact style={{ width: '100%' }}>
          <Input
            placeholder={`Enter name`}
            value={inputValue}
            onChange={(e) => {
              if (e.target.value.length > MAX_OPTION_LENGTH) {
                toast.error(`Maximum ${MAX_OPTION_LENGTH} characters allowed`);
                return;
              }
              setInputValue(e.target.value);
            }}
            onPressEnter={addNewOption}
            autoFocus
          />
          <Button
            type="primary"
            onClick={addNewOption}
            disabled={!inputValue.trim()}
          >
            Add
          </Button>
          <Button
            onClick={() => {
              setShowInputFor('');
              setInputValue('');
            }}
          >
            Cancel
          </Button>
        </Space.Compact>
      </Card>
    );
  }

  function renderEntityInfo(kind) {
    const curr = formData[kind];
    const fieldError = errors[kind];
    const locationLabel = kind === 'from' ? 'Source' : 'Destination';
    const addressError = errors[`${kind}_address`];

    if (curr.type === 'Vendor') {
      return (
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">
            {locationLabel} Vendor<span className="text-red-500 ml-1">*</span>
          </label>
          <SelectV2
            options={vendorOptions}
            name={kind}
            value={curr.vendor}
            onChange={(e) => {
              if (e.target.value === 'create_new') {
                setShowInputFor(`${kind}-vendor`);
                return;
              }
              if (e.target.value === 'create_original') {
                setShowVendorModal(true);
                setShowModalFor(kind);
                return;
              }
              const name =
                vendorOptions.find((i) => i.value === e.target.value)?.label ||
                '';
              setFormData((prev) => ({
                ...prev,
                [kind]: {
                  ...prev[kind],
                  vendor: e.target.value,
                  vendorName: name,
                  customer: '',
                  customerName: '',
                },
              }));
            }}
            placeholder={`Select ${locationLabel.toLowerCase()} vendor`}
            className={fieldError ? 'border-red-500' : ''}
          />
          {curr.vendorName && (
            <Tag color="blue" className="mt-1">
              📍 {curr.vendorName}
            </Tag>
          )}
        </div>
      );
    }

    if (curr.type === 'Customer') {
      return (
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">
            {locationLabel} Customer<span className="text-red-500 ml-1">*</span>
          </label>
          <SelectV2
            options={customerOptions}
            name={kind}
            value={curr.customer}
            onChange={(e) => {
              if (e.target.value === 'create_new') {
                setShowInputFor(`${kind}-customer`);
                return;
              }
              if (e.target.value === 'create_original') {
                setShowCustomerModal(true);
                setShowModalFor(kind);
                return;
              }
              const name =
                customerOptions.find((i) => i.value === e.target.value)
                  ?.label || '';
              setFormData((prev) => ({
                ...prev,
                [kind]: {
                  ...prev[kind],
                  customer: e.target.value,
                  customerName: name,
                  vendor: '',
                  vendorName: '',
                },
              }));
            }}
            placeholder={`Select ${locationLabel.toLowerCase()} customer`}
            className={fieldError ? 'border-red-500' : ''}
          />
          {curr.customerName && (
            <Tag color="green" className="mt-1">
              🏢 {curr.customerName}
            </Tag>
          )}
        </div>
      );
    }

    if (curr.type && curr.type !== 'Vendor' && curr.type !== 'Customer') {
      return (
        <div className="space-y-2">
          <label className="text-sm font-semibold text-gray-700">
            {locationLabel} Address <span className="text-red-500 ml-1">*</span>
          </label>
          <Input.TextArea
            rows={2}
            placeholder={`Enter address for ${locationLabel.toLowerCase()}`}
            value={curr.address}
            onChange={(e) => {
              setFormData((prev) => ({
                ...prev,
                [kind]: {
                  ...prev[kind],
                  address: e.target.value,
                },
              }));
            }}
            maxLength={200}
          />
          {addressError && (
            <Text type="danger" className="text-xs">
              {addressError}
            </Text>
          )}
        </div>
      );
    }

    return (
      <div className="space-y-2">
        <div className="h-16 bg-gray-50 border-2 border-dashed border-gray-200 rounded-lg flex items-center justify-center">
          <Text className="text-gray-400 text-sm">
            {curr.type
              ? `Add Other Information In Remarks For This Type`
              : `Select ${locationLabel.toLowerCase()} type first`}
          </Text>
        </div>
      </div>
    );
  }
  return (
    <div>
      <Modal
        title={
          <div className="flex items-center gap-2">
            <TruckOutlined className="text-blue-600" />
            {isEditMode
              ? 'Update Transport Indent'
              : 'Create Transport Indent Request'}
          </div>
        }
        onCloseModal={resetModal}
        okText={isEditMode ? 'Update' : 'Create'}
        onSubmit={handleSubmit}
        submitText={isEditMode ? 'Update' : 'Create'}
        markClosable={false}
        btnIsLoading={isLoading}
        width={900}
      >
        {() => (
          <div className="space-y-6">
            <AddVendorModal
              openVendorAddModal={showVendorModal}
              setOpenVendorAddModal={setShowVendorModal}
              setNewAddedVendorId={setNewAddedVendorId}
            />
            {showCustomerModal && (
              <CustomerModalFormData
                setShowAddCustomer={setShowCustomerModal}
                ColumnsValue={ColumnsValue}
                setColumnsValue={setColumnsValue}
                response={customerResponse}
                setResponse={setCustomerResponse}
                title={'Add New Customer'}
                type={'Add'}
              />
            )}
            {renderCustomOptionInput()}

            {/* Location Information */}
            <Card
              title={
                <div className="flex items-center gap-2">
                  <EnvironmentOutlined className="text-green-600" />
                  Location Information
                </div>
              }
              size="small"
              className="px-4 py-2"
            >
              <div className="py-4">
                <div className="grid grid-cols-1 md:grid-cols-2  gap-8">
                  {/* FROM Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 font-semibold text-sm">
                          F
                        </span>
                      </div>
                      <Title level={5} className="m-0 text-green-600">
                        From Location
                      </Title>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-gray-700">
                        Source Type<span className="text-red-500 ml-1">*</span>
                      </label>
                      <SelectV2
                        options={FROM_TO_OPTIONS}
                        name="from"
                        value={formData.from.type}
                        onChange={(e) =>
                          handleEntityChange('from', e.target.value)
                        }
                        placeholder="Select source location type"
                        className={errors.from ? 'border-red-500' : ''}
                      />
                      {errors.from && (
                        <Text type="danger" className="text-xs">
                          {errors.from}
                        </Text>
                      )}
                    </div>

                    {renderEntityInfo('from')}
                  </div>

                  {/* TO Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-semibold text-sm">
                          T
                        </span>
                      </div>
                      <Title level={5} className="m-0 text-blue-600">
                        To Location
                      </Title>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-gray-700">
                        Destination Type
                        <span className="text-red-500 ml-1">*</span>
                      </label>
                      <SelectV2
                        options={FROM_TO_OPTIONS}
                        name="to"
                        value={formData.to.type}
                        onChange={(e) =>
                          handleEntityChange('to', e.target.value)
                        }
                        placeholder="Select destination location type"
                        className={errors.to ? 'border-red-500' : ''}
                      />
                      {errors.to && (
                        <Text type="danger" className="text-xs">
                          {errors.to}
                        </Text>
                      )}
                    </div>

                    {renderEntityInfo('to')}
                  </div>
                </div>
              </div>
            </Card>

            {/* Basic Information */}
            <Card
              title={
                <div className="flex items-center gap-2">
                  <FileTextOutlined className="text-purple-600" />
                  Basic Information
                </div>
              }
              size="small"
              className="px-4 py-2"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
                {/* Indent Name */}
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-gray-700">
                    Indent Name<span className="text-red-500 ml-1">*</span>
                  </label>
                  <Input
                    placeholder="Enter descriptive name for this indent"
                    name="name"
                    value={formData.name}
                    disabled={isEditMode}
                    onChange={handleInputChange}
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <Text type="danger" className="text-xs">
                      {errors.name}
                    </Text>
                  )}
                </div>

                {/* Quantity */}
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-gray-700">
                    Quantity
                    {!quantityNA && (
                      <span className="text-red-500 ml-1">*</span>
                    )}
                  </label>
                  <Input
                    type="number"
                    disabled={quantityNA}
                    placeholder="Enter quantity"
                    name="quantity"
                    value={quantityNA ? 'N/A' : formData.quantity}
                    onChange={handleInputChange}
                    className={errors.quantity ? 'border-red-500' : ''}
                    min="0"
                  />
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={quantityNA}
                      onChange={(checked) => {
                        setQuantityNA(checked);
                        setErrors((prev) => ({ ...prev, quantity: '' }));
                        setFormData((prev) => ({
                          ...prev,
                          quantity: checked ? 'NA' : 0,
                        }));
                      }}
                      size="small"
                    />
                    <Text className="text-sm text-gray-600">Mark as N/A</Text>
                  </div>
                  {errors.quantity && (
                    <Text type="danger" className="text-xs">
                      {errors.quantity}
                    </Text>
                  )}
                </div>
              </div>
            </Card>

            {/* Transport Details */}
            <Card
              title={
                <div className="flex items-center gap-2">
                  <TruckOutlined className="text-blue-600" /> Transport Details
                </div>
              }
              size="small"
              className="px-4 py-2"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-gray-700">
                    Recommended Mode of Transport
                  </label>
                  <SelectV2
                    options={TRANSPORT_OPTIONS}
                    name="recommendedModeOfTransport"
                    value={formData.recommendedModeOfTransport?.mode || ''}
                    onChange={(e) => {
                      if (e.target.value === 'create_new')
                        setShowInputFor('recommendedModeOfTransport');
                      else
                        setFormData((prev) => ({
                          ...prev,
                          recommendedModeOfTransport: {
                            ...prev.recommendedModeOfTransport,
                            mode: e.target.value,
                          },
                        }));
                    }}
                    placeholder="Select transport mode"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-gray-700">
                    Initial Status
                  </label>
                  <SelectV2
                    options={STATUS_OPTIONS}
                    name="status"
                    value={formData.status}
                    onChange={(e) => {
                      if (e.target.value === 'create_new')
                        setShowInputFor('status');
                      else
                        setFormData((prev) => ({
                          ...prev,
                          status: e.target.value,
                        }));
                    }}
                    placeholder="Select initial status"
                  />
                </div>
              </div>
              {formData.recommendedModeOfTransport.mode && (
                <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
                  <Title level={5} className="mb-4 text-gray-700">
                    Transport Information
                  </Title>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {['vehicleNumber', 'contactDetails', 'trackingUrl'].map(
                      (field, idx) => (
                        <div className="space-y-2" key={field}>
                          <label className="text-sm font-medium text-gray-600">
                            {
                              [
                                'Vehicle Number',
                                'Contact Details',
                                'Tracking URL',
                              ][idx]
                            }
                          </label>
                          <Input
                            placeholder={`Enter ${['vehicle number', 'contact details', 'tracking url'][idx]}`}
                            value={formData.recommendedModeOfTransport[field]}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                recommendedModeOfTransport: {
                                  ...prev.recommendedModeOfTransport,
                                  [field]: e.target.value,
                                },
                              }))
                            }
                          />
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}
            </Card>

            {/* Additional Info */}
            <Card
              title={
                <div className="flex items-center gap-2">
                  <FileTextOutlined className="text-purple-600" />
                  Additional Information
                </div>
              }
              size="small"
              className="px-4 py-2"
            >
              <div className="space-y-2 py-4">
                <label className="text-sm font-semibold text-gray-700">
                  Remarks
                </label>
                <Input.TextArea
                  placeholder="Enter any additional remarks or special instructions"
                  value={formData.remarks}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      remarks: e.target.value,
                    }))
                  }
                  rows={3}
                  showCount
                  maxLength={500}
                />
              </div>
            </Card>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default TransportIndentModal;
