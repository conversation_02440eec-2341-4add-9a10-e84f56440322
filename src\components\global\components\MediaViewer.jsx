import PdfViewer from './PdfViewer';

function MediaViewer({ media, setMedia, onClose }) {
  return (
    <>
      {media?.type === 'application/pdf' ? (
        <div
          className="z-[10000] fixed top-0 left-0 h-screen w-screen flex justify-center items-center bg-black/20"
          onClick={() => {
            if (onClose) {
              onClose();
            } else {
              setMedia({ data: '', type: '' });
            }
          }}
        >
          <div className="relative w-[95%] h-[95%]">
            <PdfViewer
              file={media?.data}
              name={media?.name}
              closeClick={() => setMedia({ data: '', type: '' })}
            />
          </div>
        </div>
      ) : (
        <div
          className="z-[10000] fixed top-0 left-0 h-screen w-screen flex justify-center items-center bg-black/20"
          onClick={() => {
            if (onClose) {
              onClose();
            } else {
              setMedia({ data: '', type: '' });
            }
          }}
        >
          <img
            src={media?.data}
            alt=""
            className="h-[90%] aspect-video object-contain"
          />
        </div>
      )}
    </>
  );
}

export default MediaViewer;
