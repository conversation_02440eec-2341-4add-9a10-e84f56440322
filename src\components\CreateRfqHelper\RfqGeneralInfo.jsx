// Importing necessary helper functions and components
import {
  deliveryDateOptions,
  generateDateString,
  generatePrefixId,
  getLocalDate,
} from '../../helperFunction';
import Input from '../global/components/Input';
import MultiSelect from '../global/components/MultiSelect';
import SelectV2 from '../global/components/SelectV2';
import Textarea from '../global/components/Textarea';
import { handleSetDate } from './CreateRfqHelperFun';

// Ant Design imports for UI enhancement
import { Card, Row, Col, Typography, Space, Divider, Tag } from 'antd';
import {
  CalendarOutlined,
  IdcardOutlined,
  UserOutlined,
} from '@ant-design/icons';

const { Text } = Typography;

/**
 This component is responsible for rendering the general information section of the RFQ (Request for Quotation).
 It includes details like RFQ ID, date, vendor selection, delivery date, address, company details, and payment terms.
 */
const RfqGeneralInfo = ({
  defaultParam, //defaultParam include configurations for the RFQ.
  vendor, //The currently selected vendor(s)
  setOpenVendorAddModal, //Function to toggle the modal for adding a new vendor.
  setVendor, //Function to update the selected vendor(s).
  vendorData, //List of available vendors to select from.
  DateValue, //The current value of the date selection input.
  deliveryDate, //The selected delivery date.
  setDeliveryDate, //Function to update the delivery date.
  setDateValue, //Function to update the date value.
  addressSelector, //Function that renders the address selection component.
  contactSelector, //Function that renders the contact selection component.
  emailSelector, //Function that renders the email selection component.
  ViewCompanyDetails, //The details of the company being viewed.
  paymentTerm, //The selected payment term.
  PaymentTermOptions, //List of available payment terms to select from.
  setShowAddNewModal, // Function to toggle the modal for adding a new payment term.
  setPaymentTerm, //Function to update the payment term.
  deliveryAddress, //The specified delivery address.
  setDeliveryAddress, //Function to update the delivery address.
  IdGenComp,
  idCompData,
  id,
  rfqId,
}) => {
  return (
    <Card className="rfq-general-info-card mb-6">
      <div className="p-6">
        {/* Header Section with RFQ ID, Date, and Task ID */}
        <div className="mb-6">
          <Row gutter={[24, 16]} align="middle">
            <Col xs={24} lg={12}>
              <Space align="center" size="middle">
                <IdcardOutlined className="text-base text-blue-500" />
                <Text strong className="text-base text-gray-800">
                  RFQ ID:
                </Text>
                <div className="min-w-[200px]">
                  {id === 'new' || id === 'indent' ? (
                    <IdGenComp {...idCompData} />
                  ) : (
                    <Input value={rfqId} disabled className="w-full" />
                  )}
                </div>
              </Space>
            </Col>

            <Col xs={24} lg={12}>
              <Row gutter={16} justify="end">
                <Col>
                  <Space align="center" size="small">
                    <CalendarOutlined className="text-base text-blue-500" />
                    <Text strong className="text-gray-800">
                      RFQ Date:
                    </Text>
                    <Tag color="blue" className="m-0">
                      {getLocalDate(Date.now())}
                    </Tag>
                  </Space>
                </Col>
                <Col>
                  <Space align="center" size="small">
                    <UserOutlined className="text-base text-blue-500" />
                    <Text strong className="text-gray-800">
                      Task ID:
                    </Text>
                    <Tag color="green" className="m-0">
                      {generatePrefixId(defaultParam?.prefixIds?.['taskId'])}
                    </Tag>
                  </Space>
                </Col>
              </Row>
            </Col>
          </Row>
        </div>

        <Divider className="my-6" />

        {/* Main Form Section */}
        <Row gutter={[24, 24]}>
          {/* Vendor Selection */}
          <Col xs={24} lg={12}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong style={{ color: '#595959', fontSize: 14 }}>
                Select Vendor
              </Text>
              <MultiSelect
                className="my-2"
                placeholder="Select Vendor"
                value={vendor}
                options={[
                  { label: '+ Add New Vendor', value: '+' },
                  ...(vendorData?.map((vendor) => ({
                    label: vendor?.name,
                    value: vendor._id,
                  })) || []),
                ]}
                onChange={(e) => {
                  const allSelectedIds = e.target.value?.map((el) => el?.value);
                  if (allSelectedIds?.includes('+')) {
                    setOpenVendorAddModal(true);
                  } else {
                    setVendor(allSelectedIds);
                  }
                }}
                style={{ width: '100%' }}
              />
            </Space>
          </Col>

          {/* Delivery Date Selection */}
          <Col xs={24} lg={12}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong style={{ color: '#595959', fontSize: 14 }}>
                Delivery Date
              </Text>
              {DateValue === '+' ? (
                <Input
                  type="date"
                  style={{ width: '100%' }}
                  id="deliveryDate"
                  name="deliveryDate"
                  placeholder="Delivery Date"
                  value={deliveryDate.split('/').reverse().join('-')}
                  onChange={(e) => setDeliveryDate(e.target.value)}
                />
              ) : (
                <SelectV2
                  options={deliveryDateOptions}
                  style={{ width: '100%' }}
                  placeholder={
                    deliveryDate
                      ? generateDateString(new Date(deliveryDate))
                      : 'Select'
                  }
                  onChange={(e) => {
                    if (e.target.value === '+') {
                      setDeliveryDate('');
                      setDateValue(e.target.value);
                    } else {
                      handleSetDate(e, setDateValue, setDeliveryDate);
                    }
                  }}
                />
              )}
            </Space>
          </Col>

          {/* Contact Selector */}
          <Col xs={24} lg={12}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {contactSelector()}
            </Space>
          </Col>

          {/* Email Selector */}
          <Col xs={24} lg={12}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {emailSelector()}
            </Space>
          </Col>

          {/* Delivery Address */}
          <Col xs={24} lg={12}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong style={{ color: '#595959', fontSize: 14 }}>
                Delivery Address
              </Text>
              <Textarea
                placeholder="Enter Delivery Address"
                onChange={(e) => setDeliveryAddress(e.target.value)}
                value={deliveryAddress}
                id="ignore"
                rows={4}
                style={{ width: '100%' }}
              />
              <p id="only-on-print" style={{ display: 'none' }}>
                {deliveryAddress || ''}
              </p>
            </Space>
          </Col>

          {/* Company Details */}
          <Col xs={24} lg={12}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong style={{ color: '#595959', fontSize: 14 }}>
                Company Details
              </Text>
              <Textarea
                value={ViewCompanyDetails}
                disabled
                id="ignore"
                rows={4}
                style={{ width: '100%' }}
              />
              <p id="only-on-print" style={{ display: 'none' }}>
                {ViewCompanyDetails || ''}
              </p>
            </Space>
          </Col>

          {/* Address Selector - Full Width */}
          <Col xs={24}>
            <Card
              size="small"
              style={{
                backgroundColor: '#fafafa',
                border: '1px solid #d9d9d9',
              }}
            >
              {addressSelector()}
            </Card>
          </Col>

          {/* Payment Term */}
          <Col xs={24} lg={12}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong style={{ color: '#595959', fontSize: 14 }}>
                Payment Term
              </Text>
              <SelectV2
                value={paymentTerm}
                options={[
                  { label: '+ Add New PaymentTerm', value: '+' },
                  ...(PaymentTermOptions?.map((option) => ({
                    label: option,
                    value: option,
                  })) || []),
                ]}
                onChange={(e) => {
                  if (e.target.value === '+') {
                    setShowAddNewModal(true);
                  } else {
                    setPaymentTerm(e.target.value);
                  }
                }}
                id="ignore"
                style={{ width: '100%' }}
              />
              <p id="only-on-print" style={{ display: 'none' }}>
                {paymentTerm || ''}
              </p>
            </Space>
          </Col>
        </Row>
      </div>
    </Card>
  );
};

export default RfqGeneralInfo;
