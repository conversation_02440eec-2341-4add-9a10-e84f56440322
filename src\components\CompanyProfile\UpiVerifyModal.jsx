import { But<PERSON>, Collapse, Modal, QRCode } from 'antd';
import { useEffect, useState } from 'react';

const CollapseItems = [
  {
    key: '1',
    label: 'How to verify UPI',
    children: (
      <>
        <ul>
          <li>1. Open app that supports UPI payment.</li>
          <li>2. Scan the QR with the app.</li>
          <li>3. Verify the name you get on the app.</li>
          <li>4. Click on the verified checkbox below.</li>
        </ul>
      </>
    ),
  },
  {
    key: '2',
    label: 'Terms and conditions',
    children: (
      <>
        <h2>Consent for Storing UPI ID</h2>
        <p>
          By providing your UPI ID in this app, you acknowledge and agree to the
          following terms and conditions:
        </p>

        <ol>
          <li>
            <strong>Collection and Use of UPI ID:</strong> We collect your UPI
            ID for the purpose of facilitating payments and improving your user
            experience within the app. This may include using your UPI ID to
            process payments, perform transactions, or connect you with payment
            service providers. Your UPI ID will only be used for the specified
            purposes and will not be shared with third parties without your
            consent unless required by law.
          </li>

          <li>
            <strong>Data Security:</strong> We take the security of your
            personal information seriously. Your UPI ID will be stored in
            accordance with industry-standard encryption and data protection
            practices to ensure that it remains secure and confidential. We will
            implement appropriate measures to protect your information from
            unauthorized access or disclosure.
          </li>

          <li>
            <strong>User Control:</strong> You have the right to modify, update,
            or delete your UPI ID at any time within the app. You can also
            choose to withdraw your consent for storing your UPI ID, in which
            case we will remove your details from our system, subject to any
            legal or transactional obligations.
          </li>

          <li>
            <strong>Consent for Payment Processing:</strong> By providing your
            UPI ID, you authorize us to initiate payment requests on your behalf
            for transactions within the app. Please note that we are not
            responsible for the outcome of any transaction or payment processing
            issues, which will be handled by the relevant payment service
            provider.
          </li>

          <li>
            <strong>Changes to Terms:</strong> We may update these terms from
            time to time to reflect changes in our services, security measures,
            or legal requirements. We will notify you of any material changes
            and will request your consent if necessary.
          </li>
        </ol>
      </>
    ),
  },
];

const generateUpiString = (str = '') => {
  if (!str) return 'invalid upi id';
  const upiId = str.trim();

  const isValidUPI = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+$/i.test(upiId);

  const isPhoneNumber = /^\d{10}$/g.test(upiId);

  if (!isValidUPI && !isPhoneNumber) {
    return 'invalid upi id';
  }
  const finalUpiId = isPhoneNumber ? `${upiId}@upi` : upiId;

  return `upi://pay?pa=${finalUpiId}&cu=INR`;
};

function UpiVerifyModal({ data, setData, setMainData, setErrors }) {
  const [checks, setChecks] = useState({ upi: false, terms: false });

  useEffect(() => {
    if (data?.upiVerify) {
      setChecks({ upi: true, terms: true });
    } else {
      setChecks({ upi: false, terms: false });
    }
  }, [data?.upiVerify]);

  const onVerify = () => {
    setMainData((prev) => ({
      ...prev,
      bankDetails: prev?.bankDetails?.map((item, idx) => {
        if (idx === data?.index) {
          return { ...item, upiVerify: true };
        }

        return item;
      }),
    }));
    setErrors((prev) => {
      if (prev?.upi) prev.upi[data?.index] = '';
      return prev;
    });
    setData(null);
  };

  return (
    <Modal
      open={!!data}
      title="Verify UPI"
      width={'80%'}
      height={'70%'}
      onCancel={() => setData(null)}
      footer={[
        <Button
          key={'1'}
          type="primary"
          disabled={!checks?.upi || !checks?.terms}
          onClick={onVerify}
        >
          Verify
        </Button>,
        <Button key={'2'} onClick={() => setData(null)}>
          Cancel
        </Button>,
      ]}
    >
      <div className="w-full my-5 flex flex-col md:flex-row gap-4">
        <div className="">
          <QRCode
            value={data?.upiId ? generateUpiString(data?.upiId) : 'No upi Id'}
            status={
              !data?.upiId ? 'loading' : data?.upiVerify ? 'scanned' : 'active'
            }
            size={256}
          />
          <p className="text-center">UPI ID: {data?.upiId}</p>
        </div>
        <Collapse
          items={CollapseItems}
          className="w-full h-fit"
          defaultActiveKey={['1']}
        />
      </div>{' '}
      <div className="flex w-full items-center gap-2">
        <input
          id="qr"
          type="checkbox"
          checked={checks.upi || false}
          onChange={(e) =>
            setChecks((prev) => ({ ...prev, upi: e.target.checked }))
          }
        />
        <label htmlFor="qr">QR verified</label>
      </div>{' '}
      <div className="flex w-full items-center gap-2 mt-1">
        <input
          type="checkbox"
          id="terms"
          checked={checks.terms || false}
          onChange={(e) =>
            setChecks((prev) => ({ ...prev, terms: e.target.checked }))
          }
        />
        <label htmlFor="terms">I agree with the terms and conditions</label>
      </div>
    </Modal>
  );
}

export default UpiVerifyModal;
