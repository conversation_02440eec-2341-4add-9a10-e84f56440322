import { Fragment, useContext, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  generatePrefixId,
  renderElemBasedOnFormat,
} from '../../helperFunction';
import {
  useCreateCustomerMutation,
  useCreateValueMutation,
  useLazyGetAllcolumnsQuery,
} from '../../slices/customerDataSlice';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { Store } from '../../store/Store';
import Modal from '../global/components/Modal';
import { Input, Label } from '../v2';

const AddCustomerModal = ({
  setShowCustomerModal,
  getCustomers,
  setCustomers,
}) => {
  const [SearchParams, setSearchParams] = useSearchParams({
    customer_id: '',
  });
  const [updateDefaults] = useUpdateDefaultsMutation();
  const [customerId, setCustomerId] = useState('');
  const [customerIdFormat, setCustomerIdFormat] = useState({});
  const [format, setFormat] = useState({});
  const [newDefault, setDefault] = useState();
  const [CustomerData, setCustomerData] = useState({
    name: '',
    company_name: '',
    unique_id: '',
    phone_no: '',
    address: '',
    gstNumber: [''],
    billingAddress: '',
    deliveryAddress: '',
    paymentTerm: '',
  });
  const [AllCreatedColums, setCreatedColumns] = useState([]);
  const [ColumnsValue, setColumnsValue] = useState({});
  const [getAllcolumns, Allcolumns] = useLazyGetAllcolumnsQuery();
  const [createCustomer] = useCreateCustomerMutation();
  const [createValue] = useCreateValueMutation();
  const { defaults } = useContext(Store);

  useEffect(() => {
    if (defaults) {
      setDefault(defaults?.defaultParam);
      let temp = {
        ...(defaults?.defaultParam?.prefixIds?.['customerId'] || {}),
      };
      Object.keys(temp).forEach((elem) => {
        if (elem.includes('UserEntry')) {
          temp[elem] = '';
        }
      });
      setFormat(temp);
    }
  }, [defaults]);

  useEffect(() => {
    if (format) {
      renderElemBasedOnFormat(
        'customerId',
        format,
        setFormat,
        setCustomerIdFormat,
        setDefault,
        defaults
      );
      let id = generatePrefixId(format);
      setCustomerId(id);
    }
  }, [format]); //eslint-disable-line

  async function addCustomer() {
    for (let keys in CustomerData) {
      if (!CustomerData[keys]) {
        if (keys === 'phone_no') {
          toast.error(`Phone Number Is Required`);
          return;
        }
        toast.error(
          `${keys.charAt(0).toUpperCase() + keys.slice(1)} Is Required`
        );
        return;
      }
    }
    const data = await createCustomer({
      ...CustomerData,
      unique_id: customerId,
    }).unwrap();

    if (data?.customer) {
      setCustomerData({
        name: '',
        company_name: '',
        unique_id: '',
        phone_no: '',
        address: '',
        gstNumber: '',
      });
      let tmp = { ...ColumnsValue };
      await new Promise((resolve) => {
        for (let key in tmp) {
          tmp = {
            ...tmp,
            [key]: {
              ...tmp[key],
              customer_id: data?.customer?._id,
            },
          };
        }
        resolve(true);
      });
      await SubmitValue(false, tmp);
      toast.success('Customer Created');
      await updateDefaults(newDefault).unwrap();
      setFormat(newDefault?.prefixIds?.['customerId']);
      setShowCustomerModal(false);
      const customers = await getCustomers().unwrap();
      setCustomers(customers?.customers);
    }
  }

  const SubmitValue = async (showalert = true, columnData) => {
    const body = {};
    for (let parentKey in columnData) {
      const founded = AllCreatedColums?.find((each) => {
        return each?.column_name === parentKey;
      });
      if (founded?.mandatory && !columnData[parentKey].column_value) {
        toast.error(`Column ${parentKey} Is Mandatory`);
        return;
      }
      if (columnData[parentKey].column_value) {
        body[parentKey] = columnData[parentKey];
      }
    }
    // if (Object.keys(body).length === 0) {
    //   toast.error("Please Add Atleast One Column's Value");
    //   return;
    // }

    const data = await createValue(body);
    if (data?.value?.length !== 0) {
      if (showalert) {
        toast.success('Columns value Added');
      }
      for (let parentKey in ColumnsValue) {
        setColumnsValue((prev) => {
          return {
            ...prev,
            [parentKey]: {
              column_value: '',
              customer_id: '',
            },
          };
        });
      }
    }
    setSearchParams(
      (prev) => {
        prev.set('customer_id', '');
        prev.set('new_customer_id', '');
        return prev;
      },
      {
        replace: true,
      }
    );
  };

  useEffect(() => {
    getAllcolumns();
  }, [getAllcolumns]);

  useEffect(() => {
    setCreatedColumns(Allcolumns?.data?.columns);
    const data = {};
    Allcolumns?.data?.columns?.map((each) => {
      setColumnsValue((prev) => {
        data[each?.column_name] = {
          customer_id: '',
          column_name: '',
          column_value: '',
        };
        return { ...prev, ...data };
      });
    });
    return () => {
      setColumnsValue({});
    };
  }, [Allcolumns]);
  useEffect(() => {
    setCreatedColumns(Allcolumns?.data?.columns);
  }, [Allcolumns]);
  return (
    <Modal
      title="Add Customer"
      onCloseModal={() => {
        setShowCustomerModal(false);
      }}
      onSubmit={() => {
        addCustomer();
      }}
    >
      {() => {
        return (
          <div className="input-container flex flex-col gap-y-4">
            <div className="input-wrapper">
              <Label htmlFor="name" className="!text-[18px]">
                Customer ID
              </Label>
              <div className="flex items-center justify-start h-fit w-full">
                {Object.keys(customerIdFormat || {}).map((elem, eIdx) => {
                  return (
                    <Fragment key={eIdx}>{customerIdFormat[elem]}</Fragment>
                  );
                })}
              </div>
            </div>
            <div className="input-wrapper">
              <Label htmlFor="name" className="!text-[18px]">
                Name
              </Label>
              <Input
                placeholder="Enter Name"
                name="name"
                type="text"
                value={CustomerData?.name || ''}
                onChange={(e) => {
                  setCustomerData((prev) => {
                    return {
                      ...prev,
                      name: e.target.value,
                    };
                  });
                }}
              />
            </div>
            <div className="input-wrapper">
              <Label htmlFor="companyName" className="!text-[18px]">
                Company Name
              </Label>
              <Input
                placeholder="Enter Company Name"
                name="companyName"
                type="text"
                value={CustomerData?.company_name || ''}
                onChange={(e) => {
                  setCustomerData((prev) => {
                    return {
                      ...prev,
                      company_name: e.target.value,
                    };
                  });
                }}
              />
            </div>
            <div className="input-wrapper">
              <Label htmlFor="email" className="!text-[18px]">
                Email
              </Label>
              <Input
                placeholder="Enter Email"
                type="email"
                name="email"
                value={CustomerData?.unique_id || ''}
                onChange={(e) => {
                  setCustomerData((prev) => {
                    return {
                      ...prev,
                      unique_id: e.target.value,
                    };
                  });
                }}
              />
            </div>
            <div className="input-wrapper">
              <Label htmlFor="phoneNumber" className="!text-[18px]">
                Phone Number
              </Label>
              <Input
                placeholder="Enter Phone Number"
                type="number"
                name="phoneNumber"
                value={CustomerData?.phone_no || ''}
                onChange={(e) => {
                  setCustomerData((prev) => {
                    return {
                      ...prev,
                      phone_no: e.target.value,
                    };
                  });
                }}
              />
            </div>
            <div className="input-wrapper">
              <Label htmlFor="address" className="!text-[18px]">
                Address
              </Label>
              <Input
                placeholder="Enter Address"
                name="address"
                type="text"
                value={CustomerData?.address || ''}
                onChange={(e) => {
                  setCustomerData((prev) => {
                    return {
                      ...prev,
                      address: e.target.value,
                    };
                  });
                }}
              />
            </div>
            <div className="input-wrapper">
              {Array.isArray(CustomerData?.gstNumber) &&
                CustomerData?.gstNumber?.map((gst, i) => {
                  return (
                    <>
                      <Label htmlFor="gstNumber" className="!text-[18px]">
                        GST Number
                      </Label>
                      <Input
                        placeholder="Enter GST Number"
                        name="gstNumber"
                        type="text"
                        value={
                          CustomerData?.gstNumber
                            ? CustomerData?.gstNumber?.[i]
                            : ''
                        }
                        onChange={(event) => {
                          const newValues = [...CustomerData?.gstNumber];
                          newValues[i] = event.target.value;
                          setCustomerData((prev) => ({
                            ...prev,
                            gstNumber: newValues,
                          }));
                        }}
                      />
                    </>
                  );
                })}
              <div
                onClick={() =>
                  setCustomerData((prev) => ({
                    ...prev,
                    gstNumber: [...(prev.gstNumber || []), ''],
                  }))
                }
                className=" text-blue-400 hover:text-blue-600 mt-1 cursor-pointer justify-end"
              >
                +Add More
              </div>
            </div>
            <div className="input-wrapper">
              <Label htmlFor="gstNumber" className="!text-[18px]">
                Billing Address
              </Label>
              <Input
                placeholder="Enter Billing Address"
                name="billingAddress"
                type="text"
                value={CustomerData?.billingAddress || ''}
                onChange={(e) => {
                  setCustomerData((prev) => {
                    return {
                      ...prev,
                      billingAddress: e.target.value,
                    };
                  });
                }}
              />
            </div>
            <div className="input-wrapper">
              <Label htmlFor="gstNumber" className="!text-[18px]">
                Delivery Address
              </Label>
              <Input
                placeholder="Enter Delivery Address"
                name="deliveryAddress"
                type="text"
                value={CustomerData?.deliveryAddress || ''}
                onChange={(e) => {
                  setCustomerData((prev) => {
                    return {
                      ...prev,
                      deliveryAddress: e.target.value,
                    };
                  });
                }}
              />
            </div>
            <div className="input-wrapper">
              <Label htmlFor="gstNumber" className="!text-[18px]">
                Payment Term
              </Label>
              <Input
                placeholder="Enter Payment Address"
                name="paymentTerm"
                type="text"
                value={CustomerData?.paymentTerm || ''}
                onChange={(e) => {
                  setCustomerData((prev) => {
                    return {
                      ...prev,
                      paymentTerm: e.target.value,
                    };
                  });
                }}
              />
            </div>
            {AllCreatedColums?.map((each) => {
              return (
                <div className="input-wrapper" key={each?.column_name}>
                  <Label className="!text-[18px]">
                    {each?.column_name}
                    {each?.mandatory && (
                      <span className="text-blue-500"> * </span>
                    )}
                  </Label>
                  <Input
                    value={ColumnsValue[each?.column_name]?.column_value || ''}
                    onChange={(e) => {
                      setColumnsValue((prev) => {
                        return {
                          ...prev,
                          [each?.column_name]: {
                            column_value: e.target.value,
                            customer_id: SearchParams?.get('customer_id'),
                            column_name: each?.column_name,
                          },
                        };
                      });
                    }}
                  />
                </div>
              );
            })}
          </div>
        );
      }}
    </Modal>
  );
};

export default AddCustomerModal;
