import { Card, Typography } from 'antd';
import {
  DownloadOutlined,
  FileExcelOutlined,
  InfoCircleOutlined,
  UploadOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

const ImportInstructions = () => {
  return (
    <div>
      <Card className="px-4 py-2">
        <Title level={4}>
          <InfoCircleOutlined className="text-blue-500 mr-2" />
          Import Instructions
        </Title>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <DownloadOutlined className="text-white text-lg" />
            </div>
            <Text strong className="block">
              1. Download Template
            </Text>
            <Text type="secondary" className="text-sm">
              Get the Excel template with required columns
            </Text>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <FileExcelOutlined className="text-white text-lg" />
            </div>
            <Text strong className="block">
              2. Fill Your Data
            </Text>
            <Text type="secondary" className="text-sm">
              Add customer information following the format
            </Text>
          </div>
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <UploadOutlined className="text-white text-lg" />
            </div>
            <Text strong className="block">
              3. Upload File
            </Text>
            <Text type="secondary" className="text-sm">
              Upload your completed Excel file
            </Text>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ImportInstructions;
