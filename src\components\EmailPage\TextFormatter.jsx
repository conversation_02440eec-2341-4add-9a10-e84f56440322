import { useState } from 'react';

const TextFormatter = () => {
  const [fontStyle, setFontStyle] = useState('Arial');
  const [textSize, setTextSize] = useState('14px');
  const [bold, setBold] = useState(false);
  const [italic, setItalic] = useState(false);
  const [underline, setUnderline] = useState(false);
  const [alignment, setAlignment] = useState('left');
  const [listType, setListType] = useState('');

  const handleListChange = (type) => {
    if (listType === type) {
      setListType('');
    } else {
      setListType(type);
    }
  };

  return (
    <>
      <div className="shadow-md  bg-white z-50 px-0 rounded-md flex">
        <div className="flex items-center space-x-1">
          <select
            id="font-style"
            value={fontStyle}
            onChange={(e) => setFontStyle(e.target.value)}
            className="p-1 border border-gray-300 rounded-md"
          >
            <option value="Arial">Arial</option>
            <option value="Verdana">Verdana</option>
            <option value="Tahoma">Tahoma</option>
          </select>

          <select
            id="text-size"
            value={textSize}
            onChange={(e) => setTextSize(e.target.value)}
            className=" border border-gray-300 rounded-md"
          >
            <option value="12px">12px</option>
            <option value="14px">14px</option>
            <option value="16px">16px</option>
          </select>

          <img
            src="../../../../src/assets/images/textformat.png"
            className={`p-2 rounded-md w-8 ${
              bold ? 'bg-gray-300' : 'bg-transparent'
            }`}
            onClick={() => setBold(!bold)}
          ></img>
          <img
            src="../../../../src/assets/images/textformat.png"
            className={`p-2 rounded-md w-8 ${
              bold ? 'bg-gray-300' : 'bg-transparent'
            }`}
            onClick={() => setItalic(!italic)}
          ></img>
          <img
            src="../../../../src/assets/images/textformat.png"
            className={`p-2 rounded-md w-8 ${
              bold ? 'bg-gray-300' : 'bg-transparent'
            }`}
            onClick={() => setUnderline(!underline)}
          ></img>

          <select
            id="alignment"
            value={alignment}
            onChange={(e) => setAlignment(e.target.value)}
            className="p-2 border border-gray-300 rounded-md"
          >
            <option value="left">Left</option>
            <option value="center">Center</option>
            <option value="right">Right</option>
          </select>
          <button
            className={`p-2 rounded-md ${
              listType === 'number' ? 'bg-gray-300' : 'bg-transparent'
            }`}
            onClick={() => handleListChange('number')}
          >
            <i className="fa fa-list-ol"></i>B
          </button>
          <button
            className={`p-2 rounded-md ${
              listType === 'bullet' ? 'bg-gray-300' : 'bg-transparent'
            }`}
            onClick={() => handleListChange('bullet')}
          >
            N<i className="fa fa-list-ul"></i>
          </button>
        </div>
      </div>
    </>
  );
};

export default TextFormatter;
