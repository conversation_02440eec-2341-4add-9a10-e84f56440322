import { Button, Empty, Typography } from 'antd';
import { Download, PlusCircle, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import { useDeleteManyDepartmentRowsMutation } from '../../slices/departmentRowApiSlice.js';
import { customConfirm } from '../../utils/customConfirm.js';
import Header from '../global/components/Header';
import CreateDynamicDep from './CreateDynamicDep.jsx';
import DynamicDepTable from './DynamicDepTable.jsx';
import { sanitizeColName } from './DynamicDepTableUtilityFunc.js';
const { Text } = Typography;

const DynamicDepDashboard = ({ data }) => {
  const { _id: depColId, departmentChildNav: childNav, columns } = data;
  const [deleteManyRows] = useDeleteManyDepartmentRowsMutation();
  const [openCreate, setOpenCreate] = useState(false);
  const [checkedRowIds, setCheckedRowIds] = useState([]);
  const [exportAbleData, setExportAbleData] = useState([]);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    if (searchParams.get('kanban') === 'true' && columns) {
      setOpenCreate(true);
    }
  }, [columns, searchParams]);

  const handleDeleteRows = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to delete ?',
      'error'
    );
    if (!confirm) return;
    const res = await deleteManyRows({ ids: checkedRowIds });
    if (!res?.error) {
      if (res?.data?.nonDeletableRows > 0) {
        toast.error(
          'Some Rows Already Used in Dashboard Which Cannot Be Deleted'
        );
      }
      if (res?.data?.deletableRows > 0) {
        toast.success('Rows Deleted Successfully');
      }
    }
    setCheckedRowIds([]);
  };

  const renderEmptyState = () => (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        padding: '40px 20px',
        background: '#ffffff',
        borderRadius: '8px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.03)',
      }}
    >
      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={null} />
      <div
        style={{
          textAlign: 'center',
          marginTop: '24px',
          marginBottom: '32px',
        }}
      >
        <Text
          style={{
            fontSize: '16px',
            fontWeight: '500',
            color: '#1f2937',
            marginBottom: '8px',
            display: 'block',
          }}
        >
          No Columns Created
        </Text>
        <Text
          style={{
            color: '#6b7280',
            fontSize: '14px',
          }}
        >
          Get started by creating columns for {childNav?.cname}
        </Text>
      </div>
      <Button
        type="primary"
        icon={<PlusCircle size={16} style={{ marginRight: '8px' }} />}
        onClick={() => navigate('/settings/departments/columns')}
        style={{ height: '40px' }}
      >
        Create Column
      </Button>
    </div>
  );

  const handleExport = () => {
    // Format data for Excel
    const formattedData = exportAbleData?.map((row) => {
      const formattedRow = {};

      data?.columns?.forEach((column) => {
        let field = row?.[sanitizeColName(column.name)];
        if (!field) {
          field = row?.[column?.name];
        }
        let value = field?.value;
        const type = column?.type;

        switch (type) {
          case 'checkbox':
            value = value ? 'True' : 'False';
            break;

          case 'bom':
          case 'multiCheckbox':
          case 'assets':
          case 'dropdown':
          case 'work order':
            value =
              Array.isArray(value) && value.length > 0
                ? value?.map((val) => val?.label).join(' ,')
                : 'Not Exists';
            break;

          case 'forms':
          case 'form management':
            value =
              Array.isArray(value) && value.length > 0 ? 'Exist' : 'Not Exist';
            break;

          case 'string':
          case 'number':
          case 'select':
            value = value ? value : '';
            break;

          case 'media':
          case 'audio':
            value = value && value.length > 0 ? 'Exist' : 'Not Exist';
            break;

          case 'date':
            value = value ? value : '';
            break;

          case 'hyperlink':
            value =
              Array.isArray(value) && value.length > 0
                ? value?.join(' ,')
                : 'Not Exist';
            break;

          default:
            value = '-';
            break;
        }

        // Assign formatted value to the row based on the column name
        formattedRow[column.name] = value;
      });

      return formattedRow;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet 1');
    const sheetName = childNav?.cname + '.xlsx';
    XLSX.writeFile(wb, sheetName);
  };

  return (
    <div>
      <div className="mb-6">
        <Header
          title={childNav?.cname}
          description={`Manage ${childNav?.cname} here`}
          hasInfoPopup={false}
          classNames="w-full"
        />
      </div>

      {columns?.length === 0 ? (
        renderEmptyState()
      ) : (
        <>
          <div className="flex gap-x-2 justify-self-end">
            <Button
              type="primary"
              disabled={checkedRowIds?.length === 0}
              danger
              icon={<Trash2 size={15} />}
              onClick={handleDeleteRows}
            >
              Delete
            </Button>
            <Button
              type="primary"
              onClick={handleExport}
              style={{
                backgroundColor: '#22c55e',
              }}
              className="hover:!bg-green-400 active:!bg-green-400"
              icon={<Download size={15} />}
            >
              Export
            </Button>
            <Button
              type="primary"
              icon={<PlusCircle size={15} />}
              onClick={() => setOpenCreate(true)}
            >
              Create
            </Button>
          </div>
          {openCreate && (
            <CreateDynamicDep
              openModal={openCreate}
              setOpenModal={setOpenCreate}
              columns={columns}
              data={data}
            />
          )}
          <DynamicDepTable
            depColId={depColId}
            columns={columns}
            checkedRowIds={checkedRowIds}
            setCheckedRowIds={setCheckedRowIds}
            data={data}
            setExportAbleData={setExportAbleData}
          />
        </>
      )}
    </div>
  );
};

export default DynamicDepDashboard;
