import { useContext } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { getLocalDateTime, unCamelCaseString } from '../../helperFunction';
import { useQueryAllProjectsQuery } from '../../slices/createInputApiSlice';
import { Store } from '../../store/Store';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import Button from '../global/components/Button';
import ButtonGroup from '../global/components/ButtonGroup';
import Header from '../global/components/Header';
import Pagination from '../global/components/Pagination';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import Tooltip from '../global/components/ToolTip';

const buttons = ['scheduled', 'unScheduled', 'partial'];

const ManagePlanner = () => {
  const navigate = useNavigate();

  const [serachParams, setSearchParams] = useSearchParams({
    page: 1,
    limit: PAGINATION_LIMIT,
    archived: false,
    ps: 'scheduled',
  });

  const page = +serachParams?.get('page');
  const limit = +serachParams?.get('limit');
  const po = serachParams?.get('po') || '';
  const ps = serachParams?.get('ps') || '';

  const { defaults } = useContext(Store);

  const {
    data: ciRes = {},
    isFetching: isFetchingQuery,
    isLoading: isLoadingQuery,
  } = useQueryAllProjectsQuery(
    {
      page,
      limit,
      po,
      ps,
    },
    { skip: !page || !limit, refetchOnMountOrArgChange: true }
  );

  const {
    results: createInputs = [],
    totalPages = 0,
    totalResults = 0,
  } = ciRes;

  return (
    <>
      <div className="w-full">
        <div className="flex items-center justify-between">
          <div className="w-5/6">
            <Header
              title="Job Planner"
              description=""
              hasInfoPopup
              infoTitle="Welcome to the Job Planner Page"
              infoDesc="The Job Planner is your centralized hub for comprehensive job management. 
          Organize your projects by scheduling, unscheduled, or partial status. 
          The easy-to-navigate interface presents a clear table with key details like Serial Number, 
          Date/Time, Project ID, Batches, Model Name, and Status,The Job Planner is your go-to solution for precision project management in production. 
          Streamline tasks, optimize resources, and visualize timelines for success. Foster seamless teamwork 
          with a collaboration hub and Customizable templates to save time, Simplify, and maximize success in your 
          production projects with the comprehensive suite of tools offered by the Job Planner."
              paras={[]}
              classNames=""
            />
          </div>
        </div>

        <div className="w-full mb-4">
          <ButtonGroup
            buttons={buttons}
            value={ps}
            onChange={(val) =>
              setSearchParams((prev) => {
                prev.set('ps', val);
                return prev;
              })
            }
          />
        </div>
        <div className="flex justify-end w-full bg-white rounded-tl-lg rounded-tr-lg mt-2">
          <div className="flex justify-between mb-2 relative mt-2 items-center ">
            <Button
              className="!h-7 !text-[13px] mr-4"
              onClick={() => navigate('/jobs/jobplanner/custommachineshifts')}
            >
              + Add
            </Button>
          </div>
        </div>

        {isLoadingQuery ? (
          <Spinner />
        ) : (
          <>
            <Table className="w-full rounded-xl">
              <Table.Head className="rounded-xl">
                <Table.Row className=" w-full rounded-xl">
                  <Table.Th className="capitalize">Date/Time</Table.Th>
                  <Table.Th className="capitalize">
                    {defaults?.defaultParam?.projectDefaults?.projectIdentifier}
                  </Table.Th>
                  <Table.Th className="capitalize">Batches</Table.Th>
                  <Table.Th className="capitalize">
                    {defaults?.defaultParam?.projectDefaults?.modelLabel}
                  </Table.Th>
                  <Table.Th className="capitalize">Status</Table.Th>
                </Table.Row>
              </Table.Head>

              <Table.Body>
                {createInputs.length === 0 && (
                  <Table.Row>
                    <Table.Td colSpan={'100%'}>
                      <p className="py-2 text-center">No project found</p>
                    </Table.Td>
                  </Table.Row>
                )}
                {createInputs.map((num) => {
                  if ('isVisible' in num) {
                    if (!num?.isVisible) {
                      return;
                    }
                  }
                  const noOfBatches = num?.goalsTable?.[0]?.tableData?.length;

                  return (
                    <Table.Row
                      key={num._id}
                      isFetching={isFetchingQuery}
                      onClick={() => navigate(`/jobs/jobplanner/${num._id}`)}
                      className="cursor-pointer hover:!bg-slate-100"
                    >
                      <Table.Td> {getLocalDateTime(num.createdAt)}</Table.Td>
                      <Table.Td>{num.id || '-'}</Table.Td>
                      <Table.Td>
                        {`#${
                          num?.projectNo > 1
                            ? num?.projectNo * noOfBatches - (noOfBatches - 1)
                            : num?.projectNo
                        } - #${num?.projectNo * noOfBatches}` || '-'}
                      </Table.Td>
                      <Table.Td className={'!min-w-[24rem] !w-[24rem]'}>
                        {num.modelName?.length > 55 ? (
                          <Tooltip text={num.modelName}>
                            {num.modelName?.slice(0, 55) + '...'}
                          </Tooltip>
                        ) : (
                          num.modelName
                        )}
                      </Table.Td>
                      <Table.Td>
                        {unCamelCaseString(num?.status) || 'NA'}
                      </Table.Td>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
            <Pagination
              limit={limit}
              page={page}
              totalPages={totalPages}
              totalResults={totalResults}
              setPage={(e) =>
                setSearchParams(
                  (prev) => {
                    prev.set('page', e);
                    return prev;
                  },
                  { replace: true }
                )
              }
              setLimit={(e) =>
                setSearchParams(
                  (prev) => {
                    prev.set('limit', e);
                    return prev;
                  },
                  { replace: true }
                )
              }
            />
          </>
        )}
      </div>
    </>
  );
};

export default ManagePlanner;
