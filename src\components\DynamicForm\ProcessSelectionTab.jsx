import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';
import useFieldsAndTitle from '../../hooks/useFieldsAndTitle';
import TruncateString from '../global/TruncateString';

const RowData = ({
  item,
  multiSelectData,
  inputData,
  handleMultiSelectChange,
}) => {
  const [expandParams, setExpandParams] = useState(false);

  const [horizontalFields, verticalFields, title] = useFieldsAndTitle(item);

  const isTable =
    horizontalFields?.length > 1 &&
    verticalFields?.length > 1 &&
    title?.length > 0;

  useEffect(() => {
    if (multiSelectData?.length > 0) {
      multiSelectData
        ?.filter((itm) => itm?.category !== 'Outsource')
        ?.forEach((process) => {
          handleMultiSelectChange(process, item.Parameter, '', true);
        });
    }
  }, [multiSelectData]); // eslint-disable-line

  return (
    <>
      <tr className="w-full text-center text-base">
        <td
          className={`py-1 text-start flex justify-between items-center ${
            isTable ? 'hover:cursor-pointer' : ''
          }`}
          onClick={() => {
            if (isTable) {
              setExpandParams((prev) => !prev);
            }
          }}
        >
          <TruncateString length={18}>{item.Parameter}</TruncateString>
          {isTable && expandParams ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : isTable ? (
            <ChevronDownIcon className="h-4 w-4" />
          ) : null}
        </td>
        {multiSelectData?.map((process, pIdx) => {
          const isOutSource = process?.category === 'Outsource';

          if (isOutSource) return null;

          return (
            <td className="py-1" key={pIdx}>
              <input
                type="checkbox"
                onChange={() =>
                  handleMultiSelectChange(process, item.Parameter)
                }
                checked={
                  inputData?.[item.Parameter]?.selectOptions?.find(
                    (option) => option._id === process._id
                  ) || false
                }
              />
            </td>
          );
        })}
      </tr>
      {isTable && expandParams ? (
        <>
          {title?.map((newTitle, idx) => (
            <tr key={idx} className="w-full text-center text-base">
              <td className="py-1 text-start pl-3">
                <TruncateString length={14}>{newTitle}</TruncateString>
              </td>
              {multiSelectData?.map((process, idx) => {
                const isOutSource = process?.category === 'Outsource';

                if (isOutSource) return null;

                return (
                  <td className="py-1" key={idx}>
                    <input
                      type="checkbox"
                      onChange={() =>
                        handleMultiSelectChange(
                          process,
                          item.Parameter,
                          newTitle
                        )
                      }
                      checked={
                        inputData?.[item.Parameter]?.visibility?.[
                          process._id
                        ]?.find((i) => i === newTitle) || false
                      }
                      disabled={
                        !inputData?.[item.Parameter]?.selectOptions.find(
                          (option) => option._id === process._id
                        )
                      }
                    />
                  </td>
                );
              })}
            </tr>
          ))}
        </>
      ) : null}
    </>
  );
};

const ProcessSelectionTab = ({ data, state }) => {
  const { inputData, renderState, multiSelectData, handleMultiSelectChange } =
    state;

  return (
    <div className="w-full overflow-x-scroll">
      {renderState ? (
        <table className="w-full">
          <thead className="w-full">
            <tr className="w-full text-lg">
              <th className="w-[180px] min-w-[160px] py-2">Process</th>
              {multiSelectData?.map((process, key) => {
                const isOutSource = process?.category === 'Outsource';

                if (isOutSource) return null;

                return (
                  <th className="min-w-[80px] py-2" key={key}>
                    {process.process}
                  </th>
                );
              })}
            </tr>
          </thead>
          <tbody className="w-full">
            {data.map((item, iIdx) => (
              <RowData
                key={iIdx}
                item={item}
                multiSelectData={multiSelectData}
                inputData={inputData}
                handleMultiSelectChange={handleMultiSelectChange}
              />
            ))}
          </tbody>
        </table>
      ) : null}
    </div>
  );
};
export default ProcessSelectionTab;
