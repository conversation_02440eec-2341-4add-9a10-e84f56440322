import Table from '../../global/components/Table';

export default function RMTable({ rmTableData }) {
  const data = rmTableData;

  return (
    <div className="m-4 ">
      <Table>
        <Table.Head>
          <Table.Row>
            <Table.Th>#</Table.Th>
            <Table.Th>NAME</Table.Th>
            <Table.Th>QUANTITY</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {data.length < 1 ? (
            <Table.Row>
              <Table.Td className="font-normal">No Data</Table.Td>
            </Table.Row>
          ) : (
            data.map((el, index) => (
              <Table.Row key={index} className="font-normal">
                <Table.Td>{index + 1}</Table.Td>
                <Table.Td>{el.name}</Table.Td>
                <Table.Td>{Number(el.qty)}</Table.Td>
              </Table.Row>
            ))
          )}
        </Table.Body>
      </Table>
    </div>
  );
}
