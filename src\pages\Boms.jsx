import { useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import AssemblyBomTable from '../components/v3/BOM/AssemblyBomTable';
import { mobileWidth } from '../helperFunction';
import Header from '../components/global/components/Header';

export default function Boms() {
  const isMobile = useMediaQuery({ query: mobileWidth });
  const [selectedBom, setSelectedBom] = useState({});

  return (
    <div>
      <Header
        className="mb-5"
        title="Bill of Materials"
        description=""
        infoTitle="Welcome to the Bill of Materials Page(BOM)"
        infoDesc="Your hub for generating new bill of materials with ease."
        paras={[
          'Here, you can fill in Bill of Materials details and attach necessary documents. Once a BOM is created on this page, you have the flexibility to associate new work orders with the created BOM, streamlining your workflow management process. Simplify your work order creation and job association with our user-friendly Create Work Order Page.',
          'Effortlessly create and manage bill of materials, ensuring efficient project planning and resource allocation. Streamline your workflow management process and simplify BOM creation with our user-friendly BOM Page.',
        ]}
      />
      <AssemblyBomTable
        setSelectedBom={setSelectedBom}
        selectedBom={selectedBom}
        isMobile={isMobile}
      />
    </div>
  );
}
