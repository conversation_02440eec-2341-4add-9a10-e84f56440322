import { Fragment } from 'react';
import { useState } from 'react';
import { getLocalDateTime } from '../../../../helperFunction';

const Pointer = ({ action, showError }) => {
  const [hovered, setHovered] = useState(false);

  const act = action?.operator?.action;

  const bgColor =
    act === 'pause'
      ? 'bg-green-primary'
      : act === 'stop'
      ? 'bg-red-primary'
      : act === 'start'
      ? 'bg-blue-primary'
      : 'bg-cyan-500';

  return (
    <>
      {action?.left && (
        <>
          {hovered && (
            <div
              className={`absolute min-w-[250px] rounded-xl shadow-basic text-white -translate-x-1/2 bottom-[120%] ${bgColor}`}
              style={{ left: action.left || 0 }}
            >
              <h4 className="px-5 py-1 border-b text-center capitalize">
                {action.operator.action}
              </h4>

              <div className="grid grid-cols-3 text-sm px-5 py-2 gap-y-1 gap-x-2">
                <span>Time:</span>
                <span className="col-span-2">
                  {getLocalDateTime(action.time)}
                </span>
                <span>Operator:</span>
                <span className="col-span-2">{`${action.operator.user.name}(${action.operator.user.employeeId})`}</span>
                {act === 'pause' && showError && (
                  <>
                    <span>Error:</span>
                    <span className="col-span-2">
                      {action.errorMessage.error.message}
                    </span>
                  </>
                )}
                {act === 'stop' && showError && (
                  <>
                    <span>Errors:</span>
                    <span className="col-span-2 w-full grid grid-cols-3 gap-x-3">
                      {action.errorMessages.length === 0 && 'None'}
                      {action.errorMessages.map((em, eIdx) => (
                        <Fragment key={eIdx}>
                          <span>{em.error.parameter}</span>
                          <span className="col-span-2">{em.error.message}</span>
                        </Fragment>
                      ))}
                    </span>
                  </>
                )}
              </div>
            </div>
          )}
          <span
            className={`absolute w-[2px] h-full hover:shadow-basic -translate-x-1/2 hover:cursor-pointer hover:z-[100] ${bgColor}`}
            style={{ left: action.left || 0 }}
            onMouseOver={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
          >
            {/* <ChevronDownIcon className="absolute h-3 w-3 -translate-x-1/2 -top-3 rounded-full hover:cursor-pointer hover:bg-red-300" /> */}
          </span>
        </>
      )}
    </>
  );
};

export default Pointer;
