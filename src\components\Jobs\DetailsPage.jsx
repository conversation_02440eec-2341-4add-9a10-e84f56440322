import Details from './OrderDetails/Details';
import ProcessGoalsTables from './OrderDetails/ProcessGoalsTables';

const DetailsPage = ({
  selectedtemplateId = '',
  selectProductionFlows = '',
  isTemplateStart = false,
  productionFlow,
  goalsData,
  setGoalsData,
  multiProcessTablesData,
  setMultiProcessTablesData,
  LinkFieldOptions,
  linkFieldData,
  setLinkFieldData,
  goalsTables,
  setGoalsTables,
  projectStatus,
  isEdit,
  jobPlanningDetails,
  setJobPlanningDetails,
  createTemplate,
  setTemplateDetails,
  templateDetails,
  selectedPo,
  selectedPoData,
  SelectedBOM,
  setSelectedBOM,
  bomAndAssemblyItem,
  openMultiplierModal,
  setOpenMultiplierModal,
  setBatchIdData,
  additionalIdData,
  assemblyItem,
}) => {
  return (
    <>
      <Details
        selectedtemplateId={selectedtemplateId}
        isTemplateStart={isTemplateStart}
        selectProductionFlows={selectProductionFlows}
        goalsData={goalsData}
        setGoalsData={setGoalsData}
        multiProcessTablesData={multiProcessTablesData}
        setMultiProcessTablesData={setMultiProcessTablesData}
        LinkFieldOptions={LinkFieldOptions}
        linkFieldData={linkFieldData}
        setLinkFieldData={setLinkFieldData}
        productionFlow={productionFlow}
        goalsTables={goalsTables}
        setGoalsTables={setGoalsTables}
        projectStatus={projectStatus}
        isEdit={isEdit}
        jobPlanningDetails={jobPlanningDetails}
        setJobPlanningDetails={setJobPlanningDetails}
        selectedPo={selectedPo}
        selectedPoData={selectedPoData}
        SelectedBOM={SelectedBOM}
        setSelectedBOM={setSelectedBOM}
        bomAndAssemblyItem={bomAndAssemblyItem}
        openMultiplierModal={openMultiplierModal}
        setOpenMultiplierModal={setOpenMultiplierModal}
        assemblyItem={assemblyItem}
      />

      {!isTemplateStart && (
        <ProcessGoalsTables
          isTemplateStart={isTemplateStart}
          processes={productionFlow?.processes || []}
          goalsTables={goalsTables}
          setGoalsTables={setGoalsTables}
          detailsStep={1}
          isEdit={isEdit}
          createTemplate={createTemplate}
          setTemplateDetails={setTemplateDetails}
          templateDetails={templateDetails}
          setBatchIdData={setBatchIdData}
          additionalIdData={additionalIdData}
        />
      )}
    </>
  );
};

export default DetailsPage;
