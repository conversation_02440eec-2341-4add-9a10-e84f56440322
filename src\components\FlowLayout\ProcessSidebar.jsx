import RightSidebar from '../global/components/RightSidebar';
import { TabButton, TabContainer } from '../global/components/TabContainer';
import Table from '../global/components/Table';
import AdditionalFieldPreview from './AdditionalFieldPreview';

const ProcessSidebar = ({
  ShowErrorSidebar,
  setShowErrorSidebar,
  ErrorMessage,
  selectedPo,
  allPos,
  selectedOptionsData,
  addtionalFields,
  SetActiveTab,
  Tabs,
  toCapitalize,
  FormatDate,
}) => {
  return (
    <RightSidebar
      openSideBar={ShowErrorSidebar}
      setOpenSideBar={(val) => {
        setShowErrorSidebar(val);
      }}
      scale={736}
    >
      <>
        <section>
          <div className="heading">
            <h1>Process: {ErrorMessage?.mqtt?.process}</h1>
            <p>
              Work Order:{' '}
              {
                allPos?.find((each) => {
                  return each?.value === selectedPo;
                })?.name
              }{' '}
            </p>
            <p>Job : {selectedOptionsData?.id}</p>
          </div>
          <div className="tabs">
            <TabContainer className="!mb-3 !mt-3">
              {Object.keys(Tabs).map((tabdata) => {
                return (
                  <TabButton
                    key={Tabs[tabdata]['tab']}
                    onClick={() => {
                      SetActiveTab(Tabs[tabdata].tab);
                    }}
                    isactive={Tabs[tabdata]['active']}
                  >
                    {toCapitalize(Tabs[tabdata].tab)}
                  </TabButton>
                );
              })}
            </TabContainer>
          </div>
          {Tabs['error']['active'] && (
            <div className="errortable mt-3">
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>Error Message</Table.Th>
                    <Table.Th>Time</Table.Th>
                    <Table.Th>Operator</Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {ErrorMessage?.errorMessages?.map((each) => {
                    return (
                      <Table.Row key={each?._id}>
                        <Table.Td>{each?.error?.message}</Table.Td>
                        <Table.Td>{FormatDate(each?.time)}</Table.Td>
                        <Table.Td>{each?.operator?.name}</Table.Td>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </div>
          )}
          {Tabs['machine']['active'] && (
            <div className="machine-tab-container">
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>Machine Id</Table.Th>
                    <Table.Th>Machine Name</Table.Th>
                    <Table.Th>Status</Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {ErrorMessage?.machineAndOperator?.map((machineData) => {
                    return (
                      <Table.Row key={machineData?._id}>
                        <Table.Td>{machineData?.machine?.machineId}</Table.Td>
                        <Table.Td>{machineData?.machine?.machineName}</Table.Td>
                        <Table.Td>{machineData?.machine?.status}</Table.Td>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </div>
          )}

          {Tabs['addtionalFields']['active'] && (
            <>
              <AdditionalFieldPreview data={addtionalFields} />
            </>
          )}
        </section>
      </>
    </RightSidebar>
  );
};

export default ProcessSidebar;
