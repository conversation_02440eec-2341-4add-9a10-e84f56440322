import { useContext, useState } from 'react';
import { MdDone } from 'react-icons/md';
import {
  useAuthorizeAdminMutation,
  useEditUserMutation,
} from '../../slices/userApiSlice';
import { Store } from '../../store/Store';
import { InfoTooltip } from '../global/components/InfoTooltip';
import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import NewInput from '../global/components/NewInput';

const KanbanFilter = ({ visibleTiles, setVisibleTiles, columns }) => {
  const { state, dispatch, defaults } = useContext(Store);
  const user = state?.user;
  const [editUser] = useEditUserMutation();
  const [authorizeAdmin] = useAuthorizeAdminMutation();
  const [showMultiselect, setShowMultiselect] = useState(false);
  const [filterSubmit, setFilterSubmit] = useState(false);
  const [showAdminPopup, setShowAdminPopup] = useState(false);
  const [adminDetails, setAdminDetails] = useState({ email: '', password: '' });

  const changeVisibleColumns = (e) => {
    const { value } = e.target;
    setVisibleTiles(value.map((item) => item.value));
  };

  const submitAdminForm = async (e) => {
    e.preventDefault();
    const user = await authorizeAdmin({ data: adminDetails });
    if (user?.data) {
      setShowMultiselect((prev) => !prev);
      setFilterSubmit(true);
    }
    setShowAdminPopup(false);
    setAdminDetails({ email: '', password: '' });
  };

  return (
    <>
      {showAdminPopup && (
        <Modal onSubmit={submitAdminForm} onCloseModal={setShowAdminPopup}>
          {() => (
            <form className="flex flex-col gap-4 w-full max-w-xs p-4 text-sm">
              <h2 className="text-lg font-semibold text-gray-800">
                Admin Login
              </h2>
              <NewInput
                type="email"
                placeholder="Email"
                onChange={(e) =>
                  setAdminDetails((prev) => ({
                    ...prev,
                    email: e.target.value,
                  }))
                }
              />
              <NewInput
                type="password"
                placeholder="Password"
                onChange={(e) =>
                  setAdminDetails((prev) => ({
                    ...prev,
                    password: e.target.value,
                  }))
                }
              />
            </form>
          )}
        </Modal>
      )}

      {showMultiselect && (
        <MultiSelect
          options={columns?.map((item) => {
            const colName =
              defaults?.defaultParam?.departmentFlow?.nodeStructure?.nodes?.find(
                (node) => node?.data?.selectedPage?.[0]?.label === item.label
              )?.data?.name;
            return {
              value: item.label,
              label: colName || item.label,
            };
          })}
          onChange={changeVisibleColumns}
          value={visibleTiles}
        />
      )}

      {!filterSubmit && (
        <section className="flex items-center gap-1">
          <button
            className={`px-3 py-1.5 text-sm font-medium rounded-lg border transition-all duration-200 flex items-center gap-1 ${
              showMultiselect ? 'bg-gray-100' : 'bg-white'
            }`}
            onClick={() => {
              if (user?.role === 'superuser' || user?.role === 'admin') {
                setShowMultiselect((prev) => !prev);
                setFilterSubmit(true);
              } else {
                setShowAdminPopup(true);
              }
            }}
          >
            Customize
            <InfoTooltip width="300px" position="right" id="toggle">
              Use this button to open filtering options for visible columns.
            </InfoTooltip>
          </button>
        </section>
      )}

      {filterSubmit && (
        <section className="flex items-center gap-1">
          <button
            className="w-6 h-6 flex items-center justify-center rounded border border-green-400 text-green-600 hover:bg-green-100 transition"
            onClick={async () => {
              setFilterSubmit(false);
              setShowMultiselect(false);
              const kanbanFilter = visibleTiles?.map(
                (obj) => obj?.value || obj
              );
              let res = await editUser({
                id: state?.user?._id,
                data: { kanbanFilter },
              });
              if (res) {
                let userTemp = JSON.parse(localStorage.getItem('user'));
                userTemp = {
                  ...userTemp,
                  user: {
                    ...userTemp?.user,
                    kanbanFilter: res?.data?.kanbanFilter,
                  },
                };
                localStorage.setItem('user', JSON.stringify(userTemp));
                dispatch({
                  type: 'SET_USER',
                  payload: {
                    user: userTemp?.user,
                  },
                });
              }
            }}
          >
            <MdDone className="text-base" />
          </button>
        </section>
      )}
    </>
  );
};

export default KanbanFilter;
