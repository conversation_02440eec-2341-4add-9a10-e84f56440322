import { Modal } from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import cross from '../../assets/images/cross.png';
import { ReactComponent as JpgPng } from '../../assets/svgs/pdfsvg.svg';
import {
  useAddCustomColumnMutation,
  useEditCustomColumnMutation,
  useGetCustomColumnsQuery,
} from '../../slices/customCoulmn.ApiSlice';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Table from '../global/components/Table';
import UploadButton from '../UploadButton';
import EditButton from '../v3/global/components/EditButton';
import MediaModal from '../v3/global/components/MediaModal';
function AddInvoiceModal({
  setShowInvoiceModal,
  setInvoiceData,
  additionalFields,
  setAdditionalFields,
  showInvoiceModal,
}) {
  const [invoiceRowData, setInvoiceRowData] = useState({});
  const [allInvoices, setAllInvoices] = useState([]);
  const [pdf, setPdf] = useState([]);
  const [editMode, setEditMode] = useState({});
  const [addCustomColumns] = useAddCustomColumnMutation();
  const [editColumns] = useEditCustomColumnMutation();
  const { data: allCustomColumns } = useGetCustomColumnsQuery();
  const [allCustomCols, setAllCustomCols] = useState([]);
  const [ShowEditMediaModal, setShowEditMediaModal] = useState(false);

  const handleInputChange = (e) => {
    const name = e.target.name;
    const value = e.target.value;
    setInvoiceRowData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const removePdf = (name) => {
    const filtered = pdf.filter(
      (itemm) => (itemm.fname ?? itemm.name) !== name
    );
    setPdf(filtered);
  };
  const handleSubmit = async () => {
    setInvoiceData((prev) => [...prev, ...allInvoices]);
    setShowInvoiceModal(false);
    const withoutIds = allCustomCols?.filter((col) => col._id === undefined);
    const withIds = allCustomCols?.filter((col) => col._id !== undefined);
    const data = withoutIds?.map((col) => col?.columnName);
    const res = await addCustomColumns({
      columnInputs: data,
      pageName: 'grn/invoice',
    });
    const res2 = await editColumns(withIds);
    if (res?.data || res2?.data) {
      toast.success('Columns Added SuccesFully');
    }
    toast.success('Invoice Details Added SuccsesFully');
  };
  const handleAdd = () => {
    if (Object.keys(invoiceRowData).length > 0) {
      const data = {
        ...invoiceRowData,
        files: pdf,
        additionalFields,
      };
      setAllInvoices((prev) => [...prev, data]);
      setInvoiceRowData({});
      setPdf([]);
    } else {
      toast.info('Please Fill All Details');
    }
  };

  useEffect(() => {
    const temp = allCustomColumns?.filter(
      (col) => col?.pageName === 'grn/invoice'
    );
    setAllCustomCols(temp);
  }, [allCustomColumns]);

  useEffect(() => {
    const temp = allCustomCols?.map((col) => ({
      label: col?.columnName,
      data: '',
    }));
    setAdditionalFields(temp);
  }, [allCustomCols]); //eslint-disable-line

  const deleteRow = (idx) => {
    const updatedInvoice = allInvoices?.filter((_, ind) => ind != idx);
    setAllInvoices(updatedInvoice);
  };

  const changeHandler = (e) => {
    for (let i in e) {
      if (i === 'length') return;

      let fileName = e[i].name;
      let fileType = e[i].type;

      const fr = new FileReader();
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          name: fileName,
          type: fileType,
          data: url,
        };
        const res = pdf?.find((data1) => data1?.name === data?.name);
        if (res) {
          toast.error('Cannot add media again with same name');
        } else {
          setPdf((prev) => [...prev, data]);
        }
      });
    }
  };
  const deleteColumn = (idx) => {
    const updatedField = allCustomCols?.filter((_, ind) => idx !== ind);
    setAdditionalFields(updatedField);
    setAllCustomCols(updatedField);
    const updatedInvoices = allInvoices.map((invoice) => ({
      ...invoice,
      additionalFields: invoice.additionalFields.filter(
        (_, ind) => idx !== ind
      ),
    }));
    setAllInvoices(updatedInvoices);
  };
  const handleCustomColumnChange = (ind, e) => {
    const value = e.target.value;
    setAdditionalFields((prevFields) =>
      prevFields.map((field, index) =>
        index === ind ? { ...field, data: value } : field
      )
    );
  };
  return (
    <Modal
      title={'Add Invoices'}
      open={showInvoiceModal}
      onCancel={() => {
        setShowInvoiceModal(false);
      }}
      centered
      width={1000}
      footer={
        <div className="flex justify-between space-x-2">
          <Button onClick={handleAdd}>Add</Button>
          <Button onClick={handleSubmit}>Submit</Button>
        </div>
      }
    >
      <>
        {ShowEditMediaModal && (
          <MediaModal
            ShowModal={ShowEditMediaModal}
            FormData={pdf}
            setFormData={setPdf}
            keyName={null}
            setShowModal={setShowEditMediaModal}
          />
        )}
        <section>
          <div className="input-section grid grid-cols-1 lg:text-[13px] lg:grid-cols-3 gap-4">
            <div className="input">
              <label>Enter Invoice Id</label>
              <Input
                name={`invoiceId`}
                value={invoiceRowData?.invoiceId || ''}
                onChange={handleInputChange}
                placeholder="Enter Invoice Id"
              />
            </div>
            <div className="input">
              <label>Enter Comment</label>
              <Input
                name={`invoiceComment`}
                value={invoiceRowData?.invoiceComment || ''}
                onChange={handleInputChange}
                placeholder="Enter Comment"
              />
            </div>
            <div className="input">
              <label>Enter Invoice Amount</label>
              <Input
                name={`invoiceAmount`}
                value={invoiceRowData?.invoiceAmount || ''}
                onChange={handleInputChange}
                placeholder="Enter Invoice Amount"
              />
            </div>
          </div>
          <div className="addButton flex gap-3 justify-between my-10">
            <p className="text-[16px] font-semibold">Custom Columns</p>
            <Button
              className={
                'w-22  text-[10px] !p-[5px] md:w-[8rem]  md:text-[14px] md:!p-[8px]'
              }
              onClick={() => {
                const inc = additionalFields?.length + 1;
                setAllCustomCols((prev) => [
                  ...prev,
                  {
                    columnName: `Column ${inc}`,
                  },
                ]);
              }}
            >
              + AddColumn
            </Button>
          </div>
          <div className="custom-input-section grid grid-cols-1 md:grid-cols-3 gap-4">
            {additionalFields?.map((field, idx) => (
              <div key={idx} className="input">
                <div className="labels flex gap-3 ">
                  {editMode[idx] ? (
                    <input
                      className=""
                      onBlur={() =>
                        setEditMode((prev) => ({
                          ...prev,
                          [idx]: false,
                        }))
                      }
                      onChange={(e) => {
                        setAllCustomCols((prevFields) =>
                          prevFields.map((f, index) =>
                            index === idx
                              ? { ...f, columnName: e.target.value }
                              : f
                          )
                        );
                      }}
                      value={field?.label}
                      type="text"
                    />
                  ) : (
                    <label
                      className="text-[14px]"
                      onDoubleClick={() =>
                        setEditMode((prev) => ({
                          ...prev,
                          [idx]: true,
                        }))
                      }
                    >
                      {field?.label}
                    </label>
                  )}

                  <label
                    className="cursor-pointer"
                    onClick={() => deleteColumn(idx)}
                  >
                    X
                  </label>
                </div>
                <Input
                  value={field?.data || ''}
                  onChange={(e) => handleCustomColumnChange(idx, e)}
                  placeholder={`Enter ${field?.label}`}
                />
              </div>
            ))}
          </div>
          <div className="input my-5">
            <label>Attach Media</label>
            <UploadButton
              accept="application/pdf"
              fileType="JPG/PNG"
              svg={<JpgPng className="h-10" />}
              multiple
              onChange={changeHandler}
              className={`text-[#667085] mt-2 !h-5`}
            />
          </div>
          <div className="pdf-View">
            {pdf?.length > 0 && (
              <Table className={`mt-5  mb-5`}>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>Files</Table.Th>
                    <Table.Th>Description</Table.Th>
                    <Table.Th>
                      <EditButton
                        onClick={(e) => {
                          setShowEditMediaModal(true);
                          e.stopPropagation();
                        }}
                      />
                    </Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {pdf?.map((item, idx) => {
                    return (
                      <Table.Row key={idx}>
                        <Table.Td>{item?.name}</Table.Td>
                        {item?.description?.length > 20 ? (
                          <Table.Td>
                            <span className="break-words">
                              {item?.description?.slice(0, 20)}{' '}
                            </span>
                          </Table.Td>
                        ) : (
                          <Table.Td>{item.description}</Table.Td>
                        )}
                        <Table.Td
                          onClick={() => {
                            removePdf(item.name);
                          }}
                          className="cursor-pointer"
                        >
                          <img
                            src={cross}
                            alt="Cross Icon"
                            className="w-2 h-2"
                            // key={idx}
                          />
                        </Table.Td>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            )}
          </div>
          <div className="table-section">
            <h4 className="mt-8 mb-2">All Invoices</h4>
            <Table>
              <Table.Row>
                <Table.Th>Invoice Id</Table.Th>
                <Table.Th>Comment</Table.Th>
                <Table.Th>Invoice Amount</Table.Th>
                <Table.Th>Files</Table.Th>

                {/* {addHeadings?.map((head, idx) => (
                        <Table.Th key={idx}>{head}</Table.Th>
                      ))} */}
                {additionalFields?.map((field, idx) => (
                  <Table.Th key={idx}>{field.label}</Table.Th>
                ))}
                <Table.Th></Table.Th>
              </Table.Row>

              <Table.Body>
                {allInvoices?.map((invoice, idx) => {
                  return (
                    <Table.Row key={idx}>
                      <Table.Td>{invoice?.invoiceId}</Table.Td>
                      <Table.Td>{invoice?.invoiceComment}</Table.Td>
                      <Table.Td>{invoice?.invoiceAmount}</Table.Td>
                      <Table.Td className={`!text-blue-500 !font-semibold`}>
                        file-{invoice?.files?.length || '-'}
                      </Table.Td>
                      {/* We are mapping on ExtraHeading to make the Table Alignment Sync */}
                      {/* {addHeadings?.map((_, index) => (
                              <Table.Td key={index}>
                                {invoice?.additionalFields?.[index]?.data ||
                                  '-'}
                              </Table.Td>
                            ))} */}
                      {additionalFields.map((_, idx) => (
                        <Table.Td key={idx}>
                          {invoice.additionalFields[idx]?.data || '-'}
                        </Table.Td>
                      ))}
                      <Table.Td
                        className={`cursor-pointer`}
                        onClick={() => deleteRow(idx)}
                      >
                        X
                      </Table.Td>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
          </div>
        </section>
      </>
    </Modal>
  );
}

export default AddInvoiceModal;
