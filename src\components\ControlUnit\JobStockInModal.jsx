import Modal from '../global/components/Modal';
import CreateGRN from '../../pages/CreateGRN';

const JobStockInModal = ({
  setShowForceStop,
  // ci,
  // selectedData,
  // selectedWo,
  productFromCu,
  method,
  itemStockOutData,
  // setOperatorPopup,
  jobStopFxnToCallAfterStockIn,
  updateCiForForceStop,
  reset,
}) => {
  return (
    <div>
      <Modal
        title="Stock In"
        description={'Stock In'}
        modalWidth="90%"
        onCloseModal={() => {
          // setOperatorPopup(false);
          setShowForceStop(false);
        }}
        isSubmitRequired={false}
        canSubmit={false}
        className=""
      >
        {() => {
          return (
            <>
              <section className="w-full">
                <CreateGRN
                  fromCu={true}
                  productFromCu={productFromCu}
                  method={method}
                  itemStockOutData={itemStockOutData}
                  setShowForceStop={setShowForceStop}
                  jobStopFxnToCallAfterStockIn={jobStopFxnToCallAfterStockIn}
                  reset={reset}
                  updateCiForForceStop={updateCiForForceStop}
                />
              </section>
            </>
          );
        }}
      </Modal>
    </div>
  );
};

export default JobStockInModal;
