import { useEffect, useState } from 'react';
import { getTimeDifference } from '../../helperFunction';
import { useLazyGetAllMachineDataQuery } from '../../slices/createAnalyticsApiSlice';
import { useLazyGetCreateInputByIDQuery } from '../../slices/createInputApiSlice';

const Tile = ({ params, mid, pid }) => {
  const [macData, setMacData] = useState([]);
  const [error, setError] = useState([]);

  const [getCreateInputByID] = useLazyGetCreateInputByIDQuery();

  const [getAllMachineData] = useLazyGetAllMachineDataQuery();

  useEffect(() => {
    const fetch = async () => {
      try {
        const res = await getAllMachineData({ data: mid });
        setMacData(res.allMacData);
      } catch (error) {
        console.error('Error fetching data:', error); // eslint-disable-line
      }
    };

    if (mid) fetch();

    const fetchData = async () => {
      try {
        const allerror = await getCreateInputByID({ id: pid }, false).unwrap();
        setError(allerror.cuProjects);
      } catch (error) {
        console.error(error); // eslint-disable-line
      }
    };
    if (pid) fetchData();
  }, [pid, mid, getCreateInputByID, getAllMachineData]);

  const emsgs = [];
  const operators = [];
  const machine = [];
  const workerData = [];
  const machineUptimeData = [];
  const machineDowntimeData = [];

  error.forEach((itm) => {
    itm.machineAndOperator.forEach((item) => {
      machine.push(item.machine.machineId);
      item.operator.forEach((i) => {
        operators.push(i.user.RFId);
      });
    });
    itm.errorMessages.forEach((item) => {
      emsgs.push(item.error.message);
    });
  });

  let uniqueOperator = operators.filter(
    (item, i, ar) => ar.indexOf(item) === i
  );
  let uniqueMachine = machine.filter((item, i, ar) => ar.indexOf(item) === i);

  //Here unique machine and operator is stored in above which then used to filter the data

  uniqueOperator.forEach((item) => {
    let workerArray = [];
    error.forEach((itm) => {
      itm.machineAndOperator.forEach((itr) => {
        itr.operator.forEach((i) => {
          if (i.user.RFId === item) {
            const res = {
              action: i.action,
              time: i.time,
            };
            workerArray.push(res);
          }
        });
      });
    });
    //================================================================================================================================
    //Now we calculate the working hour for different workers in uniqueOperator--->>>

    let netActiveTime = 0;
    let isPaused = false;
    let lastEventTime = null;

    workerArray.forEach(function (event) {
      let eventType = event.action;

      if (eventType === 'start' || eventType === 'resume') {
        let eventTime = new Date(event.time).getTime(); // Convert to milliseconds
        if (lastEventTime !== null && isPaused) {
          netActiveTime += eventTime - lastEventTime;
        }
        isPaused = false;
        lastEventTime = eventTime;
      } else if (eventType === 'pause') {
        isPaused = true;
        lastEventTime = new Date(event.time).getTime(); // Convert to milliseconds
      } else if (eventType === 'stop') {
        let eventTime = new Date(event.time).getTime(); // Convert to milliseconds
        if (lastEventTime !== null && !isPaused) {
          netActiveTime += eventTime - lastEventTime;
        }
        lastEventTime = null;
      }
    });

    let netActiveHours = netActiveTime / (1000 * 60); // Convert milliseconds to hours
    const ans = {
      workerID: item,
      uptime: netActiveHours || 0,
    };

    workerData.push(ans);
  });

  //Now we calculate the uptime  for different machine in uniqueMachine--->>>

  uniqueMachine.forEach((machine) => {
    let tempData = [];
    error.forEach((cuPro) =>
      cuPro.machineAndOperator.forEach((mao) => {
        if (mao.machine.machineId === machine) {
          tempData.push({
            mao,
            errorMessages: cuPro?.errorMessages?.filter(
              (em) => em.machineAndOperatorId === mao._id
            ),
          });
        }
      })
    );
    let upTimeArray = [];
    let plannedDTArray = [];
    let unPlannedDTArray = [];

    tempData.forEach((data) => {
      const startTime = data.mao.startTime;
      const stopTime = data.mao.stopTime;
      const pauseTime = data.mao.pauseTime;
      const resumeTime = data.mao.resumeTime;
      const firstPauseTime = pauseTime[0];
      const lastStopTime = resumeTime[resumeTime?.length - 1];
      const pauseErrs = data.errorMessages.filter(
        (em) =>
          em.error.action === 'pause' &&
          em.machineAndOperatorId === data.mao._id
      );

      if (pauseTime?.length > 0) {
        // downtime
        // if machines is paused
        pauseTime.forEach((time, tIdx) => {
          const val = getTimeDifference(time, resumeTime[tIdx]);
          const timer = pauseErrs?.[tIdx]?.error?.timer;
          if (timer) {
            // if paused with timer check if time excced or not
            if (val > +timer) {
              // if val exceeds timer push val to unPlannedDT array
              plannedDTArray.push(0);
              unPlannedDTArray.push(val);
            } else {
              // push val to plannedDT array
              plannedDTArray.push(val);
              unPlannedDTArray.push(0);
            }
          } else {
            // push val to unplannedDT array
            unPlannedDTArray.push(val);
            plannedDTArray.push(0);
          }
        });

        // upTime
        const firstVal = getTimeDifference(startTime, firstPauseTime);

        let middleVals = [];
        pauseTime?.forEach((time, tIdx) => {
          if (tIdx === 0) return;
          middleVals.push(getTimeDifference(resumeTime[tIdx - 1], time));
        });

        const lastVal = getTimeDifference(lastStopTime, stopTime);

        upTimeArray.push(...[firstVal, ...middleVals, lastVal]);
      } else {
        // set both DT array to 0
        plannedDTArray.push(0);
        unPlannedDTArray.push(0);
        // set upTime array to diff between start and stop time
        const val = getTimeDifference(startTime, stopTime);
        upTimeArray.push(val);
      }
    });

    // calcuate values for temp generated data
    const upTime = Math.round(
      upTimeArray.reduce((acc, curVal) => acc + curVal, 0)
    );

    const plannedDT = Math.round(
      plannedDTArray.reduce((acc, curVal) => acc + curVal, 0)
    );

    const unPlannedDT = Math.round(
      unPlannedDTArray.reduce((acc, curVal) => acc + curVal, 0)
    );

    const ans1 = {
      macID: machine,
      uptime: upTime || 0,
    };

    machineUptimeData.push(ans1);

    const ans2 = {
      macID: machine,
      downtime: plannedDT + unPlannedDT || 0,
    };

    machineDowntimeData.push(ans2);
  });

  let frequency = emsgs.reduce(function (freq, str) {
    freq[str] = (freq[str] || 0) + 1;
    return freq;
  }, {});

  let uniqueStrings = Object.keys(frequency).map(function (key) {
    let obj = {};
    obj[key] = frequency[key];
    return obj;
  });

  const filteredError = uniqueStrings
    ?.filter((item) => item !== null)
    ?.sort((a, b) => b - a);

  const filteredWorker = workerData
    ?.filter((item) => item !== null)
    ?.sort((a, b) => b.uptime - a.uptime);

  const filteredUptime = machineUptimeData
    ?.filter((item) => item !== null)
    ?.sort((a, b) => b.uptime - a.uptime);

  const filteredDowntime = machineDowntimeData
    ?.filter((item) => item !== null)
    ?.sort((a, b) => b.downtime - a.downtime);

  const renderCard = (params) => {
    switch (params) {
      case 'Machine Count':
        return (
          <div className="max-w-lg">
            {macData.map((item, index) => (
              <div
                key={index}
                className="bg-white rounded-lg justify-center mt-[18%] p-4 flex mb-4"
              >
                <h2 className="text-2xl font-semibold ">
                  {item.machine.machineName}
                </h2>
                <p className="text-gray-600 ml-4  text-2xl">
                  {item.data.COUNT || 0}
                </p>
              </div>
            ))}
          </div>
        );

      case 'Error Count':
        return (
          <div className="max-w-lg">
            <div className="bg-white rounded-lg p-4 justify-center mt-[18%] flex mb-4">
              <h2 className="text-2xl font-semibold ">Top Error Count :</h2>
              <p className="text-gray-600 ml-4  text-2xl">
                {Object.values(filteredError?.[0] || {})?.[0]}
              </p>
            </div>
          </div>
        );

      case 'Worker Efficiency':
        return (
          <div className="max-w-lg">
            <div className="bg-white rounded-lg p-4 flex justify-center mt-[18%] mb-4">
              <h2 className="text-2xl font-semibold ">Top Worker Uptime :</h2>
              <p className="text-gray-600 ml-4  text-2xl">
                {filteredWorker?.[0]?.uptime}
              </p>
            </div>
          </div>
        );

      case 'Machine Uptime':
        return (
          <div className="max-w-lg">
            <div className="bg-white rounded-lg p-4 flex  justify-center mt-[18%] mb-4">
              <h2 className="text-2xl font-semibold ">Top Machine Uptime :</h2>
              <p className="text-gray-600 ml-4  text-2xl">
                {filteredUptime?.[0]?.uptime}
              </p>
            </div>
          </div>
        );

      case 'Machine Downtime':
        return (
          <div className="max-w-lg">
            <div className="bg-white rounded-lg p-4 flex  justify-center mt-[18%] mb-4">
              <h2 className="text-2xl font-semibold ">
                Top Machine Downtime :
              </h2>
              <p className="text-gray-600 ml-4  text-2xl">
                {filteredDowntime?.[0]?.downtime}
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return <div>{renderCard(params)}</div>;
};

export default Tile;
