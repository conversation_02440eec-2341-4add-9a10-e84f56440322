import KanbanTileV2 from './KanbanTileV2';

function TileV2({
  columns,
  adminView = false,
  dep = [],
  setHistorySidebar,
  className,
}) {
  return (
    <div
      className={`w-full flex flex-col md:flex-row gap-3  h-full ${className}`}
    >
      {' '}
      <div className="w-full gap-y-3 flex flex-col justify-start">
        {columns?.map((column, idx) => {
          return (
            <KanbanTileV2
              key={column?.id}
              column={column}
              // refetch={refetch}
              adminView={adminView}
              dep={dep[idx]}
              setHistorySidebar={setHistorySidebar}
            />
          );
        })}
      </div>
      {/* <div className="border-l w-full md:w-[50%] min-h-full flex flex-col md:mt-[-43px]">
        <h1 className="text-center w-full">Notifications</h1>
        <div className="h-[calc(100vh-200px)] mx-2 mt-3 bg-white border justify-center rounded-md"></div>
      </div> */}
    </div>
  );
}

export default TileV2;
