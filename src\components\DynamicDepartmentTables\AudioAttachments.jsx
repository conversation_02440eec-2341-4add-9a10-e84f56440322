import { useEffect, useState } from 'react';
import { useReactMediaRecorder } from 'react-media-recorder';

import { Button } from 'antd';
import Modal from 'antd/es/modal/Modal';
import Input from '../global/components/Input';
import Table from '../global/components/Table';

import { useLazyGetMediaByIdQuery } from '../../slices/mediaSlice';

import { MdDeleteOutline } from 'react-icons/md';

const AudioAttachments = ({
  setMediaModal,
  mediaModal,
  changeHandler,
  rowData,
  column,
  setDeletedMedia,
  deletedMedia,
}) => {
  const [initialData, setInitialData] = useState(rowData); //eslint-disable-line
  const [attachments, setAttachments] = useState([]);
  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [currentIdx, setCurrentIdx] = useState(0);

  const {
    status,
    startRecording,
    stopRecording,
    pauseRecording, //eslint-disable-line
    mediaBlobUrl,
  } = useReactMediaRecorder({
    video: false,
    audio: true,
    echoCancellation: true,
    // onData: (data) => {
    //   setRecordedBlob(data);
    // },
  });

  const dataUrlToMediaBlobUrl = (dataUrl) => {
    // Remove the `data:text/html;base64,` prefix
    const base64String = dataUrl.split(',')[1];

    // Decode the base64 string
    const binaryString = atob(base64String);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);

    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // Create a new Blob with the audio MIME type
    const blob = new Blob([bytes.buffer], { type: 'audio/wav' }); // or 'audio/wav', 'audio/mpeg' based on original format
    // Generate and return a new object URL
    return URL.createObjectURL(blob);
  };

  const setMedia = async () => {
    let mediaArray = [];
    if (rowData?.[column?.name]?.value) {
      for (let i of rowData?.[column?.name]?.value) {
        if (i?.name === undefined) {
          const media = await getMediaById({ id: i });
          let obj = {};
          let mediaUrl = dataUrlToMediaBlobUrl(media?.data?.media?.data);
          obj = {
            ...media?.data?.media,
            data: mediaUrl,
          };
          mediaArray?.push(obj);
        }
      }
    }
    setAttachments(mediaArray);
  };

  useEffect(() => {
    if (initialData) {
      setAttachments([]);
      setMedia();
    }
  }, [initialData]); //eslint-disable-line

  const handleAdd = () => {
    setAttachments((prev) => [
      ...prev,
      {
        name: '',
        data: '',
        type: 'audio/wav',
      },
    ]);
  };

  const deleteAttachment = (idx) => {
    let deletedAttachments = deletedMedia?.[column?.name]?.value
      ? deletedMedia?.[column?.name]?.value
      : [];
    for (let i in attachments) {
      if (parseInt(i) === idx) {
        if (attachments?.[i]?._id) {
          deletedAttachments?.push(attachments?.[i]?._id);
        }
        break;
      }
    }
    setDeletedMedia((prev) => ({
      ...prev,
      [column?.name]: {
        type: 'audio',
        value: deletedAttachments,
      },
    }));
    setAttachments((prev) => prev?.filter((elem, index) => index !== idx));
  };

  const closeModal = () => {
    setMediaModal(false);
  };

  //   const blobToString = useCallback((blobUrl, index) => {
  //     fetch(blobUrl, { mode: 'cors' })
  //       .then((response) => {
  //         if (!response.ok) {
  //           throw new Error('Failed to fetch blob. Check CORS policies.');
  //         }
  //         return response.blob();
  //       })
  //       .then((blob) => {
  //         // Ensure the blob has the correct audio MIME type
  //         const audioMimeType = blob.type.startsWith('audio/')
  //           ? blob.type
  //           : 'audio/wav'; // default to 'audio/wav' if it's not an audio blob
  //         const audioBlob = new Blob([blob], { type: audioMimeType });

  //         const fr = new FileReader();
  //         fr.readAsDataURL(audioBlob);
  //         fr.onload = () => {
  //           const url = fr.result;
  //           console.log('URL', url); // Base64 string with correct audio MIME type
  //           handleChange(url, 'data', index, audioMimeType); // Pass the correct MIME type to the handler
  //         };
  //       })
  //       .catch((error) => {
  //         console.error('Error fetching or reading the Blob:', error);
  //       });
  //   }, []);

  // const blobToString = useCallback((blobUrl, index) => {
  //   fetch(blobUrl, { mode: 'cors' })
  //     .then((response) => {
  //       if (!response.ok) {
  //         throw new Error('Failed to fetch blob. Check CORS policies.');
  //       }
  //       return response.blob();
  //     })
  //     .then((blob) => {
  //       const fr = new FileReader();
  //       fr.readAsDataURL(blob);
  //       fr.onload = () => {
  //         const url = fr.result;
  //         handleChange(url, 'data', index, blob?.type);
  //       };
  //     })
  //     .catch((error) => {
  //       console.error('Error fetching or reading the Blob:', error); // eslint-disable-line
  //     });
  // }, []);

  const handleChange = (value, name, index) => {
    setAttachments((prev) => [
      ...prev?.slice(0, index),
      {
        ...prev?.[index],
        [name]: value,
      },
      ...prev?.slice(index + 1),
    ]);
  };

  const handleSubmit = () => {
    changeHandler(attachments);
    closeModal();
  };

  useEffect(() => {
    if (mediaBlobUrl !== undefined && currentIdx !== undefined) {
      handleChange(mediaBlobUrl, 'data', currentIdx);
    }
  }, [mediaBlobUrl, currentIdx]);

  return (
    <>
      <Modal
        title="Add Audio"
        width={900}
        centered
        open={mediaModal}
        onCancel={closeModal}
        footer={[
          <Button //eslint-disable-line
            type="primary"
            onClick={handleAdd}
            className="absolute left-[20px]"
          >
            Add
          </Button>,
          <Button key="back" onClick={closeModal}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            className="bg-[#14BA6D]"
            onClick={handleSubmit}
          >
            Submit
          </Button>,
        ]}
      >
        <>
          <Table>
            <Table.Head>
              <Table.Th>Name</Table.Th>
              <Table.Th>Audio</Table.Th>
              <Table.Th></Table.Th>
            </Table.Head>
            <Table.Body>
              {attachments?.map((elem, idx) => {
                return (
                  <Table.Row key={idx}>
                    <Table.Td>
                      <Input
                        value={attachments?.[idx]?.name}
                        onChange={(e) =>
                          handleChange(e.target.value, 'name', idx)
                        }
                        placeholder="Enter Audio file name"
                      />
                    </Table.Td>
                    <Table.Td>
                      <div className="flex items-center gap-2">
                        <audio
                          src={
                            attachments?.[idx]?.data
                              ? attachments?.[idx]?.data
                              : mediaBlobUrl
                          }
                          controls
                          autoPlay
                        />
                        <Button onClick={startRecording}>
                          {status === 'recording' ? 'Recording...' : 'Record'}
                        </Button>
                        <Button
                          onClick={async () => {
                            stopRecording();
                            setCurrentIdx(idx);
                          }}
                        >
                          Stop
                        </Button>
                      </div>
                      {/* <ReactMediaRecorder
                        audio
                        render={({
                          status,
                          startRecording,
                          stopRecording,
                          mediaBlobUrl,
                        }) => (
                          <div className="flex items-center gap-2">
                            {console.log('HAHA 3', attachments?.[idx]?.data)}
                            <audio
                              src={
                                attachments?.[idx]?.data
                                  ? attachments?.[idx]?.data
                                  : mediaBlobUrl
                              }
                              controls
                              autoPlay
                            />
                            <Button onClick={startRecording}>
                              {status === 'recording'
                                ? 'Recording...'
                                : 'Record'}
                            </Button>
                            <Button
                              onClick={async () => {
                                stopRecording();
                                handleChange(mediaBlobUrl, 'data', idx);
                              }}
                            >
                              Stop
                            </Button>
                          </div>
                        )}
                      /> */}
                    </Table.Td>
                    <Table.Td>
                      <MdDeleteOutline
                        onClick={() => {
                          deleteAttachment(idx);
                        }}
                      />
                    </Table.Td>
                  </Table.Row>
                );
              })}
            </Table.Body>
          </Table>
        </>
      </Modal>
    </>
  );
};

export default AudioAttachments;
