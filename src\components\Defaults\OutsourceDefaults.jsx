import { Switch } from 'antd';

const OutsourceDefaults = ({ defaults, setDefaults }) => {
  return (
    <div className="w-[100%] border-b-2 border-t-2 my-3 border-gray-400/70  ">
      <div className="grid grid-cols-1 md:grid-cols-2 w-full py-2 justify-center items-center">
        <h3 className="text-gray-subHeading">Outsource Defaults :</h3>
      </div>
      <div className=" grid grid-cols-2 w-full py-2 text-sm justify-center items-center gap-y-2 ">
        <div className="flex items-center gap-2">
          <p>Outsource Stockout : &nbsp;</p>
          <Switch
            defaultChecked={defaults?.outsourceDefaults?.outsourceStockout}
            value={defaults?.outsourceDefaults?.outsourceStockout}
            onChange={(checked) => {
              setDefaults((prev) => ({
                ...prev,

                outsourceDefaults: {
                  ...prev.outsourceDefaults,
                  outsourceStockout: checked,
                },
              }));
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default OutsourceDefaults;
