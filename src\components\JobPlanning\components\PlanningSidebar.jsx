import { Tab } from '@headlessui/react';
import { Fragment, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLazyGetAllCuProjectsQuery } from '../../../slices/CuProjectAPiSlice';
import { useLazyGetJobsBasedOnDateQuery } from '../../../slices/createInputApiSlice';
import RightSidebar from '../../global/components/RightSidebar';
import {
  filterJobsBasedOnDateTile,
  filterUnscheduledJobsBasedOnDate,
  getMachine,
  getTotalMachines,
  getTotalWorkers,
  getWorker,
} from '../utils/JobPlanner.functions';

const selectedStyle =
  'px-4 py-2 text-sm font-medium text-gray-900 bg-gray-100 outline-none w-full h-[3%]';
const nonSelectedStyle =
  'px-4 py-2 text-sm font-medium text-gray-900 bg-white outline-none w-full h-[3%]';

const days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

const PlanningSidebar = ({ openSideBar, setOpenSideBar, initJobs }) => {
  const navigate = useNavigate();
  const [jobs, setJobs] = useState({
    scheduled: [],
    unscheduled: [],
    date: new Date(),
  });
  const [selectedTab, setSelectedTab] = useState('Scheduled');
  const [dates, setDates] = useState([]);

  const [getJobsBasedOnDate] = useLazyGetJobsBasedOnDateQuery();
  const [getAllCuProjects] = useLazyGetAllCuProjectsQuery();

  useEffect(() => {
    if (openSideBar) {
      let tomorrow;
      if (initJobs?.date) {
        tomorrow = new Date(initJobs?.date);
      } else {
        tomorrow = new Date();
      }
      let temp = [];
      for (let i = 0; i < 7; i++) {
        temp[i] = new Date(tomorrow);
        tomorrow.setDate(tomorrow.getDate() + 1);
      }
      setDates(temp);
    }
    if (
      openSideBar &&
      (initJobs?.scheduled?.length <= 0 || !initJobs?.scheduled) &&
      (initJobs?.unscheduled?.length <= 0 || !initJobs?.unscheduled)
    ) {
      (async () => {
        let date = new Date();
        let scheduled = await getJobsBasedOnDate({
          month: date?.getMonth(),
          year: date?.getFullYear(),
        }).unwrap();
        let unscheduled = await getAllCuProjects().unwrap();
        if (scheduled) {
          scheduled = filterJobsBasedOnDateTile(
            date?.getDate(),
            date?.getMonth(),
            scheduled
          );
        }
        if (unscheduled) {
          unscheduled = filterUnscheduledJobsBasedOnDate(
            date?.getDate(),
            date?.getMonth(),
            unscheduled?.cuProjects,
            scheduled
          );
        }
        setJobs({
          scheduled: scheduled,
          unscheduled: unscheduled,
          date: new Date(),
        });
      })();
    } else {
      setJobs(initJobs);
    }
  }, [openSideBar, initJobs, getJobsBasedOnDate, getAllCuProjects]);

  const getJobs = async (date) => {
    let scheduled = await getJobsBasedOnDate({
      month: date?.getMonth(),
      year: date?.getFullYear(),
    });
    let unscheduled = await getAllCuProjects().unwrap();
    scheduled = filterJobsBasedOnDateTile(
      date?.getDate(),
      date?.getMonth(),
      scheduled
    );
    unscheduled = filterUnscheduledJobsBasedOnDate(
      date?.getDate(),
      date?.getMonth(),
      unscheduled.cuProjects,
      scheduled
    );
    setJobs({
      scheduled: scheduled.data || scheduled,
      unscheduled: unscheduled,
      date: date,
    });
  };

  return (
    <>
      <RightSidebar
        openSideBar={openSideBar}
        setOpenSideBar={setOpenSideBar}
        scale={980}
        className="text-sm"
      >
        <div className="mt-7">
          <p
            className="text-sky-500 underline ml-1 cursor-pointer"
            onClick={() => {
              navigate('/jobs/jobplanning');
              setOpenSideBar(false);
            }}
          >
            {`<-`} Expand calendar view
          </p>
          <div className="flex items-center w-full my-2">
            {dates?.map((el, index) => {
              return (
                <div
                  key={el}
                  className={`px-3 py-2 border-[1px] cursor-pointer ${
                    index === 0 && 'rounded-s'
                  } ${index === 6 && 'rounded-e'} ${
                    el.toString() === jobs?.date?.toString()
                      ? 'border-black'
                      : 'border-gray-100'
                  } w-full text-center`}
                  onClick={() => getJobs(el)}
                >
                  <p>{days[el.getDay()]}</p>
                  <p>{el.getDate()}</p>
                </div>
              );
            })}
          </div>
          <h3 className="mb-2">
            {jobs?.date
              ? new Date(jobs?.date)?.toDateString()
              : new Date().toDateString()}
          </h3>
          <div className="flex justify-between">
            <p>
              Total Machines:{' '}
              <span>
                {getTotalMachines(jobs?.scheduled, jobs?.unscheduled)}
              </span>
            </p>
            <p>
              Total Workers:{' '}
              <span>{getTotalWorkers(jobs?.scheduled, jobs?.unscheduled)}</span>
            </p>
          </div>
          <div className="w-full">
            <Tab.Group
              as={'div'}
              className="mt-5 mb-2"
              selectedIndex={['Scheduled', 'Unscheduled'].findIndex(
                (tab) => tab === selectedTab
              )}
            >
              <Tab.List
                as={'div'}
                className="inline-flex rounded-lg shadow-sm overflow-hidden border border-[#d0d5dd] gap-x-px bg-[#d0d5dd] w-full h-[3%]"
              >
                {/* <div > */}
                {['Scheduled', 'Unscheduled'].map((tab) => {
                  return (
                    <Tab as={Fragment} key={tab}>
                      {({ selected }) => (
                        <button
                          className={
                            selected ? selectedStyle : nonSelectedStyle
                          }
                          onClick={() => {
                            setSelectedTab(tab);
                          }}
                        >
                          {tab}
                        </button>
                      )}
                    </Tab>
                  );
                })}
              </Tab.List>
              <div className="flex items-center w-full overflow-scroll">
                <Tab.Panels className="w-full">
                  <Tab.Panel className="mt-2 w-full">
                    {jobs?.scheduled?.map((elem) => {
                      return (
                        <div
                          className="border-l-8 border-l-cyan-100 p-4 mb-2 shadow-md"
                          key={elem?.createInput?._id}
                        >
                          <p>
                            <span className="font-medium">Job Id:</span>{' '}
                            {elem?.createInput?.id}
                          </p>
                          <p>
                            <span className="font-medium">Model Name:</span>{' '}
                            {elem?.createInput?.modelName}
                          </p>
                          <p>
                            <span className="font-medium">
                              Current Working Batch:
                            </span>{' '}
                            {elem?.createInput?.cuProjects?.[
                              elem?.createInput?.cuProjects?.length - 1
                            ]?.batchInfo?.batchName || '-'}
                          </p>
                          <p className="flex gap-1">
                            <span className="font-medium">Machine:</span>{' '}
                            {getMachine(elem).component}
                          </p>
                          <p className="flex gap-1">
                            <span className="font-medium">Worker:</span>{' '}
                            {getWorker(elem).component}
                          </p>
                          <p>
                            <span className="font-medium">
                              Expected End Date:
                            </span>{' '}
                            {new Date(elem?.endDate)?.toDateString()}
                          </p>
                        </div>
                      );
                    })}
                  </Tab.Panel>
                  <Tab.Panel className="mt-2">
                    {jobs?.unscheduled?.map((elem) => {
                      return (
                        <div
                          className="border-l-8 border-l-cyan-100 p-4 mb-2 shadow-md"
                          key={elem?.project?._id}
                        >
                          <p>
                            <span className="font-medium">Job Id:</span>{' '}
                            {elem?.project?.id}
                          </p>
                          <p>
                            <span className="font-medium">Model Name:</span>{' '}
                            {elem?.project?.modelName}
                          </p>
                          <p>
                            <span className="font-medium">
                              Current Working Batch:
                            </span>{' '}
                            {elem?.batchInfo?.batchName || '-'}
                          </p>
                          <p className="flex gap-1">
                            <span className="font-medium">Machine:</span>{' '}
                            {getMachine(elem).component}
                          </p>
                          <p className="flex gap-1">
                            <span className="font-medium">Worker:</span>{' '}
                            {getWorker(elem).component}
                          </p>
                          <p>
                            <span className="font-medium">
                              Expected End Date:
                            </span>{' '}
                            {new Date(elem?.stopTime)?.toDateString()}
                          </p>
                        </div>
                      );
                    })}
                  </Tab.Panel>
                </Tab.Panels>
              </div>
            </Tab.Group>
          </div>
        </div>
      </RightSidebar>
    </>
  );
};

export default PlanningSidebar;
