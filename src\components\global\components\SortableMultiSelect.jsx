import Select, { components } from 'react-select';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc';

const arrayMove = (array, from, to) => {
  const slicedArray = array.slice();
  slicedArray.splice(
    to < 0 ? array.length + to : to,
    0,
    slicedArray.splice(from, 1)[0]
  );
  return slicedArray;
};

const SortableMultiValue = SortableElement((props) => {
  const onMouseDown = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };
  const innerProps = { ...props.innerProps, onMouseDown };
  return <components.MultiValue {...props} innerProps={innerProps} />;
});

const SortableMultiValueLabel = SortableHandle((props) => (
  <components.MultiValueLabel {...props} />
));

const SortableSelect = SortableContainer(Select);

const SortableMultiSelect = ({ options, selected, setSelected }) => {
  const onChange = (selectedOptions) => {
    if (setSelected) {
      setSelected(selectedOptions);
    }
  };

  const onSortEnd = ({ oldIndex, newIndex }) => {
    if ((setSelected, selected)) {
      const newValue = arrayMove(selected, oldIndex, newIndex);
      setSelected(newValue);
    }
  };

  return (
    <SortableSelect
      useDragHandle
      axis="xy"
      onSortEnd={onSortEnd}
      getHelperDimensions={({ node }) => node.getBoundingClientRect()}
      isMulti
      options={options?.map((opt) => ({ value: opt, label: opt }))}
      value={selected}
      onChange={onChange}
      components={{
        MultiValue: SortableMultiValue,
        MultiValueLabel: SortableMultiValueLabel,
      }}
      closeMenuOnSelect={false}
    />
  );
};

export default SortableMultiSelect;
