import { useState } from 'react';
import { ReactComponent as JpgPng } from '../../../assets/svgs/pdfsvg.svg';
import RmService from '../../../services/createRm.services';
import DragAndDrop from '../../global/components/DragAndDrop';

const RmModalOfs = ({ closermModal, allOfs, setReRender }) => {
  const [numberValue, setNumberValue] = useState('');
  const [textValue, setTextValue] = useState('');
  const [pdf, setpdf] = useState([]);

  const handleNumberChange = (event) => {
    setNumberValue(event.target.value);
  };

  const handleTextChange = (event) => {
    setTextValue(event.target.value);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    const data = {
      rmNo: numberValue,
      rmDesc: textValue,
      files: pdf,
      createInput: allOfs.createInput,
      cuProject: allOfs.cuProject,
      ofsId: allOfs._id,
    };

    await RmService.createRm(data);
    setReRender((prev) => !prev);

    closermModal();
  };

  const changeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name;
      let ftype = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;

        let data = {
          fname: fname,
          ftype: ftype,
          data: url,
        };
        setpdf((prev) => [...prev, data]);
      });
    }
  };

  return (
    <div className="modal fixed top-0 left-0 w-full h-full flex justify-center items-center bg-black bg-opacity-50">
      <div className="modal-content bg-white p-4 rounded relative w-[50%] h-[80%] overflow-scroll">
        <button
          className="absolute top-2 right-2 text-gray-600 hover:text-gray-800"
          onClick={closermModal}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
        <form onSubmit={handleSubmit} className="mt-14">
          <div className="mb-4">
            <label
              htmlFor="numberInput"
              className="block text-sm font-medium text-gray-700"
            >
              RM Number:
            </label>
            <input
              id="numberInput"
              type="number"
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-md sm:text-sm border-gray-800 rounded-md"
              value={numberValue}
              onChange={handleNumberChange}
            />
          </div>
          <div className="mb-4">
            <label
              htmlFor="textInput"
              className="block text-sm font-medium text-gray-700"
            >
              Description:
            </label>
            <textarea
              id="textInput"
              rows="3"
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-md sm:text-sm border-gray-800 rounded-md"
              value={textValue}
              onChange={handleTextChange}
            ></textarea>
          </div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700">
              Select File
            </label>
            <DragAndDrop
              accept="application/pdf"
              fileType="JPG/PNG"
              svg={<JpgPng className="h-10" />}
              onChange={(e) => changeHandler(e, 'project')}
              multiple
            />
          </div>
          <div className="flex justify-center">
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-700 text-white  py-1 px-3 rounded"
            >
              Save
            </button>
          </div>

          {pdf?.map((item, idx) => {
            return (
              <p key={idx} className="text-black/50 text-sm ml-10">
                {item.fname}
              </p>
            );
          })}
        </form>
      </div>
    </div>
  );
};

export default RmModalOfs;
