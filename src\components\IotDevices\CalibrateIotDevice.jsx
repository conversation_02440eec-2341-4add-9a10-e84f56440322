import { iot, mqtt5 } from 'aws-iot-device-sdk-v2';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useGetIoTDeviceByIdQuery } from '../../slices/iotDeviceApiSlice';
import { Store } from '../../store/Store';
import { AWSCognitoCredentialsProvider } from '../../utils/DeviceDashboardLayout';
import Button from '../global/components/Button';
import Header from '../global/components/Header';
import Input from '../global/components/Input';
import Table from '../global/components/Table';

function CalibrateIotDevice() {
  const navigate = useNavigate();
  const { id } = useParams();

  const [calData, setCalData] = useState({
    CT1amp: '',
    CT1turn: '',
    CT1time: '40',
    CT2amp: '',
    CT2turn: '',
    CT2time: '40',
    CT3amp: '',
    CT3turn: '',
    CT3time: '40',
  });
  const [isCalibrating, setIsCalibrating] = useState('');
  const [timer, setTimer] = useState(0);
  const [iotClient, setIotClient] = useState();

  const { data: iotDevice } = useGetIoTDeviceByIdQuery(
    { id },
    { skip: !id, refetchOnMountOrArgChange: true }
  );

  const { defaults: { awsIotDetails = {} } = {} } = useContext(Store);
  const { awsIotPoolId, awsIotEndpoint, awsIotRegion } = awsIotDetails;

  const publishMessage = useCallback(
    (topic, message, retain = false) => {
      if (!topic || !message || !iotClient) return;

      iotClient.publish({
        qos: mqtt5.QoS.AtLeastOnce,
        topicName: topic,
        payload: message,
        retain,
      });
    },
    [iotClient]
  );

  useEffect(() => {
    let client = null;
    if (awsIotPoolId && awsIotEndpoint && awsIotRegion) {
      (async () => {
        const provider = new AWSCognitoCredentialsProvider({
          IdentityPoolId: awsIotPoolId,
          Region: awsIotRegion,
        });
        // Make sure the credential provider fetched before setup the connection
        await provider.refreshCredentials();

        const wsConfig = {
          credentialsProvider: provider,
          region: awsIotRegion,
        };

        const builder =
          iot.AwsIotMqtt5ClientConfigBuilder.newWebsocketMqttBuilderWithSigv4Auth(
            awsIotEndpoint,
            wsConfig
          );

        client = new mqtt5.Mqtt5Client(builder.build());

        client.start();

        setIotClient(client);
      })();
    }

    return () => {
      client?.stop();
      client?.close();
    };
  }, [awsIotPoolId, awsIotEndpoint, awsIotRegion]);

  useEffect(() => {
    if (timer <= 0) {
      setIsCalibrating('');
      if (isCalibrating)
        publishMessage(
          `/${iotDevice?.deviceId}/CALBUTTON`,
          `${isCalibrating?.[2]}1`
        );

      return;
    }
    const timeout = setTimeout(() => {
      setTimer(timer - 1);
    }, 1000);

    () => {
      clearTimeout(timeout);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timer, publishMessage, iotDevice?.deviceId]);

  const onCalibrate = (ct) => {
    let check = false;
    const vals = ['amp', 'turn', 'time'];

    vals.forEach((val) => {
      if (!calData?.[`${ct}${val}`]) {
        check = true;
      }
    });

    if (check) {
      toast.error(`Please add all fields for ${ct} before calibrating`);
      return;
    }

    publishMessage(
      `/${iotDevice?.deviceId}/REFAMP${ct}`,
      calData?.[`${ct}amp`]
    );
    publishMessage(
      `/${iotDevice?.deviceId}/TURNS${ct}`,
      calData?.[`${ct}turn`]
    );
    publishMessage(`/${iotDevice?.deviceId}/CALBUTTON`, `${ct?.[2]}0`);

    setIsCalibrating(ct);
    setTimer(+calData?.[`${ct}time`]);
  };

  return (
    <div>
      <div className="w-full flex justify-between items-center">
        <Header
          title="Calibrate IoT Device"
          description="Calibrate ct's for iot device here."
        />
        <Button onClick={() => navigate(-1)}>Back</Button>
      </div>

      <h3>Calibrate for {iotDevice?.deviceId}</h3>

      <datalist id="turns">
        <option value="1" />
        <option value="2" />
        <option value="3" />
        <option value="4" />
        <option value="5" />
        <option value="6" />
        <option value="7" />
        <option value="8" />
        <option value="9" />
        <option value="10" />
      </datalist>

      <div className="w-full overflow-x-scroll mt-3">
        <Table>
          <Table.Head>
            <Table.Row>
              <Table.Th>#</Table.Th>
              <Table.Th>Name</Table.Th>
              <Table.Th>Reference&nbsp;Amps</Table.Th>
              <Table.Th>No&nbsp;of&nbsp;Turns</Table.Th>
              <Table.Th>Timer</Table.Th>
              <Table.Th>Calibrate</Table.Th>
            </Table.Row>
          </Table.Head>

          <Table.Body>
            {['CT1', 'CT2', 'CT3'].map((ct, cIdx) => (
              <Table.Row key={ct}>
                <Table.Td>{cIdx + 1}</Table.Td>
                <Table.Td>{ct}</Table.Td>
                <Table.Td>
                  <Input
                    disabled={isCalibrating}
                    value={calData?.[`${ct}amp`] || ''}
                    onChange={(e) => {
                      setCalData((prev) => ({
                        ...prev,
                        [`${ct}amp`]: e.target.value,
                      }));
                    }}
                    type="number"
                  />
                </Table.Td>
                <Table.Td>
                  <Input
                    list="turns"
                    disabled={isCalibrating}
                    value={calData?.[`${ct}turn`] || ''}
                    onChange={(e) => {
                      setCalData((prev) => ({
                        ...prev,
                        [`${ct}turn`]: e.target.value,
                      }));
                    }}
                    type="number"
                  />
                </Table.Td>
                <Table.Td>
                  <Input
                    disabled={isCalibrating}
                    value={calData?.[`${ct}time`] || ''}
                    onChange={(e) => {
                      setCalData((prev) => ({
                        ...prev,
                        [`${ct}time`]: e.target.value,
                      }));
                    }}
                    type="number"
                  />
                </Table.Td>
                <Table.Td>
                  <Button
                    className={'w-32'}
                    isLoading={isCalibrating === ct}
                    disabled={isCalibrating}
                    onClick={() => onCalibrate(ct)}
                    spinnerSize="4"
                  >
                    Calibrate
                  </Button>
                </Table.Td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

      {isCalibrating ? (
        <p className="mt-3 text-center text-2xl font-semibold">
          Calibrating {isCalibrating}, Please wait {timer} secs
        </p>
      ) : null}
    </div>
  );
}

export default CalibrateIotDevice;
