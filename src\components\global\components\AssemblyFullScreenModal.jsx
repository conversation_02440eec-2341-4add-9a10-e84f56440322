import { Button } from 'antd';
import { useMediaQuery } from 'react-responsive';
import { mobileWidth, tabletWidth } from '../../../helperFunction';

const AssemblyFullScreenModal = ({
  title,
  description,
  onClose,
  children,
  onAdd,
  onSubmit,
}) => {
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });

  return (
    <div className="fixed inset-0 z-40 flex items-center justify-center bg-black bg-opacity-50">
      {/* Modal Container */}
      <div
        className={`relative bg-white rounded-lg shadow-lg ${!isMobile && !isTablet ? 'w-[80%]' : 'w-full'} h-full overflow-y-auto py-8 mt-[6rem] ${!isMobile && !isTablet ? 'px-16' : 'px-4'}`}
      >
        <button
          className="border-none float-right mt-1 text-black cursor-pointer"
          onClick={() => onClose()}
          aria-label="Close modal"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
        <h1 className="mt-3 text-2xl font-semibold text-gray-800">{title}</h1>
        <p className="mb-6 font-normal text-gray-500">{description}</p>

        {/* Sidebar and Content Container */}
        <div className="flex h-full">
          {/* Left Sidebar for Buttons */}
          <div className="flex flex-col w-[20%] px-4 py-6 border-r rounded-tl-xl border-gray-300 bg-gray-50">
            <Button
              className="mb-4 !bg-blue-600 !text-white !border-none !rounded-md !py-3 !hover:bg-blue-700 w-full"
              onClick={onAdd}
            >
              Add
            </Button>
            <Button
              className="!bg-blue-600 !text-white !border-none !rounded-md !py-3 !hover:bg-blue-700 w-full"
              onClick={() => onSubmit()}
            >
              Submit
            </Button>
          </div>

          {/* Right Content Area */}
          <div className="flex-1 p-6 overflow-y-auto rounded-tr-xl bg-gray-50">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssemblyFullScreenModal;
