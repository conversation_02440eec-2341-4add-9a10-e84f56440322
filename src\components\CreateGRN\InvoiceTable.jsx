import { <PERSON>, Spin, Button } from 'antd';
import { useGetCustomColumnsQuery } from '../../slices/customCoulmn.ApiSlice';
import { FilePdfOutlined, DeleteOutlined } from '@ant-design/icons';
import { useState } from 'react';
import MediaPreviewModal from '../DispatchV2/MediaPreviewModal';

const InvoiceTable = ({ invoiceData, setInvoiceData }) => {
  const { data: allCustomCols, isLoading } = useGetCustomColumnsQuery();
  const [openMediaModal, setOpenMediaModal] = useState(false);
  const [mediaData, setMediaData] = useState([]);
  const customColumns =
    allCustomCols?.filter((col) => col?.pageName === 'grn/invoice') || [];

  const customColFields =
    customColumns?.map((col) => ({
      title: col?.columnName,
      dataIndex: col?.columnName,
      key: col?.columnName,
      render: (_, record) => {
        const matched = record?.additionalFields?.find(
          (field) => col?._id === field?.columnId
        );
        return matched?.value || '-';
      },
    })) || [];

  const columns = [
    {
      title: 'Invoice ID',
      dataIndex: 'invoiceId',
      key: 'invoiceId',
    },
    {
      title: 'Invoice Amount',
      dataIndex: 'invoiceAmount',
      key: 'invoiceAmount',
    },
    {
      title: 'Invoice Comments',
      dataIndex: 'invoiceComments',
      key: 'invoiceComments',
    },
    {
      title: 'Files',
      dataIndex: 'files',
      key: 'files',
      render: (files) => (
        <Button
          icon={<FilePdfOutlined />}
          type="link"
          size="small"
          onClick={() => {
            setOpenMediaModal(true);
            setMediaData(files || []);
          }}
          disabled={!files || !files.length}
          className="whitespace-nowrap"
        />
      ),
    },
    ...customColFields,
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record, index) => (
        <div>
          <Button
            danger
            icon={<DeleteOutlined />}
            type="link"
            onClick={() => {
              setInvoiceData((prev) => prev.filter((_, i) => i !== index));
            }}
          />
        </div>
      ),
    },
  ];

  return (
    <div>
      {openMediaModal && (
        <MediaPreviewModal
          setMediaData={setMediaData}
          mediaData={{ media: mediaData }}
          openMediaModal={openMediaModal}
          setOpenMediaModal={setOpenMediaModal}
        />
      )}
      {isLoading ? (
        <Spin />
      ) : (
        <Table
          dataSource={invoiceData}
          columns={columns}
          pagination={false}
          scroll={{ x: true }}
        />
      )}
    </div>
  );
};

export default InvoiceTable;
