import {
  But<PERSON>,
  Card,
  Empty,
  Spin,
  Modal,
  Input,
  Tag,
  Tooltip,
  Avatar,
} from 'antd';
import { useState } from 'react';
import {
  useGetPaginatedTransportIndentsQuery,
  useUpdateTransportIndentMutation,
} from '../../slices/transportIndentApiSlice';
import Pagination from '../global/components/Pagination';
import TransportIndentModal from '../Indent/TransportIndentModal';
import {
  ArrowRightOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  CheckOutlined,
  TruckOutlined,
  EyeOutlined,
  UserOutlined,
  ShopOutlined,
  TeamOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import SelectV2 from '../global/components/SelectV2';
import { toast } from 'react-toastify';
import { customConfirm } from '../../utils/customConfirm';
import TransportIndentSidebar from './TransportIndentSidebar';
import RightSidebar from '../global/components/RightSidebar';

const statusOptions = [
  {
    label: '+ Create New',
    value: 'create_new',
    icon: <PlusOutlined />,
  },
  { label: 'Created', value: 'Created' },
  { label: 'Dispatched', value: 'Dispatched' },
  { label: 'On the way', value: 'On the way' },
  { label: 'Delivered', value: 'Delivered' },
  { label: 'Other', value: 'Other' },
];

const getStatusConfig = (status) => {
  const configs = {
    Created: { color: '#1890ff', bgColor: '#e6f7ff', borderColor: '#91d5ff' },
    Dispatched: {
      color: '#fa541c',
      bgColor: '#fff2e8',
      borderColor: '#ffbb96',
    },
    'On the way': {
      color: '#13c2c2',
      bgColor: '#e6fffb',
      borderColor: '#87e8de',
    },
    Delivered: { color: '#52c41a', bgColor: '#f6ffed', borderColor: '#b7eb8f' },
    Completed: { color: '#52c41a', bgColor: '#f6ffed', borderColor: '#b7eb8f' },
    Other: { color: '#8c8c8c', bgColor: '#f5f5f5', borderColor: '#d9d9d9' },
  };
  return configs[status] || configs.Other;
};

const TransportIndents = ({
  fromDashboardTab = false,
  fromCompletedTab = false,
}) => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(25);
  const filter = {
    page,
    limit,
    isStarted: fromDashboardTab || fromCompletedTab,
    isCompleted: fromCompletedTab,
  };
  const { data: allTransportIndents, isLoading } =
    useGetPaginatedTransportIndentsQuery(filter);
  const [updateTransportIndent, { isLoading: updateLoading }] =
    useUpdateTransportIndentMutation();
  const [openStartModal, setOpenStartModal] = useState(false);
  const [selectedData, setSelectedData] = useState({});
  const [inputValue, setInputValue] = useState('');
  const [openCustomOptionModal, setOpenCustomOptionModal] = useState(false);
  const [openSideBar, setOpenSideBar] = useState(false);

  const getTimeAgo = (dateString) => {
    const updatedDate = new Date(dateString);
    const now = new Date();
    const diffMs = now - updatedDate;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    if (diffMinutes < 60) {
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    }
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    }
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  };

  const handleStatusChange = async (value, record) => {
    if (value === 'create_new') {
      setOpenCustomOptionModal(true);
      return;
    }
    const res = await updateTransportIndent({
      id: record._id,
      data: { status: value },
    });
    if (!res.error) {
      toast.success('Status Updated Successfully');
    }
  };

  const handleMarkComplete = async (record) => {
    const confirm = await customConfirm(
      'Are you sure you want to mark this as completed?',
      'success'
    );
    if (!confirm) return;
    const res = await updateTransportIndent({
      id: record._id,
      data: { isCompleted: true, status: 'Completed' },
    });
    if (!res.error) {
      toast.success('Status Updated Successfully');
    }
  };

  const getLocationInfo = (locationData) => {
    const { type, vendorName, customerName, address } = locationData || {};

    if (type === 'Vendor') {
      return {
        type: 'Vendor',
        name: vendorName || 'Unknown Vendor',
        icon: <ShopOutlined className="text-blue-600" />,
        color: '#1890ff',
        bgColor: '#e6f7ff',
      };
    }

    if (type === 'Customer') {
      return {
        type: 'Customer',
        name: customerName || 'Unknown Customer',
        icon: <TeamOutlined className="text-green-600" />,
        color: '#52c41a',
        bgColor: '#f6ffed',
      };
    }

    return {
      type: type || 'Unknown',
      name: address ? address : 'Not specified',
      address: address || null,
      icon: <UserOutlined className="text-gray-500" />,
      color: '#8c8c8c',
      bgColor: '#f5f5f5',
    };
  };

  const LocationCard = ({ location, label, dotColor }) => {
    const locationInfo = getLocationInfo(location);

    return (
      <div className="flex-1 min-w-0">
        <div className="text-xs text-gray-500 mb-2 uppercase tracking-wide">
          {label}
        </div>
        <div className="flex items-center space-x-3">
          <div
            className={`w-2 h-2 rounded-full ${dotColor} flex-shrink-0`}
          ></div>
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            <div
              className="p-1 rounded flex-shrink-0"
              style={{ backgroundColor: locationInfo.bgColor }}
            >
              {locationInfo.icon}
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-gray-900 break-words whitespace-normal">
                {locationInfo.type}
              </div>
              <div className="text-xs text-gray-500">{locationInfo.name}</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const StatusBadge = ({ status }) => {
    if (!status) return null;
    const config = getStatusConfig(status);
    return <Tag color={config.color}>{status}</Tag>;
  };

  return (
    <div className="mt-6 min-h-screen bg-gray-50">
      {openStartModal && (
        <TransportIndentModal
          setOpen={setOpenStartModal}
          data={selectedData}
          startProcess={true}
        />
      )}

      <RightSidebar openSideBar={openSideBar} setOpenSideBar={setOpenSideBar}>
        <TransportIndentSidebar data={selectedData} />
      </RightSidebar>

      <Modal
        open={openCustomOptionModal}
        onCancel={() => setOpenCustomOptionModal(false)}
        okText="Add"
        centered
        title="Add New Status Option"
        onOk={() => {
          statusOptions.push({ label: inputValue, value: inputValue });
          setOpenCustomOptionModal(false);
          setInputValue('');
        }}
      >
        <div className="py-4">
          <label className="text-sm font-semibold text-gray-700 block mb-2">
            Status Name
          </label>
          <Input
            placeholder="Enter status name"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            size="large"
          />
        </div>
      </Modal>

      <div className="space-y-6">
        {isLoading ? (
          <div className="flex justify-center items-center py-24">
            <Spin size="large" />
          </div>
        ) : allTransportIndents?.results?.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
            <Empty
              description="No Transport Indents Found"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          allTransportIndents?.results?.map((indent, index) => (
            <Card
              key={indent._id || index}
              className="hover:shadow-lg transition-shadow duration-300 border border-gray-200 rounded-lg shadow-sm bg-white"
              bodyStyle={{ padding: 0 }}
            >
              {/* Mobile Layout */}
              <div className="block lg:hidden p-4">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <Avatar
                      size={32}
                      className="bg-blue-100 text-blue-600 flex-shrink-0"
                      icon={<TruckOutlined />}
                    />
                    <div className="min-w-0 flex-1">
                      <Tooltip title={indent?.name}>
                        <h3 className="text-sm font-semibold text-gray-900 break-words whitespace-normal max-w-full">
                          {indent?.name || `Transport #${index + 1}`}
                        </h3>
                      </Tooltip>
                    </div>
                  </div>
                  <Button
                    onClick={() => {
                      setOpenSideBar(true);
                      setSelectedData(indent);
                    }}
                    type="text"
                    size="small"
                    icon={<EyeOutlined />}
                    className="text-gray-600 hover:text-blue-600"
                  />
                </div>

                {/* Route Section */}
                <div className="bg-gray-50 rounded-lg p-4 mb-4">
                  <LocationCard
                    location={indent?.from}
                    label="From"
                    dotColor="bg-green-500"
                  />

                  <div className="flex justify-center my-3">
                    <ArrowDownOutlined />
                  </div>

                  <LocationCard
                    location={indent?.to}
                    label="To"
                    dotColor="bg-red-500"
                  />
                </div>

                {/* Status and Metadata */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-700">
                      Status
                    </span>
                    <StatusBadge status={indent?.status} />
                  </div>

                  <div className="space-y-1 text-xs text-gray-500">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1.5">
                        <ClockCircleOutlined />
                        <span>Updated</span>
                      </div>
                      <span>{getTimeAgo(indent?.updatedAt)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1.5">
                        <UserOutlined />
                        <span>By</span>
                      </div>
                      <span className="font-medium">
                        {indent?.createdBy?.name}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="border-t border-gray-100 pt-4">
                  {!fromDashboardTab && !fromCompletedTab && (
                    <Button
                      onClick={() => {
                        setOpenStartModal(true);
                        setSelectedData(indent);
                      }}
                      type="primary"
                      size="middle"
                      className="w-full"
                      icon={<ArrowRightOutlined />}
                    >
                      Start Process
                    </Button>
                  )}

                  {fromDashboardTab && (
                    <div className="space-y-4">
                      <SelectV2
                        className="!w-full"
                        options={statusOptions}
                        value={indent?.status}
                        onChange={async (e) =>
                          await handleStatusChange(e.target.value, indent)
                        }
                        size="small"
                      />
                      <Button
                        loading={updateLoading}
                        icon={<CheckOutlined />}
                        disabled={updateLoading}
                        onClick={async () => await handleMarkComplete(indent)}
                        type="primary"
                        size="small"
                        className="w-full bg-green-600 hover:bg-green-700"
                      >
                        Complete
                      </Button>
                    </div>
                  )}

                  {fromCompletedTab && (
                    <div className="text-center">
                      <Tag color="success" className="px-3 py-1 text-xs">
                        <CheckOutlined className="mr-1" />
                        Completed
                      </Tag>
                    </div>
                  )}
                </div>
              </div>

              {/* Desktop Layout */}
              <div className="hidden lg:block p-6">
                <div className="flex items-center justify-between">
                  {/* Left Section - Main Info */}
                  <div className="flex items-center space-x-6 flex-1 min-w-0">
                    {/* Transport Info */}
                    <div className="flex items-center space-x-4">
                      <Avatar
                        size={40}
                        className="bg-blue-100 text-blue-600"
                        icon={<TruckOutlined />}
                      />
                      <div>
                        <Tooltip title={indent?.name}>
                          <h3 className="text-base font-semibold text-gray-900 break-words whitespace-normal max-w-[180px]">
                            {indent?.name || `Transport #${index + 1}`}
                          </h3>
                        </Tooltip>
                      </div>
                    </div>

                    {/* Route */}
                    <div className="flex items-center space-x-6 flex-1 min-w-0 max-w-xl">
                      <LocationCard
                        location={indent?.from}
                        label="From"
                        dotColor="bg-green-500"
                      />

                      <div className="mt-4">
                        <ArrowRightOutlined />
                      </div>

                      <LocationCard
                        location={indent?.to}
                        label="To"
                        dotColor="bg-red-500"
                      />
                    </div>

                    {/* Status */}
                    <div className="flex-shrink-0">
                      <StatusBadge status={indent?.status} />
                    </div>
                  </div>

                  {/* Right Section - Metadata & Actions */}
                  <div className="flex items-center space-x-6 flex-shrink-0">
                    {/* Metadata */}
                    <div className="text-right space-y-1">
                      <div className="text-xs text-gray-500 flex items-center space-x-1">
                        <ClockCircleOutlined />
                        <span>{getTimeAgo(indent?.updatedAt)}</span>
                      </div>
                      <div className="text-xs text-gray-500 flex items-center space-x-1">
                        <UserOutlined />
                        <span>{indent?.createdBy?.name}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-4">
                      <Button
                        onClick={() => {
                          setOpenSideBar(true);
                          setSelectedData(indent);
                        }}
                        type="default"
                        size="small"
                        icon={<EyeOutlined />}
                      >
                        View
                      </Button>

                      {!fromDashboardTab && !fromCompletedTab && (
                        <Button
                          onClick={() => {
                            setOpenStartModal(true);
                            setSelectedData(indent);
                          }}
                          type="primary"
                          size="small"
                          icon={<ArrowRightOutlined />}
                        >
                          Start
                        </Button>
                      )}

                      {fromDashboardTab && (
                        <div className="flex items-center space-x-4">
                          <SelectV2
                            className="!w-[140px]"
                            options={statusOptions}
                            value={indent?.status}
                            onChange={async (e) =>
                              await handleStatusChange(e.target.value, indent)
                            }
                            size="small"
                          />
                          <Button
                            loading={updateLoading}
                            icon={<CheckOutlined />}
                            disabled={updateLoading}
                            onClick={async () =>
                              await handleMarkComplete(indent)
                            }
                            type="primary"
                            size="small"
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Complete
                          </Button>
                        </div>
                      )}

                      {fromCompletedTab && (
                        <Tag color="success" className="px-2 py-1 text-xs">
                          <CheckOutlined className="mr-1" />
                          Completed
                        </Tag>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>

      {allTransportIndents?.results?.length > 0 && (
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <Pagination
            data={allTransportIndents?.results}
            page={page}
            limit={limit}
            setPage={setPage}
            setLimit={setLimit}
            totalPages={allTransportIndents?.totalPages}
            totalResults={allTransportIndents?.totalResults}
          />
        </div>
      )}
    </div>
  );
};

export default TransportIndents;
