import { useEffect, useState } from 'react';
import { BiTrash } from 'react-icons/bi';
import { toast } from 'react-toastify';
import { getDecodedHTML } from '../../helperFunction';
import {
  useEditFormMutation,
  useLazyGetAllFormQuery,
} from '../../slices/createFormapiSlice';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import RichTextDescription from '../v3/global/components/rich-text-description';
import MediaModal from './MediaModal';

const createObjects = (rows, column) => {
  const result = {};
  let colsData = [];
  column.forEach((col) => {
    colsData.push({ type: col?.columnType || '', value: '' });
  });
  for (let i = 0; i < rows; i++) {
    result[i + 1] = colsData;
  }
  return result;
};

const DepartmentForm = ({
  Form,
  setForm,
  setFilledFormData,
  filledFormData,
  initialData,
  // modalLeft = '24%',
}) => {
  // console.log(filledFormData)
  const [queryform] = useLazyGetAllFormQuery();
  const [formList, setFormList] = useState(null);
  const [depForm, setDepForm] = useState({});
  const [fieldOptions, setFieldOptions] = useState([]);
  const [isAddOption, setIsAddOption] = useState({
    isAdd: false,
    fieldName: '',
  });
  const [isAddMedia, setIsAddMedia] = useState({
    isAdd: false,
    fieldName: '',
    media: [],
  });

  const [editForm] = useEditFormMutation();

  useEffect(() => {
    const getForm = async () => {
      const response = await queryform({ formType: 'Department' }).unwrap();
      setFormList(response);
      setDepForm(response?.find((item) => item?.formName === Form?.formName));
    };
    getForm();
  }, [Form?.formName, queryform, setForm]);

  useEffect(() => {
    if (Object.keys(initialData?.formData || {})?.length > 0) {
      setFilledFormData(initialData?.formData);
    } else {
      if (depForm && Object.keys(filledFormData).length === 0) {
        let data = {};

        depForm?.formData?.forEach((item, index) => {
          if (item.fieldType === 'Table') {
            data = {
              ...data,
              [`table_${index}`]: {
                type: item.fieldType,
                columns: item?.tableOptions?.column || [],
                noOfRows: +item?.tableOptions?.rows || 0,
                rows: +item?.tableOptions?.rows || 0,
                row:
                  item?.tableOptions?.row ||
                  Array.from({ length: item?.tableOptions?.rows }, () => ''),
                newRowNo: 0,
                rowData: createObjects(
                  +item?.tableOptions?.rows || 0,
                  item?.tableOptions?.column || []
                ),
              },
            };
          }
        });
        if (Object.keys(data)?.length > 0) {
          setFilledFormData(data);
        }
      }
    }
  }, [depForm, initialData]); //eslint-disable-line

  const handleOptionAdd = async () => {
    let newFormData = depForm?.formData || [];

    let filteredFieldOptions = fieldOptions?.filter(
      (itm) => itm?.value !== '' && itm !== ''
    );

    newFormData = newFormData.map((item) => {
      if (item.fieldName === isAddOption?.fieldName) {
        return {
          ...item,
          fieldOptions: [...filteredFieldOptions],
        };
      }
      return item;
    });

    let res = await editForm({
      id: depForm?._id,
      data: {
        formData: newFormData,
      },
    }).unwrap();
    if (res) {
      setDepForm((prev) => ({
        ...prev,
        formData: newFormData,
      }));
      setFieldOptions([]);
      setIsAddOption((prev) => ({ ...prev, isAdd: false, fieldName: '' }));
      toast.success('Option added successfully');
    }
  };

  return (
    <>
      {isAddMedia?.isAdd && (
        <MediaModal
          isAddMedia={isAddMedia}
          setIsAddMedia={setIsAddMedia}
          setFilledData={setFilledFormData}
          filledFormData={filledFormData}
        />
      )}
      {isAddOption?.isAdd && (
        <Modal
          title="Add Option"
          // modalLeft={modalLeft}
          onCloseModal={() =>
            setIsAddOption((prev) => ({ ...prev, isAdd: false }))
          }
          canSubmit={false}
          onSubmit={handleOptionAdd}
        >
          {() => {
            return (
              <>
                <div className="mt-4">
                  <div className="flex justify-between px-10">
                    <p className="text-sm font-medium">Dropdown Options</p>{' '}
                    <p
                      className="text-sm text-blue-500  cursor-pointer"
                      onClick={() => setFieldOptions((prev) => [...prev, ''])}
                    >
                      + Add More
                    </p>
                  </div>
                  <div className="grid grid-cols-2  gap-x-8 ml-6 mt-2">
                    {fieldOptions?.map((_, idX) => (
                      <div
                        className="flex items-center gap-3 mt-2 mx-auto"
                        key={idX}
                      >
                        <Input
                          value={fieldOptions[idX]?.value || ''}
                          onChange={(e) =>
                            setFieldOptions((prev) =>
                              prev.map((el, index) => {
                                if (index === idX) {
                                  return {
                                    label: e.target.value,
                                    value: e.target.value,
                                  };
                                }
                                return el;
                              })
                            )
                          }
                        />

                        <BiTrash
                          size={20}
                          className="cursor-pointer"
                          onClick={() =>
                            setFieldOptions((prev) =>
                              prev.filter((_, index) => idX !== index)
                            )
                          }
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </>
            );
          }}
        </Modal>
      )}

      {/* <div>
        <h1 className="text-xl">Form Fields</h1>
        <p className="text-sm text-gray-500 font-normal">
          Choose a form to fill
        </p>
        <Select
          options={formList?.map((item) => ({
            value: item,
            label: item?.formName,
          }))}
          onChange={(e) => {
            setDepForm(e.target.value);
            setForm(e.target.value);
          }}
          value={formList?.find((item) => item?._id === depForm?._id)}
          className={`mb-10 mt-5`}
          disabled={Form && Object.keys(Form).length !== 0}
        />
        <Table>
          <Table.Head>
            <Table.Row>
              <Table.Th>Field Name</Table.Th>
              <Table.Th>Action</Table.Th>
              <Table.Th></Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {depForm?.formData?.map((field, idx) => {
              return (
                <Table.Row key={idx}>
                  {field?.fieldType !== 'Section' && (
                    <Table.Td className="font-semibold flex flex-col justify-start mt-2">
                      <p className="flex">
                        <span>{field?.fieldName}</span>
                        {field?.isMandatory && (
                          <span className="text-red-500">*</span>
                        )}
                      </p>
                    </Table.Td>
                  )}

                  {field?.fieldType === 'Section' && (
                    <>
                      <Table.Td></Table.Td>
                      <Table.Td>
                        <h5 className="w-full py-1 bg-slate-500/65 text-white justify-center flex rounded-lg text-lg">
                          {field?.fieldName}
                        </h5>
                      </Table.Td>
                    </>
                  )}
                  {field?.fieldType === 'Range' && (
                    <Table.Td>
                      <div className="flex gap-5">
                        <Input
                          className=""
                          placeholder="Min Range"
                          type="number"
                          value={filledFormData?.[`range_${idx}`]?.min}
                          onChange={(e) =>
                            setFilledFormData((prev) => ({
                              ...prev,
                              [`range_${idx}`]: {
                                ...prev?.[`range_${idx}`],
                                min: e.target.value,
                              },
                            }))
                          }
                        />
                        <Input
                          className=""
                          placeholder="Max Range"
                          type="number"
                          value={filledFormData?.[`range_${idx}`]?.max}
                          onChange={(e) =>
                            setFilledFormData((prev) => ({
                              ...prev,
                              [`range_${idx}`]: {
                                ...prev?.[`range_${idx}`],
                                max: e.target.value,
                              },
                            }))
                          }
                        />
                      </div>
                    </Table.Td>
                  )}
                  {field?.fieldType === 'Range Threshold' && (
                    <Table.Td>
                      <div className="flex gap-5">
                        <Input
                          className=""
                          placeholder="Min Value"
                          value={filledFormData?.[`threshold_${idx}`]?.min}
                          onChange={(e) =>
                            setFilledFormData((prev) => ({
                              ...prev,
                              [`threshold_${idx}`]: {
                                ...prev?.[`threshold_${idx}`],
                                min: e.target.value,
                              },
                            }))
                          }
                        />
                        <Input
                          className=""
                          placeholder="Threshold Value"
                          value={
                            filledFormData?.[`threshold_${idx}`]?.threshold
                          }
                          onChange={(e) =>
                            setFilledFormData((prev) => ({
                              ...prev,
                              [`threshold_${idx}`]: {
                                ...prev?.[`threshold_${idx}`],
                                threshold: e.target.value,
                              },
                            }))
                          }
                        />
                      </div>
                    </Table.Td>
                  )}

                  {field?.fieldType === 'String' && (
                    <Table.Td>
                      <RichTextDescription
                        value={getDecodedHTML(
                          filledFormData?.[`string_${idx}`]
                        )}
                        onChange={(value) =>
                          setFilledFormData((prev) => ({
                            ...prev,
                            [`string_${idx}`]: value,
                          }))
                        }
                      />
                    </Table.Td>
                  )}

                  {field?.fieldType === 'MultiCheckbox' && (
                    <Table.Td className={'w-full'}>
                      <div className="mb-4 flex gap-2 mr-3 w-full">
                        {field.labelArray.map((option) => (
                          <div
                            key={option}
                            className="flex items-center justify-center w-full overflow-auto"
                          >
                            <Input
                              type="checkbox"
                              id={option}
                              checked={
                                filledFormData?.[`multicheckbox_${idx}`]?.[
                                  option
                                ]
                              }
                              onChange={(e) => {
                                setFilledFormData((prev) => ({
                                  ...prev,
                                  [`multicheckbox_${idx}`]: {
                                    ...prev?.[`multicheckbox_${idx}`],
                                    [option]: e.target.checked,
                                  },
                                }));
                              }}
                            />
                            <label
                              htmlFor={option}
                              className="ml-1 block text-sm text-gray-900"
                            >
                              {option}
                            </label>
                          </div>
                        ))}
                      </div>
                    </Table.Td>
                  )}
                  {field?.fieldType === 'Date' && (
                    <Table.Td>
                      <div className="flex gap-5">
                        <Input
                          className="w-full"
                          type="date"
                          value={filledFormData?.[`date_${idx}`]}
                          onChange={(e) =>
                            setFilledFormData((prev) => ({
                              ...prev,
                              [`date_${idx}`]: e.target.value,
                            }))
                          }
                        />
                      </div>
                    </Table.Td>
                  )}
                  {field?.fieldType === 'Check' && (
                    <Table.Td>
                      <input
                        className="text-[30px]"
                        type="checkbox"
                        checked={filledFormData?.[`check_${idx}`]}
                        onChange={(e) =>
                          setFilledFormData((prev) => ({
                            ...prev,
                            [`check_${idx}`]: e.target.checked,
                          }))
                        }
                      />
                    </Table.Td>
                  )}

                  {field?.fieldType === 'DropDown' && (
                    <Table.Td>
                      <Select
                        type="text"
                        name={field?.fieldName}
                        id={field?.fieldName}
                        options={[
                          { label: '+ Add New', value: 'newOption' },
                          ...field?.fieldOptions,
                        ]}
                        onChange={(e) => {
                          if (e.target.value === 'newOption') {
                            setFieldOptions(field?.fieldOptions);
                            setIsAddOption(() => ({
                              isAdd: true,
                              fieldName: field?.fieldName,
                            }));
                          } else {
                            setFilledFormData((prev) => ({
                              ...prev,
                              [`dropdown_${idx}`]: e.target.value,
                            }));
                          }
                        }}
                        value={filledFormData?.[`dropdown_${idx}`]}
                      />
                    </Table.Td>
                  )}
                  {field?.fieldType === 'Media' && (
                    <Table.Td>
                      <div className="w-full flex justify-end">
                        <button
                          type="button"
                          className="inline-flex items-center px-5 py-2.5 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                          onClick={() => {
                            setIsAddMedia(() => ({
                              isAdd: true,
                              fieldName: field?.fieldName,
                              media:
                                filledFormData?.[field?.fieldName]?.media || [],
                            }));
                          }}
                        >
                          Media
                          <span className="inline-flex items-center justify-center w-4 h-4 ms-2 text-xs font-semibold text-blue-800 bg-blue-200 rounded-full">
                            {filledFormData?.[field?.fieldName]?.media
                              ?.length || 0}
                          </span>
                        </button>
                      </div>
                    </Table.Td>
                  )}

                  {field?.fieldType === 'MultiSelect' && (
                    <Table.Td>
                      <MultiSelect
                        name={`multiselect_${idx}`}
                        id={`multiselect_${idx}`}
                        options={field?.fieldOptions}
                        onChange={(e) =>
                          setFilledFormData((prev) => ({
                            ...prev,
                            [`multiselect_${idx}`]: e.target.value.map(
                              (option) => option.value
                            ),
                          }))
                        }
                        value={filledFormData?.[`multiselect_${idx}`]}
                      />
                    </Table.Td>
                  )}

                  {field?.fieldType === 'Table' && (
                    <Table.Td>
                      <div
                        className="mt-4 flex flex-col px-3 overflow-auto w-[35rem]"
                        key={idx}
                      >
                        <div className="mt-3">
                          <div className="w-full col-span-2">
                            <div className="overflow-x-scroll pb-4 col-span-2">
                              <Table key={idx}>
                                <Table.Head>
                                  <Table.Row>
                                    <Table.Th className={`text-center`}>
                                      Sr. No.
                                    </Table.Th>
                                    {field?.tableOptions?.column?.map(
                                      (col, colIndex) => (
                                        <Table.Th
                                          key={colIndex}
                                          className={`text-center`}
                                        >
                                          {col?.columnName}
                                        </Table.Th>
                                      )
                                    )}
                                  </Table.Row>
                                </Table.Head>
                                <Table.Body>
                                  {Array.from({
                                    length:
                                      +field?.tableOptions?.rows ||
                                      0 +
                                        filledFormData?.[`table_${idx}`]
                                          ?.newRowNo ||
                                      0,
                                  }).map((_, bodyRowIndex) => {
                                    return (
                                      <Table.Row key={bodyRowIndex}>
                                        <Table.Td className={`text-center`}>
                                          {filledFormData?.[`table_${idx}`]
                                            ?.newRowNo ? (
                                            <>
                                              <Input
                                                className={'min-w-24 w-full'}
                                                type="text"
                                                value={
                                                  filledFormData?.[
                                                    `table_${idx}`
                                                  ]?.row[bodyRowIndex] || ''
                                                }
                                                onChange={(e) => {
                                                  setFilledFormData((prev) => ({
                                                    ...prev,
                                                    [`table_${idx}`]: {
                                                      ...(prev[
                                                        `table_${idx}`
                                                      ] || {}),
                                                      row: prev?.[
                                                        `table_${idx}`
                                                      ]?.row?.map(
                                                        (itm, index) =>
                                                          index === bodyRowIndex
                                                            ? e.target.value
                                                            : itm
                                                      ),
                                                    },
                                                  }));
                                                }}
                                              />
                                            </>
                                          ) : (
                                            filledFormData?.[`table_${idx}`]
                                              ?.row[bodyRowIndex] ||
                                            bodyRowIndex + 1
                                          )}
                                        </Table.Td>
                                        {field?.tableOptions?.column?.map(
                                          (col, colIndex) => {
                                            const accessKey = (
                                              bodyRowIndex + 1
                                            ).toString();
                                            if (
                                              col?.columnType === 'string' ||
                                              col?.columnType === 'number' ||
                                              col?.columnType === 'date'
                                            ) {
                                              return (
                                                <Table.Td
                                                  key={colIndex}
                                                  className={`text-center`}
                                                >
                                                  <Input
                                                    type={col?.columnType}
                                                    onChange={(e) => {
                                                      setFilledFormData(
                                                        (prev) => ({
                                                          ...prev,
                                                          [`table_${idx}`]: {
                                                            ...prev[
                                                              `table_${idx}`
                                                            ],
                                                            ['rowData']: {
                                                              ...prev[
                                                                `table_${idx}`
                                                              ]?.rowData,
                                                              [accessKey]: prev[
                                                                `table_${idx}`
                                                              ]?.rowData?.[
                                                                accessKey
                                                              ]?.map(
                                                                (itm, index) =>
                                                                  index ===
                                                                  colIndex
                                                                    ? {
                                                                        ...itm,
                                                                        value:
                                                                          e
                                                                            .target
                                                                            .value,
                                                                      }
                                                                    : itm
                                                              ) || [
                                                                {
                                                                  value:
                                                                    e.target
                                                                      .value,
                                                                },
                                                              ],
                                                            },
                                                          },
                                                        })
                                                      );
                                                    }}
                                                    value={
                                                      filledFormData?.[
                                                        `table_${idx}`
                                                      ]?.rowData?.[accessKey]?.[
                                                        colIndex
                                                      ]?.value || ''
                                                    }
                                                    className={`min-w-24 w-full`}
                                                  />
                                                </Table.Td>
                                              );
                                            } else if (
                                              col?.columnType === 'checkbox'
                                            ) {
                                              return (
                                                <Table.Td
                                                  key={colIndex}
                                                  className={`text-center`}
                                                >
                                                  <Input
                                                    type="checkbox"
                                                    checked={
                                                      filledFormData?.[
                                                        `table_${idx}`
                                                      ]?.rowData?.[accessKey]?.[
                                                        colIndex
                                                      ]?.value || false
                                                    }
                                                    onChange={(e) => {
                                                      setFilledFormData(
                                                        (prev) => ({
                                                          ...prev,
                                                          [`table_${idx}`]: {
                                                            ...prev[
                                                              `table_${idx}`
                                                            ],
                                                            ['rowData']: {
                                                              ...prev[
                                                                `table_${idx}`
                                                              ]?.rowData,
                                                              [accessKey]: prev[
                                                                `table_${idx}`
                                                              ]?.rowData?.[
                                                                accessKey
                                                              ]?.map(
                                                                (itm, index) =>
                                                                  index ===
                                                                  colIndex
                                                                    ? {
                                                                        ...itm,
                                                                        value:
                                                                          e
                                                                            .target
                                                                            .checked,
                                                                      }
                                                                    : itm
                                                              ) || [
                                                                {
                                                                  value:
                                                                    e.target
                                                                      .checked,
                                                                },
                                                              ],
                                                            },
                                                          },
                                                        })
                                                      );
                                                    }}
                                                  />
                                                </Table.Td>
                                              );
                                            } else {
                                              return (
                                                <Table.Td key={colIndex}>
                                                  <span>Unknown Type</span>
                                                </Table.Td>
                                              );
                                            }
                                          }
                                        )}
                                      </Table.Row>
                                    );
                                  })}
                                </Table.Body>
                              </Table>
                              <p
                                className={`mt-2 ml-5 ${field?.tableOptions?.rows > 0 ? 'hidden' : ''} text-blue-700 hover:text-blue-500`}
                                onClick={() => {
                                  let newRowNo =
                                    +filledFormData?.[`table_${idx}`]
                                      ?.newRowNo || 0;
                                  setFilledFormData((prev) => ({
                                    ...prev,
                                    [`table_${idx}`]: {
                                      ...prev[`table_${idx}`],
                                      row: [
                                        ...prev?.[`table_${idx}`]?.row,
                                        newRowNo + 1,
                                      ],
                                      newRowNo: newRowNo + 1,
                                      ['rowData']: {
                                        ...prev[`table_${idx}`]?.rowData,
                                        [newRowNo + 1]:
                                          field?.tableOptions?.column?.map(
                                            (item) => ({
                                              value: '',
                                              type: item?.columnType,
                                            })
                                          ),
                                      },
                                    },
                                  }));
                                }}
                              >
                                + Add Row
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Table.Td>
                  )}
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
      </div> */}

      {/* google form like ui */}

      <div>
        <h1 className="text-2xl font-medium mb-2">Form Fields</h1>
        <p className="text-md font-semibold text-gray-600 mt-6">
          Choose a form to fill
        </p>

        <Select
          options={formList?.map((item) => ({
            value: item,
            label: item?.formName,
          }))}
          onChange={(e) => {
            setDepForm(e.target.value);
            setForm(e.target.value);
          }}
          value={formList?.find((item) => item?._id === depForm?._id)}
          className="mb-10 mt-2 border-gray-300 rounded-md shadow-sm w-full"
          disabled={Form && Object.keys(Form).length !== 0}
        />

        <div className="flex flex-col space-y-6">
          {depForm?.formData?.map((field, idx) => (
            <div key={idx} className="flex flex-col">
              <label className="font-medium text-gray-800 capitalize mb-1 mt-4">
                {field?.fieldName}
                {field?.isMandatory && (
                  <span className="text-red-500 ml-1">*</span>
                )}
              </label>

              {field?.fieldType === 'Range' && (
                <div className="flex gap-4">
                  <Input
                    placeholder="Min Range"
                    type="number"
                    value={filledFormData?.[`range_${idx}`]?.min}
                    onChange={(e) =>
                      setFilledFormData((prev) => ({
                        ...prev,
                        [`range_${idx}`]: {
                          ...prev[`range_${idx}`],
                          min: e.target.value,
                        },
                      }))
                    }
                    className=" rounded-md"
                  />
                  <Input
                    placeholder="Max Range"
                    type="number"
                    value={filledFormData?.[`range_${idx}`]?.max}
                    onChange={(e) =>
                      setFilledFormData((prev) => ({
                        ...prev,
                        [`range_${idx}`]: {
                          ...prev[`range_${idx}`],
                          max: e.target.value,
                        },
                      }))
                    }
                    className="rounded-md"
                  />
                </div>
              )}

              {field?.fieldType === 'Range Threshold' && (
                <div className="flex gap-4">
                  <Input
                    placeholder="Min Value"
                    value={filledFormData?.[`threshold_${idx}`]?.min}
                    onChange={(e) =>
                      setFilledFormData((prev) => ({
                        ...prev,
                        [`threshold_${idx}`]: {
                          ...prev[`threshold_${idx}`],
                          min: e.target.value,
                        },
                      }))
                    }
                    className="rounded-md"
                  />
                  <Input
                    placeholder="Threshold Value"
                    value={filledFormData?.[`threshold_${idx}`]?.threshold}
                    onChange={(e) =>
                      setFilledFormData((prev) => ({
                        ...prev,
                        [`threshold_${idx}`]: {
                          ...prev[`threshold_${idx}`],
                          threshold: e.target.value,
                        },
                      }))
                    }
                    className="rounded-md"
                  />
                </div>
              )}

              {field?.fieldType === 'String' && (
                <RichTextDescription
                  value={getDecodedHTML(filledFormData?.[`string_${idx}`])}
                  onChange={(value) =>
                    setFilledFormData((prev) => ({
                      ...prev,
                      [`string_${idx}`]: value,
                    }))
                  }
                  className="rounded-md"
                />
              )}

              {field?.fieldType === 'MultiCheckbox' && (
                <div className="flex gap-4">
                  {field.labelArray.map((option) => (
                    <div key={option} className="flex gap-5 items-start">
                      <Input
                        type="checkbox"
                        id={option}
                        checked={
                          filledFormData?.[`multicheckbox_${idx}`]?.[option]
                        }
                        onChange={(e) =>
                          setFilledFormData((prev) => ({
                            ...prev,
                            [`multicheckbox_${idx}`]: {
                              ...prev[`multicheckbox_${idx}`],
                              [option]: e.target.checked,
                            },
                          }))
                        }
                        className="h-4 w-4 text-blue-600 rounded-sm"
                      />
                      <label htmlFor={option} className="text-gray-700 mt-2">
                        {option}
                      </label>
                    </div>
                  ))}
                </div>
              )}

              {field?.fieldType === 'Date' && (
                <Input
                  type="date"
                  value={filledFormData?.[`date_${idx}`]}
                  onChange={(e) =>
                    setFilledFormData((prev) => ({
                      ...prev,
                      [`date_${idx}`]: e.target.value,
                    }))
                  }
                  className="rounded-md"
                />
              )}

              {field?.fieldType === 'Check' && (
                <input
                  type="checkbox"
                  checked={filledFormData?.[`check_${idx}`]}
                  onChange={(e) =>
                    setFilledFormData((prev) => ({
                      ...prev,
                      [`check_${idx}`]: e.target.checked,
                    }))
                  }
                  className="!h-4 !w-4 bg-blue-600 rounded-sm"
                />
              )}

              {field?.fieldType === 'DropDown' && (
                <Select
                  options={[
                    { label: '+ Add New', value: 'newOption' },
                    ...field?.fieldOptions,
                  ]}
                  onChange={(e) => {
                    if (e.target.value === 'newOption') {
                      setFieldOptions(field?.fieldOptions);
                      setIsAddOption(() => ({
                        isAdd: true,
                        fieldName: field?.fieldName,
                      }));
                    } else {
                      setFilledFormData((prev) => ({
                        ...prev,
                        [`dropdown_${idx}`]: e.target.value,
                      }));
                    }
                  }}
                  value={filledFormData?.[`dropdown_${idx}`]}
                  className="rounded-md"
                />
              )}

              {field?.fieldType === 'MultiSelect' && (
                <MultiSelect
                  name={`multiselect_${idx}`}
                  id={`multiselect_${idx}`}
                  options={field?.fieldOptions}
                  onChange={(e) =>
                    setFilledFormData((prev) => ({
                      ...prev,
                      [`multiselect_${idx}`]: e.target.value.map(
                        (option) => option.value
                      ),
                    }))
                  }
                  value={filledFormData?.[`multiselect_${idx}`]}
                />
              )}

              {field?.fieldType === 'Media' && (
                <button
                  type="button"
                  className="px-5 py-2 mt-2 text-white bg-blue-700 rounded-lg shadow-sm hover:bg-blue-800"
                  onClick={() => {
                    setIsAddMedia(() => ({
                      isAdd: true,
                      fieldName: field?.fieldName,
                      media: filledFormData?.[field?.fieldName]?.media || [],
                    }));
                  }}
                >
                  Media
                  <span className="ml-2 px-2 py-1 bg-blue-200 rounded-full text-xs text-blue-800">
                    {filledFormData?.[field?.fieldName]?.media?.length || 0}
                  </span>
                </button>
              )}
              {field?.fieldType === 'Table' && (
                <Table.Td>
                  <div
                    className="mt-4 !px-0 flex flex-col  overflow-auto w-full"
                    key={idx}
                  >
                    <div className="w-full mt-3">
                      <div className="w-full col-span-2">
                        <div className="overflow-x-scroll pb-4 col-span-2">
                          <Table key={idx}>
                            <Table.Head>
                              <Table.Row>
                                <Table.Th className={`text-center`}>
                                  Sr. No.
                                </Table.Th>
                                {field?.tableOptions?.column?.map(
                                  (col, colIndex) => (
                                    <Table.Th
                                      key={colIndex}
                                      className={`text-center`}
                                    >
                                      {col?.columnName}
                                    </Table.Th>
                                  )
                                )}
                              </Table.Row>
                            </Table.Head>
                            <Table.Body>
                              {Array.from({
                                length:
                                  +field?.tableOptions?.rows ||
                                  0 +
                                    filledFormData?.[`table_${idx}`]
                                      ?.newRowNo ||
                                  0,
                              }).map((_, bodyRowIndex) => {
                                return (
                                  <Table.Row key={bodyRowIndex}>
                                    <Table.Td className={`text-center`}>
                                      {filledFormData?.[`table_${idx}`]
                                        ?.newRowNo ? (
                                        <>
                                          <Input
                                            className={'min-w-24 w-full'}
                                            type="text"
                                            value={
                                              filledFormData?.[`table_${idx}`]
                                                ?.row[bodyRowIndex] || ''
                                            }
                                            onChange={(e) => {
                                              setFilledFormData((prev) => ({
                                                ...prev,
                                                [`table_${idx}`]: {
                                                  ...(prev[`table_${idx}`] ||
                                                    {}),
                                                  row: prev?.[
                                                    `table_${idx}`
                                                  ]?.row?.map((itm, index) =>
                                                    index === bodyRowIndex
                                                      ? e.target.value
                                                      : itm
                                                  ),
                                                },
                                              }));
                                            }}
                                          />
                                        </>
                                      ) : (
                                        filledFormData?.[`table_${idx}`]?.row[
                                          bodyRowIndex
                                        ] || bodyRowIndex + 1
                                      )}
                                    </Table.Td>
                                    {field?.tableOptions?.column?.map(
                                      (col, colIndex) => {
                                        const accessKey = (
                                          bodyRowIndex + 1
                                        ).toString();
                                        if (
                                          col?.columnType === 'string' ||
                                          col?.columnType === 'number' ||
                                          col?.columnType === 'date'
                                        ) {
                                          return (
                                            <Table.Td
                                              key={colIndex}
                                              className={`text-center`}
                                            >
                                              <Input
                                                type={col?.columnType}
                                                onChange={(e) => {
                                                  setFilledFormData((prev) => ({
                                                    ...prev,
                                                    [`table_${idx}`]: {
                                                      ...prev[`table_${idx}`],
                                                      ['rowData']: {
                                                        ...prev[`table_${idx}`]
                                                          ?.rowData,
                                                        [accessKey]: prev[
                                                          `table_${idx}`
                                                        ]?.rowData?.[
                                                          accessKey
                                                        ]?.map((itm, index) =>
                                                          index === colIndex
                                                            ? {
                                                                ...itm,
                                                                value:
                                                                  e.target
                                                                    .value,
                                                              }
                                                            : itm
                                                        ) || [
                                                          {
                                                            value:
                                                              e.target.value,
                                                          },
                                                        ],
                                                      },
                                                    },
                                                  }));
                                                }}
                                                value={
                                                  filledFormData?.[
                                                    `table_${idx}`
                                                  ]?.rowData?.[accessKey]?.[
                                                    colIndex
                                                  ]?.value || ''
                                                }
                                                className={`min-w-24 w-full`}
                                              />
                                            </Table.Td>
                                          );
                                        } else if (
                                          col?.columnType === 'checkbox'
                                        ) {
                                          return (
                                            <Table.Td
                                              key={colIndex}
                                              className={`text-center`}
                                            >
                                              <Input
                                                type="checkbox"
                                                checked={
                                                  filledFormData?.[
                                                    `table_${idx}`
                                                  ]?.rowData?.[accessKey]?.[
                                                    colIndex
                                                  ]?.value || false
                                                }
                                                onChange={(e) => {
                                                  setFilledFormData((prev) => ({
                                                    ...prev,
                                                    [`table_${idx}`]: {
                                                      ...prev[`table_${idx}`],
                                                      ['rowData']: {
                                                        ...prev[`table_${idx}`]
                                                          ?.rowData,
                                                        [accessKey]: prev[
                                                          `table_${idx}`
                                                        ]?.rowData?.[
                                                          accessKey
                                                        ]?.map((itm, index) =>
                                                          index === colIndex
                                                            ? {
                                                                ...itm,
                                                                value:
                                                                  e.target
                                                                    .checked,
                                                              }
                                                            : itm
                                                        ) || [
                                                          {
                                                            value:
                                                              e.target.checked,
                                                          },
                                                        ],
                                                      },
                                                    },
                                                  }));
                                                }}
                                              />
                                            </Table.Td>
                                          );
                                        } else {
                                          return (
                                            <Table.Td key={colIndex}>
                                              <span>Unknown Type</span>
                                            </Table.Td>
                                          );
                                        }
                                      }
                                    )}
                                  </Table.Row>
                                );
                              })}
                            </Table.Body>
                          </Table>
                          <p
                            className={`mt-2 ml-5 ${field?.tableOptions?.rows > 0 ? 'hidden' : ''} text-blue-700 hover:text-blue-500`}
                            onClick={() => {
                              let newRowNo =
                                +filledFormData?.[`table_${idx}`]?.newRowNo ||
                                0;
                              setFilledFormData((prev) => ({
                                ...prev,
                                [`table_${idx}`]: {
                                  ...prev[`table_${idx}`],
                                  row: [
                                    ...prev?.[`table_${idx}`]?.row,
                                    newRowNo + 1,
                                  ],
                                  newRowNo: newRowNo + 1,
                                  ['rowData']: {
                                    ...prev[`table_${idx}`]?.rowData,
                                    [newRowNo + 1]:
                                      field?.tableOptions?.column?.map(
                                        (item) => ({
                                          value: '',
                                          type: item?.columnType,
                                        })
                                      ),
                                  },
                                },
                              }));
                            }}
                          >
                            + Add Row
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </Table.Td>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default DepartmentForm;
