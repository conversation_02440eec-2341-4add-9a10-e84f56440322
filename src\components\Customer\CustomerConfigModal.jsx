import { But<PERSON>, Checkbox, Modal } from 'antd';
import { ChevronDown, ChevronUp, Eye, EyeOff } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import {
  useCreateCustomerConfigMutation,
  useGetCustomerConfigQuery,
  useUpdateCustomerConfigMutation,
} from '../../slices/customerConfigApiSlice';
import { useGetAllcustomerQuery } from '../../slices/customerDataSlice';
import { useGetFormatsQuery } from '../../slices/productFormatsApiSlice';
import MultiSelect from '../global/components/MultiSelect';

const CustomerConfigModal = ({ openModal, setOpenModal }) => {
  const { data: formatsData, isLoading: isFormatsLoading } =
    useGetFormatsQuery();
  const { data: customersData, isLoading: isCustomersLoading } =
    useGetAllcustomerQuery();
  const { data: configData } = useGetCustomerConfigQuery();
  const [config, setConfig] = useState([]);
  const [createConfig, { isLoading: isCreating }] =
    useCreateCustomerConfigMutation();
  const [updateConfig, { isLoading: isUpdating }] =
    useUpdateCustomerConfigMutation();
  const [expandedFormats, setExpandedFormats] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  const formats = formatsData?.productTablesFormats;
  const customers = customersData?.customers;

  useEffect(() => {
    if (formats && formats.length > 0) {
      if (configData && Array.isArray(configData?.formats)) {
        // Pre-fill with existing configData
        const prefilledConfig = configData.formats.map((format) => ({
          formatId: format.formatId,
          formatName: format.formatName,
          customers: format.customers || [], // Ensure customers array exists
          visible: format.visible !== undefined ? format.visible : true,
          fields: format.fields.map((field) => ({
            fieldId: field.fieldId,
            fieldName: field.fieldName,
            fieldType: field.fieldType,
            isDefault: field.isDefault !== undefined ? field.isDefault : false,
            formatting: {
              isItPrice: field.formatting?.isItPrice || false,
              isItPercentage: field.formatting?.isItPercentage || false,
            },
            visible: field.visible !== undefined ? field.visible : true,
          })),
          charges: format.charges.map((charge) => ({
            chargeId: charge.chargeId,
            chargeName: charge.chargeName,
            chargeType: charge.chargeType,
            isDefault:
              charge.isDefault !== undefined ? charge.isDefault : false,
            formatting: {
              isItPrice: charge.formatting?.isItPrice || false,
              isItPercentage: charge.formatting?.isItPercentage || false,
            },
            visible: charge.visible !== undefined ? charge.visible : true,
          })),
          totalFields: format.totalFields.map((total) => ({
            fieldId: total.fieldId,
            fieldName: total.fieldName,
            columnToTotal: total.columnToTotal,
            visible: total.visible !== undefined ? total.visible : true,
          })),
        }));
        setConfig(prefilledConfig);
      } else {
        // Initialize default config
        const initialConfig = formats.map((format) => ({
          formatId: format._id,
          formatName: format.formatName,
          customers: [],
          visible: true,
          fields: format.fields.map((field) => ({
            fieldId: field.fieldId,
            fieldName: field.fieldName,
            fieldType: field.fieldType,
            isDefault: field.isDefault,
            formatting: field.formatting,
            visible: true,
          })),
          charges: format.charges.map((charge) => ({
            chargeId: charge.chargeId,
            chargeName: charge.chargeName,
            chargeType: charge.chargeType,
            isDefault: charge.isDefault,
            formatting: charge.formatting,
            visible: true,
          })),
          totalFields: format.totalFields.map((total) => ({
            fieldId: total.fieldId,
            fieldName: total.fieldName,
            columnToTotal: total.columnToTotal,
            visible: true,
          })),
        }));
        setConfig(initialConfig);
      }
    }
  }, [formats, configData]);

  const toggleFormatVisibility = (formatId) => {
    setConfig((prev) =>
      prev.map((format) =>
        format.formatId === formatId
          ? { ...format, visible: !format.visible }
          : format
      )
    );
    setHasChanges(true);
  };

  const toggleFieldVisibility = (formatId, fieldId, type = 'fields') => {
    setConfig((prev) =>
      prev.map((format) =>
        format.formatId === formatId
          ? {
              ...format,
              [type]: format[type].map((item) =>
                item[`${type === 'charges' ? 'chargeId' : 'fieldId'}`] ===
                fieldId
                  ? { ...item, visible: !item.visible }
                  : item
              ),
            }
          : format
      )
    );
    setHasChanges(true);
  };

  const toggleFormatExpansion = (formatId) => {
    setExpandedFormats((prev) => ({
      ...prev,
      [formatId]: !prev[formatId],
    }));
  };

  const selectAllFields = (formatId, type = 'fields', visible = true) => {
    setConfig((prev) =>
      prev.map((format) =>
        format.formatId === formatId
          ? {
              ...format,
              [type]: format[type].map((item) => ({
                ...item,
                visible,
              })),
            }
          : format
      )
    );
    setHasChanges(true);
  };

  const handleCustomerChange = (formatId, values) => {
    const selectedCustomerIds = values.map((customer) => customer.value);
    setConfig((prev) =>
      prev.map((format) =>
        format.formatId === formatId
          ? { ...format, customers: selectedCustomerIds }
          : format
      )
    );
    setHasChanges(true);
  };

  const handleSaveConfig = async () => {
    const hasCustomers = config.some((format) => format.customers.length > 0);
    if (!hasCustomers) {
      toast.error(
        'Please select at least one customer for at least one format.'
      );
      return;
    }

    try {
      if (!hasChanges) {
        toast.warn('No changes to save.');
        return;
      }
      if (configData && configData.formats && configData.formats.length > 0) {
        const data = {
          formats: config,
          id: configData._id,
        };
        await updateConfig({ data });
        setHasChanges(false);
        setOpenModal(false);
        toast.success('Configuration saved successfully.');
      } else {
        const payload = {
          formats: config,
        };
        await createConfig(payload);
        setHasChanges(false);
        setOpenModal(false);
        toast.success('Configuration saved successfully.');
      }
    } catch (error) {
      toast.error('Failed to save configuration.');
    }
  };

  const getFieldTypeIcon = (fieldType) => {
    switch (fieldType) {
      case 'text':
        return '📝';
      case 'number':
        return '🔢';
      case 'formula':
        return '📊';
      case 'taxDropdown':
        return '💰';
      default:
        return '⚙️';
    }
  };

  const getVisibleFieldsCount = (formatId, type = 'fields') => {
    const format = config.find((f) => f.formatId === formatId);
    return format ? format[type].filter((field) => field.visible).length : 0;
  };

  const getTotalFieldsCount = (formatId, type = 'fields') => {
    const format = config.find((f) => f.formatId === formatId);
    return format ? format[type].length : 0;
  };

  return (
    <Modal
      open={openModal}
      onCancel={() => setOpenModal(false)}
      footer={null}
      centered
      width={1000}
      title="Configure the product format for customers"
      loading={isFormatsLoading || isCustomersLoading}
      styles={{
        body: {
          maxHeight: 'calc(100vh - 200px)',
          overflowY: 'auto',
        },
      }}
    >
      <div className="p-4">
        <div className="space-y-4">
          {formats?.map((format) => {
            const configFormat = config.find((f) => f.formatId === format._id);
            return (
              <div
                key={format._id}
                className={`border rounded-lg transition-all duration-200 ${
                  configFormat?.visible
                    ? 'border-blue-200 bg-blue-50'
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={configFormat?.visible}
                        onChange={() => toggleFormatVisibility(format._id)}
                        className={`p-1 rounded transition-colors ${
                          configFormat?.visible
                            ? 'text-blue-600 hover:bg-blue-100'
                            : 'text-gray-400 hover:bg-gray-100'
                        }`}
                      />
                      <h3 className="text-lg font-semibold text-gray-900">
                        {format.formatName}
                      </h3>
                      <span className="text-sm text-gray-500">
                        ({format.fields.length} fields, {format.charges.length}{' '}
                        charges)
                      </span>
                    </div>
                    <button
                      onClick={() => toggleFormatExpansion(format._id)}
                      className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {expandedFormats[format._id] ? (
                        <ChevronUp className="w-5 h-5" />
                      ) : (
                        <ChevronDown className="w-5 h-5" />
                      )}
                    </button>
                  </div>
                  {expandedFormats[format._id] && (
                    <div className="mt-4 space-y-6">
                      {/* Customers Section */}
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Select Customers for {format.formatName}
                        </label>
                        <MultiSelect
                          placeholder="Select customers"
                          trimmedOptionLength={50}
                          value={configFormat?.customers || []}
                          onChange={(e) =>
                            handleCustomerChange(format._id, e.target.value)
                          }
                          options={customers?.map((customer) => ({
                            value: customer._id,
                            label: `${customer.name || customer.email} (${customer?.company_name})`,
                          }))}
                        />
                      </div>

                      {/* Fields Section */}
                      <div>
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="text-md font-medium text-gray-800">
                            Fields (
                            {getVisibleFieldsCount(format._id, 'fields')}/
                            {getTotalFieldsCount(format._id, 'fields')} visible)
                          </h4>
                          <div className="flex gap-2">
                            <button
                              onClick={() =>
                                selectAllFields(format._id, 'fields', true)
                              }
                              className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                            >
                              Select All
                            </button>
                            <button
                              onClick={() =>
                                selectAllFields(format._id, 'fields', false)
                              }
                              className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                            >
                              Deselect All
                            </button>
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {format.fields.map((field) => {
                            const configField = configFormat?.fields.find(
                              (f) => f.fieldId === field.fieldId
                            );
                            return (
                              <div
                                key={field.fieldId}
                                className={`p-3 rounded-lg border transition-all ${
                                  configField?.visible
                                    ? 'border-green-200 bg-green-50'
                                    : 'border-gray-200 bg-gray-50'
                                }`}
                              >
                                <div className="flex items-center gap-2">
                                  <button
                                    onClick={() =>
                                      toggleFieldVisibility(
                                        format._id,
                                        field.fieldId,
                                        'fields'
                                      )
                                    }
                                    className={`p-1 rounded transition-colors ${
                                      configField?.visible
                                        ? 'text-green-600 hover:bg-green-100'
                                        : 'text-gray-400 hover:bg-gray-100'
                                    }`}
                                  >
                                    {configField?.visible ? (
                                      <Eye className="w-4 h-4" />
                                    ) : (
                                      <EyeOff className="w-4 h-4" />
                                    )}
                                  </button>
                                  <span className="text-sm">
                                    {getFieldTypeIcon(field.fieldType)}
                                  </span>
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                      {field.fieldName}
                                    </p>
                                    <p className="text-xs text-gray-500 capitalize">
                                      {field.fieldType}
                                      {field.isDefault && ' • Default'}
                                      {field.formatting?.isItPrice &&
                                        ' • Price'}
                                      {field.formatting?.isItPercentage &&
                                        ' • Percentage'}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {hasChanges && (
          <div className="flex justify-end mt-4">
            <Button
              type="primary"
              onClick={handleSaveConfig}
              loading={configData ? isUpdating : isCreating}
            >
              {configData ? 'Update' : 'Create'} Configuration
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default CustomerConfigModal;
