import { useEffect, useState } from 'react';

import Input from '../global/components/Input';
import Table from '../global/components/Table';

const FillForm = ({ activeFormDetails, setSelectedForms }) => {
  const [formData, setFormData] = useState({});

  useEffect(() => {
    if (activeFormDetails) {
      let obj = {};
      for (let i of activeFormDetails?.formData) {
        obj = {
          ...obj,
          [i?.fieldName]: i,
        };
      }
      setFormData(obj);
    }
  }, [activeFormDetails]);

  const renderField = (val, name, idx, fieldOptions) => {
    let id = activeFormDetails?._id;
    switch (val) {
      case 'Date':
        return (
          <Input
            type="date"
            value={formData?.[name]?.fieldValue}
            onChange={(e) => {
              setFormData((prev) => ({
                ...prev,
                [name]: {
                  ...prev?.[name],
                  fieldValue: e.target.value,
                },
              }));
              setSelectedForms((prev) =>
                prev?.map((elem) => {
                  if (elem?._id === id) {
                    return {
                      ...elem,
                      formData: [
                        ...elem?.formData?.slice(0, idx),
                        {
                          ...elem?.formData?.[idx],
                          fieldValue: e.target.value,
                        },
                        ...elem?.formData?.slice(idx + 1),
                      ],
                    };
                  } else {
                    return elem;
                  }
                })
              );
            }}
          />
        );
      case 'Range':
        return (
          <div className="flex items-center gap-2">
            <Input
              type="number"
              value={formData?.[name]?.fieldValue?.min}
              placeholder="Minimum Value"
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  [name]: {
                    ...prev?.[name],
                    fieldValue: {
                      ...prev?.[name]?.fieldValue,
                      min: e.target.value,
                    },
                  },
                }));
                setSelectedForms((prev) =>
                  prev?.map((elem) => {
                    if (elem?._id === id) {
                      return {
                        ...elem,
                        formData: [
                          ...elem?.formData?.slice(0, idx),
                          {
                            ...elem?.formData?.[idx],
                            fieldValue: {
                              ...elem?.formData?.[idx]?.fieldValue,
                              min: e.target.value,
                            },
                          },
                          ...elem?.formData?.slice(idx + 1),
                        ],
                      };
                    } else {
                      return elem;
                    }
                  })
                );
              }}
            />
            <Input
              type="number"
              value={formData?.[name]?.fieldValue?.max}
              placeholder="Maximum Value"
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  [name]: {
                    ...prev?.[name],
                    fieldValue: {
                      ...prev?.[name]?.fieldValue,
                      max: e.target.value,
                    },
                  },
                }));
                setSelectedForms((prev) =>
                  prev?.map((elem) => {
                    if (elem?._id === id) {
                      return {
                        ...elem,
                        formData: [
                          ...elem?.formData?.slice(0, idx),
                          {
                            ...elem?.formData?.[idx],
                            fieldValue: {
                              ...elem?.formData?.[idx]?.fieldValue,
                              max: e.target.value,
                            },
                          },
                          ...elem?.formData?.slice(idx + 1),
                        ],
                      };
                    } else {
                      return elem;
                    }
                  })
                );
              }}
            />
          </div>
        );
      case 'Range Threshold':
        return (
          <div className="flex items-center gap-2">
            <Input
              type="number"
              placeholder="Minimum Value"
              value={formData?.[name]?.fieldValue?.min}
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  [name]: {
                    ...prev?.[name],
                    fieldValue: {
                      ...prev?.[name]?.fieldValue,
                      min: e.target.value,
                    },
                  },
                }));
                setSelectedForms((prev) =>
                  prev?.map((elem) => {
                    if (elem?._id === id) {
                      return {
                        ...elem,
                        formData: [
                          ...elem?.formData?.slice(0, idx),
                          {
                            ...elem?.formData?.[idx],
                            fieldValue: {
                              ...elem?.formData?.[idx]?.fieldValue,
                              min: e.target.value,
                            },
                          },
                          ...elem?.formData?.slice(idx + 1),
                        ],
                      };
                    } else {
                      return elem;
                    }
                  })
                );
              }}
            />
            <Input
              type="number"
              placeholder="Threshold Value"
              value={formData?.[name]?.fieldValue?.threshold}
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  [name]: {
                    ...prev?.[name],
                    fieldValue: {
                      ...prev?.[name]?.fieldValue,
                      threshold: e.target.value,
                    },
                  },
                }));
                setSelectedForms((prev) =>
                  prev?.map((elem) => {
                    if (elem?._id === id) {
                      return {
                        ...elem,
                        formData: [
                          ...elem?.formData?.slice(0, idx),
                          {
                            ...elem?.formData?.[idx],
                            fieldValue: {
                              ...elem?.formData?.[idx]?.fieldValue,
                              threshold: e.target.value,
                            },
                          },
                          ...elem?.formData?.slice(idx + 1),
                        ],
                      };
                    } else {
                      return elem;
                    }
                  })
                );
              }}
            />
          </div>
        );
      case 'Check':
        return (
          <Input
            className="w-[1.3rem] mr-auto"
            type="checkbox"
            checked={formData?.[name]?.fieldValue}
            onChange={(e) => {
              setFormData((prev) => ({
                ...prev,
                [name]: {
                  ...prev?.[name],
                  fieldValue: e.target.value,
                },
              }));
              setSelectedForms((prev) =>
                prev?.map((elem) => {
                  if (elem?._id === id) {
                    return {
                      ...elem,
                      formData: [
                        ...elem?.formData?.slice(0, idx),
                        {
                          ...elem?.formData?.[idx],
                          fieldValue: e.target.checked,
                        },
                        ...elem?.formData?.slice(idx + 1),
                      ],
                    };
                  } else {
                    return elem;
                  }
                })
              );
            }}
          />
        );
      case 'MultiCheckbox':
        return (
          <>
            {fieldOptions?.map((option, idx) => {
              return (
                <div className="flex items-center gap-2" key={idx}>
                  <Input
                    className="w-[1.3rem]"
                    type="checkbox"
                    checked={formData?.[name]?.fieldValue?.includes(
                      option?.value
                    )}
                    onChange={(e) => {
                      setFormData((prev) => ({
                        ...prev,
                        [name]: {
                          ...prev?.[name],
                          fieldValue: e.target.checked
                            ? [
                                ...(prev?.[name]?.fieldValue || []),
                                option?.value,
                              ]
                            : prev?.[name]?.fieldValue?.filter(
                                (item) => item !== item?.value
                              ),
                        },
                      }));
                      setSelectedForms((prev) =>
                        prev?.map((elem) => {
                          if (elem?._id === id) {
                            return {
                              ...elem,
                              formData: [
                                ...elem?.formData?.slice(0, idx),
                                {
                                  ...elem?.formData?.[idx],
                                  fieldValue: e.target.checked
                                    ? [
                                        ...(prev?.[name]?.fieldValue || []),
                                        option?.value,
                                      ]
                                    : prev?.[name]?.fieldValue?.filter(
                                        (item) => item !== item?.value
                                      ),
                                },
                                ...elem?.formData?.slice(idx + 1),
                              ],
                            };
                          } else {
                            return elem;
                          }
                        })
                      );
                    }}
                  />
                  <p>{option?.label}</p>
                </div>
              );
            })}
          </>
        );
      case 'String':
        return (
          <Input
            type="text"
            placeholder="Enter Value"
            value={formData?.[name]?.fieldValue}
            onChange={(e) => {
              setFormData((prev) => ({
                ...prev,
                [name]: {
                  ...prev?.[name],
                  fieldValue: e.target.value,
                },
              }));
              setSelectedForms((prev) =>
                prev?.map((elem) => {
                  if (elem?._id === id) {
                    return {
                      ...elem,
                      formData: [
                        ...elem?.formData?.slice(0, idx),
                        {
                          ...elem?.formData?.[idx],
                          fieldValue: e.target.value,
                        },
                        ...elem?.formData?.slice(idx + 1),
                      ],
                    };
                  } else {
                    return elem;
                  }
                })
              );
            }}
          />
        );
    }
  };

  return (
    <>
      {activeFormDetails?.formData?.map((elem, index) => {
        return (
          <Table.Row key={index}>
            <Table.Td>{elem?.fieldName}</Table.Td>
            <Table.Td>
              {renderField(
                elem?.fieldType,
                elem?.fieldName,
                index,
                elem?.fieldOptions
              )}
            </Table.Td>
          </Table.Row>
        );
      })}
    </>
  );
};

export default FillForm;
