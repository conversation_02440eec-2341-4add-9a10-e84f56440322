import { AnimatePresence, motion } from 'framer-motion';
import PropTypes from 'prop-types';
import { useState } from 'react';

const CircleColorPicker = ({
  value = '',
  onChange,
  circleSize = 32,
  colors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FECA57',
    '#FF9FF3',
    '#54A0FF',
    '#5F27CD',
    '#00D2D3',
    '#FF9F43',
    '#FC427B',
    '#26DE81',
    '#2D3561',
    '#FD79A8',
    '#6C5CE7',
  ],
}) => {
  const [hoveredColor, setHoveredColor] = useState(null);

  return (
    <div
      className="grid gap-3"
      style={{
        gridTemplateColumns: `repeat(5, minmax(${circleSize + 8}px, 1fr))`,
        alignItems: 'center',
        justifyItems: 'center',
        width: '100%',
      }}
    >
      <AnimatePresence>
        {colors.map((color, index) => (
          <motion.div
            key={color}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{
              delay: index * 0.05,
              type: 'spring',
              stiffness: 300,
              damping: 20,
            }}
            className="relative"
          >
            <motion.button
              type="button"
              onClick={() => onChange && onChange(color)}
              onHoverStart={() => setHoveredColor(color)}
              onHoverEnd={() => setHoveredColor(null)}
              whileHover={{
                scale: 1.2,
                rotate: [0, -5, 5, 0],
                transition: { duration: 0.3 },
              }}
              whileTap={{
                scale: 0.9,
                transition: { duration: 0.1 },
              }}
              style={{
                width: circleSize,
                height: circleSize,
                background: `linear-gradient(135deg, ${color}, ${color}dd)`,
              }}
              className={`
                relative rounded-full border-2 outline-none cursor-pointer
                shadow-md hover:shadow-lg transition-shadow duration-200
                ${
                  value === color
                    ? 'border-blue-500 ring-4 ring-blue-200'
                    : 'border-white hover:border-gray-200'
                }
              `}
              aria-label={`Pick color ${color}`}
            >
              <AnimatePresence>
                {value === color && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <div className="w-2 h-2 bg-white rounded-full shadow-sm" />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Hover ripple effect */}
              <AnimatePresence>
                {hoveredColor === color && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0.6 }}
                    animate={{ scale: 2, opacity: 0 }}
                    exit={{ scale: 0, opacity: 0 }}
                    transition={{ duration: 0.6, ease: 'easeOut' }}
                    className="absolute inset-0 rounded-full border-2 border-current"
                    style={{ borderColor: color }}
                  />
                )}
              </AnimatePresence>
            </motion.button>

            {/* Color name tooltip */}
            <AnimatePresence>
              {hoveredColor === color && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.8 }}
                  animate={{ opacity: 1, y: -8, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.8 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 25 }}
                  className="absolute left-1/2 transform -translate-x-1/2 -top-8 
                           bg-gray-800 text-white text-xs px-2 py-1 rounded-md
                           shadow-lg pointer-events-none z-10"
                >
                  {color.toUpperCase()}
                  <div
                    className="absolute top-full left-1/2 transform -translate-x-1/2 
                                w-0 h-0 border-l-2 border-r-2 border-t-2 
                                border-transparent border-t-gray-800"
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

CircleColorPicker.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  circleSize: PropTypes.number,
  colors: PropTypes.arrayOf(PropTypes.string),
};

export default CircleColorPicker;
