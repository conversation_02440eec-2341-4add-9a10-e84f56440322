import { Button, Modal } from 'antd';
import { useContext, useEffect, useState } from 'react';
import { FiPlusCircle } from 'react-icons/fi';
import { toast } from 'react-toastify';
import { useUpdateDefaultsMutation } from '../../../slices/defaultsApiSlice';
import { Store } from '../../../store/Store';
import { customConfirm } from '../../../utils/customConfirm';
import IdFormat from './IdFormat';

const generalPrefixIds = [
  'workOrderId',
  'assemblyBomId',
  'jobId',
  'modelName',
  'inputScreen',
  'batchId',
  'qr',
  'itemsPerHour',
  'changeOverTime',
  'taskId',
  'salesInquiryId',
  'salesOrderId',
  'quotationId',
  'poId',
  'customerId',
  'outsourceId',
  'ncrId',
  'leadId',
];

const inventoryPrefixIds = [
  'vendorId',
  'storeId',
  'partsId',
  'productId',
  'saId',
  'chalanId',
  'inventoryBatchNo',
  'lotNo',
];

const defaultFormats = {
  workOrderId: [{ Increment_0: 1 }],
  assemblyBomId: { Increment_0: 1 },
  jobId: { WorkOrderId_0: '', Increment_1: 1 },
  modelName: { WorkOrderId_0: '', UserEntry_1: '' },
  inputScreen: { Increment_0: 1 },
  batchId: { Increment_1: 1 },
  storeId: { Increment_0: 1 },
  vendorId: { Increment_0: 1 },
  partsId: { Increment_0: 1 },
  productId: { Increment_0: 1 },
  saId: { Increment_0: 1 },
  chalanId: [{ Increment_0: 1 }],
  inventoryBatchNo: { Increment_0: 1 },
  lotNo: { Increment_0: '' },
  qr: {},
  itemsPerHour: { String_0: 'iph' },
  changeOverTime: { String_0: 'cot' },
  poId: [{ Increment_0: 1 }],
  taskId: { Increment_0: 1 },
  salesOrderId: [{ Increment_0: 1 }],
  salesInquiryId: [{ Increment_0: 1 }],
  quotationId: [{ Increment_0: 1 }],
  customerId: [{ Increment_0: 1 }],
  outsourceId: { Increment_0: 1 },
  ncrId: [{ Increment_0: 1 }],
  leadId: [{ Increment_0: 1 }],
};
const idTypeNames = {
  workOrderId: 'Work Order Id',
  poId: 'Purchase Order Id',
  salesOrderId: 'Sales Order Id',
  salesInquiryId: 'Sales Inquiry Id',
  quotationId: 'Quotation Id',
  customerId: 'Customer Id',
  ncrId: 'NCR Id',
  chalanId: 'Chalan Id',
  inventoryBatchNo: 'Inventory Batch No',
  lotNo: 'Lot No',
  taskId: 'Task Id',
  assemblyBomId: 'Assembly Bom Id',
  jobId: 'Job Id',
  modelName: 'Model Name',
  inputScreen: 'Input Screen',
  batchId: 'Batch Id',
  storeId: 'Store Id',
  vendorId: 'Vendor Id',
  partsId: 'Parts Id',
  productId: 'Product Id',
  saId: 'SubAssembly ID:',
  qr: 'QR',
  itemsPerHour: 'Items Per Hour',
  changeOverTime: 'Change Over Time',
  outsourceId: 'Outsource Id',
  leadId: 'Lead Id',
};
const PrefixModal = ({ openModal, setOpenModal, isTablet, isMobile }) => {
  const [format, setFormat] = useState(defaultFormats);
  const { defaults: { defaultParam } = {}, setDefaultsRefresh } =
    useContext(Store);
  const [defaults, setDefaults] = useState(defaultParam);
  const [activeTab, setActiveTab] = useState('general');
  const [updateDefaults, updateData] = useUpdateDefaultsMutation();
  const { isLoading } = updateData || {};
  useEffect(() => {
    initializeWithDefaultFormat();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setDefaults((prev) => ({ ...prev, prefixIds: format }));
  }, [format]);

  const initializeWithDefaultFormat = () => {
    setFormat((prevFormat) => {
      const newFormat = { ...prevFormat };
      Object.keys(defaultFormats).forEach((key) => {
        if (
          !defaults?.prefixIds?.[key] ||
          Object.keys(defaults.prefixIds[key]).length === 0
        ) {
          newFormat[key] = defaultFormats[key];
        } else {
          newFormat[key] = defaults.prefixIds[key];
        }
      });
      return newFormat;
    });
  };

  const addIdTemplate = (idType) => {
    setFormat((prev) => ({
      ...prev,
      [idType]: Array.isArray(prev[idType])
        ? [...(prev[idType] || []), {}]
        : [prev[idType], {}],
    }));
  };

  const validatePrefixId = () => {
    return !Object.entries(defaults?.prefixIds || {}).some(
      ([_, value]) =>
        (Array.isArray(value) && value.some((id) => id.isUsed)) ||
        (!Array.isArray(value) && value.isUsed)
    );
  };
  const validatePrefixIds = async () => {
    let errorId = '';
    for (const prefixId of Object.keys(defaults?.prefixIds)) {
      if (errorId !== '') break;
      else {
        let check = 0;
        if (
          prefixId === 'qr' ||
          prefixId === 'itemsPerHour' ||
          prefixId === 'changeOverTime' ||
          prefixId === 'poId'
        )
          check++;
        for (const format of Object.keys(defaults?.prefixIds?.[prefixId])) {
          if (format.includes('UserEntry') || format?.includes('Increment')) {
            check = check + 1;
          }
        }

        if (check === 0) {
          errorId = prefixId;
          break;
        }
      }
    }
    if (errorId !== '') {
      return true;
    } else {
      return true;
    }
  };
  const saveHandler = async () => {
    validatePrefixIds().then(async (status) => {
      if (status === true) {
        const res = await updateDefaults(defaults).unwrap();
        if (res) {
          toast.success('Saved Successfully', {
            theme: 'colored',
            position: 'top-right',
            toastId: 'Successfully Updated',
          });
          setDefaultsRefresh();
          setOpenModal(false);
        }
      } else {
        toast.error(
          `${status} must contain either a UserEntry or an Increment field`,
          {
            theme: 'colored',
            position: 'top-right',
            toastId: 'Successfully Updated',
          }
        );
      }
    });
  };
  const resetAllPrefixes = async () => {
    if (!validatePrefixId()) {
      toast.error('Cannot reset Prefix ID that has been used in a template');
      return;
    }

    const confirmation = await customConfirm(
      `Are you sure you want to reset the prefix data of ${activeTab} tab?`,
      'delete'
    );

    if (confirmation) {
      const idsToReset =
        activeTab === 'general' ? generalPrefixIds : inventoryPrefixIds;
      setFormat((prev) => {
        const newFormat = { ...prev };
        idsToReset.forEach((id) => {
          newFormat[id] = Array.isArray(defaultFormats[id]) ? [] : {};
        });
        return newFormat;
      });
    }
  };

  const allowedPrefixIds = [
    'workOrderId',
    'poId',
    'salesOrderId',
    'salesInquiryId',
    'quotationId',
    'customerId',
    'ncrId',
    'chalanId',
    'leadId',
  ];

  const renderPrefixItems = () => {
    const prefixIds =
      activeTab === 'general' ? generalPrefixIds : inventoryPrefixIds;

    return prefixIds.map((key) => (
      <div key={key} className="bg-transparent p-2 rounded-lg">
        <div className="flex justify-between items-center mb-4">
          <div className="flex flex-col">
            <div className="flex gap-2">
              <h3 className="text-base  text-gray-700">
                {idTypeNames[key] || key}
              </h3>
              {allowedPrefixIds.includes(key) && (
                <button
                  onClick={() => addIdTemplate(key)}
                  className="text-green-600"
                >
                  <FiPlusCircle className="inline-block" />
                </button>
              )}
            </div>
            <IdFormat
              idType={key}
              format={format}
              setFormat={setFormat}
              defaults={defaults}
            />
          </div>
        </div>
      </div>
    ));
  };

  return (
    <Modal
      isMobile={isMobile}
      isTablet={isTablet}
      open={openModal}
      onCancel={() => setOpenModal(false)}
      centered
      footer={[
        <Button key="reset" danger onClick={resetAllPrefixes}>
          Reset All
        </Button>,
        <Button key="save" onClick={saveHandler} isLoading={isLoading}>
          save
        </Button>,
      ]}
      // width="40vw"
    >
      <div>
        <h1 className="text-xl font-semibold text-gray-500">
          Manage Prefix Ids
        </h1>
        <p className="text-sm text-gray-400 mb-4">
          Prefix Ids are used to generate unique ids
        </p>
        <div className="w-full flex justify-between bg-gray-50 mb-4">
          <div className="flex">
            <Button
              type={activeTab === 'general' ? 'primary' : 'default'}
              onClick={() => setActiveTab('general')}
            >
              General
            </Button>
            <Button
              type={activeTab === 'inventory' ? 'primary' : 'default'}
              onClick={() => setActiveTab('inventory')}
            >
              Inventory
            </Button>
          </div>
        </div>

        <div className="max-h-[65vh] overflow-y-auto">
          {renderPrefixItems()}
        </div>
      </div>
    </Modal>
  );
};

export default PrefixModal;
