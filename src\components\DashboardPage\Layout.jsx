import { useEffect, useState } from 'react';
import GridLayout from './DeviceTypeLayouts/GridLayout';
import NormalLayout from './DeviceTypeLayouts/NormalLayout';
import ColoredCircle from '../ActiveStatus/ActiveStatus';

const TypeLayout = ({ device, values, colSpan }) => {
  switch (device?.deviceType?.isGrid) {
    case false:
      return <NormalLayout device={device} values={values} colSpan={colSpan} />;
    case true:
      return <GridLayout device={device} values={values} />;
    default:
      return <p className="text-center">Please Select the type of device.</p>;
  }
};

const Layout = ({ device, values, bgColor = 'bg-gray-secondary' }) => {
  const [allStatusFields, setAllStatusFields] = useState([]);
  const [valuesStatusFields, setValuesStatusFields] = useState([]);
  const [deviceStatus, setDeviceStatus] = useState('inactive');

  useEffect(() => {
    // checks necessary parameters for setting device status

    setValuesStatusFields([]);
    setAllStatusFields(
      device.deviceType.fields.filter((field) => field.name === 'Device Status')
    );

    const layoutCount = device.deviceType.layoutCount || 1;

    device.assignedDevice.forEach((assiDev) => {
      if (values?.[assiDev.deviceId]) {
        [...Array(layoutCount)].forEach((layout, lIdx) => {
          setValuesStatusFields((prev) => [
            ...prev,
            values?.[assiDev.deviceId]?.[`LAYOUT${lIdx + 1}`]?.STATUS,
          ]);
        });
      }
    });
  }, [device, values]);

  useEffect(() => {
    // sets device status

    if (
      allStatusFields.length === valuesStatusFields.length &&
      valuesStatusFields.every((val) => val === '1') &&
      valuesStatusFields.length > 0
    ) {
      setDeviceStatus('active');
    } else {
      setDeviceStatus('inactive');
    }
  }, [allStatusFields, valuesStatusFields]);

  const colSpan =
    device?.nosOfFields < 3
      ? 'col-span-1'
      : device?.nosOfFields < 5
      ? 'col-span-2'
      : device?.nosOfFields < 7
      ? 'col-span-3'
      : 'col-span-4';

  return (
    <>
      {device.deviceType && device.deviceType.type ? (
        <div
          className={`w-full h-fit mx-auto rounded-new py-2 ${bgColor} ${
            colSpan || ''
          }`}
        >
          <p className="px-2 text-[0.94rem] font-medium w-full flex items-center gap-x-2">
            {device?.deviceId}
            <ColoredCircle status={deviceStatus?.status} />
          </p>

          {/* Switch function for grid and normal layout */}

          <TypeLayout device={device} values={values} colSpan={colSpan} />

          {/* <p className="text-[12px] text-center">
						(
						{device.assignedDevice && device.assignedDevice.length > 0
							? device.assignedDevice.map(
									(div, dIdx) =>
										`${div.deviceId}${
											dIdx + 1 === device.assignedDevice.length ? '' : '/'
										}`
							  )
							: device.deviceType.type}
						)
					</p> */}
        </div>
      ) : (
        <p className="text-center">Please Select the type of device.</p>
      )}
    </>
  );
};

export default Layout;
