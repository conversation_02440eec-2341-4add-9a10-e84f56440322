import {
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  Title,
  Tooltip,
} from 'chart.js';
import { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import { getTimeDifference } from '../../helperFunction';
import { useLazyGetAllMachineDataQuery } from '../../slices/createAnalyticsApiSlice';
import { useLazyGetCreateInputByIDQuery } from '../../slices/createInputApiSlice';
import { useGetAllMachinesQuery } from '../../slices/machineApiSlice';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const CreateAnaHorizontal = ({ params, pid }) => {
  const [error, setError] = useState([]);
  const [macData, setMacData] = useState([]);

  const [getCreateInputByID] = useLazyGetCreateInputByIDQuery();

  const [getAllMachineData] = useLazyGetAllMachineDataQuery();

  const { data: machData = {} } = useGetAllMachinesQuery();
  const { machines: allMacId = [] } = machData;

  const machineLabel = [];
  const machineData = [];

  const errorLabel = [];
  const errorData = [];

  const uptimeLabelW = [];
  const uptimeDataW = [];

  const uptimeLabelM = [];
  const uptimeDataM = [];

  const downtimeDataM = [];

  // id is passed to uniquely give data for that project

  let id = pid;

  useEffect(() => {
    const fetchData = async () => {
      try {
        const allerror = await getCreateInputByID({ id: id }, false).unwrap();
        setError(allerror.cuProjects);
      } catch (error) {
        console.error('Error fetching data:', error); // eslint-disable-line
      }
    };
    if (id) {
      fetchData();
    }
  }, [id, getCreateInputByID]);

  useEffect(() => {
    let ids = [];
    allMacId.forEach((itm) => {
      ids.push(itm._id);
    });
    const data = ids.join(',');

    const fetchData = async () => {
      try {
        const res = await getAllMachineData({ data }).unwrap();
        setMacData(res.allMacData);
      } catch (error) {
        console.error(error); // eslint-disable-line
      }
    };
    if (data) {
      fetchData();
    }
  }, [allMacId, getAllMachineData]);

  const emsgs = [];
  const operators = [];
  const machine = [];
  const workerData = [];
  const machineUptimeData = [];
  const machineDowntimeData = [];

  error.forEach((itm) => {
    itm.machineAndOperator.forEach((item) => {
      machine.push(item.machine.machineId);
      item.operator.forEach((i) => {
        operators.push(i.user.RFId);
      });
    });
    itm.errorMessages.forEach((item) => {
      emsgs.push(item.error.message);
    });
  });

  let uniqueOperator = operators.filter(
    (item, i, ar) => ar.indexOf(item) === i
  );
  let uniqueMachine = machine.filter((item, i, ar) => ar.indexOf(item) === i);

  //Here unique machine and operator is stored in above which then used to filter the data

  uniqueOperator.forEach((item) => {
    let workerArray = [];
    error.forEach((itm) => {
      itm.machineAndOperator.forEach((itr) => {
        itr.operator.forEach((i) => {
          if (i.user.RFId === item) {
            const res = {
              action: i.action,
              time: i.time,
            };
            workerArray.push(res);
          }
        });
      });
    });
    //================================================================================================================================
    //Now we calculate the working hour for different workers in uniqueOperator--->>>

    let netActiveTime = 0;
    let isPaused = false;
    let lastEventTime = null;

    workerArray.forEach(function (event) {
      let eventType = event.action;

      if (eventType === 'start' || eventType === 'resume') {
        let eventTime = new Date(event.time).getTime(); // Convert to milliseconds
        if (lastEventTime !== null && isPaused) {
          netActiveTime += eventTime - lastEventTime;
        }
        isPaused = false;
        lastEventTime = eventTime;
      } else if (eventType === 'pause') {
        isPaused = true;
        lastEventTime = new Date(event.time).getTime(); // Convert to milliseconds
      } else if (eventType === 'stop') {
        let eventTime = new Date(event.time).getTime(); // Convert to milliseconds
        if (lastEventTime !== null && !isPaused) {
          netActiveTime += eventTime - lastEventTime;
        }
        lastEventTime = null;
      }
    });

    let netActiveHours = netActiveTime / (1000 * 60); // Convert milliseconds to hours
    const ans = {
      workerID: item,
      uptime: netActiveHours || 0,
    };

    workerData.push(ans);
  });

  workerData.forEach((item) => {
    uptimeLabelW.push(item.workerID);
    uptimeDataW.push(item.uptime);
  });

  //==================================================================================================================================
  //Now we calculate the uptime  for different machine in uniqueMachine--->>>

  uniqueMachine.forEach((machine) => {
    let tempData = [];
    error.forEach((cuPro) =>
      cuPro.machineAndOperator.forEach((mao) => {
        if (mao.machine.machineId === machine) {
          tempData.push({
            mao,
            errorMessages: cuPro?.errorMessages?.filter(
              (em) => em.machineAndOperatorId === mao._id
            ),
          });
        }
      })
    );
    let upTimeArray = [];
    let plannedDTArray = [];
    let unPlannedDTArray = [];

    tempData.forEach((data) => {
      const startTime = data.mao.startTime;
      const stopTime = data.mao.stopTime;
      const pauseTime = data.mao.pauseTime;
      const resumeTime = data.mao.resumeTime;
      const firstPauseTime = pauseTime[0];
      const lastStopTime = resumeTime[resumeTime?.length - 1];
      const pauseErrs = data.errorMessages.filter(
        (em) =>
          em.error.action === 'pause' &&
          em.machineAndOperatorId === data.mao._id
      );

      if (pauseTime?.length > 0) {
        // downtime
        // if machines is paused
        pauseTime.forEach((time, tIdx) => {
          const val = getTimeDifference(time, resumeTime[tIdx]);
          const timer = pauseErrs?.[tIdx]?.error?.timer;
          if (timer) {
            // if paused with timer check if time excced or not
            if (val > +timer) {
              // if val exceeds timer push val to unPlannedDT array
              plannedDTArray.push(0);
              unPlannedDTArray.push(val);
            } else {
              // push val to plannedDT array
              plannedDTArray.push(val);
              unPlannedDTArray.push(0);
            }
          } else {
            // push val to unplannedDT array
            unPlannedDTArray.push(val);
            plannedDTArray.push(0);
          }
        });

        // upTime
        const firstVal = getTimeDifference(startTime, firstPauseTime);

        let middleVals = [];
        pauseTime?.forEach((time, tIdx) => {
          if (tIdx === 0) return;
          middleVals.push(getTimeDifference(resumeTime[tIdx - 1], time));
        });

        const lastVal = getTimeDifference(lastStopTime, stopTime);

        upTimeArray.push(...[firstVal, ...middleVals, lastVal]);
      } else {
        // set both DT array to 0
        plannedDTArray.push(0);
        unPlannedDTArray.push(0);
        // set upTime array to diff between start and stop time
        const val = getTimeDifference(startTime, stopTime);
        upTimeArray.push(val);
      }
    });

    // calcuate values for temp generated data
    const upTime = Math.round(
      upTimeArray.reduce((acc, curVal) => acc + curVal, 0)
    );

    const plannedDT = Math.round(
      plannedDTArray.reduce((acc, curVal) => acc + curVal, 0)
    );

    const unPlannedDT = Math.round(
      unPlannedDTArray.reduce((acc, curVal) => acc + curVal, 0)
    );

    const ans1 = {
      macID: machine,
      uptime: upTime || 0,
    };

    machineUptimeData.push(ans1);

    const ans2 = {
      macID: machine,
      downtime: plannedDT + unPlannedDT || 0,
    };

    machineDowntimeData.push(ans2);
  });

  // ===================================================================================================================================

  let frequency = emsgs.reduce(function (freq, str) {
    freq[str] = (freq[str] || 0) + 1;
    return freq;
  }, {});

  let uniqueStrings = Object.keys(frequency).map(function (key) {
    let obj = {};
    obj[key] = frequency[key];
    return obj;
  });

  uniqueStrings.forEach((itm) => {
    errorLabel.push(Object.keys(itm)[0]);
    errorData.push(Object.values(itm)[0]);
  });
  // ===================================================================================================================================
  for (let i = 0; i < allMacId.length; i++) {
    machineLabel.push(allMacId[i].machineId);
  }

  for (let i = 0; i < macData.length; i++) {
    if (macData[i] === null) {
      machineData.push('0');
    } else {
      machineData.push(macData[i].data.COUNT);
    }
  }
  //===========================================================================================================

  machineUptimeData.forEach((item) => {
    uptimeLabelM.push(item.macID);
  });

  machineUptimeData.forEach((item) => {
    uptimeDataM.push(item.uptime);
  });

  machineDowntimeData.forEach((item) => {
    downtimeDataM.push(item.downtime);
  });

  //=============================================
  // Change the type for switching the bar graphs
  let type = params;

  let txt;
  let Finallables = [];
  let finalData = [];
  let finalLbl;

  if (type === 'Error Count') {
    txt = 'Error Bar Graph';
    Finallables = errorLabel;
    finalData = errorData;
    finalLbl = 'Error Count';
  } else if (type === 'Machine Count') {
    txt = 'Machine Bar Graph';
    Finallables = machineLabel;
    finalData = machineData;
    finalLbl = 'Machine Count';
  } else if (type === 'Worker Efficiency') {
    txt = 'Worker Uptime( in mins )';
    Finallables = uptimeLabelW;
    finalData = uptimeDataW;
    finalLbl = 'Uptime Worker';
  } else if (type === 'Machine Uptime') {
    txt = 'Machine Uptime( in mins )';
    Finallables = uptimeLabelM;
    finalData = uptimeDataM;
    finalLbl = 'Machine Uptime';
  } else if (type === 'Machine Downtime') {
    txt = 'Machine Downtime( in mins )';
    Finallables = uptimeLabelM;
    finalData = downtimeDataM;
    finalLbl = 'Downtime Machine';
  }

  //========================================================

  const labels = Finallables;

  const options = {
    indexAxis: 'y',
    elements: {
      bar: {
        borderWidth: 2,
      },
    },
    responsive: true,
    plugins: {
      legend: {
        position: 'right',
      },
      title: {
        display: true,
        text: txt,
      },
    },
  };

  const data = {
    labels,
    datasets: [
      {
        label: finalLbl,
        data: finalData,
        borderColor: 'rgb(53, 162, 235)',
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
      },
    ],
  };

  return <Bar options={options} data={data} />;
};

export default CreateAnaHorizontal;
