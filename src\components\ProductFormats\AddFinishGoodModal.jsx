import { But<PERSON>, Modal } from 'antd';
import { Trash2 } from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import { getVariantsNameList } from '../../helperFunction';
import {
  useGetBaseProductsQuery,
  useGetSelectedProductsMutation,
  useLazyGetChildProductQuery,
} from '../../slices/productApiSlice';
import { Store } from '../../store/Store';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import Tooltip from '../global/components/ToolTip';
import { getInStock } from '../v3/WorkOrder/CreateWorkOrder';

function SelectComp({
  options = [],
  DropdownValue,
  setDropdownValue,
  idx = 0,
  setSelectedProducts,
}) {
  const [selectedOption, setSelectedOption] = useState('');
  const {
    defaults: { defaultParam },
  } = useContext(Store);
  const [getProductById, { data: proData = {}, isFetching }] =
    useLazyGetChildProductQuery();

  const handleSelect = async (e) => {
    setSelectedOption(e.target.value);
    await getProductById({ id: e.target.value }, false);
  };

  const handleAddProduct = async () => {
    const item = options?.find((opt) => opt?.value === selectedOption);
    if (item) {
      // Generate unique ID for each instance of the product
      const uniqueId = `${item.value}-${Date.now()}`;
      setDropdownValue((prev) => [
        ...(prev || []),
        {
          name: item?.label,
          _id: item?.value,
          value: item?.value,
          indent_qty: 0,
          instanceId: uniqueId, // Add unique instance ID
        },
      ]);
      setSelectedOption('');
    }
  };

  if (defaultParam?.projectDefaults?.enableMultiselectVariants) {
    return (
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Products
        </label>
        <MultiSelect
          options={options}
          value={DropdownValue?.map((item) => item?._id)}
          onChange={(e) => {
            const qtyObj = {};
            DropdownValue.forEach((i) => {
              qtyObj[i?._id] = i?.indent_qty;
            });
            setDropdownValue(
              e?.target?.value?.map((item) => ({
                name: item?.label,
                _id: item?.value,
                value: item?.value,
                indent_qty: qtyObj?.[item?.value] || 0,
                instanceId: `${item.value}-${Date.now()}`, // Add unique instance ID
              }))
            );
          }}
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {defaultParam?.projectDefaults?.variantLabels?.[idx]
            ? defaultParam?.projectDefaults?.variantLabels?.[idx]
            : idx
              ? `Variant ${idx}`
              : 'Select Product'}
        </label>
        <Select
          options={options}
          value={selectedOption}
          onChange={handleSelect}
          className="w-full"
          menuPosition="fixed"
        />
      </div>
      {proData?.children?.length > 0 ? (
        <SelectComp
          options={proData?.children?.map((opt) => ({
            label: opt?.name,
            value: opt?._id,
          }))}
          DropdownValue={DropdownValue}
          setDropdownValue={setDropdownValue}
          setSelectedProducts={setSelectedProducts}
          idx={idx + 1}
        />
      ) : selectedOption && !isFetching ? (
        <div className="flex justify-end mt-4">
          <Button
            onClick={handleAddProduct}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
            size="sm"
          >
            Add Product
          </Button>
        </div>
      ) : null}
    </div>
  );
}

const AddFinishGoodModal = ({
  openModal,
  setOpenModal,
  onAddProduct,
  showIGST,
}) => {
  const [dropdownValue, setDropdownValue] = useState([]);
  const { data: baseProducts } = useGetBaseProductsQuery();
  const {
    defaults: { defaultParam },
  } = useContext(Store);
  const [selectedPro, setSelectedPro] = useState([]);
  const [getSelectedProducts] = useGetSelectedProductsMutation();

  const handleAddProduct = async () => {
    const dataMap = {};
    const ids = [];
    for (let i = 0; i < dropdownValue?.length; i++) {
      const el = dropdownValue?.[i];
      dataMap[el?.instanceId] = el?.indent_qty; // Use instanceId instead of _id
      ids.push(el?._id);
    }
    const { data: prods } = await getSelectedProducts({ data: { ids } });
    const updatedProducts = dropdownValue
      .map((dropdownItem) => {
        const product = prods.find((p) => p._id === dropdownItem._id);
        if (!product) return null;

        // eslint-disable-next-line no-unused-vars
        const { template, ...rest } = product;
        return {
          ...rest,
          instanceId: dropdownItem.instanceId,
          itemId: product?._id || '',
          productName: product?.name || '',
          namesList: getVariantsNameList(product),
          uom: product.uom || '',
          hsn: product.itemDetails?.hsn || '',
          quantity: dataMap?.[dropdownItem.instanceId] || 0,
          rate: product?.itemDetails?.rate ?? 0,
          cgst: !showIGST ? (product?.itemDetails?.cgst ?? 0) : 0,
          igst: showIGST ? (product?.itemDetails?.igst ?? 0) : 0,
          sgst: !showIGST ? (product?.itemDetails?.sgst ?? 0) : 0,
          discount: product?.itemDetails?.discount ?? 0,
        };
      })
      .filter(Boolean);

    onAddProduct(updatedProducts);
    setOpenModal(false);
  };

  useEffect(() => {
    if (dropdownValue?.length > 0) {
      let allIds = [];
      for (let i = 0; i < dropdownValue?.length; i++) {
        allIds.push(dropdownValue[i]?._id);
      }
      if (allIds.length > 0) {
        getSelectedProducts({ data: { ids: allIds } })
          ?.unwrap()
          .then((res) => {
            let allInfo = [];
            res.forEach((item) => {
              let fg = item?.batches?.filter(
                (itm) => itm?.itemType === 'finishedGoods'
              );
              let fgQty =
                fg?.reduce((prev, curr) => prev + +curr.quantity, 0) || 0;

              let semifg = item?.batches?.filter(
                (itm) => itm?.itemType === 'semiFinishedGoods'
              );
              let semiFgQty =
                semifg?.reduce((prev, curr) => prev + +curr.quantity, 0) || 0;
              allInfo.push({
                id: item?._id,
                name: item?.name || '',
                fgQty,
                semiFgQty,
                product: item,
              });
            });
            setSelectedPro(allInfo);
          });
      }
    }
  }, [dropdownValue]); // eslint-disable-line

  return (
    <Modal
      open={openModal}
      onCancel={() => setOpenModal(false)}
      title="Add Product"
      width={1000}
      onOk={handleAddProduct}
      styles={{
        body: {
          maxHeight: `calc(100vh - 150px)`,
          overflowY: 'auto',
        },
      }}
    >
      <div
        className={`w-full mt-6 mb-8 ${
          defaultParam?.projectDefaults?.enableHorizontalSelector
            ? 'flex flex-wrap gap-4 items-start [&>div]:min-w-[300px] [&>div]:flex-1'
            : ''
        }`}
      >
        <SelectComp
          options={baseProducts?.map((opt) => ({
            label: opt?.name,
            value: opt?._id,
          }))}
          DropdownValue={dropdownValue}
          setDropdownValue={setDropdownValue}
        />
      </div>

      <div className="overflow-x-auto rounded-lg border border-gray-200">
        <Table className="min-w-full">
          <Table.Head>
            <Table.Row className="bg-gray-50">
              <Table.Th className="!py-3">Item</Table.Th>
              <Table.Th className="!py-3">In Stock</Table.Th>
              <Table.Th className="!py-3 !text-center">Finished Goods</Table.Th>
              <Table.Th className="!py-3 !text-center">
                Semi Finished Goods
              </Table.Th>
              <Table.Th className="!py-3"></Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {dropdownValue?.map((item, idx) => {
              let currItem = (selectedPro || [])?.find(
                (i) => i?.id === item?._id
              );
              return (
                <Table.Row
                  key={item.instanceId}
                  className="hover:bg-gray-50 transition-colors"
                >
                  <Table.Td className="!py-4 !min-w-[300px]">
                    {item?.name?.length > 60 ? (
                      <Tooltip text={item?.name}>
                        <span className="inline-block">
                          {item?.name.substring(0, 60) + '...'}
                        </span>
                      </Tooltip>
                    ) : (
                      <span className="inline-block">{item?.name}</span>
                    )}
                  </Table.Td>
                  <Table.Td className="text-gray-600">
                    {getInStock(currItem) || 0}
                  </Table.Td>
                  <Table.Td className="!text-center text-gray-600">
                    {currItem?.fgQty || 0}
                  </Table.Td>
                  <Table.Td className="!text-center text-gray-600">
                    {currItem?.semiFgQty || 0}
                  </Table.Td>
                  <Table.Td>
                    <button
                      onClick={() => {
                        setDropdownValue((prev) =>
                          prev.filter((_, index) => index !== idx)
                        );
                      }}
                      className="p-2 hover:bg-red-50 rounded-full transition-colors group"
                    >
                      <Trash2 className="w-5 h-5 text-red-400 group-hover:text-red-500" />
                    </button>
                  </Table.Td>
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
      </div>
    </Modal>
  );
};

export default AddFinishGoodModal;
