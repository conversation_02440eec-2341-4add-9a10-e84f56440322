import { useState } from 'react';
import { IoLocationSharp } from 'react-icons/io5';
import { toast } from 'react-toastify';
import { useGetAllIotDevicesQuery } from '../../slices/iotDeviceApiSlice';
import Input from '../global/components/Input';
import MapLocationSelector from '../global/components/MapLocationSelector';
import Modal, { GridWrapper, LabelAndField } from '../global/components/Modal';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import Textarea from '../global/components/Textarea';

const INITIAL_MACHINES_DATA = {
  name: '',
  devices: [],
  dailyThreshold: '',
  maintenanceThreshold: '',
  overloadThreshold: '',
  costPerHour: '',
};

function ManageDeviceDashboardCustomer({
  inputData,
  setInputData,
  onCloseModal,
  submitHandler,
  btnIsLoading,
}) {
  const [machineData, setMachineData] = useState(INITIAL_MACHINES_DATA);
  const [selectedDevice, setSelectedDevice] = useState([]);
  const [openMap, setOpenMap] = useState(false);

  const { data: devices = [] } = useGetAllIotDevicesQuery();

  const validateThreshold = (name, value) => {
    const check = value.split(':');

    const hasLetter = value.match(/[a-zA-Z]/);

    const isDaily = name === 'dailyThreshold';

    if (hasLetter) {
      toast.error('Cannot contain letter', {
        toastId: 'letter',
      });
      return false;
    } else if (value?.length > 5 && isDaily) {
      toast.error('Invalid format, It should be HH:MM', {
        toastId: 'format',
      });
      return false;
    } else if (+check[0] > 23 && isDaily) {
      toast.error('Hours cannot be greater than 23', { toastId: 'hour' });
      return false;
    } else if (+check[1] > 59) {
      toast.error('Minutes cannot be greater than 59', { toastId: 'minute' });
      return false;
    }

    return true;
  };

  const checkIsThreshold = (name) =>
    name === 'dailyThreshold' || name === 'maintenanceThreshold';

  const inputChangeHandler = (e) => {
    const { name, value } = e.target;
    setInputData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const machineInputChangeHandler = (e) => {
    const { name, value, type } = e.target;

    if (checkIsThreshold(name)) {
      const check = validateThreshold(name, value);

      if (!check) return;
    }

    setMachineData((prev) => ({
      ...prev,
      [name]: type === 'select' ? [value] : value,
    }));
  };

  const rowInputChageHandler = (e, idx) => {
    const { name, value } = e.target;

    const isThreshold = checkIsThreshold(name);

    if (isThreshold) {
      const check = validateThreshold(name, value);

      if (!check) return;
    }

    setInputData((prev) => ({
      ...prev,
      deviceDashboardMachineIds: prev?.deviceDashboardMachineIds?.map(
        (mac, mIdx) => {
          if (idx === mIdx) {
            return {
              ...mac,
              [name]: value,
            };
          }
          return mac;
        }
      ),
    }));
  };

  const addMachine = () => {
    if (!machineData?.name || machineData?.devices?.length === 0) {
      toast.error('Fill all details before adding', { toastId: 'incomplete' });
      return;
    }

    const exists = inputData?.deviceDashboardMachineIds?.find(
      (i) => i.name === machineData?.name?.trim()
    );

    if (exists) {
      toast.error(`Machine with name ${exists.name} already exists`, {
        toastId: 'exists',
      });
      setMachineData((prev) => ({ ...prev, name: '' }));
      return;
    }

    setInputData((prev) => {
      return {
        ...prev,
        deviceDashboardMachineIds: [
          ...prev?.deviceDashboardMachineIds,
          {
            id: Date.now(),
            ...machineData,
            name: machineData?.name?.trim(),
            devices: machineData?.devices,
          },
        ],
      };
    });

    setSelectedDevice((prev) => [...prev, ...machineData?.devices]);

    setMachineData(INITIAL_MACHINES_DATA);
  };

  return (
    <Modal
      title={'Add New Customer'}
      onCloseModal={onCloseModal}
      onSubmit={(e) => submitHandler(e, devices)}
      pages={['General', 'Machines', 'Location', 'User']}
      onAdd={{
        label: 'Add',
        func: [null, addMachine],
        step: [1],
      }}
      btnIsLoading={btnIsLoading}
    >
      {({ step }) => (
        <>
          {step === 0 ? (
            <>
              <GridWrapper>
                <LabelAndField
                  label={'Name'}
                  htmlFor={'name'}
                  className="col-span-full"
                >
                  <Input
                    value={inputData?.name}
                    id="name"
                    name={'name'}
                    onChange={inputChangeHandler}
                  />
                </LabelAndField>

                <LabelAndField label={'Contact'} htmlFor={'contact'}>
                  <Input
                    value={inputData?.contact}
                    id="contact"
                    name={'contact'}
                    onChange={inputChangeHandler}
                  />
                </LabelAndField>

                <LabelAndField label={'GST'} htmlFor={'gst'}>
                  <Input
                    value={inputData?.gst}
                    id="gst"
                    name={'gst'}
                    onChange={inputChangeHandler}
                  />
                </LabelAndField>

                <LabelAndField
                  label={'Address'}
                  htmlFor={'address'}
                  className="col-span-full"
                >
                  <Textarea
                    value={inputData?.address}
                    id="address"
                    name={'address'}
                    onChange={inputChangeHandler}
                  />
                </LabelAndField>
              </GridWrapper>
            </>
          ) : step === 1 ? (
            <>
              <GridWrapper>
                <LabelAndField label={'Name'} htmlFor={'name'}>
                  <Input
                    value={machineData?.name}
                    id="name"
                    name={'name'}
                    onChange={machineInputChangeHandler}
                  />
                </LabelAndField>

                <LabelAndField label={'Devices'} htmlFor={'devices'}>
                  <Select
                    value={machineData?.devices?.[0]}
                    id="devices"
                    name={'devices'}
                    options={devices?.map((div) => ({
                      label: div?.deviceId,
                      value: div?._id,
                      disabled:
                        selectedDevice.includes(div._id) ||
                        div?.deviceDashboardMachineId,
                    }))}
                    onChange={machineInputChangeHandler}
                  />
                </LabelAndField>

                <LabelAndField
                  label={'Daily Threshold'}
                  htmlFor={'dailyThreshold'}
                >
                  <Input
                    id="dailyThreshold"
                    name={'dailyThreshold'}
                    value={machineData?.dailyThreshold}
                    onChange={machineInputChangeHandler}
                  />
                </LabelAndField>

                <LabelAndField
                  label={'Maintenance Threshold'}
                  htmlFor={'maintenanceThreshold'}
                >
                  <Input
                    id="maintenanceThreshold"
                    name={'maintenanceThreshold'}
                    value={machineData?.maintenanceThreshold}
                    onChange={machineInputChangeHandler}
                  />
                </LabelAndField>

                <LabelAndField
                  label={'Overload Threshold'}
                  htmlFor={'overloadThreshold'}
                >
                  <Input
                    id="overloadThreshold"
                    name={'overloadThreshold'}
                    value={machineData?.overloadThreshold}
                    onChange={machineInputChangeHandler}
                  />
                </LabelAndField>

                <LabelAndField label={'Cost Per Hour'} htmlFor={'costPerHour'}>
                  <Input
                    id="costPerHour"
                    name={'costPerHour'}
                    value={machineData?.costPerHour}
                    onChange={machineInputChangeHandler}
                  />
                </LabelAndField>

                <div className="col-span-full overflow-x-scroll">
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>#</Table.Th>
                        <Table.Th>Name</Table.Th>
                        <Table.Th>Devices</Table.Th>
                        <Table.Th>Daily&nbsp;Threshold</Table.Th>
                        <Table.Th>Maintenance&nbsp;Threshold</Table.Th>
                        <Table.Th>Overload&nbsp;Threshold</Table.Th>
                        <Table.Th>Cost&nbsp;Per&nbsp;Hour</Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {inputData?.deviceDashboardMachineIds?.map(
                        (item, idx) => (
                          <Table.Row key={item?._id || item?.id}>
                            <Table.Td>{idx + 1}</Table.Td>
                            <Table.Td>{item?.name}</Table.Td>
                            <Table.Td>
                              {
                                devices?.find(
                                  (div) => div._id === item?.devices?.[0]
                                )?.deviceId
                              }
                            </Table.Td>
                            <Table.Td>
                              <Input
                                name={'dailyThreshold'}
                                value={item?.dailyThreshold}
                                onChange={(e) => rowInputChageHandler(e, idx)}
                              />
                            </Table.Td>
                            <Table.Td>
                              <Input
                                name={'maintenanceThreshold'}
                                value={item?.maintenanceThreshold}
                                onChange={(e) => rowInputChageHandler(e, idx)}
                              />
                            </Table.Td>
                            <Table.Td>
                              <Input
                                type="number"
                                name={'overloadThreshold'}
                                value={item?.overloadThreshold}
                                onChange={(e) => rowInputChageHandler(e, idx)}
                              />
                            </Table.Td>
                            <Table.Td>
                              <Input
                                type="number"
                                name={'costPerHour'}
                                value={item?.costPerHour}
                                onChange={(e) => rowInputChageHandler(e, idx)}
                              />
                            </Table.Td>
                          </Table.Row>
                        )
                      )}
                    </Table.Body>
                  </Table>
                </div>
              </GridWrapper>
            </>
          ) : step === 2 ? (
            <GridWrapper>
              <LabelAndField label={'Location'} className="col-span-full">
                <div className="flex justify-between items-center gap-5">
                  <Input readOnly value={inputData?.location?.lat} />
                  <Input readOnly value={inputData?.location?.lng} />
                  <button
                    type="button"
                    className="border h-full border-gray-300 px-2 rounded-md flex items-center"
                    onClick={() => setOpenMap(true)}
                  >
                    <IoLocationSharp className="h-8 w-8" />
                  </button>
                </div>
              </LabelAndField>

              {openMap && (
                <MapLocationSelector
                  setOpen={setOpenMap}
                  isModal={false}
                  className="col-span-full row-span-3 min-h-[300px]"
                  value={inputData?.location}
                  onSelect={(val) =>
                    setInputData((prev) => ({ ...prev, location: val }))
                  }
                />
              )}
            </GridWrapper>
          ) : step === 3 ? (
            <GridWrapper>
              <LabelAndField label={'Customer Name'} htmlFor={'customerName'}>
                <Input
                  value={inputData?.customerName}
                  id="customerName"
                  name={'customerName'}
                  onChange={inputChangeHandler}
                />
              </LabelAndField>

              <LabelAndField label={'Email'} htmlFor={'email'}>
                <Input
                  value={inputData?.email}
                  id="email"
                  name={'email'}
                  onChange={inputChangeHandler}
                />
              </LabelAndField>

              <LabelAndField label={'Password'} htmlFor={'password'}>
                <Input
                  value={inputData?.password}
                  type="password"
                  id="password"
                  name={'password'}
                  onChange={inputChangeHandler}
                />
              </LabelAndField>

              <LabelAndField label={'Confirm Password'} htmlFor={'confirm'}>
                <Input
                  value={inputData?.confirm}
                  type="password"
                  id="confirm"
                  name={'confirm'}
                  onChange={inputChangeHandler}
                />
              </LabelAndField>
            </GridWrapper>
          ) : null}
        </>
      )}
    </Modal>
  );
}

export default ManageDeviceDashboardCustomer;
