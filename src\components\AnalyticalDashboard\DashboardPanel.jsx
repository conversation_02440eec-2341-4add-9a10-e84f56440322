const DashboardPanel = () => {
  return (
    <div className="mb-4">
      <div className="w-full bg-white rounded-2xl shadow-low flex justify-between px-8 py-4 mb-3">
        <section className="w-1/4">
          <h3 className="font-semibold text-xl">Total Count</h3>
          <p className="text-6xl font-bold text-black">
            78
            <span className="ml-3 text-lg bg-[#F28585] rounded text-red-primary px-2 py-0.5">
              45% v
            </span>
          </p>
        </section>
        <section className="w-1/4">
          <h3 className="font-semibold text-xl">Total Count</h3>
          <p className="text-6xl font-bold text-black">
            78
            <span className="ml-3 text-lg bg-[#F28585] rounded text-red-primary px-2 py-0.5">
              45% v
            </span>
          </p>
        </section>
        <section className="w-1/4">
          <h3 className="font-semibold text-xl">Total Count</h3>
          <p className="text-6xl font-bold text-black">
            78
            <span className="ml-3 text-lg bg-[#F28585] rounded text-red-primary px-2 py-0.5">
              45% v
            </span>
          </p>
        </section>
      </div>
      <div className="w-full bg-white rounded-2xl shadow-low flex justify-between px-8 py-4">
        <section className="w-1/4">
          <h3 className="font-semibold text-xl">Total Error</h3>
          <p className="text-6xl font-bold text-black">
            32
            <span className="ml-3 text-lg bg-[#F28585] rounded text-red-primary px-2 py-0.5">
              45% v
            </span>
          </p>
        </section>
        <section className="w-1/4">
          <h3 className="font-semibold text-xl">Total Error</h3>
          <p className="text-6xl font-bold text-black">
            32
            <span className="ml-3 text-lg bg-[#F28585] rounded text-red-primary px-2 py-0.5">
              45% v
            </span>
          </p>
        </section>
        <section className="w-1/4">
          <h3 className="font-semibold text-xl">Total Error</h3>
          <p className="text-6xl font-bold text-black">
            32
            <span className="ml-3 text-lg bg-[#F28585] rounded text-red-primary px-2 py-0.5">
              45% v
            </span>
          </p>
        </section>
      </div>
    </div>
  );
};

export default DashboardPanel;
