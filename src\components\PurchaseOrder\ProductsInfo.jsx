import { useContext, useEffect, useMemo, useState } from 'react';

import { BiH<PERSON>, BiShow } from 'react-icons/bi';

import { toast } from 'react-toastify';
import Button from '../global/components/Button.jsx';
import Modal from '../global/components/Modal.jsx';
import MultiSelect from '../global/components/MultiSelect.jsx';
import Table from '../global/components/Table.jsx';
import CustomColumnModal from './CustomColumnModal.jsx';

import {
  addCommaToAmount,
  getCorrespondingConversionFactor,
  handleFormula,
} from '../../helperFunction.js';

import { Input, Popover } from 'antd';
import { IoSaveOutline } from 'react-icons/io5';
import { useNavigate } from 'react-router-dom';
import {
  // useAddCustomColumnMutation,
  // useDeleteCustomColumnMutation,
  // useEditCustomColumnMutation,
  useGetCustomColumnsQuery,
} from '../../slices/customCoulmn.ApiSlice.js';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice.js';
import { useGetHeaderByPageQuery } from '../../slices/headerReorderApiSlice.js';
import { Store } from '../../store/Store.js';
import { DEFAULT_PO_PRODUCT_DETAILS_HEADER } from '../../utils/Constant.js';
import { customConfirm } from '../../utils/customConfirm.js';
import Toggle from '../global/components/Toggle.jsx';
import HeaderReorder from '../Pipeline/HeaderReorder.jsx';
import ProductTableRow from './ProductTableRow.jsx';

const ProductsInfo = ({
  formData,
  setFormData,
  setItems,
  items,
  setSearchParams,
  vendor,
  columnInputs,
  setColumnInputs,
  itemTypes,
  setItemTypes,
  partsData,
  productsData,
  selectedItem,
  setSelectedItem,
  selectedParts,
  setSelectedParts,
  id,
  fromOtherPage,
  showIgst,
  setShowIgst,
  setSubtotalFormulaPrice,
  setShowAddItemModal,
  setShowMasterModal,
}) => {
  const activeHeaderFromLocal = 'activePOProductDetailsHeader';
  const [allHeader, setAllHeader] = useState(
    DEFAULT_PO_PRODUCT_DETAILS_HEADER || []
  );
  const navigate = useNavigate();
  const [isOpenHeaders, setIsOpenHeaders] = useState(false);
  const { data: allCustomCols } = useGetCustomColumnsQuery();
  const { defaults } = useContext(Store);
  const { defaultParam } = defaults;
  const [allCustomColumns, setAllCustomColumns] = useState([]);
  const [itemOptions, setItemOptions] = useState([]);
  // const [customInputValue, setCustomInputValue] = useState([]);
  const [showCustomColumnModal, setShowCustomColumnModal] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [subTotalFormula, setSubTotalFormula] = useState(
    defaults?.defaultParam?.purchaseOrder?.subTotalFormula || ''
  );
  const [updateDefaults] = useUpdateDefaultsMutation();

  const { data: headerInfo, refetch: refetchHeader } = useGetHeaderByPageQuery({
    headerFor: 'activePOProductDetailsHeader',
  });
  const [localStorgeChange, setLocalStorgeChange] = useState(false);
  const [activeHeader, setActiveHeader] = useState(
    headerInfo?.headers || DEFAULT_PO_PRODUCT_DETAILS_HEADER
  );

  const getTotalAmount = () => {
    let amt = 0;
    for (let item of items) {
      amt =
        amt +
        calculateTotalPrice(
          item?.quantity || 0,
          item?.rate || 0,
          item?.discount || 0,
          !showIgst ? item?.cgst || 0 : 0,
          !showIgst ? item?.sgst || 0 : 0,
          showIgst ? item?.igst || 0 : 0
        )?.price;
    }
    return amt;
  };
  //TODO fetch here
  useEffect(() => {
    refetchHeader();
  }, [localStorgeChange]); //eslint-disable-line

  useEffect(() => {
    const amt = getTotalAmount();
    setFormData((prev) => ({ ...prev, total: amt }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [items]);

  useEffect(() => {
    setItems((prevItems) => {
      return prevItems.map((item) => {
        if (showIgst) {
          return { ...item, sgst: 0, cgst: 0 }; // Update sgst and cgst to 0
        } else {
          return { ...item, igst: 0 }; // Update igst to 0
        }
      });
    });
  }, [showIgst]); //eslint-disable-line

  const calculateTaxAmount = (amount, percentage) =>
    (amount * (percentage || 0)) / 100;

  const calculateTotalPrice = (quantity, rate, discount, cgst, sgst, igst) => {
    let localTotalPrice = 0;
    const amount = quantity * rate * (1 - (discount || 0) / 100);
    const cgstAmount = !showIgst ? calculateTaxAmount(amount, cgst) : 0;
    const sgstAmount = !showIgst ? calculateTaxAmount(amount, sgst) : 0;
    const igstAmount = showIgst ? calculateTaxAmount(amount, igst) : 0;
    localTotalPrice += amount + cgstAmount + sgstAmount + igstAmount;

    return { price: parseInt(localTotalPrice.toFixed(2)), amount };
  };
  const { totalTaxAmount, _subTotal } = items.reduce(
    (acc, i) => {
      const conversion = getCorrespondingConversionFactor(i?.uom, i?.item);
      const conversionValue = conversion
        ? parseFloat(conversion.conversionValue)
        : 1;

      const quantity = i?.quantity ?? 0;
      const rate = i?.rate ?? 0;
      const discount = i?.discount ?? 0;

      const amt = quantity * rate * conversionValue * (1 - discount / 100);

      const cgst = !showIgst ? calculateTaxAmount(amt, i?.cgst ?? 0) : 0;
      const sgst = !showIgst ? calculateTaxAmount(amt, i?.sgst ?? 0) : 0;
      const igst = showIgst ? calculateTaxAmount(amt, i?.igst ?? 0) : 0;

      return {
        totalTaxAmount: acc.totalTaxAmount + cgst + sgst + igst,
        subTotal: acc.subTotal + amt,
      };
    },
    { totalTaxAmount: 0, subTotal: 0 }
  );

  useEffect(() => {
    if (partsData && productsData) {
      setItemOptions([...(partsData || []), ...(productsData || [])]);
    }
  }, [partsData, productsData]);

  useEffect(() => {
    const temp = allCustomCols?.filter((col) => col?.pageName === 'Create Po');
    setAllCustomColumns(temp);

    if (temp?.length === 0) {
      setColumnInputs([]);
    } else {
      setColumnInputs(temp);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allCustomCols, showCustomColumnModal]);

  const handleHideColumns = (type) => {
    setFormData((prev) => {
      return {
        ...prev,
        productColumnHideStatus: {
          ...prev?.productColumnHideStatus,
          [type]: !prev?.productColumnHideStatus?.[type] || false,
        },
      };
    });
  };

  const handleHideCustomColumns = (type) => {
    setFormData((prev) => {
      return {
        ...prev,
        customColumnsHideStatus: {
          ...prev?.customColumnsHideStatus,
          [type]: !prev?.customColumnsHideStatus?.[type] || false,
        },
      };
    });
  };

  const handelpartChange = (e) => {
    const allSelectedPartsIds = e.target.value?.map((el) => el.value);

    const allParts = itemOptions?.filter((el) =>
      allSelectedPartsIds.includes(el.value)
    );
    setSelectedParts(e.target.value);
    setSelectedItem(allParts);
  };

  const handleAddPart = () => {
    navigate(
      `/settings/inventory/${`masters?selectedTab=parts&openModal=true&navigate=${window.location.pathname}`}`
    );
  };

  const getTotalValueOfKey = (products, index, key) => {
    const valueFromProductDetails = products?.[index]?.[key] || 0;
    const valueFromCustomColumns = products?.[index]?.customColumns?.[key] || 0;
    return (
      parseFloat(valueFromProductDetails) + parseFloat(valueFromCustomColumns)
    );
  };

  const getFormulaValue = useMemo(() => {
    let initialFormula =
      subTotalFormula && subTotalFormula !== '' ? subTotalFormula : ``;

    let formula = initialFormula
      .substring(initialFormula.indexOf('{') + 1, initialFormula.indexOf('}'))
      .trim();

    const regex = /\$([A-Za-z]+)/g;
    let match;
    let totalResult = 0;

    const calculateForProducts = (products) => {
      products?.forEach((item, index) => {
        let val = {};
        let formulaForRow = formula;

        while ((match = regex.exec(formula)) !== null) {
          const key = match[1]; // Extract the key name after $

          const total = getTotalValueOfKey(products, index, key);
          val[key] = total;

          // Replace the variable in the formula with the value for the current row
          formulaForRow = formulaForRow.replaceAll(`$${key}`, total);
        }

        const allFields = [
          { name: 'cgst', percentOf: 'amount' },
          { name: 'sgst', percentOf: 'amount' },
          { name: 'igst', percentOf: 'amount' },
          { name: 'discount', percentOf: 'amount' },
        ];

        if (formulaForRow !== '' && formulaForRow) {
          const resultForRow = handleFormula(
            { formula: `calc{ ${formulaForRow} }` },
            val,
            allFields
          );
          totalResult += parseFloat(resultForRow) || 0;
        } else {
          const quantity = item?.quantity || 0;
          const rate = item?.rate || 0;
          const discount = item?.discount || 0;
          const itemTotal = quantity * rate * (1 - discount / 100);
          totalResult += itemTotal || 0;
        }
      });
    };

    // Calculate for both formData.productDetails and selectedProducts
    calculateForProducts(items);
    if (setSubtotalFormulaPrice) {
      setSubtotalFormulaPrice(totalResult);
    }
    return totalResult;
  }, [items, subTotalFormula]); //eslint-disable-line

  const addItems = () => {
    return (
      <div id="ignore">
        <div className="mt-3">
          <MultiSelect
            placeholder={`Select Part`}
            // onSearch={(e) => setSearchTerm(e)}
            AddOption={'+Add Part'}
            handleAddFunction={handleAddPart}
            doSearch={false}
            onChange={(e) => {
              handelpartChange(e);
            }}
            value={selectedParts}
            closeMenuOnSelect
            className="w-60 text-ellipsis"
            options={itemOptions?.map((el) => ({
              label: el?.name,
              value: el?.value,
            }))}
          />
        </div>
      </div>
    );
  };

  const customColumnField = allCustomColumns?.reduce((acc, col) => {
    const columnName = col?.columnName;
    if (columnName) {
      acc[columnName] = ''; // Set each column name as a key with empty string value
    }
    return acc;
  }, {});

  // New field for the New Entry
  const newFields = {
    name: '',
    quantity: 0,
    rate: 0,
    hsn: '',
    discount: 0,
    amount: 0,
    totalAmount: 0,
    manualEntry: false,
    customColumns: customColumnField,
  };

  const handleSubmit = async () => {
    const isVendorDifferent = selectedItem?.reduce((acc, curr) => {
      if (curr?.vendor_details?.length > 0) {
        const differentVendors = curr?.vendor_details?.filter(
          (el) => el?.vendor !== vendor
        );

        if (differentVendors?.length > 0) acc = true;
      }
      return acc;
    }, false);

    if (isVendorDifferent && vendor) {
      const decision = await customConfirm(
        'Selected item/s do not have the same vendor. Do you still want to continue ?',
        'Yes'
      );
      if (decision) {
        let tempItems = [];
        let tempItemTypes = [];
        selectedItem.forEach((item) => {
          const alreadyExists = items.find((el) => {
            return el.value === item.value;
          });
          if (!alreadyExists) {
            tempItems?.push({
              ...item,
              rate: item?.itemDetails?.rate,
              discount: item?.itemDetails?.discount,
              cgst: item?.itemDetails?.cgst,
              sgst: item?.itemDetails?.sgst,
              igst: item?.itemDetails?.igst,
              // customColumns: customInputValue,
            });
            tempItemTypes?.push(item?.name);
          } else {
            tempItems?.push(alreadyExists);
            tempItemTypes?.push(alreadyExists?.name);
          }
        });
        setItems(tempItems);
        setItemTypes(tempItemTypes);
      }
      // Anchor

      setSelectedItem([]);
    } else {
      let tempItems = [];
      let tempItemTypes = [];
      selectedItem.forEach((item) => {
        const alreadyExists = items.find((el) => {
          return el._id === item._id;
        });
        if (!alreadyExists) {
          tempItems?.push({
            ...item,
            rate: item?.itemDetails?.rate,
            discount: item?.itemDetails?.discount,
            cgst: item?.itemDetails?.cgst,
            sgst: item?.itemDetails?.sgst,
            igst: item?.itemDetails?.igst,
            // customColumns: customInputValue,
          });
          tempItemTypes?.push(item?.name);
        } else {
          tempItems?.push(alreadyExists);
          tempItemTypes?.push(alreadyExists?.name);
        }
      });
      setItems(tempItems);
      setItemTypes(tempItemTypes);
      setSelectedItem([]);
    }
    setShowModal(false);
    setSearchParams(
      (prev) => {
        prev.set('showModal', false);
        return prev;
      },
      { replace: true }
    );
  };

  useEffect(() => {
    if (allCustomColumns?.length > 0) {
      let allHead = [];
      allCustomColumns.forEach((row) => {
        const key = row?.columnName;
        if (!allHead.some((item) => item.key === key)) {
          allHead.push({
            headerName: row?.columnName?.toUpperCase(),
            key: row?.columnName,
            isAdditional: true,
            type: row?.columnType,
          });
        }
      });

      setAllHeader([...DEFAULT_PO_PRODUCT_DETAILS_HEADER, ...allHead]);
    }
  }, [allCustomColumns]);
  useEffect(() => {
    if (headerInfo?.headers?.length > 0) {
      const uniqueHeaders = [
        ...DEFAULT_PO_PRODUCT_DETAILS_HEADER,
        ...headerInfo.headers,
      ].reduce((acc, current) => {
        const x = acc.find((item) => item.key === current.key);
        if (!x) {
          return acc.concat([current]);
        } else {
          return acc;
        }
      }, []);
      setActiveHeader(uniqueHeaders);
    } else {
      setActiveHeader(DEFAULT_PO_PRODUCT_DETAILS_HEADER);
    }
  }, [headerInfo]);

  const renderTaxColumn = (headerName, taxType) => (
    <Table.Th className="!text-center min-w-36 border-r">
      <div className="flex items-center gap-2 justify-center">
        <span>{headerName}</span>
        {formData?.productColumnHideStatus?.[taxType] ? (
          <BiHide
            className="float-right text-base cursor-pointer"
            onClick={() => handleHideColumns(taxType)}
          />
        ) : (
          <BiShow
            className="float-right text-base cursor-pointer"
            onClick={() => handleHideColumns(taxType)}
          />
        )}
      </div>
      <Table.Row>
        <div className="flex gap-8 bg-[#FBFAFF]">
          <Table.Th>Rate</Table.Th>
          <Table.Th>Amount</Table.Th>
        </div>
      </Table.Row>
    </Table.Th>
  );

  return (
    <>
      {isOpenHeaders && (
        <HeaderReorder
          activeHeaderFromLocal={activeHeaderFromLocal}
          setIsOpenHeaders={setIsOpenHeaders}
          activeHeader={activeHeader}
          setActiveHeader={setActiveHeader}
          allHeader={allHeader}
        />
      )}
      {showModal && (
        <Modal
          title="Add items"
          onCloseModal={() => {
            setShowModal(false);
            setSearchParams(
              (prev) => {
                prev.set('showModal', false);
                return prev;
              },
              { replace: true }
            );
          }}
          onSubmit={handleSubmit}
        >
          {addItems}
        </Modal>
      )}
      {showCustomColumnModal && (
        <CustomColumnModal
          setLocalStorgeChange={setLocalStorgeChange}
          activeHeaderFromLocal={activeHeaderFromLocal}
          setShowCustomColumnModal={setShowCustomColumnModal}
          showCustomColumnModal={showCustomColumnModal}
          setColumnInputs={setColumnInputs}
          columnInputs={columnInputs}
          headers={allHeader || []}
        />
      )}
      <div className="flex w-full mb-3 mt-2">
        <div className="flex flex-col gap-3 w-full">
          <div className="w-full mr-4 mb-3">
            <div className="flex justify-between items-center ">
              <div className="flex gap-2 mt-10 mb-5">
                <div className="flex gap-1">
                  <h3>Product Details</h3>
                  <span className="text-red-500 text-xl  ml-1">*</span>
                </div>
              </div>
              <div className="flex gap-4 w-[12rem] items-center mt-3">
                <Toggle
                  value={showIgst}
                  onChange={(e) => setShowIgst(e.target.value)}
                />
                <label className="text-sm text-gray-500 font-semibold">
                  {!showIgst ? 'Show IGST ' : 'Show CGST/SGST'}
                </label>
              </div>
            </div>

            <section className="table-container mt-4 overflow-x-auto border rounded-md shadow-sm">
              <Table className="w-full border-b mb-[160px]">
                <Table.Head>
                  <Table.Row>
                    <Table.Th className={'border-r'}>
                      <div>Sr. no</div>
                    </Table.Th>

                    {activeHeader?.map((header, idx) => {
                      if (header?.headerName === 'ITEM DETAILS') {
                        return (
                          <Table.Th className="min-w-36 border-r" key={idx}>
                            <div>
                              <span>{header?.headerName}</span>
                              {formData?.productColumnHideStatus?.name && (
                                <BiHide
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('name');
                                  }}
                                />
                              )}
                              {!formData?.productColumnHideStatus?.name && (
                                <BiShow
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('name');
                                  }}
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }
                      if (header?.headerName === 'HSN/SAC CODE') {
                        return (
                          <Table.Th className="min-w-40 border-r" key={idx}>
                            <div>
                              <span>HSN/SAC Code</span>
                              {formData?.productColumnHideStatus?.hsn && (
                                <BiHide
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('hsn');
                                  }}
                                />
                              )}
                              {!formData?.productColumnHideStatus?.hsn && (
                                <BiShow
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('hsn');
                                  }}
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }

                      if (header?.headerName === 'QUANTITY') {
                        return (
                          <Table.Th className="min-w-32 border-r" key={idx}>
                            <div>
                              <span>Quantity</span>
                              {formData?.productColumnHideStatus?.quantity && (
                                <BiHide
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('quantity');
                                  }}
                                />
                              )}
                              {!formData?.productColumnHideStatus?.quantity && (
                                <BiShow
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('quantity');
                                  }}
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }

                      if (header?.headerName === 'UOM') {
                        return (
                          <Table.Th className="min-w-10 border-r" key={idx}>
                            <div className="flex gap-2">
                              <span>UOM</span>
                              {formData?.productColumnHideStatus?.uom && (
                                <BiHide
                                  className={` text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('uom');
                                  }}
                                />
                              )}
                              {!formData?.productColumnHideStatus?.uom && (
                                <BiShow
                                  className={` text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('uom');
                                  }}
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }

                      if (header?.headerName === 'RATE') {
                        return (
                          <Table.Th className="min-w-24 border-r" key={idx}>
                            <div>
                              <span>Rate</span>
                              {formData?.productColumnHideStatus?.rate && (
                                <BiHide
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('rate');
                                  }}
                                />
                              )}
                              {!formData?.productColumnHideStatus?.rate && (
                                <BiShow
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('rate');
                                  }}
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }

                      if (header?.headerName === 'DISCOUNT %') {
                        return (
                          <Table.Th className="min-w-36 border-r" key={idx}>
                            <div>
                              <span>Discount %</span>
                              {formData?.productColumnHideStatus?.discount && (
                                <BiHide
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('discount');
                                  }}
                                />
                              )}
                              {!formData?.productColumnHideStatus?.discount && (
                                <BiShow
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('discount');
                                  }}
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }

                      if (header?.headerName === 'AMOUNT') {
                        return (
                          <Table.Th className="min-w-32 border-r" key={idx}>
                            <div>
                              <span>Amount</span>
                              {formData?.productColumnHideStatus?.amount && (
                                <BiHide
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('amount');
                                  }}
                                />
                              )}
                              {!formData?.productColumnHideStatus?.amount && (
                                <BiShow
                                  className={`float-right text-base cursor-pointer`}
                                  onClick={() => {
                                    handleHideColumns('amount');
                                  }}
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }

                      if (!showIgst) {
                        if (header?.headerName === 'CGST') {
                          return renderTaxColumn('CGST', 'cgst');
                        }
                        if (header?.headerName === 'SGST') {
                          return renderTaxColumn('SGST', 'sgst');
                        }
                      }

                      if (showIgst) {
                        if (header?.headerName === 'IGST') {
                          return renderTaxColumn('IGST', 'igst');
                        }
                      }

                      if (header?.headerName === 'TOTAL AMOUNT') {
                        return (
                          <Table.Th className="min-w-39 border-r" key={idx}>
                            <div className="flex gap-1">
                              <span className="text-nowrap">
                                {header?.headerName}
                              </span>
                              {formData?.productColumnHideStatus
                                ?.totalAmount ? (
                                <BiHide
                                  className="float-right text-base cursor-pointer"
                                  onClick={() =>
                                    handleHideColumns('totalAmount')
                                  }
                                />
                              ) : (
                                <BiShow
                                  className="float-right text-base cursor-pointer"
                                  onClick={() =>
                                    handleHideColumns('totalAmount')
                                  }
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }

                      if (header?.isAdditional)
                        return (
                          <Table.Th
                            className={`!text-center mx-2 min-w-32 border-r`}
                            key={idx}
                          >
                            <span>{header?.headerName}</span>
                            {formData?.customColumnsHideStatus?.[
                              header?.key
                            ] && (
                              <BiHide
                                className={`float-right text-base cursor-pointer`}
                                onClick={() => {
                                  handleHideCustomColumns(header?.key);
                                }}
                              />
                            )}
                            {!formData?.customColumnsHideStatus?.[
                              header?.key
                            ] && (
                              <BiShow
                                className={`float-right text-base cursor-pointer`}
                                onClick={() => {
                                  handleHideCustomColumns(header?.key);
                                }}
                              />
                            )}
                          </Table.Th>
                        );
                    })}

                    <Table.Options
                      className={'border-b-2 p-2'}
                      onRearrangeColumn={() => setIsOpenHeaders(true)}
                      onAddCustomColumn={() => setShowCustomColumnModal(true)}
                    ></Table.Options>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  <ProductTableRow
                    items={items}
                    setItems={setItems}
                    columnInputs={columnInputs}
                    itemTypes={itemTypes}
                    newFields={newFields}
                    id={id}
                    // allParts={allParts}
                    showIgst={showIgst}
                    itemOptions={itemOptions}
                    fromOtherPage={fromOtherPage}
                    vendor={vendor}
                    activeHeader={activeHeader}
                  />
                </Table.Body>
              </Table>
            </section>
            <div className=" w-full !mt-10">
              <div className="flex flex-col">
                <div className="flex items-center gap-5 justify-start mb-8">
                  <Button
                    className={'h-7 text-[12px] w-[90px] !px-2'}
                    onClick={() => {
                      if (Object.keys(vendor)?.length > 0) {
                        // setItems((prev) => [...prev, newFields]);
                        setShowAddItemModal(true);
                      } else {
                        toast.error('Please select a vendor.');
                      }
                    }}
                    id="ignore"
                  >
                    + Add Items
                  </Button>
                  {defaultParam?.projectDefaults?.showProductMasterModal && (
                    <Button
                      className={'h-7 text-[12px] !px-2'}
                      onClick={() => {
                        if (Object.keys(vendor)?.length > 0) {
                          setShowMasterModal(true);
                        } else {
                          toast.error('Please select a vendor.');
                        }
                      }}
                      id="ignore"
                    >
                      + Add FG With Master
                    </Button>
                  )}

                  <Button
                    className={'h-7 text-[12px] !px-2'}
                    onClick={() => {
                      if (Object.keys(vendor)?.length > 0) {
                        setItems((prev) => [
                          ...prev,
                          { ...newFields, manualEntry: true },
                        ]);
                        // setShowAddItemModal(true);
                      } else {
                        toast.error('Please select a vendor.');
                      }
                    }}
                    id="ignore"
                  >
                    + Add Manual Items
                  </Button>
                </div>
              </div>
              <div className="flex justify-end text-sm">
                <div className="w-80 min-w-[18vw] bg-white p-4 rounded">
                  <div className="flex justify-between mb-2">
                    <span className="flex items-center gap-2">
                      Subtotal:{' '}
                      {formData?.productColumnHideStatus?.subTotal && (
                        <BiHide
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns('subTotal');
                          }}
                        />
                      )}
                      {!formData?.productColumnHideStatus?.subTotal && (
                        <BiShow
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns('subTotal');
                          }}
                        />
                      )}
                      <Popover
                        trigger={'click'}
                        content={
                          <div className="flex gap-2 items-center">
                            <Input
                              type="text"
                              placeholder="Enter Formula"
                              className={`text-[10px]`}
                              onChange={(e) =>
                                setSubTotalFormula(e.target.value)
                              }
                              value={subTotalFormula}
                            />
                            <IoSaveOutline
                              className="cursor-pointer"
                              size={20}
                              onClick={async () => {
                                if (
                                  subTotalFormula === '' &&
                                  !subTotalFormula
                                ) {
                                  toast.error('Please enter Formula');
                                  return;
                                }
                                const res = await updateDefaults({
                                  ...defaultParam,
                                  purchaseOrder: {
                                    ...defaultParam?.purchaseOrder,
                                    subTotalFormula: subTotalFormula,
                                  },
                                });
                                if (!res?.error) {
                                  toast.success('Formula Added successfully');
                                }
                              }}
                            />
                          </div>
                        }
                      >
                        <Button className="!text-[12px] !p-2" type="primary">
                          Formula
                        </Button>
                      </Popover>
                    </span>
                    <span>₹ {addCommaToAmount(getFormulaValue) || 0}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="flex items-center gap-2">
                      Total Tax Amount:
                      {formData?.productColumnHideStatus?.totalTaxAmount && (
                        <BiHide
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns('totalTaxAmount');
                          }}
                        />
                      )}
                      {!formData?.productColumnHideStatus?.totalTaxAmount && (
                        <BiShow
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns('totalTaxAmount');
                          }}
                        />
                      )}
                    </span>
                    <span>₹ {addCommaToAmount(totalTaxAmount) || 0}</span>
                  </div>
                  <div className="flex justify-between font-bold">
                    <span className="flex items-center gap-2">
                      Total Amout After Tax :
                      {formData?.productColumnHideStatus
                        ?.totalAmountAfterTax && (
                        <BiHide
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns('totalAmountAfterTax');
                          }}
                        />
                      )}
                      {!formData?.productColumnHideStatus
                        ?.totalAmountAfterTax && (
                        <BiShow
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns('totalAmountAfterTax');
                          }}
                        />
                      )}
                    </span>
                    <span>
                      ₹{' '}
                      {addCommaToAmount(getFormulaValue + totalTaxAmount) || 0}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductsInfo;
