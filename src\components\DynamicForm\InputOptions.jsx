import Multiselect from 'react-select';
import useFieldsAndTitle from '../../hooks/useFieldsAndTitle';
import TruncateString from '../global/TruncateString';
import AlphaNumeric from './InputTypes/AlphaNumeric';
import CheckBox from './InputTypes/CheckBox';
import Date from './InputTypes/Date';
import DropDown from './InputTypes/DropDown';
import Formula from './InputTypes/Formula';
import NumDropDown from './InputTypes/NumDropDown';
import Numeric from './InputTypes/Numeric';
import Text from './InputTypes/Text';

const multiSelectStyles = {
  control: (base) => ({
    ...base,
    minHeight: '20px',
    maxHeight: '26px',
    padding: '0px',
    margin: '0px',
    width: '100%',
    minWidth: '200px',
  }),
  container: (base) => ({
    ...base,
    height: '26px',
    padding: '0px',
    margin: '0px',
    width: '100%',
    minWidth: '200px',
  }),
  valueContainer: (base) => ({
    ...base,
    height: '26px',
    padding: '0px',
    margin: '0px',
  }),
  indicatorsContainer: (base) => ({
    ...base,
    height: '26px',
    padding: '0px',
    margin: '0px',
  }),

  multiValue: (base) => ({
    ...base,
    marginBlock: '0px',
    height: '20px',
  }),
  multiValueLabel: (base) => ({
    ...base,
    marginBlock: '0px',
    height: '20px',
    fontSize: '11px',
  }),
  input: (base) => ({
    ...base,
    margin: '0px',
  }),
};

const InputOptions = ({ data, state, processes }) => {
  const [horizontalFields, verticalFields, title] = useFieldsAndTitle(data);

  const types = data.Type.split('; ');

  // Input types TEXT, NUMERIC, ALPHANUMERIC, DATE, CHECKBOX, DROPDOWN, NUMDROPDOWN

  const switchFunction = (type, horIdx, verIdx, isDisabled) => {
    switch (type) {
      case 'TEXT':
        return (
          <Text
            data={data}
            state={state}
            type={type}
            horIdx={horIdx}
            verIdx={verIdx}
            isDisabled={isDisabled}
          />
        );
      case 'NUMERIC':
        return (
          <Numeric
            data={data}
            state={state}
            type={type}
            horIdx={horIdx}
            verIdx={verIdx}
            isDisabled={isDisabled}
          />
        );
      case 'ALPHANUMERIC':
        return (
          <AlphaNumeric
            data={data}
            state={state}
            type={type}
            horIdx={horIdx}
            verIdx={verIdx}
            isDisabled={isDisabled}
          />
        );
      case 'DATE':
        return (
          <Date
            data={data}
            state={state}
            type={type}
            horIdx={horIdx}
            verIdx={verIdx}
            isDisabled={isDisabled}
          />
        );
      case 'CHECKBOX':
        return (
          <CheckBox
            data={data}
            state={state}
            type={type}
            horIdx={horIdx}
            verIdx={verIdx}
            isDisabled={isDisabled}
          />
        );
      case 'DROPDOWN':
        return (
          <DropDown
            data={data}
            state={state}
            type={type}
            horIdx={horIdx}
            verIdx={verIdx}
            isDisabled={isDisabled}
          />
        );
      case 'FORMULA':
        return (
          <Formula
            data={data}
            state={state}
            type={type}
            horIdx={horIdx}
            verIdx={verIdx}
            isDisabled={isDisabled}
          />
        );
      case 'NUMDROPDOWN':
        return (
          <NumDropDown
            data={data}
            state={state}
            type={type}
            horIdx={horIdx}
            verIdx={verIdx}
            isDisabled={isDisabled}
          />
        );
      default:
        return <h2>Wrong input type</h2>;
    }
  };

  const {
    handleRowsChange,
    renderState,
    inputData,
    multiProcessData,
    handleSubProcessMultiSelect,
  } = state;

  const rowsValue = inputData?.[data?.Parameter]?.rows || '';

  return (
    <>
      <div className="mb-4 h-full w-full">
        <div className="flex w-full justify-between items-center mb-2">
          {/* param name */}

          <p className="text-sm text-black font-semibold w-full">
            <TruncateString length={25}>{data.Parameter}</TruncateString>
          </p>

          {/* input for determining number of rows */}

          {renderState && data?.['Is Table'] === 'yes' ? (
            <input
              placeholder="No of Rows"
              value={rowsValue}
              onChange={handleRowsChange}
              name={data?.Parameter}
              type="number"
              min={0}
              max={verticalFields?.length}
              className={`appearance-none rounded-lg w-1/5 min-w-[80px] outline-none border px-3 py-1`}
            />
          ) : null}
        </div>

        {/* multiprocess select for table */}

        <div
          className={`relative flex flex-row w-full${
            horizontalFields.length > 5 ? ' overflow-x-scroll' : ''
          }`}
        >
          {renderState && data?.['Is Table'] === 'yes' ? (
            <div className={`flex flex-col justify-end w-full`}>
              <h5 className="text-center flex mb-1">
                <TruncateString length={15}>Multi Process</TruncateString>
              </h5>
              {verticalFields?.map((verField, vIdx) => {
                const isDisabled = rowsValue && rowsValue < vIdx + 1;

                // get array of all selected process for row
                const valArray = multiProcessData?.[data?.Parameter]?.[vIdx];

                // find all selected multiprocess in that row
                const vals = processes?.filter((pro) =>
                  valArray?.includes(pro._id)
                );

                return (
                  <div
                    key={vIdx}
                    className={`min-w-[150px] border border-transparent mr-2
											${verticalFields?.length - 1 === vIdx ? '' : 'mb-2'}
											`}
                  >
                    <Multiselect
                      isMulti
                      closeMenuOnSelect={false}
                      value={vals?.map((item) => ({
                        label: item?.processName,
                        value: item?._id,
                      }))}
                      onChange={(e) => {
                        handleSubProcessMultiSelect(e, data, vIdx);
                      }}
                      isDisabled={isDisabled}
                      styles={multiSelectStyles}
                      options={processes
                        ?.filter((pro) => pro.isMultiProcess)
                        ?.map((option) => ({
                          label: option?.processName,
                          value: option?._id,
                        }))}
                    />
                  </div>
                );
              })}
            </div>
          ) : null}

          {/* input field according to param vertical and horizontal repeatition */}

          {horizontalFields.map((horField, horIdx) => (
            <div
              key={data.Parameter + horIdx.toString()}
              className={`flex flex-col justify-end w-full${
                horizontalFields.length > 1 &&
                horizontalFields.length - 1 !== horIdx
                  ? ' mr-2'
                  : ''
              }${horIdx === 0 ? ' sticky top-0 left-0 bg-white' : ''}`}
            >
              {/* titles if exists */}

              {horizontalFields.length > 1 && (
                <h5 className="text-center flex mb-1">
                  <TruncateString length={15}>{title[horIdx]}</TruncateString>
                </h5>
              )}

              {verticalFields.map((verField, verIdx) => {
                const isDisabled = rowsValue && rowsValue < verIdx + 1;
                return (
                  <div
                    key={data.Parameter + horIdx.toString() + verIdx.toString()}
                    className={
                      (data.Type !== 'CHECKBOX' && 'min-w-[150px]') + ' flex'
                    }
                  >
                    {/* input field according to param type */}

                    {switchFunction(
                      data.Type === 'CHECKBOX' ? 'CHECKBOX' : types[horIdx],
                      horIdx,
                      verIdx,
                      isDisabled
                    )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default InputOptions;
