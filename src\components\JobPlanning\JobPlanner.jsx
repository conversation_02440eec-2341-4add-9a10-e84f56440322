import {
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import { useLazyGetAllCuProjectsQuery } from '../../slices/CuProjectAPiSlice';
import { useLazyGetJobsBasedOnDateQuery } from '../../slices/createInputApiSlice';
import { useLazyGetAllMaintenanceByDateRangeQuery } from '../../slices/maintenanceApiSlice';
import Select from '../global/components/Select';
import DateTile from './components/DateTile';
import PlanningSidebar from './components/PlanningSidebar';
import { useGetYearsMonthsWeeks } from './utils/useGetYearsMonthsWeeks';

const JobPlanner = () => {
  const todayDate = new Date();
  const [year, setYear] = useState(todayDate.getFullYear());
  const [month, setMonth] = useState(todayDate.getMonth());
  const [calendar, setCalendar] = useState();
  const [openSideBar, setOpenSideBar] = useState(false);
  const [selectedDate, setSelectedDate] = useState();
  const [Requests, setRequests] = useState([]);

  const { weeks, months, years } = useGetYearsMonthsWeeks(year);

  const [getJobsBasedOnDate, { data: jobs = [] }] =
    useLazyGetJobsBasedOnDateQuery();

  const [getAllCuProjects, { data: cuData = {} }] =
    useLazyGetAllCuProjectsQuery();
  const { cuProjects: uncheduledJobs = [] } = cuData;

  const [getMaintenances] = useLazyGetAllMaintenanceByDateRangeQuery();

  useEffect(() => {
    if (month && year) {
      getJobsBasedOnDate({ month, year });
      getAllCuProjects({}, false);
    }
  }, [month, year, getJobsBasedOnDate, getAllCuProjects]);

  useEffect(() => {
    (async () => {
      const data = await getMaintenances({ date: todayDate }).unwrap();
      setRequests(data?.requests);
    })();
  }, [getMaintenances]); // eslint-disable-line

  useEffect(() => {
    const newMonth = months[month];
    if (newMonth && year) {
      const temp = [...Array(newMonth.days)].map((t, day) => {
        return new Date(`${year}-${newMonth.name}-${day + 1}`);
      });

      let tempArr = [];

      const startDay = temp[0].getDay();

      weeks.forEach((week, wIdx) => {
        if (wIdx < startDay) {
          tempArr.push(null);
        }
      });

      temp.forEach((date) => {
        tempArr.push(date);
      });

      setCalendar(tempArr);
    }
  }, [month, year, months]); //eslint-disable-line

  const calculatorFunction = (date) => {
    const isTodayDate =
      date.getDate() === todayDate.getDate() &&
      date.getMonth() === todayDate.getMonth() &&
      date.getFullYear() === todayDate.getFullYear();

    return {
      isTodayDate: isTodayDate,
    };
  };

  const handleDateTileClick = (jobs, unscheduledJobs, date) => {
    setSelectedDate({
      scheduled: jobs,
      unscheduled: unscheduledJobs,
      date: date,
    });
    setOpenSideBar(true);
  };

  return (
    <>
      <div className="w-full">
        <PlanningSidebar
          openSideBar={openSideBar}
          setOpenSideBar={setOpenSideBar}
          initJobs={selectedDate}
        />
        <div className="w-full rounded-new overflow- ">
          <div className="w-full flex items-center gap-x-10 px-10 py-3 bg-[#EBF3FF] border-b rounded-t-new">
            <span
              onClick={() =>
                setYear((prev) => {
                  if (prev === years[0]) return years[0];
                  return +prev - 1;
                })
              }
            >
              <ChevronDoubleLeftIcon className="h-5 w-5 hover:cursor-pointer" />
            </span>
            <span
              onClick={() =>
                setMonth((prev) => {
                  if (prev === 0) return 0;
                  return +prev - 1;
                })
              }
            >
              <ChevronLeftIcon className="h-5 w-5 hover:cursor-pointer" />
            </span>
            <Select
              value={month}
              onChange={(e) => setMonth(+e.target.value)}
              options={months.map((month, mIdx) => ({
                name: month.name,
                value: mIdx,
              }))}
              className="shadow-none"
            />
            <Select
              value={year}
              onChange={(e) => setYear(+e.target.value)}
              options={years.map((year) => ({ name: year, value: year }))}
              className="shadow-none"
            />
            <span
              onClick={() =>
                setMonth((prev) => {
                  if (prev === 11) return 11;
                  return +prev + 1;
                })
              }
            >
              <ChevronRightIcon className="h-5 w-5 hover:cursor-pointer" />
            </span>
            <span
              onClick={() =>
                setYear((prev) => {
                  if (prev === years[years?.length - 1])
                    return years[years?.length - 1];
                  return +prev + 1;
                })
              }
            >
              <ChevronDoubleRightIcon className="h-5 w-5 hover:cursor-pointer" />
            </span>
          </div>

          <div className="w-full grid grid-cols-7 bg-white rounded-b-new">
            {weeks.map((week) => {
              return (
                <p
                  key={week}
                  className={`text-center py-3 font-bold hover:cursor-pointer hover:bg-blue-light`}
                  // onClick={() => handleWeeksHoliday(week)}
                >
                  {week.slice(0, 3)}
                </p>
              );
            })}

            {calendar?.map((date, dIdx) => {
              if (!date) {
                return <p key={dIdx}></p>;
              }

              const { isTodayDate } = calculatorFunction(date);

              return (
                <DateTile
                  key={dIdx}
                  date={date}
                  isTodayDate={isTodayDate}
                  jobs={jobs}
                  unscheduledJobs={uncheduledJobs}
                  handleDateTileClick={handleDateTileClick}
                  requests={Requests}
                />
              );
            })}
          </div>
        </div>
      </div>
    </>
  );
};

export default JobPlanner;
