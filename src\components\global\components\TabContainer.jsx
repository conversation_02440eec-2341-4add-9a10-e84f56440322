const TabButton = ({
  children,
  isactive = false,
  onClick,
  disabled,
  className,
}) => {
  const selectedStyle = `px-4 py-1 text-sm font-medium text-gray-900 bg-gray-100 outline-none w-auto ${className}`;
  const nonSelectedStyle = `px-4 py-1 text-sm font-medium text-gray-900 bg-white outline-none w-auto ${className}`;
  return (
    <button
      className={isactive ? selectedStyle : nonSelectedStyle}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};
const TabContainer = ({ children, className }) => {
  return (
    <div
      className={`inline-flex rounded-lg shadow-sm overflow-hidden border border-[#d0d5dd] gap-x-px bg-[#d0d5dd] mb-10 ${className}`}
    >
      {children}
    </div>
  );
};

export { TabButton, TabContainer };
