import { Button, Dropdown, Empty, Spin } from 'antd';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  Eye,
  Plus,
  ShoppingCart,
  Trash,
  User,
} from 'lucide-react';
import { useContext, useEffect, useMemo, useState } from 'react';
import { AiOutlineStock } from 'react-icons/ai';
import { FaUserTie } from 'react-icons/fa';
import { GiReturnArrow } from 'react-icons/gi';
import { TbListCheck, TbPackageExport } from 'react-icons/tb';
import { useMediaQuery } from 'react-responsive';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import RequestStoreSidebar from '../SidebarComponents/RequestStoreSidebar';
import StockOutSidebarData from '../components/StockOutSidebarData';
import CustomToolTip from '../components/global/CustomToolTip';
import TruncateString from '../components/global/TruncateString';
import Header from '../components/global/components/Header';
import { InfoTooltip } from '../components/global/components/InfoTooltip';
import Pagination from '../components/global/components/Pagination';
import Spinner from '../components/global/components/Spinner';
import TablePopup from '../components/global/components/TablePopup';
import Tooltip from '../components/global/components/ToolTip';
import Labour from '../components/v3/HomeInventory/Labour';
import Returnable from '../components/v3/HomeInventory/Returnable';
import SelfStockOut from '../components/v3/HomeInventory/SelfStockOut';
import {
  // FormatDate,
  getLocalDate,
  getLocalDateTime,
  handleCanCreateStockOutOrder,
  mobileWidth,
} from '../helperFunction';
import useDebounceValue from '../hooks/useDebounceValue';
import useHeaderAndFooter from '../hooks/useHeaderAndFooter';
import { useLazyCheckCreateDeviceTypeQuery } from '../slices/createDeviceApiSlice';
import { useGetPosForStockOutQuery } from '../slices/createPoApiSlice';
import {
  useDeleteManyOutPagesMutation,
  useGetAllStockoutReqQuery,
  useLazyGetAllReturnableQuery,
  useLazyGetOutPagePagesQuery,
  useLazyQueryOrdersForExcelQuery,
  useGetFilterOptionsForOutPageQuery,
} from '../slices/outPageApiSlice';
import { Store } from '../store/Store';
import { PAGINATION_LIMIT } from '../utils/Constant';
import WithSelectAll from '../utils/HOC/WithSelectAll';
import { customConfirm } from '../utils/customConfirm';
import RightSidebar from './../components/global/components/RightSidebar';
import { FilterIcon, FilterV2 } from '../components/global/components/FilterV2';
import { Input } from 'antd';
import WOStockOut from '../components/v3/HomeInventory/WOStockOut';
import StockOutRequestList from '../components/v3/HomeInventory/StockOutRequestList';
import {
  FileExcelOutlined,
  ExportOutlined,
  DownloadOutlined,
} from '@ant-design/icons';

// const colors = {
//   Rent: 'bg-yellow-500 text-white',
//   Repair: 'bg-red-500 text-white',
//   Sales: 'bg-green-500 text-white',
//   'Personal Use': 'bg-cyan-500 text-white',
// };

// const exportBtn =
//   'bg-green-600 text-white rounded-l-md text-gray-button px-4 text-sm h-10 border-r border-gray-200 flex items-center gap-1 hover:bg-green-500 mb-3 transition-all ease-in-out duration-300';
const _btnStyling =
  'bg-gray-50 rounded-md text-gray-button px-2 !w-[9rem]  !ml-2 text-[13px] !h-[29px] border border-gray-200 flex items-center gap-1 hover:bg-gray-100 mb-3 mt-3 transition-all ease-in-out duration-300';
const _inputStyling =
  'border-2 border-[#E5E8EC] rounded-md focus:outline-none w-1/2';
// const blueBtnStyling =
//   'bg-blue-600 rounded-md text-white px-4 text-sm h-10 border border-gray-200 flex items-center gap-1 hover:bg-blue-500 mb-3 transition-all ease-in-out duration-300';

const _TaskTable = ({ clickedRow, setClickedRow }) => {
  const isMobile = useMediaQuery({ query: mobileWidth });
  const { defaults } = useContext(Store);

  const [SearchParams, setSearchParams] = useSearchParams({
    childtab: 'bom',
  });
  const { data: bompo, isLoading: isGetPoLoading } = useGetPosForStockOutQuery({
    query: { type: 'Inhouse' },
  });

  const [workOrderStockOutSidebar, setWorkOrderStockOutSidebar] =
    useState(null);
  const [rightSidebar, setRightSidebar] = useState(false);
  const workOrders = useMemo(() => {
    return (
      bompo?.map((wo) => {
        return {
          ...wo,
          ...handleCanCreateStockOutOrder(wo),
        };
      }) || []
    );
  }, [bompo]);

  return (
    <section>
      {isMobile && clickedRow && (
        <TablePopup
          onBack={() => setClickedRow(null)}
          isEdit={false}
          isDownload={false}
        >
          <div className="space-y-4 !text-[12px]">
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">
                {defaults?.defaultParam?.inventoryIdDefaults?.outPageCustomId?.toUpperCase()}
              </label>
              <p>{clickedRow?.chalaanNo}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">TASK ID</label>
              <p>
                {(clickedRow?.taskId?.customTaskId
                  ? `${clickedRow?.taskId?.customTaskId}(${clickedRow?.taskId?.taskId})`
                  : clickedRow?.taskId?.taskId) || '-'}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">BATCH</label>
              <div>
                {clickedRow?.batch?.map((item) => (
                  <p key={item}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">LOT NO.</label>
              <div>
                {clickedRow?.lot?.map((item) => (
                  <p key={item}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">RECEIVED BY</label>
              <div>
                {clickedRow?.receivedBy?.map((item) => (
                  <p key={item}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">QUANTITY</label>
              <div>
                {clickedRow?.quantity?.map((item, idx) => (
                  <p key={idx}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">REMAINING QUANTITY</label>
              <div>
                {clickedRow?.remainingQuantity?.map((item, idx) => (
                  <p key={idx}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">STORE WORKER</label>
              <p>{clickedRow?.worker?.name}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">STORE AREA</label>
              <div>
                {clickedRow?.storeArea?.map((item, idx) => (
                  <p key={idx}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">OUT TIME</label>
              <p>{getLocalDateTime(clickedRow?.createdAt)}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">FIFO/LIFO PASSED</label>
              {clickedRow?.items?.map((item) => (
                <p key={item?.inpageId?._id}>
                  {item?.inpageId?.part?.valuation ||
                    item?.inpageId?.product?.valuation ||
                    '-'}
                </p>
              ))}
            </div>
          </div>
        </TablePopup>
      )}

      <div className="flex gap-4 mb-3">
        <Button
          type={SearchParams?.get('childtab') === 'bom' ? 'primary' : 'default'}
          icon={<TbListCheck />}
          onClick={() => {
            setSearchParams(
              (prev) => {
                prev.set('childtab', 'bom');
                return prev;
              },
              {
                replace: true,
              }
            );
          }}
        >
          Work Order
        </Button>
        <Button
          type={
            SearchParams?.get('childtab') === 'request' ? 'primary' : 'default'
          }
          icon={<TbPackageExport />}
          onClick={() => {
            setSearchParams(
              (prev) => {
                prev.set('childtab', 'request');
                return prev;
              },
              {
                replace: true,
              }
            );
          }}
        >
          Stockout Requests
        </Button>
      </div>
      <RightSidebar
        openSideBar={rightSidebar}
        setOpenSideBar={setRightSidebar}
        scale={736}
      >
        <RequestStoreSidebar wo={workOrderStockOutSidebar} />
      </RightSidebar>
      {SearchParams?.get('childtab') === 'bom' ? (
        <>
          {isGetPoLoading ? (
            <>
              <div className="w-full flex justify-center items-center">
                <Spinner className="w-6 h-6 my-4" />
              </div>
            </>
          ) : (
            workOrders
              ?.filter((wo) => wo.canCreate)
              ?.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
              ?.map((wo, index) => {
                const totalStockedOut = wo?.previouslyCreatedStock?.reduce(
                  (total, item) => total + item.quantity,
                  0
                );
                const totalRaised = wo?.requestStore?.reduce(
                  (total, item) => total + item.stockOutQty,
                  0
                );
                return (
                  <motion.li
                    key={wo._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.01 }}
                    className="list-none hover:bg-gray-100 transition-all duration-200 mt-2"
                    onClick={() => {
                      setWorkOrderStockOutSidebar(wo);
                      setRightSidebar(true);
                    }}
                  >
                    <div className="px-3 sm:px-6 py-4 bg-white group-hover:bg-gray-50 transition-colors duration-200">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                        <div className="flex-1 w-full">
                          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full sm:w-auto">
                              <motion.div className="flex-shrink-0 p-2 bg-blue-50 rounded-lg text-sm">
                                #{wo?.workOrderId}
                              </motion.div>
                              <div className="w-full sm:w-auto">
                                <div className="flex items-center">
                                  <h3 className="text-base sm:text-lg font-medium text-gray-900">
                                    Work Order: {wo?.name}
                                  </h3>
                                </div>
                                <div className="mt-2 flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-6 text-xs sm:text-sm text-gray-500">
                                  <div className="flex items-center">
                                    <Calendar className="h-4 w-4 mr-1" />
                                    <span className="whitespace-nowrap">
                                      Deadline:{' '}
                                      {new Date(
                                        wo?.deadline
                                      ).toLocaleDateString('en-In')}
                                    </span>
                                  </div>
                                  <div className="flex items-center">
                                    <Clock className="h-4 w-4 mr-1" />
                                    <span className="whitespace-nowrap">
                                      Created: {getLocalDateTime(wo?.createdAt)}
                                    </span>
                                  </div>
                                  <div className="flex items-center">
                                    <User className="h-4 w-4 mr-1" />
                                    <span className="whitespace-nowrap">
                                      By: {wo?.createdBy || '-'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="mt-2">
                            <p
                              className={`${totalStockedOut < totalRaised ? 'text-red-400 font-semibold text-sm' : 'hidden'}`}
                            >
                              Stocked Out Quantity: {totalStockedOut}/
                              {totalRaised}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 w-full sm:w-auto">
                          <Button
                            onClick={() => {
                              setWorkOrderStockOutSidebar(wo);
                              setRightSidebar(true);
                            }}
                            className="inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 w-full sm:w-auto"
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                        </div>
                      </div>
                    </div>
                  </motion.li>
                );
              })
          )}
        </>
      ) : (
        <StockOutReqFromWO />
      )}
    </section>
  );
};

const StockOutReqFromWO = () => {
  const [openWoStockOut, setOpenWoStockOut] = useState(false);
  const [checkedRows, setCheckedRows] = useState([]);
  const [stockReqfilters, setStockReqfilters] = useState([]);
  const { data: stockoutreq = [], isLoading: isStockOutReqLoading } =
    useGetAllStockoutReqQuery({ filters: stockReqfilters });

  return (
    <>
      {openWoStockOut && (
        <WOStockOut
          openModal={openWoStockOut}
          setOpenModal={setOpenWoStockOut}
          items={checkedRows || []}
          setItems={setCheckedRows}
        />
      )}
      <StockOutRequestList
        checkedRows={checkedRows}
        setCheckedRows={setCheckedRows}
        setOpenWoStockOut={setOpenWoStockOut}
        stockoutreq={stockoutreq}
        isLoading={isStockOutReqLoading}
        filters={stockReqfilters}
        setFilters={setStockReqfilters}
      />
    </>
  );
};

const OutPage = ({
  handleCheckBoxChange,
  handleSelectAll,
  selectAll,
  checkedRows,
  setCheckedRows,
  rows,
  setRows,
}) => {
  const [isSelfStockoutModalOpen, setIsSelfStockoutModalOpen] = useState(false);
  const { data: filterOptions } = useGetFilterOptionsForOutPageQuery(
    {},
    { refetchOnMountOrArgChange: true }
  );
  const [showFilters, setShowFilters] = useState(true);
  const [filters, setFilters] = useState([]);

  const filterConfig = [
    {
      key: 'date',
      label: 'Date',
      path: 'createdAt',
      type: 'date',
    },
    {
      key: 'challanNo',
      label: 'Challan No',
      path: 'chalaanNo',
      type: 'multiSelect',
      options: filterOptions?.challanNos || [],
    },
    {
      key: 'parts',
      label: 'Parts',
      path: 'part',
      type: 'multiSelect',
      isObjectId: true,
      options: filterOptions?.parts || [],
    },
    {
      key: 'products',
      label: 'Products',
      path: 'product',
      type: 'multiSelect',
      isObjectId: true,
      options: filterOptions?.products || [],
    },
    {
      key: 'quantity',
      label: 'Quantity',
      path: 'quantity',
      type: 'multiSelect',
      options: filterOptions?.quantities || [],
    },
    {
      key: 'store',
      label: 'Store',
      path: 'store',
      type: 'multiSelect',
      isObjectId: true,
      options: filterOptions?.stores || [],
    },
  ];

  const [SearchParams, setSearchParams] = useSearchParams({
    tab: 'stockout',
  });

  const [clickedRow, setClickedRow] = useState(null);
  const isMobile = useMediaQuery({ query: mobileWidth });
  const navigate = useNavigate();
  const [getOutPagePages, pageData] = useLazyGetOutPagePagesQuery();

  const [getAllReturnable, { data: returnableData }] =
    useLazyGetAllReturnableQuery();

  const { defaults, mqttClient } = useContext(Store);
  const [columns, setColumns] = useState([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [totalPages, setTotalPages] = useState('');
  const [totalResults, setTotalResults] = useState('');
  const [topics, setTopics] = useState([]);
  const [searchTerm, setSearchTerm] = useState(''); // temporary removed
  const [rightBar, setRightBar] = useState(null);

  const [checkCreateDeviceType] = useLazyCheckCreateDeviceTypeQuery();
  const [deleteManyOutPages] = useDeleteManyOutPagesMutation();
  const [getExcelOrders, { isFetching: isCsvLoading }] =
    useLazyQueryOrdersForExcelQuery();

  useEffect(() => {
    if (SearchParams.get('kanban') === 'true' && !SearchParams.get('orderId')) {
      navigate(
        `/inventory/outpage/stockout?kanban=true&department=${SearchParams.get('department')}&page=${SearchParams.get('page')}&refType=${SearchParams.get('refType')}`
      );
    }
    if (SearchParams.get('kanban') === 'true' && SearchParams.get('orderId')) {
      navigate(
        `/inventory/outpage/stockout?kanban=true&department=${SearchParams.get(
          'department'
        )}&page=${SearchParams.get(
          'page'
        )}&refType=${SearchParams.get('refType')}&orderId=${SearchParams.get('orderId')}`
      );
    }
  }, [SearchParams.get('kanban')]); //eslint-disable-line

  const { header, footer } = useHeaderAndFooter({});

  const debounceSearch = useDebounceValue(searchTerm) || '';

  const handleDeleteAll = async () => {
    const idsToDelete = checkedRows.map((item) => item._id);
    const confirmation = await customConfirm(
      'Are you sure you want to delete the selected entries?',
      'delete'
    );
    if (confirmation) {
      const res = await deleteManyOutPages({ ids: idsToDelete });
      if (res?.data?.message) {
        toast.success(res?.data?.message);
      }
      setCheckedRows([]);
    }
  };

  useEffect(() => {
    getOutPagePages({
      page,
      limit,
      debounceSearch,
      filters,
    })
      .unwrap()
      .catch((err) => {
        toast.error(err?.data?.message, {
          theme: 'colored',
          position: 'top-right',
          toastId: err?.data?.message,
        });
        return;
      });
  }, [page, limit, getOutPagePages, debounceSearch, filters]);

  useEffect(() => {
    getAllReturnable().unwrap();
  }, [getAllReturnable]);

  useEffect(() => {
    setColumns([...(defaults?.defaultParam?.outPageColumns || [])]);

    setRows(pageData?.data?.results);
    setTotalPages(pageData?.data?.totalPages);
    setTotalResults(pageData?.data?.totalResults);
  }, [defaults, pageData, setRows]);

  useEffect(() => {
    const fetchDeviceIds = async () => {
      const res2 = await checkCreateDeviceType({ type: 'weight' }).unwrap();
      setTopics(
        res2?.createDevice?.fields
          ?.filter((field) => field.type === 'value')
          .map((field) => field.topic)
      );
    };
    fetchDeviceIds();
  }, [checkCreateDeviceType]);

  useEffect(() => {
    if (mqttClient?.unsubscribe && topics?.length > 0) {
      mqttClient?.unsubscribe(topics);
    }
  }, [topics, mqttClient]);

  const handleRightSidebarOpen = async (e) => {
    if (event.target.tagName.toLowerCase() !== 'input') setRightBar(e?._id);
  };

  const exportToExcel = (data) => {
    if (!data || !data.length) return;

    const excelData = [];
    const merges = [];
    let rowIndex = 1;
    let challanSrNo = 1;

    data.forEach((parent) => {
      const startRow = rowIndex;

      if (!parent.items || parent.items.length === 0) {
        excelData.push({
          'Sr No.': challanSrNo,
          'Challan ID': parent.chalaanNo,
          Date: new Date(parent.createdAt).toLocaleDateString(),
          Items: '-',
          Batch: '-',
          Quantity: '-',
          UOM: '-',
        });
        challanSrNo++;
        rowIndex++;
        return;
      }

      parent.items.forEach((child, idx) => {
        const itemName = child?.name || '-';
        const batch =
          child?.batchNo || child?.lotNo
            ? `${child?.batchNo ?? ''} (${child?.lotNo ?? ''})`
            : '-';
        const qty = child?.quantity || '0';
        const uom = child?.uom || '-';

        excelData.push({
          'Sr No.': idx === 0 ? challanSrNo : '',
          'Challan ID': parent.chalaanNo,
          Date: new Date(parent.createdAt).toLocaleDateString(),
          Items: itemName,
          Batch: batch,
          Quantity: qty,
          UOM: uom,
        });
        rowIndex++;
      });

      const endRow = rowIndex - 1;

      if (endRow > startRow) {
        merges.push({ s: { r: startRow, c: 0 }, e: { r: endRow, c: 0 } }); // Sr No.
        merges.push({ s: { r: startRow, c: 1 }, e: { r: endRow, c: 1 } }); // Challan No
        merges.push({ s: { r: startRow, c: 2 }, e: { r: endRow, c: 2 } }); // Date
      }

      challanSrNo++;
    });

    const worksheet = XLSX.utils.json_to_sheet(excelData);
    worksheet['!merges'] = merges;

    worksheet['!cols'] = [
      { wch: 10 }, // Sr No.
      { wch: 15 }, // Challan ID
      { wch: 12 }, // Date
      { wch: 30 }, // Items
      { wch: 20 }, // Batch
      { wch: 10 }, // Quantity
      { wch: 10 }, // UOM
    ];

    const range = XLSX.utils.decode_range(worksheet['!ref']);

    for (let r = 0; r <= range.e.r; r++) {
      for (let c = 0; c <= range.e.c; c++) {
        const cellAddress = XLSX.utils.encode_cell({ r, c });
        if (!worksheet[cellAddress]) continue;

        if (typeof worksheet[cellAddress] !== 'object') {
          worksheet[cellAddress] = { v: worksheet[cellAddress] };
        }

        if (r === 0) {
          worksheet[cellAddress].s = {
            font: { bold: true },
            alignment: { horizontal: 'center', vertical: 'center' },
          };
        } else {
          if (c === 0 || c === 1 || c === 2 || c === 4 || c === 5 || c === 6) {
            worksheet[cellAddress].s = {
              alignment: { horizontal: 'center', vertical: 'center' },
            };
          }
        }
      }
    }

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Stock Out Data');
    XLSX.writeFile(workbook, 'StockOut_Export.xlsx');
  };

  const fetchAndExport = async () => {
    const res = await getExcelOrders();
    exportToExcel(res?.data || []);
  };

  const exportItems = [
    {
      key: 1,
      label: 'Export CSV',
      onClick: () => exportToExcel(rows),
      icon: <FileExcelOutlined />,
    },
    {
      key: 2,
      label: 'Export All',
      onClick: fetchAndExport,
      icon: <ExportOutlined />,
    },
  ];

  return (
    <>
      {isMobile && clickedRow && (
        <TablePopup
          onBack={() => setClickedRow(null)}
          isEdit={false}
          isDownload={false}
        >
          <div className="space-y-4 text-xs">
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">
                {defaults?.defaultParam?.inventoryIdDefaults?.outPageCustomId?.toUpperCase()}
              </label>
              <p>{clickedRow?.chalaanNo}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">TASK ID</label>
              <p>
                {(clickedRow?.taskId?.customTaskId
                  ? `${clickedRow?.taskId?.customTaskId}(${clickedRow?.taskId?.taskId})`
                  : clickedRow?.taskId?.taskId) || '-'}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">BATCH</label>
              <div>
                {clickedRow?.batch?.map((item) => (
                  <p key={item}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">LOT NO.</label>
              <div>
                {clickedRow?.lot?.map((item) => (
                  <p key={item}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">RECEIVED BY</label>
              <div>
                {clickedRow?.receivedBy?.map((item) => (
                  <p key={item}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">QUANTITY</label>
              <div>
                {clickedRow?.quantity?.map((item, idx) => (
                  <p key={idx}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">REMAINING QUANTITY</label>
              <div>
                {clickedRow?.remainingQuantity?.map((item, idx) => (
                  <p key={idx}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">STORE WORKER</label>
              <p>{clickedRow?.worker?.name}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">STORE AREA</label>
              <div>
                {clickedRow?.storeArea?.map((item, idx) => (
                  <p key={idx}>{item}</p>
                ))}
              </div>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">OUT TIME</label>
              <p>{getLocalDateTime(clickedRow?.createdAt)}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">FIFO/LIFO PASSED</label>
              {clickedRow?.items?.map((item) => (
                <p key={item?.inpageId?._id}>
                  {item?.inpageId?.part?.valuation ||
                    item?.inpageId?.product?.valuation ||
                    '-'}
                </p>
              ))}
            </div>
          </div>
        </TablePopup>
      )}
      <RightSidebar
        title="Stock Out"
        openSideBar={!!rightBar}
        setOpenSideBar={() => setRightBar(null)}
        scale={736}
      >
        <>
          <StockOutSidebarData
            defaults={defaults}
            chalaanNo={rightBar}
            columns={columns}
          />
        </>
      </RightSidebar>
      <div>
        <div className="flex items-center justify-between">
          <Header
            title="Stock Out"
            description=""
            infoTitle="Welcome to the Out Page"
            infoDesc="Your gateway to efficient item checkout."
            paras={[
              'Here, you can seamlessly complete the checkout process for items. You have two convenient options: create a cart directly from the Stock Navigator or generate a cart by scanning QR codes of items.',
              'During checkout, real-time stock entries are updated, guaranteeing accurate inventory tracking. Plus, you can easily generate a detailed Stock Out receipt for your order, ensuring transparency and traceability in your operations.',
              'Simplify your item checkout process and enhance inventory management with our intuitive Out Page.',
              'Streamline item checkout, track real-time stock, and generate detailed Stock Out receipts',
            ]}
          />
          <div className="flex w-[80%] items-center justify-end gap-3">
            <Link to="/inventory/outpage/stockout">
              <Button
                type="primary"
                icon={<ShoppingCart className="w-4 h-4" />}
              >
                Create Cart
              </Button>
            </Link>
          </div>
        </div>
        <div className="flex justify-between items-center w-full rounded-tl-lg rounded-tr-lg mt-2">
          {!isMobile && (
            <div className="task-manager mb-3 flex items-center justify-start gap-x-3">
              <Button
                className={`${SearchParams.get('tab') === 'task' ? 'bg-blue-primary text-white' : ''}`}
                onClick={() => {
                  setSearchParams(
                    (prev) => {
                      prev.set('tab', 'task');
                      return prev;
                    },
                    { replace: true }
                  );
                }}
              >
                Pending Orders
                <InfoTooltip id="stockout-taskmanager">
                  Work Order details with item that need to be stock out.
                </InfoTooltip>
              </Button>
              <Button
                icon={<AiOutlineStock />}
                className={`${SearchParams.get('tab') === 'stockout' ? 'bg-blue-primary text-white' : ''}`}
                onClick={() => {
                  setSearchParams(
                    (prev) => {
                      prev.set('tab', 'stockout');
                      return prev;
                    },
                    {
                      replace: true,
                    }
                  );
                }}
              >
                Stocks
              </Button>
              <Button
                icon={<GiReturnArrow />}
                className={`${SearchParams.get('tab') === 'returnable' ? 'bg-blue-primary text-white' : ''}`}
                onClick={() => {
                  setSearchParams(
                    (prev) => {
                      prev.set('tab', 'returnable');
                      return prev;
                    },
                    {
                      replace: true,
                    }
                  );
                }}
              >
                Returnable
              </Button>
              <Button
                icon={<FaUserTie />}
                className={`${SearchParams.get('tab') === 'labour' ? 'bg-blue-primary text-white' : ''}`}
                onClick={() => {
                  setSearchParams(
                    (prev) => {
                      prev.set('tab', 'labour');
                      return prev;
                    },
                    {
                      replace: true,
                    }
                  );
                }}
              >
                Labour
              </Button>
              {/* <Button
                className="text-center px-2 bg-[#14BA6D] disabled:bg-gray-200 text-white"
                onClick={() => setIsSelfStockoutModalOpen(true)}
              >
                Internal Stock Transfer
              </Button> */}
            </div>
          )}

          {SearchParams.get('tab') === 'labour' ? (
            <Button
              icon={<Plus />}
              onClick={() => {
                setSearchParams((prev) => {
                  prev.set('isAdd', 'labour');
                  return prev;
                });
              }}
            >
              Add Labour
            </Button>
          ) : SearchParams.get('tab') === 'task' ? null : (
            <>
              <div className="flex items-center gap-4 mb-4 mt-4">
                <div className="flex items-center gap-x-2">
                  <FilterIcon
                    showFilters={showFilters}
                    setShowFilters={setShowFilters}
                  />
                  <Input.Search
                    placeholder="Search"
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                {!isMobile && (
                  <div className="flex items-center">
                    <CustomToolTip
                      tooltipId="delete-tooltip"
                      content="Delete"
                      place="top"
                      effect="solid"
                      className={`bg-black text-white p-2 rounded-md `}
                    >
                      <button
                        onClick={handleDeleteAll}
                        className={`p-2.5 rounded-lg transition-all duration-200 hidden`}
                      >
                        <Trash className={`h-5 w-5 `} />
                      </button>
                    </CustomToolTip>

                    <Dropdown
                      trigger={['click']}
                      placement="bottomRight"
                      arrow
                      menu={{ items: exportItems }}
                    >
                      <Button
                        disabled={pageData?.isLoading || isCsvLoading}
                        loading={isCsvLoading}
                        icon={<DownloadOutlined />}
                        color="green"
                        variant="solid"
                      >
                        Export
                      </Button>
                    </Dropdown>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {SearchParams?.get('tab') === 'stockout' ? (
          <>
            <section className="w-full" id="print-stockout">
              {header()}
              <FilterV2
                showFilters={showFilters}
                config={filterConfig}
                setFilters={setFilters}
              />
              <div className="overflow-x-auto shadow-md rounded-md">
                <table className="min-w-full border-collapse border text-xs">
                  <thead className="bg-gray-50 text-gray-700 text-xs">
                    <tr>
                      {!isMobile && (
                        <th className="p-2 border border-gray-200 text-left text-sm">
                          {checkedRows?.length > 0 ? (
                            <div className="flex items-center text-sm">
                              <input
                                type="checkbox"
                                className="mr-1 h-4 w-4"
                                checked={selectAll}
                                onChange={(e) => handleSelectAll(e)}
                              />
                              <span>Select All</span>
                            </div>
                          ) : (
                            ''
                          )}
                        </th>
                      )}
                      {!isMobile && (
                        <th className="p-2 border border-gray-200 text-left text-sm">
                          Task ID
                        </th>
                      )}
                      <th className="p-2 border border-gray-200 text-left text-sm">
                        <div className="flex items-center">
                          <div>Date</div>
                        </div>
                      </th>
                      <th className="p-2 border border-gray-200 text-left text-sm">
                        {defaults?.defaultParam?.inventoryIdDefaults
                          ?.outPageCustomId || 'Chalaan No.'}
                      </th>

                      <th className="p-2 border border-gray-200 text-left text-sm">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div>Items</div>
                          </div>
                          <div className="gap-x-4 justify-end hidden lg:flex">
                            <span className="w-28 text-center">Quantity</span>
                            <span className="w-36 text-center">UOM</span>
                          </div>
                        </div>
                      </th>
                      {!isMobile && (
                        <th className="p-2 border border-gray-200 text-left text-sm">
                          Usage
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="text-xs">
                    {pageData.isLoading ? (
                      <tr>
                        <td
                          colSpan={100}
                          className="py-28 bg-gray-50 text-center text-xs"
                        >
                          <Spin />
                        </td>
                      </tr>
                    ) : rows?.length <= 0 ? (
                      <tr>
                        <td
                          colSpan={100}
                          className="py-28 bg-gray-50 text-center text-xs"
                        >
                          <Empty />
                        </td>
                      </tr>
                    ) : (
                      rows?.map((e, eIdx) => (
                        <tr
                          key={eIdx}
                          className="hover:bg-gray-50 bg-white cursor-pointer text-xs border-t border-gray-200"
                          onClick={() => {
                            isMobile
                              ? setClickedRow(e)
                              : handleRightSidebarOpen(e);
                          }}
                        >
                          {!isMobile && (
                            <>
                              <td className="p-2 border border-gray-200 text-xs">
                                <input
                                  type="checkbox"
                                  onChange={(event) =>
                                    handleCheckBoxChange(event, e)
                                  }
                                  checked={checkedRows.includes(e)}
                                  className="h-4 w-4"
                                />
                              </td>
                              <td className="p-2 border border-gray-200 text-xs">
                                <TruncateString length={15}>
                                  {(e?.taskId?.customTaskId
                                    ? `${e?.taskId?.customTaskId}(${e?.taskId?.taskId})`
                                    : e?.taskId?.taskId) || '-'}
                                </TruncateString>
                              </td>
                            </>
                          )}
                          <td className="p-2 border border-gray-200 text-xs">
                            {getLocalDate(e?.createdAt)}
                          </td>
                          <td className="p-2 border border-gray-200 text-xs">
                            <button className="text-blue-600 underline">
                              <TruncateString length={10}>
                                {e.chalaanNo || '-'}
                              </TruncateString>
                            </button>
                          </td>
                          <td className="p-2 border border-gray-200 text-xs">
                            {e.items?.map((item, idx) => (
                              <div
                                key={idx}
                                className="flex justify-between items-center bg-gray-50 border rounded-md px-2 py-1 mb-1 text-xs"
                              >
                                <p
                                  key={idx}
                                  className="mb-1 font-semibold text-xs"
                                >
                                  {item?.name?.length <= 80 ? (
                                    item?.name
                                  ) : (
                                    <Tooltip text={item?.name}>
                                      {item?.name?.substring(0, 80) + '...'}
                                    </Tooltip>
                                  )}
                                </p>
                                <div className="flex items-center flex-wrap gap-2 ml-4">
                                  <span className="bg-blue-100 text-center text-blue-800 px-2 py-0.5 rounded text-xs font-semibold md:min-w-32">
                                    <span className="lg:hidden">Qty:</span>{' '}
                                    {item.quantity}
                                  </span>
                                  <span className="bg-purple-100 text-center text-purple-800 px-2 py-0.5 rounded text-xs font-semibold md:min-w-32 lg:inline-block hidden">
                                    {item?.uom?.length > 15
                                      ? `${item?.uom?.substring(0, 15)}...`
                                      : item?.uom || '-'}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </td>
                          {!isMobile && (
                            <td className="p-2 border border-gray-200 text-xs">
                              {e.items?.map((item, idx) => (
                                <div key={idx} className="mb-1 text-xs">
                                  {item?.returnable?.isReturnable ? (
                                    <>
                                      {`Returnable (${getLocalDate(
                                        item?.returnable?.returnDate
                                      )})`}
                                    </>
                                  ) : (
                                    <Tooltip
                                      text={`${item?.usage?.type} (${item?.usage?.id ? '' : '-'})`}
                                    >
                                      {item?.usage?.type?.length > 10
                                        ? `${item?.usage?.type.substring(0, 10)}...`
                                        : item?.usage?.type}
                                      (-)
                                    </Tooltip>
                                  )}
                                </div>
                              ))}
                            </td>
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {footer()}
            </section>
            <Pagination
              limit={limit}
              page={page}
              totalPages={totalPages}
              totalResults={totalResults}
              setPage={setPage}
              setLimit={setLimit}
              className={`w-full`}
            />
          </>
        ) : SearchParams?.get('tab') === 'returnable' ? (
          <Returnable returnableData={returnableData} />
        ) : SearchParams?.get('tab') === 'labour' ? (
          <Labour />
        ) : (
          // FOR NOW WE DirectLy Show the StockOut Request List
          // <TaskTable clickedRow={clickedRow} setClickedRow={setClickedRow} />
          <StockOutReqFromWO />
        )}
      </div>
      <SelfStockOut
        isSelfStockoutModalOpen={isSelfStockoutModalOpen}
        setIsSelfStockoutModalOpen={setIsSelfStockoutModalOpen}
      />
    </>
  );
};

export default WithSelectAll(OutPage);
