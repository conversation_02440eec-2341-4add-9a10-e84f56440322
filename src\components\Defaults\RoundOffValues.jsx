import { useEffect, useState } from 'react';
import {
  useEditRoundOffValuesMutation,
  useGetRoundOffValuesQuery,
} from '../../slices/roundOffValuesApiSlice';
import Button from '../global/components/Button';
import { toast } from 'react-toastify';
import { unCamelCaseString } from '../../helperFunction';

function RoundOffValues() {
  const [inputData, setInputData] = useState({});

  const { data } = useGetRoundOffValuesQuery();
  const [editRoundOffValues, { isLoading: isLoadingEdit }] =
    useEditRoundOffValuesMutation();

  useEffect(() => {
    if (data) setInputData(data);
  }, [data]);

  const handleSubmit = async (e) => {
    e?.preventDefault();
    const data = await editRoundOffValues({ data: inputData }).unwrap();
    if (data) {
      toast.success('Round off values updated succesfully');
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className={`w-full ${isLoadingEdit ? 'animate-pulse' : ''} `}
    >
      <h2>Round Off Values</h2>
      {Object?.entries(inputData || {}).map(([model, modelVal], mIdx) => {
        if (typeof modelVal !== 'object') return null;
        return (
          <div key={mIdx} className="border-b px-5 py-2">
            <p className="text-lg font-semibold">{unCamelCaseString(model)}</p>
            <div className="grid responsive-grid-checkbox px-2 gap-1">
              {Object?.keys(modelVal || {})?.map((field) => (
                <label
                  key={field}
                  htmlFor={model + field}
                  className="flex w-full gap-2 items-center cursor-pointer"
                >
                  <input
                    type="checkbox"
                    name={field}
                    id={model + field}
                    checked={inputData?.[model]?.[field] || false}
                    onChange={(e) =>
                      setInputData((prev) => ({
                        ...(prev || {}),
                        [model]: {
                          ...(prev?.[model] || {}),
                          [field]: e?.target?.checked,
                        },
                      }))
                    }
                  />
                  {unCamelCaseString(field)}
                </label>
              ))}
            </div>
          </div>
        );
      })}
      <div className="mt-5 flex w-full justify-end">
        <Button type="submit">Update</Button>
      </div>
    </form>
  );
}

export default RoundOffValues;
