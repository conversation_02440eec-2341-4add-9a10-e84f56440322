import { Briefcase, TrashIcon } from 'lucide-react';
import { ReactComponent as JpgPng } from '../../assets/svgs/pdfsvg.svg';
import { getLocalDateTime } from '../../helperFunction';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import Pagination from '../global/components/Pagination';
import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import Tooltip from '../global/components/ToolTip';
import UploadButton from '../UploadButton';

const AssemblyForm = ({
  forModal,
  isMobile,
  checkedRows,
  selectAll,
  handleSelectAll,
  rows,
  setField,
  setType,
  handleCheckBoxChange,
  setIsCopy,
  limit,
  page,
  setPage,
  setLimit,
  isOpen,
  isLoadingAssemblyForm,
  handleAssemblyDelete,
  handleAssemblyEdit,
  assemblyFormData,
  isUpdateAssembly,
  handleAssemblyStepAdd,
  isLoadingUpdateAssembly,
  setAddAssemblyFormData,
  handleAssemblyModalClose,
  isLoadingCreateAssembly,
  handleAssemblySave,
  addAssemblyFormData,
  handleStepDelete,
  assemblyFileChangeHandler,
  handleRemovePdf,
  stepCount,
  assemblyFormSteps,
  setAssembyFormSteps,
}) => {
  return (
    <div className=" w-full">
      {!forModal && (
        <div>
          {isLoadingAssemblyForm ? (
            <Spinner />
          ) : (
            <div className="w-full">
              <Table className="w-full">
                <Table.Head>
                  <Table.Row>
                    {!isMobile && (
                      <Table.Th>
                        {checkedRows.length > 0 ? (
                          <div>
                            <input
                              type="checkbox"
                              className="mr-2"
                              checked={selectAll}
                              onChange={(e) => handleSelectAll(e)}
                            />
                            Select All
                          </div>
                        ) : (
                          ''
                        )}
                      </Table.Th>
                    )}
                    <Table.Th
                      onClick={() => {
                        setField('createdAt');
                      }}
                    >
                      <div className="flex">
                        <div>Date</div>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-4 h-4 ml-2 cursor-pointer"
                          onClick={() => {
                            setType((prev) => {
                              if (prev === 'desc') {
                                return 'aesc';
                              } else {
                                return 'desc';
                              }
                            });
                          }}
                        >
                          {' '}
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M19.5 13.5L12 21m0 0l-7.5-7.5M12 21V3"
                          />
                        </svg>
                      </div>
                    </Table.Th>
                    <Table.Th
                      onClick={() => {
                        setField('formName');
                      }}
                    >
                      <div className="flex">
                        <div>Form Name</div>

                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-4 h-4 ml-2 cursor-pointer"
                          onClick={() => {
                            setType((prev) => {
                              if (prev === 'desc') {
                                return 'aesc';
                              } else {
                                return 'desc';
                              }
                            });
                          }}
                        >
                          {' '}
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M19.5 13.5L12 21m0 0l-7.5-7.5M12 21V3"
                          />
                        </svg>
                      </div>
                    </Table.Th>
                    <Table.Th>Category</Table.Th>
                    <Table.Th></Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {rows?.map((assembly, aidx) => {
                    return (
                      <Table.Row key={aidx} className="hover:bg-gray-100">
                        {!isMobile && (
                          <Table.Td>
                            <input
                              type="checkbox"
                              onChange={(event) =>
                                handleCheckBoxChange(event, assembly)
                              }
                              checked={checkedRows.includes(assembly)}
                            />
                          </Table.Td>
                        )}
                        <Table.Td>
                          {getLocalDateTime(assembly?.createdAt)}
                        </Table.Td>
                        <Table.Td className={'md:!min-w-[24rem] md:!w-[24rem]'}>
                          {assembly?.formName?.length > 55 ? (
                            <Tooltip text={assembly?.formName}>
                              {assembly?.formName?.slice(0, 55) + '...'}
                            </Tooltip>
                          ) : (
                            assembly?.formName
                          )}
                        </Table.Td>
                        <Table.Td>{assembly?.category || '-'}</Table.Td>
                        <Table.Options
                          className={'bg-white'}
                          onDelete={() => {
                            handleAssemblyDelete(assembly._id);
                          }}
                          onEdit={() => {
                            handleAssemblyEdit(assembly._id);
                          }}
                          onCopy={() => {
                            handleAssemblyEdit(assembly._id);
                            setIsCopy(true);
                          }}
                        />
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
              <Pagination
                limit={limit}
                page={page}
                totalPages={assemblyFormData?.totalPages}
                totalResults={assemblyFormData?.totalResults}
                setPage={setPage}
                setLimit={setLimit}
                className={`w-full`}
              />
            </div>
          )}
        </div>
      )}
      {isOpen && (
        <Modal
          title={
            !isUpdateAssembly ? 'Create Assembly Forms' : 'Edit Assembly Forms'
          }
          svg={<Briefcase className="h-8 w-8" />}
          description={
            'Create and Update Assembly Form with the required steps'
          }
          onCloseModal={handleAssemblyModalClose}
          btnIsLoading={
            isUpdateAssembly ? isLoadingUpdateAssembly : isLoadingCreateAssembly
          }
          onSubmit={handleAssemblySave}
          onAdd={{
            label: 'Add',
            func: [handleAssemblyStepAdd],
            step: [0],
          }}
        >
          {() => (
            <div>
              <section>
                <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
                  <div className="flex flex-col my-5">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Assembly Form Name
                      <span className="text-xl text-red-500 -mt-1">*</span>
                    </label>
                    <Input
                      type="text"
                      id="assemblyFormName"
                      name="assemblyFormName"
                      placeholder="Assembly Form Name"
                      value={addAssemblyFormData?.formName}
                      onChange={(e) =>
                        setAddAssemblyFormData((prev) => ({
                          ...prev,
                          formName: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="flex flex-col my-5">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Category
                      <span className="text-xl text-red-500 -mt-1">*</span>
                    </label>
                    <Select
                      placeholder="Select SOP"
                      id="SOP"
                      name="SOP"
                      menuPlacement="auto"
                      onChange={(e) =>
                        setAddAssemblyFormData((prev) => ({
                          ...prev,
                          category: e.target.value,
                        }))
                      }
                      value={addAssemblyFormData?.category}
                      closeMenuOnSelect={true}
                      options={['WO SOP', 'Job Sop'].map((el) => ({
                        value: el,
                        label: el,
                      }))}
                    />
                  </div>
                </div>
              </section>
              <section>
                {addAssemblyFormData?.steps?.length > 0 &&
                  addAssemblyFormData?.steps?.map((item, index) => {
                    return (
                      <div key={index}>
                        <div className="outline-[#4085ed80] rounded-lg border p-2 mb-5">
                          <div className="flex flex-row justify-between px-2">
                            <label className="font-bold text-[#667085] text-lg">
                              Step {item?.stepNumber}:
                            </label>
                            <div
                              className="hover:cursor-pointer"
                              onClick={() => handleStepDelete(item)}
                            >
                              <TrashIcon className="w-6 h-6 text-black my-auto" />
                            </div>
                          </div>
                          <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2 mt-2">
                            <div className="flex flex-col">
                              <label className="mb-1 font-semibold text-[#667085]">
                                Step Name
                              </label>
                              <Input
                                type="text"
                                id="stepName"
                                name="stepName"
                                placeholder="Step Name"
                                value={item?.stepName}
                                onChange={(e) =>
                                  setAddAssemblyFormData((prev) => ({
                                    ...prev,
                                    steps: prev?.steps?.map((prevItem, i) => {
                                      if (i === index) {
                                        return {
                                          ...prevItem,
                                          stepName: e.target.value,
                                        };
                                      }
                                      return prevItem;
                                    }),
                                  }))
                                }
                              />
                            </div>
                            <div className="flex flex-row justify-between">
                              <div></div>
                              <div className="flex flex-row space-x-2 mt-8 items-center ">
                                <Input
                                  type="checkbox"
                                  id="isMediaMandatory"
                                  name="isMediaMandatory"
                                  value={item?.isMediaMandatory}
                                  checked={item?.isMediaMandatory}
                                  onChange={(e) =>
                                    setAddAssemblyFormData((prev) => ({
                                      ...prev,
                                      steps: prev?.steps?.map((prevItem, i) => {
                                        if (i === index) {
                                          return {
                                            ...prevItem,
                                            isMediaMandatory:
                                              e?.target?.checked,
                                          };
                                        }
                                        return prevItem;
                                      }),
                                    }))
                                  }
                                />
                                <label className="mb-1 font-semibold text-[#667085]">
                                  Media Mandatory?
                                </label>
                              </div>
                            </div>
                            <div className="mt-2 w-full col-span-2">
                              <label className="mb-1 font-semibold text-[#667085]">
                                Select SOP File
                              </label>
                              <UploadButton
                                accept="application/pdf"
                                fileType="JPG/PNG"
                                svg={<JpgPng className="h-10" />}
                                onChange={(e) => {
                                  assemblyFileChangeHandler(
                                    e,
                                    'project',
                                    true,
                                    index
                                  );
                                }}
                                multiple
                                className={`text-[#667085]`}
                              />
                            </div>
                            {item?.attachments?.length > 0 && (
                              <div className="mt-2 col-span-2">
                                <Table className="mt-2">
                                  <Table.Head>
                                    <Table.Row>
                                      <Table.Th>S.No.</Table.Th>
                                      <Table.Th>File Name</Table.Th>
                                      <Table.Th></Table.Th>
                                    </Table.Row>
                                  </Table.Head>
                                  <Table.Body>
                                    {item?.attachments?.map((file, idx) => {
                                      return (
                                        <Table.Row key={idx}>
                                          <Table.Td>{idx + 1}</Table.Td>
                                          <Table.Td>{file?.name}</Table.Td>
                                          <Table.Td
                                            onClick={() =>
                                              handleRemovePdf(file, true, index)
                                            }
                                          >
                                            <div className="hover:cursor-pointer">
                                              x
                                            </div>
                                          </Table.Td>
                                        </Table.Row>
                                      );
                                    })}
                                  </Table.Body>
                                </Table>
                              </div>
                            )}
                            <div className="flex flex-col mt-2 col-span-2">
                              <label className="mb-1 font-semibold text-[#667085]">
                                Description
                              </label>
                              <textarea
                                type="text"
                                id="description"
                                name="description"
                                className="py-1 pl-4 pr-2 bg-transparent relative outline-[#4085ed80] rounded-lg flex justify-between items-center border text-black"
                                placeholder="Description"
                                value={item?.description}
                                onChange={(e) =>
                                  setAddAssemblyFormData((prev) => ({
                                    ...prev,
                                    steps: prev?.steps?.map((prevItem, i) => {
                                      if (i === index) {
                                        return {
                                          ...prevItem,
                                          description: e.target.value,
                                        };
                                      }
                                      return prevItem;
                                    }),
                                  }))
                                }
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </section>
              <section>
                <label className="font-bold text-[#667085] text-lg">
                  Step {stepCount}:
                </label>
                <div className="outline-[#4085ed80] rounded-lg border p-2">
                  <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2 mt-2">
                    <div className="flex flex-col">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Step Name
                        <span className="text-xl text-red-500 -mt-1">*</span>
                      </label>
                      <Input
                        type="text"
                        id="stepName"
                        name="stepName"
                        placeholder="Step Name"
                        value={assemblyFormSteps?.stepName}
                        onChange={(e) =>
                          setAssembyFormSteps((prev) => ({
                            ...prev,
                            stepName: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div className="flex flex-row justify-between">
                      <div></div>
                      <div className="flex flex-row space-x-2 mt-8 items-center ">
                        <Input
                          type="checkbox"
                          id="isMediaMandatory"
                          name="isMediaMandatory"
                          checked={assemblyFormSteps?.isMediaMandatory}
                          onChange={(e) => {
                            setAssembyFormSteps((prev) => ({
                              ...prev,
                              isMediaMandatory: e.target.checked,
                            }));
                          }}
                        />
                        <label className="mb-1 font-semibold text-[#667085]">
                          Media Mandatory?
                        </label>
                      </div>
                    </div>
                    <div className="mt-2 w-full col-span-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Select SOP File
                      </label>
                      <UploadButton
                        accept="application/pdf"
                        fileType="JPG/PNG"
                        svg={<JpgPng className="h-10" />}
                        onChange={(e) => {
                          assemblyFileChangeHandler(e, 'project', false);
                        }}
                        multiple
                        className={`text-[#667085]`}
                      />
                    </div>
                    {assemblyFormSteps?.attachments?.length > 0 && (
                      <div className="mt-2 col-span-2">
                        <Table>
                          <Table.Head>
                            <Table.Row>
                              <Table.Th>S.No.</Table.Th>
                              <Table.Th>File Name</Table.Th>
                              <Table.Th></Table.Th>
                            </Table.Row>
                          </Table.Head>
                          <Table.Body>
                            {assemblyFormSteps?.attachments?.map(
                              (file, index) => {
                                return (
                                  <Table.Row key={index}>
                                    <Table.Td>{index + 1}</Table.Td>
                                    <Table.Td>{file?.name}</Table.Td>
                                    <Table.Td
                                      onClick={() =>
                                        handleRemovePdf(file, false)
                                      }
                                    >
                                      <div className="hover:cursor-pointer">
                                        x
                                      </div>
                                    </Table.Td>
                                  </Table.Row>
                                );
                              }
                            )}
                          </Table.Body>
                        </Table>
                      </div>
                    )}
                    <div className="flex flex-col mt-2 col-span-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Description
                      </label>
                      <textarea
                        type="text"
                        id="description"
                        name="description"
                        className="py-1 pl-4 pr-2 bg-transparent relative outline-[#4085ed80] rounded-lg flex justify-between items-center border text-black"
                        placeholder="Description"
                        value={assemblyFormSteps?.description}
                        onChange={(e) =>
                          setAssembyFormSteps((prev) => ({
                            ...prev,
                            description: e.target.value,
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>
              </section>
              <div className="flex flex-row justify-between">
                <div></div>
                <div className="flex flex-row space-x-2 mt-8 items-center ">
                  <Input
                    type="checkbox"
                    id="sequenceFollow"
                    name="sequenceFollow"
                    value={addAssemblyFormData?.isSequential}
                    checked={addAssemblyFormData?.isSequential}
                    onChange={(e) => {
                      setAddAssemblyFormData((prev) => ({
                        ...prev,
                        isSequential: e.target.checked,
                      }));
                    }}
                  />
                  <label className="mb-1 font-semibold text-[#667085]">
                    Sequential Steps
                  </label>
                </div>
              </div>
            </div>
          )}
        </Modal>
      )}
    </div>
  );
};

export default AssemblyForm;
