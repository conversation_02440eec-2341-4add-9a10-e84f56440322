import { useEffect } from 'react';
import Select from 'react-select';
import { Hand<PERSON>, Position } from 'reactflow';
import { useLazyGetUserByIdQuery } from '../../../slices/userApiSlice';
import CustomToolTip from '../../global/CustomToolTip';
import Input from '../../v2/global/components/Input';
import DurationPicker from '../../v3/global/components/DurationPicker';

const SelectNode = ({ data, isConnectable }) => {
  const user = JSON.parse(localStorage.getItem('user'));
  const [getUserById, { data: userData }] = useLazyGetUserByIdQuery();
  useEffect(() => {
    if (user?.user?._id) {
      getUserById({ id: user.user._id });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onPageEdit = (page) => {
    let tempPage = {};

    if (page?.value === '/purchase/indent') {
      tempPage = {
        label: page?.label,
        value: '/purchase/indent/',
        group: page?.group,
      };
    } else if (page?.value === '/purchase/requestforquotation') {
      tempPage = {
        label: page?.label,
        value: '/purchase/requestforquotation',
        group: page?.group,
      };
    } else if (page?.value === '/purchase/po') {
      tempPage = {
        label: page?.label,
        value: '/purchase/po',
        group: page?.group,
      };
    } else {
      tempPage = {
        label: page?.label,
        value: page?.value,
        group: page?.group,
      };
    }

    let department = userData.departments?.find((elem) => {
      return elem._id === page?.departIds;
    });

    data.setNodes((nds) =>
      nds.map((node) => {
        if (node?.id === data?.id) {
          node.data = {
            ...node.data,
            selectedPage: [tempPage],
            selectedDepartment: department,
          };
        }

        return node;
      })
    );
  };

  const onDelete = () => {
    data?.setNodes?.((nds) => nds.filter((node) => node?.id !== data?.id));
    data?.setEdges?.((edge) => {
      return edge?.filter(
        (e) => !(e.source === data?.id || e.target === data?.id)
      );
    });
    let flow = {};
    for (let el of Object.keys(data?.departmentFlow || {})) {
      let children = [];
      if (el !== data?.id) {
        for (let elem of data?.departmentFlow?.[el] || []) {
          if (elem !== data?.id && elem !== null) children.push(elem);
        }
        flow = { ...flow, [el]: children };
      }
    }

    data?.setDepartmentFlow?.(flow);
  };

  const pagesOptionInfo = userData?.departments.flatMap((department) => {
    return department?.navs?.map((nav) => {
      return {
        departId: department._id,
        name: nav.name,
        childNavs: nav.childNavs,
      };
    });
  });

  const pagesOptions = pagesOptionInfo?.map((g) => ({
    label: g.name,
    options: g.childNavs
      ?.filter((c) => c.cslug !== '/crm/pipelines')
      ?.map((c) => {
        if (c.cslug === '/accountmanagement/proformainvoice') {
          return {
            departIds: g.departId,
            label: c.cname,
            value: '/accountmanagement/proformainvoice',
            group: g.name,
          };
        }

        if (c.cslug === '/dispatch/dashboard') {
          return {
            departIds: g.departId,
            label: c.cname,
            value: '/dispatch/dashboard/bom',
            group: g.name,
          };
        }

        if (c.cslug === '/purchase/po') {
          return {
            departIds: g.departId,
            label: c.cname,
            value: '/purchase/po',
            group: g.name,
          };
        }

        if (c.cslug === '/inventory/inpage') {
          return {
            departIds: g.departId,
            label: c.cname,
            value: '/inventory/inpage/grn',
            group: g.name,
          };
        }

        if (c.cslug === '/inventory/outpage') {
          return {
            departIds: g.departId,
            label: c.cname,
            value: '/inventory/outpage/stockout',
            group: g.name,
          };
        }

        return {
          departIds: g.departId,
          label: c.cname,
          value: c.cslug,
          group: g.name,
        };
      }),
    value: g.name,
  }));
  const onInputChange = (e) => {
    data.setNodes((nds) =>
      nds.map((node) => {
        if (node?.id === data?.id) {
          node.data = {
            ...node.data,
            name: e.target.value,
          };
        }
        return node;
      })
    );
  };

  const onDurationChange = (e) => {
    data.setNodes((nds) =>
      nds.map((node) => {
        if (node?.id === data?.id) {
          node.data = {
            ...node.data,
            duration: e.target.value,
          };
        }
        return node;
      })
    );
  };

  const addNode = () => {
    const id = `node-${Date.now()}`;

    data.setNodes((prevNodes) => {
      const parentNode = prevNodes.find((node) => node.id === data.id);

      if (!parentNode) {
        console.error('Parent node not found!'); //eslint-disable-line
        return prevNodes;
      }

      // Count how many nodes have been added from the same parent node
      const siblingsCount = prevNodes.filter(
        (node) => node.data.previousNodeId === data.id
      ).length;

      const baseX =
        parentNode.position.x +
        250 +
        (siblingsCount % 2 === 0
          ? 80 + siblingsCount * 50
          : 26 + siblingsCount * 50);

      const baseY = parentNode.position.y;

      // Adjust Y position for the new node based on the number of siblings
      const adjustedY =
        baseY +
        (siblingsCount % 2 === 0
          ? 240 + siblingsCount * 140
          : -100 - siblingsCount * 140);

      // Add the new node
      return [
        ...prevNodes,
        {
          id: id,
          type: 'select',
          position: {
            x: baseX,
            y: adjustedY,
          },
          data: {
            departments: data.departments,
            nodes: data.nodes,
            setNodes: data.setNodes,
            setEdges: data.setEdges,
            id: id,
            selectedDepartment: data?.departments?.find(
              (dpt) => dpt._id === pagesOptions?.[0]?.options?.[0]?.departIds
            ),
            selectedPage: [
              {
                label: pagesOptions?.[0]?.options?.[0]?.label,
                value: pagesOptions?.[0]?.options?.[0]?.value,
                group: pagesOptions?.[0]?.options?.[0]?.group,
              },
            ],
            setDepartmentFlow: data.setDepartmentFlow,
            onConnect: data.onConnect,
            previousNodeId: data.id,
          },
        },
      ];
    });

    // Automatically connect with previous node
    data.onConnect({
      source: id,
      target: data.id,
      sourceHandle: 'b',
      targetHandle: null,
      autoConnect: true,
    });
  };

  const handleStyles = {
    height: '20px',
    width: '8px',
    borderRadius: '0px',
    border: 'none',
  };

  return (
    <div className="flex justify-between items-center gap-2">
      <div className="relative h-fit p-2 border-2 border-black border-solid rounded-md bg-slate-50">
        <Handle
          type="target"
          position={Position.Right}
          isConnectable={1}
          style={handleStyles}
        />
        <div>
          <div className="flex align-center justify-between pb-2">
            <label
              htmlFor="text"
              className="block text-black-300 text-sm pt-2 px-2"
            >
              Department:
            </label>

            <CustomToolTip
              tooltipId="delete-node-tooltip"
              content="Delete Department"
              place="top"
              effect="solid"
              className="bg-black text-white p-1 rounded"
            >
              <p
                className="block text-black-300 text-sm rounded-md py-1 px-2 text-white cursor-pointer"
                onClick={onDelete}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_2571_1073)">
                    <path
                      d="M1.42871 5H18.5716"
                      stroke="#E70000"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M3.57178 5H16.4289V17.8571C16.4289 18.236 16.2784 18.5994 16.0105 18.8673C15.7426 19.1352 15.3792 19.2857 15.0004 19.2857H5.00035C4.62147 19.2857 4.25811 19.1352 3.9902 18.8673C3.72229 18.5994 3.57178 18.236 3.57178 17.8571V5Z"
                      stroke="#E70000"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M6.42871 4.99958V4.2853C6.42871 3.33809 6.80499 2.42969 7.47476 1.75991C8.14453 1.09014 9.05294 0.713867 10.0001 0.713867C10.9473 0.713867 11.8557 1.09014 12.5255 1.75991C13.1953 2.42969 13.5716 3.33809 13.5716 4.2853V4.99958"
                      stroke="#E70000"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_2571_1073">
                      <rect width="20" height="20" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </p>
            </CustomToolTip>
          </div>
          <Select
            id="select"
            className="nodrag"
            options={pagesOptions}
            value={data?.selectedPage?.[0] || {}}
            onChange={onPageEdit}
            placeholder="Select Page"
          />
          <Input
            placeholder="Enter Name"
            onChange={onInputChange}
            value={data?.name || ''}
          />
          <DurationPicker
            onChange={onDurationChange}
            defaultValue={data?.duration}
            isHours
            isDays
            isMinutes
          />
        </div>
        <Handle
          type="source"
          position={Position.Left}
          id="b"
          isConnectable={isConnectable}
          style={handleStyles}
        />
      </div>

      <div>
        <CustomToolTip
          tooltipId={data.id}
          content="Add Department"
          place="top"
          effect="solid"
          className={`bg-black text-white p-1 rounded`}
        >
          <div onClick={() => addNode()}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-8 h-8 !mt-2 !bg-gray-50 hover:!bg-black rounded-full text-black hover:text-white cursor-pointer"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
          </div>
        </CustomToolTip>
      </div>
    </div>
  );
};

export default SelectNode;
