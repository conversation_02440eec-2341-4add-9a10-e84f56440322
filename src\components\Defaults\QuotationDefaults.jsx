// import Input from '../global/components/Input';

// const QuotationDefaults = ({ defaults, setDefaults }) => {
//   return (
//     <div>
//       <h3 className="text-gray-subHeading">Quoatation Defaults</h3>
//       <div className="mt-4 flex justify-between gap-x-5 w-full">
//         <div className="w-full">
//           <h5 className="mb-2 text-gray-label">Company Name:</h5>
//           <Input
//             value={defaults?.businessDetails?.companyName}
//             className="w-full"
//             onChange={(e) => {
//               setDefaults((prev) => ({
//                 ...prev,
//                 businessDetails: {
//                   ...prev.businessDetails,
//                   companyName: e.target.value,
//                 },
//               }));
//             }}
//             placeholder="Enter Company Name"
//           />
//         </div>
//         <div className="w-full">
//           <h5 className="mb-2 text-gray-label">Address:</h5>
//           <Input
//             value={defaults?.businessDetails?.address}
//             onChange={(e) => {
//               setDefaults((prev) => ({
//                 ...prev,
//                 businessDetails: {
//                   ...prev.businessDetails,
//                   address: e.target.value,
//                 },
//               }));
//             }}
//             placeholder="Enter Address"
//           />
//         </div>
//         <div className="w-full">
//           <h5 className="mb-2 text-gray-label">Contact:</h5>
//           <Input
//             value={defaults?.businessDetails?.contact}
//             onChange={(e) => {
//               setDefaults((prev) => ({
//                 ...prev,
//                 businessDetails: {
//                   ...prev.businessDetails,
//                   contact: e.target.value,
//                 },
//               }));
//             }}
//             placeholder="Enter Contact Info"
//           />
//         </div>
//         <div className="w-full">
//           <h5 className="mb-2 text-gray-label">GST Number:</h5>
//           <Input
//             value={defaults?.businessDetails?.gstNumber}
//             onChange={(e) => {
//               setDefaults((prev) => ({
//                 ...prev,
//                 businessDetails: {
//                   ...prev.businessDetails,
//                   gstNumber: e.target.value,
//                 },
//               }));
//             }}
//             placeholder="Enter GST Number"
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default QuotationDefaults;
