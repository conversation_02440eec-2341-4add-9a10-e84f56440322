import { Checkbox } from 'antd';
import { useEffect, useRef, useState } from 'react';
import SaveIcon from '../../../assets/images/save_proceed.png';
import { generateDateString, unCamelCaseString } from '../../../helperFunction';
import Button from '../../global/components/Button';
import ColumnAccess from './Admin/ColumnAccess';
import StoreAccess from './Admin/StoreAccess';
import { InfoTooltip } from './InfoTooltip';
import Input from './Input';
import MultiSelect from './MultiSelect';
import Select from './Select';

const FullScreenUserModal = ({
  title,
  description,
  onClose,
  onSubmit,
  userRole,
  inputData,
  inputChangeHandler,
  profiles,
  isEdit,
  filteredDepartments,
  onActive,
  roles,
  isSuperuser,
  setActive,
  setShowMore,
  showMore,
  setInputData,
  mqttData,
  allSlugs,
  APPROVAL_ACCESS,
  selectedDepartments,
  getAllColumns,
}) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [tabObjects, setTabObjects] = useState([
    {
      name: 'General',
      active: true,
      id: 1,
    },
    {
      name: 'Page\xA0Access',
      active: false,
      id: 2,
    },
    {
      name: 'Kanban\xA0Info',
      active: false,
      id: 3,
    },
    {
      name: 'Column Access',
      active: false,
      id: 4,
    },
    {
      name: 'Store Access',
      active: false,
      id: 5,
    },
  ]);

  const [selectedTab, setSelectedTab] = useState(tabObjects[0]);
  let closesidebarRef = useRef();

  // Close sidebar if clicked outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // If the sidebar is open and the click is outside of it
      if (
        isSidebarOpen &&
        closesidebarRef.current &&
        !closesidebarRef.current.contains(event.target)
      ) {
        setIsSidebarOpen(false); // Close the sidebar
      }
    };

    // Add event listener to detect clicks
    document.addEventListener('mousedown', handleClickOutside);

    // Cleanup event listener when component is unmounted
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isSidebarOpen]);

  const selectedStyle = `w-[100%] px-4 py-2 text-xs font-medium outline-none rounded-md text-start bg-blue-primary text-white`;
  const nonSelectedStyle =
    'w-full px-4 py-2 text-xs hover:rounded-md font-medium outline-none text-start !text-white hover:bg-[#3B5C73] hover:text-blue-700';

  return (
    <div className="!fixed z-[9999] flex w-screen h-full bg-white mt-[-15px]">
      <div
        className={`w-full rounded-tl-lg rounded-tr-lg px-10 transition-all duration-300 h-full overflow-x-hidden`}
      >
        <div className="w-full flex pt-5 justify-between items-center">
          <div className="flex flex-col gap-1">
            <div className="font-semibold text-md text-black-500 ">{title}</div>
            <div className="text-sm text-gray-500">{description}</div>
          </div>
          <div
            className={`!h-8 !text-sm border-none cursor-pointer`}
            onClick={onClose}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="size-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6 18 18 6M6 6l12 12"
              />
            </svg>
          </div>
        </div>
        <div className="flex justify-end items-center w-full  mt-2 p-2">
          <Button className={`!h-8 !text-sm`} onClick={onSubmit}>
            <img
              src={SaveIcon}
              alt="Save Icon"
              className="w-5 h-5 object-contain relative"
            />
            Save
          </Button>
        </div>

        <div className="w-full flex gap-2  overflow-y-scroll pt-2">
          <div className="flex flex-col bg-[#213343] overflow-x-scroll rounded-md no-scrollbar gap-x-px w-[20%] h-screen p-4">
            {tabObjects.map((tab) => (
              <div key={tab.id} className="w-full">
                <button
                  className={
                    tab?.active ? `${selectedStyle}` : `${nonSelectedStyle}`
                  }
                  onClick={() => {
                    let temp = [];
                    tabObjects.forEach((obj) => {
                      if (obj?.id === tab?.id) {
                        let activeTab = {
                          ...obj,
                          active: true,
                        };
                        setSelectedTab(tab);
                        temp.push(activeTab);
                      } else {
                        temp.push({
                          ...obj,
                          active: false,
                        });
                      }
                    });
                    setTabObjects(temp);
                    setIsSidebarOpen(false); // Optionally close sidebar after selection
                  }}
                >
                  {tab?.name}
                </button>
              </div>
            ))}
          </div>

          <div className="w-full">
            {selectedTab.id === 1 && (
              <div className="w-full h-100vh overflow-auto bg-gray-50 p-6">
                <div className="w-full h-screen overflow-y-auto p-6 pb-[20rem] bg-white shadow-md rounded-lg max-w-7xl mx-auto">
                  <div className="grid grid-cols-2 gap-3">
                    {/* Profile Selection Section for Superusers */}
                    {userRole === 'superuser' && (
                      <div className="w-full">
                        <label className="text-sm font-semibold w-full text-gray-700 mb-2">
                          Select Profile
                        </label>
                        <Select
                          id="profileId"
                          value={inputData.profileId || ''}
                          onChange={inputChangeHandler}
                          name="profileId"
                          placeholder="Select Profile ID"
                          options={profiles.map((pro) => ({
                            label: pro.name,
                            value: pro._id,
                            disabled: !!pro?.assigned,
                          }))}
                          className="rounded-md border-gray-300 w-full"
                        />
                      </div>
                    )}

                    {/* Employee ID Field */}
                    <div className="w-full">
                      <label className="text-sm font-semibold w-full text-gray-700 mb-1">
                        User/Employee ID
                      </label>
                      <Input
                        id="employeeId"
                        type="text"
                        value={inputData.employeeId || ''}
                        onChange={inputChangeHandler}
                        name="employeeId"
                        placeholder="Enter your ID"
                        className="rounded-md border-gray-300 w-full"
                      />
                    </div>

                    {/* Name and Email Fields */}
                    <div className="w-full">
                      <label className="text-sm font-semibold text-gray-700 mb-1">
                        Name <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="name"
                        type="text"
                        value={inputData.name || ''}
                        onChange={inputChangeHandler}
                        name="name"
                        required
                        placeholder="Enter your name"
                        className="rounded-md border-gray-300 w-full"
                      />
                    </div>
                    <div className="w-full">
                      <label className="text-sm font-semibold text-gray-700 mb-1">
                        Email <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="email"
                        type="email"
                        value={inputData.email || ''}
                        onChange={inputChangeHandler}
                        name="email"
                        required
                        placeholder="Enter your email"
                        className="rounded-md border-gray-300 w-full"
                      />
                    </div>

                    {/* Mobile Number Field */}
                    <div className="w-full">
                      <label className="text-sm font-semibold text-gray-700 mb-1">
                        Mobile No.
                      </label>
                      <Input
                        id="mobile"
                        type="text"
                        value={inputData.mobile || ''}
                        onChange={inputChangeHandler}
                        name="mobile"
                        placeholder="Enter your mobile number"
                        className="rounded-md border-gray-300 w-full"
                      />
                    </div>

                    {/* Password Fields */}
                    {isEdit && (
                      <div className="w-full">
                        <label className="text-sm font-semibold text-gray-700 mb-1">
                          {isSuperuser
                            ? 'Master Password'
                            : 'Current / Admin Password'}
                        </label>
                        <Input
                          id={isSuperuser ? 'MasterPassword' : 'CurrPassword'}
                          type="password"
                          value={
                            isSuperuser
                              ? inputData.masterpassword
                              : inputData.currpassword || ''
                          }
                          onChange={inputChangeHandler}
                          name={isSuperuser ? 'masterpassword' : 'currpassword'}
                          placeholder="Enter current password"
                          className="rounded-md border-gray-300 w-full"
                        />
                      </div>
                    )}

                    <div className="w-full">
                      <label className="text-sm font-semibold text-gray-700 mb-1">
                        Password <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="password"
                        type="password"
                        value={inputData.password || ''}
                        onChange={inputChangeHandler}
                        name="password"
                        required
                        placeholder="Enter your password"
                        className="rounded-md border-gray-300 w-full"
                        onFocus={() => setActive(true)}
                        onBlur={() => setActive(false)}
                      />
                      {onActive && (
                        <p className="text-xs text-gray-500 mt-2">
                          Your password must be at least 8 characters long and
                          include at least one number and one letter.
                        </p>
                      )}
                    </div>

                    <div className="w-full">
                      <label className="text-sm font-semibold text-gray-700 mb-1">
                        Confirm Password <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="confirm"
                        type="password"
                        value={inputData.confirm || ''}
                        onChange={inputChangeHandler}
                        name="confirm"
                        required
                        placeholder="Confirm your password"
                        className="rounded-md border-gray-300 w-full"
                      />
                    </div>
                  </div>
                  <div className="py-5 px-0">
                    <hr className="border-gray-200 border-t border-1 shadow-sm" />
                  </div>
                  {/* Role Selection with Tooltip */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="w-full">
                      <label className="text-sm font-semibold text-gray-700 mb-1 flex items-center gap-2">
                        Role <span className="text-red-500">*</span>
                        <InfoTooltip position="right" id="adminRole">
                          Select a User Role: Choose 'Admin' for full control or
                          'Supervisor' for limited access.
                        </InfoTooltip>
                      </label>
                      <Select
                        id="role"
                        value={inputData.role || ''}
                        onChange={inputChangeHandler}
                        options={roles
                          ?.filter((r) => r !== 'superuser')
                          .map((role) => ({
                            label: role,
                            value: role,
                          }))}
                        name="role"
                        required
                        placeholder="Select a role"
                        className="rounded-md border-gray-300 w-full"
                      />
                    </div>

                    {/* Process Control MultiSelect */}
                    <div className="w-full">
                      <label className="text-sm font-semibold text-gray-700 mb-1 flex items-center gap-2">
                        Process Control
                        <InfoTooltip position="right" id="processControl">
                          Select the processes accessible to this user.
                        </InfoTooltip>
                      </label>
                      <MultiSelect
                        options={mqttData.map((mqtt) => ({
                          label: mqtt.process,
                          value: mqtt._id,
                        }))}
                        name="processAccess"
                        value={inputData?.processAccess || []}
                        onChange={inputChangeHandler}
                        className="rounded-md border-gray-300 w-full"
                      />
                    </div>

                    {/* RFID Field */}
                    <div className="w-full">
                      <label className="text-sm font-semibold text-gray-700 mb-1 flex items-center gap-2">
                        RFID
                        <InfoTooltip position="right" id="rfid">
                          Enter the RFID number assigned to this user.
                        </InfoTooltip>
                      </label>
                      <Input
                        id="rfid"
                        type="text"
                        value={inputData.rfid || ''}
                        onChange={inputChangeHandler}
                        name="rfid"
                        placeholder="Enter RFID"
                        className="rounded-md border-gray-300 w-full"
                      />
                    </div>

                    {/* RFID Expiry Date */}
                    <div className="w-full">
                      <label className="text-sm font-semibold text-gray-700 mb-1">
                        RFID Expiry Date
                      </label>
                      <Input
                        id="rfidExpiryDate"
                        type="date"
                        value={
                          isEdit
                            ? generateDateString(inputData.rfidExpiryDate)
                            : inputData.rfidExpiryDate || ''
                        }
                        onChange={inputChangeHandler}
                        name="rfidExpiryDate"
                        className="rounded-md border-gray-300 w-full"
                      />
                    </div>
                  </div>

                  {/* Additional Information Toggle */}
                  <p
                    className="text-blue-500 cursor-pointer mt-5"
                    onClick={() => setShowMore((prev) => !prev)}
                  >
                    {showMore
                      ? '- Hide Additional Information'
                      : '+ Add More Details'}
                  </p>
                  {showMore && (
                    <div className="grid grid-cols-2 gap-8 mt-4">
                      <div className="w-full">
                        <label className="text-sm font-semibold text-gray-700 mb-1">
                          Designation
                        </label>
                        <Input
                          id="designation"
                          type="text"
                          value={inputData.designation || ''}
                          onChange={inputChangeHandler}
                          name="designation"
                          placeholder="Enter your designation"
                          className="rounded-md border-gray-300 w-full"
                        />
                      </div>
                      <div className="w-full">
                        <label className="text-sm font-semibold text-gray-700 mb-1">
                          Contractor Name
                        </label>
                        <Input
                          id="contractorName"
                          type="text"
                          value={inputData.contractorName || ''}
                          onChange={inputChangeHandler}
                          name="contractorName"
                          placeholder="Enter contractor name"
                          className="rounded-md border-gray-300 w-full"
                        />
                      </div>
                      <div className="w-full">
                        <label className="text-sm font-semibold text-gray-700 mb-1">
                          Wages Per Hour
                        </label>
                        <Input
                          id="wagesPerHour"
                          type="number"
                          value={inputData.workPerWages || ''}
                          onChange={inputChangeHandler}
                          name="workPerWages"
                          placeholder="Enter hourly wages"
                          className="rounded-md border-gray-300 w-full"
                        />
                      </div>
                    </div>
                  )}

                  {/* Mobile Credentials Checkbox */}
                  {/* <div className="flex items-center mt-6">
                    <Input
                      type="checkbox"
                      checked={inputData.isMobileUser}
                      onChange={() =>
                        setInputData((prev) => ({
                          ...prev,
                          isMobileUser: !prev.isMobileUser,
                        }))
                      }
                      className="mr-2 h-4 w-4 rounded border-gray-300"
                      disabled={isAlreadyFirebaseUser}
                    />
                    <p
                      className={
                        isAlreadyFirebaseUser
                          ? 'text-gray-300'
                          : 'text-gray-700'
                      }
                    >
                      Use as default mobile credentials
                    </p>
                  </div> */}
                </div>
              </div>
            )}

            {selectedTab.id === 2 && (
              <div className="w-full h-screen overflow-auto bg-gray-50 p-6 pb-[14rem]">
                <div className="max-w-7xl mx-auto bg-white shadow-md rounded-lg p-8">
                  {/* Departments Section */}
                  <div className="mb-8">
                    <label className="mb-2 font-semibold text-gray-600 flex items-center gap-2">
                      Departments
                      <InfoTooltip
                        position="right"
                        id="adminDepartments"
                        isHtml={false}
                      >
                        Select a User Role: This dropdown lets you assign roles
                        to new users. Choose 'Admin' for full control or
                        'Supervisor' for limited access.
                      </InfoTooltip>
                    </label>
                    <MultiSelect
                      className="!min-w-[10rem] !w-[100%] p-2 border rounded-md"
                      id="departments"
                      value={inputData.departments || []}
                      onChange={inputChangeHandler}
                      options={filteredDepartments?.map((opt) => ({
                        label: opt.name,
                        value: opt._id,
                      }))}
                      name="departments"
                      required
                      placeholder="Select departments"
                    />
                  </div>

                  {/* Select All Section */}
                  <section className="w-full flex justify-end items-center gap-3 mb-6">
                    <label
                      className="cursor-pointer font-medium text-gray-700 mt-[-7px]"
                      htmlFor="selectAll"
                    >
                      Select All
                    </label>
                    <Checkbox
                      className="cursor-pointer"
                      checked={
                        inputData?.pageAccess?.length > 0 &&
                        inputData?.pageAccess.length === allSlugs?.length &&
                        inputData?.approvalAccess?.includes(
                          'isEditableKanban'
                        ) &&
                        inputData?.isKanbanAccess
                      }
                      onChange={(e) => {
                        setInputData((prev) => ({
                          ...prev,
                          pageAccess: e.target.checked ? allSlugs : [],
                          approvalAccess: e.target.checked
                            ? [
                                ...APPROVAL_ACCESS.map((i) => i.slug),
                                'isEditableKanban',
                              ]
                            : [],
                          isKanbanAccess: e.target.checked,
                        }));
                      }}
                    />
                  </section>

                  {/* Departments and Permissions */}
                  {selectedDepartments?.map((dep) => (
                    <div
                      key={dep._id}
                      className="px-4 py-4 bg-gray-100 rounded-lg mb-6"
                    >
                      <p className="capitalize font-semibold text-lg text-gray-800 mb-2">
                        {dep.name}
                      </p>
                      <div className="px-3">
                        {dep?.navs?.map((nav) => (
                          <div key={nav._id} className="mb-4">
                            <p className="text-md text-gray-700 font-medium mb-2">
                              {nav.name}
                            </p>
                            <div className="grid grid-cols-3 gap-4">
                              {nav?.childNavs?.map((cnav) => (
                                <section
                                  key={cnav._id}
                                  className="flex items-center gap-3"
                                >
                                  <Checkbox
                                    className="cursor-pointer"
                                    checked={inputData?.pageAccess?.includes(
                                      cnav.cslug
                                    )}
                                    onChange={(e) => {
                                      if (e.target.checked) {
                                        setInputData((prev) => ({
                                          ...prev,
                                          pageAccess: [
                                            ...(prev?.pageAccess || []),
                                            cnav.cslug,
                                          ],
                                        }));
                                      } else {
                                        setInputData((prev) => ({
                                          ...prev,
                                          pageAccess: prev.pageAccess.filter(
                                            (i) => i !== cnav.cslug
                                          ),
                                          approvalAccess:
                                            prev.approvalAccess.filter(
                                              (i) => i !== cnav.cslug
                                            ),
                                        }));
                                      }
                                    }}
                                  />
                                  <label
                                    htmlFor={cnav.cslug}
                                    className="text-sm text-gray-600 cursor-pointer"
                                  >
                                    {cnav.cname}
                                  </label>
                                </section>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}

                  {/* Approval Section */}
                  <div className="px-4 py-4 bg-gray-100 rounded-lg mb-6">
                    <p className="capitalize font-semibold text-lg text-gray-800 mb-2">
                      Approval
                    </p>
                    <div className="grid grid-cols-3 gap-4">
                      {APPROVAL_ACCESS?.filter((i) =>
                        inputData?.pageAccess?.includes(i.slug)
                      ).map((item, iIdx) => (
                        <section key={iIdx} className="flex items-center gap-3">
                          <Checkbox
                            className="cursor-pointer"
                            checked={inputData?.approvalAccess?.includes(
                              item.slug
                            )}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setInputData((prev) => ({
                                  ...prev,
                                  approvalAccess: [
                                    ...(prev?.approvalAccess || []),
                                    item.slug,
                                  ],
                                }));
                              } else {
                                setInputData((prev) => ({
                                  ...prev,
                                  approvalAccess: prev.approvalAccess.filter(
                                    (i) => i !== item.slug
                                  ),
                                }));
                              }
                            }}
                          />
                          <label
                            htmlFor={item.value}
                            className="text-sm text-gray-600 cursor-pointer"
                          >
                            {unCamelCaseString(item.value)}
                          </label>
                        </section>
                      ))}
                    </div>
                  </div>

                  {/* Kanban Access Section */}
                  <div className="px-4 py-4 bg-gray-100 rounded-lg mb-6">
                    <p className="capitalize font-semibold text-lg text-gray-800 mb-2">
                      Kanban Access
                    </p>
                    <section className="flex items-center gap-3">
                      <Checkbox
                        className="cursor-pointer"
                        checked={inputData?.isKanbanAccess}
                        onChange={(e) => {
                          const newIsKanbanAccess = e.target.checked;
                          setInputData((prev) => ({
                            ...prev,
                            isKanbanAccess: newIsKanbanAccess,
                          }));
                        }}
                      />
                      <label
                        htmlFor="isKanbanAccess"
                        className="text-sm text-gray-600 cursor-pointer"
                      >
                        Kanban Access
                      </label>
                    </section>
                  </div>
                  <div className="px-4 py-4 bg-gray-100 rounded-lg  mb-6">
                    <p className="capitalize font-semibold text-lg text-gray-800 mb-2">
                      Edit/Delete Access
                    </p>
                    <div className="grid grid-cols-3 gap-4">
                      <section className="flex items-center gap-3">
                        <Checkbox
                          className="cursor-pointer"
                          checked={inputData?.deletePendingStockIn}
                          onChange={(e) => {
                            setInputData((prev) => ({
                              ...prev,
                              deletePendingStockIn: e.target.checked,
                            }));
                          }}
                        />
                        <label
                          htmlFor="deletePendingStockIn"
                          className="text-sm text-gray-600 cursor-pointer"
                        >
                          Enable Delete Access To Pending Stock In
                        </label>
                      </section>
                      <section className="flex items-center gap-3">
                        <Checkbox
                          className="cursor-pointer"
                          checked={inputData?.archivePartAccess}
                          onChange={(e) => {
                            setInputData((prev) => ({
                              ...prev,
                              archivePartAccess: e.target.checked,
                            }));
                          }}
                        />
                        <label
                          htmlFor="deletePendingStockIn"
                          className="text-sm text-gray-600 cursor-pointer"
                        >
                          Enable Archive Access To Items
                        </label>
                      </section>
                      <section className="flex items-center gap-3">
                        <Checkbox
                          className="cursor-pointer"
                          checked={inputData?.canDeleteInpage}
                          onChange={(e) => {
                            setInputData((prev) => ({
                              ...prev,
                              canDeleteInpage: e.target.checked,
                            }));
                          }}
                        />
                        <label
                          htmlFor="deletePendingStockIn"
                          className="text-sm text-gray-600 cursor-pointer"
                        >
                          Enable Acces to Delete Stock In Items
                        </label>
                      </section>
                      <section className="flex items-center gap-3">
                        <Checkbox
                          className="cursor-pointer"
                          checked={inputData?.canEditQuotation}
                          onChange={(e) => {
                            setInputData((prev) => ({
                              ...prev,
                              canEditQuotation: e.target.checked,
                            }));
                          }}
                        />
                        <label
                          htmlFor="deletePendingStockIn"
                          className="text-sm text-gray-600 cursor-pointer"
                        >
                          Enable Acces to Edit Quotation
                        </label>
                      </section>
                      <section className="flex items-center gap-3">
                        <Checkbox
                          className="cursor-pointer"
                          checked={inputData?.canEditSalesOrder}
                          onChange={(e) => {
                            setInputData((prev) => ({
                              ...prev,
                              canEditSalesOrder: e.target.checked,
                            }));
                          }}
                        />
                        <label
                          htmlFor="deletePendingStockIn"
                          className="text-sm text-gray-600 cursor-pointer"
                        >
                          Enable Acces to Edit Sales Order
                        </label>
                      </section>
                      <section className="flex items-center gap-3">
                        <Checkbox
                          className="cursor-pointer"
                          checked={inputData?.canEditPurchaseOrder}
                          onChange={(e) => {
                            setInputData((prev) => ({
                              ...prev,
                              canEditPurchaseOrder: e.target.checked,
                            }));
                          }}
                        />
                        <label
                          htmlFor="deletePendingStockIn"
                          className="text-sm text-gray-600 cursor-pointer"
                        >
                          Enable Acces to Edit Purchase Order
                        </label>
                      </section>
                    </div>
                  </div>
                  <div className="px-4 py-4 bg-gray-100 rounded-lg  mb-6">
                    <p className="capitalize font-semibold text-lg text-gray-800 mb-2">
                      Archive option in sales order management
                    </p>
                    <div className="grid grid-cols-3">
                      <section className="flex items-center gap-3">
                        <Checkbox
                          className="cursor-pointer"
                          checked={inputData?.archiveSalesOrderManagement}
                          onChange={(e) => {
                            setInputData((prev) => ({
                              ...prev,
                              archiveSalesOrderManagement: e.target.checked,
                            }));
                          }}
                        />
                        <label
                          htmlFor="deletePendingStockIn"
                          className="text-sm text-gray-600 cursor-pointer"
                        >
                          Enable Archive Access To Sales Order Management
                        </label>
                      </section>
                    </div>
                  </div>
                  <div className="px-4 py-4 bg-gray-100 rounded-lg">
                    <p className="capitalize font-semibold text-lg text-gray-800 mb-2">
                      Archive option in purchase order management
                    </p>
                    <div className="grid grid-cols-3">
                      <section className="flex items-center gap-3">
                        <Checkbox
                          className="cursor-pointer"
                          checked={inputData?.archivePurchaseOrderManagement}
                          onChange={(e) => {
                            setInputData((prev) => ({
                              ...prev,
                              archivePurchaseOrderManagement: e.target.checked,
                            }));
                          }}
                        />
                        <label
                          htmlFor="deletePendingStockIn"
                          className="text-sm text-gray-600 cursor-pointer"
                        >
                          Enable Archive Access To Purchase Order Management
                        </label>
                      </section>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {selectedTab.id === 3 && (
              <div className="w-full h-screen overflow-auto bg-gray-50 p-6 pb-[14rem]">
                <div className="w-full !min-h-[50rem] h-100vh max-w-7xl bg-white shadow-lg rounded-lg p-8 pb-[13rem]">
                  {/* Kanban Info Heading */}
                  <p className="text-2xl font-semibold text-gray-800 mb-4">
                    Kanban Information
                  </p>
                  <p className="text-sm text-gray-500 mb-6">
                    Select the Kanban information categories that you wish to
                    access.
                  </p>

                  {/* Kanban Info MultiSelect */}
                  <div className="w-full">
                    <MultiSelect
                      className="!w-full min-w-[10rem] border border-gray-300 rounded-md shadow-sm"
                      id="kanbanInfo"
                      value={inputData.kanbanFilter || []}
                      onChange={inputChangeHandler}
                      options={getAllColumns()}
                      name="kanbanFilter"
                      required
                      placeholder="Select Kanban Info Access"
                    />
                  </div>
                  <div className="w-full  bg-white  mt-10">
                    <p className="text-xl font-semibold text-gray-800 mb-4">
                      Selected Kanban Info:
                    </p>

                    {/* Ordered List of Kanban Info */}
                    <ol className="list-decimal list-inside space-y-2 text-gray-700">
                      {inputData.kanbanFilter?.map((depart, iddx) => (
                        <li key={iddx} className="text-sm  text-gray-700">
                          <label
                            htmlFor={`kanban-info-${depart}`}
                            className="text-gray-600 font-medium pl-2"
                          >
                            {depart}
                          </label>
                        </li>
                      ))}
                    </ol>
                  </div>
                </div>
              </div>
            )}
            {selectedTab.id === 4 && (
              <ColumnAccess setInputData={setInputData} inputData={inputData} />
            )}
            {selectedTab.id === 5 && (
              <StoreAccess setInputData={setInputData} inputData={inputData} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FullScreenUserModal;
