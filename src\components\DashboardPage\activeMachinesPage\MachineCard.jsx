import { useContext, useEffect, useState } from 'react';
import { ReactComponent as DownVector } from '../../../assets/svgs/DownVector.svg';
import { ReactComponent as UpVector } from '../../../assets/svgs/UpVector.svg';
import {
  calculateValueForViews,
  convertToHrsAndMins,
  getLocalDateTime,
  getMinutesPassed,
  handlePercentDeicmalValue,
} from '../../../helperFunction';

import Marquee from 'react-fast-marquee';
import { Store } from '../../../store/Store';
import { toCapitalize } from '../../../utils/toCapitalize';
import ProgressBar from '../../global/components/ProgressBar';
import RightSidebar from '../../global/components/RightSidebar';
import { TabButton, TabContainer } from '../../global/components/TabContainer';
import Table from '../../global/components/Table';

const MachineCard = ({
  machine,
  values,
  cuProjects,
  deviceDatas,
  deviceKpis,
  goalsTable,
}) => {
  const [count, setCount] = useState(0);
  const [newValues, setNewValues] = useState(values);
  const [cuProject, setCuProject] = useState(null);
  const [openSideBar, setOpenSideBar] = useState(false);
  const [machineDetails, setMachineDetails] = useState({
    upTime: [0, 0],
    downTime: [0, 0],
    speed: 0,
    oee: 0,
  });
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const kpiForMachine = defaultParam?.heatMapThresholds?.find(
    (item) => item?.machine === machine._id
  );

  const machineAndOperator = cuProject?.machineAndOperator?.find(
    (mao) => mao?.machine === machine?._id
  );

  const operator = machineAndOperator?.operator?.findLast((i) => i);

  const [Tabs, setTabs] = useState({
    error: {
      tab: 'error',
      active: true,
    },
    'Upcoming Jobs': {
      tab: 'Upcoming Jobs',
      active: false,
    },
  });

  const SetActiveTab = (tabname) => {
    let NewActiveTabs = {};
    for (let keys in Tabs) {
      if (Tabs[keys].tab === tabname) {
        NewActiveTabs[keys] = {
          tab: Tabs[keys].tab,
          active: true,
        };
      } else {
        NewActiveTabs[keys] = {
          tab: Tabs[keys].tab,
          active: false,
        };
      }
    }
    setTabs(NewActiveTabs);
  };

  useEffect(() => {
    if (machineAndOperator && values && machine) {
      const isActive = machineAndOperator?.status === 'active';

      machine?.devices?.forEach((device) => {
        device?.assignedDevice?.forEach((item) => {
          if (isActive) {
            setNewValues((prev) => ({
              ...prev,
              [item?.deviceId]: values?.[item?.deviceId] || {},
            }));
          } else {
            setNewValues((prev) => ({
              ...prev,
              [item?.deviceId]:
                cuProject?.lastValues?.[machine?.machineId]?.[item?.deviceId] ||
                {},
            }));
          }
        });
      });
    }
  }, [machineAndOperator, values, machine, cuProject?.lastValues]);

  useEffect(() => {
    if (!machine?.isManual) {
      const formula = machine?.projectId?.processGoalView?.parameters?.find(
        (param) => param.name === 'Batch Size'
      )?.formula;
      if (formula && newValues && machine.machineId) {
        const value = calculateValueForViews(formula, newValues, [
          machine.machineId,
        ]);
        setCount(value);
      }
    }
    cuProjects?.forEach((e) => {
      e?.machineAndOperator?.forEach((mao) => {
        if (mao.machine === machine._id) {
          setCuProject(e);
        }
      });
    });
  }, [machine, newValues, cuProjects]);

  useEffect(() => {
    const value = cuProject?.machineAndOperator?.find((item) => {
      return item.machine === machine?._id;
    });
    if (value) {
      // const countVal = value.manualStopData;
      // setCount(countVal);
      if (value?.manualStopData) {
        const countVal = value.manualStopData;
        setCount(countVal);
      } else {
        const countVal = value.manualPauseData[0];
        setCount(countVal);
      }
    }
  }, [cuProject, machine]);
  useEffect(() => {
    if (defaultParam && machineAndOperator) {
      const isComplete = machineAndOperator?.status === 'complete';
      const timePassed = getMinutesPassed(
        isComplete ? new Date(machineAndOperator?.stopTime) : new Date(),
        new Date(machineAndOperator?.startTime),
        defaultParam,
        isComplete
      );
      let hoursPassed = timePassed / 60;

      const macItemsPerHour = Math.floor(
        count / (hoursPassed !== 0 ? hoursPassed : 1)
      );

      let sum = 0;
      if (deviceDatas?.length) {
        const idleTimesData =
          deviceDatas?.filter(
            (item) =>
              item?.cuProject === cuProject?._id &&
              item?.machine === machineAndOperator?.machine &&
              item.type === 'kpi' &&
              item.data.hasOwnProperty('IDLE') //eslint-disable-line
          ) || [];
        sum =
          idleTimesData?.reduce((acc, curVal) => acc + +curVal?.data?.IDLE, 0) /
          60;
      } else {
        if (
          machineAndOperator?.pauseTime?.length ===
          machineAndOperator?.resumeTime?.length
        ) {
          for (let i = 0; i < machineAndOperator?.pauseTime?.length; i++) {
            sum += getMinutesPassed(
              new Date(machineAndOperator?.resumeTime[i]),
              new Date(machineAndOperator?.pauseTime[i]),
              defaultParam,
              true
            );
          }
        } else {
          for (let i = 0; i < machineAndOperator?.pauseTime?.length; i++) {
            sum += getMinutesPassed(
              machineAndOperator?.resumeTime[i]
                ? new Date(machineAndOperator?.resumeTime[i])
                : new Date(),
              new Date(machineAndOperator?.pauseTime[i]),
              defaultParam,
              false
            );
          }
        }
      }
      const idleTime = sum / 60;

      const tempUptime = hoursPassed - idleTime;

      const upTime = convertToHrsAndMins(tempUptime);
      const downTime = convertToHrsAndMins(idleTime);
      let oee = 0;

      if (kpiForMachine && deviceKpis) {
        const deviceKpi = deviceKpis
          ?.find((kpi) => kpi?.machine?._id === kpiForMachine?.machine)
          ?.kpis?.find((item) => item.name === kpiForMachine?.kpi);

        if (deviceKpi) {
          oee = handlePercentDeicmalValue(
            ((199 * +deviceKpi?.value) / timePassed) * 100
          );
        }
      }

      setMachineDetails({ upTime, downTime, speed: macItemsPerHour, oee });
    }
  }, [
    defaultParam,
    machineAndOperator,
    count,
    deviceDatas,
    kpiForMachine,
    deviceKpis,
    cuProject?._id,
  ]);

  // const statusColor =
  //   machine.status === 'active'
  //     ? 'bg-[#E3FFE3]'
  //     : machine.status === 'inactive'
  //     ? 'bg-[#c8deff]'
  //     : 'bg-[#FFD9D9]';
  const statusColor =
    machine.status === 'active'
      ? 'bg-[#c8deff]'
      : machine?.status === 'pause'
        ? 'bg-[#fbedb7]'
        : 'bg-[#FFD9D9]';
  // const borderColor =
  //   machine.status === 'active'
  //     ? 'border-[#E3FFE3]'
  //     : machine.status === 'inactive'
  //     ? 'border-[#c8deff]'
  //     : 'border-[#FFD9D9]';
  // const textColor =
  //   machine.status === 'active'
  //     ? 'text-[#77DD77]'
  //     :
  //     machine.status === 'inactive'
  //       ?
  //       'text-[#0070FF]'
  // : 'text-[#ED4040]';
  const textColor =
    machine.status === 'active'
      ? 'text-[#0070FF]'
      : machine?.status === 'pause'
        ? 'text-[#d9b008]'
        : 'text-[#ED4040]';
  // const progressColor =
  //   machine.status === 'active'
  //     ? '#77DD77'
  //     : machine.status === 'inactive'
  //     ? '#0070FF'
  //     : '#ED4040';
  // const darkBg =
  //   cuProject?.status === 'active'
  //     ? 'bg-[#77DD77]'
  //     : cuProject?.status === 'complete'
  //     ? 'bg-[#ED4040]'
  //     : 'bg-[#0070FF]';
  const darkBg =
    machine?.status === 'active'
      ? 'bg-[#0070FF]'
      : machine?.status === 'pause'
        ? 'bg-[#F1C40F]'
        : 'bg-[#ED4040]';

  const errorMessages = cuProject?.errorMessages?.filter(
    (em) => em.machineAndOperatorId === machineAndOperator?._id
  );

  return (
    <>
      {openSideBar && (
        <RightSidebar
          openSideBar={openSideBar}
          setOpenSideBar={setOpenSideBar}
          scale={736}
        >
          <>
            <section>
              <div className="heading">
                <h1>{machine?.machineName}</h1>
              </div>
              <div className="tabs">
                <TabContainer className="!mb-3 !mt-3">
                  {Object.keys(Tabs).map((tabdata) => {
                    return (
                      <TabButton
                        key={Tabs[tabdata]['tab']}
                        onClick={() => {
                          SetActiveTab(Tabs[tabdata].tab);
                        }}
                        isactive={Tabs[tabdata]['active']}
                      >
                        {toCapitalize(Tabs[tabdata].tab)}
                      </TabButton>
                    );
                  })}
                </TabContainer>
              </div>
              {Tabs['error']['active'] && (
                <div className="errortable mt-3">
                  <h3>Error</h3>
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>Job Name</Table.Th>
                        <Table.Th>Batch No.</Table.Th>
                        <Table.Th>Error Message</Table.Th>
                        <Table.Th>Pause Time</Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      <Table.Td>
                        {cuProject?.project?.modelName || '-'}(
                        {cuProject?.project?.id || '-'})
                      </Table.Td>
                      <Table.Td>
                        {cuProject?.batchInfo?.batchNo || '-'}
                      </Table.Td>
                      <Table.Td>
                        {cuProject?.errorMessages
                          ? cuProject?.errorMessages[0]?.error?.message ||
                            'No Error'
                          : '-'}
                      </Table.Td>
                      <Table.Td>
                        {cuProject?.machineAndOperator
                          ? getLocalDateTime(
                              cuProject?.machineAndOperator[0]?.pauseTime[
                                cuProject?.machineAndOperator[0]?.pauseTime
                                  .length - 1
                              ]
                            )
                          : '-'}
                      </Table.Td>
                    </Table.Body>
                  </Table>
                </div>
              )}
              {Tabs['Upcoming Jobs']['active'] && (
                <div className="machine-tab-container">
                  <h3>Upcoming Jobs</h3>
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>Job name</Table.Th>
                        <Table.Th>Start Date</Table.Th>
                        <Table.Th>End Date</Table.Th>
                        <Table.Th>Order Quantity</Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {goalsTable?.map((el, index) => {
                        const isBatchStarted =
                          el?.tableData[0]?.status === null;

                        return isBatchStarted ? (
                          <Table.Row key={index}>
                            <Table.Td>
                              {el?.createInput?.modelName || '-'}(
                              {el?.createInput?.id || '-'})
                            </Table.Td>
                            <Table.Td>
                              {getLocalDateTime(
                                el?.tableData[0]?.startDate || '-'
                              )}
                            </Table.Td>
                            <Table.Td>
                              {getLocalDateTime(el?.tableData?.slice(-1)[0]) ||
                                '-'}
                            </Table.Td>
                            <Table.Td>{el?.goalsData?.qty || '-'}</Table.Td>
                          </Table.Row>
                        ) : null;
                      })}
                    </Table.Body>
                  </Table>
                </div>
              )}
            </section>

            {/* <h5 className="my-4">Upcoming Jobs</h5>
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Td>JOB NAME</Table.Td>
                  <Table.Td>START DATE</Table.Td>
                  <Table.Td>END DATE</Table.Td>
                  <Table.Td>ORDER QUANTITY</Table.Td>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                {goalsTable.map((el, index) => {
                  const isBatchStarted = el?.tableData[0]?.status === null;

                  return isBatchStarted ? (
                    <Table.Row key={index}>
                      <Table.Td>{el?.createInput?.modelName}</Table.Td>
                      <Table.Td>
                        {getLocalDateTime(el?.tableData[0]?.startDate)}
                      </Table.Td>
                      <Table.Td>
                        {getLocalDateTime(el?.tableData?.slice(-1)[0])}
                      </Table.Td>
                      <Table.Td>{el?.goalsData?.qty}</Table.Td>
                    </Table.Row>
                  ) : null;
                })}
              </Table.Body>
            </Table> */}
          </>
        </RightSidebar>
      )}
      {/* <div
        className={`border-2 ${borderColor} rounded-xl w-[250px] h-auto shadow-sm shadow-gray-400 overflow-hidden bg-white cursor-pointer`}
        onClick={() => setOpenSideBar(true)}
      >
        <div className="flex flex-col h-full">
          <div className="p-1">
            <p
              className={`${textColor} ${statusColor} rounded-xl text-[0.5rem] p-1 px-2 w-fit`}
            >
              <span className="text-[0.6rem]">●</span>
              {cuProject ? (
                <>
                  {machine.status === 'active'
                    ? ' Active'
                    : machine.status === 'inactive'
                    ? ' Completed'
                    : ' Paused'}
                </>
              ) : (
                ' Null'
              )}
            </p>
            <div className="relative h-fit w-full flex flex-col items-center p-0 mt-[-30px]">
              <SemiCircleProgress
                percentage={machineDetails?.oee}
                size={{
                  width: 250,
                  height: 150,
                }}
                strokeWidth={5}
                strokeColor={progressColor}
                hasBackground={true}
                fontStyle={{ fontSize: '15', fontWeight: 'bold' }}
              />
              <p className="mt-[-50px] text-xs text-gray-600">OEE</p>
            </div>
            <div className={`flex justify-between ${textColor}`}>
              <div
                className={`m-1 py-1 px-2 flex flex-col justify-center items-center h-[60px] w-[28%] rounded-lg ${statusColor}`}
              >
                <p>Count</p>
                <h2>{count || 0}</h2>
              </div>
              <div
                className={`m-1 py-1 px-2 flex flex-col justify-center items-center h-[60px] w-[28%] rounded-lg ${statusColor}`}
              >
                <p>Speed</p>
                <h2>{machineDetails?.speed}</h2>
              </div>
              <div
                className={`m-1 py-1 px-2 flex flex-col justify-center items-center h-[60px] w-[28%] rounded-lg ${statusColor}`}
              >
                <p>Error</p>
                <h3>{errorMessages?.length || 0}</h3>
              </div>
            </div>
          </div>
          <Marquee
            pauseOnHover
            className={`w-full text-center text-white font-semibold py-3.5 ${darkBg}`}
          >
            {machine?.machineName} ({machine?.machineId})
          </Marquee>
          <div
            className={`h-full left-0 bottom-0 right-0 top-0 p-2 ${statusColor}`}
          >
            <div className="grid grid-cols-2">
              <div className="text-xs flex flex-col h-[60px] justify-center">
                <span className="text-[10px] font-medium text-[#667085]">
                  Job:
                </span>
                <p>
                  {cuProject
                    ? cuProject?.project?.id +
                      ' ' +
                      cuProject?.project?.modelName
                    : '-'}{' '}
                  <span className="opacity-0">space taker</span>
                </p>
              </div>
              <div className="text-xs flex flex-col justify-center">
                <span className="text-[10px] font-medium text-[#667085]">
                  Batch:
                </span>
                {cuProject ? (
                  <p>
                    {`${cuProject?.project?.createPo?.name}/${cuProject?.project?.modelName}/${cuProject?.batchInfo?.batchNo}`}{' '}
                    {cuProject?.subProcessData?.process ? (
                      <span className="text-xs">
                        ({cuProject?.subProcessData?.process})
                      </span>
                    ) : null}
                  </p>
                ) : (
                  '-'
                )}
              </div>
              <div className="flex items-center gap-1">
                <UpVector />
                <div className="flex flex-col">
                  <span className="text-[10px] font-medium text-[#667085]">
                    Uptime
                  </span>
                  <p className="text-md text-[#77dd77]">{`${machineDetails?.upTime?.[0]}h ${machineDetails?.upTime?.[1]}m`}</p>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <DownVector />
                <div className="flex flex-col">
                  <span className="text-[10px] font-medium text-[#667085]">
                    Downtime
                  </span>
                  <p className="text-md text-[#ed4040]">{`${machineDetails?.downTime?.[0]}h ${machineDetails?.downTime?.[1]}m`}</p>
                </div>
              </div>
              <div className="text-xs flex flex-col justify-center">
                <span className="text-[10px] font-medium text-[#667085]">
                  Worker Name:
                </span>
                <p>{operator?.user?.name || '-'}</p>
              </div>
              <div className="text-xs flex flex-col justify-center">
                <span className="text-[10px] font-medium text-[#667085]">
                  Start Time:
                </span>
                <p>
                  {machineAndOperator
                    ? getLocalDateTime(machineAndOperator?.startTime)
                    : '-'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div> */}
      <div
        className={`relative rounded-xl overflow-hidden w-[250px] shadow-lg bg-white direction-straight cursor-pointer ${
          !machine
            ? 'flex flex-col gap-2 justify-center items-center min-h-[330px]'
            : ''
        } `}
        onClick={() => setOpenSideBar(true)}
      >
        <div>
          <section
            className={`${textColor} w-full rounded-xl text-[0.8rem] flex justify-between font-semibold p-2 px-5 h-full`}
          >
            <p>
              {cuProject ? (
                <>
                  {machine.status === 'active'
                    ? ' Active'
                    : machine?.status === 'pause'
                      ? 'Paused'
                      : ' Inactive'}
                </>
              ) : (
                ' Inactive'
              )}
            </p>
          </section>
          <div className="flex flex-col items-center justify-center ">
            <div className="flex flex-col items-center ">
              <p className="text-3xl font-semibold text-[#343]">
                {machineDetails?.oee}
                <span className="text-xl">%</span>
              </p>

              <p className="text-xs text-[#aaa]">OEE</p>
            </div>

            <section className="w-full px-10">
              <ProgressBar
                max={100}
                progress={machineDetails?.oee}
                progressColor={darkBg}
              />
            </section>

            <div className={`flex justify-center my-2`}>
              <div
                className={`pr-2 border-r flex flex-col justify-center items-center ${textColor}`}
              >
                <p>Count</p>
                <h2>{count || 0}</h2>
              </div>
              <div
                className={`px-2 border-r flex flex-col justify-center items-center ${textColor}`}
              >
                <p>Speed</p>
                <h2>{machineDetails.speed || 0}</h2>
              </div>
              <div
                className={`pl-2 flex flex-col justify-center items-center ${textColor}`}
              >
                <p>Error</p>
                <h2>{errorMessages?.length || 0}</h2>
              </div>
            </div>
            <Marquee
              pauseOnHover
              className={`w-full text-center text-white font-semibold ${darkBg}`}
            >
              {machine?.machineName} ({machine?.machineId})
            </Marquee>
          </div>

          <div className={`${statusColor} h-full w-full`}>
            <div className={`px-5 ${statusColor} h-full w-full`}>
              <div className={` py-2 space-y-2`}>
                <p className="text-xs w-full flex justify-between">
                  <span className="font-semibold">Work Order:</span>{' '}
                  {cuProject ? cuProject?.project?.createPo?.name : '-'}{' '}
                </p>
                <p className="text-xs w-full flex justify-between">
                  <span className="font-semibold">Job:</span>{' '}
                  {cuProject ? cuProject?.project?.modelName : '-'}{' '}
                </p>

                <span className="text-xs w-full flex justify-between">
                  <span className="font-semibold">Batch:</span>{' '}
                  {cuProject ? (
                    <p>
                      {cuProject?.batchInfo?.batchName}
                      {cuProject?.subProcessData?.process ? (
                        <span className="text-xs">
                          ({cuProject?.subProcessData?.process})
                        </span>
                      ) : null}
                    </p>
                  ) : (
                    '-'
                  )}
                </span>
                <div className="grid grid-cols-2 text-xs gap-1 ">
                  <section className="flex flex-col">
                    <p className="font-semibold">Uptime</p>
                    <p className="flex items-center mt-1 gap-x-2 text-green-400 font-semibold">
                      <UpVector />{' '}
                      {`${machineDetails?.upTime?.[0]}h ${machineDetails?.upTime?.[1]}m`}
                    </p>
                  </section>
                  <section className="flex flex-col">
                    <p className="font-semibold">Downtime</p>
                    <p className="flex items-center mt-1 gap-x-2 text-red-400 font-semibold">
                      <DownVector />{' '}
                      {`${machineDetails?.downTime?.[0]}h ${machineDetails?.downTime?.[1]}m`}
                    </p>
                  </section>

                  <section
                    className={`flex flex-col mt-2 ${operator ? '' : 'pb-4'}`}
                  >
                    <p className="font-semibold">Worker Name: </p>
                    <p>{operator?.user?.name || '-'}</p>
                  </section>

                  <section
                    className={`flex flex-col mt-2 ${
                      machineAndOperator ? '' : 'pb-4'
                    }`}
                  >
                    <p className="font-semibold">Start time: </p>
                    <p>
                      {machineAndOperator
                        ? getLocalDateTime(machineAndOperator?.startTime)
                        : '-'}
                    </p>
                  </section>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MachineCard;
