export const createDepOrder = async ({
  id,
  department,
  refType,
  currentPage,
  userId,
  taskIdFromParams,
  createOrder,
  clearUrlParams,
  filterDetails,
}) => {
  if (clearUrlParams) {
    clearUrlParams();
  }
  await createOrder({
    data: {
      objRef: id,
      currentDepartment: department,
      refKey: refType,
      currentPage: currentPage,
      userId: userId,
      customTaskId: taskIdFromParams,
      filterDetails,
    },
  });
};

export const updateDepOrder = async ({
  id,
  department,
  refType,
  currentPage,
  orderId,
  updateOrder,
  // dispatch,
  user,
  filterDetails,
}) => {
  await updateOrder({
    data: {
      objRef: id,
      currentDepartment: department,
      refKey: refType,
      id: orderId,
      stepPage: currentPage,
      userId: user?._id,
      filterDetails,
    },
  });
  // if (res) {
  //   dispatch({
  //     type: 'REFETCH_KANBAN',
  //   });
  // }
};

export const handleInputChange = (e, setVisibleTiles) => {
  const { value } = e.target;
  setVisibleTiles(value.map((item) => item.value));
  // dispatch({
  //   type: 'SET_KANBAN_FILTER',
  //   payload: {
  //     data: temp,
  //   },
  // });
};

export const getNextColumns = ({ defaultParam, column }) => {
  if (column?.id === 'Completed') return ['Completed'];
  defaultParam = defaultParam?.defaultParam
    ? defaultParam?.defaultParam
    : defaultParam;
  let currentNode;
  // let step = getStep();
  for (let node of defaultParam?.departmentFlow?.nodeStructure?.nodes) {
    if (node?.data?.selectedPage?.[0]?.label === column?.label) {
      currentNode = node;
      break;
    }
  }

  let correspondingNodes = [];
  for (let edge of defaultParam?.departmentFlow?.nodeStructure?.edges) {
    if (edge?.target === currentNode?.id) {
      correspondingNodes.push(edge?.source);
    }
  }
  let columns = [];
  for (let i in correspondingNodes) {
    for (let node of defaultParam?.departmentFlow?.nodeStructure?.nodes) {
      if (correspondingNodes[i] === node?.id) {
        columns.push(node?.data?.selectedPage?.[0]);
      }
    }
  }
  return columns;
};

export const isLastColumn = ({ defaultParam, column }) => {
  let currentNode;
  defaultParam = defaultParam?.defaultParam
    ? defaultParam?.defaultParam
    : defaultParam;
  // let step = getStep();
  for (let node of defaultParam?.departmentFlow?.nodeStructure?.nodes) {
    if (node?.data?.selectedPage?.[0]?.label === column?.label) {
      currentNode = node;
      break;
    }
  }

  let correspondingEdge;
  for (let edge of defaultParam?.departmentFlow?.nodeStructure?.edges) {
    if (edge?.target === currentNode?.id) {
      correspondingEdge = edge;
      break;
    }
  }
  let isLastColumn = true;
  for (let node of defaultParam?.departmentFlow?.nodeStructure?.nodes) {
    if (node?.id === correspondingEdge?.source) {
      isLastColumn = false;
      break;
    }
  }

  return isLastColumn;
};

export const handleShiftColumn = async ({
  nextColumns,
  isPartial,
  selectedColumns,
  column,
  partiallyDone,
  setOpenShiftModal,
  // dispatch,
  shiftKanbanColumn,
  defaultParam,
  card,
}) => {
  let columns = selectedColumns;
  if (isPartial) {
    if (isLastColumn({ defaultParam, column }) === true) {
      await partiallyDone({
        id: card?._id,
        page: column?.label,
        isComplete: true,
      });
      setOpenShiftModal({
        open: false,
        isPartial: false,
      });
      // dispatch({
      //   type: 'PARTIALLY_SHIFT_CARD',
      //   payload: {
      //     data: card,
      //     nextColumns: ['Completed'],
      //     currentColumn: column?.page?.label,
      //   },
      // });
      // await refetch();
    } else {
      await partiallyDone({
        id: card?._id,
        page: column?.label,
        data: {
          nextColumns: nextColumns ? nextColumns : columns,
        },
        isComplete: false,
      });
      setOpenShiftModal({
        open: false,
        isPartial: false,
      });
      // dispatch({
      //   type: 'PARTIALLY_SHIFT_CARD',
      //   payload: {
      //     data: card,
      //     nextColumns: nextColumns ? nextColumns : columns,
      //     currentColumn: column?.page?.label,
      //   },
      // });
      // await refetch();
    }
  } else {
    if (isLastColumn({ defaultParam, column }) === true) {
      await shiftKanbanColumn({
        id: card?._id,
        page: column?.label,
        isComplete: true,
      });
      setOpenShiftModal({
        open: false,
        isPartial: false,
      });
      // dispatch({
      //   type: 'SHIFT_CARD',
      //   payload: {
      //     data: card,
      //     nextColumns: ['Completed'],
      //     currentColumn: column?.page?.label,
      //   },
      // });
      // await refetch();
    } else {
      await shiftKanbanColumn({
        id: card?._id,
        page: column?.label,
        data: {
          nextColumns: nextColumns ? nextColumns : columns,
        },
        isComplete: false,
      });
      // dispatch({
      //   type: 'SHIFT_CARD',
      //   payload: {
      //     data: card,
      //     nextColumns: nextColumns ? nextColumns : columns,
      //     currentColumn: column?.page?.label,
      //   },
      // });
      setOpenShiftModal({
        open: false,
        isPartial: false,
      });
      // await refetch();
    }
  }
};

export const handleUpdateAssignUser = async ({
  user,
  updateAssignUsers,
  selectedUsers,
  card,
  toast,
  setUserModal,
}) => {
  let adminId;
  if (user.role === 'admin' || user.role === 'superuser') {
    adminId = user?._id;
  }
  const res = await updateAssignUsers({
    selectedUsers: selectedUsers,
    id: card?._id,
    adminId: adminId,
  });
  if (res) {
    toast.success('Assigned User Successfully');
    setUserModal(false);
  }
};

export const getStep = ({ card, column }) => {
  if (card?.steps) {
    for (let step of card?.steps) {
      if (column?.page?.label === step?.stepPage) {
        return step;
      }
    }
  }
  return undefined;
};

export const openHistorySidebar = ({ setHistorySidebar, card }) => {
  setHistorySidebar({
    open: true,
    steps: card?.steps,
    delayReasons: card?.delayReasons,
    orderId: card?._id,
  });
};

export const canUpgrade = ({ card, column }) => {
  if (card?.currentStep === 0) return false;
  let step = getStep({ card, column });
  if (step) {
    return false;
  } else {
    return true;
  }
};

export const onUpgrade = async ({
  card,
  column,
  // dispatch,
  generatePrefixId,
  navigate,
  defaultParam,
}) => {
  let getSalesData = false;
  let prevStep = card?.currentStep - 1;
  if (
    column?.label === 'Sales Inquiry' ||
    column?.label === 'Quotation' ||
    column?.label === 'Sales Order'
  ) {
    getSalesData = true;
    let prevSteps = [];
    for (let step of card?.steps) {
      if (step?.stepNumber === prevStep) {
        prevSteps.push(step);
      }
    }
  }
  let willEdit = false;
  // let editId = "";
  for (let i of card?.steps) {
    if (i?.stepNumber === card?.currentStep && i?.stepPage === column?.label) {
      willEdit = true;
      // editId = i?._id;
      break;
    }
  }
  if (willEdit) {
    navigate(`${column?.value}`);
  } else {
    let taskId =
      card?.taskId || generatePrefixId(defaultParam?.prefixIds?.['taskId']);
    navigate(
      `${column?.value}?kanban=true&department=${column?.data?.selectedDepartment?.name}&page=${column?.label}&orderId=${card?._id}&refType=${column?.label}&getSalesData=${getSalesData}&taskID=${taskId}`
    );
  }
};

export const setDatesForDelayReason = ({
  remainingTime,
  setReasonForDelay,
}) => {
  const delayedTimeInDays = remainingTime?.days;
  if (delayedTimeInDays === 0) {
    return;
  }
  const currentTime = new Date();
  const startDate = new Date(currentTime);
  startDate.setDate(startDate.getDate() - delayedTimeInDays);
  // Generate delay reasons array
  const delayReasons = [];
  for (let i = 0; i < delayedTimeInDays; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const formattedDate = date.toISOString().split('T')[0];
    delayReasons.push({
      reason: ``,
      delayDate: formattedDate,
    });
  }
  if (delayReasons.length > 0) {
    setReasonForDelay(delayReasons);
  }
};

export const handleDone = async ({
  setInitialScrollIndex,
  column,
  card,
  defaultParam,
  remainingTime,
  setReasonForDelay,
  setIsReasonModalOpen,
  selectedColumns,
  partiallyDone,
  setOpenShiftModal,
  dispatch,
  shiftKanbanColumn,
  toast,
  index,
  canForwardCard,
}) => {
  const cardId = card?.steps?.[card?.currentStep]?.data;
  if (!cardId) {
    toast.error(`Please create document for ${card?.currentPage?.[0]}`, {
      toastId: 'Please create',
    });
    return;
  }
  const res = await canForwardCard({
    query: {
      cslug: column?.value,
      cardId,
    },
  }).unwrap();

  if (!res?.canForwardCard) {
    toast.error('Approval required', { toastId: 'Approval required' });
    return;
  }

  let canMarkAsDone = true;
  setInitialScrollIndex(index);
  let currentColumn = column?.label;
  if (currentColumn === 'Quotation') {
    for (let step of card?.steps) {
      if (
        step?.stepPage === 'Quotation' &&
        (step?.data?.quoteStatus === 'pending' ||
          step?.data?.quoteStatus === 'rejected')
      ) {
        canMarkAsDone = false;
        break;
      }
    }
  }
  if (defaultParam?.projectDefaults?.disableApprovals) {
    canMarkAsDone = true;
  }
  if (canMarkAsDone) {
    if (remainingTime.isExceed) {
      setDatesForDelayReason({
        remainingTime,
        setReasonForDelay,
      });
      setIsReasonModalOpen(true);
    } else {
      let nextColumns = getNextColumns({
        defaultParam,
        column,
      });
      nextColumns = nextColumns?.map((elem) => elem?.label);
      if (nextColumns?.length > 1) {
        if (isLastColumn({ defaultParam, column })) {
          handleShiftColumn({
            nextColumns: selectedColumns,
            isPartial: false,
            selectedColumns,
            column,
            partiallyDone,
            setOpenShiftModal,
            dispatch,
            shiftKanbanColumn,
            defaultParam,
            card,
          });
        } else {
          setOpenShiftModal({
            open: true,
            isPartial: false,
          });
        }
      } else {
        handleShiftColumn({
          nextColumns: nextColumns,
          isPartial: false,
          selectedColumns,
          column,
          partiallyDone,
          setOpenShiftModal,
          dispatch,
          shiftKanbanColumn,
          defaultParam,
          card,
        });
      }
    }
  } else {
    toast.error('Quotation approval pending');
  }
};

export const handleForward = async ({
  setInitialScrollIndex,
  column,
  card,
  defaultParam,
  remainingTime,
  setReasonForDelay,
  setIsReasonModalOpen,
  selectedColumns,
  partiallyDone,
  setOpenShiftModal,
  dispatch,
  shiftKanbanColumn,
  toast,
  index,
  canForwardCard,
}) => {
  const cardId = card?.steps?.[card?.currentStep]?.data;
  if (!cardId) {
    toast.error(`Please create document for ${card?.currentPage?.[0]}`, {
      toastId: 'Please create',
    });
    return;
  }

  const res = await canForwardCard({
    query: {
      cslug: column?.value,
      cardId,
    },
  }).unwrap();

  if (!res?.canForwardCard) {
    toast.error('Approval required', { toastId: 'Approval required' });
    return;
  }

  let canMarkAsDone = true;
  setInitialScrollIndex(index);
  let currentColumn = column?.label;
  if (currentColumn === 'Quotation') {
    for (let step of card?.steps) {
      if (
        step?.stepPage === 'Quotation' &&
        (step?.data?.quoteStatus === 'pending' ||
          step?.data?.quoteStatus === 'rejected')
      ) {
        canMarkAsDone = false;
        break;
      }
    }
  }
  if (defaultParam?.projectDefaults?.disableApprovals) {
    canMarkAsDone = true;
  }
  if (canMarkAsDone) {
    if (remainingTime.isExceed) {
      setDatesForDelayReason({
        remainingTime,
        setReasonForDelay,
      });
      setIsReasonModalOpen(true);
    } else {
      let nextColumns = getNextColumns({
        defaultParam,
        column,
      });
      nextColumns = nextColumns?.map((elem) => elem?.label);
      if (nextColumns?.length > 1) {
        if (isLastColumn({ defaultParam, column })) {
          handleShiftColumn({
            nextColumns: selectedColumns,
            isPartial: true,
            selectedColumns,
            column,
            partiallyDone,
            setOpenShiftModal,
            dispatch,
            shiftKanbanColumn,
            defaultParam,
            card,
          });
        } else {
          setOpenShiftModal({
            open: true,
            isPartial: true,
          });
        }
      } else {
        handleShiftColumn({
          nextColumns: nextColumns,
          isPartial: true,
          selectedColumns,
          column,
          partiallyDone,
          setOpenShiftModal,
          dispatch,
          shiftKanbanColumn,
          defaultParam,
          card,
        });
      }
    }
  } else {
    toast.error('Quotation approval pending');
  }
};

export const handleArchive = async ({
  id,
  archiveOrder,
  dispatchFn,
  user,
  card,
  apiSlice,
}) => {
  await archiveOrder({
    id,
    data: { user: user._id, archive: card?.archive.includes(user?._id) },
  });
  dispatchFn(apiSlice.util.invalidateTags(['DepartmentalOrders']));
};

export const handleUpdateRemainderDateAndTime = async ({
  updateReminderDateAndTime,
  reminderData,
  reminderRemarks,
  user,
  card,
  setReminderRemarks,
  toast,
  setReminderModal,
}) => {
  const res = await updateReminderDateAndTime({
    date: reminderData.date,
    time: reminderData.time,
    id: card?._id,
    reminderRemarks: reminderRemarks,
    reminderSetBy: user?._id,
  });

  if (res) {
    toast.success('Updated remainder time and date');
    setReminderModal(false);
  }
  setReminderRemarks('');
};

export const handleAssignTags = async ({
  addCustomTags,
  setInitialScrollIndex,
  setShowSelectTags,
  toast,
  index,
  card,
  selectedTags,
}) => {
  await addCustomTags({ _id: card?._id, tags: selectedTags });
  setInitialScrollIndex(index);
  setShowSelectTags(false);
  toast.success('Tags Assigned SuccessFully');
};

export const handleFileChange = async (file, card, toast, addMedia) => {
  try {
    if (file) {
      await addMedia({ id: card?._id, file });
      toast.success('Media Added Successfully');
    }
  } catch (error) {
    toast.error(error);
  }
};
