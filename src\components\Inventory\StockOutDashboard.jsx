import { Input, Table, Tag } from 'antd';
import { useEffect, useState } from 'react';
import * as XLSX from 'xlsx';
import useDebounceValue from '../../hooks/useDebounceValue';
import {
  useLazyGetFilterOptionsForInventoryDashboardQuery,
  useLazyGetInventoryDashboardStockOutQuery,
} from '../../slices/transactionsApiSlice';
import Pagination from '../global/components/Pagination';
import InventoryDashboardFilterAndExport from './InventoryDashboardFilterAndExport';

const StockOutDashboard = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(100);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    timePeriod: 'All Time',
    itemNames: [],
    uoms: [],
  });
  const debounceValue = useDebounceValue(searchTerm);

  const [getstockOutData, { data: stockOutData, isLoading: stockOutLoading }] =
    useLazyGetInventoryDashboardStockOutQuery();
  const [getCSVData, { isLoading: csvLoading }] =
    useLazyGetFilterOptionsForInventoryDashboardQuery(); // Used Filter Options Because this query also return the Whole Data

  useEffect(() => {
    getstockOutData({
      page,
      limit,
      searchTerm: debounceValue,
      filters: {
        ...filters,
        uoms: filters?.uoms?.map((itm) => itm?.value),
        itemNames: filters?.itemNames?.map((itm) => itm?.value),
      },
    });
  }, [page, limit, debounceValue, filters, getstockOutData]);

  const fetchAndExport = async (exportType) => {
    const response = await getCSVData({ type: 'stockOut' });
    const headers = ['Sr No', 'Item Name', 'Total Stock Out Qty', 'UOM'];
    const dataSource = exportType === 'filtered' ? csvData : response.data;

    const dataToExport = dataSource.map((item, idx) => {
      return [
        idx + 1,
        item.object,
        item.stockOut,
        item?.part?.uom || item?.product?.uom || 'N/A',
      ];
    });

    const ws = XLSX.utils.aoa_to_sheet([headers, ...dataToExport]);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, `Stock Out Dashboard`);

    const range = XLSX.utils.decode_range(ws['!ref']);
    const wscols = [];
    for (let C = range.s.c; C <= range.e.c; ++C) {
      let max = 0;
      for (let R = range.s.r; R <= range.e.r; ++R) {
        const cell = ws[XLSX.utils.encode_cell({ c: C, r: R })];
        if (cell && cell.v) {
          max = Math.max(max, String(cell.v).length);
        }
      }
      wscols.push({ wch: max + 2 });
    }
    ws['!cols'] = wscols;

    XLSX.writeFile(
      wb,
      `stockOutDashboard_${exportType === 'filtered' ? 'filtered' : 'all'}.xlsx`,
      { bookType: 'xlsx' }
    );
  };

  const columns = [
    {
      title: 'Item Name',
      dataIndex: 'object',
      key: 'object',
      render: (text) => <b>{text}</b>,
    },
    {
      title: 'Total Stocked Out',
      dataIndex: 'stockOut',
      key: 'stockOut',
      render: (stockOut) => (
        <Tag
          className="min-w-14 text-center"
          color={stockOut > 50 ? 'volcano' : 'green'}
        >
          {stockOut}
        </Tag>
      ),
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      render: (_, item) => {
        const uom = item?.part?.uom || item?.product?.uom;
        return (
          <Tag className="min-w-24 text-center" color="gold">
            {uom || 'N/A'}
          </Tag>
        );
      },
    },
  ];

  const csvData =
    stockOutData?.results?.map((item, idx) => ({
      ...item,
      serialNumber: idx + 1,
      object: item.object,
      stockOut: item.stockOut,
    })) || [];

  return (
    <div className="p-5 bg-white shadow-md rounded-lg">
      <div className="flex justify-self-end gap-x-2 items-center">
        <Input.Search
          placeholder="Search"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <InventoryDashboardFilterAndExport
          filters={filters}
          setFilters={setFilters}
          type={'stockIn'}
          fetchAndExport={fetchAndExport}
          isCsvLoading={csvLoading}
          csvData={csvData}
        />
      </div>
      <Table
        columns={columns}
        dataSource={stockOutData?.results}
        rowKey="name"
        pagination={false}
        loading={stockOutLoading}
        className="mt-4"
      />
      <Pagination
        limit={limit}
        page={page}
        totalPages={stockOutData?.totalPages}
        totalResults={stockOutData?.totalItems}
        setPage={setPage}
        setLimit={setLimit}
        className="w-full mt-4"
      />
    </div>
  );
};

export default StockOutDashboard;
