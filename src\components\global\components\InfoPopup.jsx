import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { FiInfo } from 'react-icons/fi';

export const InfoPopup = ({ children, title, description, isPersistent }) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (isPersistent) {
      setIsOpen(true);
    }
  }, [isPersistent]);

  const closeModal = () => {
    if (!isPersistent) {
      setIsOpen(false);
    }
  };

  const openModal = () => {
    if (!isPersistent) {
      setIsOpen(true);
    }
  };

  return (
    <>
      {!isPersistent && (
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={openModal}
          className="text-blue-500 hover:text-blue-600 transition-colors duration-200"
        >
          <FiInfo className="w-5 h-5" />
        </motion.button>
      )}

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={closeModal}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: 'spring', damping: 20, stiffness: 300 }}
              className="w-full max-w-md bg-white rounded-2xl p-6 shadow-xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="mb-6 flex justify-center">
                <FiInfo className="text-5xl text-blue-500" />
              </div>
              <h3 className="text-lg font-semibold leading-6 text-gray-900 mb-2">
                {title}
              </h3>
              <p className="text-sm text-gray-500 mb-4">{description}</p>
              <div className="mt-4 text-sm text-gray-700 space-y-2">
                {children}
              </div>
              {!isPersistent && (
                <div className="mt-6 flex justify-center">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                    onClick={closeModal}
                  >
                    Got it, thanks!
                  </motion.button>
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
