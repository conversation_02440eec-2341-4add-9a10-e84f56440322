const Directions = ({
  isMobile,
  isTablet,
  isComplete,
  scrap,
  isReversed,
  isLastInRow,
  stoplocation,
  haltData,
}) => {
  if (isLastInRow) {
    return (
      <div className="absolute z-50 top-[104%] left-1/2 -translate-x-1/2">
        <div className="bg-[#F3F4F6] rounded-md w-11 h-16 flex flex-col items-center justify-center text-[12px] font-medium text-gray-700 relative">
          <span className="rotate-90 whitespace-nowrap">
            Scrap&nbsp;<strong>{isComplete ? scrap || '-' : '-'}</strong>
          </span>

          {/* Tail pointer */}
          <div className="absolute left-1/2 top-full -translate-x-1/2 w-0 h-0 border-x-[10px] border-x-transparent border-t-[10px] border-t-[#F3F4F6]"></div>

          {/* Halt time */}
          {haltData?.status && (
            <div className="absolute bottom-full mb-1 text-red-500 text-[11px] font-semibold text-center w-max">
              Halt {haltData?.hrs < 1 ? 0 : haltData?.hrs}h
              {haltData?.mins < 1 ? 0 : haltData?.mins}m
            </div>
          )}
        </div>
        <div className="text-[11px] font-medium text-center mt-1 text-gray-600">
          {stoplocation}
        </div>
      </div>
    );
  }

  return (
    <div
      className={`absolute z-50 w-24 h-8 bg-[#F3F4F6] rounded-md text-gray-700 text-sm font-medium
        ${
          isMobile || isTablet
            ? isReversed
              ? '-rotate-90 top-[110%] left-[50%]'
              : 'rotate-90 top-[110%] left-[50%]'
            : isReversed
              ? '-rotate-0 top-[50%] left-[-20%]'
              : 'rotate-0 top-[50%] left-[121%]'
        }
        -translate-x-1/2 flex items-center justify-center`}
    >
      Scrap&nbsp;<strong>{isComplete ? scrap || '-' : '-'}</strong>
      {/* Arrow pointer */}
      <div
        className={`absolute top-0 w-0 h-0 border-[8px] border-y-transparent border-[#F3F4F6] ${
          isReversed
            ? 'right-full border-l-transparent'
            : 'left-full border-r-transparent'
        }`}
      ></div>
      {/* Halt info */}
      {haltData?.status && (
        <div className="absolute bottom-full mb-[3px] left-1 text-red-500 text-[11px] font-semibold">
          Halt {haltData?.hrs < 1 ? 0 : haltData?.hrs}h
          {haltData?.mins < 1 ? 0 : haltData?.mins}m
        </div>
      )}
      {/* Stop location */}
      <div className="absolute top-full mt-[3px] text-[11px] font-medium text-center text-gray-600">
        {stoplocation}
      </div>
    </div>
  );
};

export default Directions;
