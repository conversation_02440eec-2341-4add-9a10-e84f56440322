import { apiSlice } from './apiSlice';

const baseRoute = '/v1/kanban';

const kanbanApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getKanbanData: builder.query({
      query: () => baseRoute,
      providesTags: ['DepartmentalOrders'],
    }),
    getColumnByUser: builder.query({
      query: () => baseRoute + `/columns`,
      providesTags: ['KanbanColumns'],
    }),
    getCardsByColumn: builder.query({
      query: ({
        columnName,
        page = 1,
        limit = 1,
        filter_name,
        filter_value,
        debounceSearch,
        searchField,
      }) =>
        baseRoute +
        `/cards/${columnName}?page=${page}&limit=${limit}&filter_name=${filter_name}&filter_value=${filter_value}&searchTerm=${debounceSearch}&searchField=${searchField}`,
      // serializeQueryArgs: ({ queryArgs }) => {
      //   return queryArgs.columnName;
      // },
      // merge: (currentCache, newItems, arg) => {
      //   if (arg?.arg?.page === 1) {
      //     return newItems;
      //   }
      //   return [...currentCache, ...newItems];
      // },
      // forceRefetch({ currentArg, previousArg }) {
      //   return currentArg !== previousArg
      // },
      providesTags: ['DepartmentalOrders'],
    }),
    getAllPendingStatusData: builder.query({
      query: () => baseRoute + '/pending',
      providesTags: ['PendingDatas'],
    }),
    getAllDepartmentalOrder: builder.query({
      query: () => '/v1/departmentalOrder',
      providesTags: ['DepartmentalOrders'],
    }),
    getOrderById: builder.mutation({
      query: ({ id }) => ({
        url: `/v1/departmentalOrder/orderInfo/${id}`,
        method: 'POST',
      }),
      invalidatesTags: ['DepartmentalOrders'],
    }),
    canForwardCard: builder.query({
      query: ({ query = {} }) =>
        baseRoute +
        `/canforward?cardId=${query?.cardId || ''}&cslug=${query?.cslug || ''}`,
    }),
    getAllCardsForFilterOptions: builder.query({
      query: () => baseRoute + `/filterOptionsofCards`,
      providesTags: ['DepartmentalOrders'],
    }),
    bulkApproveCards: builder.mutation({
      query: ({ cards, action }) => ({
        url: baseRoute + '/approve',
        method: 'POST',
        body: { cards, action },
      }),
      invalidatesTags: ['PendingDatas', 'DepartmentalOrders'],
    }),
  }),
});

export const {
  useLazyGetKanbanDataQuery,
  useGetKanbanDataQuery,
  useGetAllPendingStatusDataQuery,
  useGetAllDepartmentalOrderQuery,
  useGetOrderByIdMutation,
  useGetColumnByUserQuery,
  useGetCardsByColumnQuery,
  useLazyCanForwardCardQuery,
  useGetAllCardsForFilterOptionsQuery,
  useBulkApproveCardsMutation,
} = kanbanApiSlice;
