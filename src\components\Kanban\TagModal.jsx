import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  ColorPicker,
  Empty,
  Form,
  Input,
  Modal,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';

const { Title, Text } = Typography;
const { TextArea } = Input;

const TagModal = ({
  openModal,
  setOpenModal,
  handleTagsSubmit,
  allTags,
  setAllTags,
  deleteTag,
}) => {
  const [form] = Form.useForm();
  const [isAdding, setIsAdding] = useState(false);
  const [editIndex, setEditIndex] = useState(-1);
  const [intermediateAllTags, setIntermediateAllTags] = useState([]);

  useEffect(() => {
    if (openModal && allTags) {
      setIntermediateAllTags(allTags);
    }
  }, [openModal, allTags]);

  const handleSubmit = () => {
    setAllTags(intermediateAllTags);
    handleTagsSubmit(intermediateAllTags);
    setOpenModal(false);
  };

  const startAddingTag = () => {
    form.resetFields();
    form.setFieldsValue({
      color: '#1890ff',
    });
    setEditIndex(-1);
    setIsAdding(true);
  };

  const handleTagFormSubmit = (values) => {
    if (editIndex !== -1) {
      setIntermediateAllTags((prev) => [
        ...prev.slice(0, editIndex),
        {
          name: values.name,
          description: values.description,
          color: values.color,
        },
        ...prev.slice(editIndex + 1),
      ]);
    } else {
      setIntermediateAllTags((prev) => [
        ...prev,
        {
          name: values.name,
          description: values.description,
          color: values.color,
        },
      ]);
    }
    setIsAdding(false);
    setEditIndex(-1);
    form.resetFields();
  };

  const editTag = (record, index) => {
    setEditIndex(index);
    form.setFieldsValue({
      name: record.name,
      description: record.description,
      color: record.color,
    });
    setIsAdding(true);
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Tag
          color={record.color}
          style={{ padding: '4px 8px', fontSize: '14px' }}
        >
          {typeof text === 'object' ? text.name : text}
        </Tag>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Actions',
      key: 'action',
      width: 120,
      render: (_, record, index) => (
        <Space size="middle">
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined style={{ color: '#1890ff' }} />}
              onClick={() => editTag(record, index)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button
              type="text"
              icon={<DeleteOutlined style={{ color: '#ff4d4f' }} />}
              onClick={() => deleteTag(index, record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Modal
      title={<Title level={4}>Manage Tags</Title>}
      open={openModal}
      onCancel={() => setOpenModal(false)}
      width={700}
      footer={[
        <Button key="back" onClick={() => setOpenModal(false)}>
          Cancel
        </Button>,
        <Button
          key="clear"
          danger
          disabled={intermediateAllTags.length === 0}
          onClick={() => setIntermediateAllTags([])}
        >
          Clear All
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          Save Tags
        </Button>,
      ]}
    >
      <div className="mb-4">
        <Text type="secondary">
          Customize and organize your work with tags. Create, edit, or delete
          tags to better categorize your items.
        </Text>
      </div>

      <div className="my-4 flex justify-between items-center">
        <Title level={5} style={{ margin: 0 }}>
          Tag List
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={startAddingTag}
          disabled={isAdding}
        >
          Add Tag
        </Button>
      </div>

      <AnimatePresence>
        {isAdding && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="bg-gray-50 p-4 rounded-lg mb-4 border border-gray-200">
              <Form
                form={form}
                layout="vertical"
                onFinish={handleTagFormSubmit}
              >
                <div className="flex gap-4">
                  <Form.Item
                    name="name"
                    label="Tag Name"
                    rules={[
                      { required: true, message: 'Please enter a tag name' },
                    ]}
                    className="flex-1"
                  >
                    <Input placeholder="Enter tag name" />
                  </Form.Item>

                  <Form.Item name="color" label="Color">
                    <ColorPicker />
                  </Form.Item>
                </div>

                <Form.Item name="description" label="Description">
                  <TextArea
                    placeholder="Enter tag description (optional)"
                    autoSize={{ minRows: 2, maxRows: 4 }}
                  />
                </Form.Item>

                <div className="flex justify-end gap-2">
                  <Button onClick={() => setIsAdding(false)}>Cancel</Button>
                  <Button type="primary" htmlType="submit">
                    {editIndex !== -1 ? 'Update Tag' : 'Create Tag'}
                  </Button>
                </div>
              </Form>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <Table
        columns={columns}
        dataSource={intermediateAllTags?.map((tag, index) => ({
          ...tag,
          key: index,
        }))}
        pagination={false}
        size="middle"
        locale={{
          emptyText: (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="No tags created yet"
            />
          ),
        }}
        className="tag-table"
      />
    </Modal>
  );
};

export default TagModal;
