import Table from '../global/components/Table';

const AssemblyItem = ({ bomAndAssemblyItem }) => {
  return (
    <div className="bg-white p-4 overflow-scroll">
      <Table className={'mt-5'}>
        <Table.Head>
          <Table.Row>
            <Table.Th>#</Table.Th>
            <Table.Th>Name</Table.Th>
            <Table.Th>Units</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {bomAndAssemblyItem?.assemblyItem?.rawMaterials?.map((data, dIdx) => (
            <Table.Row key={dIdx}>
              <Table.Td>{dIdx + 1}</Table.Td>
              <Table.Td>{data?.label}</Table.Td>
              <Table.Td>{data?.units}</Table.Td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
};

export default AssemblyItem;
