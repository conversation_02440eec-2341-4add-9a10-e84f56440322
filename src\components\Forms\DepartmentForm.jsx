import { Briefcase } from 'lucide-react';
import { BiTrash } from 'react-icons/bi';
import { toast } from 'react-toastify';
import { getLocalDateTime } from '../../helperFunction';
import { InfoTooltip } from '../global/components/InfoTooltip';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import Pagination from '../global/components/Pagination';
import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import Tooltip from '../global/components/ToolTip';
import TableGenerate from './TableGenerate';
import TrComponent from './TrComponent';

const DepartmentForm = ({
  forModal,
  isLoadingQuery,
  isMobile,
  checkedRows,
  selectAll,
  handleSelectAll,
  formTableHeading,
  rows,
  setField,
  setType,
  handleCheckBoxChange,
  deleteFormHandler,
  formData,
  setSearchParams,
  setFormCategoryType,
  setOpenEditModal,
  setIsCopy,
  viewForm,
  limit,
  page,
  totalPages,
  totalResults,
  setPage,
  setLimit,
  isOpen,
  handleSave,
  handleAdd,
  FormName,
  setFormName,
  FieldName,
  setFieldName,
  FieldType,
  setFieldType,
  ctg,
  noOption,
  setnoOption,
  fieldOptions,
  setFieldOptions,
  setlabel,
  label,
  tableOptions,
  setTableOptions,
  DragItem,
  DragOverItem,
  handleSort,
  removeHandler,
  isFetchingQuery,
  handleDepFormEdit,
  isLoadingCreate,
  onCloseFxn,
  tooltipContent,
  isMandatory,
  setIsMandatory,
  isKanbanEditable,
  setIsKanbanEditable,
  dragImage,
}) => {
  return (
    <div className=" w-full">
      {!forModal && (
        <div>
          {isLoadingQuery ? (
            <Spinner />
          ) : (
            <div className="w-full ">
              <Table className="w-full">
                <Table.Head>
                  <Table.Row>
                    {!isMobile && (
                      <Table.Th>
                        {checkedRows.length > 0 ? (
                          <div>
                            <input
                              type="checkbox"
                              className="mr-2"
                              checked={selectAll}
                              onChange={(e) => handleSelectAll(e)}
                            />
                            Select All
                          </div>
                        ) : (
                          ''
                        )}
                      </Table.Th>
                    )}
                    {formTableHeading?.map((h) => (
                      <Table.Th key={h}>
                        <div className="flex flex-row items-center">
                          <div>{h}</div>
                          {(h === 'Form Name' || h === 'Date') && (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              strokeWidth={1.5}
                              stroke="currentColor"
                              className="w-4 h-4 ml-2 cursor-pointer"
                              onClick={() => {
                                if (h === 'Form Name') {
                                  setField('formName');
                                } else if (h === 'Date') {
                                  setField('createdAt');
                                }
                                setType((prev) => {
                                  if (prev === 'desc') {
                                    return 'aesc';
                                  } else {
                                    return 'desc';
                                  }
                                });
                              }}
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M19.5 13.5L12 21m0 0l-7.5-7.5M12 21V3"
                              />
                            </svg>
                          )}
                        </div>
                      </Table.Th>
                    ))}
                  </Table.Row>
                </Table.Head>

                <Table.Body>
                  {rows?.map((pro, pIdx) => {
                    return (
                      <Table.Row key={pIdx} isFetching={isFetchingQuery}>
                        {!isMobile && (
                          <Table.Td>
                            <input
                              type="checkbox"
                              onChange={(event) =>
                                handleCheckBoxChange(event, pro)
                              }
                              checked={checkedRows.includes(pro)}
                            />
                          </Table.Td>
                        )}
                        <Table.Td>{getLocalDateTime(pro?.createdAt)}</Table.Td>
                        <Table.Td className={'md:!min-w-[24rem] md:!w-[24rem]'}>
                          {pro?.formName?.length > 55 ? (
                            <Tooltip text={pro?.formName}>
                              {pro?.formName.substring(0, 55) + '...'}
                            </Tooltip>
                          ) : (
                            pro?.formName || '-'
                          )}
                        </Table.Td>
                        {/* Below changes were made to add date and resolve styling bug */}

                        <Table.Options
                          className={'bg-white'}
                          onDelete={() =>
                            deleteFormHandler(pro?._id, pro?.isUsed)
                          }
                          onEdit={async () => await handleDepFormEdit(pro)}
                          onView={() => viewForm(pro?._id, 'DEPARTMENT')}
                          onCopy={() => {
                            setSearchParams(
                              (prev) => {
                                prev.set('form_id', pro?._id);
                                return prev;
                              },
                              {
                                replace: true,
                              }
                            );
                            setFormCategoryType('DEPARTMENT');
                            setOpenEditModal(true);
                            setIsCopy(true);
                          }}
                        />
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
              <Pagination
                limit={limit}
                page={page}
                totalPages={totalPages}
                totalResults={totalResults}
                setPage={setPage}
                setLimit={setLimit}
                className={`w-full`}
              />
            </div>
          )}
        </div>
      )}

      {isOpen && (
        <Modal
          title={'Create Forms'}
          svg={<Briefcase className="h-8 w-8" />}
          description={'Add Field Name and associated type'}
          onCloseModal={onCloseFxn}
          onSubmit={handleSave}
          onAdd={{
            label: 'Add',
            func: [handleAdd],
            step: [0],
          }}
          btnIsLoading={isLoadingCreate}
        >
          {() => (
            <>
              <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
                <div className="flex flex-col mt-5 col-span-2">
                  <label className="mb-1 font-semibold text-[#667085]">
                    Form Name{' '}
                    <span className="text-xl text-red-500 -mt-2 -ml-1">*</span>
                  </label>
                  <Input
                    value={FormName}
                    onChange={(e) => setFormName(e.target.value)}
                    placeholder="Form Name"
                  />
                </div>

                <div className="flex flex-col mt-5">
                  <label className="mb-1 font-semibold text-[#667085]">
                    Field Name{' '}
                    <span className="text-xl text-red-500 -mt-2 -ml-1">*</span>
                  </label>
                  <Input
                    value={FieldName}
                    onChange={(e) => setFieldName(e.target.value)}
                    placeholder="Field Name"
                  />
                </div>

                <div className="flex flex-col  mt-5 ml-4">
                  <label className="mb-1 font-semibold text-[#667085] flex items-center gap-[5px]">
                    Field Type
                    <span className="text-xl text-red-500 -mt-2 -ml-2">*</span>
                    <InfoTooltip
                      position="right"
                      id="adminRole"
                      width="400px"
                      isHtml={true}
                      content={tooltipContent()}
                    />
                  </label>
                  <Select
                    value={FieldType}
                    onChange={(e) => setFieldType(e.target.value)}
                    options={[...ctg, 'Section', 'Media'].map((option) => ({
                      name: option,
                      value: option,
                    }))}
                    placeholder="Select Type"
                  />
                </div>
                <div className="flex flex-row space-x-2 ml-3 items-center">
                  <Input
                    type="checkbox"
                    id="isMandatory"
                    name="isMandatory"
                    checked={isMandatory[FieldName] || false}
                    onChange={(e) => {
                      setIsMandatory((prev) => ({
                        ...prev,
                        [FieldName]: e.target.checked,
                      }));
                    }}
                  />
                  <label className="mb-1 font-semibold text-[#667085]">
                    Mandatory Field
                  </label>
                </div>
                <div className="flex flex-row space-x-2  ml-3 items-center ">
                  <Input
                    type="checkbox"
                    id="sequenceFollow"
                    name="sequenceFollow"
                    value={isKanbanEditable}
                    checked={isKanbanEditable}
                    onChange={(e) => {
                      setIsKanbanEditable(e.target.checked);
                    }}
                  />
                  <label className="mb-1 font-semibold text-[#667085]">
                    Allow editing in Kanban
                  </label>
                </div>
                {FieldType === 'MultiCheckbox' && (
                  <div className="flex flex-col  mt-5 ml-4">
                    <label className="mb-1 font-semibold text-[#667085]">
                      No of Options{' '}
                    </label>
                    <Input
                      type="number"
                      value={noOption}
                      onChange={(e) => {
                        if (e.target.value > 10) {
                          toast.error('Cannot Set More than 10 Options');
                          return;
                        }
                        setnoOption(e.target.value);
                      }}
                      placeholder="Number of Options"
                    />
                  </div>
                )}

                {(FieldType === 'DropDown' || FieldType === 'MultiSelect') && (
                  <>
                    <div className="mt-4">
                      <p className="text-sm font-medium">Dropdown Options</p>
                      {fieldOptions?.map((_, idX) => (
                        <div className="flex items-center gap-3 mt-2" key={idX}>
                          <Input
                            value={fieldOptions[idX]}
                            onChange={(e) =>
                              setFieldOptions((prev) =>
                                prev.map((el, index) => {
                                  if (index === idX) {
                                    return e.target.value;
                                  }
                                  return el;
                                })
                              )
                            }
                          />

                          <BiTrash
                            size={20}
                            className="cursor-pointer"
                            onClick={() =>
                              setFieldOptions((prev) =>
                                prev.filter((_, index) => idX !== index)
                              )
                            }
                          />
                        </div>
                      ))}
                      <p
                        className="text-sm text-blue-500 mt-2 cursor-pointer"
                        onClick={() => setFieldOptions((prev) => [...prev, ''])}
                      >
                        + Add More
                      </p>
                    </div>
                  </>
                )}
              </div>

              {noOption !== 0 && (
                <div className="flex flex-row flex-wrap justify-start mt-4 px-8 gap-x-3">
                  {[...Array(+noOption)].map((e, i) => (
                    <TrComponent
                      setlabel={setlabel}
                      i={i}
                      label={label}
                      key={i}
                    />
                  ))}
                </div>
              )}

              {FieldType === 'Table' && (
                <TableGenerate
                  tableOptions={tableOptions}
                  setTableOptions={setTableOptions}
                />
              )}

              {/* <div className="flex ml-[77%] w-fit  mt-3">
                <button
                  onClick={handleAdd}
                  type="button"
                  className="text-center text-sm bg-blue-primary hover:bg-blue-hover text-white rounded-[8px] w-[130px] h-[32px]"
                >
                  Add
                </button>
              </div> */}
              {/* --------------------Modal table------------------ */}
              {formData?.length > 0 ? (
                <div className="w-full  overflow-scroll mt-8">
                  <Table className="w-full">
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>FieldName</Table.Th>
                        <Table.Th>FieldType</Table.Th>
                        <Table.Th>Mandatory</Table.Th>
                        <Table.Th>Action</Table.Th>
                      </Table.Row>
                    </Table.Head>

                    <Table.Body>
                      {formData?.map((pro, pIdx) => {
                        return (
                          <Table.Row
                            key={pIdx}
                            isFetching={isFetchingQuery}
                            draggable
                            onDragStart={() => {
                              DragItem.current = pIdx;
                            }}
                            onDragEnter={() => {
                              DragOverItem.current = pIdx;
                            }}
                            onDragOver={(e) => e.preventDefault()}
                            onDragEnd={handleSort}
                          >
                            <Table.Td
                              className={'md:!min-w-[24rem] md:!w-[24rem]'}
                            >
                              {pro?.fieldName?.length > 55 ? (
                                <Tooltip>
                                  {pro?.fieldName?.slice(0, 55) + '...'}
                                </Tooltip>
                              ) : (
                                pro?.fieldName
                              )}
                            </Table.Td>
                            <Table.Td>{pro?.fieldType}</Table.Td>
                            <Table.Td>
                              <Table.Td>
                                {pro?.isMandatory ? 'Yes' : 'No'}
                              </Table.Td>
                            </Table.Td>
                            <Table.Td className="px-3 py-0.5">
                              <div className="flex justify-around">
                                <button
                                  type="button"
                                  onClick={() => removeHandler(pIdx)}
                                  className="outline-none p-4 rounded text-red-primary hover:text-white hover:bg-red-primary"
                                >
                                  Remove
                                </button>
                                <span className="">
                                  <img
                                    title="Drag the field"
                                    src={dragImage}
                                    className="w-5 h-5 mt-5 ml-2  bg:white  select-none  hover:scale-(1) ease-in duration-300"
                                  />
                                </span>
                              </div>
                            </Table.Td>
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                  </Table>
                </div>
              ) : null}
            </>
          )}
        </Modal>
      )}
    </div>
  );
};

export default DepartmentForm;
