import { useContext } from 'react';
import { Store } from '../../../store/Store';

const EditJobDetails = ({ jobDetails }) => {
  const { defaults } = useContext(Store);
  return (
    <>
      <div className="flex flex-col w-full">
        <div className="flex items-center gap-[5px]">
          <label className="block text-md mt-2 mb-1 text-black font-bold">
            Work Order
          </label>
        </div>
        <span
          className={`border text-left text-black outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full`}
        >
          {jobDetails?.workOrderId || ''}{' '}
        </span>
      </div>
      <div className="flex flex-col w-full">
        <div className="flex items-center gap-[5px]">
          <label
            htmlFor="selectinput"
            className="block text-md mt-2 mb-1 text-black font-bold"
          >
            {defaults?.defaultParam?.projectDefaults?.inputScreenLabel}
          </label>
        </div>

        <span
          className={`border text-left text-black outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full`}
        >
          {jobDetails?.inputScreen || ''}{' '}
        </span>
      </div>
      <div className="flex flex-col w-full">
        <label className="block text-md mt-2 mb-1 mr-2 text-black font-bold">
          Production Flow
        </label>
        <span
          className={`border text-left text-black outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full`}
        >
          {jobDetails?.productionFlow || ''}{' '}
        </span>
      </div>
      <div className="flex flex-col w-full">
        <label className="text-md mt-2 mb-1 mr-2 text-black font-bold">
          {defaults?.defaultParam?.projectDefaults?.modelLabel}
        </label>
        <div className="flex items-center">
          <span
            className={`border text-left text-black outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full`}
          >
            {jobDetails?.modelName}{' '}
          </span>
        </div>
      </div>
      <div className="flex flex-col w-full">
        <label className="block text-md mt-2 mb-1 mr-2 text-black font-bold">
          {defaults?.defaultParam?.projectDefaults?.projectIdentifier || ''}
        </label>
        <div className="flex items-center w-full">
          <span
            className={`border text-left text-black outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full`}
          >
            {jobDetails?.projectId || ''}{' '}
          </span>
        </div>
      </div>
    </>
  );
};

export default EditJobDetails;
