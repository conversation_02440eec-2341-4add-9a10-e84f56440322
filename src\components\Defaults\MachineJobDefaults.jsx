import { TrashIcon } from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { camelCaseString } from '../../helperFunction';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { MAX_CHAR_ALLOWED } from '../../utils/Constant';
import { getstrLen } from '../../utils/Getstrlen';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import Input from '../v2/global/components/Input';
import Select from '../global/components/Select';
import { PlusIcon } from '@heroicons/react/24/outline';

export default function MachineJobDefaults({ defaults }) {
  const [name, setName] = useState('');
  // const [type, setType] = useState('text');
  const [fieldType, setFieldType] = useState('string');
  const [fieldOptions, setFieldOptions] = useState([]);
  const [isEdit, setIsEdit] = useState(false);
  const [editIndex, setEditIndex] = useState(null);

  const [updateDefaults, { isLoading: isUpdateDefaultsLoading }] =
    useUpdateDefaultsMutation();

  const handleColDelete = async (index, isUsed) => {
    if (isUsed) {
      const userConfirmed = await customConfirm(
        'Are you sure? Data will be deleted!',
        'delete'
      );

      if (userConfirmed) {
        let data = defaults?.machineJobColumns?.filter((_, i) => i !== index);

        await updateDefaults({ machineJobColumns: data });

        toast.success('Column deleted successfully');
      }
    } else {
      const userConfirmed = await customConfirm('Are you sure?', 'delete');

      if (userConfirmed) {
        let data = defaults?.machineJobColumns?.filter((_, i) => i !== index);

        await updateDefaults({ machineJobColumns: data });

        toast.success('Column deleted successfully');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (getstrLen(name) > MAX_CHAR_ALLOWED) {
      toast.error(`Column name cannot exceeds ${MAX_CHAR_ALLOWED} characters`, {
        position: 'top-right',
        theme: 'colored',
        toastId: 'name len error',
      });
      return;
    }

    if (name === '') {
      toast.error('Column name cannot be empty');
      return;
    }

    if (isEdit) {
      let data = defaults?.machineJobColumns?.map((item, index) => {
        if (index === editIndex) {
          return {
            ...item,
            title: name,
            fieldType: fieldType,
            fieldOptions,
          };
        }
        return item;
      });
      await updateDefaults({ ...defaults, machineJobColumns: data }).unwrap();
      setIsEdit(false);
      setEditIndex(null);
      setName('');
      setFieldType('string');
      setFieldOptions([]);
      toast.success('Column updated successfully!');
      return;
    }

    const exist = defaults?.machineJobColumns?.find((item) => {
      return item.title.toLowerCase() === name.toLowerCase();
    });

    if (exist) {
      toast.warning('Same column cannot be added', {
        position: toast.POSITION.TOP_RIGHT,
        toastId: 'warning',
      });
      setName('');
      return;
    }

    const data = [
      ...(defaults?.machineJobColumns || []),
      {
        field: camelCaseString(name),
        title: name,
        fieldType: fieldType,
        fieldOptions,
        isUsed: false,
        customColumn: true,
      },
    ];

    await updateDefaults({ ...defaults, machineJobColumns: data }).unwrap();
    setName('');
    setFieldType('string');
    setFieldOptions([]);
    toast.success('Column added successfully!');
  };

  return (
    <div className="w-full">
      <div className="flex gap-3 mb-4">
        <div>
          <label>Enter Field Name</label>{' '}
          <span className="text-xl text-red-500 -mt-1 -ml-1">*</span>
          <Input
            type="text"
            placeholder="Please enter name"
            onChange={(e) => setName(e.target.value)}
            value={name}
            disabled={isUpdateDefaultsLoading}
          />
        </div>
        <div>
          <label>Enter Field Type</label>{' '}
          <span className="text-xl text-red-500 -mt-1 -ml-1">*</span>
          <Select
            value={fieldType}
            menuPlacement="top"
            options={[
              { value: 'string', label: 'String' },
              { value: 'number', label: 'Number' },
              { value: 'Dropdown', label: 'Dropdown' },
              // { value: 'Date', label: 'Date' },
              // { value: 'Check', label: 'Checkbox' },
              // { value: 'table', label: 'Table' },
              // { value: 'Description', label: 'Description' },
              // {
              //   value: 'MultiSelect',
              //   label: 'MultiSelect',
              // },
            ]}
            onChange={(e) => {
              setFieldType(e.target.value);
            }}
          />
        </div>
        {(fieldType === 'Dropdown' || fieldType === 'MultiSelect') && (
          <>
            <div className="flex gap-5 ml-3 items-center  ">
              <label>Enter dropdown options</label>
              <PlusIcon
                onClick={() => setFieldOptions((prev) => [...prev, ''])}
                width={20}
                height={20}
                className="text-white bg-blue-400 rounded-full cursor-pointer"
              />
            </div>
            <div className=" mt-5">
              {fieldOptions?.map((options, index) => (
                <div className="flex gap-2 items-center" key={index}>
                  <Input
                    key={index}
                    onChange={(e) => {
                      const updated = [...fieldOptions];
                      updated[index] = e.target.value;
                      setFieldOptions(updated);
                    }}
                    placeholder="Enter option"
                    value={options}
                    className={`mb-1`}
                  />
                  <TrashIcon
                    className="cursor-pointer text-red-500 hover:fill-red-900 w-6 h-6"
                    onClick={() => {
                      setFieldOptions((prev) =>
                        prev.filter((_, i) => i !== index)
                      );
                    }}
                  />
                </div>
              ))}
            </div>
          </>
        )}

        <Button
          onClick={handleSubmit}
          isLoading={isUpdateDefaultsLoading}
          className={'mt-7'}
        >
          Add
        </Button>
      </div>
      <ol>
        {defaults?.machineJobColumns?.map((val, index) => (
          <React.Fragment key={index}>
            <div className="w-full md:w-[270px] my-1  h-auto flex justify-between">
              <span className="ml-2 text-sm">
                {index + 1}
                {'.'}
              </span>
              <li
                className="text-sm ml-3 w-full underline text-blue-500"
                onClick={() => {
                  setIsEdit(true);
                  setEditIndex(index);
                  setName(val.title);
                  setFieldType(val.fieldType);
                  setFieldOptions(val.fieldOptions || []);
                }}
              >
                {val.title}
              </li>
              <TrashIcon
                className="cursor-pointer text-red-500 hover:fill-red-900 w-6 h-6"
                onClick={() => handleColDelete(index, val.isUsed)}
              />
            </div>
            <hr className="w-[310px]" key={`hr-${index}`} />
          </React.Fragment>
        ))}
      </ol>
    </div>
  );
}
