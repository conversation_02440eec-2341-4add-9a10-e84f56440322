import { Button, Modal } from 'antd';
import { INITIAL_MODAL_FOR } from './bomConstants';
import { useEffect, useState } from 'react';
import MultiSelect from '../global/components/MultiSelect';
import Table from '../global/components/Table';
import Input from '../global/components/Input';
import { toast } from 'react-toastify';

export default function RawMaterialModal({
  modalFor,
  setModalfor,
  allParts,
  bom,
  setBoms,
}) {
  const [rawMaterials, setRawMaterials] = useState([]);

  const { open, rowId } = modalFor;

  useEffect(() => {
    if (bom?.rawMaterials?.length > 0) {
      setRawMaterials(bom?.rawMaterials);
    }
  }, [bom?.rawMaterials]);

  const handleClose = () => {
    setModalfor(INITIAL_MODAL_FOR);
  };

  const handleSubmit = () => {
    if (rawMaterials?.find((i) => i?.units < 1)) {
      toast.error('Raw Materials units cannot be 0');
      return;
    }

    setBoms((prev) =>
      prev?.map((b) => {
        if (b.rowId === rowId) {
          return {
            ...b,
            isUpdated: true,
            rawMaterials,
          };
        }
        return b;
      })
    );
    setModalfor(INITIAL_MODAL_FOR);
  };

  const partsMap = allParts?.reduce((acc, curval) => {
    acc[curval?.value] = { name: curval?.name, uom: curval?.uom || '-' };
    return acc;
  }, {});

  return (
    <Modal
      open={open}
      onClose={handleClose}
      onCancel={handleClose}
      title="Select Raw Materials"
      width={'50%'}
      footer={[
        <Button key={'1'} type="primary" onClick={handleSubmit}>
          Submit
        </Button>,
      ]}
    >
      <label htmlFor="rms">Raw Materials</label>
      <MultiSelect
        value={rawMaterials?.map((v) => v?.item)}
        onChange={(e) =>
          setRawMaterials(
            e?.target?.value?.map((val) => ({
              partType: 'Part',
              item: val?.value,
              units: 1,
            }))
          )
        }
        options={allParts
          .filter((part) => part?.category?.name === 'Raw Materials')
          .map((part) => ({
            label: part.name,
            value: part?.value,
            uom: part.uom,
          }))}
      />

      <Table className={'mt-5'}>
        <Table.Head>
          <Table.Row>
            <Table.Th>Name</Table.Th>
            <Table.Th>Uom</Table.Th>
            <Table.Th>Units</Table.Th>
          </Table.Row>
        </Table.Head>
        {rawMaterials?.map((rm) => (
          <Table.Body key={rm?.item}>
            <Table.Row>
              <Table.Td>{partsMap?.[rm?.item]?.name}</Table.Td>
              <Table.Td>{partsMap?.[rm?.item]?.uom}</Table.Td>
              <Table.Td>
                <Input
                  type="number"
                  value={rm?.units}
                  onChange={(e) =>
                    setRawMaterials((prev) =>
                      prev?.map((it) => {
                        if (it?.item === rm?.item) {
                          return { ...it, units: +e.target.value };
                        }
                        return it;
                      })
                    )
                  }
                />
              </Table.Td>
            </Table.Row>
          </Table.Body>
        ))}
      </Table>
    </Modal>
  );
}
