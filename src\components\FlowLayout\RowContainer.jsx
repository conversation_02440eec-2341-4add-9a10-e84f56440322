import FlowTile from './FlowTile';

const RowContainer = ({
  isMobile,
  isTablet,
  data,
  cuProjects,
  selectedFilter,
  proLenght,
  processGoalViews,
  isReversed,
  deviceDatas,
  isLast,
  reportData,
  setReportData,
  selectedBatch,
  setAllSelectedMachines,
  setOpenSideBar,
  selectedOptionsData,
  setShowErrorSidebar,
  setAddtionalFields,
}) => {
  return (
    <div
      className={`[&:not(:first-child)]:mt-[100px] first:mt-6 grid grid-cols-1 md:grid-cols-1 gap-[8rem] lg:grid-cols-3  place-items-center ${
        isReversed ? 'grid-reverse' : ''
      } `}
    >
      {data.map((item, index) => {
        const isLastInRow = data?.length - 1 === index;

        const isLastItem = isLast && isLastInRow;

        const qcSampleData = selectedOptionsData?.linkedForms.find(
          (el) => el?.processId === item?.mqtt?._id
        );

        return (
          <FlowTile
            isMobile={isMobile}
            isTablet={isTablet}
            key={item._id}
            data={item}
            index={index}
            cuProjects={cuProjects?.filter((pro) => pro.flowId === item._id)}
            nextCuProjects={cuProjects?.filter(
              (pro) => pro.flowId === item.nextFlowId
            )}
            selectedFilter={selectedFilter}
            proLenght={proLenght}
            processGoalViews={processGoalViews}
            deviceDatas={deviceDatas}
            isReversed={isReversed}
            isLastInRow={isLastInRow}
            isLastItem={isLastItem}
            reportData={reportData}
            setReportData={setReportData}
            selectedBatch={selectedBatch}
            setAllSelectedMachines={setAllSelectedMachines}
            setOpenSideBar={setOpenSideBar}
            qcSampleData={qcSampleData}
            setShowErrorSidebar={setShowErrorSidebar}
            setAddtionalFields={setAddtionalFields}
          />
        );
      })}
    </div>
  );
};

export default RowContainer;
