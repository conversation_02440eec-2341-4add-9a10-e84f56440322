import { useContext, useEffect, useState } from 'react';

import { useMediaQuery } from 'react-responsive';
import highcount from '../../../assets/images/highCount.png';
import highError from '../../../assets/images/highError.png';
import highSpeed from '../../../assets/images/highSpeed.png';
import highUptime from '../../../assets/images/highUptime.png';
import lowUptime from '../../../assets/images/lowUptime.png';
import LowestCountimg from '../../../assets/images/lowestCount.png';
import lowestError from '../../../assets/images/lowestError.png';
import lowestSpeed from '../../../assets/images/lowestSpeed.png';
import lowestdowntime from '../../../assets/images/lowestdowntime.png';
import lowestuptime from '../../../assets/images/lowestuptime.png';
import { mobileWidth } from '../../../helperFunction';
import { MqttContext } from '../../../mqtt/DashboardMqttContext';
import { useLazyGetCuProjectsByMachineStatusQuery } from '../../../slices/CuProjectAPiSlice';
import { useGetKpiDataForSatelliteMutation } from '../../../slices/deviceDataApiSlice';
import { useGetALlDeviceKipsQuery } from '../../../slices/deviceKpiApiSlice';
import { useGetAllGoalsTableQuery } from '../../../slices/goalsTableapiSlice';
import { useLazyGetAllMachinesForSatelliteQuery } from '../../../slices/machineApiSlice';
import { useGetAllMachinesStatsQuery } from '../../../slices/machineStatsApiSlice';
import DropDownButton from '../../DropDownButton/DropDownButton';
import Button from '../../global/components/Button';
import Header from '../../global/components/Header';
import RightSidebar from '../../global/components/RightSidebar';
import Table from '../../global/components/Table';
import TablePopup from '../../global/components/TablePopup';
import MachineCard from './MachineCard';

const ActiveMachines = () => {
  const allFilters = [
    {
      name: 'All',
      value: 'all',
    },
    {
      name: 'Active',
      value: 'active',
    },
    {
      name: 'Inactive',
      value: 'inactive',
    },
    {
      name: 'Paused',
      value: 'pause',
    },
  ];

  const [LowestCountMachineData, setLowestCountMachineData] = useState([]);
  const [HighestCountMachineData, setHighestCountMachineData] = useState([]);
  const [LowestUptimeMachineData, setLowestUptimeMachineData] = useState([]);
  const [HighestUptimeMachineData, setHighestUptimeMachineData] = useState([]);
  const [LowestDowntimeMachineData, setLowestDowntimeMachineData] = useState(
    []
  );
  const [HighestDowntimeMachineData, setHighestDowntimeMachineData] = useState(
    []
  );
  const [LowestMachineErrorsData, setLowestMachineErrorsData] = useState([]);
  const [HighestMachineErrorsData, setHighestMachineErrorsData] = useState([]);
  const [LowestSpeedData, setLowestSpeedData] = useState([]);
  const [HighestSpeedData, setHighestSpeedData] = useState([]);
  const [machines, setMachines] = useState([]);
  const [cuProjects, setCuProjects] = useState([]);
  const [selectedFilter] = useState(allFilters[0].value);
  const [filterMachines, setFilterMachines] = useState([]);
  const [HighestCount, setHighestCount] = useState('');
  const [LowestCount, setLowestCount] = useState('');
  const [HighestUptime, setHighestUptime] = useState('');
  const [LowestUptime, setLowestUptime] = useState('');
  const [HighestDownTime, setHighestDownTime] = useState('');
  const [LowestDownTime, setLowestDownTime] = useState('');
  const [HighestMachineError, setHighestMachineError] = useState('');
  const [LowestMachineError, setLowestMachineError] = useState('');
  const [HighestSpeed, setHighestSpeed] = useState('');
  const [LowestSpeed, setLowestSpeed] = useState('');
  const [allStatsData, setAllStatsData] = useState([]);
  const [openSideBar, setOpenSidebar] = useState(false);
  const [type, setType] = useState('');
  const [isTableOpen, setIsTableOPen] = useState(false);
  const isMobile = useMediaQuery({ query: mobileWidth });
  const { data: gtData = {} } = useGetAllGoalsTableQuery();
  const { goalsTable: allGoalsTable = [] } = gtData;

  const [getCuProjectsByMachineStatus] =
    useLazyGetCuProjectsByMachineStatusQuery();

  const { data: kpiData = {} } = useGetALlDeviceKipsQuery();
  const { deviceKpis = [] } = kpiData;

  const [getAllMachinesForSatellite] = useLazyGetAllMachinesForSatelliteQuery();
  const { data: machinestats } = useGetAllMachinesStatsQuery();

  const [getKpiDataForSatellite, { data: divData = {} }] =
    useGetKpiDataForSatelliteMutation();
  const { deviceDatas = [] } = divData;

  const { values } = useContext(MqttContext);

  useEffect(() => {
    (async () => {
      setAllStatsData(machinestats?.stats);
    })();
  }, [machinestats]);

  useEffect(() => {
    const getHighestAndLowestData = () => {
      if (allStatsData) {
        const res = allStatsData.slice().sort((a, b) => a.count - b.count);
        const decRes = allStatsData.slice().sort((a, b) => b.count - a.count);
        const highestCount = res[res.length - 1];
        const lowestCount = res[0];
        setLowestCountMachineData(res);
        setHighestCountMachineData(decRes);

        const res2 = allStatsData.slice().sort((a, b) => a.uptime - b.uptime);
        const decRes2 = allStatsData
          .slice()
          .sort((a, b) => b.uptime - a.uptime);
        const highestUptime = res2[res2.length - 1];
        const lowestUptime = res2[0];
        setLowestUptimeMachineData(res2);
        setHighestUptimeMachineData(decRes2);

        const res3 = allStatsData
          .slice()
          .sort((a, b) => a.downtime - b.downtime);
        const decRes3 = allStatsData
          .slice()
          .sort((a, b) => b.downtime - a.downtime);
        const highestDownTime = res3[res3.length - 1];
        const lowestDownTime = res3[0];
        setLowestDowntimeMachineData(res3);
        setHighestDowntimeMachineData(decRes3);

        const res4 = allStatsData
          .slice()
          .sort((a, b) => a.machineErrors.length - b.machineErrors.length);
        const decRes4 = allStatsData
          .slice()
          .sort((a, b) => b.machineErrors.length - a.machineErrors.length);
        const higestMachineError = res4[res4.length - 1];
        const lowestMachineError = res4[0];
        setLowestMachineErrorsData(res4);
        setHighestMachineErrorsData(decRes4);

        const res5 = allStatsData.slice().sort((a, b) => a.speed - b.speed);
        const decRes5 = allStatsData.slice().sort((a, b) => b.speed - a.speed);
        const higestSpeed = res5[res5.length - 1];
        const lowestSpeed = res5[0];
        setLowestSpeedData(res5);
        setHighestSpeedData(decRes5);

        setHighestCount(highestCount);
        setLowestCount(lowestCount);
        setHighestUptime(highestUptime);
        setLowestUptime(lowestUptime);
        setHighestDownTime(highestDownTime);
        setLowestDownTime(lowestDownTime);
        setHighestMachineError(higestMachineError);
        setLowestMachineError(lowestMachineError);
        setHighestSpeed(higestSpeed);
        setLowestSpeed(lowestSpeed);
      }
    };
    getHighestAndLowestData();
  }, [allStatsData]);

  useEffect(() => {
    (async () => {
      const res = await getAllMachinesForSatellite({}, false).unwrap();
      setMachines(res.machines);
      setFilterMachines(res.machines);

      const res2 = await getCuProjectsByMachineStatus().unwrap();
      setCuProjects(res2);

      const cuProjectIds = res2?.map((item) => item._id);
      getKpiDataForSatellite({
        data: {
          cuProjects: cuProjectIds,
        },
      }).unwrap();
    })();
  }, [
    getCuProjectsByMachineStatus,
    getAllMachinesForSatellite,
    getKpiDataForSatellite,
  ]);

  useEffect(() => {
    if (selectedFilter === 'all') {
      setFilterMachines(machines);
    } else if (selectedFilter === 'active') {
      setFilterMachines(() =>
        machines.filter((machine) => {
          const cuProject = cuProjects.find((el) =>
            el.machineAndOperator.find(
              (mao) =>
                (mao.machine === machine._id &&
                  mao.status === 'complete' &&
                  machine.status === 'inactive') ||
                mao.status === machine.status
            )
          );
          return machine.status === selectedFilter && cuProject;
        })
      );
    } else {
      setFilterMachines(() =>
        machines.filter((machine) => machine.status === selectedFilter)
      );
    }
  }, [selectedFilter, cuProjects, machines]);

  const renderMachineTableData = () => {
    switch (type) {
      case 'Highest Count':
        return (
          <>
            {HighestCountMachineData.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{item?.count}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );
      case 'Lowest Count':
        return (
          <>
            {LowestCountMachineData?.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{item?.count}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );

      case 'Lowest Uptime':
        return (
          <>
            {LowestUptimeMachineData?.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{getformattedTime(item?.uptime)}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );

      case 'Highest Uptime':
        return (
          <>
            {HighestUptimeMachineData?.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{getformattedTime(item?.uptime)}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );

      case 'Highest Downtime':
        return (
          <>
            {HighestDowntimeMachineData?.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{getformattedTime(item?.downtime)}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );

      case 'Lowest Downtime':
        return (
          <>
            {LowestDowntimeMachineData?.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{getformattedTime(item?.downtime)}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );

      case 'Lowest Machine Error':
        return (
          <>
            {LowestMachineErrorsData?.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{item?.machineErrors?.length}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );

      case 'Highest Machine Error':
        return (
          <>
            {HighestMachineErrorsData?.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{item?.machineErrors?.length}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );

      case 'Highest Speed':
        return (
          <>
            {HighestSpeedData.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{item?.speed}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );

      case 'Lowest Speed':
        return (
          <>
            {LowestSpeedData?.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td>{item?.machine?.machineName}</Table.Td>
                  <Table.Td>{item?.machine?.machineId}</Table.Td>
                  <Table.Td>{item?.speed}</Table.Td>
                </Table.Row>
              );
            })}
          </>
        );
    }
  };

  const SideBarContent = () => {
    return (
      <div className="mt-8 w-full !text-[12px]">
        <div className="text-center font-bold text-lg">Machine Stats</div>
        <div
          className={`grid !xs:grid-cols-2 sm:grid-cols-2  md:grid-cols-2 gap-3 }`}
        >
          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Highest Count');
              setIsTableOPen(true);
            }}
          >
            <div className="mt-4  border rounded-lg p-4  shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px]    text-gray-500 ">Highest Count</h2>

              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[20px]  mt-1 font-bold ">
                    {HighestCount?.count}
                  </p>
                  <img src={highcount} className="w-[25px] h-[25px]  ml-7" />
                </div>
              </div>
              <p className="text-sm  text-gray-400 mt-2 ">
                {HighestCount?.machine?.machineName || '-'}(
                {HighestCount?.machine?.machineId || '-'})
              </p>
            </div>
          </div>

          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Lowest Count');
              setIsTableOPen(true);
            }}
          >
            <div className="mt-4  border rounded-lg p-4 shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px]   text-gray-500 ">Lowest Count</h2>
              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[25px]  mt-1 font-bold ">
                    {LowestCount?.count}
                  </p>
                  <img
                    src={LowestCountimg}
                    className="w-[25px] h-[25px]  ml-7"
                  />
                </div>
              </div>
              <p className="text-sm  text-gray-400 mt-2 ">
                {LowestCount?.machine?.machineName}(
                {LowestCount?.machine?.machineId})
              </p>
            </div>
          </div>

          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Highest Uptime');
              setIsTableOPen(true);
            }}
          >
            <div className="mt-4  border rounded-lg p-4  shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px]   text-gray-500 ">Highest Uptime</h2>
              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[18px] mt-1 font-bold ">
                    {getformattedTime(HighestUptime?.uptime)}
                  </p>
                  <img src={highUptime} className="w-[25px] h-[25px]  ml-7" />
                </div>
              </div>
              <p className="text-sm  text-gray-400 mt-2 ">
                {HighestUptime?.machine?.machineName}(
                {HighestUptime?.machine?.machineId})
              </p>
            </div>
          </div>

          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Lowest Uptime');
              setIsTableOPen(true);
            }}
          >
            <div className=" mt-4  border rounded-lg p-4  shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px]   text-gray-500 ">Lowest Uptime</h2>
              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[18px]  mt-1 font-bold ">
                    {getformattedTime(LowestUptime?.uptime)}
                  </p>
                  <img src={lowestuptime} className="w-[25px] h-[25px]  ml-7" />
                </div>
              </div>
              <p className="text-sm  text-gray-400 mt-2 ">
                {LowestUptime?.machine?.machineName}(
                {LowestUptime?.machine?.machineId})
              </p>
            </div>
          </div>

          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Highest Downtime');
              setIsTableOPen(true);
            }}
          >
            <div className=" mt-4  border rounded-lg p-4 shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px]   text-gray-500 ">Highest Downtime</h2>
              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[18px]  mt-1 font-bold ">
                    {getformattedTime(HighestDownTime?.downtime)}
                  </p>
                  <img src={lowUptime} className="w-[25px] h-[25px]  ml-7" />
                </div>
              </div>
              <p className="text-sm  text-gray-400 mt-2 ">
                {HighestDownTime?.machine?.machineName}(
                {HighestDownTime?.machine?.machineId})
              </p>
            </div>
          </div>

          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Lowest Downtime');
              setIsTableOPen(true);
            }}
          >
            <div className=" mt-4  border rounded-lg p-4  shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px]   text-gray-500 ">Lowest Downtime</h2>
              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[18px]  mt-1 font-bold ">
                    {getformattedTime(LowestDownTime?.downtime)}
                  </p>
                  <img
                    src={lowestdowntime}
                    className="w-[25px] h-[25px]  ml-7"
                  />
                </div>
              </div>
              <p className="text-sm  text-gray-400 mt-2 ">
                {LowestDownTime?.machine?.machineName}(
                {LowestDownTime?.machine?.machineId})
              </p>
            </div>
          </div>

          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Highest Machine Error');
              setIsTableOPen(true);
            }}
          >
            <div className="mt-4  border rounded-lg p-4  shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px]   text-gray-500 ">
                Highest Machine Error
              </h2>
              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[25px]  mt-1 font-bold ">
                    {HighestMachineError?.machineErrors?.length}
                  </p>
                  <img src={highError} className="w-[25px] h-[25px]  ml-7" />
                </div>
              </div>
              <p className="text-sm  text-gray-400 mt-2 ">
                {HighestMachineError?.machine?.machineName}(
                {HighestMachineError?.machine?.machineId})
              </p>
            </div>
          </div>

          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Lowest Machine Error');
              setIsTableOPen(true);
            }}
          >
            <div className="mt-4  border rounded-lg p-4  shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px]   text-gray-500 ">
                Lowest Machine Error
              </h2>
              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[25px]  mt-1 font-bold ">
                    {LowestMachineError?.machineErrors?.length}
                  </p>
                  <img src={lowestError} className="w-[25px] h-[25px]  ml-7" />
                </div>
              </div>
              <p className="text-sm  text-gray-400 mt-2 ">
                {LowestMachineError?.machine?.machineName}(
                {LowestMachineError?.machine?.machineId})
              </p>
            </div>
          </div>

          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Highest Speed');
              setIsTableOPen(true);
            }}
          >
            <div className=" mt-4  border rounded-lg p-4  shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px] text-gray-500">Highest Speed</h2>
              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[25px] mt-1 font-bold ">
                    {HighestSpeed?.speed}
                  </p>
                  <img src={highSpeed} className="w-[25px] h-[25px]  ml-7" />
                </div>
              </div>
              <p className="text-sm text-gray-400  mt-2 ">
                {HighestSpeed?.machine?.machineName}(
                {HighestSpeed?.machine?.machineId})
              </p>
            </div>
          </div>

          <div
            className="flex-col cursor-pointer"
            onClick={() => {
              setType('Lowest Speed');
              setIsTableOPen(true);
            }}
          >
            <div className="mt-4  border rounded-lg p-4  shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105">
              <h2 className="text-[15px]  text-gray-500">Lowest Speed</h2>
              <div className="text-sm font-gray-100 mt-1">
                <div className="flex justify-between">
                  <p className="text-[25px]  mt-1 font-bold ">
                    {LowestSpeed?.speed}
                  </p>
                  <img src={lowestSpeed} className="w-[25px] h-[25px]  ml-7" />
                </div>
              </div>
              <p className="text-sm  text-gray-400 mt-2 ">
                {LowestSpeed?.machine?.machineName}(
                {LowestSpeed?.machine?.machineId})
              </p>
            </div>
          </div>
        </div>
        {isTableOpen && (
          <div className={` mt-10`}>
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Th>Machine Name</Table.Th>
                  <Table.Th>Machine ID</Table.Th>
                  <Table.Th>
                    {type === 'Lowest Machine Error' ||
                    type === 'Highest Machine Error'
                      ? type.split(' ')[2]
                      : type.split(' ')[1]}
                  </Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>{renderMachineTableData()}</Table.Body>
            </Table>
          </div>
        )}

        {/* <div className={isShowOthers ? 'block' : 'hidden'}>
                    <div className="text-center text-xl font-semibold w-full mt-6">
                      Other Machine Stats
                    </div>
                    <div className="flex gap-6 mt-6">
                      {othersMachineStats
                        .filter((item) => {
                          return (
                            item?.machine?.machineId !==
                              HighestCount?.machine.machineId &&
                            item?.machine?.machineId !==
                              HighestSpeed?.machine.machineId &&
                            item?.machine?.machineId !==
                              LowestMachineError?.machine.machineId &&
                            item?.machine?.machineId !==
                              HighestMachineError?.machine.machineId &&
                            item?.machine?.machineId !==
                              LowestDownTime?.machine.machineId &&
                            item?.machine?.machineId !==
                              HighestDownTime?.machine.machineId &&
                            item?.machine?.machineId !==
                              LowestCount?.machine.machineId &&
                            item?.machine?.machineId !==
                              LowestUptime?.machine.machineId &&
                            item?.machine?.machineId !==
                              HighestUptime?.machine.machineId &&
                            item?.machine?.machineId !==
                              LowestSpeed?.machine.machineId
                          );
                        })
                        .map((item, index) => {
                          return (
                            <div
                              key={index}
                              className="w-[23rem] mt-4  border rounded-lg p-4 bg-blue-50 shadow-md hover:shadow-lg transform transition duration-300 ease-in-out hover:scale-105"
                            >
                              <p className="text-md font-semibold text-blue-700 ">
                                {item?.machine.machineName}(
                                {item?.machine.machineId})
                              </p>
  
                              <div className="text-sm font-gray-100 mt-6">
                                <p>Count:&nbsp; &nbsp; {item?.count}</p>
                                <p>
                                  Uptime:&nbsp; &nbsp;{' '}
                                  {getformattedTime(item?.uptime)}
                                  &nbsp;{' '}
                                </p>
                                <p>
                                  Down time: &nbsp; &nbsp;
                                  {getformattedTime(item?.downtime)}&nbsp;
                                </p>
                                <p>Machine speed: &nbsp; &nbsp;{item?.speed}</p>
                                <p>
                                  Machine error: &nbsp; &nbsp;
                                  {item?.machineErrors.length}
                                </p>
                                <p>
                                  Machine started: &nbsp; &nbsp;
                                  {getLocalDateTime(item?.machineStarted)}
                                </p>
                                <p>
                                  Machine stopped: &nbsp; &nbsp;
                                  {getLocalDateTime(item?.machineStopped)}
                                </p>
                              </div>
                            </div>
                          );
                        })}
                    </div>
                  </div> */}
      </div>
    );
  };

  const getformattedTime = (totalminutes) => {
    const hour = Math.floor(totalminutes / 60);
    const minutes = totalminutes % 60;
    return hour + ' ' + 'hr' + ' ' + minutes + ' ' + 'min';
  };

  return (
    <>
      <div>
        {openSideBar && !isMobile && (
          <RightSidebar
            openSideBar={openSideBar}
            setOpenSideBar={setOpenSidebar}
            setIsTableOPen={setIsTableOPen}
            scale={736}
          >
            <>{SideBarContent()}</>
          </RightSidebar>
        )}
        {isMobile && openSideBar && (
          <TablePopup
            isEdit={false}
            isDownload={false}
            onBack={() => setOpenSidebar(false)}
          >
            {SideBarContent()}
          </TablePopup>
        )}
        <div className="flex flex-col md:flex-row justify-between items-center mb-5">
          <div className="flex flex-col !min-w-[20rem]">
            <div className="flex gap-[5px] items-center">
              <Header
                title="Satellite View"
                description=""
                infoTitle="Welcome to Satellite View Page"
                infoDesc="Your panoramic view of the shop floor."
                paras={[
                  'Here, you can easily grasp the status of each machine and worker, gauge machine performance, track assignments,production rates, and worker allocation.',
                  'Assess machine and worker availability on the shop floor for project planning',
                  'Get a panoramic shop floor snapshot, tracking machine and worker status, performance, and project details for precise planning.',
                ]}
              />
            </div>
          </div>
          <div className="flex gap-3">
            <div>
              <Button
                className={`{Show ? 'hidden' : 'block'} hover:bg-blue-500 `}
                onClick={() => setOpenSidebar(!openSideBar)}
              >
                Get Insights
              </Button>
            </div>
            <DropDownButton allFilters={allFilters}></DropDownButton>
          </div>
        </div>

        <div className="flex flex-wrap mt-8 gap-4">
          {filterMachines?.map((e) => (
            <MachineCard
              key={e._id}
              machine={e}
              values={values}
              cuProjects={cuProjects}
              deviceDatas={deviceDatas}
              deviceKpis={deviceKpis?.filter(
                (kpi) => kpi?.machine?._id === e._id
              )}
              goalsTable={allGoalsTable?.filter(
                (goalTable) => goalTable?.mqtt === e?.mqtt
              )}
            />
          ))}
        </div>
      </div>
    </>
  );
};

export default ActiveMachines;
