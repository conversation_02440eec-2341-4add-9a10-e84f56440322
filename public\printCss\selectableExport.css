@tailwind base;
@tailwind components;
@tailwind utilities;

#print-only,
#print-only * {
  visibility: visible;
  padding: initial;
  margin: initial;
  box-shadow: initial;
  border: initial;
  border-radius: initial;
  font-weight: initial;
  font-size: 10px;
  color: black;
}

#print-only {
    display: block;
    border: 1px solid black;
  }

  #print-only {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: initial;
  }

  #print-only table {
    padding-top: 30px;
    margin-top: 30px;
    border: black 1px solid;
  }

  #print-only thead tr {
    border: black 1px solid;
    background: #878787;
    background-color: #878787;
  }
  #print-only thead th {
    background: #E6E6E6 !important;
    print-color-adjust: exact;
    border-left: black 1px solid;
  }

  #print-only thead th tr th:nth-child(1) {
    background: #E6E6E6 !important;
    print-color-adjust: exact;
    border: black 0px solid !important;
    border-right: 1px black solid;
  }

  
  #print-only th,
  #print-dispatch th {
    text-align: center;
  }

  #print-only tbody,
  #print-dispatch tbody {
    border: black 1px solid;
  }

  #print-only tbody,
  #print-dispatch tbody {
    border: black 1px solid;
  }

  #print-only td,
  #print-dispatch td {
    border-left: black 1px solid;
    text-align: center;
  }

  #print-dispatch td:last-child {
    border-right: black 1px solid;
  }

  .print-only tr:nth-child(even) {
    background: #878787;
  }

  #print-only td,
  #print-only th,
  #print-dispatch td,
  #print-dispatch th {
    padding-inline: 2px;
    padding-block: 10px;
    min-width: unset;
    margin: auto;
  }

  
  #print-only th {
    font-weight: bold;
  }

  #print-only tr#border {
    border: 1px black solid;
  }
