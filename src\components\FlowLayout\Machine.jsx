import { useContext, useEffect, useState } from 'react';
import {
  CircularProgressbarWithChildren,
  buildStyles,
} from 'react-circular-progressbar';
import { ReactComponent as DownVector } from '../../assets/svgs/DownVector.svg';
import { ReactComponent as UpVector } from '../../assets/svgs/UpVector.svg';
import {
  calculateValueForViews,
  convertToHrsAndMins,
  getMinutesPassed,
  handlePercentDeicmalValue,
} from '../../helperFunction';
import { MqttContext } from '../../mqtt/DashboardMqttContext';
import { Store } from '../../store/Store';
import ProgressBar from '../global/components/ProgressBar';
import Directions from './Directions';

export default function Machine({
  isMobile,
  isTablet,
  data,
  cuProjects,
  nextCuProjects,
  processGoalViews,
  deviceDatas,
  isReversed,
  isLastInRow,
  isLastItem,
}) {
  const [realtimeBatchSize, setRealtimeBatchSize] = useState(0);
  const [newValues, setNewValues] = useState({});
  const [haltData, setHaltData] = useState({ hrs: 0, mins: 0, status: false });

  const { values } = useContext(MqttContext);
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const cuProject = cuProjects.findLast((i) => i);

  const nextFlowCuPro = nextCuProjects?.findLast((i) => i);

  const isComplete = cuProject?.status === 'complete';

  const batchSizeParam = processGoalViews
    ?.find((item) => item?.project?.projectId === cuProject?.mqtt?._id)
    ?.parameters?.find((item) => item.name === 'Batch Size');

  const machineList = cuProject?.machineAndOperator?.map(
    (mao) => mao?.machine?.machineId
  );

  const batchData = cuProject?.isMultiProcess
    ? cuProject?.subProcessData
    : cuProject?.batchInfo;

  useEffect(() => {
    if (cuProject?.stopTime && nextFlowCuPro?.startTime) {
      const cuStop = new Date(cuProject?.stopTime);
      const nextCuStart = new Date(nextFlowCuPro?.startTime);

      if (nextCuStart > cuStop) {
        const totalMinutesPassed = getMinutesPassed(
          nextCuStart,
          cuStop,
          defaultParam,
          true
        );

        const [hrs, mins] = convertToHrsAndMins(totalMinutesPassed / 60);
        setHaltData({ hrs, mins, status: true });
      } else {
        setHaltData({ hrs: 0, mins: 0, status: false });
      }
    } else {
      setHaltData({ hrs: 0, mins: 0, status: false });
    }
  }, [cuProject, nextFlowCuPro, defaultParam]);

  useEffect(() => {
    let manualCount = 0;
    let val = 0;
    const manualMachines = cuProject?.machineAndOperator?.filter(
      (mao) => mao?.machine?.isManual
    );

    manualMachines?.forEach((mac) => {
      manualCount =
        manualCount +
        (mac?.manualStopData || mac?.manualPauseData?.findLast((i) => i) || 0);
    });

    if (batchSizeParam && machineList && newValues) {
      val = calculateValueForViews(
        batchSizeParam?.formula,
        newValues,
        machineList
      );
    }
    setRealtimeBatchSize(val + manualCount || 0);
  }, [batchSizeParam, machineList, newValues, cuProject?.machineAndOperator]);

  useEffect(() => {
    if (cuProject && values) {
      cuProject?.machineAndOperator?.forEach((mao) => {
        const isActive = mao?.status === 'active';

        const machine = mao.machine;

        machine?.devices?.forEach((device) => {
          device?.assignedDevice?.forEach((item) => {
            if (isActive) {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]: values?.[item?.deviceId] || {},
              }));
            } else {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]:
                  cuProject?.lastValues?.[machine?.machineId]?.[
                    item?.deviceId
                  ] || {},
              }));
            }
          });
        });
      });
    }
  }, [cuProject, values]);

  const progressPercent = (realtimeBatchSize / batchData?.['Batch Size']) * 100;

  let progress =
    handlePercentDeicmalValue(progressPercent > 100 ? 100 : progressPercent) ||
    0;

  const machineAndOperator = cuProject?.machineAndOperator;

  return (
    <>
      <div
        className={`relative rounded-xl w-[250px] h-[330px] shadow-lg bg-white ${
          !cuProject ? 'flex flex-col gap-2 justify-center items-center' : ''
        }`}
      >
        {cuProject ? (
          <div className="w-full h-full flex flex-col">
            <div className="w-full flex justify-between items-center px-5 py-1 border-b">
              <section className="w-[80%]">
                <p className="mt-1 text-[12px] font-semibold">
                  {data?.processName}
                </p>
                <ProgressBar
                  progress={progress}
                  max={100}
                  progressColor={'#77DD77'}
                />
              </section>
              <section className="flex w-[15%] items-end">
                <p className="text-xl">{progress}</p>
                <span className="text-sm mb-0.5">%</span>
              </section>
            </div>

            <div className="overflow-y-scroll no-scrollbar h-full">
              {machineAndOperator?.map((mao) => {
                const operator = mao?.operator?.findLast((i) => i);

                const isManual = mao?.machine?.isManual;

                let count = 0;
                if (isManual) {
                  count =
                    mao?.manualStopData ||
                    mao?.manualPauseData?.findLast((i) => i) ||
                    0;
                } else if (batchSizeParam?.formula && newValues) {
                  count = calculateValueForViews(
                    batchSizeParam?.formula,
                    newValues,
                    [mao?.machine?.machineId]
                  );
                }

                const isComplete = mao?.status === 'complete';

                const timePassed = getMinutesPassed(
                  isComplete ? new Date(mao?.stopTime) : new Date(),
                  new Date(mao?.startTime),
                  defaultParam,
                  isComplete
                );

                let macHpsst = timePassed / 60;

                const macItemsPerHour = Math.floor(count / macHpsst);

                const idleTimesData =
                  deviceDatas?.filter(
                    (item) =>
                      item?.cuProject === cuProject?._id &&
                      item?.machine === mao?.machine?._id &&
                      item.type === 'kpi' &&
                      hasOwnProperty
                  ) || [];

                const idleTime =
                  idleTimesData?.reduce(
                    (acc, curVal) => acc + +curVal?.data?.IDLE,
                    0
                  ) / 60;

                const tempUptime = macHpsst - idleTime;

                const upTime = convertToHrsAndMins(tempUptime);
                const downtime = convertToHrsAndMins(idleTime);

                return (
                  <div
                    key={mao._id}
                    className="w-full px-5 text-[#36434D] text-[11px] border-b"
                  >
                    <section className="flex justify-between mt-1 font-semibold">
                      <p>{`${mao?.machine?.machineName} (${mao?.machine?.machineId})`}</p>
                      <p>{operator?.user?.name}</p>
                    </section>

                    <section className="grid grid-cols-2 gap-y-2 mt-1 place-items-center">
                      <section className="w-1/2 flex justify-center">
                        <CircularProgressbarWithChildren
                          value={count}
                          maxValue={+batchData?.['Batch Size']}
                          strokeWidth={8}
                          styles={buildStyles({
                            rotation: 0.25,
                            strokeLinecap: 'butt',
                            pathTransitionDuration: 0.5,
                            pathColor: `#71A5DE`,
                            trailColor: '#F3F2F2',
                          })}
                        >
                          <section className="flex flex-col justify-center items-center">
                            <p className="text-md text-blue-primary">{count}</p>
                            <p className="text-[8px]">Count</p>
                          </section>
                        </CircularProgressbarWithChildren>
                      </section>
                      <section className="w-1/2 flex justify-center">
                        <CircularProgressbarWithChildren
                          value={
                            +batchData?.itemsPerHour
                              ? isFinite(macItemsPerHour) &&
                                !isNaN(macItemsPerHour)
                                ? macItemsPerHour
                                : 0
                              : 0
                          }
                          maxValue={
                            +batchData?.itemsPerHour
                              ? +batchData?.itemsPerHour
                              : 1
                          }
                          strokeWidth={8}
                          styles={buildStyles({
                            rotation: 0.25,
                            strokeLinecap: 'butt',
                            pathTransitionDuration: 0.5,
                            pathColor: `#71A5DE`,
                            trailColor: '#F3F2F2',
                          })}
                        >
                          <p className="text-md text-blue-primary">
                            {isFinite(macItemsPerHour) &&
                            !isNaN(macItemsPerHour)
                              ? macItemsPerHour
                              : 0}
                          </p>
                          <p className="text-[8px]">Speed</p>
                        </CircularProgressbarWithChildren>
                      </section>
                      {/* </section>
                    <section className="grid grid-cols-3 gap-y-2 mt-1"> */}
                      <section className="w-full flex items-center gap-x-1 justify-center">
                        <UpVector />
                        <section className="flex flex-col">
                          <p className="font-semibold">Uptime</p>
                          <p className="text-[#77DD77]">{`${upTime?.[0]}h ${upTime?.[1]}m`}</p>
                        </section>
                      </section>
                      {/* <section className="w-full flex items-center gap-x-1 justify-center">
                        <section className="flex flex-col">
                          <p className="font-semibold">OEE</p>
                          <p className="text-[#77DD77]">{`${getRandomNumber(
                            100
                          )}%`}</p>
                        </section>
                      </section> */}
                      <section className="w-full flex items-center gap-x-1 justify-center">
                        <DownVector />
                        <section className="flex flex-col">
                          <p className="font-semibold">Downtime</p>
                          <p className="text-[#ED4040]">{`${downtime?.[0]}h ${downtime?.[1]}m`}</p>
                        </section>
                      </section>
                    </section>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <>
            <p>No project started</p>
            <p>{data?.processName}</p>
          </>
        )}
        <div className="lable-container">
          {!isLastItem && (
            <Directions
              isComplete={isComplete}
              scrap={batchData?.['Batch Size'] - realtimeBatchSize}
              isReversed={isReversed}
              isLastInRow={isLastInRow}
              haltData={haltData}
              isMobile={isMobile}
              isTablet={isTablet}
            />
          )}
        </div>
      </div>
    </>
  );
}
