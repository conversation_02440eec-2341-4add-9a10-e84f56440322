import { useState } from 'react';
import { PAGINATION_LIMIT } from '../../utils/Constant';

import { Button, Table, Tag } from 'antd';
import Header from '../../components/global/components/Header';
import Pagination from '../../components/global/components/Pagination';
import { useGetEmployeesForHRMSQuery } from '../../slices/userApiSlice';
import HrmsUserSidebar from './UserManagement/HrmsUserSidebar';
import UserCreationModal from './UserManagement/UserCreationModal';
import UserPayrollModal from './global/UserPayrollModal';

const UserManagement = () => {
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const [type, setType] = useState('desc'); //eslint-disable-line
  const [field, setField] = useState('createdAt'); //eslint-disable-line
  const [SelectedHeading, setSelectedHeading] = useState(''); //eslint-disable-line
  const [SelectedHeadingValue, setSelectedHeadingValue] = useState(''); //eslint-disable-line

  const [openCreationModal, setOpenCreationModal] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editData, setEditData] = useState({});

  const [openPayrollModal, setOpenPayrollModal] = useState(false);
  const [payrollInfo, setPayrollInfo] = useState('');

  const [openSidebar, setOpenSidebar] = useState(false);

  const {
    data: employees = {},
    isLoading: isLoadingQuery,
    // isFetching: isFetchingQuery,
    // refetch: refetchEmployees,
  } = useGetEmployeesForHRMSQuery(
    {
      page,
      limit,
      type,
      field,
      field_name: SelectedHeading,
      field_value: SelectedHeadingValue,
    },
    { skip: !page || !limit }
  );

  const columns = [
    {
      title: 'Name',
      key: 'name',
      render: (_, record) => (
        <p
          className="text-blue-500 underline cursor-pointer hover:text-blue-400"
          onClick={() => {
            setOpenSidebar(true);
            setEditData(record?._id);
          }}
        >
          {record?.name}
        </p>
      ),
    },
    {
      title: 'Gender',
      key: 'gender',
      render: (_, record) => <p>{record?.gender}</p>,
    },
    {
      title: 'Email',
      key: 'email',
      render: (_, record) => <Tag color="blue">{record?.email}</Tag>,
    },
    {
      title: 'Contact',
      key: 'contactNumber',
      render: (_, record) => <p>{record?.contactNumber}</p>,
    },
    {
      title: 'Fixed Salary',
      key: 'fixedSalary',
      render: (_, record) => <Tag color="green">₹ {record?.fixedSalary}</Tag>,
    },
    {
      title: 'Work Hours (per Day)',
      key: 'workingHours',
      render: (_, record) => <p>{record?.workingHours}</p>,
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <div className="flex items-center gap-2">
          <Button
            onClick={() => {
              setIsEdit(true);
              setEditData(record);
              setOpenCreationModal(true);
            }}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            Edit
          </Button>
          <Button
            className="bg-purple-500 hover:bg-purple-600 text-white"
            onClick={() => {
              setOpenPayrollModal(true);
              let temp = {
                bonusData: record?.bonusData,
                deductionData: record?.deductionData,
                advanceData: record?.advanceData,
                adHocData: record?.adHocData,
                reimbursements: record?.reimbursements,
                userId: record?._id,
              };
              setPayrollInfo(temp);
            }}
          >
            Payroll
          </Button>
        </div>
      ),
    },
  ];

  return (
    <>
      {openSidebar && (
        <HrmsUserSidebar
          openSideBar={openSidebar}
          setOpenSideBar={setOpenSidebar}
          recordId={editData}
        />
      )}
      <UserCreationModal
        openModal={openCreationModal}
        setOpenModal={setOpenCreationModal}
        isEdit={isEdit}
        editData={editData}
        setIsEdit={setIsEdit}
      />
      <UserPayrollModal
        openModal={openPayrollModal}
        setOpenModal={setOpenPayrollModal}
        payrollInfo={payrollInfo}
      />
      <div className="flex gap-[5px] w-full items-center justify-between">
        <Header
          title="User Management"
          description="Manage User Profiles for HR Purposes"
          infoTitle="Welcome to the Downtime Page"
          infoDesc="Get a comprehensive overview of machine events and performance on our Analytics Downtime Page. The Analytics Downtime Page provides a comprehensive overview of
                            machine events, offering insights into start and stop times,
                            pauses, errors, downtime, uptime, idle periods, and cycle times.  It empowers users with valuable data for improved
                            decision-making and optimizing overall operational efficiency."
          paras={['']}
        />
        <div>
          <Button onClick={() => setOpenCreationModal(true)}>
            +Add Employee
          </Button>
        </div>
      </div>
      <div className="mt-4">
        <Table
          columns={columns}
          loading={isLoadingQuery}
          dataSource={
            Array.isArray(employees?.results)
              ? employees?.results?.filter((elem) => elem?.isHrms)
              : []
          }
          rowKey={(_, index) => index}
          pagination={false}
          size="middle"
          scroll={{ x: true }}
          locale={{ emptyText: 'No Employees added yet' }}
        />
        <Pagination
          limit={limit}
          page={page}
          totalPages={employees?.totalPages}
          totalResults={employees?.totalResults}
          setPage={setPage}
          setLimit={setLimit}
          className={`w-full`}
        />
      </div>
    </>
  );
};

export default UserManagement;
