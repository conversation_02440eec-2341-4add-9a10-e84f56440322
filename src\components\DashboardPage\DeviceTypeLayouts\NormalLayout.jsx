import { useState, useEffect } from 'react';
import FieldsTypeLayout from './FieldsTypeLayout';

const NormalLayout = ({ device, values, colSpan }) => {
  const [buttonFields, setButtonFields] = useState([]);
  const [inputFields, setInputFields] = useState([]);

  useEffect(() => {
    if (device) {
      setButtonFields(
        device.deviceType.fields.filter((field) => field.type === 'button')
      );
      setInputFields(
        device.deviceType.fields.filter((field) => field.type === 'input')
      );
    }
  }, [device]);

  const temp = +colSpan?.split('-')?.[2];

  const gridCols = `grid-cols-${temp * 2}`;

  return (
    <>
      <div className={`grid mt-2 ${gridCols || ''}`}>
        {device.deviceType.fields.map((field, fIdx) => {
          if (
            field.type === 'dropdown' ||
            field.type === 'button' ||
            field.name === 'Device Status' ||
            field.type === 'input'
          ) {
            return null;
          }
          return (
            <div key={fIdx} className="flex flex-col items-center">
              <FieldsTypeLayout field={field} values={values} device={device} />
              <span className="text-[0.88rem] font-medium">{field.name}</span>
            </div>
          );
        })}
      </div>

      {/* input fields */}
      <div className={inputFields?.length > 0 ? 'pt-1 pb-2' : ''}>
        {inputFields?.map((iField, iIdx) => (
          <div key={iIdx} className="flex flex-col items-center">
            <span className="text-sm">{iField.name}</span>
            <FieldsTypeLayout field={iField} values={values} device={device} />
          </div>
        ))}
      </div>

      {/* button fields */}
      <div
        className={
          buttonFields.length > 0
            ? buttonFields.length > 1
              ? 'grid grid-cols-2 gap-4 pt-1 pb-2'
              : 'pb-2 pt-1 text-center'
            : ''
        }
      >
        {buttonFields &&
          buttonFields.map((bField, bIdx) => (
            <FieldsTypeLayout
              key={bIdx}
              field={bField}
              values={values}
              device={device}
            />
          ))}
      </div>
    </>
  );
};

export default NormalLayout;
