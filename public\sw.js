self.addEventListener('install', (_event) => {
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  event.waitUntil(self.clients.claim());
});

self.addEventListener('push', (event) => {
  if (!event.data) return;

  try {
    const data = event.data.json();
    const options = {
      body: data.body || data.message,
      icon: '/optiwise_logo.png',
      badge: '/optiwise_logo.png',
    };

    event.waitUntil(
      self.registration.showNotification(
        data.title || 'New Notification',
        options
      )
    );
  } catch (error) {
    event.waitUntil(
      self.registration.showNotification('New Notification', {
        body: 'You have a new notification',
        icon: '/optiwise_logo.png',
        badge: '/optiwise_logo.png',
      })
    );
  }
});
