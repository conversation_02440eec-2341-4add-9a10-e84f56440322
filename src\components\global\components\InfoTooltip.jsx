import { FcInfo } from 'react-icons/fc';
import { Tooltip } from 'react-tooltip';

export const InfoTooltip = ({
  className,
  isHtml = false,
  anchorName = '',
  id,
  children,
  position,
  content,
  contentWithBreaks = '',
  width = '200px',
}) => {
  return (
    <div className={className}>
      {isHtml === false ? (
        <FcInfo
          tabIndex={-1}
          className={`cursor-pointer ${anchorName}`}
          data-tooltip-id={id}
          data-tooltip-content={children}
          data-tooltip-place={position}
        />
      ) : (
        <FcInfo
          tabIndex={-1}
          className={`cursor-pointer ${anchorName}`}
          data-tooltip-id={id}
          data-tooltip-html={content}
          data-tooltip-place={position}
        />
      )}
      <Tooltip
        id={id}
        style={{
          backgroundColor: '#1e293b',
          color: 'white',
          fontWeight: 'normal',
          padding: '12px',
          fontSize: '12px',
          maxWidth: width,
          zIndex: '1000',
          borderRadius: '6px',
          lineHeight: '20px',
          whiteSpace: 'normal',
          overflowWrap: 'break-word',
        }}
        arrowColor="transparent"
      >
        <p>{contentWithBreaks}</p>
      </Tooltip>
    </div>
  );
};
