import { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
// import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ReactComponent as Preview } from '../../../assets/svgs/preview.svg';
import { calculate } from '../../../calculateString';
import { getPreviousProcess } from '../../../helperFunction';
import {
  useLazyGetCuProjectByIdQuery,
  useLazyGetCuProjectForIntervalQuery,
  useUpdateCuProjectQcMutation,
  useUpdateProcessGoalDataMutation,
} from '../../../slices/CuProjectNewApiSlice';
import { apiSlice } from '../../../slices/apiSlice';
import { useEditWorkOrderMutation } from '../../../slices/createPoApiSlice';
import { useUpdateCuNotificationMutation } from '../../../slices/cuNotificationApiSlice';
import { useEditGoalsTableMutation } from '../../../slices/goalsTableapiSlice';
import { useLazyGetMediaByIdQuery } from '../../../slices/mediaSlice';
import { useCreateNotificationMutation } from '../../../slices/notificationApiSlice';
import { Store } from '../../../store/Store';
import { DEFAULT_MULTIPLIER_VALUE } from '../../../utils/Constant';
import { customConfirm } from '../../../utils/customConfirm';
import {
  updateNextProcesses,
  updateWoItemProgress,
} from '../../../utils/updateNextProcess';
import { useUpdateWoItemProgressMutation } from '../../../slices/woItemProgressApiSlice';
import PreviewPopUp from '../../PreviewPop';
import Button from '../../global/components/Button';
import AdditionalFieldRender from '../AdditionalFieldRender';
import DragAndDrop from '../DragAndDrop';
import ActionPopup from '../Inhouse/ActionPopup';
import PopupWrapper from '../PopupWrapper';
import DynamicForm from './DynamicForm';

const createObjects = (rows, column) => {
  const result = {};
  let colsData = [];
  column.forEach((col) => {
    colsData.push({ type: col?.columnType || '', value: '' });
  });
  for (let i = 0; i < rows; i++) {
    result[i + 1] = colsData;
  }
  return result;
};

const QcDashboard = ({
  activeTile,
  cuProject,
  refreshPrevTile,
  currProcess,
  allEmployees,
  woId,
  getAllPo,
  itemForJob,
  setCusForStatus,
  selectedCi,
  selectedData,
}) => {
  const dispatch = useDispatch();

  const [capturedPhotos, setCapturedPhotos] = useState([]);
  const [stopDisable, setStopDisable] = useState(true);
  const [otherBtnDis, setotherBtnDis] = useState(true);
  const [userBatchInput, setuserBatchInput] = useState(false);
  const [qcData, setQcData] = useState();
  const [formData, setFormData] = useState({});
  const [remSubmit, setRemSub] = useState();
  const [currPass, setcurrPass] = useState(0);
  const [currScrap, setcurrScrap] = useState(0);
  const [currRework, setcurrRework] = useState(0);
  const [desc, setDesc] = useState('');
  const [ref, setref] = useState({});
  const [formDetail, setFormDetail] = useState({});
  const [isOpen, setIsOpen] = useState(false);
  const [isStop, setIsStop] = useState(false);
  const [samplingBatchSize, setSamplingBatchSize] = useState('');
  const [noOfSubmit, setnoOfSubmit] = useState('');
  const [currBatchSize, setCurrBatchSize] = useState();
  const [locationName, setLocationName] = useState('');
  const [generatedQrData, setGeneratedQrData] = useState({});
  const [numOfInterval, setNumOfInterval] = useState(0);
  const [numOfSamples, setNumOfSamples] = useState(0);
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [blobUrl, setBlobUrl] = useState(null);
  const [isCheckedAudio, setIsCheckedAudio] = useState(false); // eslint-disable-line

  const prevEmployee = allEmployees?.find(
    (user) => user._id === cuProject?.qcData?.operator
  );

  const batchSize =
    qcData?.batchInfo?.newBatchSize || qcData?.batchInfo?.['Batch Size'] || 0;

  const [getCuProjectById] = useLazyGetCuProjectByIdQuery();
  const [updateProgress] = useUpdateWoItemProgressMutation();
  const [getCuProjectForInterval] = useLazyGetCuProjectForIntervalQuery();
  const [updateProcessGoalData] = useUpdateProcessGoalDataMutation();

  const [updateCuProjectQc, { isLoading: isLoadingUpdateQC }] =
    useUpdateCuProjectQcMutation();
  const [editGoalsTable, { isLoading: isLoadingGoalsTables }] =
    useEditGoalsTableMutation();

  const [createNotification, { isLoading: isLoadingCreateNoti }] =
    useCreateNotificationMutation();

  const [updateCuNotification, { isLoading: isLoadingUpdateNotiCu }] =
    useUpdateCuNotificationMutation();

  const [editPo] = useEditWorkOrderMutation();
  const [getMediaById] = useLazyGetMediaByIdQuery();

  const {
    defaults,
    mqttClient,
    refreshParams: { refreshUrl },
    state: { cuData } = {},
  } = useContext(Store);

  let prevProcess = null;
  let prevGoalsTable = null;

  if (qcData?._id) prevProcess = getPreviousProcess(qcData) || null;

  //Previous process goalsTable
  if (prevProcess) {
    prevGoalsTable = qcData?.project?.goalsTable?.find(
      (table) => table?.flowId === prevProcess?._id
    );
  }

  //Current process goalsTable
  const currentGoalsTable = qcData?.project?.goalsTable?.find(
    (table) => table?.flowId === qcData?.flowId
  );

  //Multiplier of current processs
  const multiplier = currentGoalsTable?.multiplier || DEFAULT_MULTIPLIER_VALUE;
  const multiplierSybmol = multiplier.charAt(0);
  const multiplierValue = +multiplier.slice(1);

  //Previous process goalsTable
  const prevmultiplier = prevGoalsTable?.multiplier || DEFAULT_MULTIPLIER_VALUE;
  const prevmultiplierSybmol = prevmultiplier.charAt(0);
  const prevmultiplierValue = +prevmultiplier.slice(1);

  const defaultNoti = defaults?.defaultParam?.notificationDefaults;
  const QrFormat = defaults?.defaultParam?.prefixIds?.qr || {};

  const isFirstQc =
    cuData?.machineId?.mqtt?._id ===
    qcData?.productionFlow?.processes?.[0]?.mqtt;

  useEffect(() => {
    if (qcData) {
      const processArrayQC = qcData?.productionFlow?.processes;
      const currentIndexQC = processArrayQC?.findIndex(
        (item) => item?._id === qcData?.flowId
      );

      // const currentProcessQC = processArrayQC?.[currentIndexQC]?.mqtt;

      const currentProcessQC = processArrayQC?.[currentIndexQC]?.mqtt;
      const flowId = processArrayQC?.[currentIndexQC]?._id;

      const linkedForms = qcData?.project?.linkedForms;
      let currForm = {};
      if (linkedForms?.find((item) => item.flowId === flowId)) {
        currForm = linkedForms?.find((item) => item.flowId === flowId);
      } else {
        currForm = linkedForms?.find(
          (item) => item.processId?._id === currentProcessQC
        );
      }

      // const linkedForms = qcData?.project?.linkedForms;
      // const currForm = linkedForms?.find(
      //   (item) => item.processId?._id === currentProcessQC
      // );

      const index = +qcData.batchInfo?.batchNo - 1;

      //Setting number of submission based on its QC type
      if (currForm?.refData?.Sampling) {
        let submissions;

        if (
          (+qcData?.batchInfo?.newBatchSize ||
            +qcData?.batchInfo?.['Batch Size']) <
          +currForm?.refData?.interval[index]
        ) {
          setnoOfSubmit(+currForm?.refData?.samples[index]);
        } else {
          submissions = Math.floor(
            Math.floor(
              (+qcData?.batchInfo?.newBatchSize ||
                +qcData?.batchInfo?.['Batch Size']) /
                +currForm?.refData?.interval[index]
            ) * +currForm?.refData?.samples[index]
          );
        }

        if (currForm?.refData?.type === 'Percentage') {
          const submissions = Math.floor(
            ((+qcData?.batchInfo?.newBatchSize ||
              +qcData?.batchInfo?.['Batch Size']) *
              +currForm?.refData?.percent) /
              100
          );

          setnoOfSubmit(
            submissions ||
              +qcData?.batchInfo?.newBatchSize ||
              +qcData?.batchInfo?.['Batch Size']
          );
        } else if (currForm?.refData?.type === 'Single') {
          setnoOfSubmit(1);
        } else if (submissions) {
          setnoOfSubmit(submissions);
        }
      } else {
        setnoOfSubmit('');
      }
      //Setting up formDetails for further use
      setFormDetail(currForm || {});

      //Setting up QR data to show in QC form according to QrFormat setted from default page Optiwise
      let qrData = {};

      Object.keys(QrFormat || {}).forEach((type) => {
        let elem = type.substring(0, type.indexOf('_'));
        switch (elem) {
          case 'ModelName':
            qrData = {
              ...qrData,
              ModelName: qcData?.project?.modelName,
            };
            break;
          case 'Process':
            qrData = {
              ...qrData,
              Process: currProcess,
            };

            break;
          case 'BatchNo':
            qrData = {
              ...qrData,
              BatchNo: qcData?.batchInfo?.batchNo,
            };
            break;
        }
      });
      //Added signature and unit in QR
      qrData = {
        ...qrData,
        Signature: 'AICAN',
        Unit: currPass + currScrap + 1,
      };
      setGeneratedQrData(qrData);
    }
  }, [qcData, batchSize]); // eslint-disable-line

  //Setting up the qcData
  useEffect(() => {
    if (activeTile) {
      (async () => {
        const res = await getCuProjectById({ id: activeTile }).unwrap();
        if (res) {
          setQcData(res.cuProject);
        }
      })();
    }
  }, [
    activeTile,
    samplingBatchSize,
    currPass,
    currScrap,
    getCuProjectById,
    refreshPrevTile,
  ]);
  //refresh is removed from dependency, can add later

  useEffect(() => {
    const pass = qcData?.qcData?.passData?.quantity || 0;
    const scrap = qcData?.qcData?.scrapData?.quantity || 0;
    const rework = qcData?.qcData?.reworkData?.quantity || 0;

    setRemSub(batchSize);

    setDesc('');
    setFormData({});
    setcurrPass(pass);
    setcurrRework(rework);
    setcurrScrap(scrap);
  }, [qcData, batchSize]);

  useEffect(() => {
    setCapturedPhotos([]);
    setuserBatchInput(false);
  }, [activeTile]);

  //-----------------------------------------------------------------------------------------------
  //Setting up the currentBatchSize according to type of process in Production Flow(PF)
  useEffect(() => {
    if (qcData?._id) {
      if (
        isFirstQc ||
        prevProcess?.processCategory === 'QC' ||
        prevProcess?.processCategory === 'Outsource' ||
        prevProcess?.processCategory === 'Assembly'
      ) {
        setCurrBatchSize(
          qcData?.batchInfo?.newBatchSize || qcData?.batchInfo?.['Batch Size']
        );
      } else {
        (async () => {
          try {
            const data = {
              project: qcData?.project._id,
              flowId: prevProcess?._id,
              batchNo: qcData?.batchInfo?.batchNo,
              productionFlow: qcData?.productionFlow._id,
            };
            const res = await getCuProjectForInterval({ query: data }).unwrap();

            let count = 0;
            const mo = res?.cuProject?.machineAndOperator;

            mo?.forEach((item) => {
              count += item.manualStopData || 0;
            });

            let finalSentBatchSize = 0;
            if (prevmultiplierSybmol === '*') {
              finalSentBatchSize = Math.floor(count / prevmultiplierValue);
            } else if (prevmultiplierSybmol === '/') {
              finalSentBatchSize = Math.floor(count * prevmultiplierValue);
            }

            finalSentBatchSize = Math.floor(finalSentBatchSize);

            let finalReceivedBatchSize = 0;
            if (multiplierSybmol === '*') {
              finalReceivedBatchSize = Math.floor(
                finalSentBatchSize * multiplierValue
              );
            } else if (multiplierSybmol === '/') {
              finalReceivedBatchSize = Math.floor(
                finalSentBatchSize / multiplierValue
              );
            }

            setCurrBatchSize(finalReceivedBatchSize);
          } catch (error) {
            console.log(error); // eslint-disable-line
          }
        })();
      }
    }
  }, [refreshPrevTile, qcData, getCuProjectForInterval, isFirstQc]); // eslint-disable-line

  useEffect(() => {
    if (formDetail?.refData?.Sampling) {
      // const batchSize = +batchSize;

      const index = +qcData.batchInfo?.batchNo - 1;
      const type = formDetail?.refData?.type;

      let interval;
      let samples;

      if (type === 'Sampling') {
        interval = +formDetail?.refData['interval'][index];
        samples = +formDetail?.refData['samples'][index];
      } else if (type === 'Percentage') {
        interval = batchSize;
        samples = Math.floor(
          (+batchSize * +formDetail?.refData?.percent) / 100
        );
      } else if (type === 'Single') {
        interval = batchSize;
        samples = 1;
      }

      setNumOfInterval(interval);
      setNumOfSamples(samples);

      let arr = [];
      let x = interval;
      // Converting batch size into array of interval x
      while (x <= batchSize) {
        if (x === 0) break;
        arr.push(x);
        x += interval;
      }

      // Logic to check the index in which currBatchSize lies
      let left = 0;
      let right = arr.length - 1;
      let result = -1;
      let target = currBatchSize;

      while (left <= right) {
        let mid = Math.floor((left + right) / 2);

        if (arr[mid] <= target) {
          result = mid;
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }

      const incomingSample = qcData?.qcData?.samples || 0;

      const submissions = Math.floor(
        Math.floor(+batchSize / +interval) * +samples
      );

      if (
        (+qcData?.batchInfo?.newBatchSize ||
          +qcData?.batchInfo?.['Batch Size']) < +interval
      ) {
        setotherBtnDis(false);
        setStopDisable(false);
        if (formDetail?.refData?.Sampling) setuserBatchInput(true);
      } else {
        if (formDetail?.refData?.type === 'Percentage') {
          setStopDisable(false);
          setotherBtnDis(false);
          setuserBatchInput(true);
        } else if (submissions === currPass + currScrap) {
          setStopDisable(false);
          setotherBtnDis(true);
          setuserBatchInput(true);
        } else {
          setStopDisable(true);
          setotherBtnDis(false);
          setuserBatchInput(false);
        }

        if (
          formDetail?.refData?.type !== 'Percentage' &&
          formDetail?.refData?.type !== 'Single'
        ) {
          if (isFirstQc) {
            setotherBtnDis(false);
          } else {
            if (result !== -1) {
              let testSample = (result + 1) * samples;
              if (testSample === incomingSample) {
                setotherBtnDis(true);
              } else {
                setotherBtnDis(false);
              }
            } else {
              setotherBtnDis(true);
            }
          }
        }
      }
    } else {
      if (+batchSize === currPass + currScrap) {
        setStopDisable(false);
        setotherBtnDis(true);
      } else {
        setStopDisable(true);
        setotherBtnDis(false);
      }
    }
  }, [
    currPass,
    currScrap,
    currBatchSize,
    qcData,
    activeTile,
    refreshPrevTile,
    formDetail?.refData,
    batchSize,
    isFirstQc,
  ]);

  //-----------------------------------------------------------------------------------------------
  // Setting up default value for each field type
  useEffect(() => {
    if (formDetail) {
      let data = {};
      formDetail?.formId?.formData?.forEach((item) => {
        if (item.fieldType === 'Date') {
          data = {
            ...data,
            [item.fieldName]: {
              value: '',
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'Range') {
          data = {
            ...data,
            [item.fieldName]: {
              value: '',
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'Range Threshold') {
          data = {
            ...data,
            [item.fieldName]: {
              value: '',
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'Check') {
          data = {
            ...data,
            [item.fieldName]: {
              value: false,
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'String') {
          data = {
            ...data,
            [item.fieldName]: {
              value: '',
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'Table') {
          data = {
            ...data,
            [item.fieldName]: {
              type: item.fieldType,
              columns: item?.tableOptions?.column || [],
              noOfRows: +item?.tableOptions?.rows || 0,
              newRowNo: 0,
              rows: +item?.tableOptions?.rows || 0,
              row:
                item?.tableOptions?.row ||
                Array.from({ length: item?.tableOptions?.rows }, () => ''),
              rowData: createObjects(
                +item?.tableOptions?.rows || 0,
                item?.tableOptions?.column || []
              ),
            },
          };
        }
      });

      setFormData(data);
    }
  }, [activeTile, formDetail, remSubmit]);

  //-----------------------------------------------------------------------------------------------
  // Generating Reference Data(ref data) for validation during form submission
  useEffect(() => {
    if (formDetail) {
      const refData = formDetail?.refData;
      const formfield = formDetail?.formId?.formData;

      let data = {};
      formfield?.forEach((item) => {
        if (item.fieldType === 'Date') {
          let fName = item.fieldName;
          let cond = fName + '-Condition';
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';

          data = {
            ...data,
            [fName]: {
              value: refData?.[fName],
              type: 'Date',
              condition: refData?.[cond],
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        } else if (item.fieldType === 'Range') {
          let fName = item.fieldName;
          let min = fName + '-Min';
          let max = fName + '-Max';
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';

          data = {
            ...data,
            [fName]: {
              min: Number(refData?.[min]),
              max: Number(refData?.[max]),
              type: 'Range',
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        } else if (item.fieldType === 'Range Threshold') {
          let fName = item.fieldName;
          let main = fName + '-Main';
          let thres = fName + '-Thres';
          let min = Number(refData?.[main]) - Number(refData?.[thres]);
          let max = Number(refData?.[main]) + Number(refData?.[thres]);
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';

          data = {
            ...data,
            [fName]: {
              min: min,
              max: max,
              type: 'Range Threshold',
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        } else if (item.fieldType === 'String') {
          let fName = item.fieldName;
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';
          data = {
            ...data,
            [fName]: {
              type: 'String',
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        } else if (item.fieldType === 'Check') {
          let fName = item.fieldName;
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';
          data = {
            ...data,
            [fName]: {
              type: 'Check',
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        }
      });
      setref(data);

      if (refData?.audioFile) {
        (async () => {
          const res = await getMediaById({ id: refData?.audioFile }).unwrap();
          setBlobUrl(res?.media?.data);
        })();
      }
    }
  }, [formDetail]); // eslint-disable-line

  //-----------------------------------------------------------------------------------------------
  // STOP Handler
  const handleStopSubmit = async () => {
    let data = {
      description: desc,
      media: capturedPhotos,
    };

    const fData = {
      date: Date.now(),
      id: activeTile,
      batchDesc: data,
      stopLocation: locationName,
      samplingBatchSize,
    };

    let tempData = {};
    if (samplingBatchSize === '') {
      tempData = {
        ['Batch Size']: currPass,
      };
    } else if (
      (formDetail?.refData?.type === 'Percentage' ||
        formDetail?.refData?.type === 'Single') &&
      Number(samplingBatchSize) <=
        Number(
          +qcData?.batchInfo?.newBatchSize || +qcData?.batchInfo?.['Batch Size']
        )
    ) {
      tempData = {
        ['Batch Size']: +samplingBatchSize,
      };
    } else if (Number(samplingBatchSize) <= Number(currBatchSize)) {
      tempData = {
        ['Batch Size']: +samplingBatchSize,
      };
    } else {
      toast.error(
        `Entered batch size cannot surpass the current batch size (${currBatchSize})`
      );
      return;
    }

    // if (selectedEmployee === '') {
    //   toast.error(`Select operator to stop!`);
    //   return;
    // }

    try {
      const update = await updateCuProjectQc({
        data: fData,
        type: 'stop',
      }).unwrap();
      if (update) {
        let currentDate = new Date();
        setCusForStatus((prev) => {
          let currentCu = prev?.find(
            (cu) =>
              cu?.project === selectedCi?._id &&
              cu?.flowId === selectedData?.flow._id &&
              cu?.batchInfo?.batchNo ===
                selectedData?.batchNo?.[selectedCi?._id]
          );
          let newCus = [];
          for (let i = 0; i < prev?.length; i++) {
            if (prev?.[i]?._id === currentCu?._id) {
              newCus.push({
                ...prev?.[i],
                status: 'complete',
                stopTime: currentDate,
                stopLocation: locationName,
              });
            } else {
              newCus.push(prev?.[i]);
            }
          }
          return newCus;
        });

        // Update acitivity time for respective workOrder
        await editPo({
          id: woId,
          data: { latestActivityTime: Date.now(), activity: true },
        }).unwrap();

        let nextProcess = null;
        const flowid = qcData?.flowId;
        const processArray = qcData?.productionFlow?.processes;
        const currentIndex = processArray?.findIndex(
          (item) => item?._id === flowid
        );

        if (currentIndex === processArray?.length - 1) {
          nextProcess = null;
        } else if (
          processArray?.[currentIndex + 1]?.processCategory === 'Outsource'
        ) {
          nextProcess = null;
        } else {
          nextProcess = processArray?.[currentIndex + 1]?.mqtt;
        }

        if (nextProcess) {
          const finaldata = {
            batchNo: qcData.batchInfo.batchNo,
            createInputId: cuProject.project._id,
            index: currentIndex,
            action: 'stop',
          };

          await updateCuNotification({ data: finaldata }).unwrap();
        }

        if (defaultNoti?.Stop) {
          const response = await createNotification({
            data: {
              createdAt: new Date(),
              cuProject: cuProject?._id,
              type: 'stop',
            },
          }).unwrap();

          if (response) {
            if (mqttClient?.publish)
              mqttClient?.publish(refreshUrl, `ACTION_CHANGE`);
          }
        }

        if (
          processArray[currentIndex + 1]?.processCategory === 'Outsource' &&
          currentIndex !== processArray.length - 1
        ) {
          const response = await createNotification({
            data: {
              createdAt: new Date(),
              cuProject: cuProject._id,
              type: 'outsource',
            },
          }).unwrap();

          if (response) {
            if (mqttClient?.publish)
              mqttClient?.publish(refreshUrl, `OUT_SOURCE`);
          }
        }

        if (mqttClient?.publish)
          mqttClient?.publish(
            '/IP/REFRESH/IOT/CONTROLUNIT/APP',
            `REFRESH_PREV_TILE`
          );

        const newData = update?.cuProject;
        const allGoalsTables = newData?.project?.goalsTable;
        const productionFlow = newData?.productionFlow;
        const goalsTable = allGoalsTables?.find(
          (gt) => gt.flowId === newData?.flowId
        );

        const currentFlowIdIdx = productionFlow?.processes?.findIndex(
          (pro) => pro._id === newData?.flowId
        );

        const { multiplier = DEFAULT_MULTIPLIER_VALUE } = goalsTable;

        if (multiplier?.startsWith('*')) {
          tempData['Batch Size'] = calculate(
            `${tempData['Batch Size']}${multiplier.replace('*', '/')}`
          );
        } else if (multiplier?.startsWith('/')) {
          tempData['Batch Size'] = calculate(
            `${tempData['Batch Size']}${multiplier.replace('/', '*')}`
          );
        }

        tempData['Batch Size'] = +Math.floor(tempData?.['Batch Size']);

        // for process that is multiprocess
        if (newData?.isMultiProcess) {
          // modify tableData from updated cuProject
          const tableData = goalsTable?.tableData?.map((table) => {
            // run for every subprocess subprocess
            updateNextProcesses(
              productionFlow,
              newData,
              tempData,
              currentFlowIdIdx,
              allGoalsTables,
              editGoalsTable
            );

            // modify the data of batch in goals table for stopped cu project
            if (table?.batchNo === newData?.batchInfo?.batchNo) {
              // variable to store the index of subprocess process that is stopped
              let stoppedIdx;
              return {
                ...table,
                status:
                  +newData?.subProcessIndex ===
                  table?.subProcessData?.length - 1
                    ? 'complete'
                    : table?.status,
                subProcessData: table?.subProcessData?.map((item, iIdx) => {
                  if (iIdx === newData?.subProcessIndex) {
                    // update the stopped sub process status
                    stoppedIdx = iIdx;
                    return { ...item, status: 'complete' };
                  }

                  // update batch size of all the sub processes after stopped sub process
                  if (iIdx >= stoppedIdx && stoppedIdx >= 0) {
                    return {
                      ...item,
                      ['Batch Size']:
                        tempData?.['Batch Size'] || item?.['Batch Size'],
                      newBatchSize: calculate(
                        `${tempData?.['Batch Size'] || item?.['Batch Size']}${
                          goalsTable?.multiplier || DEFAULT_MULTIPLIER_VALUE
                        }`
                      ),
                    };
                  }

                  return item;
                }),
              };
            }
            return table;
          });

          if (tableData.length > 0)
            await editGoalsTable({
              id: goalsTable._id,
              data: {
                tableData,
              },
            }).unwrap();
        } else {
          // for process that is not multiprocess

          const tableData = goalsTable?.tableData?.map((table) => {
            // modify the data of batch in goals table for stopped cu project
            if (table?.batchNo === newData?.batchInfo?.batchNo) {
              return {
                ...table,
                status: 'complete',
                outputSize: tempData?.['Batch Size'],
              };
            }
            return table;
          });

          if (tableData?.length > 0)
            await editGoalsTable({
              id: goalsTable._id,
              data: {
                tableData,
              },
            }).unwrap();

          updateNextProcesses(
            productionFlow,
            newData,
            tempData,
            currentFlowIdIdx,
            allGoalsTables,
            editGoalsTable
          );
        }

        // Update PROGRESS
        // if current process is the last process then update woItemProgress
        if (currentFlowIdIdx === productionFlow?.processes?.length - 1) {
          await updateWoItemProgress(
            updateProgress,
            {
              indexId: itemForJob?._id,
              workOrder: woId,
            },
            {
              producedQuantity: tempData?.['Batch Size'] || 0,
              cuProject: newData?._id,
            }
          );
        }

        tempData['Batch Size'] = calculate(
          `${tempData['Batch Size']}${multiplier}`
        );

        await updateProcessGoalData({
          id: cuProject.processGoalData._id,
          data: {
            dataOnStop: {
              machineAndOperatorId: selectedEmployee,
              data: tempData,
            },
          },
        }).unwrap();

        setDesc('');
        setFormData({});
        setcurrPass(0);
        setcurrRework(0);
        setcurrScrap(0);
        setQcData({});
        setIsStop(false);
        setSelectedEmployee('');
        setCapturedPhotos([]);
      }

      if (typeof getAllPo !== 'undefined') {
        getAllPo();
      }

      dispatch(apiSlice.util.invalidateTags(['CuProject', 'CuProjectBatch']));
    } catch (err) {
      toast.error(err?.response?.data?.message || err?.message, {
        position: 'top-right',
        toastId: err?.response?.data?.message || err?.message,
      });
    }
  };

  //-----------------------------------------------------------------------------------------------
  // PASS Handler
  const addPass = async (e) => {
    let flag = true;
    let cnt = 0;
    let msg = '';
    let textManMsg = '';
    let textManCnt = 0;
    let mediaManMsg = '';
    let mediaManCnt = 0;

    const { name, value } = e.target;
    for (let key in formData) {
      if (ref[key]?.textMan) {
        if (formData[key] === '' || formData[key]?.value === '') {
          textManMsg = textManMsg + ' ' + key;
          textManCnt++;
        }
      }

      if (ref[key]?.mediaMan) {
        if (formData[key] === '') {
          mediaManMsg = mediaManMsg + ' ' + key;
          mediaManCnt++;
        } else if (
          !formData[key].hasOwnProperty('media') || // eslint-disable-line
          formData[key]?.media?.length === 0
        ) {
          mediaManMsg = mediaManMsg + ' ' + key;
          mediaManCnt++;
        }
      }

      if (ref[key]?.type === 'Range') {
        if (
          Number(formData[key]?.value) >= ref[key]?.min &&
          Number(formData[key]?.value) <= ref[key]?.max
        ) {
          flag = true;
        } else {
          flag = false;
          msg = msg + ' ' + key;
          cnt++;
        }
      } else if (ref[key]?.type === 'Range Threshold') {
        if (
          Number(formData[key]?.value) >= ref[key]?.min &&
          Number(formData[key]?.value) <= ref[key]?.max
        ) {
          flag = true;
        } else {
          flag = false;
          msg = msg + ' ' + key;
          cnt++;
        }
      } else if (ref[key]?.type === 'Date') {
        if (ref[key]?.condition === 'GT') {
          if (formData[key]?.value > ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ' + key;
            cnt++;
          }
        } else if (ref[key]?.condition === 'LT') {
          if (formData[key]?.value < ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ' + key;
            cnt++;
          }
        } else if (ref[key]?.condition === 'ET') {
          if (formData[key]?.value === ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ' + key;
            cnt++;
          }
        }
      }
    }

    if (textManCnt) {
      toast.error(`${textManMsg} are required!`);
      setotherBtnDis(false);
      return;
    }

    if (mediaManCnt) {
      toast.error(`Media for ${mediaManMsg} are required!`);
      setotherBtnDis(false);
      return;
    }

    if (cnt) {
      if (
        await customConfirm(
          `Validation Check for ${msg}, Do you want to proceed?`
        )
      ) {
        flag = true;
      } else {
        flag = false;
      }
    }

    if (flag) {
      if (remSubmit > 0) {
        const data = {
          ...formData,
          [name]: value,
          qr: {
            qrData: generatedQrData,
            createInput: qcData?.project._id,
            flowId: qcData?.flowId,
            batchNo: Number(qcData?.batchInfo?.batchNo),
            productionFlow: qcData?.productionFlow?._id,
          },
        };

        let fData = {};
        if (formDetail?.refData?.Sampling) {
          fData = {
            id: activeTile,
            formData: data,
            type: 'Pass',
            sampling: true,
          };
        } else {
          fData = {
            id: activeTile,
            formData: data,
            type: 'Pass',
          };
        }

        const update = await updateCuProjectQc({ data: fData }).unwrap();
        if (update) {
          setcurrPass(update?.finalForm?.passData?.quantity);
          setRemSub(remSubmit - 1);

          if (mqttClient?.publish)
            mqttClient?.publish(
              '/IP/REFRESH/IOT/CONTROLUNIT/APP',
              `REFRESH_PREV_TILE`
            );
          toast.success('Pass Added Successfully');
          // Update acitivity time for respective workOrder
          await editPo({
            id: woId,
            data: { latestActivityTime: Date.now(), activity: true },
          }).unwrap();
        }
        dispatch(apiSlice.util.invalidateTags(['CuProject']));
      }
    } else {
      setotherBtnDis(false);
    }
  };
  //-----------------------------------------------------------------------------------------------
  // SCRAP Handler
  const addScrap = async (e) => {
    let flag = true;
    let cnt = 0;
    let msg = '';
    let textManMsg = '';
    let textManCnt = 0;
    let mediaManMsg = '';
    let mediaManCnt = 0;

    const { name, value } = e.target;
    for (let key in formData) {
      if (ref[key]?.textMan) {
        if (formData[key] === '' || formData[key]?.value === '') {
          textManMsg = textManMsg + ' ' + key;
          textManCnt++;
        }
      }

      if (ref[key]?.mediaMan) {
        if (formData[key] === '') {
          mediaManMsg = mediaManMsg + ' ' + key;
          mediaManCnt++;
        } else if (
          !formData[key].hasOwnProperty('media') || // eslint-disable-line
          formData[key]?.media?.length === 0
        ) {
          mediaManMsg = mediaManMsg + ' ' + key;
          mediaManCnt++;
        }
      }

      if (ref[key]?.type === 'Range') {
        if (
          Number(formData[key]?.value) >= ref[key]?.min &&
          Number(formData[key]?.value) <= ref[key]?.max
        ) {
          flag = true;
        } else {
          flag = false;
          msg = msg + ' ,' + key;
          cnt++;
        }
      } else if (ref[key]?.type === 'Range Threshold') {
        if (
          Number(formData[key]?.value) >= ref[key]?.min &&
          Number(formData[key]?.value) <= ref[key]?.max
        ) {
          flag = true;
        } else {
          flag = false;
          msg = msg + ' ,' + key;
          cnt++;
        }
      } else if (ref[key]?.type === 'Date') {
        if (ref[key]?.condition === 'GT') {
          if (formData[key]?.value > ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ,' + key;
            cnt++;
          }
        } else if (ref[key]?.condition === 'LT') {
          if (formData[key]?.value < ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ,' + key;
            cnt++;
          }
        } else if (ref[key]?.condition === 'ET') {
          if (formData[key]?.value === ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ,' + key;
            cnt++;
          }
        }
      }
    }

    if (textManCnt) {
      toast.error(`${textManMsg} are required!`);
      setotherBtnDis(false);
      return;
    }

    if (mediaManCnt) {
      toast.error(`Media for ${mediaManMsg} are required!`);
      setotherBtnDis(false);
      return;
    }

    if (cnt) {
      if (
        await customConfirm(
          `Validation Check for ${msg}, Do you want to proceed?`
        )
      ) {
        flag = true;
      } else {
        flag = false;
      }
    }

    if (flag) {
      if (remSubmit > 0) {
        const data = {
          ...formData,
          [name]: value,
          qr: {
            qrData: generatedQrData,
            createInput: qcData?.project._id,
            flowId: qcData?.flowId,
            batchNo: Number(qcData?.batchInfo?.batchNo),
            productionFlow: qcData?.productionFlow?._id,
          },
        };

        let fData = {};
        if (formDetail?.refData?.Sampling) {
          fData = {
            id: activeTile,
            formData: data,
            type: 'Scrap',
            sampling: true,
          };
        } else {
          fData = {
            id: activeTile,
            formData: data,
            type: 'Scrap',
          };
        }

        const update = await updateCuProjectQc({ data: fData }).unwrap();
        if (update) {
          setcurrScrap(update?.finalForm?.scrapData?.quantity);
          setRemSub(remSubmit - 1);
          // setFormData({});

          if (mqttClient?.publish)
            mqttClient?.publish(
              '/IP/REFRESH/IOT/CONTROLUNIT/APP',
              `REFRESH_PREV_TILE`
            );
          toast.success('Scrap Added Successfully');
          // Update acitivity time for respective workOrder
          await editPo({
            id: woId,
            data: { latestActivityTime: Date.now(), activity: true },
          }).unwrap();
        }

        dispatch(apiSlice.util.invalidateTags(['CuProject']));
      }
    } else {
      setotherBtnDis(false);
    }
  };

  //-----------------------------------------------------------------------------------------------
  // REWORK Handler
  const addRework = async (e) => {
    let flag = true;
    let cnt = 0;
    let msg = '';
    let textManMsg = '';
    let textManCnt = 0;
    let mediaManMsg = '';
    let mediaManCnt = 0;

    const { name, value } = e.target;
    for (let key in formData) {
      if (ref[key]?.textMan) {
        if (formData[key] === '' || formData[key]?.value === '') {
          textManMsg = textManMsg + ' ' + key;
          textManCnt++;
        }
      }

      if (ref[key]?.mediaMan) {
        if (formData[key] === '') {
          mediaManMsg = mediaManMsg + ' ' + key;
          mediaManCnt++;
        } else if (
          !formData[key].hasOwnProperty('media') || // eslint-disable-line
          formData[key]?.media?.length === 0
        ) {
          mediaManMsg = mediaManMsg + ' ' + key;
          mediaManCnt++;
        }
      }

      if (ref[key]?.type === 'Range') {
        if (
          Number(formData[key]?.value) >= ref[key]?.min &&
          Number(formData[key]?.value) <= ref[key]?.max
        ) {
          flag = true;
        } else {
          flag = false;
          msg = msg + ' ' + key;
          cnt++;
        }
      } else if (ref[key]?.type === 'Range Threshold') {
        if (
          Number(formData[key]?.value) >= ref[key]?.min &&
          Number(formData[key]?.value) <= ref[key]?.max
        ) {
          flag = true;
        } else {
          flag = false;
          msg = msg + ' ,' + key;
          cnt++;
        }
      } else if (ref[key]?.type === 'Date') {
        if (ref[key]?.condition === 'GT') {
          if (formData[key]?.value > ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ,' + key;
            cnt++;
          }
        } else if (ref[key]?.condition === 'LT') {
          if (formData[key]?.value < ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ,' + key;
            cnt++;
          }
        } else if (ref[key]?.condition === 'ET') {
          if (formData[key]?.value === ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ,' + key;
            cnt++;
          }
        }
      }
    }

    if (textManCnt) {
      toast.error(`${textManMsg} are required!`);
      setotherBtnDis(false);
      return;
    }

    if (mediaManCnt) {
      toast.error(`Media for ${mediaManMsg} are required!`);
      setotherBtnDis(false);
      return;
    }

    if (cnt) {
      if (
        await customConfirm(
          `Validation Check for ${msg}, Do you want to proceed?`
        )
      ) {
        flag = true;
      } else {
        flag = false;
      }
    }

    if (flag) {
      if (remSubmit > 0 && name !== 'Submit') {
        const data = {
          ...formData,
          [name]: value,
        };

        const fData = {
          id: activeTile,
          formData: data,
          type: 'Rework',
        };

        const update = await updateCuProjectQc({ data: fData }).unwrap();
        if (update) {
          setcurrRework(update?.finalForm?.reworkData?.quantity);
          setRemSub(remSubmit - 1);
          // setFormData({});

          if (mqttClient?.publish)
            mqttClient?.publish(
              '/IP/REFRESH/IOT/CONTROLUNIT/APP',
              `REFRESH_PREV_TILE`
            );
          toast.success('Rework Added Successfully');
          // Update acitivity time for respective workOrder
          await editPo({
            id: woId,
            data: { latestActivityTime: Date.now(), activity: true },
          }).unwrap();
        }
        dispatch(apiSlice.util.invalidateTags(['CuProject']));
        setotherBtnDis(false);
      }
    } else {
      setotherBtnDis(false);
    }
  };

  //-----------------------------------------------------------------------------------------------
  // Change Handler
  const changeHandler = (e) => {
    for (let i in e) {
      const fr = new FileReader();
      if (i === 'length') return;
      let name = e[i].name;
      let type = e[i].type;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        setCapturedPhotos((prev) => [
          ...prev,
          {
            name: name,
            type: type,
            data: url,
          },
        ]);
      });
    }
  };

  const removeImgHandler = (url) => {
    setCapturedPhotos((prev) => prev.filter((item) => item.data !== url));
  };

  const refData = formDetail.refData;

  const loaderState = // eslint-disable-line
    isLoadingCreateNoti ||
    isLoadingUpdateQC ||
    isLoadingGoalsTables ||
    isLoadingUpdateNotiCu ||
    false;

  return (
    <div className=" bg-white h-full flex flex-col items-center justify-center  w-full ">
      {isStop && (
        <PopupWrapper setOpenPopup={() => setIsStop(false)}>
          <ActionPopup
            popupFor="stop"
            setOperatorPopup={setIsStop}
            userBatchSizeInput={userBatchInput}
            samplingBatchSize={samplingBatchSize}
            setSamplingBatchSize={setSamplingBatchSize}
            setLocationName={setLocationName}
            locationName={locationName}
            allEmployees={allEmployees}
            selectedEmployee={selectedEmployee}
            setSelectedEmployee={setSelectedEmployee}
            prevEmployee={prevEmployee}
            submitFunction={handleStopSubmit}
            itemForJob={itemForJob}
            cuProject={cuProject}
          />
        </PopupWrapper>
      )}
      {qcData?._id ? (
        <div className="bg-white  md:px-6 w-full lg:h-full md:h-[110%] h-[90%] ">
          <div className="grid grid-cols-2">
            {/* TODO : Commented for now */}
            {/* <label className="inline-flex items-center cursor-pointer w-fit ">
              <input
                type="checkbox"
                value=""
                checked={isCheckedAudio}
                onChange={() => {
                  if (!blobUrl && !isCheckedAudio) {
                    toast.error('No attachment found');
                  }
                  setIsCheckedAudio((prev) => !prev);
                }}
                className="sr-only peer"
              />
              <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300  rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              <span className="ms-3 text-sm font-medium text-gray-900">
                Show Player
              </span>
            </label> */}
            {isCheckedAudio && (
              <>
                {blobUrl && (
                  <audio src={blobUrl} controls className="w-full px-2" />
                )}
              </>
            )}
          </div>
          <div className="mt-1 flex flex-col w-full h-full  ">
            {cuProject?.mqtt?.additionalFields.length > 0 && (
              <div className="w-full mt-5">
                <AdditionalFieldRender
                  fields={cuProject?.mqtt?.additionalFields || []}
                  activeTile={activeTile}
                  saveDisabled={!cuProject?.additionalFields ? false : true}
                />
              </div>
            )}
            <div className="flex justify-between sm:mt- w-full">
              <div className="flex  w-full">
                <h1 className=" text-[16px] md:text-lg font-medium sm:mt-2 mt-3 w-[110%] tracking-tighter sm:tracking-normal ">
                  Unit submitted from {prevProcess?.processName || 'N/A'} :
                  &nbsp;
                  <span className=" text-green-600 ">{currBatchSize}</span>
                </h1>
              </div>
              <h1 className="text-[16px] md:text-lg flex justify-end  font-medium sm:mt-1 md:mt-2 w-full  tracking-tighter  sm:tracking-normal">
                Mode:{' '}
                {refData?.type === 'Percentage'
                  ? `${refData?.type}(${refData?.percent}%)`
                  : `${refData?.type || 'None'}`}
              </h1>
              {/* <div className=" w-[50%] md:w-[30%] flex justify-evenly ">
                <Link to="/outward" className="w-[30%]">
                  <Button color="brown" className="w-full ">
                    OUT
                  </Button>
                </Link>
                <Link to="/inward" className="w-[30%] ">
                  <Button color="darkgreen" className="w-full  ">
                    IN
                  </Button>
                </Link>
              </div> */}
            </div>
            {/* <h1 className="text-[16px] md:text-lg  font-medium sm:mt-1 md:mt-2 w-full  tracking-tighter  sm:tracking-normal">
              Mode:{' '}
              {refData?.type === 'Percentage'
                ? `${refData?.type}(${refData?.percent}%)`
                : `${refData?.type || 'None'}`}
            </h1> */}

            {refData?.Sampling && (
              <>
                {refData?.type === 'Single' ||
                refData?.type === 'Percentage' ? (
                  <>
                    <h1 className="text-md font-medium text-[16px] md:text-lg  w-full flex   ">
                      Number of units to be sampled : &nbsp;
                      <span className=" text-green-600 text-xl">
                        {numOfSamples}
                      </span>
                      &nbsp;units
                    </h1>
                  </>
                ) : (
                  <>
                    <h1 className=" w-full flex text-md font-medium text-[16px] md:text-lg ">
                      <span className=" text-green-600 text-xl tracking-tighter">
                        {' '}
                        {numOfSamples}
                      </span>{' '}
                      &nbsp;units to be sampled after every&nbsp;
                      <span className=" text-green-600 text-xl">
                        {numOfInterval}
                      </span>
                      &nbsp;units
                    </h1>
                  </>
                )}
              </>
            )}
            {/* from here form start */}
            <div className="flex justify-evenly mt-2 ml-4">
              <div className="">
                <h1 className=" text-[14px] md:text-md font-semibold w-full flex justify-center ">
                  Batch Size
                </h1>
                <h1 className="text-[14px] md:text-2xl sm:font-bold font-semibold w-full flex justify-center text-xl">
                  {batchSize}
                </h1>
              </div>
              <div>
                <h1 className=" text-[14px] md:text-md font-semibold w-full flex justify-center">
                  Pass
                </h1>
                <h1 className="text-[14px] md:text-2xl sm:font-bold font-semibold w-full flex justify-center text-xl">
                  ({currPass}/{noOfSubmit === '' ? batchSize : noOfSubmit})
                </h1>
              </div>
              <div>
                <h1 className=" text-[14px] md:text-md font-semibold w-full flex justify-center">
                  Rework
                </h1>
                <h1 className="text-[14px] md:text-2xl sm:font-bold font-semibold w-full flex justify-center text-xl">
                  {currRework}
                </h1>
              </div>
              <div>
                <h1 className=" text-[14px] md:text-md font-semibold w-full flex justify-center">
                  Scrap
                </h1>
                <h1 className="text-[14px] md:text-2xl sm:font-bold font-semibold w-full flex justify-center text-xl">
                  ({currScrap}/ {noOfSubmit === '' ? batchSize : noOfSubmit})
                </h1>
              </div>
            </div>
            <div
              onSubmit={(e) => {
                e.preventDefault();
              }}
              className=" w-full  "
            >
              {formDetail && (
                <DynamicForm
                  formDetail={formDetail}
                  formData={formData}
                  setFormData={setFormData}
                  generatedQrData={generatedQrData}
                />
              )}

              {isOpen && (
                <PreviewPopUp isOpen={isOpen} setIsOpen={setIsOpen}>
                  <div className="px-8 mt-[6%] ">
                    <h3 className="mb-1  text-lg">Uploaded Images </h3>
                    {capturedPhotos?.length < 1 && (
                      <p className="ml-[42%] mt-5">No Image Selected</p>
                    )}
                    <div className="flex w-full gap-x-5 overflow-y-scroll">
                      {capturedPhotos?.map((item, uIdx) => (
                        <section
                          key={uIdx}
                          className="p-2 border rounded-md w-fit"
                        >
                          <img
                            src={item?.data}
                            alt=""
                            className="hover:cursor-pointer min-w-[180px] max-w-[250px]"
                          />

                          <section className="flex justify-between items-center text-sm mt-2">
                            <span className="px-2">{item?.name}</span>
                            <button
                              onClick={() => removeImgHandler(item?.data)}
                              className="outline-none text-red-primary hover:text-white hover:bg-red-primary rounded px-2 py-1"
                            >
                              Remove
                            </button>
                          </section>
                        </section>
                      ))}
                    </div>
                  </div>
                </PreviewPopUp>
              )}
              <div className="flex justify-between items-center w-full pl-6">
                <div className="mb-4 w-[70%] mt-3 ">
                  <label
                    htmlFor="description"
                    className="block text-gray-700 font-bold mb-2"
                  >
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={desc}
                    onChange={(e) => setDesc(e.target.value)}
                    className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight ml-4 focus:outline-none focus:shadow-outline"
                    rows="2"
                    placeholder="Enter your description"
                  />
                </div>
                <div className="flex w-[23%]">
                  <DragAndDrop
                    className="h-8 mt-5 md:mt-8 lg:mr-[3.8%] text-xs px-[2.3%] "
                    accept="image/png, image/jpeg"
                    fileType="JPG/PNG"
                    onChange={(e) => changeHandler(e)}
                    multiple
                  />
                  <Preview
                    className="w-[15%] h-[15%] mt-6 md:mt-9  ml-2"
                    onClick={() => setIsOpen(!isOpen)}
                  />
                </div>
              </div>
            </div>

            <div className="w-full justify-between  flex py-3 px-2 h-fit  ">
              <Button
                className="py-2"
                color="green"
                width="w-[21%]"
                name="Quantity"
                value="Pass"
                type="button"
                disabled={otherBtnDis || isLoadingUpdateQC}
                onClick={(e) => {
                  setotherBtnDis(true);
                  addPass(e);
                }}
              >
                Pass
              </Button>
              <Button
                className="py-2"
                color="brown"
                width="w-[21%]"
                name="Quantity"
                value="Rework"
                type="button"
                disabled={otherBtnDis || isLoadingUpdateQC}
                onClick={(e) => {
                  setotherBtnDis(true);

                  addRework(e);
                }}
              >
                Rework
              </Button>
              <Button
                className="py-2"
                color="blue"
                width="w-[21%]"
                name="Quantity"
                value="Scrap"
                type="button"
                disabled={otherBtnDis || isLoadingUpdateQC}
                onClick={(e) => {
                  setotherBtnDis(true);
                  addScrap(e);
                }}
              >
                Scrap
              </Button>

              <Button
                className="py-2"
                color="red"
                width="w-[21%]"
                name="Submit"
                value="Submit"
                type="button"
                disabled={stopDisable || isLoadingUpdateQC}
                onClick={() => setIsStop((prev) => !prev)}
              >
                Stop
              </Button>
            </div>
          </div>
        </div>
      ) : (
        'Please Select an Active batch'
      )}
    </div>
  );
};

export default QcDashboard;
