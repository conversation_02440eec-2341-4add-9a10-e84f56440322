import {
  BankOutlined,
  HomeOutlined,
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Divider, Tag, Typography } from 'antd';
import ShowTemplateValues from '../../../v3/global/components/ShowTemplateValues';

const { Text } = Typography;

const CustomerSidebar = ({ sidebarData }) => {
  if (!sidebarData) return null;
  const {
    name,
    id,
    company_name,
    paymentTerm,
    unique_id,
    gstNumber,
    address,
    phone_no,
    billingAddress,
  } = sidebarData;

  const SectionTitle = ({ icon, text }) => (
    <div className="flex items-center gap-2 mb-3">
      {icon}
      <span className="text-gray-700 font-medium text-base">{text}</span>
    </div>
  );

  return (
    <div>
      <div className="mb-4">
        <SectionTitle
          icon={<UserOutlined className="text-blue-600" />}
          text="Customer Details"
        />
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-1">
            <Text className="text-gray-500 text-sm">Company ID</Text>
            <Text className="block font-medium">{id}</Text>
          </div>
          <div className="space-y-1">
            <Text className="text-gray-500 text-sm">Name</Text>
            <Text className="block font-medium">{name}</Text>
          </div>
          <div className="space-y-1">
            <Text className="text-gray-500 text-sm">Company Name</Text>
            <Text className="block font-medium">{company_name}</Text>
          </div>
          <div className="space-y-1">
            <Text className="text-gray-500 text-sm">Payment Term</Text>
            <div className="block">
              <Tag color="blue">{paymentTerm}</Tag>
            </div>
          </div>
        </div>
      </div>

      <Divider className="my-3" />

      <div className="mb-4">
        <SectionTitle
          icon={<MailOutlined className="text-green-600" />}
          text="Email Addresses"
        />
        <div className="space-y-2">
          {unique_id
            ?.filter((id) => id !== '' && id !== 'null')
            .map((email, index) => (
              <Tag key={index} color="green" className="mr-2 mb-2 py-1">
                {email}
              </Tag>
            ))}
        </div>
      </div>

      <Divider className="my-3" />

      <div className="mb-4">
        <SectionTitle
          icon={<BankOutlined className="text-purple-600" />}
          text="GST Numbers"
        />
        <div className="space-y-2">
          {gstNumber
            ?.filter((gst) => gst !== '' && gst !== 'null')
            .map((gst, index) => (
              <Tag key={index} color="purple" className="mr-2 mb-2 py-1">
                {gst}
              </Tag>
            ))}
        </div>
      </div>

      <Divider className="my-3" />

      <div className="mb-4">
        <SectionTitle
          icon={<HomeOutlined className="text-orange-600" />}
          text="Addresses"
        />
        <div className="space-y-2">
          {address
            ?.filter((addr) => addr !== '' && addr !== 'null')
            .map((addr, index) => (
              <div key={index} className="bg-gray-50 p-2 rounded-md text-sm">
                {index + 1}. {addr}
              </div>
            ))}
        </div>
      </div>

      {billingAddress && (
        <>
          <Divider className="my-3" />
          <div className="mb-4">
            <SectionTitle
              icon={<HomeOutlined className="text-red-600" />}
              text="Billing Address"
            />
            <div className="space-y-2">
              {billingAddress
                ?.filter((addr) => addr !== '' && addr !== 'null')
                .map((addr, index) => (
                  <div
                    key={index}
                    className="bg-gray-50 p-2 rounded-md text-sm"
                  >
                    {index + 1}. {addr}
                  </div>
                ))}
            </div>
          </div>
        </>
      )}

      <Divider className="my-3" />

      <div className="mb-4">
        <SectionTitle
          icon={<PhoneOutlined className="text-cyan-600" />}
          text="Phone Numbers"
        />
        <div className="space-y-2">
          {phone_no
            ?.filter((phone) => phone !== '' && phone !== 'null')
            .map((phone, index) => (
              <Tag key={index} color="cyan" className="mr-2 mb-2 py-1">
                {phone}
              </Tag>
            ))}
        </div>
      </div>

      <Divider className="my-3" />

      <ShowTemplateValues
        template={sidebarData?.additionalFields?.templateData}
      />
    </div>
  );
};

export default CustomerSidebar;
