import Input from '../global/components/Input';

function PrefixDefaults({ defaults, setDefaults }) {
  return (
    <div>
      <h3 className="text-gray-subHeading">Inventory Prefix:</h3>
      <div className="mt-2 grid grid-cols-3 gap-x-5 gap-y-5 w-full">
        <div>
          <h5 className="mb-2 text-gray-label">Part Id Prefix:</h5>

          <Input
            value={defaults?.inventoryPrefixDefaults?.partPrefix}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryPrefixDefaults: {
                  ...prev.inventoryPrefixDefaults,
                  partPrefix: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">Product Id Prefix:</h5>

          <Input
            value={defaults?.inventoryPrefixDefaults?.productPrefix}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryPrefixDefaults: {
                  ...prev.inventoryPrefixDefaults,
                  productPrefix: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">SubAssembly Id Prefix:</h5>

          <Input
            value={defaults?.inventoryPrefixDefaults?.subAssemblyPrefix}
            onChange={(e) => {
              // setDefaults((prev) => {
              //   return { ...prev, partDefaults: e.target.value };
              // });
              setDefaults((prev) => ({
                ...prev,
                inventoryPrefixDefaults: {
                  ...prev.inventoryPrefixDefaults,
                  subAssemblyPrefix: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">Vendor Id Prefix:</h5>

          <Input
            value={defaults?.inventoryPrefixDefaults?.vendorPrefix}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryPrefixDefaults: {
                  ...prev.inventoryPrefixDefaults,
                  vendorPrefix: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">Store Id Prefix:</h5>

          <Input
            value={defaults?.inventoryPrefixDefaults?.storePrefix}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryPrefixDefaults: {
                  ...prev.inventoryPrefixDefaults,
                  storePrefix: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
        <div>
          <h5 className="mb-2 text-gray-label">DropDown Id Prefix:</h5>

          <Input
            value={defaults?.inventoryPrefixDefaults?.dropDownPrefix}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                inventoryPrefixDefaults: {
                  ...prev.inventoryPrefixDefaults,
                  dropDownPrefix: e.target.value,
                },
              }));
            }}
            placeholder="Enter Part ID Label"
          />
        </div>
      </div>
    </div>
  );
}

export default PrefixDefaults;
