import { calculate } from '../../../calculateString';
import { DEFAULT_MULTIPLIER_VALUE } from '../../../utils/Constant';
import Input from '../../global/components/Input';
import Modal from '../../global/components/Modal';
import Table from '../../global/components/Table';

export const MultiplierModal = ({
  onCloseModal,
  productionFlow,
  goalsTables,
  setGoalsTables,
}) => {
  const handleMultiplierChange = (e, flow) => {
    const regex = new RegExp(/^[*/]\d+/);
    const pass = regex.test(e.target.value);
    if (pass) {
      document
        .getElementById(`input${flow._id}`)
        ?.classList.remove('!bg-red-200');
    } else {
      document.getElementById(`input${flow._id}`)?.classList.add('!bg-red-200');
    }

    setGoalsTables((prev) =>
      prev.map((item) => {
        if (item.flowId === flow._id) {
          return {
            ...item,
            multiplier: e.target.value || DEFAULT_MULTIPLIER_VALUE,
            tableData: pass
              ? item.tableData.map((elem) => {
                  const isMulti = !!elem?.subProcessData;

                  if (isMulti) {
                    return {
                      ...elem,
                      newBatchSize: calculate(
                        `${elem?.['Batch Size']}${
                          e.target.value || DEFAULT_MULTIPLIER_VALUE
                        }`
                      ),
                      subProcessData: elem?.subProcessData?.map((sPro) => ({
                        ...sPro,
                        newBatchSize: calculate(
                          `${sPro?.['Batch Size']}${
                            e.target.value || DEFAULT_MULTIPLIER_VALUE
                          }`
                        ),
                      })),
                    };
                  }

                  return {
                    ...elem,
                    newBatchSize: calculate(
                      `${elem?.['Batch Size']}${
                        e.target.value || DEFAULT_MULTIPLIER_VALUE
                      }`
                    ),
                  };
                })
              : item.tableData,
          };
        }
        return item;
      })
    );
  };
  return (
    <Modal
      onCloseModal={() => {
        onCloseModal(false);
      }}
      title={'Manage Multiplier'}
      description={'Manage Multiplier for all process'}
      canSubmit={false}
      onSubmit={() => onCloseModal(false)}
    >
      {() => {
        return (
          <>
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Th>#</Table.Th>
                  <Table.Th>Process</Table.Th>
                  <Table.Th>Multiplier</Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                {productionFlow?.processes?.map((flow, fIdx) => (
                  <Table.Row key={flow._id}>
                    <Table.Td>{fIdx + 1}</Table.Td>
                    <Table.Td>{flow.processName}</Table.Td>
                    <Table.Td>
                      <Input
                        id={`input${flow._id}`}
                        value={
                          goalsTables?.[fIdx]?.multiplier ||
                          DEFAULT_MULTIPLIER_VALUE
                        }
                        onChange={(e) => handleMultiplierChange(e, flow)}
                      />
                    </Table.Td>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </>
        );
      }}
    </Modal>
  );
};
