import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import { useContext } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { convertSecsToHrsAndMins } from '../../helperFunction';
import { useLazyCheckWorkerAvailabilityQuery } from '../../slices/jobScheduleApiSlice';
import {
  useLazyCheckLeaveForJobPlannerQuery,
  useLazyIsUserCheckedInQuery,
} from '../../slices/leaveManagementApiSlice';
import { Store } from '../../store/Store';
import MultiSelect from '../global/components/MultiSelect';
import MachineRow, { TIME_REGEXP } from './MachineRow';
import PlannerMultiProcessRow from './PlannerMultiProcessRow';

const ProcessRow = ({
  process = {},
  currBatch = {},
  currGoalsTable = {},
  setGoalsTable,
  machines = [],
  machineSchedules = [],
  setMachineSchedules,
  customMachineTimes,
  allLocations,
  usersForAssigning,
}) => {
  const { id } = useParams();

  const { defaults: { defaultParam = {} } = {} } = useContext(Store);
  const { isMultiProcess, processCategory } = process;

  const [checkWorkerAvailability] = useLazyCheckWorkerAvailabilityQuery();
  const [checkLeaveForJobPlanner] = useLazyCheckLeaveForJobPlannerQuery();
  const [isUserCheckedIn] = useLazyIsUserCheckedInQuery();

  const appDtIds =
    currGoalsTable?.applicableDowntime?.map((i) => i.value) || [];
  const applicableDowntime =
    defaultParam?.plannedDowntimes?.filter((i) => appDtIds?.includes(i._id)) ||
    [];

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;

    const objToSpread = {};

    if (name === 'hour') {
      const check = value.split(':');

      const hasLetter = value.match(/[a-zA-Z]/);
      if (hasLetter) {
        toast.error('Cannot contain letter. Format is HH:MM', {
          toastId: 'letter',
        });
      } else if (+check[1] > 59) {
        toast.error('Minutes cannot be greater than 59', {
          toastId: 'minutes',
        });
      }

      objToSpread.startDate = null;
      objToSpread.stopDate = null;
    }

    setGoalsTable((prev) =>
      prev.map((gt) => {
        if (gt?.flowId === process._id) {
          return {
            ...gt,
            tableData: gt?.tableData?.map((tdt) => {
              if (tdt?.batchNo === currBatch?.batchNo) {
                return {
                  ...tdt,
                  ...objToSpread,
                  [name]: type === 'number' ? +value : value,
                };
              }
              return tdt;
            }),
          };
        }
        return gt;
      })
    );
  };

  const handleStopDateGeneration = (e, currBatch) => {
    const { name, value } = e?.target;

    const objToSpread = {};

    if (name === 'startDate' && currBatch?.hour) {
      const [hh, mm] = currBatch?.hour?.split(':');
      const tempDate = new Date(value);
      objToSpread.stopDate = new Date(
        tempDate.setHours(
          tempDate?.getHours() + +(hh || 0),
          tempDate?.getMinutes() + +(mm || 0),
          0,
          0
        )
      );
    } else if (name === 'stopDate') {
      const diff = value - currBatch?.startDate;

      if (diff <= 1000) {
        return toast.error('Stop date cannot be less or equal to Start date');
      }

      objToSpread.hour = convertSecsToHrsAndMins(diff / 1000) || '';
    }

    setGoalsTable((prev) =>
      prev.map((gt) => {
        if (gt?.flowId === process._id) {
          return {
            ...gt,
            tableData: gt?.tableData?.map((tdt) => {
              if (tdt?.batchNo === currBatch?.batchNo) {
                return {
                  ...tdt,
                  ...objToSpread,
                  [name]: value,
                };
              }
              return tdt;
            }),
          };
        }
        return gt;
      })
    );
  };

  const handleUserAssigning = async (
    e,
    data,
    dataForQcSchedule,
    flowId,
    batchNo
  ) => {
    if (!data) return;

    if (!data?.start || !data?.stop) {
      toast.error('Cannot assign user before assigning time slot');
      return;
    }

    const handleUser = async (value, valsArr) => {
      if (
        defaultParam?.projectDefaults?.enableUserCheckinPlanner &&
        new Date(data?.start).toLocaleDateString() ===
          new Date().toLocaleDateString()
      ) {
        const isCheckedIn = await isUserCheckedIn(
          {
            userId: value,
            date: data?.start,
          },
          false
        ).unwrap();

        if (!isCheckedIn) {
          toast.error('User is not checked in yet', { toastId: 'notcheckin' });
          return;
        } else {
          const session =
            isCheckedIn?.sessions?.[isCheckedIn?.sessions?.length - 1];
          if (session?.checkOut) {
            toast.error(
              'User has already checked out and may not be available',
              {
                toastId: 'checkedout',
              }
            );
            return;
          }
        }
      }

      const isOnLeave = await checkLeaveForJobPlanner(
        {
          userId: value,
          start: data?.start,
          stop: data?.stop,
        },
        false
      ).unwrap();

      if (isOnLeave) {
        toast.error('User is on leave for selected dated', 'onleave');
        return;
      }

      let isLocalConflict = false;

      for (let i = 0; i < machineSchedules?.length; i++) {
        const elem = machineSchedules?.[i];

        if (elem?.assignedUsers?.includes(value)) {
          // check if start date is in between any schedule of local machine schedules
          const checkStartDate = new Date(data?.start);

          const isStartConflict =
            checkStartDate >= new Date(elem.start) &&
            checkStartDate <= new Date(elem.stop);

          // check if stop date is in between any schedule of local machine schedules
          const checkStopDate = new Date(data?.stop);

          const isStopConflict =
            checkStopDate >= new Date(elem.start) &&
            checkStopDate <= new Date(elem.stop);

          // check if any schedule of local machine schedules is in between start and stop
          const isInBetweenConflict =
            checkStartDate <= new Date(elem.start) &&
            checkStopDate >= new Date(elem.stop);

          isLocalConflict =
            !!isStartConflict || !!isStopConflict || !!isInBetweenConflict;
        }

        if (isLocalConflict) {
          break;
        }
      }

      if (isLocalConflict) {
        toast.error('Worker is not available in the selected time slot');
        return;
      }

      const { isConflict, message } = await checkWorkerAvailability(
        {
          query: {
            workerId: value,
            start: data?.start,
            stop: data?.stop,
            ciId: id,
          },
        },
        false
      ).unwrap();

      if (isConflict) {
        toast.error(message, { toastId: message });
        return;
      }

      valsArr.push(value);
    };

    const { value, action, clickedOption } = e.target;

    let valsArr = [];

    if (action === 'single') {
      valsArr = dataForQcSchedule?.assignedUsers || [];
      await handleUser(clickedOption?.value, valsArr);
    } else if (action === 'remove') {
      valsArr = value?.map((i) => i?.value);
    } else if (action === 'selectAll') {
      for (let i = 0; i < value.length; i++) {
        const element = value[i];
        await handleUser(element?.value, valsArr);
      }
    }

    if (dataForQcSchedule) {
      setMachineSchedules((prev) =>
        prev?.map((elem) => {
          if (
            elem?.batchNo === batchNo &&
            elem?.flowId === flowId &&
            elem?.type === 'QC'
          ) {
            return { ...elem, assignedUsers: valsArr };
          }
          return elem;
        })
      );
    } else {
      setMachineSchedules((prev) => [
        ...prev,
        {
          batchNo,
          status: 'notStarted',
          flowId,
          type: 'QC',
          start: data?.start,
          stop: data?.stop,
          assignedUsers: valsArr,
        },
      ]);
    }
  };

  const hasIntervalDt = !!applicableDowntime?.find(
    (dt) => dt.type === 'interval'
  );
  const hasQuantityDt = !!applicableDowntime?.find(
    (dt) => dt.type === 'quantity'
  );

  const isInhouse = processCategory === 'Inhouse';
  const isQc = processCategory === 'QC';

  const filteredUsers = usersForAssigning?.filter((u) =>
    u?.processAccess?.includes(process?.mqtt?._id)
  );

  const dataForQcSchedule = machineSchedules?.find(
    (i) =>
      i?.batchNo === currBatch?.batchNo &&
      i?.flowId === currGoalsTable?.flowId &&
      i?.type === 'QC'
  );

  if (isMultiProcess)
    return (
      <PlannerMultiProcessRow
        process={process}
        currBatch={currBatch}
        applicableDowntime={applicableDowntime}
        hasIntervalDt={hasIntervalDt}
        hasQuantityDt={hasQuantityDt}
        currGoalsTable={currGoalsTable}
        setGoalsTable={setGoalsTable}
        machines={machines}
        machineSchedules={machineSchedules}
        setMachineSchedules={setMachineSchedules}
        customMachineTimes={customMachineTimes}
        allLocations={allLocations}
        usersForAssigning={filteredUsers}
      />
    );

  return (
    <>
      <tr className="group hover:bg-gray-50/50 transition-colors">
        <td className="px-4 py-3 font-medium text-gray-700">
          {process.processName}
        </td>
        <td className="px-4 py-3">{currBatch?.['Batch Size']}</td>
        <td className="px-4 py-3">
          {currBatch?.newBatchSize || currBatch?.['Batch Size']}
        </td>
        <td className="px-4 py-3">
          <span className="text-red-400">NA</span>
        </td>
        <td className="px-4 py-3 text-center">-</td>
        <td
          className={`px-4 py-3 text-center ${
            (currBatch?.hour?.length > 0 &&
              !currBatch?.hour?.match(TIME_REGEXP)) ||
            currBatch?.hour === '0'
              ? '!bg-red-300'
              : '!bg-transparent'
          }`}
        >
          {!isInhouse ? (
            <input
              type="string"
              className="w-full outline outline-1 min-w-[100px] p-1 bg-transparent rounded-sm"
              name="hour"
              value={currBatch?.hour || ''}
              onChange={(e) => handleInputChange(e)}
              // readOnly={!macData?.target}
              pattern={TIME_REGEXP}
            />
          ) : (
            '-'
          )}
        </td>
        <td className="px-4 py-3 text-center">-</td>
        <td className="px-4 py-3">
          <DatePicker
            showTime
            name="startDate"
            format={'DD-MM-YYYY, hh:mm A'}
            disabled={isInhouse}
            value={currBatch?.startDate ? dayjs(currBatch.startDate) : null}
            onChange={
              !isInhouse
                ? (date) =>
                    handleStopDateGeneration(
                      {
                        target: {
                          name: 'startDate',
                          value: date?.toDate() || null,
                        },
                      },
                      currBatch
                    )
                : null
            }
            className="w-full"
          />
        </td>
        <td className="px-4 py-3">
          <DatePicker
            showTime
            name="stopDate"
            format={'DD-MM-YYYY, hh:mm A'}
            disabled={isInhouse || !currBatch?.startDate}
            value={currBatch?.stopDate ? dayjs(currBatch.stopDate) : null}
            onChange={
              !isInhouse
                ? (date) =>
                    handleStopDateGeneration(
                      {
                        target: {
                          name: 'stopDate',
                          value: date?.toDate() || null,
                        },
                      },
                      currBatch
                    )
                : null
            }
            className="w-full"
          />
        </td>
        <td className="px-4 py-3">
          {isQc ? (
            <MultiSelect
              value={dataForQcSchedule?.assignedUsers || []}
              onChange={(e) => {
                handleUserAssigning(
                  e,
                  { start: currBatch?.startDate, stop: currBatch?.stopDate },
                  dataForQcSchedule,
                  currGoalsTable?.flowId,
                  currBatch?.batchNo
                );
              }}
              name="assignedUsers"
              className={'!min-w-[200px]'}
              innerClassname="!rounded-sm border min-h-8 !border-black focus-within:!border-[#2684ff]"
              placeholder="Assign User"
              options={filteredUsers?.map((u) => ({
                label: u?.name,
                value: u?._id,
              }))}
            />
          ) : (
            '-'
          )}
        </td>
        <td className="px-4 py-3">{currBatch?.duration || '-'}</td>
        <td className="px-4 py-3">-</td>
        <td className="px-4 py-3">-</td>
      </tr>
      {isInhouse && (
        <MachineRow
          machines={machines}
          machineSchedules={machineSchedules}
          setMachineSchedules={setMachineSchedules}
          flowId={process._id}
          batchNo={currBatch?.batchNo}
          customMachineTimes={customMachineTimes}
          allLocations={allLocations}
          setGoalsTable={setGoalsTable}
          mqttId={process?.mqtt?._id}
          usersForAssigning={filteredUsers}
        />
      )}
    </>
  );
};

export default ProcessRow;
