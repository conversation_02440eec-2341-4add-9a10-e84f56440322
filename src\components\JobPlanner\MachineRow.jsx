import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import { useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  convertHrsMinStrToVal,
  convertToHrsAndMins,
} from '../../helperFunction';
import {
  useLazyCheckMachineAvailabilityQuery,
  useLazyCheckWorkerAvailabilityQuery,
  useLazyGetAvailableSlotsQuery,
} from '../../slices/jobScheduleApiSlice';
import {
  useLazyCheckLeaveForJobPlannerQuery,
  useLazyIsUserCheckedInQuery,
} from '../../slices/leaveManagementApiSlice';
import { Store } from '../../store/Store';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import { generateMachineStopDate } from './generateStopDate';

const selectStyles = {
  control: (p) => ({
    ...p,
    minHeight: '32px',
    height: '32px',
    paddingLeft: '8px',
    paddingY: '4px',
    background: 'transparent',
    borderColor: 'black',
  }),
  singleValue: (p) => ({ ...p, fontSize: '14px', fontWeight: '500' }),
  valueContainer: (p) => ({
    ...p,
    minHeight: '32px',
    height: '32px',
    padding: '0px',
    fontWeight: '400',
    fontSize: '12px',
  }),
  indicatorsContainer: (p) => ({
    ...p,
    minHeight: '32px',
    height: '32px',
    padding: '0px',
  }),
};

const inputClassnames =
  'w-full outline-[#2684ff] bg-transparent border border-black min-w-24 min-h-8 h-8 px-2 py-1 text-[14px] rounded-sm';

export const TIME_REGEXP = new RegExp(/^\d+:?([0-9]|[0-5][0-9])?$/);

function SuggestedDates({ macData, mac, handleStopDateGeneration }) {
  const [selectDate, setSelectDate] = useState(false);
  const [getAvailableSlots, { data, isFetching }] =
    useLazyGetAvailableSlotsQuery();

  useEffect(() => {
    if (!data?.dates?.length && macData?.start) {
      const exists = data?.dates?.find((date) => date.value === macData.start);
      if (!exists) {
        setSelectDate(true);
      }
    }
  }, [data?.dates, macData?.start]);

  const dataCheck = !!(macData?.speed && macData?.target);

  if (selectDate) {
    return (
      <div className="flex items-center gap-x-3">
        <DatePicker
          showTime
          name="startDate"
          format={'DD-MM-YYYY, hh:mm A'}
          value={macData.start ? dayjs(macData.start) : null}
          onChange={(date) =>
            handleStopDateGeneration(
              { target: { value: date.toDate() } },
              mac,
              macData
            )
          }
          className="min-w-[200px] w-full text-sm border-black rounded-sm"
        />

        <span
          className="hover:cursor-pointer"
          onClick={() => setSelectDate(false)}
        >
          x
        </span>
      </div>
    );
  }

  return (
    <Select
      isLoading={isFetching}
      options={[
        { label: 'Select Date', value: '+' },
        ...(dataCheck ? data?.dates || [] : []),
      ]}
      onChange={(e) => {
        if (e.target.value === '+') {
          setSelectDate(true);
        } else {
          handleStopDateGeneration(e, mac, macData);
        }
      }}
      styles={selectStyles}
      value={macData?.start}
      className={'!min-w-[200px]'}
      menuPosition="fixed"
      onMenuOpen={() => {
        if (dataCheck) {
          getAvailableSlots(
            {
              query: {
                macId: macData.machineId,
                target: macData.target,
                speed: macData.speed,
              },
            },
            false
          );
        } else {
          toast.warn('Target and Speed required to get available slots', {
            toastId: 'noAvailableSlots',
          });
        }
      }}
    />
  );
}

function MachineRow({
  machines,
  machineSchedules,
  setMachineSchedules,
  flowId,
  batchNo,
  customMachineTimes,
  subProcess = null,
  subProIdx = null,
  allLocations,
  setGoalsTable,
  usersForAssigning = [],
}) {
  const { id } = useParams();

  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const [checkMachineAvailability] = useLazyCheckMachineAvailabilityQuery();
  const [checkWorkerAvailability] = useLazyCheckWorkerAvailabilityQuery();
  const [checkLeaveForJobPlanner] = useLazyCheckLeaveForJobPlannerQuery();
  const [isUserCheckedIn] = useLazyIsUserCheckedInQuery();

  const isMultiProcess = !!subProcess;

  const handleInputChange = (e, macId) => {
    const { value, name, type } = e.target;

    setMachineSchedules((prev) => {
      const exists = prev?.find(
        (item) =>
          item.machineId === macId &&
          item?.flowId === flowId &&
          item?.batchNo === batchNo &&
          (isMultiProcess ? item.subProcessIndex === subProIdx : true)
      );

      if (exists) {
        return prev.map((elem) => {
          if (
            elem.machineId === macId &&
            elem?.flowId === flowId &&
            elem?.batchNo === batchNo &&
            (isMultiProcess ? elem.subProcessIndex === subProIdx : true)
          ) {
            let objToSpread = {};

            if (name === 'speed') {
              const calc = elem.target / +value || 0;

              const [hh, mm] = convertToHrsAndMins(calc);

              objToSpread.hour = `${hh}:${mm}`;
            } else if (name === 'hour') {
              const check = value.split(':');

              const hasLetter = value.match(/[a-zA-Z]/);

              if (hasLetter) {
                toast.error('Cannot contain letter. Format is HH:MM', {
                  toastId: 'letter',
                });
              } else if (+check[1] > 59) {
                toast.error('Minutes cannot be greater than 59', {
                  toastId: 'minutes',
                });
              }

              const isMatch = value.match(TIME_REGEXP);
              if (isMatch && value !== '0') {
                const newTime = convertHrsMinStrToVal(value);

                const newSpeed = Math.ceil(elem?.target / newTime);

                objToSpread.speed = newSpeed;
              }
            } else if (name === 'target') {
              const calc = (+value || 0) / +elem?.speed || 0;

              const [hh, mm] = convertToHrsAndMins(calc);

              objToSpread.hour = `${hh}:${mm}`;
            }

            if (
              name !== 'location' &&
              name !== 'buffer' &&
              name !== 'assignedUsers'
            ) {
              objToSpread.start = '';
              objToSpread.stop = '';
            }

            return {
              ...elem,
              ...objToSpread,
              [name]:
                type === 'number'
                  ? +value
                  : name === 'assignedUsers'
                    ? [value]
                    : value,
              isEdited: !!elem._id,
            };
          }
          return elem;
        });
      } else {
        return [
          ...prev,
          {
            machineId: macId,
            flowId,
            batchNo,
            subProcessIndex: subProIdx,
            subProcess: subProcess?.process || null,
            [name]: type === 'number' ? +value : value,
          },
        ];
      }
    });
  };

  const machineOccupiedWarning = () => {
    toast.warn('Machine is occupied', { toastId: 'machineOccupied' });
  };

  const handleStopDateGeneration = async (e, mac, macData) => {
    const { value } = e.target;

    const { startDate, stopDate } = generateMachineStopDate(
      macData,
      value,
      mac,
      customMachineTimes,
      defaultParam?.holidays
    );

    if (!stopDate) {
      toast.error('Error generting stop date please try again later', {
        toastId: 'dateGenerateError',
      });
      return;
    }

    // isSelfEdit is used to skip the time conflict check for same schedule
    let isSelfEdit = false;
    let isConflict = false;
    let index = null;

    // check for conflicts in current jobs
    for (let i = 0; i < machineSchedules?.length; i++) {
      const item = machineSchedules?.[i];

      if (item.machineId === mac._id) {
        if (
          item?.flowId === flowId &&
          item?.batchNo === batchNo &&
          (isMultiProcess ? item.subProcessIndex === subProIdx : true)
        ) {
          index = i;
          isSelfEdit = !!(item?.start && item?.stop);
        }

        if (!isSelfEdit) {
          // check if start date is in between any schedule of local machine schedules
          const checkStartDate = new Date(startDate);

          const isStartConflict =
            checkStartDate >= new Date(item.start) &&
            checkStartDate <= new Date(item.stop);

          // check if stop date is in between any schedule of local machine schedules
          const checkStopDate = new Date(stopDate);

          const isStopConflict =
            checkStopDate >= new Date(item.start) &&
            checkStopDate <= new Date(item.stop);

          // check if any schedule of local machine schedules is in between start and stop
          const isInBetweenConflict =
            checkStartDate <= new Date(item.start) &&
            checkStopDate >= new Date(item.stop);

          isConflict =
            !!isStartConflict || !!isStopConflict || !!isInBetweenConflict;
        }
      }

      if (isConflict) {
        index = null;
        break;
      }

      isSelfEdit = false;
    }

    if (isConflict) {
      machineOccupiedWarning();
      return;
    }

    // check for conflicts in other jobs
    isConflict = await checkMachineAvailability(
      { query: { macId: mac._id, start: startDate, stop: stopDate, ciId: id } },
      false
    ).unwrap();

    if (isConflict) {
      machineOccupiedWarning();
      return;
    }

    let startDateForBatch = startDate;
    let stopDateForBatch = stopDate;

    const otherMacSchedules = machineSchedules.filter((ms) => {
      return (
        ms.batchNo === batchNo &&
        ms.flowId === flowId &&
        ms.machineId !== macData?.machineId &&
        (isMultiProcess ? ms.subProcessIndex === subProIdx : true)
      );
    });

    for (let i = 0; i < otherMacSchedules.length; i++) {
      const curItem = otherMacSchedules[i];

      startDateForBatch = startDateForBatch
        ? new Date(startDateForBatch) > new Date(curItem.start)
          ? curItem.start
          : startDateForBatch
        : curItem.start;

      stopDateForBatch = stopDateForBatch
        ? new Date(stopDateForBatch) < new Date(curItem.stop)
          ? curItem.stop
          : stopDateForBatch
        : curItem.stop;
    }

    setGoalsTable((prev) => {
      return prev.map((item) => {
        if (item.flowId === flowId) {
          if (isMultiProcess) {
            return {
              ...item,
              tableData: item?.tableData?.map((tData) => {
                if (tData.batchNo === batchNo) {
                  return {
                    ...tData,
                    startDate:
                      subProIdx === 0
                        ? startDateForBatch
                        : tData.startDate || '',
                    stopDate:
                      subProIdx === tData?.subProcesses?.length - 1
                        ? stopDateForBatch
                        : tData.stopDate || '',

                    subProcessData: tData?.subProcessData?.map(
                      (sData, sIdx) => {
                        if (sIdx === subProIdx) {
                          return {
                            ...sData,
                            startDate: startDateForBatch,
                            stopDate: stopDateForBatch,
                          };
                        }
                        return sData;
                      }
                    ),
                  };
                }
                return tData;
              }),
            };
          }

          return {
            ...item,
            tableData: item?.tableData?.map((tData) => {
              if (tData.batchNo === batchNo) {
                return {
                  ...tData,
                  startDate: startDateForBatch,
                  stopDate: stopDateForBatch,
                };
              }
              return tData;
            }),
          };
        }
        return item;
      });
    });

    setMachineSchedules((prev) => {
      return prev.map((item, i) => {
        if (i === index) {
          return {
            ...item,
            start: startDate,
            stop: stopDate,
            assignedUsers: [],
            isEdited: !!item?._id,
          };
        }
        return item;
      });
    });
  };

  const handleUserAssigning = async (e, mac, macData) => {
    if (!macData) return;

    if (!macData?.start || !macData?.stop) {
      toast.error('Cannot assign user before assigning time slot');
      return;
    }

    const handleUser = async (value, valsArr) => {
      if (
        defaultParam?.projectDefaults?.enableUserCheckinPlanner &&
        new Date(macData?.start).toLocaleDateString() ===
          new Date().toLocaleDateString()
      ) {
        const isCheckedIn = await isUserCheckedIn(
          {
            userId: value,
            date: macData?.start,
          },
          false
        ).unwrap();

        if (!isCheckedIn) {
          toast.error('User is not checked in yet', { toastId: 'notcheckin' });
          return;
        } else {
          const session =
            isCheckedIn?.sessions?.[isCheckedIn?.sessions?.length - 1];
          if (session?.checkOut) {
            toast.error(
              'User has already checked out and may not be available',
              {
                toastId: 'checkedout',
              }
            );
            return;
          }
        }
      }

      const isOnLeave = await checkLeaveForJobPlanner(
        {
          userId: value,
          start: macData?.start,
          stop: macData?.stop,
        },
        false
      ).unwrap();

      if (isOnLeave) {
        toast.error('User is on leave for selected dated', 'onleave');
        return;
      }

      let isLocalConflict = false;

      for (let i = 0; i < machineSchedules?.length; i++) {
        const elem = machineSchedules?.[i];

        if (elem?.assignedUsers?.includes(value)) {
          // check if start date is in between any schedule of local machine schedules
          const checkStartDate = new Date(macData?.start);

          const isStartConflict =
            checkStartDate >= new Date(elem.start) &&
            checkStartDate <= new Date(elem.stop);

          // check if stop date is in between any schedule of local machine schedules
          const checkStopDate = new Date(macData?.stop);

          const isStopConflict =
            checkStopDate >= new Date(elem.start) &&
            checkStopDate <= new Date(elem.stop);

          // check if any schedule of local machine schedules is in between start and stop
          const isInBetweenConflict =
            checkStartDate <= new Date(elem.start) &&
            checkStopDate >= new Date(elem.stop);

          isLocalConflict =
            !!isStartConflict || !!isStopConflict || !!isInBetweenConflict;
        }

        if (isLocalConflict) {
          break;
        }
      }

      if (isLocalConflict) {
        toast.error('Worker is not available in the selected time slot');
        return;
      }

      const { isConflict, message } = await checkWorkerAvailability(
        {
          query: {
            workerId: value,
            start: macData?.start,
            stop: macData?.stop,
            ciId: id,
          },
        },
        false
      ).unwrap();

      if (isConflict) {
        toast.error(message, { toastId: message });
        return;
      }

      valsArr.push(value);
    };

    const { value, action, clickedOption } = e.target;

    let valsArr = [];

    if (action === 'single') {
      valsArr = macData?.assignedUsers || [];
      await handleUser(clickedOption?.value, valsArr);
    } else if (action === 'remove') {
      valsArr = value?.map((i) => i?.value);
    } else if (action === 'selectAll') {
      for (let i = 0; i < value.length; i++) {
        const element = value[i];
        await handleUser(element?.value, valsArr);
      }
    }

    setMachineSchedules((prev) => {
      return prev?.map((elem) => {
        if (
          elem.machineId === mac?._id &&
          elem?.flowId === flowId &&
          elem?.batchNo === batchNo &&
          (isMultiProcess ? elem.subProcessIndex === subProIdx : true)
        ) {
          return { ...elem, assignedUsers: valsArr };
        }
        return elem;
      });
    });
  };

  const toggleRemove = (elem) => {
    if (elem?._id) {
      setMachineSchedules((prev) => {
        return prev?.map((item) => {
          if (item._id === elem._id) {
            return { ...item, isRemoved: !item?.isRemoved };
          }
          return item;
        });
      });
    } else {
      setMachineSchedules((prev) => {
        return prev?.filter((item) => {
          return !(
            item.machineId === elem?.machineId &&
            item?.flowId === flowId &&
            item?.batchNo === batchNo &&
            (isMultiProcess ? item.subProcessIndex === subProIdx : true)
          );
        });
      });
    }

    const remainingMacSchedules = machineSchedules.filter((ms) => {
      return (
        ms.batchNo === batchNo &&
        ms.flowId === flowId &&
        (elem._id
          ? elem._id === ms._id
            ? elem.isRemoved
            : !ms.isRemoved
          : ms.machineId !== elem?.machineId) &&
        (isMultiProcess ? ms.subProcessIndex === subProIdx : true)
      );
    });

    let startDate = '';
    let stopDate = '';

    for (let i = 0; i < remainingMacSchedules.length; i++) {
      const curItem = remainingMacSchedules[i];

      startDate = startDate
        ? new Date(startDate) > new Date(curItem.start)
          ? curItem.start
          : startDate
        : curItem.start;

      stopDate = stopDate
        ? new Date(stopDate) < new Date(curItem.stop)
          ? curItem.stop
          : stopDate
        : curItem.stop;
    }

    setGoalsTable((prev) => {
      return prev.map((item) => {
        if (item.flowId === flowId) {
          if (isMultiProcess) {
            return {
              ...item,
              tableData: item?.tableData?.map((tData) => {
                if (tData.batchNo === batchNo) {
                  return {
                    ...tData,
                    startDate:
                      subProIdx === 0 ? startDate : tData?.startDate || '',
                    stopDate:
                      subProIdx === tData?.subProcesses.length - 1
                        ? stopDate
                        : tData?.stopDate || '',
                    subProcessData: tData?.subProcessData?.map(
                      (sData, sIdx) => {
                        if (sIdx === subProIdx) {
                          return {
                            ...sData,
                            startDate,
                            stopDate,
                          };
                        }
                        return sData;
                      }
                    ),
                  };
                }
                return tData;
              }),
            };
          }

          return {
            ...item,
            tableData: item?.tableData?.map((tData) => {
              if (tData.batchNo === batchNo) {
                return {
                  ...tData,
                  startDate,
                  stopDate,
                };
              }
              return tData;
            }),
          };
        }
        return item;
      });
    });
  };

  return (
    <>
      {machines?.map((mac) => {
        let macData = {};

        if (isMultiProcess) {
          macData = machineSchedules?.find(
            (sc) =>
              sc.machineId === mac._id &&
              sc.subProcessIndex === subProIdx &&
              sc.flowId === flowId &&
              sc.batchNo === batchNo
          );
        } else {
          macData = machineSchedules?.find(
            (sc) =>
              sc.machineId === mac._id &&
              sc.flowId === flowId &&
              sc.batchNo === batchNo
          );
        }

        return (
          <Table.Row className="!border-l-2 !border-l-yellow-400" key={mac._id}>
            {/* <Table.Td>-</Table.Td> */}
            <Table.Td>{mac?.machineName}</Table.Td>
            <Table.Td>
              {/* Target */}
              <input
                min={0}
                type="number"
                className={`${inputClassnames}`}
                name="target"
                value={macData?.target || ''}
                onChange={(e) => handleInputChange(e, mac._id)}
              />
            </Table.Td>
            <Table.Td>-</Table.Td>
            <Table.Td>-</Table.Td>
            <Table.Td>
              {/* Speed */}
              <input
                min={1}
                list={mac._id}
                type="number"
                className={`${inputClassnames}`}
                name="speed"
                value={macData?.speed || ''}
                onChange={(e) => handleInputChange(e, mac._id)}
                readOnly={!macData?.target}
              />
              <datalist id={mac._id}>
                {mac?.itemsPerHour?.map((i) => (
                  <option key={i.value} value={i.value}>
                    {i.label}
                  </option>
                ))}
              </datalist>
            </Table.Td>
            <Table.Td
              className={
                (macData?.hour?.length > 0 &&
                  !macData?.hour?.match(TIME_REGEXP)) ||
                macData?.hour === '0'
                  ? '!bg-red-300'
                  : '!bg-transparent'
              }
            >
              {/* Hour */}
              <input
                type="string"
                className={`${inputClassnames}`}
                name="hour"
                value={macData?.hour || ''}
                onChange={(e) => handleInputChange(e, mac._id)}
                readOnly={!macData?.target}
                pattern={TIME_REGEXP}
              />
            </Table.Td>

            <Table.Td>
              {/* Buffer */}
              <input
                min={0}
                type="number"
                className={`${inputClassnames}`}
                name="buffer"
                value={macData?.buffer || ''}
                onChange={(e) => handleInputChange(e, mac._id)}
              />
            </Table.Td>
            <Table.Td>
              <SuggestedDates
                macData={macData}
                mac={mac}
                handleStopDateGeneration={handleStopDateGeneration}
              />
            </Table.Td>
            <Table.Td>
              <DatePicker
                showTime
                name="startDate"
                format={'DD-MM-YYYY, hh:mm A'}
                value={macData.stop ? dayjs(macData.stop) : null}
                disabled
                className="min-w-[200px] w-full border-black rounded-sm"
              />

              {/* <input */}
              {/*   type="datetime" */}
              {/*   className={`${inputClassnames} !min-w-48`} */}
              {/*   name="stop" */}
              {/*   value={ */}
              {/*     macData?.stop */}
              {/*       ? generateDateTimeStringProduction(new Date(macData?.stop)) */}
              {/*       : '' */}
              {/*   } */}
              {/*   readOnly */}
              {/* /> */}
            </Table.Td>
            <Table.Td>
              <MultiSelect
                value={macData?.assignedUsers || []}
                onChange={(e) => handleUserAssigning(e, mac, macData)}
                name="assignedUsers"
                styles={selectStyles}
                className={'!min-w-[200px]'}
                innerClassname="!rounded-sm border min-h-8 !border-black focus-within:!border-[#2684ff]"
                placeholder="Assign User"
                options={usersForAssigning?.map((u) => ({
                  label: u?.name,
                  value: u?._id,
                }))}
              />
            </Table.Td>
            <Table.Td>-</Table.Td>
            <Table.Td>
              <Select
                value={macData?.location}
                onChange={(e) => handleInputChange(e, mac._id)}
                name="location"
                styles={selectStyles}
                className={'!min-w-[200px]'}
                placeholder="Select Location"
                menuPosition="fixed"
                options={allLocations.map((option) => ({
                  label: option.locationName,
                  value: option._id,
                }))}
              />
            </Table.Td>
            <Table.Td>
              <button
                disabled={!macData}
                type="button"
                className={` outline outline-1 hover:text-white px-5 py-0.5 rounded ${!macData ? 'pointer-events-none' : ''} ${macData?.isRemoved ? 'hover:bg-blue-400' : 'hover:bg-red-500 '} rounded-sm`}
                onClick={() => toggleRemove(macData)}
              >
                {!macData?.isRemoved ? 'Remove' : 'Restore'}
              </button>
            </Table.Td>
          </Table.Row>
        );
      })}
    </>
  );
}

export default MachineRow;
