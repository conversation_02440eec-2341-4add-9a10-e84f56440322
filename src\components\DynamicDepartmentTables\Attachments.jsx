import { useEffect, useState } from 'react';

import Modal from 'antd/es/modal/Modal';

import DragAndDrop from '../global/components/DragAndDrop';
// import Modal from '../global/components/Modal';
import Button from '../global/components/Button';
import PdfViewer from '../global/components/PdfViewer';
import Table from '../global/components/Table';

import { useLazyGetMediaByIdQuery } from '../../slices/mediaSlice';

import { MdDeleteOutline } from 'react-icons/md';

const Attachments = ({
  setMediaModal,
  mediaModal,
  changeHandler,
  rowData,
  column,
  setDeletedMedia,
  deletedMedia,
  showRecording,
}) => {
  const [initialData, setInitialData] = useState(rowData); //eslint-disable-line
  const [attachments, setAttachments] = useState([]);
  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [selectedMedia, setSelectedMedia] = useState(null);

  const setMedia = async () => {
    let mediaArray = [];
    if (rowData?.[column?.name]?.value) {
      for (let i of rowData?.[column?.name]?.value) {
        if (i?.name === undefined) {
          const media = await getMediaById({ id: i });
          mediaArray?.push(media?.data?.media);
        }
      }
    }
    setAttachments(mediaArray);
  };

  useEffect(() => {
    if (initialData) {
      setAttachments([]);
      setMedia();
    }
  }, [initialData]); //eslint-disable-line

  const handleMediaAdd = (e) => {
    for (let i in e) {
      const fr = new FileReader();
      if (e[i] instanceof File) {
        const name = e[i].name;
        const type = e[i].type;
        fr.onload = (e) => {
          changeHandler(column, { name, type, data: e.target.result }, true);
          setAttachments((prev) => [
            ...prev,
            { name, type, data: e.target.result },
          ]);
        };
        fr.readAsDataURL(e[i]);
      }
    }
  };

  const deleteAttachment = (idx) => {
    let deletedAttachments = deletedMedia?.[column?.name]?.value
      ? deletedMedia?.[column?.name]?.value
      : [];
    for (let i in attachments) {
      if (parseInt(i) === idx) {
        if (attachments?.[i]?._id) {
          deletedAttachments?.push(attachments?.[i]?._id);
        }
        break;
      }
    }
    setDeletedMedia((prev) => ({
      ...prev,
      [column?.name]: {
        type: 'media',
        value: deletedAttachments,
      },
    }));
    setAttachments((prev) => prev?.filter((elem, index) => index !== idx));
  };

  const closeModal = () => {
    setMediaModal(false);
  };

  return (
    <>
      <Modal
        title="Add Media"
        width={700}
        centered
        open={mediaModal}
        onOk={() => {
          handleMediaAdd();
          closeModal();
        }}
        onCancel={closeModal}
      >
        {selectedMedia && (
          <div className="absolute w-[60%] h-[85vh] bg-white top-[50%] left-[50%] -translate-x-[50%] -translate-y-[50%] z-50">
            {selectedMedia?.type === 'application/pdf' && (
              <PdfViewer
                file={selectedMedia?.data}
                name={selectedMedia?.name}
                closeClick={() => {
                  setSelectedMedia(null);
                }}
              />
            )}
            {selectedMedia?.type?.includes('image') && (
              <div className="relative w-full h-full">
                <Button
                  className="!absolute !right-1 !top-1 !z-50 !bg-blue-500 !text-white"
                  onClick={() => {
                    setSelectedMedia(null);
                  }}
                >
                  X
                </Button>
                <img
                  src={selectedMedia?.data}
                  className="block object-contain w-full h-full"
                />
              </div>
            )}
          </div>
        )}
        <>
          <Table>
            <Table.Head>
              <Table.Th>Name</Table.Th>
              <Table.Th></Table.Th>
            </Table.Head>
            <Table.Body>
              {attachments?.map((elem, idx) => {
                return (
                  <Table.Row key={idx}>
                    <Table.Td
                      className="hover:!text-blue-500 cursor-pointer"
                      onClick={() => {
                        setSelectedMedia(elem);
                      }}
                    >
                      {elem?.name}
                    </Table.Td>
                    <Table.Td>
                      <MdDeleteOutline
                        onClick={() => {
                          deleteAttachment(idx);
                        }}
                      />
                    </Table.Td>
                  </Table.Row>
                );
              })}
            </Table.Body>
          </Table>
          {showRecording ? (
            <p>Hello</p>
          ) : (
            <div className={`mt-3`}>
              <DragAndDrop onChange={handleMediaAdd} multiple={true} />
            </div>
          )}
        </>
      </Modal>
    </>
  );
};

export default Attachments;
