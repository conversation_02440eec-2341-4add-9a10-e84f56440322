// import React from 'react';
import { ReactComponent as Arrow } from '../../../assets/svgs/arrow.svg';
import { ReactComponent as Tick } from '../../../assets/svgs/tick.svg';
const tileStyle = {
  active:
    'w-full sm:p-4 p-2  text-blue-700 bg-blue-100 border border-blue-300 rounded-lg ',
  completed:
    'w-full sm:p-4 p-2  text-green-700 border border-green-300 rounded-lg bg-green-50',
  none: 'w-full sm:p-4 p-2  text-gray-900 bg-gray-100 border border-gray-300 rounded-lg',
};
const Steps = ({
  name,
  stepNo,
  type,
  index,
  setActiveIndex,
  item,
  setActiveForm,
  isSequential,
}) => {
  return (
    <div
      onClick={() => {
        if (!isSequential) {
          setActiveIndex(index);
          setActiveForm(item);
        }
      }}
      className="cursor-pointer"
    >
      <div
        className={
          type === 'completed'
            ? tileStyle.completed
            : type === 'active'
              ? tileStyle.active
              : tileStyle.none
        }
      >
        <div className="flex items-center justify-between">
          <p className="font-medium tracking-tight text-nowrap text-sm">{`${stepNo}. ${name}`}</p>

          {type === 'completed' ? (
            <Tick />
          ) : type === 'active' ? (
            <Arrow />
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default Steps;
