import { Button } from 'antd';
import { useEffect, useState } from 'react';
import Voice from '../../assets/images/voice.png';
import DragAndDrop from '../global/components/DragAndDrop';
import MultiSelect from '../global/components/MultiSelect';
import PdfViewer from '../global/components/PdfViewer';
import Table from '../global/components/Table';
import CustomToolTip from '../global/CustomToolTip';
import RecordingPopup from '../Jobs/QcForms/RecordingPopup';

const DepartmentAttachments = ({
  setCapturedImage,
  setOpenCamera,
  isMobile,
  isTablet,
  rowMediaData,
  department,
  setDepartments,
  setRowMediaData,
  handleMediaAdd,
  data,
  mediaType,
}) => {
  const [DropdownDepartment, setDropdownDepartment] = useState([]);
  const [showRecording, setShowRecording] = useState(false);
  const [SelectedFileForPreview, setSelectedFileForPreview] = useState(null);

  useEffect(() => {
    if (!department) return;
    const departmentdata = department?.results?.filter(
      (department) =>
        (department?.isDefault === false ||
          department?.name === 'production') &&
        department?._id !== data?.department?._id
    );
    setDropdownDepartment(departmentdata);
  }, [department, data]);

  return (
    <section>
      {SelectedFileForPreview && (
        <div className="absolute w-[60%] h-[85vh] bg-white top-[50%] left-[50%] -translate-x-[50%] -translate-y-[50%] z-50">
          {SelectedFileForPreview?.type === 'application/pdf' && (
            <PdfViewer
              file={SelectedFileForPreview?.data}
              name={SelectedFileForPreview?.name}
              closeClick={() => {
                setSelectedFileForPreview(null);
              }}
            />
          )}
          {SelectedFileForPreview?.type?.includes('image') && (
            <div className="relative w-full h-full">
              <Button
                className="!absolute !right-1 !top-1 !z-50 !bg-blue-500 !text-white"
                onClick={() => {
                  setSelectedFileForPreview(null);
                }}
              >
                X
              </Button>
              <img
                src={SelectedFileForPreview?.data}
                className="block object-contain w-full h-full"
              />
            </div>
          )}
          {SelectedFileForPreview?.type?.includes('audio') && (
            <div className="relative w-full h-full flex items-center justify-center">
              <Button
                className="!absolute !right-1 !top-1 !z-50 !bg-blue-500 !text-white"
                onClick={() => {
                  setSelectedFileForPreview(null);
                }}
              >
                X
              </Button>
              <audio controls src={SelectedFileForPreview?.data} />
            </div>
          )}
        </div>
      )}

      {showRecording && (
        <RecordingPopup
          isMobile={isMobile}
          isTablet={isTablet}
          isUpload={false}
          isQC={false}
          setShowRecording={setShowRecording}
          // formData={formData}
          setFormdata={setRowMediaData}
        />
      )}

      <div className="">
        <div className="flex justify-end items-center mb-2 h-6">
          {isMobile && (
            <CustomToolTip
              tooltipId={'camera-id'}
              content={'Camera'}
              place="top"
            >
              {' '}
              <div
                className="cursor-pointer  hover:bg-gray-200 p-2 rounded-full"
                onClick={() => {
                  setCapturedImage([]), setOpenCamera((prev) => !prev);
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  className="size-5 text-gray-400 hover:text-blue-400"
                >
                  <path
                    fillRule="evenodd"
                    d="M1 8a2 2 0 0 1 2-2h.93a2 2 0 0 0 1.664-.89l.812-1.22A2 2 0 0 1 8.07 3h3.86a2 2 0 0 1 1.664.89l.812 1.22A2 2 0 0 0 16.07 6H17a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8Zm13.5 3a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM10 14a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </CustomToolTip>
          )}
          <CustomToolTip tooltipId={'mic-id'} content={'Mike'} place="top">
            {' '}
            <img
              src={Voice}
              className="h-6 ml-2 cursor-pointer"
              onClick={() => setShowRecording(true)}
            ></img>
          </CustomToolTip>
        </div>
      </div>

      <Table>
        <Table.Head>
          <Table.Row>
            <Table.Th>Files</Table.Th>
            <Table.Th>Select Departments</Table.Th>
            <Table.Th></Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {rowMediaData?.map((media) => {
            return (
              <Table.Row key={media?.name}>
                <Table.Td
                  className="!text-blue-500 !cursor-pointer"
                  onClick={() => {
                    setSelectedFileForPreview(media);
                  }}
                >
                  {media?.name}
                </Table.Td>
                <Table.Td>
                  <MultiSelect
                    options={DropdownDepartment?.map((department) => ({
                      label: department?.name,
                      value: department?._id,
                    }))}
                    value={media?.departments}
                    onChange={(e) => setDepartments(e, media?.name)}
                  />
                </Table.Td>
                <Table.Td
                  className="!cursor-pointer"
                  onClick={() => {
                    const filter = rowMediaData?.filter((item) => {
                      return item?.name !== media?.name;
                    });
                    setRowMediaData(filter);
                  }}
                >
                  x
                </Table.Td>
              </Table.Row>
            );
          })}
        </Table.Body>
      </Table>
      <div className={`mt-3 ${mediaType === 'audio' ? 'hidden' : ''}`}>
        <DragAndDrop onChange={handleMediaAdd} multiple={true} />
      </div>
    </section>
  );
};

export default DepartmentAttachments;
