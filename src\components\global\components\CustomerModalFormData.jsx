import Modal from '../../../components/global/components/Modal';
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import Input from '../../../components/global/components/Input';
import usePrefixIds from '../../../hooks/usePrefixIds';
import {
  useCreateCustomerMutation,
  useCreateValueMutation,
  useEditCustomerMutation,
} from '../../../slices/customerDataSlice';
import { useGetDropdownsQuery } from '../../../slices/dropdownApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice';
import { customConfirm } from '../../../utils/customConfirm';
import { Label } from '../../v2';
import AddPaymentTerm from '../../v3/InventoryMasters/AddPaymentTerm';
import Select from './Select';

export default function CustomerModalFormData({
  setIsAdded,
  isMobile,
  isTablet,
  setShowAddCustomer,
  getresponse,
  ColumnsValue,
  setColumnsValue,
  title,
  type,
  AllCreatedColums,
  setShowAddvalueModal,
  setResponse,
  getResponse,
  allCustomers,
  isCopy,
  setShowCopyModal,
}) {
  const [SearchParams, setSearchParams] = useSearchParams({
    customer_id: '',
    new_customer_id: '',
  });
  const [PaymentTermOptions, setPaymentTermOptions] = useState([]);
  const [ShowAddNewModal, setShowAddNewModal] = useState(false);
  const [customerId, setCustomerId] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState();
  const navigate = useNavigate();
  const [createCustomer] = useCreateCustomerMutation();
  const [editCustomer] = useEditCustomerMutation();
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [additionalFields, setAdditionalFields] = useState(null);
  const [createValue] = useCreateValueMutation();
  const { data: dropdowns } = useGetDropdownsQuery();
  const [isOn, setIsOn] = useState(false);
  const [CustomerData, setCustomerData] = useState({
    name: '',
    company_name: '',
    unique_id: '',
    phone_no: '',
    address: '',
    gstNumber: [''],
    billingAddress: '',
    deliveryAddress: '',
    paymentTerm: '',
  });
  const { IdGenComp, idCompData } = usePrefixIds({ idFor: 'customerId' });

  useEffect(() => {
    if (isOn) {
      let ele = document.getElementById('topSection');
      ele.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isOn]);

  const handleToggle = () => {
    setIsOn(!isOn);
  };

  useEffect(() => {
    const getCols = async () => {
      const path = '/settings/config/customer';
      getTemplates({ path });
    };
    getCols();
  }, [getTemplates]);

  useEffect(() => {
    if (allCustomers && allCustomers.length > 0 && templatesData) {
      const lastEntry = allCustomers[allCustomers?.length - 1];
      const previouslyUsedTemplate = templatesData?.find(
        (item) => item.name === lastEntry?.additionalFields?.name
      );
      const defaultTemplate = templatesData?.find((item) =>
        item.name.startsWith('Default')
      );
      if (previouslyUsedTemplate) {
        setAdditionalFields(previouslyUsedTemplate);
        setSelectedTemplate(previouslyUsedTemplate);
      } else {
        setAdditionalFields(defaultTemplate);
        setSelectedTemplate(defaultTemplate);
      }
    } else {
      const defaultTemplate = templatesData?.find((item) =>
        item.name.startsWith('Default')
      );
      setAdditionalFields(defaultTemplate);
      setSelectedTemplate(defaultTemplate);
    }
  }, [allCustomers, templatesData]);

  // useEffect(() => {
  //   if (type === 'Edit') {
  //     console.log('CustomerData', CustomerData);
  //     setCustomerData((prev) => {
  //       return {
  //         ...prev,
  //         unique_id: [prev?.unique_id[0]],
  //         customerValues: prev?.unique_id?.slice(1),
  //         phone_no: [prev?.phone_no[0]],
  //         phoneValues: prev?.phone_no?.slice(1),
  //         address: [prev?.address[0]],
  //         addressValues: prev?.address?.slice(1),
  //         billingAddress: [prev?.billingAddress[0]],
  //         deliveryAddress: [prev?.deliveryAddress[0]],
  //         billingAddressValues: prev.billingAddress?.slice(1),
  //         deliveryAddressValues: prev.deliveryAddress?.slice(1),
  //       };
  //     });
  //   }
  // }, [type]); // eslint-disable-line

  //adding the customer
  async function addCustomer() {
    //validation (company_name, gstNumber)
    if (!CustomerData?.company_name.trim()) {
      toast.error('Company Name Is Required');
      return;
    }
    if (CustomerData?.gstNumber?.length > 15) {
      toast.error('Gst should be 15 characters long');
      return;
    }
    for (let keys in CustomerData) {
      if (
        !CustomerData[keys] &&
        keys !== 'deliveryAddress' &&
        keys !== 'billingAddress' &&
        keys !== 'paymentTerm' &&
        keys !== '__v' &&
        keys !== '_id'
      ) {
        // if (!customerId && customerIdFormat) {
        //   Object.keys(customerIdFormat)?.map((item) => {
        //     if (
        //       !(
        //         typeof customerIdFormat[item] === 'string' &&
        //         customerIdFormat[item].startsWith('Increment_')
        //       )
        //     )
        //       toast.error('Cutomer Id  Is Required');
        //   });
        //   return;
        // }
        if (keys === 'company_name') {
          toast.error('Company Name Is Required');
          return;
        }
      }
    }
    let data = {};
    if (isCopy) {
      const filteredArr = Object.keys(CustomerData)?.filter(
        (key) =>
          key !== '_id' &&
          key !== 'createdAt' &&
          key !== 'updatedAt' &&
          key !== 'lastUsed' &&
          key !== '__v'
      );
      const updatedRowData = filteredArr.reduce((acc, key) => {
        acc[key] = CustomerData[key];
        return acc;
      }, {});

      let temp = updatedRowData;
      if (temp.phone_no[0] == '') {
        temp.phone_no = [];
      }

      if (temp.unique_id[0] == '') {
        temp.unique_id = [];
      }

      if (temp.address[0] == '') {
        temp.address = [];
      }
      let billingaddress = [];
      // check temp have an address or not
      if (temp?.billingAddress)
        billingaddress = [...(temp?.billingAddress || [])];
      billingaddress.push(...(temp?.billingAddressValues || []));
      temp.billingAddress = billingaddress;

      let deliveryaddress = [];
      // check temp have an address or not
      if (temp?.deliveryAddress)
        deliveryaddress = [...(temp?.deliveryAddress || [])];
      deliveryaddress.push(...(temp?.deliveryAddressValues || []));
      temp.deliveryAddress = deliveryaddress;

      let unique_id1 = [];

      if (temp?.unique_id) unique_id1 = [...(temp.unique_id || [])];
      unique_id1.push(...(temp?.customerValues || []));
      temp.unique_id = unique_id1;

      let phone_id1 = [];

      if (temp.phone_no) phone_id1 = [...(temp.phone_no || [])];
      phone_id1.push(...(temp?.phoneValues || []));
      temp.phone_no = phone_id1;

      let address1 = [];

      if (temp?.address) address1 = [...(temp.address || [])];
      address1.push(...(temp?.addressValues || []));
      temp.address = address1;

      data = await createCustomer({
        ...temp,
        idData: idCompData?.dataToReturn,
        id: customerId,
      }).unwrap();
    } else {
      let columnArr = Object.keys(ColumnsValue).map((key) => ({
        column_name: ColumnsValue[key].column_name,
        column_value: ColumnsValue[key].column_value,
      }));

      let temp = CustomerData;
      if (temp.phone_no[0] == '') {
        temp.phone_no = [];
      }

      if (temp.unique_id[0] == '') {
        temp.unique_id = [];
      }

      if (temp.address[0] == '') {
        temp.address = [];
      }

      let billingaddress = [];
      // check temp have an address or not
      if (temp?.billingAddress)
        billingaddress = [...(temp?.billingAddress || [])];
      billingaddress.push(...(temp?.billingAddressValues || []));
      temp.billingAddress = billingaddress;

      let deliveryaddress = [];
      // check temp have an address or not
      if (temp?.deliveryAddress)
        deliveryaddress = [...(temp?.deliveryAddress || [])];
      deliveryaddress.push(...(temp?.deliveryAddressValues || []));
      temp.deliveryAddress = deliveryaddress;

      let unique_id1 = [];

      if (temp?.unique_id) unique_id1 = [...(temp.unique_id || [])];
      unique_id1.push(...(temp?.customerValues || []));
      temp.unique_id = unique_id1;

      let phone_id1 = [];

      if (temp.phone_no) phone_id1 = [...(temp.phone_no || [])];
      phone_id1.push(...(temp?.phoneValues || []));
      temp.phone_no = phone_id1;

      let address1 = [];

      if (temp?.address) address1 = [...(temp.address || [])];
      address1.push(...(temp?.addressValues || []));
      temp.address = address1;
      try {
        data = await createCustomer({
          ...temp,
          customColumns: columnArr,
          additionalFields: additionalFields,
          idData: idCompData?.dataToReturn,
        }).unwrap();
      } catch (error) {
        return;
      }
    }
    //reset
    if (data?.customer) {
      setCustomerData({
        name: '',
        company_name: '',
        unique_id: [],
        phone_no: [],
        address: [],
        gstNumber: [''],
        billingAddress: [],
        deliveryAddress: [],
        customerValues: [],
        billingAddressValues: [],
        deliveryAddressValues: [],
        phoneValues: [],
        addressValues: [],
        paymentTerm: '',
      });
      setColumnsValue({});
      setCustomerId('');
      if (setResponse) {
        setResponse(data?.customer);
      }
      !isCopy && setShowAddCustomer(false);
      isCopy && setShowCopyModal(false);

      for (let key in ColumnsValue) {
        setColumnsValue((prev) => {
          return {
            ...prev,
            [key]: {
              ...prev[key],
              customer_id: data?.customer?._id,
            },
          };
        });
      }
      isCopy
        ? toast.success('Customer Copied')
        : toast.success('Customer Created');

      setIsAdded((prev) => !prev);

      getResponse();
      if (SearchParams.get('salesInquiry') === 'true') {
        navigate(
          `/salesordermanagement/salesinquirydashboard?customerId=${data?.customer?._id}`
        );
      }
    }
  }

  // editing the customer
  const EditCustomer = async () => {
    // validation (company_name, gstNumber)

    if (!CustomerData?.company_name.trim()) {
      toast.error('Company Name Is Required');
      return;
    }
    if (CustomerData?.gstNumber?.length > 15) {
      toast.error('Gst should be 15 characters long');
      return;
    }

    const confirm = await customConfirm(
      'Are you sure you want to save these changes?'
    );
    for (let parentKey in ColumnsValue) {
      const founded = AllCreatedColums?.find((each) => {
        return each?.column_name === parentKey;
      });
      if (founded?.mandatory && !ColumnsValue[parentKey].column_value) {
        toast.error(`Column ${parentKey} Is Mandatory`);
        return;
      }
    }
    if (!confirm) return;

    const temp = CustomerData;

    let billingaddress = [];
    // check temp have an address or not
    if (temp?.billingAddress)
      billingaddress = [...(temp?.billingAddress || [])];
    billingaddress.push(...(temp?.billingAddressValues || []));
    temp.billingAddress = billingaddress;

    let deliveryaddress = [];
    // check temp have an address or not
    if (temp?.deliveryAddress)
      deliveryaddress = [...(temp?.deliveryAddress || [])];
    deliveryaddress.push(...(temp?.deliveryAddressValues || []));
    temp.deliveryAddress = deliveryaddress;

    const unique_id1 = [...temp.unique_id];
    unique_id1.push(...(temp?.customerValues || []));
    temp.unique_id = unique_id1;

    const phoneNo1 = [...temp.phone_no];
    phoneNo1.push(...(temp.phoneValues || []));
    temp.phone_no = phoneNo1;

    const address1 = [...temp.address];
    address1.push(...(temp.addressValues || []));
    temp.address = address1;

    const data = await editCustomer({
      id: SearchParams?.get('customer_id'),
      body: temp,
    }).unwrap();
    if (data?.customer) {
      SubmitValue(false);
      setCustomerData({
        name: '',
        company_name: '',
        unique_id: [],
        phone_no: [],
        address: [],
        gstNumber: '',
        billingAddress: [],
        deliveryAddress: [],
        customerValues: [],
        billingAddressValues: [],
        deliveryAddressValues: [],
        phoneValues: [],
        addressValues: [],
        paymentTerm: '',
      });
      toast.success('Customer Edited Successfully');
      getresponse();
      setShowAddvalueModal(false);
    }
  };

  const SubmitValue = async (showalert = true) => {
    const body = {};
    for (let parentKey in ColumnsValue) {
      const founded = AllCreatedColums?.find((each) => {
        return each?.column_name === parentKey;
      });
      if (founded?.mandatory && !ColumnsValue[parentKey].column_value) {
        toast.error(`Column ${parentKey} Is Mandatory`);
        return;
      }
      if (ColumnsValue[parentKey].column_value) {
        body[parentKey] = ColumnsValue[parentKey];
      }
    }
    // if (Object.keys(body).length === 0) {
    //   toast.error("Please Add Atleast One Column's Value");
    //   return;
    // }

    const data = await createValue(body);
    if (data?.value?.length !== 0) {
      if (showalert) {
        toast.success('Columns value Added');
      }
      getresponse();
      setShowAddvalueModal(false);
      for (let parentKey in ColumnsValue) {
        setColumnsValue((prev) => {
          return {
            ...prev,
            [parentKey]: {
              column_value: '',
              customer_id: '',
            },
          };
        });
      }
    }
    setSearchParams(
      (prev) => {
        prev.set('customer_id', '');
        prev.set('new_customer_id', '');
        return prev;
      },
      {
        replace: true,
      }
    );
  };

  useEffect(() => {
    if (!dropdowns) return;
    const PaymentTerm = dropdowns?.dropdowns?.find((dropdown) => {
      return dropdown?.name === 'Payment Term';
    });
    setPaymentTermOptions(PaymentTerm?.values);
  }, [dropdowns]);

  // useEffect(() => {
  //   if (defaults) {
  //     setDefault(defaults?.defaultParam);
  //     let temp = {
  //       ...(defaults?.defaultParam?.prefixIds?.['customerId'] || []),
  //     };
  //     Object.keys(temp).forEach((elem) => {
  //       if (elem.includes('UserEntry')) {
  //         temp[elem] = '';
  //       }
  //     });
  //     setFormat(temp);
  //   }
  // }, [defaults]);

  // const getIdOptions = () => {
  //   let formatArray = [];
  //   Object.keys(format)?.forEach((index) => {
  //     let idFormat = format?.[index];
  //     let transformedIdFormat = '';
  //     for (let fieldType of Object.keys(idFormat).filter(
  //       (item) => item !== 'isUsed'
  //     )) {
  //       let type = fieldType.substring(0, fieldType?.indexOf('_'));
  //       if (type === 'String' || type === 'Increment') {
  //         transformedIdFormat = transformedIdFormat.concat(
  //           idFormat?.[fieldType]
  //         );
  //       } else if (type === 'UserEntry') {
  //         transformedIdFormat = transformedIdFormat.concat('User Entry');
  //       } else {
  //         transformedIdFormat = transformedIdFormat.concat(fieldType);
  //       }
  //     }
  //     formatArray.push({
  //       label: transformedIdFormat,
  //       value: idFormat,
  //     });
  //   });
  //   return formatArray;
  // };
  // const [selectedIndex, setSelectedIndex] = useState(-1);

  return (
    <>
      <Modal
        title={title}
        onCloseModal={() => {
          type === 'Add'
            ? setShowAddCustomer(false)
            : setShowAddvalueModal(false);
          if (typeof setShowCopyModal === 'function') {
            setShowCopyModal(false);
          }
        }}
        onSubmit={() => {
          type === 'Add'
            ? addCustomer()
            : type === 'Edit' && !isCopy
              ? EditCustomer()
              : addCustomer();
        }}
      >
        {() => {
          return (
            <div className="px-4">
              {type === 'Add' ? (
                <section className="add-customer-section">
                  <div className="input-container flex flex-col gap-y-4">
                    <div className="input-wrapper flex flex-col gap-4">
                      <Label htmlFor="name" className="!text-[18px]">
                        Customer ID
                      </Label>{' '}
                      <IdGenComp {...idCompData} />
                    </div>

                    <div className="input-wrapper">
                      <Label htmlFor="companyName" className="!text-[18px]">
                        Company Name
                      </Label>{' '}
                      <span className="text-xl text-red-500 -mt-2 -ml-1">
                        *
                      </span>
                      <Input
                        placeholder="Enter Company Name"
                        name="companyName"
                        type="text"
                        value={CustomerData?.company_name || ''}
                        onChange={(e) => {
                          setCustomerData((prev) => {
                            return {
                              ...prev,
                              company_name: e.target.value,
                            };
                          });
                        }}
                      />
                    </div>

                    <div className="input-wrapper">
                      <Label htmlFor="companyName" className="!text-[18px]">
                        Password
                      </Label>{' '}
                      <Input
                        placeholder="Enter Password"
                        name="companyName"
                        type="password"
                        onChange={(e) => {
                          setCustomerData((prev) => {
                            return {
                              ...prev,
                              password: e.target.value,
                            };
                          });
                        }}
                      />
                    </div>

                    <div className="input-wrapper">
                      <Label htmlFor="email" className="!text-[18px]">
                        Email 1
                      </Label>{' '}
                      <Input
                        placeholder="Enter Email"
                        type="email"
                        name="email"
                        value={
                          CustomerData?.unique_id &&
                          CustomerData?.unique_id?.length > 0
                            ? CustomerData?.unique_id[0]
                            : ''
                        }
                        onChange={(e) => {
                          const updatedUniqueId =
                            CustomerData?.unique_id &&
                            CustomerData?.unique_id.length > 0
                              ? [...CustomerData?.unique_id]
                              : [''];
                          updatedUniqueId[0] = e.target.value;

                          setCustomerData((prev) => ({
                            ...prev,
                            unique_id: updatedUniqueId.filter(
                              (unique_id) =>
                                unique_id !== '' && unique_id !== null
                            ),
                          }));
                        }}
                      />
                    </div>

                    {/* email dynamic fields */}
                    {Array.isArray(CustomerData?.customerValues) &&
                      CustomerData?.customerValues?.map((_, i) => {
                        return (
                          <div className="flex items-center !w-full" key={i}>
                            <div className="flex flex-col w-full">
                              <label className=" font-semibold text-[#667085]">
                                Email{' '}
                                {i + 1 + (CustomerData?.unique_id?.length || 0)}
                                :
                              </label>
                              <Input
                                type="text"
                                className="!w-full"
                                name={`value${i}`}
                                value={
                                  CustomerData.customerValues
                                    ? CustomerData.customerValues[i]
                                    : ''
                                }
                                onChange={(event) => {
                                  const newValues = [
                                    ...CustomerData.customerValues,
                                  ];
                                  newValues[i] = event.target.value;
                                  setCustomerData((prev) => ({
                                    ...prev,
                                    customerValues: newValues,
                                  }));
                                }}
                              />
                            </div>
                            <div
                              onClick={() =>
                                setCustomerData((prev) => ({
                                  ...prev,
                                  customerValues: prev.customerValues.filter(
                                    (_, idx) => idx !== i
                                  ),
                                }))
                              }
                              className="cursor-pointer mt-5 -mr-4 "
                            >
                              {' '}
                              &nbsp;&nbsp;x
                            </div>
                          </div>
                        );
                      })}

                    <div
                      onClick={() =>
                        setCustomerData((prev) => ({
                          ...prev,
                          customerValues: [...(prev.customerValues || []), ''],
                        }))
                      }
                      className=" text-blue-400 text-md hover:text-blue-700 -mt-3 cursor-pointer justify-end"
                    >
                      + Add Email
                    </div>

                    {/* phone  */}

                    <div className="input-wrapper">
                      <Label htmlFor="phone" className="!text-[18px]">
                        Phone 1
                      </Label>{' '}
                      <Input
                        placeholder="Enter Phone"
                        type="number"
                        name="phone"
                        value={
                          CustomerData?.phone_no &&
                          CustomerData?.phone_no?.length > 0 &&
                          CustomerData?.phone_no[0] !== 'null'
                            ? CustomerData?.phone_no[0]
                            : ''
                        }
                        onChange={(e) => {
                          const updatedPhoneNo =
                            CustomerData?.phone_no &&
                            CustomerData?.phone_no.length > 0
                              ? [...CustomerData?.phone_no]
                              : [''];
                          updatedPhoneNo[0] = e.target.value;

                          setCustomerData((prev) => ({
                            ...prev,
                            phone_no: updatedPhoneNo.filter(
                              (phone_no) => phone_no !== '' && phone_no !== null
                            ),
                          }));
                        }}
                      />
                    </div>

                    {/* Dynamic phone fields */}

                    {Array.isArray(CustomerData?.phoneValues) &&
                      CustomerData?.phoneValues?.map((_, i) => {
                        return (
                          <div className="flex items-center !w-full" key={i}>
                            <div className="flex flex-col !w-full">
                              <label className="mb-1 font-semibold text-[#667085]">
                                Phone{' '}
                                {i + 1 + (CustomerData.phone_no?.length || 0)}:
                              </label>
                              <Input
                                type="number"
                                className="!w-full"
                                name={`value${i}`}
                                value={
                                  CustomerData.phoneValues
                                    ? CustomerData.phoneValues[i]
                                    : ''
                                }
                                onChange={(event) => {
                                  const newValues = [
                                    ...CustomerData.phoneValues,
                                  ];
                                  newValues[i] = event.target.value;
                                  setCustomerData((prev) => ({
                                    ...prev,
                                    phoneValues: newValues,
                                  }));
                                }}
                              />
                            </div>
                            <div
                              onClick={() =>
                                setCustomerData((prev) => ({
                                  ...prev,
                                  phoneValues: prev.phoneValues.filter(
                                    (_, idx) => idx !== i
                                  ),
                                }))
                              }
                              className="cursor-pointer mt-5 -mr-4 "
                            >
                              {' '}
                              &nbsp;&nbsp;x
                            </div>
                          </div>
                        );
                      })}

                    <div
                      onClick={() =>
                        setCustomerData((prev) => ({
                          ...prev,
                          phoneValues: [...(prev.phoneValues || []), ''],
                        }))
                      }
                      className=" text-blue-400 text-md hover:text-blue-700 -mt-3 cursor-pointer justify-end"
                    >
                      +Add Phone
                    </div>

                    <div className="flex gap-2 justify-end items-center">
                      <span className="text-lg font-medium text-blue-500 text-center mt-[-7px]">
                        {isOn ? 'Show Less' : 'Add More'}
                      </span>

                      <div
                        className="flex items-center cursor-pointer justify-center"
                        onClick={handleToggle}
                      >
                        <div
                          className={`w-12 h-5 flex items-center rounded-full p-1 duration-300 ease-in-out mt-[-4px] ${
                            isOn ? 'bg-blue-500' : 'bg-gray-300'
                          }`}
                        >
                          <div
                            className={`bg-white w-4 h-4 rounded-full shadow-md transform duration-300 ease-in-out ${
                              isOn ? 'translate-x-7' : ''
                            }`}
                          ></div>
                        </div>
                      </div>
                    </div>

                    {/* add more detail section */}

                    {isOn && (
                      <div
                        id="topSection"
                        className="input-container flex flex-col gap-y-4 mt-5"
                      >
                        <div className="input-wrapper">
                          <label className="mr-3 font-medium">
                            Choose Template:{' '}
                          </label>
                          <div className="font-semibold text-[#515357] w-full">
                            <Select
                              className="w-full"
                              options={templatesData?.map((template) => ({
                                value: template,
                                name: template.name,
                              }))}
                              onChange={(e) => {
                                if (selectedTemplate === e.target.value) {
                                  return;
                                }
                                setAdditionalFields(e.target.value);
                                setSelectedTemplate(e.target.value);
                                if (
                                  additionalFields &&
                                  additionalFields.idIndex ===
                                    e.target.value.idIndex
                                ) {
                                  return;
                                }
                                setCustomerId('');
                              }}
                              value={selectedTemplate}
                            />
                          </div>
                        </div>

                        <div className="input-wrapper">
                          <Label htmlFor="name" className="!text-[18px]">
                            Name
                          </Label>

                          <Input
                            placeholder="Enter Name"
                            name="name"
                            type="text"
                            value={CustomerData?.name || ''}
                            onChange={(e) => {
                              setCustomerData((prev) => {
                                return {
                                  ...prev,
                                  name: e.target.value,
                                };
                              });
                            }}
                          />
                        </div>

                        {/*shipping address  */}

                        <div className="input-wrapper">
                          <Label htmlFor="phone" className="!text-[18px]">
                            Ship To
                          </Label>{' '}
                          <Input
                            placeholder="Enter Address"
                            type="text"
                            name="address"
                            value={
                              CustomerData?.address &&
                              CustomerData?.address?.length > 0
                                ? CustomerData?.address[0]
                                : ''
                            }
                            onChange={(e) => {
                              const updatedAddress =
                                CustomerData?.address &&
                                CustomerData?.address.length > 0
                                  ? [...CustomerData?.address]
                                  : [''];
                              updatedAddress[0] = e.target.value;

                              setCustomerData((prev) => ({
                                ...prev,
                                address: updatedAddress.filter(
                                  (address) =>
                                    address !== '' && address !== null
                                ),
                              }));
                            }}
                          />
                        </div>

                        {Array.isArray(CustomerData.addressValues) &&
                          CustomerData.addressValues?.map((_, i) => {
                            return (
                              <div
                                className="flex items-center !w-full"
                                key={i}
                              >
                                <div className="flex flex-col !w-full">
                                  <label className="mb-1 font-semibold text-[#667085]">
                                    Shiping Address{' '}
                                    {i +
                                      1 +
                                      (CustomerData.address?.length || 0)}
                                    :
                                  </label>
                                  <Input
                                    type="text"
                                    name={`value${i}`}
                                    value={
                                      CustomerData.addressValues
                                        ? CustomerData.addressValues[i]
                                        : ''
                                    }
                                    onChange={(event) => {
                                      const newValues = [
                                        ...CustomerData.addressValues,
                                      ];
                                      newValues[i] = event.target.value;
                                      setCustomerData((prev) => ({
                                        ...prev,
                                        addressValues: newValues,
                                      }));
                                    }}
                                  />
                                </div>
                                <div
                                  onClick={() =>
                                    setCustomerData((prev) => ({
                                      ...prev,
                                      addressValues: prev.addressValues.filter(
                                        (_, idx) => idx !== i
                                      ),
                                    }))
                                  }
                                  className="cursor-pointer mt-5 -mr-4 "
                                >
                                  {' '}
                                  &nbsp;&nbsp;x
                                </div>
                              </div>
                            );
                          })}
                        <div
                          onClick={() =>
                            setCustomerData((prev) => ({
                              ...prev,
                              addressValues: [
                                ...(prev.addressValues || []),
                                '',
                              ],
                            }))
                          }
                          className=" text-blue-400 text-md hover:text-blue-700 -mt-4 cursor-pointer justify-end"
                        >
                          +Add Address
                        </div>

                        <div className="input-wrapper">
                          <Label htmlFor="phone" className="!text-[18px]">
                            Bill To
                          </Label>{' '}
                          <Input
                            placeholder="Enter Address"
                            type="text"
                            name="address"
                            value={
                              CustomerData?.billingAddress &&
                              CustomerData?.billingAddress?.length > 0
                                ? CustomerData?.billingAddress[0]
                                : ''
                            }
                            onChange={(e) => {
                              const updatedAddress =
                                CustomerData?.billingAddress &&
                                CustomerData?.billingAddress.length > 0
                                  ? [...CustomerData?.billingAddress]
                                  : [''];
                              updatedAddress[0] = e.target.value;

                              setCustomerData((prev) => ({
                                ...prev,
                                billingAddress: updatedAddress.filter(
                                  (address) =>
                                    address !== '' && address !== null
                                ),
                              }));
                            }}
                          />
                        </div>
                        {Array.isArray(CustomerData?.billingAddressValues) &&
                          CustomerData?.billingAddressValues?.map((_, i) => {
                            return (
                              <div
                                className="flex items-center !w-full"
                                key={i}
                              >
                                <div className="flex flex-col !w-full">
                                  <label className="mb-1 font-semibold text-[#667085]">
                                    Billing Address{' '}
                                    {i +
                                      1 +
                                      (CustomerData.billingAddress?.length ||
                                        0)}
                                    :
                                  </label>
                                  <Input
                                    type="text"
                                    className="!w-full"
                                    name={`value${i}`}
                                    value={
                                      CustomerData.billingAddressValues
                                        ? CustomerData.billingAddressValues[i]
                                        : ''
                                    }
                                    onChange={(event) => {
                                      const newValues = [
                                        ...CustomerData.billingAddressValues,
                                      ];
                                      newValues[i] = event.target.value;
                                      setCustomerData((prev) => ({
                                        ...prev,
                                        billingAddressValues: newValues,
                                      }));
                                    }}
                                  />
                                </div>
                                <div
                                  onClick={() =>
                                    setCustomerData((prev) => ({
                                      ...prev,
                                      billingAddressValues:
                                        prev.billingAddressValues.filter(
                                          (_, idx) => idx !== i
                                        ),
                                    }))
                                  }
                                  className="cursor-pointer mt-5 -mr-4 "
                                >
                                  {' '}
                                  &nbsp;&nbsp;x
                                </div>
                              </div>
                            );
                          })}
                        <div
                          onClick={() =>
                            setCustomerData((prev) => ({
                              ...prev,
                              billingAddressValues: [
                                ...(prev.billingAddressValues || []),
                                '',
                              ],
                            }))
                          }
                          className=" text-blue-400 hover:text-blue-600 -mt-4 cursor-pointer justify-end"
                        >
                          +Add Address
                        </div>

                        <div className="input-wrapper w-full">
                          {Array.isArray(CustomerData?.gstNumber) &&
                            CustomerData?.gstNumber?.map((elem, i) => {
                              return (
                                <div
                                  className="flex items-center w-full"
                                  key={i}
                                >
                                  <div className="flex flex-col mt-3 w-full">
                                    <label
                                      htmlFor={`gstNumber-${i}`}
                                      className="text-[18px] w-full"
                                    >
                                      GST Number {i + 1}
                                    </label>
                                    <input
                                      id={`gstNumber-${i}`}
                                      placeholder="Enter GST Number"
                                      name="gstNumber"
                                      type="text"
                                      value={CustomerData?.gstNumber?.[i] || ''}
                                      onChange={(event) => {
                                        const newValues = [
                                          ...CustomerData?.gstNumber,
                                        ];
                                        newValues[i] = event.target.value;
                                        setCustomerData((prev) => ({
                                          ...prev,
                                          gstNumber: newValues,
                                        }));
                                      }}
                                      className="w-full border border-gray-300 p-2 rounded"
                                    />
                                  </div>
                                  {i > 0 && (
                                    <div
                                      onClick={() =>
                                        setCustomerData((prev) => ({
                                          ...prev,
                                          gstNumber: prev.gstNumber.filter(
                                            (_, idx) => idx !== i
                                          ),
                                        }))
                                      }
                                      className="cursor-pointer mt-7 -mr-5"
                                    >
                                      &nbsp;&nbsp;x
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          <div
                            onClick={() =>
                              setCustomerData((prev) => ({
                                ...prev,
                                gstNumber: [...(prev.gstNumber || []), ''],
                              }))
                            }
                            className="text-blue-400 text-md hover:text-blue-700 mt-1 cursor-pointer"
                          >
                            + Add GST
                          </div>
                        </div>

                        <div className="input-wrapper">
                          <Label htmlFor="address" className="!text-[18px]">
                            Payment Term
                          </Label>
                          <Select
                            options={[
                              { label: '+ Add New PaymentTerm', value: '+' },
                              ...(PaymentTermOptions?.map((dropdown) => {
                                return {
                                  label: dropdown,
                                  value: dropdown,
                                };
                              }) || []),
                            ]}
                            value={CustomerData?.paymentTerm || ''}
                            onChange={(e) => {
                              if (e.target.value === '+') {
                                setShowAddNewModal(true);
                              } else {
                                setCustomerData((prev) => {
                                  return {
                                    ...prev,
                                    paymentTerm: e.target.value,
                                  };
                                });
                              }
                            }}
                          />
                        </div>

                        {/* Additional fields Customer */}

                        {AllCreatedColums?.map((each) => {
                          return (
                            <div
                              className="input-wrapper"
                              key={each?.column_name}
                            >
                              <Label className="!text-[18px]">
                                {each?.column_name}
                                {each?.mandatory && (
                                  <span className="text-blue-500"> * </span>
                                )}
                              </Label>
                              <Input
                                value={
                                  ColumnsValue[each?.column_name]
                                    ?.column_value || ''
                                }
                                onChange={(e) => {
                                  setColumnsValue((prev) => {
                                    return {
                                      ...prev,
                                      [each?.column_name]: {
                                        column_value: e.target.value,
                                        customer_id:
                                          SearchParams?.get('customer_id'),
                                        column_name: each?.column_name,
                                      },
                                    };
                                  });
                                }}
                              />
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </section>
              ) : (
                <section className="add-customer-section">
                  <div className="input-container flex flex-col gap-y-4">
                    <div className="input-wrapper">
                      {!isCopy ? (
                        <>
                          {' '}
                          <Label htmlFor="name" className="!text-[18px]">
                            Customer ID
                          </Label>{' '}
                          <Input
                            type="text"
                            value={CustomerData?.id}
                            disabled
                          />
                        </>
                      ) : (
                        <>
                          <div className="input-wrapper">
                            <label className="mr-3 !text-md font-medium">
                              Choose Template:{' '}
                            </label>
                            <div className="font-semibold text-[#515357] w-full">
                              <Select
                                className="w-full"
                                options={templatesData?.map((template) => ({
                                  value: template,
                                  name: template.name,
                                }))}
                                onChange={(e) => {
                                  setCustomerId('');
                                  setAdditionalFields(e.target.value);
                                  setSelectedTemplate(e.target.value);
                                }}
                                value={selectedTemplate}
                              />
                            </div>
                          </div>
                          <div className="input-wrapper">
                            <Label htmlFor="name" className="!text-[18px]">
                              Customer ID
                            </Label>{' '}
                            <IdGenComp {...idCompData} />
                          </div>
                        </>
                      )}
                    </div>
                    <div className="input-wrapper">
                      <Label htmlFor="name" className="!text-[18px]">
                        Name
                      </Label>
                      <Input
                        placeholder="Enter Name"
                        name="name"
                        type="text"
                        value={CustomerData?.name || ''}
                        onChange={(e) => {
                          setCustomerData((prev) => {
                            return {
                              ...prev,
                              name: e.target.value,
                            };
                          });
                        }}
                      />
                    </div>
                    <div className="input-wrapper">
                      <Label htmlFor="companyName" className="!text-[18px]">
                        Company Name
                      </Label>{' '}
                      <span className="text-xl text-red-500 -mt-2 -ml-1">
                        *
                      </span>
                      <Input
                        placeholder="Enter Company Name"
                        name="companyName"
                        type="text"
                        value={CustomerData?.company_name || ''}
                        onChange={(e) => {
                          setCustomerData((prev) => {
                            return {
                              ...prev,
                              company_name: e.target.value,
                            };
                          });
                        }}
                      />
                    </div>
                    <div className="input-wrapper">
                      <Label htmlFor="companyName" className="!text-[18px]">
                        Password
                      </Label>{' '}
                      <Input
                        placeholder="Enter Company Name"
                        name="companyName"
                        type="password"
                        onChange={(e) => {
                          setCustomerData((prev) => {
                            return {
                              ...prev,
                              password: e.target.value,
                            };
                          });
                        }}
                      />
                    </div>
                    {CustomerData?.unique_id &&
                    CustomerData?.unique_id?.length === 0 ? (
                      <div className="input-wrapper">
                        <Label htmlFor="email" className="!text-[18px]">
                          Email
                        </Label>{' '}
                        <Input
                          placeholder="Enter Email"
                          type="email"
                          name="email"
                          value={
                            CustomerData?.unique_id &&
                            CustomerData?.unique_id?.length > 0
                              ? CustomerData?.unique_id[0]
                              : ''
                          }
                          onChange={(e) => {
                            const updatedUniqueId =
                              CustomerData?.unique_id &&
                              CustomerData?.unique_id.length > 0
                                ? [...CustomerData?.unique_id]
                                : [''];
                            updatedUniqueId[0] = e.target.value;

                            setCustomerData((prev) => ({
                              ...prev,
                              unique_id: updatedUniqueId.filter(
                                (unique_id) =>
                                  unique_id !== '' && unique_id !== null
                              ),
                            }));
                          }}
                        />
                      </div>
                    ) : (
                      CustomerData?.unique_id?.map((customer, idx) => {
                        return (
                          <div className="input-wrapper" key={idx}>
                            <Label htmlFor="email" className="!text-[18px]">
                              {`Email ${idx + 1}`}
                            </Label>{' '}
                            <Input
                              placeholder={`Enter Email ${idx + 1}`}
                              type="email"
                              name="email"
                              value={customer || ''}
                              onChange={(e) => {
                                const updatedUniqueId =
                                  CustomerData?.unique_id &&
                                  CustomerData?.unique_id.length > 0
                                    ? [...CustomerData?.unique_id]
                                    : [''];
                                updatedUniqueId[idx] = e.target.value;

                                setCustomerData((prev) => ({
                                  ...prev,
                                  unique_id: updatedUniqueId.filter(
                                    (unique_id) =>
                                      unique_id !== '' && unique_id !== null
                                  ),
                                }));
                              }}
                            />
                          </div>
                        );
                      })
                    )}

                    {/* Email dynamic fields edit */}

                    {Array.isArray(CustomerData?.customerValues) &&
                      CustomerData?.customerValues?.map((_, i) => {
                        return (
                          <div className="flex items-center gap-1" key={i}>
                            <div className="flex flex-col !min-w-[150px] w-[1000px]">
                              <label className=" font-semibold text-[#667085]">
                                Email{' '}
                                {i + 1 + (CustomerData.unique_id?.length || 0)}:
                              </label>
                              <Input
                                type="text"
                                className="!min-w-[45rem]"
                                name={`value${i}`}
                                value={
                                  CustomerData.customerValues
                                    ? CustomerData.customerValues[i]
                                    : ''
                                }
                                onChange={(event) => {
                                  const newValues = [
                                    ...CustomerData.customerValues,
                                  ];
                                  newValues[i] = event.target.value;
                                  setCustomerData((prev) => ({
                                    ...prev,
                                    customerValues: newValues,
                                  }));
                                }}
                              />
                            </div>
                            <div
                              onClick={() =>
                                setCustomerData((prev) => ({
                                  ...prev,
                                  customerValues: prev.customerValues.filter(
                                    (_, idx) => idx !== i
                                  ),
                                }))
                              }
                              className="cursor-pointer mt-5 -mr-4 "
                            >
                              {' '}
                              &nbsp;&nbsp;x
                            </div>
                          </div>
                        );
                      })}

                    <div
                      onClick={() =>
                        setCustomerData((prev) => ({
                          ...prev,
                          customerValues: [...(prev.customerValues || []), ''],
                        }))
                      }
                      className=" text-blue-400 hover:text-blue-600 -mt-4 cursor-pointer justify-end"
                    >
                      +Add Email
                    </div>

                    {/* phone  */}
                    {CustomerData?.phone_no &&
                    CustomerData?.phone_no?.length === 0 ? (
                      <div className="input-wrapper">
                        <Label htmlFor="phone" className="!text-[18px]">
                          Phone
                        </Label>{' '}
                        <Input
                          placeholder="Enter Phone"
                          type="text"
                          name="phone"
                          value={
                            CustomerData?.phone_no &&
                            CustomerData?.phone_no?.length > 0 &&
                            CustomerData?.phone_no[0] !== 'null'
                              ? CustomerData?.phone_no[0]
                              : ''
                          }
                          onChange={(e) => {
                            const updatedPhoneNo =
                              CustomerData?.phone_no &&
                              CustomerData?.phone_no.length > 0
                                ? [...CustomerData?.phone_no]
                                : [''];
                            updatedPhoneNo[0] = e.target.value;

                            setCustomerData((prev) => ({
                              ...prev,
                              phone_no: updatedPhoneNo.filter(
                                (phone_no) =>
                                  phone_no !== '' && phone_no !== null
                              ),
                            }));
                          }}
                        />
                      </div>
                    ) : (
                      CustomerData?.phone_no?.map((phone, idx) => {
                        return (
                          <div className="input-wrapper" key={idx}>
                            <Label htmlFor="phone" className="!text-[18px]">
                              {`Phone ${idx + 1}`}
                            </Label>{' '}
                            <Input
                              placeholder={`Enter Phone ${idx + 1}`}
                              type="text"
                              name="phone"
                              value={phone || ''}
                              onChange={(e) => {
                                const updatedPhoneNo =
                                  CustomerData?.phone_no &&
                                  CustomerData?.phone_no.length > 0
                                    ? [...CustomerData?.phone_no]
                                    : [''];
                                updatedPhoneNo[idx] = e.target.value;

                                setCustomerData((prev) => ({
                                  ...prev,
                                  phone_no: updatedPhoneNo.filter(
                                    (phone_no) =>
                                      phone_no !== '' && phone_no !== null
                                  ),
                                }));
                              }}
                            />
                          </div>
                        );
                      })
                    )}

                    {/* Phone dynamic fields edit */}

                    {Array.isArray(CustomerData?.phoneValues) &&
                      CustomerData?.phoneValues?.map((_, i) => {
                        return (
                          <div className="flex items-center gap-1" key={i}>
                            <div className="flex flex-col !min-w-[150px] w-[1000px]">
                              <label className="mb-1 font-semibold text-[#667085]">
                                Phone{' '}
                                {i + 1 + (CustomerData.phone_no?.length || 0)}:
                              </label>
                              <Input
                                type="text"
                                className="!min-w-[45rem]"
                                name={`value${i}`}
                                value={
                                  CustomerData.phoneValues
                                    ? CustomerData.phoneValues[i]
                                    : ''
                                }
                                onChange={(event) => {
                                  const newValues = [
                                    ...CustomerData.phoneValues,
                                  ];
                                  newValues[i] = event.target.value;
                                  setCustomerData((prev) => ({
                                    ...prev,
                                    phoneValues: newValues,
                                  }));
                                }}
                              />
                            </div>
                            <div
                              onClick={() =>
                                setCustomerData((prev) => ({
                                  ...prev,
                                  phoneValues: prev.phoneValues.filter(
                                    (_, idx) => idx !== i
                                  ),
                                }))
                              }
                              className="cursor-pointer mt-5 -mr-4 "
                            >
                              {' '}
                              &nbsp;&nbsp;x
                            </div>
                          </div>
                        );
                      })}
                    <div
                      onClick={() =>
                        setCustomerData((prev) => ({
                          ...prev,
                          phoneValues: [...(prev.phoneValues || []), ''],
                        }))
                      }
                      className=" text-blue-400 hover:text-blue-600 -mt-4 cursor-pointer justify-end"
                    >
                      +Add Phone
                    </div>

                    {/*shipping address  */}

                    {CustomerData?.address &&
                    CustomerData?.address?.length === 0 ? (
                      <div className="input-wrapper">
                        <Label htmlFor="phone" className="!text-[18px]">
                          Ship To
                        </Label>{' '}
                        <Input
                          placeholder="Enter Address"
                          type="text"
                          name="address"
                          value={
                            CustomerData?.address &&
                            CustomerData?.address?.length > 0
                              ? CustomerData?.address[0]
                              : ''
                          }
                          onChange={(e) => {
                            const updatedAddress =
                              CustomerData?.address &&
                              CustomerData?.address.length > 0
                                ? [...CustomerData?.address]
                                : [''];
                            updatedAddress[0] = e.target.value;

                            setCustomerData((prev) => ({
                              ...prev,
                              address: updatedAddress.filter(
                                (address) => address !== '' && address !== null
                              ),
                            }));
                          }}
                        />
                      </div>
                    ) : (
                      CustomerData?.address?.map((address, idx) => {
                        return (
                          <div className="input-wrapper" key={idx}>
                            <Label htmlFor="address" className="!text-[18px]">
                              {`Shiping Address ${idx + 1}`}
                            </Label>{' '}
                            <Input
                              placeholder={`Enter Address ${idx + 1}`}
                              type="text"
                              name="address"
                              value={address || ''}
                              onChange={(e) => {
                                const updatedAddress =
                                  CustomerData?.address &&
                                  CustomerData?.address.length > 0
                                    ? [...CustomerData?.address]
                                    : [''];
                                updatedAddress[idx] = e.target.value;

                                setCustomerData((prev) => ({
                                  ...prev,
                                  address: updatedAddress.filter(
                                    (address) =>
                                      address !== '' && address !== null
                                  ),
                                }));
                              }}
                            />
                          </div>
                        );
                      })
                    )}
                    {/* Address dynamic fields edit */}
                    {Array.isArray(CustomerData?.addressValues) &&
                      CustomerData?.addressValues?.map((_, i) => {
                        return (
                          <div className="flex items-center gap-1" key={i}>
                            <div className="flex flex-col !min-w-[150px] w-[1000px]">
                              <label className="mb-1 font-semibold text-[#667085]">
                                Shiping Address{' '}
                                {i + 1 + (CustomerData.address?.length || 0)}:
                              </label>
                              <Input
                                type="text"
                                // className="border p-2 text-xs rounded pr-14"
                                name={`value${i}`}
                                value={
                                  CustomerData.addressValues
                                    ? CustomerData.addressValues[i]
                                    : ''
                                }
                                onChange={(event) => {
                                  const newValues = [
                                    ...CustomerData.addressValues,
                                  ];
                                  newValues[i] = event.target.value;
                                  setCustomerData((prev) => ({
                                    ...prev,
                                    addressValues: newValues,
                                  }));
                                }}
                              />
                            </div>
                            <div
                              onClick={() =>
                                setCustomerData((prev) => ({
                                  ...prev,
                                  addressValues: prev.addressValues.filter(
                                    (_, idx) => idx !== i
                                  ),
                                }))
                              }
                              className="cursor-pointer mt-5 -mr-4 "
                            >
                              {' '}
                              &nbsp;&nbsp;x
                            </div>
                          </div>
                        );
                      })}
                    <div
                      onClick={() =>
                        setCustomerData((prev) => ({
                          ...prev,
                          addressValues: [...(prev.addressValues || []), ''],
                        }))
                      }
                      className=" text-blue-400 hover:text-blue-600 -mt-4 cursor-pointer justify-end"
                    >
                      +Add Address
                    </div>
                    {/* billing address */}
                    {CustomerData?.billingAddress &&
                    CustomerData?.billingAddress?.length === 0 ? (
                      <div className="input-wrapper">
                        <Label htmlFor="phone" className="!text-[18px]">
                          Bill To
                        </Label>{' '}
                        <Input
                          placeholder="Enter Address"
                          type="text"
                          name="address"
                          value={
                            CustomerData?.billingAddress &&
                            CustomerData?.billingAddress?.length > 0
                              ? CustomerData?.billingAddress[0]
                              : ''
                          }
                          onChange={(e) => {
                            const updatedAddress =
                              CustomerData?.billingAddress &&
                              CustomerData?.billingAddress.length > 0
                                ? [...CustomerData?.billingAddress]
                                : [''];
                            updatedAddress[0] = e.target.value;

                            setCustomerData((prev) => ({
                              ...prev,
                              billingAddress: updatedAddress.filter(
                                (address) => address !== '' && address !== null
                              ),
                            }));
                          }}
                        />
                      </div>
                    ) : (
                      CustomerData?.billingAddress?.map((address, idx) => {
                        return (
                          <div className="input-wrapper" key={idx}>
                            <Label htmlFor="address" className="!text-[18px]">
                              {`Billing Address ${idx + 1}`}
                            </Label>{' '}
                            <Input
                              placeholder={`Enter Address ${idx + 1}`}
                              type="text"
                              name="address"
                              value={address || ''}
                              onChange={(e) => {
                                const updatedAddress =
                                  CustomerData?.billingAddress &&
                                  CustomerData?.billingAddress.length > 0
                                    ? [...CustomerData?.billingAddress]
                                    : [''];
                                updatedAddress[idx] = e.target.value;

                                setCustomerData((prev) => ({
                                  ...prev,
                                  billingAddress: updatedAddress.filter(
                                    (address) =>
                                      address !== '' && address !== null
                                  ),
                                }));
                              }}
                            />
                          </div>
                        );
                      })
                    )}
                    {/* billing address fields edit */}
                    {Array.isArray(CustomerData?.billingAddressValues) &&
                      CustomerData?.billingAddressValues?.map((_, i) => {
                        return (
                          <div className="flex items-center gap-1" key={i}>
                            <div className="flex flex-col !min-w-[150px] w-[1000px]">
                              <label className="mb-1 font-semibold text-[#667085]">
                                Billing Address{' '}
                                {i +
                                  1 +
                                  (CustomerData.billingAddress?.length || 0)}
                                :
                              </label>
                              <Input
                                type="text"
                                name={`value${i}`}
                                value={
                                  CustomerData.billingAddressValues
                                    ? CustomerData.billingAddressValues[i]
                                    : ''
                                }
                                onChange={(event) => {
                                  const newValues = [
                                    ...CustomerData.billingAddressValues,
                                  ];
                                  newValues[i] = event.target.value;
                                  setCustomerData((prev) => ({
                                    ...prev,
                                    billingAddressValues: newValues,
                                  }));
                                }}
                              />
                            </div>
                            <div
                              onClick={() =>
                                setCustomerData((prev) => ({
                                  ...prev,
                                  billingAddressValues:
                                    prev.billingAddressValues.filter(
                                      (_, idx) => idx !== i
                                    ),
                                }))
                              }
                              className="cursor-pointer mt-5 -mr-4 "
                            >
                              {' '}
                              &nbsp;&nbsp;x
                            </div>
                          </div>
                        );
                      })}
                    <div
                      onClick={() =>
                        setCustomerData((prev) => ({
                          ...prev,
                          billingAddressValues: [
                            ...(prev.billingAddressValues || []),
                            '',
                          ],
                        }))
                      }
                      className=" text-blue-400 hover:text-blue-600 -mt-4 cursor-pointer justify-end"
                    >
                      +Add Address
                    </div>

                    <div className="input-wrapper">
                      {Array.isArray(CustomerData?.gstNumber) &&
                        CustomerData?.gstNumber?.map((elem, i) => {
                          return (
                            <>
                              <Label
                                htmlFor="gstNumber"
                                className="!text-[18px]"
                              >
                                GST Number
                              </Label>{' '}
                              <Input
                                placeholder="Enter GST Number"
                                name="gstNumber"
                                type="text"
                                value={
                                  CustomerData?.gstNumber
                                    ? CustomerData?.gstNumber?.[i]
                                    : ''
                                }
                                onChange={(event) => {
                                  const newValues = [
                                    ...CustomerData?.gstNumber,
                                  ];
                                  newValues[i] = event.target.value;
                                  setCustomerData((prev) => ({
                                    ...prev,
                                    gstNumber: newValues,
                                  }));
                                }}
                              />
                            </>
                          );
                        })}
                      <div
                        onClick={() =>
                          setCustomerData((prev) => ({
                            ...prev,
                            gstNumber: [...(prev.gstNumber || []), ''],
                          }))
                        }
                        className=" text-blue-400  hover:text-blue-600 mt-1 cursor-pointer justify-end"
                      >
                        +Add GST
                      </div>
                    </div>

                    <div className="input-wrapper">
                      <Label htmlFor="address" className="!text-[18px]">
                        Payment Term
                      </Label>
                      <Select
                        options={[
                          { label: '+ Add New PaymentTerm', value: '+' },
                          ...(PaymentTermOptions?.map((dropdown) => {
                            return {
                              label: dropdown,
                              value: dropdown,
                            };
                          }) || []),
                        ]}
                        value={CustomerData?.paymentTerm || ''}
                        onChange={(e) => {
                          if (e.target.value === '+') {
                            setShowAddNewModal(true);
                          } else {
                            setCustomerData((prev) => {
                              return {
                                ...prev,
                                paymentTerm: e.target.value,
                              };
                            });
                          }
                        }}
                      />
                    </div>

                    {AllCreatedColums?.map((each) => {
                      return (
                        <div className="input-wrapper" key={each?.column_name}>
                          <Label className="!text-[18px]">
                            {each?.column_name}
                            {each?.mandatory && (
                              <span className="text-blue-500"> * </span>
                            )}
                          </Label>
                          <Input
                            value={
                              ColumnsValue[each?.column_name]?.column_value ||
                              ''
                            }
                            onChange={(e) => {
                              setColumnsValue((prev) => {
                                return {
                                  ...prev,
                                  [each?.column_name]: {
                                    column_value: e.target.value,
                                    customer_id:
                                      SearchParams?.get('customer_id'),
                                    column_name: each?.column_name,
                                  },
                                };
                              });
                            }}
                          />
                        </div>
                      );
                    })}
                  </div>
                </section>
              )}
            </div>
          );
        }}
      </Modal>
      {ShowAddNewModal && (
        <AddPaymentTerm
          isTablet={isTablet}
          isMobile={isMobile}
          setShowModal={setShowAddNewModal}
          dropdowns={dropdowns}
        />
      )}
    </>
  );
}
