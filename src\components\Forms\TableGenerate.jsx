import Input from '../global/components/Input';
import Select from '../global/components/Select';
const TableGenerate = ({ tableOptions, setTableOptions }) => {
  const handleColumnNameChange = (value, index, type) => {
    setTableOptions((prev) => {
      const updatedTableOptions = prev || {};

      if (!updatedTableOptions.column) {
        updatedTableOptions.column =
          Array.from({ length: tableOptions?.columns }, () => ({
            columnName: '',
            columnType: '',
          })) || [];
      } else {
        updatedTableOptions.column = [...updatedTableOptions.column];
      }
      if (!updatedTableOptions.column[index]) {
        updatedTableOptions.column[index] = {
          columnName: '',
          columnType: '',
        };
      }
      if (type === 'name') {
        updatedTableOptions.column[index] = {
          ...updatedTableOptions.column[index],
          columnName: value,
        };
      } else if (type === 'type') {
        if (value === 'Min-Max') {
          updatedTableOptions.column[index] = {
            ...updatedTableOptions.column[index],
            columnType: value,
            nestedColumns: ['Min', 'Max'],
          };
        } else {
          updatedTableOptions.column[index] = {
            ...updatedTableOptions.column[index],
            columnType: value,
            nestedColumns: [],
          };
        }
      }

      return {
        ...prev,
        ...updatedTableOptions,
      };
    });
  };

  return (
    <div className="w-full  mt-5 px-3">
      {/* START-  Input of no of column and rows */}
      <div className=" grid grid-cols-2 gap-2 w-full">
        <span className="flex items-center gap-1 w-full">
          <label className="text-sm">Number of columns: </label>
          <Input
            value={tableOptions?.columns || 0}
            type="number"
            onChange={(e) => {
              setTableOptions((prev) => ({
                ...prev,
                columns: +e.target.value,
              }));
            }}
          />
        </span>
        <span className="flex items-center gap-1">
          <label className=" text-sm">Number of rows: </label>
          <Input
            value={tableOptions?.rows || 0}
            onChange={(e) => {
              setTableOptions((prev) => ({
                ...prev,
                rows: +e.target.value,
              }));
            }}
            type="number"
          />
        </span>
      </div>
      {/* START-  Input of no of column and rows */}

      <hr className="my-2" />

      <div className="overflow-x-scroll pb-3 mb-3">
        {Array.from({
          length: tableOptions?.columns,
        }).map((_, index) => (
          <div key={index}>
            <label className="block mb-1 text-lg text-gray-800 font-medium">
              Column {index + 1}:
            </label>
            <div className="w-full flex pb-3">
              <span className="w-1/2 flex items-center gap-1">
                <label className="block mb-1 text-sm text-gray-500 font-medium">
                  Column Name:{' '}
                </label>
                <Input
                  onChange={(e) => {
                    const value = e.target.value;
                    handleColumnNameChange(value, index, 'name');
                  }}
                  value={tableOptions?.column?.[index]?.columnName || ''}
                  className={`!w-fit`}
                />
              </span>
              <span className="w-1/2 flex items-center gap-1 justify-start">
                <label className="block mb-1 text-sm text-gray-500 font-medium">
                  Column Type:{' '}
                </label>
                <Select
                  onChange={(e) => {
                    const value = e.target.value;
                    handleColumnNameChange(value, index, 'type');
                  }}
                  menuPlacement={
                    tableOptions?.columns / 2 <= index ? 'top' : 'bottom'
                  }
                  options={[
                    { value: 'date', label: 'Date' },
                    { value: 'number', label: 'Number' },
                    { value: 'string', label: 'String' },
                    { value: 'Min-Max', label: 'Min-Max' },
                  ]}
                  value={tableOptions?.column?.[index]?.columnType || ''}
                  className={`!w-1/2`}
                />
              </span>
            </div>

            <hr />
          </div>
        ))}
      </div>

      <div className="overflow-x-scroll pb-16 mb-3">
        {Array.from({
          length: tableOptions?.rows,
        }).map((_, index) => (
          <div key={index}>
            <label className="block mb-1 text-lg text-gray-800 font-medium">
              Row {index + 1}:
            </label>
            <div className="w-full flex pb-3">
              <span className="w-full flex items-center gap-1 justify-between">
                <label className="block mb-1 text-sm text-gray-500 font-medium">
                  Row Name:{' '}
                </label>
                <Input
                  type="text"
                  onChange={(e) => {
                    const value = e.target.value;
                    setTableOptions((prev) => {
                      let updatedTableOptions = prev || {};

                      if (!updatedTableOptions.row) {
                        updatedTableOptions.row =
                          Array.from(
                            { length: tableOptions?.rows },
                            () => ''
                          ) || [];
                      } else {
                        updatedTableOptions.row = [...updatedTableOptions.row];
                      }

                      updatedTableOptions.row[index] = value;
                      return {
                        ...prev,
                        ...updatedTableOptions,
                      };
                    });
                  }}
                  value={tableOptions?.row?.[index] || ''}
                  className={`!w-fit`}
                />
              </span>
              {/* <span className="w-1/2 flex items-center gap-1 justify-start">
                <label className="block mb-1 text-sm text-gray-500 font-medium">
                  Column Type:{' '}
                </label>
                <Select
                  onChange={(e) => {
                    const value = e.target.value;
                    handleColumnNameChange(value, index, 'type');
                  }}
                  menuPlacement={
                    tableOptions?.columns / 2 <= index ? 'top' : 'bottom'
                  }
                  options={[
                    { value: 'date', label: 'Date' },
                    { value: 'number', label: 'Number' },
                    { value: 'string', label: 'String' },
                  ]}
                  value={tableOptions?.column?.[index]?.columnType || ''}
                  className={`!w-1/2`}
                />
              </span> */}
            </div>

            <hr />
          </div>
        ))}
      </div>
    </div>
  );
};

export default TableGenerate;
