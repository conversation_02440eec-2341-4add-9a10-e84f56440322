import TruncateString from '../../global/TruncateString';
const OperatorAndMachineDetails = ({ data }) => {
  const operator = data.operator?.[data.operator.length - 1]?.user;

  return (
    <div className="flex border-b border-slate-300 px-3 w-full justify-between">
      <span className="font-medium text-[14px] md:text-lg mb-1 pt-1">
        {`${data?.machine?.machineName} (${data?.machine?.machineId})`}{' '}
      </span>
      {/* <span className="font-medium text-lg pt-1 ">Duration </span>
      <ProgressBar
        completed="78"
        height="8px"
        width="200px"
        margin="13px"
        customLabel=" "
        bgColor="green"
      ></ProgressBar> */}
      {/* <span className="font-medium text-lg pt-1 pr-32">18m</span> */}
      <span className="font-medium text-[14px] md:text-lg mb-1 pt-1 ">
        {' '}
        <TruncateString length={30}>
          {`${operator ? `${operator?.name} (${operator?.employeeId || ''})` : ''}`}
        </TruncateString>
      </span>
    </div>
  );
};

export default OperatorAndMachineDetails;
