import { Tooltip as AntTooltip } from 'antd';

const Tooltip = ({
  children,
  text,
  isText = true,
  className,
  minWidth,
  maxWidth,
  disabled,
}) => {
  if (disabled) {
    return children;
  }

  return (
    <AntTooltip
      title={isText ? text : <div dangerouslySetInnerHTML={{ __html: text }} />}
      overlayClassName={className}
      // overlayStyle={{
      //   minWidth: minWidth ? minWidth : '40ch',
      //   maxWidth: maxWidth ? maxWidth : '470ch',
      // }}
      minWidth={minWidth ? minWidth : '40ch'}
      maxWidth={maxWidth ? maxWidth : '470ch'}
      autoAdjustOverflow={true}
    >
      {children}
    </AntTooltip>
  );
};

export default Tooltip;
