import { ChevronDown } from 'lucide-react';
import moment from 'moment';
import { useState } from 'react';
import ProcessRow from './ProcessRow';

const BatchCard = ({
  batchData = [],
  productionFlow = {},
  goalsTable,
  setGoalsTable,
  machineSchedules = [],
  setMachineSchedules,
  allMachines = [],
  customMachineTimes,
  allLocations,
  usersForAssigning,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { processes } = productionFlow;

  const firstStartDate = batchData?.[0]?.startDate || '';
  const lastStopDate = batchData?.[batchData?.length - 1]?.stopDate || '';

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-200 hover:shadow-md">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center space-x-4">
          <span className="text-blue-600 font-semibold">
            {batchData?.[0]?.batchName}
          </span>
          <span className="text-gray-300">|</span>
          <div className="flex items-center space-x-2 text-gray-600">
            <span>
              {firstStartDate
                ? moment(firstStartDate).format('DD-MM-YYYY HH:mm')
                : '-'}
            </span>
            {firstStartDate && lastStopDate && (
              <ChevronDown className="h-4 w-4 rotate-[-90deg] text-gray-400" />
            )}
            <span>
              {lastStopDate
                ? moment(lastStopDate).format('DD-MM-YYYY HH:mm')
                : '-'}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500">
            {processes.length}{' '}
            {processes.length === 1 ? 'process' : 'processes'}
          </span>
          <div
            className={`transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
          >
            <ChevronDown className="h-5 w-5 text-gray-400" />
          </div>
        </div>
      </button>

      <div
        className={`
          transition-all duration-300 ease-in-out
          ${isExpanded ? 'opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}
        `}
      >
        <div className="overflow-x-auto">
          <table className="w-full min-w-[1200px] border-collapse">
            <thead>
              <tr className="bg-gray-50 text-sm font-medium text-gray-500">
                <th className="px-4 py-3 text-left w-[180px] whitespace-nowrap">
                  Process/Name
                </th>
                <th className="px-4 py-3 text-left w-[140px]">
                  Batch Size/Target (Qty)
                </th>
                <th className="px-4 py-3 text-left w-[140px]">
                  New Batch Size
                </th>
                <th className="px-4 py-3 text-left w-[120px]">Sub Process</th>
                <th className="px-4 py-3 text-left w-[100px]">
                  Speed (Items/hour)
                </th>
                <th className="px-4 py-3 text-left w-[120px]">Time (HH:MM)</th>
                <th className="px-4 py-3 text-left w-[100px]">Buffer (Mins)</th>
                <th className="px-4 py-3 text-left w-[180px]">Start Date</th>
                <th className="px-4 py-3 text-left w-[180px]">Stop Date</th>
                <th className="px-4 py-3 text-left w-[140px]">User</th>
                <th className="px-4 py-3 text-left w-[100px]">Duration</th>
                <th className="px-4 py-3 text-left w-[140px]">Location</th>
                <th className="px-4 py-3 text-left w-[100px]">Action</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {processes?.map((process, pIdx) => {
                const gt = goalsTable?.find((gt) => gt.flowId === process._id);

                return (
                  <ProcessRow
                    key={process._id}
                    process={process}
                    currBatch={batchData?.[pIdx]}
                    currGoalsTable={gt}
                    machines={allMachines.filter((mac) =>
                      gt.selectedMachines.includes(mac._id)
                    )}
                    setGoalsTable={setGoalsTable}
                    machineSchedules={machineSchedules}
                    setMachineSchedules={setMachineSchedules}
                    customMachineTimes={customMachineTimes}
                    allLocations={allLocations}
                    usersForAssigning={usersForAssigning}
                  />
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default BatchCard;
