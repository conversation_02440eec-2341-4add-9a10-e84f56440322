import { useEffect, useState } from 'react';

import { FaCube } from 'react-icons/fa6';
import { GrMoney } from 'react-icons/gr';
import { IoWalletOutline } from 'react-icons/io5';
import { TbCalendarTime, TbReport } from 'react-icons/tb';
import { useParams } from 'react-router-dom';
import { useHrmsContext } from './utils/HrmsContext';

import { useGetPayrollByIdQuery } from '../../slices/HRMS/payrollApiSlice';

import { Steps } from 'antd';

import AuxillaryPayments from './PayrollComponents/AuxillaryPayments';
import LeavesManagement from './PayrollComponents/LeavesManagement';
import Reimbursements from './PayrollComponents/Reimbursements';
import Review from './PayrollComponents/Review';
import SalaryComponent from './PayrollComponents/SalaryComponents';

const PayrollSteps = () => {
  const { id } = useParams();
  const { data: payroll } = useGetPayrollByIdQuery({ id });

  const [formData, setFormData] = useState([]);
  const [current, setCurrent] = useState(0);

  const { setPayrollRunData, setUserDataDuringPayrollRun } = useHrmsContext();

  const steps = [
    {
      title: 'Leave Reviews',
      // status: 'finish',
      icon: <TbCalendarTime className="text-[1.8rem]" />,
    },
    {
      title: 'Auxillary Payments',
      // status: 'finish',
      icon: <GrMoney className="text-[1.8rem]" />,
    },
    {
      title: 'Reimbursements',
      // status: 'process',
      icon: <IoWalletOutline className="text-[1.8rem]" />,
    },
    {
      title: 'Salary Component',
      // status: 'wait',
      icon: <FaCube className="text-[1.8rem]" />,
    },
    {
      title: 'Review',
      // status: 'wait',
      icon: <TbReport className="text-[1.8rem]" />,
    },
  ];

  useEffect(() => {
    if (payroll?._id !== undefined) {
      setFormData(payroll?.users);
      setPayrollRunData(payroll?.users);
      for (let i of payroll?.users) {
        setUserDataDuringPayrollRun((prev) => ({
          ...prev,
          [i?._id]: i,
        }));
      }
    }
  }, [payroll]); //eslint-disable-line

  return (
    <div>
      <div className="bg-white rounded-[10px] px-6 py-4 m-2">
        <Steps items={steps} current={current} />
      </div>
      <div className="bg-white rounded-[10px] px-6 py-4 m-2">
        {current === 0 && (
          <LeavesManagement
            payroll={payroll}
            formData={formData}
            setFormData={setFormData}
            setCurrent={setCurrent}
            current={current}
          />
        )}
        {current === 1 && (
          <AuxillaryPayments
            payroll={payroll}
            formData={formData}
            setFormData={setFormData}
            setCurrent={setCurrent}
            current={current}
          />
        )}
        {current === 2 && (
          <Reimbursements
            payroll={payroll}
            formData={formData}
            setFormData={setFormData}
            setCurrent={setCurrent}
            current={current}
          />
        )}
        {current === 3 && (
          <SalaryComponent payroll={payroll} setCurrent={setCurrent} />
        )}
        {current === 4 && <Review payroll={payroll} setCurrent={setCurrent} />}
      </div>
    </div>
  );
};

export default PayrollSteps;
