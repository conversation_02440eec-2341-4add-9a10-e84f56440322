import { useEffect, useState } from 'react';
import {
  <PERSON>,
  AreaChart,
  Bar,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  Line,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON><PERSON>,
  YAxis,
} from 'recharts';
import Card from '../../components/global/components/Card';
import Table from '../../components/global/components/Table';
import { getRandomNumber } from '../../helperFunction';

const colorArray = [
  '#8884d8',
  '#82ca9d',
  '#FF6633',
  '#FFFF99',
  '#00B3E6',
  '#E6B333',
  '#FF33FF',
  '#3366E6',
  '#999966',
  '#99FF99',
  '#B34D4D',
  '#80B300',
  '#809900',
  '#E6B3B3',
  '#6680B3',
  '#66991A',
  '#FF99E6',
  '#CCFF1A',
  '#FF1A66',
  '#E6331A',
  '#33FFCC',
  '#66994D',
  '#B366CC',
  '#4D8000',
  '#B33300',
  '#CC80CC',
  '#66664D',
  '#FFB399',
  '#991AFF',
  '#E666FF',
  '#4DB3FF',
  '#1AB399',
  '#E666B3',
  '#33991A',
  '#CC9999',
  '#B3B31A',
  '#00E680',
  '#4D8066',
  '#809980',
  '#E6FF80',
  '#1AFF33',
  '#999933',
  '#FF3380',
  '#CCCC00',
  '#66E64D',
  '#4D80CC',
  '#9900B3',
  '#E64D66',
  '#4DB380',
  '#FF4D4D',
  '#99E6E6',
  '#6666FF',
];

const CustomerDashboardPageAnalytics = ({ rows }) => {
  const [data, setData] = useState([]);
  const [data2, setData2] = useState([]);
  const [pieChartState, setPieChartState] = useState(0);
  const [pieChartPercent] = useState([getRandomNumber(6), getRandomNumber(10)]);

  useEffect(() => {
    const tempData = [];
    const tempData2 = [];
    [...Array(8)].forEach((_, idx) => {
      const obj = {};
      const obj2 = {};
      rows?.forEach((row) => {
        obj[row.machineName] = getRandomNumber(10000);
        obj2[row.machineName] = getRandomNumber(rows?.length);
      });
      tempData.unshift({
        name: new Date(
          new Date().setDate(new Date().getDate() - idx)
        ).toDateString(),
        ...obj,
      });
      tempData2.unshift({
        name: new Date(
          new Date().setDate(new Date().getDate() - idx)
        ).toDateString(),
        ...obj2,
      });
    });

    setData(tempData);
    setData2(tempData2);
  }, [rows]);

  return (
    <section>
      <div className="grid md:grid-cols-2 items-center gap-4 mt-4">
        <Card className="flex-1 h-full">
          <ResponsiveContainer height="100%" width="100%">
            <BarChart data={data} className="flex-1 w-full h-full">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {rows?.map((row, rIdx) => (
                <Bar
                  key={row?.machineName}
                  dataKey={row?.machineName}
                  fill={colorArray?.[rIdx]}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </Card>
        <div className="grid md:grid-cols-2 gap-2 w-full h-full flex-1">
          <Card className="flex flex-1 h-full items-center gap-2">
            <div className="w-full">
              <p className="text-slate-400">Active Hours</p>
              <h1>₹4,345</h1>
              <p className="mt-4">
                <span className="text-green-400">12%</span> increase from last
                month
              </p>
            </div>
            <div className="w-full h-full">
              <ResponsiveContainer height="100%" width="100%">
                <LineChart
                  data={data}
                  margin={{ top: 30, right: 20, left: 0, bottom: 0 }}
                >
                  <Line
                    type="monotone"
                    dataKey={
                      rows?.[getRandomNumber(rows?.length - 1)]?.machineName
                    }
                    stroke="#8884d8"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
          <Card className="flex flex-1 h-full items-center gap-2">
            <div className="w-full">
              <p className="text-slate-400">Total Active Hours</p>
              <h1>₹4,345</h1>
              <p className="mt-4">
                <span className="text-green-400">12%</span> increase from last
                month
              </p>
            </div>
            <div className="w-full h-full">
              <ResponsiveContainer height="100%" width="100%">
                <LineChart
                  data={data}
                  margin={{ top: 30, right: 20, left: 0, bottom: 0 }}
                >
                  <Line
                    type="monotone"
                    dataKey={
                      rows?.[getRandomNumber(rows?.length - 1)]?.machineName
                    }
                    stroke="#8884d8"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
          <Card className="flex flex-1 h-full items-center gap-2">
            <div className="w-full">
              <p className="text-slate-400">Energy Cost</p>
              <h1>₹4,345</h1>
              <p className="mt-4">
                <span className="text-green-400">12%</span> increase from last
                month
              </p>
            </div>
            <div className="w-full h-full">
              <ResponsiveContainer height="100%" width="100%">
                <LineChart
                  data={data}
                  margin={{ top: 30, right: 20, left: 0, bottom: 0 }}
                >
                  <Line
                    type="monotone"
                    dataKey={
                      rows?.[getRandomNumber(rows?.length - 1)]?.machineName
                    }
                    stroke="#8884d8"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
          <Card className="flex flex-1 h-full items-center gap-2">
            <div className="w-full">
              <p className="text-slate-400">Total Energy Cost</p>
              <h1>₹4,345</h1>
              <p className="mt-4">
                <span className="text-green-400">12%</span> increase from last
                month
              </p>
            </div>
            <div className="w-full h-full">
              <ResponsiveContainer height="100%" width="100%">
                <LineChart
                  data={data}
                  margin={{ top: 30, right: 20, left: 0, bottom: 0 }}
                >
                  <Line
                    type="monotone"
                    dataKey={
                      rows?.[getRandomNumber(rows?.length - 1)]?.machineName
                    }
                    stroke="#8884d8"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </div>
      </div>
      <div className="mt-6 grid md:grid-cols-2 items-center gap-4">
        <Card className="flex-1 w-full">
          <h2>Total Machine Active</h2>
          <ResponsiveContainer height={270} width="100%">
            <AreaChart
              data={data2}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis dataKey="name" />
              <YAxis />
              <CartesianGrid strokeDasharray="3 3" />
              <Tooltip />
              <Area
                type="monotone"
                dataKey={rows?.[getRandomNumber(rows?.length - 1)]?.machineName}
                stroke="#8884d8"
                fillOpacity={1}
                fill="url(#colorUv)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>
        <Card className="flex items-center gap-2 flex-1 w-full">
          <div className="w-full flex flex-col justify-between h-full">
            <h2 className="mb-5">Active Hours vs Idle Hours</h2>
            <h3>{rows?.[pieChartState]?.machineName}</h3>
            <div className="my-5">
              <div>
                <p className="font-bold">Idle Time</p>
                <p className="font-[12px] text-slate-400">
                  {pieChartPercent?.[pieChartState]}% Less Idle time compared to
                  last month
                </p>
              </div>
              <div className="mt-2">
                <p className="font-bold">Active Hour</p>
                <p className="font-[12px] text-slate-400">
                  Active hours for machine
                </p>
              </div>
            </div>
            <div className="mt-2 flex justify-around w-full">
              <button
                type="button"
                className="link-hover"
                onClick={() =>
                  setPieChartState((prev) => {
                    if (prev < 1) {
                      return 0;
                    }
                    return prev - 1;
                  })
                }
              >
                Prev
              </button>
              <button
                type="button"
                className="link-hover"
                onClick={() =>
                  setPieChartState((prev) => {
                    if (prev >= rows?.length - 1) {
                      return rows?.length - 1;
                    }
                    return prev + 1;
                  })
                }
              >
                Next
              </button>
            </div>
          </div>
          <ResponsiveContainer width="100%" height={270}>
            <PieChart>
              <Tooltip />
              <Pie
                data={data?.slice(0, 2)}
                dataKey={rows?.[pieChartState]?.machineName}
                nameKey="name"
                fill="#8884d8"
              />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>
      <div className="mt-6">
        <Card>
          <h3 className="mb-2">Machine Details:</h3>
          <Table>
            <Table.Head>
              <Table.Th>#</Table.Th>
              <Table.Th>Name</Table.Th>
              <Table.Th>Device Id</Table.Th>
              <Table.Th>Cost Per Hour</Table.Th>
              <Table.Th>Daily Threshold</Table.Th>
              <Table.Th>Maintenance Threshold</Table.Th>
            </Table.Head>
            <Table.Body>
              {rows?.map((row, rIdx) => (
                <Table.Row key={rIdx}>
                  <Table.Td>{rIdx + 1}</Table.Td>
                  <Table.Td>{row.machineName}</Table.Td>
                  <Table.Td>{row?.device?.deviceId}</Table.Td>
                  <Table.Td>{row?.device?.costPerHour || '0'}</Table.Td>
                  <Table.Td>{row?.device?.dailyThreshold || '0'}</Table.Td>
                  <Table.Td>
                    {row?.device?.maintenanceThreshold || '0'}
                  </Table.Td>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </Card>
      </div>
    </section>
  );
};

export default CustomerDashboardPageAnalytics;
