import { useState } from 'react';
import { Modal } from 'antd';
import { IoMdClose } from 'react-icons/io';
import Input from '../../global/components/Input';
import Button from '../../global/components/Button';
import { toast } from 'react-toastify';

function DropdownField({
  idType,
  idIndex,
  type,
  tVal,
  setFormat,
  setEditData,
}) {
  const [openModal, setOpenModal] = useState(false);
  const [inputData, setInputData] = useState('');

  const onAdd = () => {
    const val = inputData?.trim();
    if (!val) {
      toast.error('Value cannot be empty');
      return;
    }
    if (tVal?.includes(val)) {
      toast.error(`${val} already exists in the list`);
      return;
    }

    setFormat((prev) => {
      const data = prev?.[idType]?.map((i, idx) => {
        if (idIndex === idx) {
          return {
            ...i,
            [type]: [...(i?.[type] || []), val],
          };
        }
        return i;
      });
      setEditData((prev) => ({ ...(prev || {}), [idType]: data }));
      return {
        ...prev,
        [idType]: data,
      };
    });
    toast.success('Option added');
    setInputData('');
  };

  const onRemove = (item) => {
    setFormat((prev) => {
      const data = prev?.[idType]?.map((i, idx) => {
        if (idIndex === idx) {
          return {
            ...i,
            [type]: i?.[type]?.filter((el) => el !== item),
          };
        }
        return i;
      });
      setEditData((prev) => ({ ...(prev || {}), [idType]: data }));
      return {
        ...prev,
        [idType]: data,
      };
    });
  };

  return (
    <div>
      <Modal
        open={openModal}
        onClose={() => setOpenModal(false)}
        onCancel={() => setOpenModal(false)}
        onOk={() => setOpenModal(false)}
        title="Dropdown Options"
      >
        {tVal?.map((val, vIdx) => (
          <p
            className="flex items-center w-full gap-4 px-2 border-b my-2"
            key={val}
          >
            <span className="w-3">{vIdx + 1}</span>
            <span className="w-full">{val}</span>
            <span
              onClick={() => onRemove(val)}
              className="text-red-400 cursor-pointer hover:bg-red-500 hover:text-white rounded-md"
            >
              <IoMdClose />
            </span>
          </p>
        ))}
        <div className="flex w-full items-center gap-4">
          <Input
            value={inputData}
            onChange={(e) => setInputData(e?.target?.value)}
            className={'w-full'}
          />
          <Button onClick={onAdd}>Add</Button>
        </div>
      </Modal>

      <span
        onClick={() => setOpenModal(true)}
        className="h-[45px] w-fit px-2 border border-[#C8CEE1] rounded-lg flex items-center cursor-pointer hover:bg-gray-100"
      >
        Dropdown
      </span>
    </div>
  );
}

export default DropdownField;
