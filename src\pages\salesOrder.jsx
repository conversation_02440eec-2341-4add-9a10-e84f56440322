import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import { Archive, ArrowDown, ArrowUp, Trash } from 'lucide-react';
import { useContext, useEffect, useMemo, useState } from 'react';
import { Line } from 'react-chartjs-2';
import { FaLock } from 'react-icons/fa';
import { IoMdNotifications, IoMdNotificationsOff } from 'react-icons/io';
import { useMediaQuery } from 'react-responsive';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import HistorySidebar from '../components/Kanban/HistorySidebar';
import CustomToolTip from '../components/global/CustomToolTip';
import TruncateString from '../components/global/TruncateString';
import Button from '../components/global/components/Button';
import { FilterIcon, FilterV2 } from '../components/global/components/FilterV2';
import Header from '../components/global/components/Header';
import { InfoTooltip } from '../components/global/components/InfoTooltip';
import Pagination from '../components/global/components/Pagination';
import RightSidebar from '../components/global/components/RightSidebar';
import SelectV2 from '../components/global/components/SelectV2';
import Table from '../components/global/components/Table';
import TablePopup from '../components/global/components/TablePopup';
import ToolTip from '../components/global/components/ToolTip';
import SalesInfoTab from '../components/salesOrder/SalesInfoTab';
import SoForm from '../components/salesOrder/SoForm';
import StatusModal from '../components/salesOrder/StatusModal';
import SalesOrderSideBar from '../components/salesOrder/salesOrderSideBar';
import SalesReport from '../components/salesOrder/salesReport';
import MediaModal from '../components/v3/global/components/MediaModal';
import SendMail from '../components/v3/global/components/SendMail';
import {
  generatePrefixId,
  getLocalDate,
  getLocalDateTime,
  handlePdf,
  mobileWidth,
  tabletWidth,
} from '../helperFunction';
import useDebounceValue from '../hooks/useDebounceValue';
import { useLazyGetSalesForecastQuery } from '../slices/ai/forecastApiSlice';
import { useUpdateStatusByFieldNameMutation } from '../slices/defaultsApiSlice';
import { useGetAllNcrsQuery } from '../slices/ncrApiSlice';
import { useLazyGetPdfQuery } from '../slices/pdfApiSlice';
import {
  useArchiveSalesOrdersMutation,
  useDeleteManySalesOrdersMutation,
  useGetSOFilterOptionsQuery,
  useQuerySalesDataQuery,
  useSendSalesOrderMutation,
  useUpdateSalesOrderMutation,
} from '../slices/salesOrderSlices';
import { Store } from '../store/Store';
import {
  dateOptions,
  PAGINATION_LIMIT,
  SALES_ORDER_FIELDS,
} from '../utils/Constant';
import { customConfirm } from '../utils/customConfirm';

function getFinalStatus(array) {
  if (array.length === 0) return 'No Job yet';
  if (array.length === 1 && array[0]) return array[0];
  if (array.includes('ongoing')) {
    return 'ongoing';
  } else if (array.every((status) => status === 'complete')) {
    return 'complete';
  } else {
    return 'not started';
  }
}

const MOBILE_VIEW_HEADERS = ['DATE', 'SALES ORDER ID', 'SO STATUS'];
const DESKTOP_VIEW_HEADERS = [
  '',
  'TASK ID',
  'DATE',
  'SALES ORDER ID',
  'QUOTATION ID',
  'COMPANY NAME',
  'DELIVERY DATE',
  'SO STATUS',
  'JOB STATUS',
];
const SalesOrder = () => {
  const [checkedRows, setCheckedRows] = useState([]);
  const soStatusFieldName = 'salesOrderCustomStatus';
  const user = JSON.parse(localStorage.getItem('user'))?.user;
  const columnKeys = SALES_ORDER_FIELDS?.map((elem) => elem?.value);
  const handleNavigate = useNavigate();
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const [searchParams] = useSearchParams();
  const [historySidebar, setHistorySidebar] = useState({
    open: false,
    steps: [],
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const [openSideBar, setOpenSideBar] = useState(false);
  const [pos, setPos] = useState(null);
  const [editModal, setEditModal] = useState(false);
  const [createModal, setCreateModal] = useState(
    searchParams.get('kanban') === 'true'
  );
  const debounceSearch = useDebounceValue(searchTerm || '');
  const [updateSalesOrder] = useUpdateSalesOrderMutation();
  const [period, setPeriod] = useState(30);
  const [forecastRightSide, setForecastRightSide] = useState(false);
  const [ShowEmailModal, setShowEmailModal] = useState(false);
  const [SendingMail, setSendingMail] = useState(false);
  const [mailData, setMailData] = useState({
    receiver: '',
    body: '',
    subject: '',
    input: {},
    attachments: [],
  });
  const [clickedRow, setClickedRow] = useState('');
  const navigate = useNavigate();
  const [ReadMore, setReadMore] = useState(false);
  const [copyModal, setCopyModal] = useState(false);
  const [deleteManySalesOrders] = useDeleteManySalesOrdersMutation();
  const [archiveSalesOrders] = useArchiveSalesOrdersMutation();
  const [sendSalesOrder] = useSendSalesOrderMutation();
  const { data: ncrs = [] } = useGetAllNcrsQuery();
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const [showNotification, setShowNotifications] = useState(false);
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [statusToCreate, setStatusToCreate] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [updateSoStatusInDefaults] = useUpdateStatusByFieldNameMutation();
  const [type, setType] = useState('desc');
  const { data: filterOptions } = useGetSOFilterOptionsQuery();
  const [showFilters, setShowFilters] = useState(true);
  const [filters, setFilters] = useState([]);

  const filterConfig = [
    {
      label: 'Sales Order ID',
      key: 'salesOrderId',
      path: 'salesOrderID',
      type: 'multiSelect',
      options: filterOptions?.salesOrderID,
    },
    {
      key: 'Date',
      label: 'Date',
      path: 'createdAt',
      type: 'date',
    },
    {
      key: 'Delivery Date',
      label: 'Delivery Date',
      path: 'deliveryDate',
      type: 'date',
    },
    {
      key: 'salesOrderStatus',
      label: 'Sales Order Status',
      path: 'salesOrderStatus',
      type: 'multiSelect',
      options: filterOptions?.salesOrderStatus,
    },
    {
      key: 'customer',
      label: 'Customer',
      path: 'customer',
      type: 'multiSelect',
      options: filterOptions?.customer,
    },
    {
      key: 'quotationID',
      label: 'Quotation ID',
      path: 'quotationID',
      type: 'multiSelect',
      options: filterOptions?.quotationID,
    },
  ];

  const colors = {
    PENDING: 'bg-yellow-200 text-yellow-600',
    REJECTED: 'bg-red-200 text-red-600',
    APPROVED: 'bg-[#DCF0DD] text-[#0F6A2E]',
  };

  const defaultStatuses = [
    {
      label: 'Pending',
      value: 'pending',
    },
    {
      label: 'Approved',
      value: 'approved',
    },
    {
      label: 'Rejected',
      value: 'rejected',
    },
  ];

  const getStatusColor = (status) => {
    if (colors[status]) return colors[status];
    return 'bg-fuchsia-200 text-fuchsia-700';
  };

  const periodOptions = [
    { label: '1 Month', value: 30 },
    { label: '3 Months', value: 90 },
    { label: '6 Months', value: 180 },
    { label: '1 Year', value: 360 },
  ];
  const graphOptions = {
    elements: {
      point: {
        radius: 2,
      },
      line: {
        borderWidth: 1.5,
      },
    },
    scales: {
      x: {
        title: { display: true, text: 'Date' },
        grid: {
          display: false,
        },
      },
      y: {
        title: { display: true, text: 'Cost' },
        grid: {
          display: false,
        },
        beginAtZero: true,
      },
    },
  };

  const { data: salesData = {}, isLoading: isLoadingSales } =
    useQuerySalesDataQuery(
      { page, limit, filters, debounceSearch, type },
      { skip: !page || !limit, refetchOnMountOrArgChange: true }
    );
  const { results: rows = [], totalPages = 0, totalResults = 1 } = salesData;

  const {
    state,
    defaults: { defaultParam },
  } = useContext(Store);

  const ordersMapping = useMemo(() => {
    const mapping = {};
    const corresponding = { 'Not Assigned': 'Not Assigned' };
    rows?.forEach((order) => {
      const status =
        order.workOrders?.map((el) => corresponding[el.value]) || [];
      mapping[order._id] = getFinalStatus(status);
    });
    return mapping;
  }, [rows]);

  const [
    salesForecasting,
    { data: forecastSalesData = {}, isLoading: forecastLoading },
  ] = useLazyGetSalesForecastQuery();

  const handleSalesForecast = (period) => {
    salesForecasting(period)
      .unwrap()
      .then(() => {
        setForecastRightSide(true);
      });
  };
  const sideBarHandler = (e, pos) => {
    setPos(pos);
    // prevent opening sidebar when checkbox is checked
    if (e.target.tagName.toLowerCase() !== 'input') setOpenSideBar(true);
  };

  const editModalHandler = () => {
    setEditModal(!editModal);
  };

  const createModalHandler = () => {
    setCreateModal(!createModal);
  };

  const handleDeleteAll = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to delete these Sales Orders?',
      'delete'
    );
    if (!confirm) return;
    const idsToDelete = checkedRows.map((item) => item._id);
    const res = await deleteManySalesOrders({ ids: idsToDelete });
    if (res?.data?.message) {
      const { msg1, msg2 } = res.data.message;
      if (msg1) toast.error(msg1);
      if (msg2) toast.success(msg2);
    }
    setCheckedRows([]);
  };

  const handleArchiveAll = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to archive these Sales Orders?',
      'success'
    );
    if (!confirm) return;
    try {
      const idsToArchive = checkedRows.map((item) => item._id);
      const res = await archiveSalesOrders({ ids: idsToArchive });
      if (res) {
        toast.success('Sales Orders archived successfully');
        setCheckedRows([]);
      }
    } catch (err) {
      toast.error(err?.data?.message || err?.message || 'Something went wrong');
    }
  };

  const handleSendmail = async () => {
    setSendingMail(true);
    const fd = new FormData();
    fd.append('receiver', mailData.receiver);
    fd.append('id', clickedRow);
    fd.append('subject', mailData.subject);
    fd.append('input', JSON.stringify({ ...rows[pos] }));
    fd.append('body', mailData.body);

    mailData.attachments?.forEach((file, index) => {
      fd.append(`attachments[${index}][data]`, file.data);
      fd.append(`attachments[${index}][name]`, file.name);
      fd.append(`attachments[${index}][type]`, file.type);
    });

    const res = await sendSalesOrder(fd);
    setSendingMail(false);
    if (res) toast.success('Mail Sent Successfully');
    setShowEmailModal(false);
    setMailData({
      receiver: '',
      body: '',
      subject: '',
      input: {},
      attachments: [],
    });
  };

  const isPIDisabled = checkedRows?.at(0)?.salesOrderStatus !== 'approved';
  const isDeleteVisible = checkedRows?.length > 0;
  const isArchiveVisible = checkedRows?.length > 0;
  const selectAll = checkedRows.length === rows.length && rows.length > 0;

  useEffect(() => {
    if (rows?.length === 0) return;
    const navigateTo = searchParams.get('navigateTo');
    const editId = searchParams.get('editid');
    const createModalParam = searchParams.get('createModal');

    if (navigateTo && editId) {
      const orderToEdit = rows?.findIndex((order) => order?._id === editId);
      setPos(orderToEdit);
      setEditModal(true);
    }
    if (createModalParam === 'true') {
      setCreateModal(true);
    }
  }, [searchParams, rows]);

  const setSteps = (taskId) => {
    let tasks = state?.allTiles;
    let chosenTask = tasks?.find((elem) => elem?.taskId === taskId);
    if (chosenTask) {
      setHistorySidebar({
        open: true,
        steps: chosenTask?.steps,
        orderId: chosenTask?._id,
      });
    }
  };

  const getSOStatus = () => {
    const allCustomStatuses = defaultParam?.[soStatusFieldName];
    return [...defaultStatuses, ...(allCustomStatuses || [])];
  };

  const handleCreateStatus = async () => {
    if (!statusToCreate) return toast.error('Please enter a status');

    const allStatuses = getSOStatus();
    if (allStatuses?.find((status) => status?.label === statusToCreate)) {
      return toast.error('Status already exists');
    }

    const body = {
      fieldName: soStatusFieldName,
      value: [
        ...(allStatuses || []),
        { label: statusToCreate.trim(), value: statusToCreate.trim() },
      ],
    };

    const res = await updateSoStatusInDefaults(body);
    if (!res?.error) {
      toast.success('Status created successfully');
      setStatusToCreate('');
    }
  };

  const handleDeleteCustomStatus = async (statusToDelete) => {
    const allStatuses = getSOStatus()?.filter(
      (status) => status.label !== statusToDelete.label
    );
    const body = {
      fieldName: soStatusFieldName,
      value: [...(allStatuses || [])],
    };
    const res = await updateSoStatusInDefaults(body);
    if (!res?.error) {
      toast.success('Status deleted successfully');
      setSelectedStatus('');
    }
  };

  const handleCheckBoxChange = (event, order) => {
    if (event.target.checked) {
      setCheckedRows((prev) => [...prev, order]);
    } else {
      setCheckedRows((prev) => prev.filter((row) => row._id !== order._id));
    }
  };

  const handleSelectAll = (e) => {
    setCheckedRows(e.target.checked ? [...rows] : []);
  };
  return (
    <div>
      {isMobile && clickedRow && (
        <TablePopup
          isEdit={false}
          onDownload={() => handlePdf(getPdf, clickedRow?._id, 'salesOrder')}
          downloading={isFetchingPdf}
          onBack={() => setClickedRow(null)}
        >
          <div className="space-y-4 !text-[12px]">
            {[
              [
                'TASK ID',
                clickedRow?.taskId?.customTaskId
                  ? `${clickedRow.taskId.customTaskId}(${clickedRow.taskId.taskId})`
                  : clickedRow?.taskId?.taskId || '-',
              ],
              [
                'SALES ORDER DATE',
                getLocalDateTime(clickedRow?.salesOrderDate),
              ],
              ['SALES ORDER ID', clickedRow?.salesOrderID],
              ['QUOTATION ID', clickedRow?.quotationID],
              ['CUSTOMER NAME', clickedRow?.customer],
              ['DELIVERY DATE', getLocalDateTime(clickedRow?.deliveryDate)],
              [
                'SALES ORDER STATUS',
                !defaultParam?.projectDefaults?.disableApprovals ? (
                  <span
                    className={`${colors[clickedRow?.salesOrderStatus?.charAt(0).toUpperCase() + clickedRow?.salesOrderStatus?.slice(1)]} px-3 py-1 rounded-md font-medium whitespace-nowrap`}
                  >
                    {clickedRow?.salesOrderStatus?.charAt(0).toUpperCase() +
                      clickedRow?.salesOrderStatus?.slice(1)}
                  </span>
                ) : (
                  '-'
                ),
              ],
              ['ORDER STATUS', ordersMapping[clickedRow?._id]],
            ].map(([label, value], index) => (
              <div
                key={index}
                className="w-full flex items-center justify-between gap-4"
              >
                <label className="font-semibold">{label}</label>
                <p>{value}</p>
              </div>
            ))}
          </div>
        </TablePopup>
      )}
      <StatusModal
        openModal={openStatusModal}
        setModalOpen={setOpenStatusModal}
        statusToCreate={statusToCreate}
        setStatusToCreate={setStatusToCreate}
        handleCreateStatus={handleCreateStatus}
        getSOStatus={getSOStatus}
        selectedStatus={selectedStatus}
        setSelectedStatus={setSelectedStatus}
        handleDeleteCustomStatus={handleDeleteCustomStatus}
        defaultStatuses={defaultStatuses}
        clickedRow={clickedRow}
      />
      <SalesReport className="absolute" data={rows[pos]} />
      <HistorySidebar sidebar={historySidebar} setSidebar={setHistorySidebar} />
      <div className="w-full flex justify-between items-center">
        <div className="flex flex-col !min-w-[30rem]">
          {!createModal && !editModal && !copyModal && (
            <Header
              description=""
              title="Sales Order Dashboard"
              infoTitle="Welcome to the Sales Order Page"
              infoDesc="Your hub for generating new Sales Order with ease. The Sales Order page simplifies the process of creating and managing
                Sales Order."
              paras={[
                'The Sales Order page simplifies the process of creating and managing Sales Order. Quickly generate, review, and track Sales Order with ease, enhancing your Sales Order efficiency. Easily communicate pricing details and streamline your Sales Order workflow.',
              ]}
            />
          )}
        </div>
      </div>
      {!editModal && !createModal && !copyModal && (
        <SalesInfoTab isMobile={isMobile} />
      )}

      {editModal || createModal || copyModal ? (
        <SoForm
          isMobile={isMobile}
          isTablet={isTablet}
          {...(editModal && {
            isEdit: true,
            edit: true,
            setIsEdit: setEditModal,
            modalHandler: editModalHandler,
            ...rows[pos],
          })}
          {...(createModal && {
            isAdd: true,
            setIsAdd: setCreateModal,
            modalHandler: createModalHandler,
            newId: defaultParam?.salesOrderId,
            ncr: ncrs,
          })}
          {...(copyModal && {
            isEdit: true,
            edit: true,
            setIsEdit: setCopyModal,
            modalHandler: editModalHandler,
            salesOrderStatus: 'pending',
            isCopy: true,
            ...rows[pos],
          })}
          orders={rows}
          setIsAdd={setCreateModal}
          setIsEdit={setEditModal}
          defaultParam={defaultParam}
        />
      ) : (
        <>
          <div className="flex justify-between items-center w-full bg-white rounded-tl-lg rounded-tr-lg mt-2 p-2">
            {!isMobile && (
              <p
                className="text-[#005EEC] font-medium cursor-pointer"
                onClick={() => setShowNotifications(!showNotification)}
              >
                {showNotification ? (
                  <IoMdNotifications />
                ) : (
                  <IoMdNotificationsOff />
                )}
              </p>
            )}
            <div className={`relative flex items-center w-full`}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5 absolute left-3 ml-2 text-gray-400 cursor-pointer"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                />
              </svg>

              <input
                className={`${isMobile ? '!w-[140px] ml-3 mr-1 !pl-8 text-[10px]' : isTablet ? '!min-w-[50px] !pl-10 ml-3 w-[118px]' : '!min-w-[100px] !pl-10 ml-3 w-[400px]'} !rounded-3xl !px-5 !py-1.5 outline-none md:text-sm bg-[#F2F1FB]`}
                placeholder={
                  isMobile ? 'Search Items...' : 'Search Items from list...'
                }
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex items-center justify-end mb-1 w-full mr-2 gap-4">
              {user?.archiveSalesOrderManagement && isArchiveVisible && (
                <CustomToolTip
                  tooltipId="archive-tooltip"
                  content="Archive"
                  place="top"
                  effect="solid"
                  className="bg-black text-white p-1 rounded"
                >
                  <Archive
                    className="h-5 w-5 text-blue-500 cursor-pointer"
                    onClick={handleArchiveAll}
                  />
                </CustomToolTip>
              )}
              {!isMobile && isDeleteVisible && (
                <CustomToolTip
                  tooltipId="delete-tooltip"
                  content="Delete"
                  place="top"
                  effect="solid"
                  className="bg-black text-white p-1 rounded"
                >
                  <Trash
                    className="w-5 h-5 cursor-pointer text-red-500"
                    onClick={handleDeleteAll}
                  />
                </CustomToolTip>
              )}
              {!isMobile && !createModal && !editModal && !copyModal && (
                <FilterIcon
                  showFilters={showFilters}
                  setShowFilters={setShowFilters}
                />
              )}
              {!isMobile && (
                <Button
                  className={`!p-1 !px-2 !text-[12px] !h-6 ${isTablet && 'text-xs'}`}
                  onClick={() =>
                    handleNavigate(
                      `/accountmanagement/invoices/create/sales?returnTab=sales&salesOrderId=${checkedRows[0]?._id}`
                    )
                  }
                  disabled={isPIDisabled}
                >
                  Convert to SI
                </Button>
              )}
              {!createModal && !editModal && !copyModal && (
                <Button
                  className={`!p-1 !px-2 !text-[12px] !h-6 ${isTablet && 'text-xs'}`}
                  onClick={() => handleSalesForecast(period)}
                >
                  Forecast
                </Button>
              )}
              {!createModal && !editModal && !copyModal && (
                <Button
                  className="!px-2 !py-1 !h-6 !text-xs !mr-3"
                  onClick={createModalHandler}
                >
                  +&nbsp;Create
                </Button>
              )}
            </div>
          </div>
          {showNotification ? (
            <div>
              {ncrs.filter((notif) => notif.orderCheck).length === 0 ? (
                <div className="flex items-center justify-center p-4 text-gray-500">
                  <p className="text-sm font-medium">
                    No new notifications available
                  </p>
                </div>
              ) : (
                <div className="mb-3">
                  {ncrs
                    .filter((notif) => notif.orderCheck)
                    .map((notif) => (
                      <div
                        className="flex items-center justify-between px-[1.3rem] py-[0.8rem] border shadow-md rounded-[1rem] mb-3 cursor-pointer"
                        key={notif._id}
                      >
                        <div className="flex items-center gap-x-5">
                          <div
                            className="color-box w-[1rem] h-[1rem] rounded-md"
                            style={{ backgroundColor: '#7f04e4' }}
                          />
                          <div className="details">
                            <p className="font-medium text-[12px]">
                              <span>New NCR {notif?.id || ''} is Raised</span>
                            </p>
                            <span className="text-slate-500 text-[12px]">
                              Raised on: {getLocalDate(notif.createdAt)}
                            </span>
                          </div>
                        </div>
                        <Button
                          className="!rounded-2xl"
                          onClick={() => {
                            const taskId = generatePrefixId(
                              defaultParam?.prefixIds?.['taskId']
                            );
                            navigate(
                              `/salesordermanagement/orders?kanban=true&department=sales&page=Sales%20Order&index=2&refType=Sales%20Order&taskID=${taskId}&ncrId=${notif._id}`
                            );
                            setCreateModal(true);
                          }}
                        >
                          + Create Sales Order
                        </Button>
                      </div>
                    ))}
                </div>
              )}
            </div>
          ) : (
            <>
              <FilterV2
                showFilters={showFilters}
                config={filterConfig}
                setFilters={setFilters}
              />
              <Table>
                <Table.Head>
                  <Table.Row>
                    {DESKTOP_VIEW_HEADERS.map((el, index) => {
                      const isHide =
                        isMobile && !MOBILE_VIEW_HEADERS.includes(el);
                      if (isHide) return null;

                      if (el === '' && !isMobile) {
                        return (
                          <Table.Th key={index}>
                            {checkedRows.length > 0 && (
                              <div>
                                <input
                                  type="checkbox"
                                  className="mr-2"
                                  checked={selectAll}
                                  onChange={handleSelectAll}
                                />
                                Select All
                              </div>
                            )}
                          </Table.Th>
                        );
                      }

                      if (el === 'DATE') {
                        return (
                          <Table.Th key={index}>
                            <div className="flex">
                              <div>Date</div>
                              {type === 'asc' ? (
                                <ArrowUp
                                  cursor="pointer"
                                  size={15}
                                  onClick={() => setType('desc')}
                                />
                              ) : (
                                <ArrowDown
                                  cursor="pointer"
                                  size={15}
                                  onClick={() => setType('asc')}
                                />
                              )}
                            </div>
                          </Table.Th>
                        );
                      }
                      return <Table.Th key={index}>{el}</Table.Th>;
                    })}
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {isLoadingSales ? (
                    <Table.Row>
                      <Table.Td colSpan={DESKTOP_VIEW_HEADERS.length + 1}>
                        <div className="flex justify-center items-center">
                          <Spin
                            size="large"
                            indicator={<LoadingOutlined spin />}
                          />
                        </div>
                      </Table.Td>
                    </Table.Row>
                  ) : (
                    rows?.map((order, orderIndex) => {
                      const companyName =
                        order?.CustomerData?.companyName ||
                        order?.CustomerData?.company_name;
                      return (
                        <Table.Row
                          key={order?.salesOrderID}
                          isClickable={isMobile}
                          onClick={() => setClickedRow(order)}
                          className="hover:bg-gray-100 hover:cursor-pointer"
                        >
                          {!isMobile && (
                            <Table.Td>
                              <input
                                type="checkbox"
                                onChange={(event) =>
                                  handleCheckBoxChange(event, order)
                                }
                                checked={checkedRows.includes(order)}
                              />
                            </Table.Td>
                          )}
                          {!isMobile && (
                            <Table.Td
                              className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                              onClick={() => setSteps(order?.taskId?.taskId)}
                            >
                              {order?.taskId?.customTaskId
                                ? `${order?.taskId?.customTaskId.slice(0, 20)}(${order?.taskId?.taskId})...`
                                : order?.taskId?.taskId || '-'}
                            </Table.Td>
                          )}

                          <Table.Td>
                            {user?.columnAccess?.includes(columnKeys?.[2]) ? (
                              new Date(
                                order?.salesOrderDate
                              ).toLocaleDateString('en-in', dateOptions)
                            ) : (
                              <div className="h-[3rem] w-full flex items-center">
                                <FaLock className="text-2xl text-slate-400" />
                              </div>
                            )}
                          </Table.Td>

                          <Table.Td
                            className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                            isClickable={isMobile}
                            onClick={(e) => {
                              if (isMobile) {
                                setClickedRow(order);
                              } else {
                                sideBarHandler(e, orderIndex);
                              }
                            }}
                          >
                            <TruncateString length={13}>
                              {order?.salesOrderID}
                            </TruncateString>
                          </Table.Td>
                          {!isMobile && (
                            <Table.Td>
                              {user?.columnAccess?.includes(columnKeys?.[1]) ? (
                                order?.quotationID
                              ) : (
                                <div className="h-[3rem] w-full flex items-center ml-[2rem]">
                                  <FaLock className="text-2xl text-slate-400" />
                                </div>
                              )}
                            </Table.Td>
                          )}
                          {!isMobile && (
                            <Table.Td>
                              {user?.columnAccess?.includes(columnKeys?.[1]) ? (
                                companyName ? (
                                  companyName?.length <= 13 ? (
                                    companyName
                                  ) : (
                                    <ToolTip text={companyName}>
                                      {companyName.substring(0, 13) + '...'}
                                    </ToolTip>
                                  )
                                ) : (
                                  '-'
                                )
                              ) : (
                                <div className="h-[3rem] w-full flex items-center ml-[2rem]">
                                  <FaLock className="text-2xl text-slate-400" />
                                </div>
                              )}
                            </Table.Td>
                          )}
                          {!isMobile && (
                            <Table.Td>
                              {user?.columnAccess?.includes(columnKeys?.[3]) ? (
                                order?.deliveryDate ? (
                                  new Date(
                                    order.deliveryDate
                                  ).toLocaleDateString('en-in', dateOptions)
                                ) : (
                                  '-'
                                )
                              ) : (
                                <div className="h-[3rem] w-full flex items-center ml-[2rem]">
                                  <FaLock className="text-2xl text-slate-400" />
                                </div>
                              )}
                            </Table.Td>
                          )}
                          <Table.Td>
                            {user?.columnAccess?.includes(columnKeys?.[0]) ? (
                              !defaultParam?.projectDefaults
                                ?.disableApprovals ? (
                                <div className="flex items-center">
                                  <span
                                    onClick={() => {
                                      setClickedRow(order);
                                      setOpenStatusModal(true);
                                    }}
                                    className={`${getStatusColor(order?.salesOrderStatus?.toUpperCase()?.trim())} px-3 py-1 rounded-full font-medium whitespace-nowrap`}
                                  >
                                    {order?.salesOrderStatus
                                      ?.charAt(0)
                                      .toUpperCase() +
                                      order?.salesOrderStatus?.slice(1)}
                                  </span>
                                  {order?.salesOrderStatus?.toLowerCase() ===
                                    'rejected' &&
                                    [...(order?.statusTimeline || [])].sort(
                                      (a, b) =>
                                        new Date(b.timestamp).getTime() -
                                        new Date(a.timestamp).getTime()
                                    )?.[0]?.remark && (
                                      <InfoTooltip
                                        id="quote status"
                                        position="top"
                                        className="ml-2"
                                      >
                                        {
                                          [
                                            ...(order?.statusTimeline || []),
                                          ].sort(
                                            (a, b) =>
                                              new Date(b.timestamp).getTime() -
                                              new Date(a.timestamp).getTime()
                                          )?.[0]?.remark
                                        }
                                      </InfoTooltip>
                                    )}
                                </div>
                              ) : (
                                <span>-</span>
                              )
                            ) : (
                              <div className="h-[3rem] w-full flex items-center ml-[1.5rem]">
                                <FaLock className="text-2xl text-slate-400" />
                              </div>
                            )}
                          </Table.Td>
                          {!isMobile && (
                            <Table.Td>{ordersMapping[order?._id]}</Table.Td>
                          )}
                        </Table.Row>
                      );
                    })
                  )}
                </Table.Body>
              </Table>
              <Pagination
                limit={limit}
                page={page}
                totalPages={totalPages}
                totalResults={totalResults}
                setPage={setPage}
                setLimit={setLimit}
                className="w-full"
              />
            </>
          )}
        </>
      )}
      {ReadMore && (
        <MediaModal
          FormData={rows[pos]?.files}
          isView
          setShowModal={setReadMore}
          ShowModal={ReadMore}
        />
      )}
      {rows.length > 0 && (
        <RightSidebar
          openSideBar={openSideBar}
          setOpenSideBar={setOpenSideBar}
          scale={736}
        >
          <SalesOrderSideBar
            corresponding={ordersMapping}
            setCopyModal={setCopyModal}
            data={rows}
            pos={pos}
            setPos={setPos}
            openSideBar={openSideBar}
            setOpenSideBar={setOpenSideBar}
            editModal={editModal}
            editModalHandler={setEditModal}
            updateSalesOrder={updateSalesOrder}
            setShowEmailModal={setShowEmailModal}
            setReadMore={setReadMore}
            setClickedRow={setClickedRow}
          />
        </RightSidebar>
      )}
      <RightSidebar
        scale={736}
        openSideBar={forecastRightSide}
        setOpenSideBar={setForecastRightSide}
      >
        {forecastLoading && <Spin indicator={<LoadingOutlined spin />} />}
        {forecastSalesData?.[0]?.labels && (
          <>
            <h3 className="p-2">Sales Forecasting</h3>
            <div className="flex w-full justify-end">
              <SelectV2
                options={periodOptions}
                onChange={(e) => {
                  setPeriod(e.target.value);
                  handleSalesForecast(e.target.value);
                }}
                value={period}
              />
            </div>
            <Line
              className="mb-6"
              options={graphOptions}
              data={forecastSalesData[0] || {}}
            />
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Th>Forecast Date</Table.Th>
                  <Table.Th>Sales amount expected</Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                <Table.Row>
                  <Table.Td>{forecastSalesData[1]}</Table.Td>
                  <Table.Td>{forecastSalesData[2]}</Table.Td>
                </Table.Row>
              </Table.Body>
            </Table>
          </>
        )}
      </RightSidebar>
      {ShowEmailModal && (
        <SendMail
          title="Send Sales Order Mail"
          mailData={mailData}
          setMailData={setMailData}
          SendingMail={SendingMail}
          setShowEmailModal={setShowEmailModal}
          handleSendmail={handleSendmail}
        />
      )}
    </div>
  );
};

export default SalesOrder;
