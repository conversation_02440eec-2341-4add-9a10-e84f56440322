import { ArrowUpTrayIcon } from '@heroicons/react/24/outline';
import { Checkbox } from 'antd';
import { useMemo, useState } from 'react';
import { CiCircleCheck } from 'react-icons/ci';
import { FcOk } from 'react-icons/fc';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { ReactComponent as Fill } from '../../assets/svgs/icons8-edit.svg';
import { ReactComponent as Page } from '../../assets/svgs/icons8-page.svg';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import Table from '../global/components/Table';

const TdComponent = ({
  col,
  row,
  setRowMediaData,
  assets,
  boms = [],
  setDepartmentData,
  idx,
  DepartmentData = [],
  showModal,
  setSelectedRow,
  setShowFormModal,
  setForm,
  // setBomData,
  setShowBom,
  setFormManagementModal,
  setShowHyperLink,
  // setShowModal,
  // setSelectedBom,
  setSelectedCol,
  setFormType,
  setMediaType,
  allPos = [],
  setShowFillForm,
}) => {
  const allPosOptions = useMemo(
    () =>
      [
        {
          name: '+ Add Workorder',
          value: '+',
        },
        ...allPos?.map((el) => ({
          label: el?.name,
          value: el?._id,
        })),
      ] || [],
    [allPos]
  );
  const [val, setStringVal] = useState(''); //eslint-disable-line
  const [show, setShow] = useState(false);
  const [event, setEvent] = useState(); //eslint-disable-line
  const handleNavigate = useNavigate();
  const [searchParams, _setSearchParams] = useSearchParams();
  const location = useLocation();
  const path = location.pathname;
  const menuPlacement = DepartmentData?.length / 2 < idx ? 'top' : 'bottom';
  const styles = {
    menuList: (provided) => {
      return {
        ...provided,
        maxHeight: '100px',
      };
    },
  };

  const handelInputChange = (e, fieldname, colType) => {
    if (colType === 'assembly form') {
      const tmp = DepartmentData?.map((rowdata, index) => {
        if (index === idx) {
          return {
            ...rowdata,
            data: {
              ...rowdata?.data,
              [fieldname]: {
                value: e.target.value,
                type: colType,
              },
            },
          };
        } else {
          return rowdata;
        }
      });
      setDepartmentData(tmp);
      return;
    }

    if (
      colType === 'checkbox' ||
      fieldname === 'CHeckbox' ||
      fieldname === 'Check' ||
      fieldname === 'Checkbox'
    ) {
      const tmp = DepartmentData?.map((rowdata, index) => {
        if (index === idx) {
          return {
            ...rowdata,
            data: {
              ...rowdata?.data,
              [fieldname]: {
                value: e.target.checked,
                type: colType,
              },
            },
          };
        } else {
          return rowdata;
        }
      });
      setDepartmentData(tmp);
    } else {
      const tmp = DepartmentData?.map((rowdata, index) => {
        if (index === idx) {
          return {
            ...rowdata,
            data: {
              ...rowdata?.data,
              [fieldname]: {
                value:
                  colType === 'bom' || colType === 'bomComment'
                    ? [e.target.value]
                    : e.target.value,
                type: colType,
              },
            },
          };
        } else {
          return rowdata;
        }
      });
      setDepartmentData(tmp);
    }
  };

  const handleSelect = (selectedOption) => {
    const val = boms.find((item) => item._id === selectedOption);
    setStringVal(val.name);
  };

  if (col?.type === 'checkbox') {
    return (
      <Table.Td>
        <Checkbox
          checked={row?.data?.[col?.name]?.value}
          onChange={(e) => {
            handelInputChange(e, col?.name, col?.type);
          }}
        />
      </Table.Td>
    );
  }

  if (col?.type === 'bom' || col?.type === 'bomComment') {
    // {row?.bom?.name || ''}
    return (
      <Table.Td>
        <div
          className=""
          // onMouseEnter={() => (event ? setShow(true) : setShow(false))}
          // onMouseLeave={() => setShow(false)}
        >
          <div className="flex items-center gap-2 w-full justify-center">
            <Select
              value={row?.data?.[`${col?.name}`]?.value}
              menuPlacement={menuPlacement}
              styles={styles}
              options={[
                { value: '+', name: `+ Add BOM` },
                ...(boms?.map((item) => ({
                  name: item?.name,
                  value: item?._id,
                })) || []),
              ]}
              className={`!h-[3rem] !w-[8rem] ${DepartmentData[idx]?.data[col?.name]?.value ? 'hidden' : ''}`}
              onChange={(e) => {
                {
                  if (e.target.value === '+') {
                    if (
                      !searchParams.get('kanban') ||
                      searchParams.get('kanban') !== true
                    ) {
                      handleNavigate(
                        `/jobs/workorder?selectedTab=bom&modalOpen=true&path=${path}`
                      );
                    }
                    if (searchParams.get('orderId')) {
                      handleNavigate(
                        `/jobs/workorder?selectedTab=bom&modalOpen=true&path=${path}&kanban=${searchParams.get('kanban')}&department=${searchParams.get('department')}&refType=${searchParams.get('refType')}&page=${searchParams.get('page')}&orderId=${searchParams.get('orderId')}&index=${searchParams.get('index')}`
                      );
                    }
                    if (!searchParams.get('orderId')) {
                      handleNavigate(
                        `/jobs/workorder?selectedTab=bom&modalOpen=true&path=${path}&kanban=${searchParams.get('kanban')}&department=${searchParams.get('department')}&refType=${searchParams.get('refType')}&page=${searchParams.get('page')}&index=${searchParams.get('index')}`
                      );
                    }

                    const kanban = searchParams.get('kanban') === 'true';
                    const orderId = searchParams.get('orderId');
                    const navigateParams = {
                      kanban,
                      department: searchParams.get('department'),
                      refType: searchParams.get('refType'),
                      page: searchParams.get('page'),
                      taskId: searchParams.get('taskId'),
                      orderId,
                    };

                    const filteredParams = Object.fromEntries(
                      Object.entries(navigateParams).filter(
                        ([_, value]) => value !== null
                      )
                    );

                    const navigateStr = `/jobs/workorder?selectedTab=bom&modalOpen=true&path=${path}&${new URLSearchParams(filteredParams).toString()}`;

                    if (kanban) {
                      handleNavigate(navigateStr);
                    } else {
                      handleNavigate(
                        `/jobs/workorder?selectedTab=bom&modalOpen=true&path=${path}`
                      );
                    }
                  } else {
                    setEvent(e.target.value);
                    setShow(false);
                    handelInputChange(e, col?.name, col?.type);
                    // let selectedBom = boms?.find(
                    //   (bom) => bom?._id === e.target.value
                    // );
                    // setBomData(selectedBom);
                    setShowBom(true);
                    handleSelect(e.target.value);
                    setSelectedCol(col?.name);
                    setSelectedRow(idx);
                  }
                }
              }}
            />
            <Button
              className={`!h-[30px] !w-[8rem] ${DepartmentData?.[idx]?.data[col?.name]?.value ? 'bg-[#037ffc]' : 'hidden'}`}
              onClick={() => {
                // let selectedBom = boms?.find(
                //   (bom) =>
                //     bom?._id === DepartmentData[idx]?.data[col?.name]?.value
                // );
                setShowBom(true);
                setSelectedCol(col?.name);
                setSelectedRow(idx);
                // setBomData(selectedBom);
              }}
            >
              View BOM
              <Page className="bg-transparent w-4 h-4" />
            </Button>
            <CiCircleCheck
              className={`w-6 h-6 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
            />
          </div>
          {show && (
            <div className="text-center w-[15rem]  absolute top-[55px] rounded-md p-3 left-20 bg-gray-800 text-white z-10">
              {row?.data?.bom?.name}
            </div>
          )}
        </div>
      </Table.Td>
    );
  }
  if (col?.type === 'multiCheckbox') {
    return (
      <Table.Td>
        <MultiSelect
          className="!w-[10rem]"
          options={col?.values?.map((item) => ({
            label: item,
            value: item,
          }))}
          onChange={(e) => {
            handelInputChange(e, col?.name, col?.type);
          }}
          value={row?.data?.[col?.name]?.value}
        />
      </Table.Td>
    );
  }

  if (col?.type === 'work order') {
    return (
      <Table.Td>
        <Select
          options={allPosOptions || []}
          onChange={(e) => {
            if (e.target.value === '+') {
              if (
                !searchParams.get('kanban') ||
                searchParams.get('kanban') !== true
              ) {
                handleNavigate(
                  `/jobs/workorder?selectedTab=workOrder&modalOpen=true&path=${path}`
                );
              }
              if (searchParams.get('orderId')) {
                handleNavigate(
                  `/jobs/workorder?selectedTab=workOrder&modalOpen=true&path=${path}&kanban=${searchParams.get('kanban')}&department=${searchParams.get('department')}&refType=${searchParams.get('refType')}&page=${searchParams.get('page')}&orderId=${searchParams.get('orderId')}&index=${searchParams.get('index')}`
                );
              }
              if (!searchParams.get('orderId')) {
                handleNavigate(
                  `/jobs/workorder?selectedTab=workOrder&modalOpen=true&path=${path}&kanban=${searchParams.get('kanban')}&department=${searchParams.get('department')}&refType=${searchParams.get('refType')}&page=${searchParams.get('page')}&index=${searchParams.get('index')}`
                );
              }
            } else {
              handelInputChange(e, col?.name, col?.type);
            }
          }}
          value={row?.data?.[col?.name]?.value || ''}
        />
        <FcOk
          className={`w-4 h-4 rounded-full ${row?.data?.[col?.name]?.value ? '' : 'hidden'}`}
        />
      </Table.Td>
    );
  }

  if (col?.type === 'string') {
    return (
      <Table.Td>
        <div className="flex items-center gap-2 w-full">
          <Input
            className="w-2/4"
            height="30px"
            onChange={(e) => {
              {
                setShow(false);
                handelInputChange(e, col?.name, col?.type);
                setStringVal(e.target.value);
              }
            }}
            value={row?.data?.[`${col?.name}`]?.value}
            placeholder={`Enter value for ${col?.name}`}
          />
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type === 'number') {
    return (
      <Table.Td>
        <div className="flex items-center gap-2 w-full">
          <Input
            type="number"
            className="w-2/4"
            height="30px"
            onChange={(e) => {
              {
                handelInputChange(e, col?.name, col?.type);
                setStringVal(e.target.value);
              }
            }}
            value={row?.data?.[`${col?.name}`]?.value}
          />
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type === 'email') {
    return (
      <Table.Td className="text-center  ml-1">
        <div className="flex items-center gap-2 w-full justify-center">
          <Input
            className="w-[9rem]"
            height="30px"
            onChange={(e) => {
              {
                handelInputChange(e, col?.name, col?.type);
                setStringVal(e.target.value);
              }
            }}
            value={row?.data?.[`${col?.name}`]?.value}
          />
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type === 'date') {
    return (
      <Table.Td className="text-center   ml-1">
        <div className=" flex items-center gap-2 w-full justify-center">
          <Input
            type="date"
            className="!w-[9rem]"
            height="30px"
            onChange={(e) => {
              {
                handelInputChange(e, col?.name, col?.type);
                setStringVal(e.target.value);
              }
            }}
            value={row?.data?.[`${col?.name}`]?.value}
          />
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type === 'assets') {
    return (
      <Table.Td className="text-center">
        <div
          className="
        flex items-center gap-2 w-full justify-center"
        >
          <MultiSelect
            className="!h-11 w-full"
            value={row?.data?.[col?.name]?.value?.flatMap((el) => el)}
            options={assets?.map((asset) => {
              return {
                label: asset?.name,
                value: asset?._id,
              };
            })}
            onChange={(e) => {
              handelInputChange(e, col?.name, col?.type);
            }}
          />
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value?.length > 0 ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type === 'forms') {
    return (
      <Table.Td
        className={'text-center'}
        onClick={() => {
          setShowFormModal(true);
          setForm(col?.form);
          setSelectedRow(idx);
          handelInputChange(
            { target: { value: row?.data?.[col?.name]?.value } },
            col?.name,
            col?.type
          );
          setSelectedCol(col?.name);
        }}
      >
        <div className=" flex items-center gap-2 w-full justify-center">
          <Button
            className={`${row?.data?.[`${col?.name}`]?.value ? 'hidden' : 'bg-[#037ffc]'} !py-3  !w-32 !h-8`}
          >
            Fill Form
            <Fill className="bg-transparent w-4 h-4" />
          </Button>
          <Button
            className={`${row?.data?.[`${col?.name}`]?.value ? 'bg-[#037ffc]' : 'hidden'} !py-3  !h-8 !w-32 `}
            onClick={() => setForm(DepartmentData[idx]?.data[col?.name]?.value)}
          >
            View Form
            <Page className="bg-transparent w-4 h-4" />
          </Button>
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.formType === 'fill') {
    return (
      <Table.Td
        className={'text-center'}
        onClick={() => {
          setShowFillForm(true);
          // setForm(col?.form);
          setSelectedRow(idx);
          handelInputChange(
            { target: { value: row?.data?.[col?.name]?.value } },
            col?.name,
            col?.type
          );
          setSelectedCol(col?.name);
        }}
      >
        <div className=" flex items-center gap-2 w-full justify-center">
          <Button
            className={`${row?.data?.[`${col?.name}`]?.value ? 'hidden' : 'bg-[#037ffc]'} !py-3  !w-32 !h-8`}
          >
            Fill Form
            <Fill className="bg-transparent w-4 h-4" />
          </Button>
          <Button
            className={`${row?.data?.[`${col?.name}`]?.value ? 'bg-[#037ffc]' : 'hidden'} !py-3  !h-8 !w-32 `}
            onClick={() => setForm(DepartmentData[idx]?.data[col?.name]?.value)}
          >
            View Form
            <Page className="bg-transparent w-4 h-4" />
          </Button>
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type === 'assembly form') {
    return (
      <Table.Td
        className="text-center"
        onClick={() => {
          setFormManagementModal(true);
          setSelectedRow(idx);
          handelInputChange(
            {
              target: {
                value: {
                  formName: '',
                  category: '',
                  steps: [],
                  isSequential: false,
                },
              },
            },
            col?.name,
            col?.type
          );
          setSelectedCol(col?.name);
          setFormType('assembly');
        }}
      >
        <div className="flex items-center gap-2  !py-2 w-full justify-center">
          <Button
            className={`${row?.data?.[`${col?.name}`]?.value ? 'bg-[#037ffc]' : ''} !py-3  !h-8`}
          >
            Add Assembly
            <Page className="w-4 h-4 bg-transparent" />
          </Button>
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type === 'form management') {
    return (
      <Table.Td
        className="text-center"
        onClick={() => {
          setFormManagementModal(true);
          setSelectedRow(idx);
          handelInputChange(
            { target: { value: row?.data?.[col?.name]?.value || [] } },
            col?.name,
            col?.type
          );
          setSelectedCol(col?.name);
        }}
      >
        <div className="flex items-center gap-2 w-full justify-center">
          <Button
            className={`${row?.data?.[`${col?.name}`]?.value ? 'bg-[#037ffc]' : ''} !py-3  !w-32 !h-8`}
          >
            Create Form
            <Page className="w-4 h-4 bg-transparent" />
          </Button>
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type === 'hyperlink') {
    return (
      <Table.Td
        className=" items-center gap-2  text-center"
        onClick={() => {
          setSelectedRow(idx);
          handelInputChange(
            { target: { value: row?.data?.[col?.name]?.value } },
            col?.name,
            col?.type
          );
          setSelectedCol(col?.name);
          setShowHyperLink(true);
        }}
      >
        <div className="flex items-center gap-2 w-full justify-center">
          <Button
            className={`${row?.data?.[`${col?.name}`]?.value ? 'hidden' : 'bg-[#037ffc]'} !py-3  !w-32 !h-8`}
          >
            Add Links
            <Fill className="bg-transparent w-4 h-4" />
          </Button>
          <Button
            className={`${row?.data?.[`${col?.name}`]?.value ? 'bg-[#037ffc]' : 'hidden'} !py-3  !h-8 !w-32 `}
            onClick={() => setForm(DepartmentData[idx]?.data[col?.name]?.value)}
          >
            View Links
            <Fill className="bg-transparent w-4 h-4" />
          </Button>
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type === 'audio') {
    return (
      <Table.Td
        className=" text-center"
        onClick={() => {
          setSelectedRow(idx);
          handelInputChange(
            { target: { value: row?.data?.[col?.name]?.value } },
            col?.name,
            col?.type
          );
          setSelectedCol(col?.name);
          showModal(true);
          setMediaType('audio');
        }}
      >
        <div className="flex items-center gap-2 w-full justify-center">
          <Button
            className={`!p-2 !min-w-[8rem] ${
              row?.data?.[`${col?.name}`]?.value !== 0 ? 'bg-[#037ffc]' : ''
            }`}
          >
            Add Audio
            <ArrowUpTrayIcon className="!w-[16px] !h-[16px]" />
            {/* Add Media */}
          </Button>
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.data?.[`${col?.name}`]?.value ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  if (col?.type !== 'media') {
    return (
      <Table.Td className="text-center   !h-11 ">
        {/* {row?.data?.[col?.name]} */}
        <Input
          value={row?.data?.[`${col?.name}`]?.value}
          onChange={(e) => {
            handelInputChange(e, col?.name, col?.type);
          }}
          inputClassname="!h-[2rem] !max-w-[9rem]"
        />
      </Table.Td>
    );
  }

  if (col?.type === 'media') {
    return (
      <Table.Td
        className="!text-center"
        onClick={() => {
          const media = row?.data?.[col?.name];
          const mediaAttachments = row?.attachments?.filter((attach) =>
            media?.value?.includes(attach?._id)
          );
          const mediaWithoutAudio = media?.value?.filter(
            (mediaItem) => mediaItem.type !== 'audio'
          );
          if (mediaAttachments?.length > 0) {
            setRowMediaData(mediaAttachments);
          } else {
            setRowMediaData(mediaWithoutAudio);
          }
          setMediaType('attachments');
          showModal(true);
          setSelectedRow(idx);
          setSelectedCol(col?.name);
          handelInputChange(
            { target: { value: row?.attachments } },
            col?.name,
            col?.type
          );
        }}
      >
        <div className="flex items-center justify-center gap-2 !min-w-[10rem]">
          <Button
            className={`!w-fit ${
              row?.attachments?.length !== 0 ? 'bg-[#037ffc]' : ''
            }  !w-32 `}
          >
            Add Media
            <ArrowUpTrayIcon className="!w-[16px] !h-[16px]" />
            {/* Add Media */}
          </Button>
          <FcOk
            className={`w-4 h-4 rounded-full ${row?.attachments?.length > 0 ? '' : 'hidden'}`}
          />
        </div>
      </Table.Td>
    );
  }

  return <Table.Td className="text-center">NA</Table.Td>;
};

export default TdComponent;
