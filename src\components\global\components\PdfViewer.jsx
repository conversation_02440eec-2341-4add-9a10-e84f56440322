import { useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { Button, Tooltip, Typography, Progress } from 'antd';
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  LeftOutlined,
  RightOutlined,
  DownloadOutlined,
  CloseOutlined,
  MenuOutlined,
} from '@ant-design/icons';
import { downloadMedia } from '../../../helperFunction';
import useSize from '../../../hooks/useSize';

const { Text } = Typography;

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.js',
  import.meta.url
).toString();

const PdfViewer = ({ file, name, closeClick }) => {
  const [numPages, setNumPages] = useState(1);
  const [page, setPage] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [showControls, setShowControls] = useState(true);
  const [loading, setLoading] = useState(true);

  const containerRef = useRef(null);
  const containerSize = useSize(containerRef);

  const handleZoomIn = () => setScale((prev) => prev + 0.1);
  const handleZoomOut = () =>
    setScale((prev) => (prev > 0.5 ? prev - 0.1 : 0.5));
  const handlePrevPage = () => setPage((prev) => (prev > 1 ? prev - 1 : 1));
  const handleNextPage = () =>
    setPage((prev) => (prev < numPages ? prev + 1 : numPages));

  const handleDownload = () => {
    downloadMedia({ name, data: file, type: 'application/pdf' });
  };

  return (
    <div
      className="w-full h-full flex flex-col relative bg-zinc-100"
      ref={containerRef}
    >
      {/* PDF Viewer */}
      <div
        className="flex-1 overflow-auto flex justify-center items-start mt-4"
        style={{
          height: containerSize?.height ? containerSize.height - 64 : '85vh',
        }}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-80 z-10">
            <Progress type="circle" percent={99} status="active" />
            <Text className="block mt-4">Loading PDF...</Text>
          </div>
        )}
        <Document
          file={file}
          onLoadSuccess={({ numPages }) => {
            setNumPages(numPages);
            setLoading(false);
          }}
          onLoadError={() => setLoading(false)}
          loading={null}
        >
          <Page
            pageNumber={page}
            scale={scale}
            renderTextLayer={false}
            renderAnnotationLayer={false}
            height={
              containerSize?.height ? containerSize.height - 80 : undefined
            }
            loading={null}
          />
        </Document>
      </div>

      {/* Controls Menu */}
      <div className="absolute bottom-4 right-4 z-20 flex gap-x-2 items-center">
        <span className="font-semibold text-gray-500 text-sm">Controls</span>
        <Button
          shape="circle"
          type="primary"
          icon={<MenuOutlined />}
          onClick={() => setShowControls((prev) => !prev)}
          className="shadow-lg transform transition-transform duration-300"
          style={{ transform: showControls ? 'rotate(90deg)' : 'rotate(0deg)' }}
        />
      </div>

      {/* Controls Bar (Expanding) */}
      {showControls && (
        <div className="absolute bottom-16 right-4 bg-black bg-opacity-70 p-3 rounded-lg shadow-lg flex flex-col space-y-2 transition-opacity duration-300">
          <Tooltip title="Previous Page">
            <Button
              icon={<LeftOutlined />}
              onClick={handlePrevPage}
              disabled={page <= 1}
              type="text"
              style={{ color: 'white' }}
            />
          </Tooltip>

          <Text style={{ color: 'white' }}>
            {page} / {numPages}
          </Text>

          <Tooltip title="Next Page">
            <Button
              icon={<RightOutlined />}
              onClick={handleNextPage}
              disabled={page >= numPages}
              type="text"
              style={{ color: 'white' }}
            />
          </Tooltip>

          <Tooltip title="Zoom Out">
            <Button
              icon={<ZoomOutOutlined />}
              onClick={handleZoomOut}
              type="text"
              style={{ color: 'white' }}
            />
          </Tooltip>

          <Text style={{ color: 'white' }}>{Math.round(scale * 100)}%</Text>

          <Tooltip title="Zoom In">
            <Button
              icon={<ZoomInOutlined />}
              onClick={handleZoomIn}
              type="text"
              style={{ color: 'white' }}
            />
          </Tooltip>

          <Tooltip title="Download">
            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownload}
              type="text"
              style={{ color: 'white' }}
            />
          </Tooltip>

          <Tooltip title="Close">
            <Button
              icon={<CloseOutlined />}
              onClick={closeClick}
              type="text"
              style={{ color: 'white' }}
            />
          </Tooltip>
        </div>
      )}
    </div>
  );
};

export default PdfViewer;
