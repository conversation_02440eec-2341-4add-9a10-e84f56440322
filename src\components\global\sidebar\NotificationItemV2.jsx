import { Popover, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { CiRead } from 'react-icons/ci';
import { HiDotsVertical } from 'react-icons/hi';
import { MdDeleteOutline } from 'react-icons/md';
import {
  useDeleteNewNotificationMutation,
  useToggleMarkAsReadMutation,
} from '../../../slices/newNotificationApiSlice';

const NotificationItemV2 = ({ notif, user }) => {
  const [deleteNewNotification] = useDeleteNewNotificationMutation();
  const [toggleMarkAsRead] = useToggleMarkAsReadMutation();

  function getDaysBetweenDates(creationDate) {
    const date1 = new Date(creationDate);
    const date2 = new Date();

    // Get the time difference in milliseconds
    const differenceInTime = date2.getTime() - date1.getTime();

    // Convert milliseconds to days
    const millisecondsInDay = 1000 * 60 * 60 * 24;
    const daysDifference = Math.floor(differenceInTime / millisecondsInDay);

    return daysDifference;
  }

  let msg = notif?.description;
  let createdDate = new Date(notif?.createdAt);
  let daysDifference = getDaysBetweenDates(notif?.createdAt);

  let dayArray = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];
  let dateToShow;
  if (daysDifference < 7) {
    let day = dayArray[createdDate?.getDay()];
    let time = createdDate?.toLocaleTimeString('en-US');

    dateToShow = `${day}, ${time}`;
  } else {
    dateToShow = createdDate?.toDateString();
  }

  return (
    <div className="flex items-center gap-2 mb-4 relative">
      <div className="rounded-full py-2 px-4 bg-slate-200 text-slate-400">
        {notif?.sender?.charAt(0)?.toUpperCase()}
      </div>
      <div className="mt-2 pb-2">
        <p className="text-sm">{msg}</p>
        <p className="text-[12px]">{dateToShow}</p>
      </div>
      <Popover
        as="div"
        className={`relative ml-auto aspect-square w-fit flex justify-center`}
      >
        <Popover.Button className="outline-none relative ml-auto">
          <HiDotsVertical className="ml-auto text-lg" />
        </Popover.Button>
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Popover.Panel className="-left-[130px] border-[1px] z-20 top-10 min-w-[170px] absolute rounded-lg overflow-y-scroll shadow-2xl no-scrollbar bg-white">
            <p
              className="hover:bg-slate-100 px-4 py-2 text-sm cursor-pointer flex items-center gap-2"
              onClick={() => {
                deleteNewNotification({ id: notif?._id });
              }}
            >
              <span>
                <MdDeleteOutline className="text-base" />
              </span>
              <span>Clear</span>
            </p>
            <p
              className="hover:bg-slate-100 px-4 py-2 text-sm cursor-pointer flex items-center gap-2"
              onClick={() => {
                toggleMarkAsRead({ id: notif?._id });
              }}
            >
              <span>
                <CiRead className="text-base" />
              </span>
              <span>
                Mark As {notif?.read?.includes(user?._id) ? 'Unread' : 'Read'}
              </span>
            </p>
          </Popover.Panel>
        </Transition>
      </Popover>
      <div></div>
    </div>
  );
};

export default NotificationItemV2;
