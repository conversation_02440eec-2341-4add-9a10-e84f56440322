import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useUpdateCuProjectAdditionalFieldsMutation } from '../../slices/CuProjectNewApiSlice';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Table from '../global/components/Table';

const createObjects = (rows, column) => {
  const result = {};
  let colsData = [];
  column.forEach((col) => {
    colsData.push({ type: col?.columnType || '', value: '' });
  });
  for (let i = 0; i < rows; i++) {
    result[i + 1] = colsData;
  }
  return result;
};

const AdditionalFieldRender = ({ fields, activeTile, saveDisabled }) => {
  /**
   * Renders a form to add or edit additional fields for a cuProject
   * @param {Object[]} fields - array of additional fields
   * @param {string} activeTile - current tile id
   * @param {boolean} saveDisabled - whether the save button is disabled
   * @returns {JSX.Element} - a form to add or edit additional fields
   */
  const [formData, setFormData] = useState({});
  const [updateCuProjectAdditionalFields] =
    useUpdateCuProjectAdditionalFieldsMutation();

  // Initialize formData with default values based on field types
  useEffect(() => {
    const initialData = {};
    fields.forEach((field) => {
      switch (field.fieldType) {
        case 'Check':
          initialData[field.fieldName] = {
            value: false,
            type: field.fieldType,
          };
          break;
        case 'MultiCheckbox':
          initialData[field.fieldName] = {
            type: field.fieldType,
          };
          field.labelArray.forEach((option) => {
            initialData[field.fieldName][option] = false;
          });
          break;
        case 'Table':
          initialData[field.fieldName] = {
            type: field.fieldType,
            columns: field?.tableOptions?.column || [],
            noOfRows: +field?.tableOptions?.rows || 0,
            rowData: createObjects(
              +field?.tableOptions?.rows || 0,
              field?.tableOptions?.column || []
            ),
          };
          break;
        default:
          initialData[field.fieldName] = {
            value: '',
            type: field.fieldType,
          };
      }
    });
    setFormData(initialData);
  }, [fields, activeTile]);

  const tableType = fields.filter((field) => field.fieldType === 'Table');
  const nonTableType = fields.filter((field) => field.fieldType !== 'Table');

  /**
   * Handles the change event for a form field.
   *
   * @param {Event} e - The event object representing the change event.
   * @param {string} fieldName - The name of the form field.
   * @param {string|null} [option=null] - The optional option for multi-select fields.
   * @return {void} This function does not return anything.
   */
  const handleChange = (e, fieldName, fieldType, option = null) => {
    const value =
      e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    if (option) {
      setFormData({
        ...formData,
        [fieldName]: {
          ...formData[fieldName],
          [option]: value,
          type: fieldType,
        },
      });
    } else {
      setFormData({
        ...formData,
        [fieldName]: { ...formData[fieldName], value: value, type: fieldType },
      });
    }
  };

  /**
   * Asynchronously handles saving the form data to the additional fields of a cuProject.
   *
   * @return {Promise<void>} A promise that resolves when the form data is successfully saved.
   */
  const handleSave = async () => {
    const update = await updateCuProjectAdditionalFields({
      id: activeTile,
      data: formData,
    }).unwrap();

    if (update) {
      toast.success('Saved successfully');
    }
  };

  return (
    <div className="mx-auto px-3">
      <div className=" h-48 overflow-auto">
        <div className=" p-4 rounded-lg grid grid-cols-2 sm:grid-cols-4 gap-6">
          {nonTableType?.map((field) => {
            switch (field.fieldType) {
              case 'Date':
                return (
                  <div key={field.fieldName} className="mb-4">
                    <label
                      htmlFor={field.fieldName}
                      className="block text-sm font-medium text-gray-700"
                    >
                      {field.fieldName}
                    </label>
                    <Input
                      type="date"
                      id={field.fieldName}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      onChange={(e) =>
                        handleChange(e, field.fieldName, field.fieldType)
                      }
                    />
                  </div>
                );
              case 'Check':
                return (
                  <div key={field.fieldName} className="flex items-center mb-2">
                    <Input
                      type="checkbox"
                      id={field.fieldName}
                      checked={formData[field.fieldName]?.value}
                      onChange={(e) =>
                        handleChange(e, field.fieldName, field.fieldType)
                      }
                      className="h-4 w-4 text-blue-500 focus:ring-blue-400 border-gray-300 rounded"
                    />
                    <label
                      htmlFor={field.fieldName}
                      className="ml-2 block text-sm text-gray-900"
                    >
                      {field.fieldName}
                    </label>
                  </div>
                );
              case 'String':
                return (
                  <div key={field.fieldName} className="mb-4">
                    <label
                      htmlFor={field.fieldName}
                      className="block text-sm font-medium text-gray-700"
                    >
                      {field.fieldName}
                    </label>
                    <Input
                      type="text"
                      id={field.fieldName}
                      value={formData[field.fieldName]?.value}
                      onChange={(e) =>
                        handleChange(e, field.fieldName, field.fieldType)
                      }
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                  </div>
                );
              case 'MultiCheckbox':
                return (
                  <div key={field.fieldName} className="mb-4">
                    <label className="block text-sm font-medium text-gray-700">
                      {field.fieldName}
                    </label>
                    {field.labelArray.map((option) => (
                      <div key={option} className="flex items-center">
                        <Input
                          type="checkbox"
                          id={option}
                          checked={formData[field.fieldName]?.[option]}
                          onChange={(e) =>
                            handleChange(
                              e,
                              field.fieldName,
                              field.fieldType,
                              option
                            )
                          }
                          className="h-4 w-4 text-blue-500 focus:ring-blue-400 border-gray-300 rounded"
                        />
                        <label
                          htmlFor={option}
                          className="ml-2 block text-sm text-gray-900"
                        >
                          {option}
                        </label>
                      </div>
                    ))}
                  </div>
                );

              default:
                return null;
            }
          })}
        </div>
        {tableType?.map((field, idx) => {
          if (field?.fieldType === 'Table') {
            return (
              <div className="w-full col-span-2 px-3" key={idx}>
                <div className="overflow-x-scroll pb-4 col-span-2">
                  <label className="block text-sm font-medium text-gray-700">
                    {field.fieldName}
                  </label>
                  <Table key={idx}>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th className={`text-center`}>Sr. No.</Table.Th>
                        {field?.tableOptions?.column?.map((col, colIndex) => (
                          <Table.Th key={colIndex} className={`text-center`}>
                            {col?.columnName}
                          </Table.Th>
                        ))}
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {Array.from({ length: field?.tableOptions?.rows }).map(
                        (_, bodyRowIndex) => (
                          <Table.Row key={bodyRowIndex}>
                            <Table.Td className={`text-center`}>
                              {bodyRowIndex + 1}
                            </Table.Td>
                            {field?.tableOptions?.column?.map(
                              (col, colIndex) => {
                                if (col?.columnType === 'string') {
                                  return (
                                    <Table.Td
                                      key={colIndex}
                                      className={`text-center`}
                                    >
                                      <Input
                                        type="string"
                                        onChange={(e) => {
                                          setFormData((prev) => ({
                                            ...prev,
                                            [field?.fieldName]: {
                                              ...prev[field?.fieldName],
                                              ['rowData']: {
                                                ...prev[field?.fieldName][
                                                  'rowData'
                                                ],
                                                [bodyRowIndex + 1]: prev?.[
                                                  field?.fieldName
                                                ]?.['rowData']?.[
                                                  bodyRowIndex + 1
                                                ]?.map((itm, index) =>
                                                  index !== colIndex
                                                    ? itm
                                                    : {
                                                        ...itm,
                                                        value: e.target.value,
                                                        type: 'string',
                                                      }
                                                ),
                                              },
                                            },
                                          }));
                                        }}
                                        value={
                                          formData[field?.fieldName]?.[
                                            'rowData'
                                          ]?.[bodyRowIndex]?.value
                                        }
                                        className={`min-w-24 w-full`}
                                      />
                                    </Table.Td>
                                  );
                                } else if (col?.columnType === 'number') {
                                  return (
                                    <Table.Td
                                      key={colIndex}
                                      className={`text-center`}
                                    >
                                      <Input
                                        type="number"
                                        onChange={(e) => {
                                          setFormData((prev) => ({
                                            ...prev,
                                            [field?.fieldName]: {
                                              ...prev[field?.fieldName],
                                              ['rowData']: {
                                                ...prev[field?.fieldName][
                                                  'rowData'
                                                ],
                                                [bodyRowIndex + 1]: prev?.[
                                                  field?.fieldName
                                                ]?.['rowData']?.[
                                                  bodyRowIndex + 1
                                                ]?.map((itm, index) =>
                                                  index !== colIndex
                                                    ? itm
                                                    : {
                                                        ...itm,
                                                        value: e.target.value,
                                                        type: 'number',
                                                      }
                                                ),
                                              },
                                            },
                                          }));
                                        }}
                                        value={
                                          formData[field?.fieldName]?.[
                                            'rowData'
                                          ]?.[bodyRowIndex]?.value
                                        }
                                        className={`min-w-24 w-full`}
                                      />
                                    </Table.Td>
                                  );
                                } else if (col?.columnType === 'date') {
                                  return (
                                    <Table.Td
                                      key={colIndex}
                                      className={`text-center`}
                                    >
                                      <Input
                                        type="date"
                                        onChange={(e) => {
                                          setFormData((prev) => ({
                                            ...prev,
                                            [field?.fieldName]: {
                                              ...prev[field?.fieldName],
                                              ['rowData']: {
                                                ...prev[field?.fieldName][
                                                  'rowData'
                                                ],
                                                [bodyRowIndex + 1]: prev?.[
                                                  field?.fieldName
                                                ]?.['rowData']?.[
                                                  bodyRowIndex + 1
                                                ]?.map((itm, index) =>
                                                  index !== colIndex
                                                    ? itm
                                                    : {
                                                        ...itm,
                                                        value: e.target.value,
                                                        type: 'date',
                                                      }
                                                ),
                                              },
                                            },
                                          }));
                                        }}
                                        value={
                                          formData[field?.fieldName]?.[
                                            'rowData'
                                          ]?.[bodyRowIndex]?.value
                                        }
                                        className={`min-w-24 w-full`}
                                      />
                                    </Table.Td>
                                  );
                                } else if (col?.columnType === 'checkbox') {
                                  return (
                                    <Table.Td
                                      key={colIndex}
                                      className={`text-center`}
                                    >
                                      <Input
                                        type="checkbox"
                                        value={
                                          formData[field?.fieldName]?.[
                                            'rowData'
                                          ]?.[bodyRowIndex]?.value
                                        }
                                        className={`min-w-24 w-full`}
                                      />
                                    </Table.Td>
                                  );
                                } else {
                                  return (
                                    <Table.Td key={colIndex}>
                                      <span>Unknown Type</span>
                                    </Table.Td>
                                  );
                                }
                              }
                            )}
                          </Table.Row>
                        )
                      )}
                    </Table.Body>
                  </Table>
                </div>
              </div>
            );
          }
        })}
      </div>

      <div className="w-full flex justify-end">
        <Button
          type="button"
          onClick={handleSave}
          disabled={saveDisabled}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-4 mr-4 w-fit"
        >
          Save
        </Button>
      </div>
    </div>
  );
};

export default AdditionalFieldRender;
