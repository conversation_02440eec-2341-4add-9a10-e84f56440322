import { useState } from 'react';
import Modal from './Modal';

const ImageStackButton = ({ images }) => {
  const [isOpen, setIsOpen] = useState(false);

  const togglePreview = () => setIsOpen(!isOpen);

  return (
    <div>
      <div
        className="flex -space-x-7 cursor-pointer transition-all duration-1000 ease-in-out hover:-space-x-3"
        onClick={togglePreview}
      >
        {images.slice(0, 2).map((image, index) => (
          <img
            key={index}
            src={image}
            alt={`Preview ${index + 1}`}
            className="w-10 h-10 rounded-full border-2 border-white shadow-md"
            style={{ zIndex: images.length - index }}
          />
        ))}
        {images.length > 2 && (
          <span className="w-10 h-10 rounded-full bg-gray-300 text-xs flex items-center justify-center border-2 border-white shadow-md">
            +{images.length - 2}
          </span>
        )}
      </div>

      {isOpen && (
        <Modal
          title="Attachments"
          showModal={isOpen}
          onCloseModal={() => {
            setIsOpen(false);
          }}
          isSubmitRequired={false}
          canSubmit={false}
          //   modalWidth="30%"
          //   modalTop="78.5%"
          //   modalLeft="10%"
        >
          {() => {
            return (
              <div className="grid grid-cols-2 gap-4 mt-4 pt-2   ">
                {images?.map((image, index) => (
                  <div
                    key={index}
                    className="relative group flex justify-center"
                  >
                    <img
                      src={image}
                      alt={`Full view ${index + 1}`}
                      className="w-[70%] h-full rounded-lg transform transition-transform duration-200 group-hover:scale-105 shadow-md object-contain"
                    />
                  </div>
                ))}
              </div>
            );
          }}
        </Modal>
      )}
    </div>
  );
};

export default ImageStackButton;
