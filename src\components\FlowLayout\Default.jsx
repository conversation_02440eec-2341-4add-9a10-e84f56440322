import {
  ArrowRightOutlined,
  CheckCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { Button, Col, Progress, Row, Space, Tag, Typography } from 'antd';
import { motion } from 'framer-motion';
import {
  Clock,
  Package,
  Pause,
  Play,
  Settings,
  TrendingDown,
  TrendingUp,
  Zap,
} from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import Marquee from 'react-fast-marquee';
import { BsExclamationTriangle as ExclamationTriangleIcon } from 'react-icons/bs';
import { useDispatch } from 'react-redux';

import {
  calculateValueForViews,
  convertToHrsAndMins,
  getLocalDateTime,
  getMinutesPassed,
  handlePercentDeicmalValue,
} from '../../helperFunction';
import useLeadingLagging from '../../hooks/useLeadingLagging';
import { MqttContext } from '../../mqtt/DashboardMqttContext';
import { useLazyGetCuNotificationQuery } from '../../slices/cuNotificationApiSlice';
import { handelSidebarAndErroData } from '../../slices/processErrorSlice';
import { Store } from '../../store/Store';
import { getLastValue } from '../../utils/getLastValue';

const { Title, Text } = Typography;

export default function Default({
  data,
  cuProjects,
  nextCuProjects,
  processGoalViews,
  deviceDatas,
  isLastItem,
  reportData,
  setReportData,
  setAllSelectedMachines,
  setOpenSideBar,
  qcSampleData,
  setShowErrorSidebar,
  setAddtionalFields,
}) {
  const [realtimeBatchSize, setRealtimeBatchSize] = useState(0);
  const [newValues, setNewValues] = useState({});
  const [haltData, setHaltData] = useState({ hrs: 0, mins: 0, status: false });
  const [qcProgressPercentage, setQCProgressPercentage] = useState(0);
  const [stopBatchSize, setStopBatchSize] = useState(0);
  // const [ExpectedEndTime, setExpectedEndTime] = useState(null);
  const [Errors, setErrors] = useState([]);

  const dispatch = useDispatch();

  const [getCuNotification] = useLazyGetCuNotificationQuery();

  const { values } = useContext(MqttContext);
  const {
    defaults: { defaultParam },
    refreshPrevTile,
  } = useContext(Store);

  const cuProject = cuProjects?.findLast((i) => i);

  const nextFlowCuPro = nextCuProjects?.findLast((i) => i);

  const isComplete = cuProject?.status === 'complete';

  const isQC = cuProject?.type === 'QC';
  const isAssem = cuProject?.type === 'Assembly';

  const batchSizeParam = processGoalViews
    ?.find((item) => item?.project?.projectId === cuProject?.mqtt?._id)
    ?.parameters?.find((item) => item.name === 'Batch Size');

  const machineList = cuProject?.machineAndOperator?.map(
    (mao) => mao?.machine?.machineId
  );

  const batchData = cuProject?.isMultiProcess
    ? cuProject?.subProcessData
    : cuProject?.batchInfo;

  const flow = cuProject?.productionFlow?.processes?.find((flow) => {
    return flow._id === cuProject?.flowId;
  });

  const idleTimesData =
    deviceDatas?.filter(
      (item) =>
        item?.cuProject === cuProject?._id &&
        item.type === 'kpi' &&
        item.data.hasOwnProperty('IDLE') // eslint-disable-line
    ) || [];

  const idleTime =
    idleTimesData?.reduce((acc, curVal) => acc + +curVal?.data?.IDLE, 0) / 60;

  const {
    status,
    timeDifference,
    realTimeItemsPerHour = 0,
    completionDateTime,
    hpsst,
  } = useLeadingLagging(
    batchData,
    realtimeBatchSize,
    flow,
    isComplete,
    cuProject?.stopTime
  );

  const tempUptime = hpsst - idleTime;

  const uptime = convertToHrsAndMins(tempUptime);
  const downtime = convertToHrsAndMins(idleTime);

  useEffect(() => {
    if (cuProject?.stopTime && nextFlowCuPro?.startTime) {
      const cuStop = new Date(cuProject?.stopTime);
      const nextCuStart = new Date(nextFlowCuPro?.startTime);

      if (nextCuStart > cuStop) {
        const totalMinutesPassed = getMinutesPassed(
          nextCuStart,
          cuStop,
          defaultParam,
          true
        );

        const [hrs, mins] = convertToHrsAndMins(totalMinutesPassed / 60);
        setHaltData({ hrs, mins, status: true });
      } else {
        setHaltData({ hrs: 0, mins: 0, status: false });
      }
    } else {
      setHaltData({ hrs: 0, mins: 0, status: false });
    }
  }, [cuProject, nextFlowCuPro, defaultParam]);

  const batchSize =
    cuProject?.batchInfo?.newBatchSize || cuProject?.batchInfo['Batch Size'];

  const noOfSamples =
    Math.floor(
      batchSize /
        qcSampleData?.refData?.interval?.[+cuProject?.batchInfo?.batchNo - 1]
    ) * qcSampleData?.refData?.samples?.[+cuProject?.batchInfo?.batchNo - 1] ||
    1;

  useEffect(() => {
    let manualCount = 0;
    let val = 0;
    const manualMachines = cuProject?.machineAndOperator?.filter(
      (mao) => mao?.machine?.isManual
    );

    manualMachines?.forEach((mac) => {
      manualCount =
        manualCount +
        (mac?.manualStopData || mac?.manualPauseData?.findLast((i) => i) || 0);
    });

    if (batchSizeParam && machineList && newValues) {
      val = calculateValueForViews(
        batchSizeParam?.formula,
        newValues,
        machineList
      );
    }
    setRealtimeBatchSize(val + manualCount || 0);
  }, [batchSizeParam, machineList, newValues, cuProject?.machineAndOperator]);

  useEffect(() => {
    const project = cuProjects?.findLast((i) => i);
    if (project !== undefined) {
      let temp = reportData.find((elem) => elem?.processId === data?._id);
      if (temp !== undefined) {
        temp.count = realtimeBatchSize || 0;
        temp.speed = realTimeItemsPerHour
          ? isFinite(realTimeItemsPerHour)
            ? realTimeItemsPerHour
            : 0
          : 0;
        setErrors(project);
        temp.pauses = project?.errorMessages;
        temp.errors = cuProject?.errorMessages?.length || 0;
        temp.start = getLocalDateTime(cuProject?.startTime);
        temp.expected = getLocalDateTime(completionDateTime) || '-';
        temp.uptime = `${uptime?.[0]}h ${uptime?.[1]}m`;
        temp.downtime = `${downtime?.[0]}h ${downtime?.[1]}m`;
        temp.machine = machineId;
        temp.worker = worker?.name;
        temp.status = project.status;
        temp.leadingOrlagging = `${status} by ${timeDifference?.hrs}hrs ${timeDifference?.mins}mins`;
        temp.pass = cuProject?.qcData?.passData?.quantity;
        temp.rework = cuProject?.qcData?.reworkData?.quantity;
        temp.scrap = cuProject?.qcData?.scrapData?.quantity;

        // CalculateExpectedTime(
        //   temp?.start,
        //   stopBatchSize - realtimeBatchSize,
        //   realTimeItemsPerHour
        // );
        let newReportData = reportData.map((elem) => {
          if (elem?.processId === data?._id) return temp;
          else return elem;
        });

        setReportData(newReportData);
      }
    }
  }, [cuProjects._id, batchData, realtimeBatchSize, realTimeItemsPerHour]); // eslint-disable-line

  useEffect(() => {
    if (cuProject && values) {
      cuProject?.machineAndOperator?.forEach((mao) => {
        const isActive = mao?.status === 'active';

        const machine = mao.machine;

        machine?.devices?.forEach((device) => {
          device?.assignedDevice?.forEach((item) => {
            if (isActive) {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]: values?.[item?.deviceId] || {},
              }));
            } else {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]:
                  cuProject?.lastValues?.[machine?.machineId]?.[
                    item?.deviceId
                  ] || {},
              }));
            }
          });
        });
      });
    }
  }, [cuProject, values]);

  useEffect(() => {
    if (data?.mqtt?.machineid?.id) {
      (async () => {
        const res = await getCuNotification({
          id: data?.mqtt?.machineid?.id,
        }).unwrap();
        if (res?.response?.length > 0) {
          res?.response.forEach((el) => {
            if (
              el?.action === 'stop' &&
              Number(el?.batchNo) === cuProject?.batchInfo?.batchNo
            ) {
              setStopBatchSize(el?.prevBatchSize || 0);
              return;
            }
          });
        }
        setStopBatchSize(
          cuProject?.batchInfo?.newBatchSize ||
            cuProject?.batchInfo['Batch Size']
        );
      })();
    }
    // eslint-disable-next-line
  }, [
    data?.mqtt?.machineid,
    cuProject?.batchInfo?.batchNo,
    refreshPrevTile,
    getCuNotification,
  ]);

  const isPaused = !!cuProject?.machineAndOperator?.find(
    (el) => el?.status === 'pause' && el?.status !== 'active'
  );

  let worker = isQC
    ? cuProject?.qcData?.operator
    : isAssem
      ? cuProject?.assemblyData?.operator
      : getLastValue(cuProject?.machineAndOperator)?.operator?.[
          cuProject.machineAndOperator[cuProject.machineAndOperator.length - 1]
            .operator.length - 1
        ]?.user;

  let machineId = getLastValue(cuProject?.machineAndOperator)?.machine
    ?.machineId;

  let machineIds = cuProject?.machineAndOperator?.map(
    (el) => el?.machine?.machineId
  );

  const textColor =
    cuProject?.status === 'active' && !isPaused
      ? '#77DD77'
      : cuProject?.status === 'complete' && !isPaused
        ? '#0070FF'
        : isPaused
          ? '#d9b00e'
          : '#ED4040';
  const darkBg =
    cuProject?.status === 'active' && !isPaused
      ? '#77DD77'
      : cuProject?.status === 'complete' && !isPaused
        ? '#0070FF'
        : isPaused
          ? '#F1C40F'
          : '#ED4040';

  const progressPercent =
    (data?.mqtt?.category === 'Inhouse'
      ? realtimeBatchSize /
        (batchData?.newBatchSize || batchData?.['Batch Size'])
      : data?.mqtt?.category === 'Assembly'
        ? cuProject?.assemblyData?.samplingBatchSize / batchSize
        : data?.mqtt?.category === 'QC'
          ? !qcSampleData?.refData?.type ||
            qcSampleData?.refData?.type === 'None'
            ? cuProject?.qcData?.passData?.quantity / batchSize
            : cuProject?.qcData?.samplingBatchSize / batchSize
          : cuProject?.outSourceData?.inwardSize / batchSize) * 100;

  let progress =
    handlePercentDeicmalValue(progressPercent > 100 ? 100 : progressPercent) ||
    0;

  useEffect(() => {
    if (
      !qcSampleData?.refData?.type ||
      qcSampleData?.refData?.type === 'None'
    ) {
      const tempQcProgressPercentage =
        (cuProject?.qcData?.passData?.quantity / noOfSamples) * 100;

      let QCProgress = handlePercentDeicmalValue(
        tempQcProgressPercentage > 100 ? 100 : tempQcProgressPercentage
      );

      setQCProgressPercentage(QCProgress || 0);
    } else {
      let progressPercent =
        (cuProject?.qcData?.samplingBatchSize / noOfSamples) * 100;
      let progress =
        handlePercentDeicmalValue(
          progressPercent > 100 ? 100 : progressPercent
        ) || 0;
      setQCProgressPercentage(progress || 0);
    }
  }, [
    cuProject?.qcData?.passData?.quantity,
    cuProject?.qcData?.samplingBatchSize,
    noOfSamples,
    qcSampleData?.refData?.type,
  ]);

  const handleExtraMachineClick = async () => {
    const targettedData = cuProject?.machineAndOperator?.map((el) => ({
      machineId: el?.machine?.machineId,
      startTime: el?.startTime,
      workerName: getLastValue(el?.operator)?.user?.name,
      noOfPauses: el?.pauseTime?.length,
    }));
    setAllSelectedMachines(targettedData);
    setOpenSideBar(true);
  };

  const getScrapValue = () => {
    if (data?.mqtt?.category === 'Inhouse') {
      return (
        (batchData?.newBatchSize || batchData?.['Batch Size']) -
        realtimeBatchSize
      );
    } else if (data?.mqtt?.category === 'QC') {
      return cuProject?.qcData?.scrapData?.quantity;
    } else {
      return batchSize - (cuProject?.outSourceData?.inwardSize || 0);
    }
  };

  return (
    <div className="flex items-center gap-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`w-72 h-[600px] rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group relative overflow-hidden flex flex-col ${
          cuProject?.status === 'active' && !isPaused
            ? 'bg-emerald-50 border-emerald-200'
            : cuProject?.status === 'complete' && !isPaused
              ? 'bg-blue-50 border-blue-200'
              : isPaused
                ? 'bg-yellow-50 border-yellow-200'
                : 'bg-gray-50 border-gray-200'
        }`}
        onClick={() => {
          setAddtionalFields(cuProject?.additionalFields || {});
          setShowErrorSidebar((prev) => !prev);
          dispatch(handelSidebarAndErroData({ data: Errors }));
        }}
      >
        {/* Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent pointer-events-none" />

        {/* Header */}
        <div className="px-4 pt-4 pb-2 border-b border-gray-100 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isPaused ? (
                <Pause className="w-4 h-4 text-yellow-600" />
              ) : cuProject?.status === 'complete' ? (
                <CheckCircleOutlined className="w-4 h-4 text-blue-600" />
              ) : cuProject?.status === 'active' ? (
                <Play className="w-4 h-4 text-emerald-600" />
              ) : (
                <Clock className="w-4 h-4 text-gray-400" />
              )}
              <Tag color={textColor} className="text-xs font-semibold">
                {isPaused
                  ? 'PAUSED'
                  : cuProject?.status === 'complete'
                    ? 'COMPLETED'
                    : cuProject?.status === 'active'
                      ? 'ACTIVE'
                      : 'INACTIVE'}
              </Tag>
            </div>
            <div className="text-xs text-gray-500 font-medium">
              {data.mqtt.category}
            </div>
          </div>

          <div className="h-6 overflow-hidden">
            <Title level={4} className="m-0 truncate group-hover:animate-pulse">
              {data.processName}
            </Title>
          </div>

          <Text type="secondary" className="text-sm truncate">
            {data?.mqtt?.machineId?.cuId || ''}
          </Text>
        </div>

        {cuProject ? (
          <div className="flex-1 flex flex-col p-4">
            {/* Progress Circle */}
            <div className="flex items-center justify-center mb-2">
              <Progress
                type="circle"
                percent={qcSampleData ? qcProgressPercentage : progress}
                strokeColor={darkBg}
                strokeWidth={8}
                size={80}
              />
            </div>

            <div className="flex-1 flex flex-col">
              {/* Category-specific Metrics */}
              <div className="mb-4 h-14">
                {data?.mqtt?.category === 'Inhouse' && (
                  <div className="grid grid-cols-3 gap-2 h-full">
                    <div className="text-center p-2 bg-white/60 rounded-lg flex flex-col justify-center">
                      <div className="flex items-center justify-center mb-1">
                        <Package className="w-3 h-3 text-blue-500" />
                      </div>
                      <div className="text-xs text-gray-600">Count</div>
                      <div className="text-sm font-bold">
                        {realtimeBatchSize || 0}
                      </div>
                    </div>
                    <div className="text-center p-2 bg-white/60 rounded-lg flex flex-col justify-center">
                      <div className="flex items-center justify-center mb-1">
                        <Zap className="w-3 h-3 text-emerald-500" />
                      </div>
                      <div className="text-xs text-gray-600">Speed</div>
                      <div className="text-sm font-bold">
                        {isFinite(realTimeItemsPerHour)
                          ? realTimeItemsPerHour
                          : 'N/A'}
                      </div>
                    </div>
                    <div className="text-center p-2 bg-white/60 rounded-lg flex flex-col justify-center">
                      <div className="flex items-center justify-center mb-1">
                        <ExclamationTriangleIcon className="w-3 h-3 text-amber-500" />
                      </div>
                      <div className="text-xs text-gray-600">Errors</div>
                      <div className="text-sm font-bold">
                        {cuProject?.errorMessages?.length || 0}
                      </div>
                    </div>
                  </div>
                )}

                {data?.mqtt?.category === 'QC' && (
                  <div className="grid grid-cols-3 gap-2 h-full">
                    <div className="text-center p-2 bg-emerald-50 rounded-lg border border-emerald-100">
                      <div className="text-xs text-emerald-600 font-medium">
                        Pass
                      </div>
                      <div className="text-sm font-bold text-emerald-700">
                        {cuProject?.qcData?.passData?.quantity || 0}
                      </div>
                    </div>
                    <div className="text-center p-2 bg-amber-50 rounded-lg border border-amber-100">
                      <div className="text-xs text-amber-600 font-medium">
                        Rework
                      </div>
                      <div className="text-sm font-bold text-amber-700">
                        {cuProject?.qcData?.reworkData?.quantity || 0}
                      </div>
                    </div>
                    <div className="text-center p-2 bg-red-50 rounded-lg border border-red-100">
                      <div className="text-xs text-red-600 font-medium">
                        Scrap
                      </div>
                      <div className="text-sm font-bold text-red-700">
                        {cuProject?.qcData?.scrapData?.quantity || 0}
                      </div>
                    </div>
                  </div>
                )}

                {data?.mqtt?.category === 'Outsource' && (
                  <div className="grid grid-cols-2 gap-3 h-full">
                    <div className="text-center p-2 bg-white/60 rounded-lg">
                      <div className="text-xs text-gray-600">Outward</div>
                      <div className="text-sm font-bold">{batchSize || 0}</div>
                    </div>
                    <div className="text-center p-2 bg-white/60 rounded-lg">
                      <div className="text-xs text-gray-600">Inward</div>
                      <div className="text-sm font-bold">
                        {cuProject?.outSourceData?.inwardSize || 0}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <Marquee
                pauseOnHover
                style={{
                  backgroundColor: '#fff',
                  color: '#333',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  fontSize: '14px',
                  fontWeight: 500,
                }}
              >
                {data?.processName}
                {cuProject?.subProcessData?.process && (
                  <span
                    style={{ fontSize: '12px', marginLeft: 8, opacity: 0.8 }}
                  >
                    ({cuProject?.subProcessData?.process})
                  </span>
                )}
                <span style={{ marginLeft: 12 }}>
                  {data?.mqtt?.machineid?.cuId || ''}
                </span>
                {!!qcSampleData && (
                  <span style={{ marginLeft: 12 }}>
                    Sampled: {qcSampleData?.refData?.type}
                  </span>
                )}
              </Marquee>
              {/* Time Info */}
              <div className="text-xs mb-2">
                {completionDateTime && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Expected:</span>
                    <span className="font-medium">
                      {getLocalDateTime(completionDateTime)}
                    </span>
                  </div>
                )}
              </div>

              {/* Bottom section */}
              <div className="flex-1 flex flex-col justify-end  mb-2">
                {/* Uptime/Downtime */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center gap-2 p-2 bg-emerald-50 rounded-lg">
                    <TrendingUp className="w-3 h-3 text-emerald-600 flex-shrink-0" />
                    <div>
                      <div className="text-xs text-emerald-600">Uptime</div>
                      <div className="text-sm font-bold text-emerald-700">
                        {uptime?.[0]}h {uptime?.[1]}m
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-red-50 rounded-lg">
                    <TrendingDown className="w-3 h-3 text-red-600 flex-shrink-0" />
                    <div>
                      <div className="text-xs text-red-600">Downtime</div>
                      <div className="text-sm font-bold text-red-700">
                        {downtime?.[0]}h {downtime?.[1]}m
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Timeline and Details */}
            <div style={{ padding: '0 20px 20px' }}>
              <Space
                direction="vertical"
                style={{ width: '100%' }}
                size="middle"
              >
                <div>
                  {' '}
                  {[
                    {
                      label: 'Start',
                      value: getLocalDateTime(cuProject?.startTime),
                    },
                    {
                      label: 'Expected',
                      value: completionDateTime
                        ? getLocalDateTime(completionDateTime)
                        : '-',
                    },
                    {
                      label: 'Batch Size',
                      value: stopBatchSize?.toString() || batchSize,
                    },
                  ].map((item, idx) => (
                    <Row
                      key={idx}
                      justify="space-between"
                      style={{ marginBottom: 8 }}
                    >
                      <Col>
                        <Text
                          type="secondary"
                          style={{ fontSize: '13px', fontWeight: 500 }}
                        >
                          {item.label}:
                        </Text>
                      </Col>
                      <Col>
                        <Text strong style={{ fontSize: '13px' }}>
                          {item.value}
                        </Text>
                      </Col>
                    </Row>
                  ))}
                </div>

                {/* Uptime/Downtime */}
                <Row gutter={16}>
                  <Col span={12}>
                    <Space align="center" style={{ marginBottom: 4 }}>
                      <TrendingUp style={{ color: '#52c41a', fontSize: 14 }} />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        Uptime
                      </Text>
                    </Space>
                    <div>
                      <Text
                        strong
                        style={{ fontSize: '14px', color: '#52c41a' }}
                      >
                        {uptime?.[0]}h {uptime?.[1]}m
                      </Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <Space align="center" style={{ marginBottom: 4 }}>
                      <TrendingDown
                        style={{ color: '#ff4d4f', fontSize: 14 }}
                      />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        Downtime
                      </Text>
                    </Space>
                    <div>
                      <Text
                        strong
                        style={{ fontSize: '14px', color: '#ff4d4f' }}
                      >
                        {downtime?.[0]}h {downtime?.[1]}m
                      </Text>
                    </div>
                  </Col>
                </Row>

                {/* Machine and Worker Info */}
                <Row gutter={16}>
                  <Col span={12}>
                    <Text
                      type="secondary"
                      style={{ fontSize: '12px', fontWeight: 500 }}
                    >
                      {qcSampleData
                        ? qcSampleData?.refData?.type === 'Percentage'
                          ? 'Percentage'
                          : 'No of Samples'
                        : 'Machine'}
                    </Text>
                    <div>
                      {isQC ? (
                        <Text strong style={{ fontSize: '13px' }}>
                          {qcSampleData?.refData?.type === 'Percentage'
                            ? `${qcSampleData?.refData?.percent}%`
                            : noOfSamples}
                        </Text>
                      ) : (
                        <Button
                          type="link"
                          size="small"
                          style={{
                            padding: 0,
                            height: 'auto',
                            fontSize: '13px',
                            fontWeight: 600,
                          }}
                          onClick={handleExtraMachineClick}
                        >
                          {machineIds?.[0]}
                          {[...new Set(machineIds)].length > 1 && (
                            <Text type="secondary" style={{ marginLeft: 4 }}>
                              + ({[...new Set(machineIds)].length - 1})
                            </Text>
                          )}
                        </Button>
                      )}
                    </div>
                  </Col>
                  <Col span={12}>
                    <Text
                      type="secondary"
                      style={{ fontSize: '12px', fontWeight: 500 }}
                    >
                      Worker
                    </Text>
                    <div>
                      <Text strong style={{ fontSize: '13px' }}>
                        {worker?.name || '-'}
                      </Text>
                    </div>
                  </Col>
                </Row>
              </Space>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center flex-1 text-center p-4">
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
              <Settings className="w-6 h-6 text-gray-400" />
            </div>
            <Text strong className="text-gray-600 mb-1">
              No Active Project
            </Text>
            <Text type="secondary" className="text-sm">
              Waiting to start
            </Text>
          </div>
        )}
      </motion.div>

      {/* Flow Arrow */}
      {!isLastItem && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="flex flex-col items-center gap-2"
        >
          <div className={`flex items-center `}>
            <div className="w-8 h-0.5 bg-gradient-to-r from-blue-400 to-purple-500 animate-pulse"></div>
            <div
              className={`mx-2 p-2 rounded-full bg-white shadow-lg border-2 border-blue-200  `}
            >
              <ArrowRightOutlined className="text-blue-500 text-lg" />
            </div>
            <div className="w-8 h-0.5 bg-gradient-to-r from-purple-500 to-blue-400 animate-pulse"></div>
          </div>
          <div className="flex flex-col gap-3">
            {(getScrapValue() > 0 || cuProject?.stopLocation) && (
              <div className="min-w-32 border-0 shadow-lg rounded-xl bg-white/90 backdrop-blur-md">
                <div className="text-center w-full">
                  {getScrapValue() > 0 && (
                    <div className="flex items-center gap-2 justify-center py-1">
                      <WarningOutlined className="text-red-500 text-xs" />
                      <Text className="text-xs text-red-600 font-medium">
                        {getScrapValue()} scrapped
                      </Text>
                    </div>
                  )}
                  {cuProject?.stopLocation && (
                    <Text className="text-xs text-gray-600">
                      → {cuProject.stopLocation.locationName}
                    </Text>
                  )}
                </div>
              </div>
            )}

            {haltData.status && (
              <div className="min-w-24 border-0 shadow-lg rounded-xl bg-yellow-50 border-yellow-200">
                <div className="text-center">
                  <Text className="text-xs text-yellow-700 font-medium">
                    ⏸️ Halt: {haltData.hrs}h {haltData.mins}m
                  </Text>
                </div>
              </div>
            )}

            {isComplete && (
              <div className="flex items-center gap-2 bg-green-50 px-3 py-2 rounded-xl border border-green-200">
                <CheckCircleOutlined className="text-green-500 text-sm" />
                <Text className="text-xs text-green-700 font-medium">
                  Complete
                </Text>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
}
