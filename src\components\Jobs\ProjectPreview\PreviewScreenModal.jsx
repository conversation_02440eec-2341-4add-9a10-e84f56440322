import { Fragment, useContext, useEffect, useState } from 'react';
import Marquee from 'react-fast-marquee';
import pdf from '../../../assets/images/pdf.png';
import { getLocalDateTime, isObjectEmpty } from '../../../helperFunction';
import { Store } from '../../../store/Store';
import Table from '../../global/components/Table';
import PreviewModal from '../../v3/global/components/PreviewModal';
import AssignedAssemblyBom from '../AssignedAssemblyBom';
import JobDetailsView from './JobDetailsView';

const KeyValueComponent = ({ label, value }) => {
  return (
    <div className="w-full">
      <label className="text-sm font-medium ">{label}</label>
      <p className="text-xs">{value}</p>
    </div>
  );
};

const PreviewScreenModal = ({
  isOpen,
  handleSuccess,
  handleClose,
  allData,
  // selectedPo,
  parameters,
  modalData,
  goalsTables,
  processes,
  imageURLs,
  // bom,
  allQCData,
  newLocalData,
  inputScreen,
  selectProductionFlows,
  productionFlows,
  jobId,
  modelName,
  allQCForms,
  formData,
  isLoading,
}) => {
  const [filteredProcesses, setFilteredProcesses] = useState([]);

  const outterObj = isObjectEmpty(parameters)
    ? {}
    : Object.values(parameters)[0];

  const qcProcess = processes?.filter((itm) => itm.processCategory === 'QC');

  const batchHeaders = [
    'Batch No',
    'Batch Size',
    'Items per hour',
    'Changes Over Time',
    'Start Date',
    'Stop Date',
    'Duration',
  ];

  useEffect(() => {
    let unqPro = [];
    processes?.forEach((pro) => {
      const exists = unqPro?.find((i) => i?._id === pro?.mqtt?._id);
      if (!exists) {
        unqPro.push(pro.mqtt);
      }
    });

    setFilteredProcesses(unqPro);
  }, [processes]);

  const { defaults } = useContext(Store);

  // const renderTable = (data) => {
  //   return (
  //     <Table>
  //       <Table.Head>
  //         <Table.Row>
  //           <Table.Th>#</Table.Th>
  //           <Table.Th>Part</Table.Th>
  //           <Table.Th>Bom Qty</Table.Th>
  //           <Table.Th>Order Qty</Table.Th>
  //           <Table.Th className={'w-1/2'}>No. of Process</Table.Th>
  //         </Table.Row>
  //       </Table.Head>
  //       <Table.Body>
  //         {data?.map((item, iIdx) => (
  //           <Table.Row key={iIdx}>
  //             <Table.Td>{iIdx + 1}</Table.Td>
  //             <Table.Td>{item?.detail?.name}</Table.Td>
  //             <Table.Td>{item?.bomQty}</Table.Td>
  //             <Table.Td>{item?.orderQty}</Table.Td>
  //             <Table.Td>{item?.process?.length}</Table.Td>
  //           </Table.Row>
  //         ))}
  //       </Table.Body>
  //     </Table>
  //   );
  // };

  return (
    <PreviewModal
      isOpen={isOpen}
      handleClose={handleClose}
      handleSuccess={handleSuccess}
      isLoading={isLoading}
      name="Preview Jobs"
    >
      <div className="p-4 space-y-6">
        {/* Job Details */}
        <div className="space-y-4 w-full">
          <h1 className="text-xl font-semibold">Job Details</h1>
          <div className="w-full grid-cols-3 grid grid-rows-1 justify-between items-center gap-4">
            <KeyValueComponent
              label="Work Order"
              value={allData?.workOrderId}
            />
            <KeyValueComponent
              label={defaults?.defaultParam?.projectDefaults?.modelLabel}
              value={modelName}
            />
            <KeyValueComponent
              label={defaults?.defaultParam?.projectDefaults?.projectIdentifier}
              value={jobId}
            />
            <KeyValueComponent
              label={defaults?.defaultParam?.projectDefaults?.inputScreenLabel}
              value={`${inputScreen?.name} (${
                productionFlows.find((el) => el._id === selectProductionFlows)
                  ?.name
              })`}
            />
            <KeyValueComponent
              label="Production Flow"
              value={
                productionFlows.find((el) => el._id === selectProductionFlows)
                  ?.name
              }
            />
          </div>
        </div>

        {/* Order Details */}
        <div className="space-y-4">
          <h1 className="text-xl font-semibold">Order Details</h1>
          <div className="w-full grid-cols-3 grid grid-rows-2 gap-4">
            {Object.keys(outterObj)?.map((el, index) => (
              <KeyValueComponent
                key={index}
                label={el}
                value={Object.values(outterObj)[index]}
              />
            ))}
          </div>
        </div>

        {/* Raw Material */}
        <div className="space-y-4">
          <h1 className="text-xl font-semibold">Raw Material</h1>
          <Table className="mt-4">
            <Table.Head>
              <Table.Row>
                <Table.Th>#</Table.Th>
                <Table.Th>RM No.</Table.Th>
                <Table.Th>Description</Table.Th>
                <Table.Th>Pdf's</Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {modalData?.map((data, dIdx) => (
                <Table.Row key={dIdx}>
                  <Table.Td>{dIdx + 1}</Table.Td>
                  <Table.Td>{data?.rmNo}</Table.Td>
                  <Table.Td>{data?.rmDesc}</Table.Td>
                  <Table.Td>
                    {data?.files?.map((file) => file.fname).join(', ')}
                  </Table.Td>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>

        {/* Batch Planning */}
        <div className="space-y-4">
          <h1 className="text-xl font-semibold">Batch Planning</h1>

          {goalsTables?.map((el, eIdx) => {
            const targettedProcess = processes?.find(
              (process) => process._id === el.flowId
            );
            return (
              <Fragment key={eIdx}>
                <h5 className="mt-4 mb-1">{targettedProcess?.processName}</h5>
                <Table>
                  <Table.Head>
                    <Table.Row>
                      {batchHeaders.map((el, index) => {
                        return <Table.Th key={index}>{el}</Table.Th>;
                      })}
                    </Table.Row>
                  </Table.Head>
                  <Table.Body>
                    {el?.tableData?.map((currElem, index) => (
                      <Table.Row key={index}>
                        <Table.Td>{currElem.batchName || '-'}</Table.Td>
                        <Table.Td>{currElem['Batch Size'] || '-'}</Table.Td>
                        <Table.Td>{currElem.itemsPerHour || 0}</Table.Td>
                        <Table.Td>{currElem.changeOverTime || 0}</Table.Td>
                        <Table.Td>
                          {getLocalDateTime(currElem.startDate) || '-'}
                        </Table.Td>
                        <Table.Td>
                          {getLocalDateTime(currElem.stopDate) || '-'}
                        </Table.Td>
                        <Table.Td>{currElem.duration || '-'}</Table.Td>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </Fragment>
            );
          })}
        </div>

        {/* Media */}
        <div className="space-y-4">
          <h1 className="text-xl font-semibold">Media</h1>
          {filteredProcesses.map((process) => {
            return (
              imageURLs?.[process?._id]?.length > 0 && (
                <div>
                  <h3 className="text-sm mb-2">{process.process}</h3>
                  <div key={process._id} className="flex flex-wrap gap-4">
                    {imageURLs?.[process._id]?.map((el, idX) => (
                      <section
                        key={idX}
                        className="p-2 border rounded-md w-[170px] flex flex-col justify-between"
                      >
                        <section className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer ">
                          <img
                            className="w-full aspect-video object-contain"
                            src={
                              el?.type === 'application/pdf' ? pdf : el?.data
                            }
                            alt=""
                          />
                        </section>
                        <Marquee className="w-full overflow-hidden">
                          {el.name}
                        </Marquee>
                      </section>
                    ))}
                  </div>
                </div>
              )
            );
          })}
          {imageURLs?.project?.length > 0 && (
            <div>
              <h3 className="text-sm mb-2">Project</h3>
              <div className="flex flex-wrap gap-4">
                {imageURLs?.project?.map((el, idX) => (
                  <section
                    key={idX}
                    className="p-2 border rounded-md w-[170px] flex flex-col justify-between"
                  >
                    <section className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer ">
                      <img
                        className="w-full aspect-video object-contain"
                        src={el?.type === 'application/pdf' ? pdf : el?.data}
                        alt=""
                      />
                    </section>
                    <Marquee className="w-full overflow-hidden">
                      {el.name}
                    </Marquee>
                  </section>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Job Form */}
        <div className="space-y-4">
          <h1 className="text-xl font-semibold">Job Form</h1>
          {newLocalData.length > 0 && <JobDetailsView data={formData} />}
        </div>

        {/* BOM (Bill of Material) */}
        <div className="space-y-4">
          <h1 className="text-xl font-semibold">BOM (Bill of Material)</h1>
          {allData?.bom ? (
            <>
              <AssignedAssemblyBom po={allData} className={'!p-0'} />
            </>
          ) : null}
        </div>

        {/* QC Planning */}
        <div className="space-y-4">
          <h1 className="text-xl font-semibold">QC Planning</h1>
          {allQCData.length > 0 ? (
            <Table className="w-full">
              <Table.Head>
                <Table.Row>
                  <Table.Th>#</Table.Th>
                  <Table.Th>Process</Table.Th>
                  <Table.Th>Form</Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                {allQCData.map((item, iIdx) => {
                  const targettedForm = allQCForms?.find(
                    (el) => el._id === item?.formId
                  );
                  const targettedProcess = qcProcess?.find(
                    (el) => el?.mqtt?._id === item?.processId
                  );

                  return (
                    <Table.Row key={iIdx}>
                      <Table.Td>{iIdx + 1}</Table.Td>
                      <Table.Td>
                        {targettedProcess?.processName || '-'}
                      </Table.Td>
                      <Table.Td>{targettedForm?.formName || '-'}</Table.Td>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
          ) : (
            <p>No QC Data</p>
          )}
        </div>

        {/* Process Select */}
        <div className="space-y-4">
          <h1 className="text-xl font-semibold">Process Select</h1>
          {formData?.map((el, index) => (
            <Table className="w-full" key={index}>
              <Table.Head className="w-full">
                <Table.Row className="w-full">
                  <Table.Th className="w-1/3">#</Table.Th>
                  <Table.Th className="w-1/3">Process</Table.Th>
                  <Table.Th className="w-1/3">Parameters</Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body className="w-full">
                {el?.data.map((currElem, cIdx) => {
                  const filteredKeys = Object.entries(currElem?.inputData || {})
                    .filter(
                      ([_key, value]) =>
                        typeof value === 'object' &&
                        Array.isArray(value.selectOptions) &&
                        value.selectOptions.length >= 1
                    )
                    .map(([parameter, targettedKey]) => ({
                      parameter,
                      processes: targettedKey.selectOptions.map(
                        (op) => op.process
                      ),
                    }));

                  const uniqueProcessesSet = new Set();
                  const uniqueProcessesRows = [];

                  filteredKeys.forEach((key) => {
                    key.processes.forEach((process) => {
                      if (!uniqueProcessesSet.has(process)) {
                        uniqueProcessesSet.add(process);
                        uniqueProcessesRows.push(
                          <Table.Row className="w-full" key={process}>
                            <Table.Td>
                              {uniqueProcessesRows.length + 1}
                            </Table.Td>
                            <Table.Td>{process}</Table.Td>
                            <Table.Td>
                              {filteredKeys
                                .filter((entry) =>
                                  entry.processes.includes(process)
                                )
                                .map((entry) => entry.parameter)
                                .join(', ')}
                            </Table.Td>
                          </Table.Row>
                        );
                      }
                    });
                  });

                  return <Fragment key={cIdx}>{uniqueProcessesRows}</Fragment>;
                })}
              </Table.Body>
            </Table>
          ))}
        </div>
      </div>
    </PreviewModal>
  );
};

export default PreviewScreenModal;
