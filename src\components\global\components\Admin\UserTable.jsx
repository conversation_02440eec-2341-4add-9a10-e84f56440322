import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Button, Space, Table, Tooltip } from 'antd';
import QRCode from 'react-qr-code';
import {
  generateDateString,
  getLocalDateTime,
} from '../../../../helperFunction';

const UserTable = ({
  users,
  isFetchingQuery,
  handleEdit,
  handleSingleDelete,
  setOpenRightSidebar,
  setRightSideBarData,
  filteredDepartments,
  mqttData,
  setQrData,
  setIsOpenQr,
}) => {
  const columns = [
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => getLocalDateTime(text),
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      className: 'min-w-[10rem]',
      render: (text) =>
        text.length > 55 ? (
          <Tooltip title={text}>{text.substring(0, 55)}...</Tooltip>
        ) : (
          text
        ),
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (text) => text || '-',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      render: (text) => text || '-',
    },
    {
      title: 'Mobile',
      dataIndex: 'mobile',
      key: 'mobile',
      render: (text) => text || '-',
    },
    {
      title: 'Employee ID',
      dataIndex: 'employeeId',
      key: 'employeeId',
      render: (text) => text || '-',
    },
    {
      title: 'Departments',
      key: 'departments',
      render: (_, record) => (
        <span
          onClick={() => {
            setOpenRightSidebar(true);
            setRightSideBarData({
              for: 'dep',
              data: filteredDepartments?.filter((dep) =>
                record.departments?.includes(dep._id)
              ),
            });
          }}
          style={{ cursor: 'pointer', color: '#1890ff' }}
        >
          {record.departments?.length || '-'}
        </span>
      ),
    },
    {
      title: 'Process Access',
      key: 'processAccess',
      render: (_, record) => (
        <span
          onClick={() => {
            setOpenRightSidebar(true);
            setRightSideBarData({
              for: 'pro',
              data: mqttData?.filter((mqtt) =>
                record.processAccess?.includes(mqtt._id)
              ),
            });
          }}
          style={{ cursor: 'pointer', color: '#1890ff' }}
        >
          {record.processAccess?.length || '-'}
        </span>
      ),
    },
    {
      title: 'RFID',
      dataIndex: 'rfid',
      key: 'rfid',
      render: (text) => text || '-',
    },
    {
      title: 'RFID Expiry Date',
      dataIndex: 'rfidExpiryDate',
      key: 'rfidExpiryDate',
      render: (text) => (text ? generateDateString(text) : '-'),
    },
    {
      title: 'QR Code',
      key: 'qrCode',
      render: (_, record) => (
        <button
          type="button"
          onClick={() => {
            setQrData({
              rfid: record?.rfid?.toString(),
              name: record?.name,
              empId: record?._id,
              qrFor: 'operator',
            });
            setIsOpenQr(true);
          }}
        >
          <QRCode
            value={JSON.stringify({
              rfid: record?.rfid,
              name: record?.name,
            })}
            size={20}
          />
        </button>
      ),
    },
    {
      title: 'Designation',
      dataIndex: 'designation',
      key: 'designation',
      render: (text) => text || '-',
    },
    {
      title: 'Contractor Name',
      dataIndex: 'contractorName',
      key: 'contractorName',
      render: (text) => text || '-',
    },
    {
      title: 'Action',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleSingleDelete(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Table
      className="mt-5 w-full"
      columns={columns}
      size="small"
      dataSource={users}
      loading={isFetchingQuery}
      scroll={{ x: 'max-content' }}
      rowKey="_id"
      pagination={false}
      bordered
    />
  );
};

export default UserTable;
