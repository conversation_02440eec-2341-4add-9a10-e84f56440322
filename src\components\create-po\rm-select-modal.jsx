import { Tab } from '@headlessui/react';
import clsx from 'clsx';
import he from 'he';
import { Fragment, useMemo, useState } from 'react';
import { calculateRMQuantity } from '../../helperFunction';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import Table from '../global/components/Table';
import { toast } from 'react-toastify';
import { InputNumber, Button } from 'antd';
import Select from '../global/components/Select';
import { PlusOutlined, SwapOutlined } from '@ant-design/icons';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { Store } from '../../store/Store';
import { useContext } from 'react';

const ALL_TABS = ['RAW MATERIALS', 'ASSETS'];

const SELECTED_STYLE =
  'px-4 py-2 text-sm font-medium text-gray-900 bg-gray-100 outline-none w-auto';

const NON_SELECTED_STYLE =
  'px-4 py-2 text-sm font-medium text-gray-900 outline-none w-auto bg-white ';

const TABLE_HEADINGS = [
  'NAME',
  'UNITS',
  'UOM',
  'IN STOCK',
  'NET QUANTITY',
  'SCRAP PERCENTAGE',
  'GROSS QUANTITY',
];

export default function RMSelectModal({
  openRawMaterialModal,
  setOpenRawMaterialModal,
  allParts,
  setRawMaterials,
  rawMaterials,
  orderQuantity,
  assetData,
  setAssetData,
  allAssets,
  setPopup,
}) {
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [isCustomValue, setIsCustomValue] = useState(false);
  const [selectedValue, setSelectedValue] = useState(null);

  const [updateDefaults] = useUpdateDefaultsMutation();
  const {
    defaults: {
      defaultParam: { scrapPercentage },
    },
  } = useContext(Store);

  const [predefinedPercentages, setPredefinedPercentages] = useState([
    'Manual Entry',
    ...scrapPercentage,
  ]);

  const handleSelectChange = (percentage, el, index) => {
    if (percentage === 'Manual Entry') {
      setSelectedValue(null);
      setIsCustomValue(true);
    } else {
      if (percentage < 0) {
        toast.warning('Please enter valid input');
        return;
      }

      setSelectedValue(percentage);

      let reqStock =
        calculateRMQuantity({
          itemUnits: openRawMaterialModal?.units || 0,
          itemQuantity: el?.quantity || 0,
          orderQuantity: openRawMaterialModal?.inhOrderQty
            ? openRawMaterialModal?.inhOrderQty
            : +orderQuantity?.[openRawMaterialModal?.selectedBOM] || 0,
          rmUnits: el.units || 0,
          prevUnits: openRawMaterialModal?.prevUnits,
        }) || 0;

      let grossQuantity = +reqStock + (+reqStock * +percentage) / 100;

      setRawMaterials((prev) => {
        const itemIndex = prev?.findIndex(
          (el) => el?.partId === openRawMaterialModal?.selectedItem
        );

        return prev.map((el, i) => {
          if (i === itemIndex) {
            return {
              ...el,
              rawMaterials: el.rawMaterials?.map((material, rIdx) => {
                if (rIdx === index) {
                  return {
                    ...material,
                    scrapPercentage: percentage,
                    grossQuantity,
                  };
                }
                return material;
              }),
            };
          }
          return el;
        });
      });

      setIsCustomValue(false);
    }
  };

  const handleCustomChange = (percentage, el, index) => {
    if (percentage < 0) {
      toast.warning('Please enter valid input');
      return;
    }

    setSelectedValue(percentage);
    let reqStock =
      calculateRMQuantity({
        itemUnits: openRawMaterialModal?.units || 0,
        itemQuantity: el?.quantity || 0,
        orderQuantity: openRawMaterialModal?.inhOrderQty
          ? openRawMaterialModal?.inhOrderQty
          : +orderQuantity?.[openRawMaterialModal?.selectedBOM] || 0,
        rmUnits: el.units || 0,
        prevUnits: openRawMaterialModal?.prevUnits,
      }) || 0;

    let grossQuantity = +reqStock + (+reqStock * +percentage) / 100;

    setRawMaterials((prev) => {
      const itemIndex = prev?.findIndex(
        (el) => el?.partId === openRawMaterialModal?.selectedItem
      );

      return prev.map((el, i) => {
        if (i === itemIndex) {
          return {
            ...el,
            rawMaterials: el.rawMaterials?.map((material, rIdx) => {
              if (rIdx === index) {
                return {
                  ...material,
                  scrapPercentage: percentage,
                  grossQuantity,
                };
              }
              return material;
            }),
          };
        }
        return el;
      });
    });
  };

  const handleAddCustom = async () => {
    if (
      isCustomValue === true &&
      !predefinedPercentages.includes(selectedValue) &&
      selectedValue
    ) {
      // add logic to update default values
      let updatedpercentages = [...scrapPercentage, +selectedValue].sort(
        (a, b) => a - b
      );

      await updateDefaults({ scrapPercentage: updatedpercentages });

      setPredefinedPercentages(
        [...predefinedPercentages, +selectedValue].sort((a, b) => a - b)
      );
    }

    setIsCustomValue(false);
  };

  const allRawMaterialParts = useMemo(() => {
    return allParts
      ?.filter((el) => el?.category?.name?.toLowerCase() === 'raw materials')
      ?.map((el) => ({ label: el?.name, value: el?.value }));
  }, [allParts]);

  const handleChange = (event) => {
    const allIds = event.target.value?.map((el) => el.value);

    const selectedParts = allParts.filter((el) => allIds.includes(el?.value));

    const alreadyExists = rawMaterials?.findIndex(
      (el) => el?.partId === openRawMaterialModal?.selectedItem
    );

    if (alreadyExists === -1) {
      setRawMaterials((prev) => [
        ...prev,
        {
          partId: openRawMaterialModal?.selectedItem,
          bomId: openRawMaterialModal?.selectedBOM,
          units: openRawMaterialModal?.units,
          rawMaterials: selectedParts?.map((part) => {
            return {
              part: part?.value,
              units: 0,
              partType: part?.type,
              value: part.value,
              label: part?.name,
              quantity: part?.quantity,
              grossQuantity: 0,
              scrapPercentage: 0,
            };
          }),
        },
      ]);
    } else {
      const rms = rawMaterials?.[alreadyExists]?.rawMaterials;

      setRawMaterials((prev) =>
        prev.map((el) => {
          if (el?.partId === openRawMaterialModal.selectedItem) {
            return {
              ...el,
              rawMaterials: selectedParts?.map((part) => {
                const RM = rms?.find((i) => i.value === part?.value);
                return {
                  part: part?.value,
                  units: RM?.units || 0,
                  grossQuantity: RM?.grossQuantity || 0,
                  scrapPercentage: RM?.scrapPercentage || 0,
                  partType: part?.type,
                  value: part.value,
                  label: part?.name,
                  quantity: part?.quantity,
                };
              }),
            };
          }

          return el;
        })
      );
    }
  };

  const handleChangeUnits = (index, value, el) => {
    if (value < 0) {
      toast.warning('Please enter valid units');
      return;
    }

    let reqStock =
      calculateRMQuantity({
        itemUnits: openRawMaterialModal?.units || 0,
        itemQuantity: el?.quantity || 0,
        orderQuantity: openRawMaterialModal?.inhOrderQty
          ? openRawMaterialModal?.inhOrderQty
          : +orderQuantity?.[openRawMaterialModal?.selectedBOM] || 0,
        rmUnits: value || 0,
        prevUnits: openRawMaterialModal?.prevUnits,
      }) || 0;

    setRawMaterials((prev) => {
      const itemIndex = prev?.findIndex(
        (el) => el?.partId === openRawMaterialModal?.selectedItem
      );

      return prev.map((el, i) => {
        if (i === itemIndex) {
          return {
            ...el,
            rawMaterials: el.rawMaterials?.map((material, rIdx) => {
              if (rIdx === index) {
                return {
                  ...material,
                  units: +value,
                  grossQuantity:
                    +reqStock + (+reqStock * material?.scrapPercentage) / 100,
                };
              }
              return material;
            }),
          };
        }
        return el;
      });
    });
  };

  const allTableRows = useMemo(() => {
    return rawMaterials?.find(
      (el) => el?.partId === openRawMaterialModal?.selectedItem
    )?.rawMaterials;
  }, [rawMaterials, openRawMaterialModal.selectedItem]);

  return (
    <Modal
      title="Select Raw Materials"
      description="Select Raw Materials"
      onCloseModal={() =>
        setOpenRawMaterialModal({
          open: false,
          selectedItem: null,
          selectedItemType: null,
          woType: null,
          isManual: false,
          manualEntry: null,
          selectedBOM: null,
        })
      }
      onSubmit={() =>
        setOpenRawMaterialModal({
          open: false,
          selectedItem: null,
          selectedItemType: null,
          woType: null,
          isManual: false,
          manualEntry: null,
          selectedBOM: null,
        })
      }
    >
      {() => (
        <>
          <Tab.Group as="div" selectedIndex={selectedTabIndex}>
            <Tab.List
              as="div"
              className="inline-flex rounded-lg shadow-sm overflow-hidden border border-[#d0d5dd] gap-x-px w-2/4 h-[3%]"
            >
              {ALL_TABS?.map((el, i) => {
                return (
                  <Tab as={Fragment} key={i}>
                    {({ selected }) => (
                      <button
                        className={clsx(
                          'w-full',
                          selected ? SELECTED_STYLE : NON_SELECTED_STYLE
                        )}
                        onClick={() => {
                          setSelectedTabIndex(i);
                        }}
                      >
                        {el}
                      </button>
                    )}
                  </Tab>
                );
              })}
            </Tab.List>
            <Tab.Panels as="div" className="mt-4 w-full">
              <Tab.Panel>
                <div className="w-full">
                  <label className="block mb-1 text-sm">
                    Select Raw Materials
                  </label>
                  <MultiSelect
                    options={allRawMaterialParts}
                    onChange={handleChange}
                    AddOption={'+ Add RM'}
                    handleAddFunction={() => {
                      setPopup('addPart');
                    }}
                    value={
                      rawMaterials?.find(
                        (el) =>
                          el?.partId === openRawMaterialModal?.selectedItem
                      )?.rawMaterials
                    }
                  />
                </div>
                <div className="w-full mt-4">
                  <Table>
                    <Table.Head>
                      {TABLE_HEADINGS.map((el, index) => (
                        <Table.Th key={index}>{el}</Table.Th>
                      ))}
                    </Table.Head>
                    <Table.Body>
                      {allTableRows?.map((el, index) => {
                        return (
                          <Table.Row key={index}>
                            <Table.Td>{el.label}</Table.Td>
                            <Table.Td>
                              <Input
                                type="number"
                                value={el.units}
                                onChange={(e) =>
                                  handleChangeUnits(index, e.target.value, el)
                                }
                              />
                            </Table.Td>
                            <Table.Td>{el?.part?.uom || '-'}</Table.Td>
                            <Table.Td>
                              {el?.quantity
                                ? parseFloat(el?.quantity)?.toFixed(2)
                                : 'N/A'}
                            </Table.Td>
                            <Table.Td>
                              {calculateRMQuantity({
                                itemUnits: openRawMaterialModal?.units || 0,
                                itemQuantity: el?.quantity || 0,
                                orderQuantity: openRawMaterialModal?.inhOrderQty
                                  ? openRawMaterialModal?.inhOrderQty
                                  : +orderQuantity?.[
                                      openRawMaterialModal?.selectedBOM
                                    ] || 0,
                                rmUnits: el.units || 0,
                                prevUnits: openRawMaterialModal?.prevUnits,
                              })}
                            </Table.Td>
                            <Table.Td>
                              <div className="flex items-center gap-2">
                                {isCustomValue ? (
                                  <div className="flex items-center gap-1 border border-gray-300 rounded px-2 py-1">
                                    <InputNumber
                                      min={0}
                                      max={100}
                                      value={el?.scrapPercentage || 0}
                                      onChange={(e) =>
                                        handleCustomChange(e, el, index)
                                      }
                                      placeholder="Custom %"
                                    />

                                    <Button
                                      type="text"
                                      icon={
                                        !predefinedPercentages.includes(
                                          selectedValue
                                        ) ? (
                                          <PlusOutlined />
                                        ) : (
                                          <SwapOutlined />
                                        )
                                      }
                                      onClick={handleAddCustom}
                                      // disabled={customValue === null}
                                    />
                                  </div>
                                ) : (
                                  <Select
                                    className={'!w-32'}
                                    value={el?.scrapPercentage || 0}
                                    onChange={(e) =>
                                      handleSelectChange(
                                        e.target.value,
                                        el,
                                        index
                                      )
                                    }
                                    options={predefinedPercentages?.map(
                                      (el) => ({
                                        value: el,
                                        label: `${el === 'Manual Entry' ? 'Manual Entry' : el + '%'}`,
                                      })
                                    )}
                                    placeholder="Select"
                                  ></Select>
                                )}
                              </div>
                            </Table.Td>
                            <Table.Td>{el?.grossQuantity || '-'}</Table.Td>
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                  </Table>
                </div>
              </Tab.Panel>
              <Tab.Panel>
                <div className="w-full">
                  <label className="block mb-1 text-sm">Select Asset</label>
                  <MultiSelect
                    options={allAssets?.map((el) => ({
                      ...el,
                      label: el?.name,
                    }))}
                    AddOption={'+ Add Asset'}
                    handleAddFunction={() => {
                      setPopup('addPart');
                    }}
                    onChange={(e) => {
                      setAssetData((prev) => ({
                        ...prev,
                        assets: {
                          ...prev.assets,
                          [openRawMaterialModal?.selectedItem]:
                            e.target.value?.map((el) => ({
                              ...el,
                              modelType: el.type,
                            })),
                        },
                      }));
                    }}
                    value={
                      assetData.assets?.[openRawMaterialModal?.selectedItem] ??
                      []
                    }
                  />
                </div>
                <div className="mt-6">
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>NAME</Table.Th>
                        <Table.Th>DESCRIPTION</Table.Th>
                        <Table.Th>STATUS</Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {assetData?.assets?.[
                        openRawMaterialModal?.selectedItem
                      ]?.map((el, i) => (
                        <Table.Row key={i} className="py-2">
                          <Table.Td>{el?.name}</Table.Td>
                          <Table.Td>
                            <div
                              dangerouslySetInnerHTML={{
                                __html: he.decode(el?.description ?? ''),
                              }}
                            ></div>
                          </Table.Td>
                          <Table.Td>
                            <p className="rounded-full bg-yellow-600 px-2 font-semibold text-white text-xs py-1 w-[120px] text-center">
                              {el?.job
                                ? 'IN PRODUCTION'
                                : el?.action === 'in'
                                  ? 'IN STORE'
                                  : 'NOT IN USE'}
                            </p>
                          </Table.Td>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </div>
              </Tab.Panel>
            </Tab.Panels>
          </Tab.Group>
        </>
      )}
    </Modal>
  );
}
