import { useState } from 'react';
import { toast } from 'react-toastify';
import { getDecodedHTML } from '../../helperFunction';
import { useForWardMailMutation } from '../../slices/EmailApiSlice';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import Label from '../v2/global/components/Label';
import RichTextDescription from '../v3/global/components/rich-text-description';
import EmailDetail from './EmailDetail';
const ForwardMailModal = ({ mail, setShowModal }) => {
  const [ForwardEmailData, setForwardEmailData] = useState({
    to: '',
    body: '',
  });
  const [forwardMail] = useForWardMailMutation();
  const [recipients, setRecipients] = useState([]);
  const handleChange = (field, value) => {
    setForwardEmailData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };
  const ForwardEmail = async () => {
    const attachments = mail?.attachments?.map((attachment) => {
      const buffer = new Uint8Array(attachment?.content?.data);
      const blob = new Blob([buffer], { type: attachment?.contentType });
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = (e) => {
        const dataurl = e.target.result;
        return {
          type: attachment?.contentType,
          data: dataurl,
          name: attachment?.filename,
        };
      };
    });
    if (recipients.length === 0 && ForwardEmailData.to) {
      recipients.push(ForwardEmailData.to);
    }
    const body = {
      ...ForwardEmailData,
      to: recipients.join(', '),
      mail: { ...mail, attachments },
    };
    const data = await forwardMail(body).unwrap();
    if (data?.info) {
      toast.success('Mail Forwarded');
      setShowModal(false);
    }
  };
  const addRecipient = (email) => {
    if (validateEmail(email)) {
      setRecipients((prev) => [...prev, email]);
      setForwardEmailData((prev) => ({ ...prev, to: '' }));
    } else {
      toast.error('Invalid email address');
    }
  };

  const validateEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };
  const removeRecipient = (index) => {
    setRecipients((prev) => prev.filter((_, i) => i !== index));
  };
  const handleKeyDown = (e) => {
    if (
      (e.key === 'Enter' || e.key === ' ') &&
      ForwardEmailData.to.trim() !== ''
    ) {
      addRecipient(ForwardEmailData.to.trim());
      e.preventDefault(); // Prevent form submission on Enter key
    }
  };

  return (
    <Modal
      title="Forward Mail"
      onCloseModal={() => {
        setShowModal(false);
      }}
      onSubmit={ForwardEmail}
    >
      {() => (
        <section className="space-y-2">
          <div className="input-wrapper">
            <Label>Forward To</Label>{' '}
            {recipients.map((email, index) => (
              <div
                key={index}
                className="inline-flex gap-2 items-center p-1 my-1 bg-[#ffffff] border rounded-md overflow-x-auto text-sm"
              >
                {email}
                <button
                  type="button"
                  onClick={() => removeRecipient(index)}
                  className="bg-transparent border-none text-[#ff3f3f]"
                >
                  X
                </button>
              </div>
            ))}
            <Input
              placeholder="Enter Email"
              type="email"
              value={ForwardEmailData.to}
              onKeyDown={handleKeyDown}
              onChange={(e) => {
                setForwardEmailData((prev) => {
                  return {
                    ...prev,
                    to: e.target.value,
                  };
                });
              }}
            />
          </div>
          <div className="input-wrapper">
            <Label>Body</Label>
            <RichTextDescription
              value={getDecodedHTML(ForwardEmailData.body)}
              onChange={(value) => handleChange('body', value)}
            />
          </div>
          <div className="mail">
            <EmailDetail mail={mail} showActionButtons={false} />
          </div>
        </section>
      )}
    </Modal>
  );
};

export default ForwardMailModal;
