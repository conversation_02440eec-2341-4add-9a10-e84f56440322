import { useMediaQuery } from 'react-responsive';
import { mobileWidth, tabletWidth } from '../../../helperFunction';
import Button from './Button';

const CreateFormFullScreenModal = ({
  title,
  description,
  onClose,
  onSubmit,
  children,
}) => {
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  return (
    <div
      className={`fixed inset-0 z-40 flex items-center justify-center  bg-black bg-opacity-50 `}
    >
      {/* Modal Container */}
      <div
        className={`relative bg-white rounded-md shadow-md ${!isMobile && !isTablet ? 'w-[80%]' : 'w-full'} h-full  overflow-y-auto py-10 mt-[8rem] ${!isMobile && !isTablet ? 'px-20' : 'px-5'}`}
      >
        <div
          className="!bg-white border border-none float-right border-gray-500 !text-black !mt-1"
          onClick={() => onClose()}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6 cursor-pointer"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6 18 18 6M6 6l12 12"
            />
          </svg>
        </div>
        <h1 className="mt-5">{title}</h1>
        <p className="mb-8 font-normal text-gray-500">{description}</p>
        {children}
        <div className="flex justify-end gap-2 mt-10">
          <Button
            className="!bg-blue-primary border mb-20 border-gray-500 !text-white !h-8 !mt-1 ml-3"
            onClick={() => onSubmit()}
          >
            Submit
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateFormFullScreenModal;
