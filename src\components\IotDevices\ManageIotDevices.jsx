import { useState } from 'react';
import { IoCloseSharp } from 'react-icons/io5';
import { toast } from 'react-toastify';
import {
  useCreateIotDeviceMutation,
  useEditIotDeviceMutation,
} from '../../slices/iotDeviceApiSlice';

import { useGetAllProfileQuery } from '../../slices/profileApiSlice';
import { customConfirm } from '../../utils/customConfirm';
import Input from '../global/components/Input';
import Modal, {
  GridWrapper,
  LabelAndField,
  Spacer,
} from '../global/components/Modal';
import Select from '../global/components/Select';
import Table from '../global/components/Table';

const OPTIONS = [
  {
    label: 'NONE',
    value: '',
  },
  {
    label: 'RS232',
    value: 'RS232',
  },
  {
    label: 'RS485',
    value: 'RS485',
  },
  {
    label: 'CAN',
    value: 'CAN',
  },
];

const INITIAL_INPUT_DATA = {
  name: '',
  deviceId: '',
  profileId: '',
  fields: [
    {
      isDefault: true,
      id: 1,
      name: 'STATUS',
      type: 'value',
      topic: '/DID/STATUS',
    },
    {
      isDefault: true,
      id: 2,
      name: 'Relay 1',
      type: 'toggle',
      topic: '/DID/RELAY1',
    },
    {
      isDefault: true,
      id: 3,
      name: 'Relay 2',
      type: 'toggle',
      topic: '/DID/RELAY2',
    },
    {
      isDefault: true,
      id: 4,
      name: 'CT 1',
      type: 'value',
      topic: '/DID/CT1',
    },
    {
      isDefault: true,
      id: 5,
      name: 'CT 2',
      type: 'value',
      topic: '/DID/CT2',
    },
    {
      isDefault: true,
      id: 6,
      name: 'CT 3',
      type: 'value',
      topic: '/DID/CT3',
    },
    {
      isDefault: true,
      id: 7,
      name: 'Active Hours',
      type: 'value',
      topic: '/DID/ACTIVEHOURS',
    },
    {
      isDefault: true,
      id: 8,
      name: 'Start',
      type: 'value',
      topic: '/DID/START',
    },
    {
      isDefault: true,
      id: 9,
      name: 'Stop',
      type: 'value',
      topic: '/DID/STOP',
    },
    {
      isDefault: true,
      id: 10,
      name: 'Last Updated Time',
      type: 'value',
      topic: '/DID/LASTUPDATEDTIME',
    },
    {
      isDefault: true,
      id: 11,
      name: 'Total Active Hours',
      type: 'value',
      topic: '/DID/TOTALACTIVEHOURS',
    },
    {
      isDefault: true,
      id: 12,
      name: 'Average Current',
      type: 'value',
      topic: '/DID/AVGCURRENT',
    },
    {
      isDefault: true,
      id: 13,
      name: 'Maintenance Hours',
      type: 'value',
      topic: '/DID/MAINTENANCEHOURS',
    },
    {
      isDefault: true,
      id: 14,
      name: 'Idle Hours',
      type: 'value',
      topic: '/DID/IDLE',
    },
    {
      isDefault: true,
      id: 15,
      name: 'Production Count',
      type: 'value',
      topic: '/DID/COUNT',
    },
  ],
};

let INITIAL_FIELD_DATA = {
  id: Date.now(),
  name: '',
  type: 'value',
  topic: '',
  protocol: '',
};

function ManageIotDevices({ onCloseModal, editData, setEditData }) {
  const [inputData, setInputData] = useState(editData || INITIAL_INPUT_DATA);
  const [fieldData, setFieldData] = useState(INITIAL_FIELD_DATA);

  const isEdit = !!editData?._id;

  const [createIotDevice, { isLoading: isLoadingCreate }] =
    useCreateIotDeviceMutation();
  const [editIotDevice, { isLoading: isLoadingEdit }] =
    useEditIotDeviceMutation();

  const { data: profiles = [] } = useGetAllProfileQuery();

  const inputChangeHandler = (e) => {
    let { name, value } = e.target;

    let objToSpread = {};

    if (name === 'deviceId') {
      value = value.toUpperCase();
      objToSpread.fields = inputData?.fields?.map((f) => {
        let newTopic = `/${value || 'DID'}/${f.topic.split('/').slice(2).join('/')}`;
        return { ...f, topic: newTopic };
      });
    }

    setInputData((prev) => ({ ...prev, [name]: value, ...objToSpread }));
  };
  const fieldsChangeHandler = (e) => {
    let { name, value } = e.target;

    if (name === 'topic') value = value.toUpperCase();

    setFieldData((prev) => ({ ...prev, [name]: value }));
  };

  const addField = () => {
    if (!fieldData?.name || !fieldData?.topic) {
      toast.error('Add field name and topic before adding', {
        toastId: 'fieldnamemissing',
      });
      return;
    }

    let genTopic = `/${inputData?.deviceId || 'DID'}`;

    if (fieldData?.protocol) {
      genTopic += `/${fieldData?.protocol}`;
    }

    genTopic += `/${fieldData.topic}`;

    for (let i = 0; i < inputData?.fields?.length; i++) {
      const field = inputData?.fields?.[i];

      if (field?.name === fieldData.name) {
        toast.error(`Field topic ${field.name} already exists`, {
          toastId: 'samename',
        });
        setFieldData((prev) => ({ ...prev, name: '' }));
        return;
      }
      if (field?.topic === genTopic) {
        toast.error(`Field topic ${field.topic} already exists`, {
          toastId: 'sametopic',
        });
        setFieldData((prev) => ({
          ...prev,
          topic: `/${inputData?.deviceId ? inputData?.deviceId : 'DID'}/`,
        }));
        return;
      }
    }

    setInputData((prev) => ({
      ...prev,
      fields: [
        ...prev?.fields,
        {
          ...fieldData,
          topic: genTopic,
          id: Date.now(),
        },
      ],
    }));
    setFieldData(INITIAL_FIELD_DATA);
  };

  const removeField = async (field) => {
    if (field.isDefault) return;
    const confirm = await customConfirm(
      'Are you sure you want to delete a field',
      'delete'
    );
    if (!confirm) return;

    setInputData((prev) => ({
      ...prev,
      fields: prev?.fields?.filter((i) => i.name !== field.name),
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!inputData?.deviceId || !inputData?.name) {
      toast.error('Add name and device ID before submitting', {
        toastId: 'namemissing',
      });
      return;
    }

    let res;
    if (isEdit) {
      res = await editIotDevice({
        id: editData?._id,
        data: inputData,
      }).unwrap();
    } else {
      res = await createIotDevice({ data: inputData }).unwrap();
    }
    if (res) {
      toast.success(`Device ${isEdit ? 'edited' : 'added'} successfully`, {
        toastId: 'success',
      });
      onCloseModal();
      setEditData();
    }
  };

  return (
    <Modal
      title={`${isEdit ? 'Edit' : 'Add'} Iot Devices`}
      onCloseModal={onCloseModal}
      onAdd={{ label: 'Add', func: [addField], step: [0] }}
      btnIsLoading={isLoadingCreate || isLoadingEdit}
      onSubmit={handleSubmit}
    >
      {() => {
        return (
          <>
            <GridWrapper>
              <LabelAndField
                htmlFor="profileId"
                label={'Profile'}
                className="col-span-full"
              >
                <Select
                  name="profileId"
                  id="profileId"
                  value={inputData?.profileId?._id || inputData?.profileId}
                  onChange={inputChangeHandler}
                  options={profiles?.map((pro) => ({
                    label: pro.name,
                    value: pro._id,
                  }))}
                />
              </LabelAndField>
              <LabelAndField htmlFor="name" label={'Name'}>
                <Input
                  name="name"
                  id="name"
                  value={inputData?.name}
                  onChange={inputChangeHandler}
                />
              </LabelAndField>
              <LabelAndField htmlFor="deviceId" label={'Device ID'}>
                <Input
                  disabled={isEdit}
                  name="deviceId"
                  id="deviceId"
                  value={inputData?.deviceId}
                  onChange={inputChangeHandler}
                />
              </LabelAndField>

              <Spacer text="Fields" />

              <LabelAndField htmlFor="fieldName" label={'Field Name'}>
                <Input
                  name="name"
                  id="fieldName"
                  value={fieldData?.name}
                  onChange={fieldsChangeHandler}
                />
              </LabelAndField>
              <LabelAndField htmlFor="fieldTopic" label={'Field Topic'}>
                <div className="flex items-center gap-1">
                  {`/${inputData?.deviceId || 'DID'}/`}
                  <Select
                    name="protocol"
                    value={fieldData?.protocol}
                    onChange={fieldsChangeHandler}
                    options={OPTIONS}
                  />
                  /
                  <Input
                    name="topic"
                    id="fieldTopic"
                    value={fieldData?.topic}
                    placeholder="Topic"
                    onChange={fieldsChangeHandler}
                  />
                </div>
              </LabelAndField>

              <Table className={'col-span-full'}>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>#</Table.Th>
                    <Table.Th>Name</Table.Th>
                    <Table.Th>Type</Table.Th>
                    <Table.Th>Topic</Table.Th>
                    <Table.Th></Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {inputData?.fields?.map((field, fIdx) => (
                    <Table.Row key={field?._id || field.id}>
                      <Table.Td>{fIdx + 1}</Table.Td>
                      <Table.Td>{field?.name}</Table.Td>
                      <Table.Td>{field?.type}</Table.Td>
                      <Table.Td>{field?.topic}</Table.Td>
                      <Table.Td>
                        <IoCloseSharp
                          className={`text-lg ${field?.isDefault ? 'text-gray-400 cursor-not-allowed ' : 'text-red-500 cursor-pointer '}`}
                          onClick={() => removeField(field)}
                        />
                      </Table.Td>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </GridWrapper>
          </>
        );
      }}
    </Modal>
  );
}

export default ManageIotDevices;
