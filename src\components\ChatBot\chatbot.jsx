import { useCallback, useEffect, useRef, useState } from 'react';
import { FaMaximize, FaMinimize } from 'react-icons/fa6';
import { FiSend } from 'react-icons/fi';
import { HiSparkles } from 'react-icons/hi2';
import { <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/im';
import {
  IoIosCamera,
  IoIosSwap,
  IoMdAttach,
  IoMdCamera,
  IoMdTrash,
} from 'react-icons/io';
import { RiArrowGoBackFill } from 'react-icons/ri';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import Webcam from 'react-webcam';
import {
  useSendImageMutation,
  useSendMessageMutation,
} from '../../slices/ai/chatbotApislice';
import { apiSlice } from '../../slices/apiSlice';
import { useLazyGetMediaByIdQuery } from '../../slices/mediaSlice';
import Button from '../global/components/Button';
import DragAndDrop from '../global/components/DragAndDrop';
import PopoverModal from '../global/components/PopoverModal';
import HistorySidebar from '../Kanban/HistorySidebar';

const refactorMessage = (message) => {
  let newMessage = message.split(/@nav|@table|@revalidate|@timer/)?.[0];
  return newMessage;
};

function Chatbox({ open, setOpen, chatboxState, setChatboxState }) {
  const dispatch = useDispatch();
  const [sessionId, setSessionId] = useState(Date.now());
  /**
   * The state variable `showModal` is a boolean that controls the visibility of
   * a modal dialog. When `true`, the modal is visible, and when `false`, it is
   * hidden.
   * @type {boolean}
   */
  const [showModal, setShowModal] = useState(false);
  const [temp, setTemp] = useState('');
  const [_timer, setTimer] = useState(false);
  const [messages, setMessages] = useState([
    {
      name: 'bot',
      message:
        'Hi! Welcome to AICAN Assistant! Please continue by selecting one of the options. @tile Work Order , Inventory ,  Sales , Purchase , Insight , System ,Task Summary',
    },
  ]);
  /**
   * `file` is a state variable that represents the current file being
   * uploaded or viewed. When the user uploads a file, it contains the
   * data of the file. When the user views a file, it contains the metadata
   * of the file. The file object has the following structure:
   ** @type {{ data: string; fileName: string; fileType: string; }}
   * **/
  const [file, setFile] = useState(null);

  /* 
  The state variable `facingMode` is used to set the camera facing mode.
  Possible values are:
  - 'user': the camera is facing the user, typically the front camera.
  - 'environment': the camera is facing the environment, typically the rear camera.
  This state variable is used to control the facing mode of the camera.
  It is initially set to 'user'.
  */
  const [facingMode, setFacingMode] = useState('user');

  /**
   * `isOpenCam` is a state variable that controls whether the camera modal is open or not.
   * When `true`, the camera modal is open and when `false`, it is closed.
   * @type {boolean}
   */
  const [isOpenCam, setIsOpenCam] = useState(false);

  const [sendMessage, { isLoading }] = useSendMessageMutation();
  const ref = useRef(null);
  const inputRef = useRef(null);
  const loaderRef = useRef(null);

  const webcamRef = useRef(null);

  const capture = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();

    let data = {
      fileName: 'captured-image.jpeg',
      fileType: 'image/jpeg',
      data: imageSrc,
    };
    setFile(data);
  }, [webcamRef]);
  const [minimized, setMinimized] = useState(false);
  /**
   * A function that toggles the facing mode between 'user' and 'environment'.
   *
   * @param None
   * @return None
   */
  const toggleFacingMode = () => {
    const newFacingMode = facingMode === 'user' ? 'environment' : 'user';
    setFacingMode(newFacingMode);
  };

  const [getMediaById] = useLazyGetMediaByIdQuery();

  const [sendImage, { isLoading: isLoadingImage }] = useSendImageMutation();
  const [historySidebar, setHistorySidebar] = useState({
    open: false,
    steps: [],
  });
  const GetMedia = async (temp) => {
    let arr = temp.split(',');
    return await getMediaById({ id: arr[1] }, false).unwrap();
  };

  const resetState = () => {
    setMessages([
      {
        name: 'bot',
        message:
          'Hi! Welcome to AICAN Assistant! Please continue by selecting one of the options. @tile Work Order , Inventory , Sales , Purchase , Insight , System , Task Summary',
      },
    ]);
    setTimer(false);
    setSessionId(Date.now());
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    handleSendMessage(e.target.elements.sendingText.value);
    e.target.reset();
  };

  const handleSendMessage = async (text) => {
    if (text !== '') {
      setMessages((prevMessages) => [
        ...prevMessages,
        { name: 'User', message: text },
      ]);

      sendMessage({ data: { message: text, sessionId } })
        .unwrap()
        .then((res) => {
          setTemp(res.chatbotData);
          if (res.chatbotData.includes('@nav')) {
            renderSideBar(res.chatbotData.split('@nav')[1]);
          }
          if (res.chatbotData.includes('@timer')) {
            setTimer(true);
          }
          if (res.chatbotData.includes('@revalidate:')) {
            const tags = res.chatbotData
              ?.split('@revalidate:')?.[1]
              ?.split(', ');
            dispatch(apiSlice.util.invalidateTags(tags));
          }
          setMessages((prevMessages) => [
            ...prevMessages,
            {
              name: 'bot',
              message: res.chatbotData.includes('W@3n')
                ? res.chatbotData.replace(
                    res.chatbotData,
                    'Your file is ready to view'
                  )
                : res.chatbotData,
            },
          ]);
          if (temp.includes('W@3n')) {
            GetMedia(temp).then((item2) => setFile(item2));
          }
        });
    }
  };
  const navigate = useNavigate();
  const handleNavigation = (url) => {
    navigate(url);
  };
  useEffect(() => {
    if (isLoading || messages.length) {
      ref.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
      });
      loaderRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
    if (chatboxState && inputRef.current) {
      inputRef.current.focus();
    }
    if (messages.length > 0 && inputRef.current) {
      inputRef.current.focus();
    }
  }, [messages.length, chatboxState, isLoading]);

  /**
   * Handles the change event for a given input element for media upload and updates the file state.
   *
   * @param {Object} e - The event object containing the input element data.
   */
  const changeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name;
      let ftype = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;

        let data = {
          fileName: fname,
          fileType: ftype,
          data: url,
        };
        setFile(data);
      });
    }
  };

  const handleImageSubmit = () => {
    sendImage(file)
      .unwrap()
      .then(() => {
        setMessages((prevMessages) => [
          ...prevMessages,
          {
            name: 'bot',
            message: `Customer has been created. \nl1nk:/settings/config/customer:Customer Tab:l1nk.`,
          },
        ]);
        setFile(null);
      })
      .catch(() => {
        setMessages((prevMessages) => [
          ...prevMessages,
          {
            name: 'bot',
            message: `An error occured! Customer could not be created.`,
          },
        ]);
      });
  };
  const buttonValue = (value) => {
    handleSendMessage(value);
  };
  const renderTable = (data, header) => {
    const rows = data.trim().split(',');
    const tableData = rows.map((row) => {
      const [key, value] = row.split(':');
      return { key: key.trim(), value: value.trim() };
    });

    return (
      <div className="w-full max-w-md mx-auto bg-white rounded-lg overflow-hidden">
        <div className="px-6 py-4">
          <h2 className="text-sm font-semibold text-gray-800">{header}</h2>
        </div>
        <hr />
        <div className="flex flex-col divide-y divide-gray-200">
          {tableData.map((row, index) => (
            <div
              key={index}
              className="flex justify-between items-center px-6 py-4"
            >
              <span className="text-gray-600 font-medium flex-1 text-sm">
                {row.key}
              </span>
              <span className="text-gray-800 font-semibold flex-1 text-sm">
                {row.value}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };
  const renderMessageWithLink = (message) => {
    const hrefText = message.split(':')[2];
    return (
      <span>
        {message
          .replace(`${hrefText}:l1nk.`, ' ')
          .split(' ')
          .map((word, idx) =>
            word.includes('l1nk:') ? (
              <span
                key={idx}
                onClick={() => handleNavigation(word.split(':')[1])}
                style={{
                  color: 'blue',
                  textDecoration: 'underline',
                  cursor: 'pointer',
                }}
              >
                {hrefText}{' '}
              </span>
            ) : (
              <span key={idx}>{word} </span>
            )
          )}
      </span>
    );
  };

  const renderSideBar = (obj) => {
    setHistorySidebar((prevState) => ({
      open: true,
      steps: [...prevState.steps],
      orderId: obj.trim(),
    }));
  };

  return (
    <div className="w-full">
      <HistorySidebar sidebar={historySidebar} setSidebar={setHistorySidebar} />
      <div
        className={`fixed z-[50] ${minimized ? '' : 'top-0'} bottom-0 right-0 ${
          minimized ? 'h-[95vh]' : ''
        } transition-all duration-300 ${
          open ? (minimized ? 'w-[450px]' : 'w-full') : 'w-0'
        }`}
      >
        <div
          className={`h-full bg-white ${
            minimized ? 'rounded-t-2xl' : ''
          } shadow-md overflow-hidden transition-all duration-300 ${
            open ? 'translate-x-0' : 'translate-x-full'
          }`}
        >
          {true && (
            <div>
              {chatboxState && (
                <div
                  tabIndex={0}
                  className={`flex flex-col ${
                    minimized ? 'w-[450px]' : 'w-full'
                  } h-[88vh] transition-all 0.5s ease-in-out`}
                >
                  <div
                    className={`p-4 flex w-full ${
                      minimized ? 'bg-[#483285] rounded-t-2xl' : ''
                    } flex-initial items-center justify-between`}
                  >
                    <div className="mt-1 flex">
                      <img src="/optiwise_logo.png" width={25} height={2} />
                      <div
                        className={`text-white ml-2 text-xl font-semibold ${
                          minimized ? 'visible' : 'hidden'
                        }`}
                      >
                        ChatAI
                      </div>
                    </div>

                    <div
                      className={`mt-1 flex ${minimized ? 'w-[20%] text-white' : 'text-[#5c51ff]'}`}
                    >
                      {minimized ? (
                        <>
                          <button>
                            <IoMdTrash
                              size={22}
                              onClick={resetState}
                              className={`mr-2 ${minimized ? 'text-white' : 'text-black'}`}
                            />
                          </button>
                          <FaMaximize
                            size={22}
                            className={`mr-2 ${minimized ? 'text-white' : 'text-black'}`}
                            onClick={() => setMinimized(false)}
                          />
                          <button>
                            <ImCross
                              className={`${minimized ? 'text-white' : 'text-black'}`}
                              onClick={() => {
                                setChatboxState(false);
                                setOpen(false);
                              }}
                            />
                          </button>
                        </>
                      ) : (
                        <button>
                          <FaMinimize
                            className={`text-[#5c51ff] font-bold`}
                            onClick={() => setMinimized(true)}
                          />
                        </button>
                      )}
                    </div>
                  </div>
                  <div
                    className={`flex flex-row ${minimized ? 'h-[80vh]' : 'h-[87vh]'}`}
                  >
                    <div
                      className={`flex-auto ${minimized ? 'lg:ml-0' : 'lg:ml-[350px] mb-8'} bg-white overflow-y-scroll`}
                    >
                      {messages.map((message, mIdx) => (
                        <div
                          key={mIdx}
                          className={`px-2 flex ${message.name === 'bot' ? 'justify-start' : 'justify-end'}`}
                        >
                          <div className="flex flex-col gap-2">
                            <div className="flex">
                              {message.name === 'bot' && (
                                <div className="rounded-full mt-auto mr-1">
                                  <HiSparkles size={20} color="#ff8e09" />
                                </div>
                              )}
                              <div>
                                {message.name === 'bot' && mIdx > 1 && (
                                  <p
                                    className={`underline text-blue-primary cursor-pointer text-sm flex ${messages.length - 1 === mIdx ? '' : 'pointer-events-none'}`}
                                    onClick={resetState}
                                  >
                                    Go Back{' '}
                                    <RiArrowGoBackFill
                                      size={10}
                                      className="ml-1 mt-1"
                                    />
                                  </p>
                                )}
                                <div
                                  key={mIdx}
                                  className={`p-2 ${minimized ? 'max-w-[315px]' : 'w-[775px]'} rounded-lg flex ${
                                    message.name === 'bot'
                                      ? 'bg-[#f5f4fe] text-gray-900'
                                      : 'bg-[#5c51ff] text-white'
                                  }`}
                                >
                                  {message.name === 'bot' &&
                                  message.message.includes('@table') ? (
                                    renderTable(
                                      message.message.split('@table')[1],
                                      message.message.slice(
                                        0,
                                        message.message.indexOf('@table') !== -1
                                          ? message.message.indexOf('@table')
                                          : message.message.length
                                      )
                                    )
                                  ) : message.name === 'bot' &&
                                    message.message.includes('@nav') ? (
                                    <p
                                      className={`whitespace-pre-line ${minimized ? 'text-[15px]' : 'text-[20px]'} p-1 font-sans`}
                                    >
                                      {message.message.slice(
                                        0,
                                        message.message.indexOf('@nav') !== -1
                                          ? message.message.indexOf('@nav')
                                          : message.message.length
                                      )}
                                    </p>
                                  ) : message.name === 'bot' &&
                                    message.message.includes('l1nk') ? (
                                    <p
                                      className={`whitespace-pre-line ${minimized ? 'text-[15px]' : 'text-[20px]'} p-1 font-sans`}
                                    >
                                      {renderMessageWithLink(
                                        refactorMessage(message.message)
                                      )}
                                    </p>
                                  ) : (
                                    <p
                                      className={`whitespace-pre-line ${minimized ? 'text-[15px]' : 'text-[20px]'} p-1 font-sans`}
                                    >
                                      {message.name === 'bot' &&
                                      message.message.includes('@btn')
                                        ? message.message.slice(
                                            0,
                                            message.message.indexOf('@btn') !==
                                              -1
                                              ? message.message.indexOf('@btn')
                                              : message.message.length
                                          )
                                        : message.name === 'bot' &&
                                            message.message.includes('@tile')
                                          ? message.message.slice(
                                              0,
                                              message.message.indexOf(
                                                '@tile'
                                              ) !== -1
                                                ? message.message.indexOf(
                                                    '@tile'
                                                  )
                                                : message.message.length
                                            )
                                          : refactorMessage(message.message)}
                                    </p>
                                  )}
                                  {temp.includes('W@3n') ? (
                                    <div>
                                      {message.message.includes(
                                        'Your file is ready to view'
                                      ) ? (
                                        <Button
                                          onClick={() => setShowModal(true)}
                                        >
                                          View
                                        </Button>
                                      ) : null}
                                    </div>
                                  ) : null}
                                </div>
                              </div>
                            </div>
                            {message.message.includes('@btn') ? (
                              <div
                                className={`grid  ${minimized ? ' grid-cols-2' : ' grid-cols-4'} gap-2 justify-end max-w-[675px] ml-6`}
                              >
                                {message.message
                                  .split('@btn')[1]
                                  .trim()
                                  .split(',')
                                  .map((btnText, index) => (
                                    <button
                                      key={index}
                                      title={btnText.trim()}
                                      disabled={mIdx !== messages?.length - 1}
                                      className="px-2 py-1 rounded-md text-[#5c51ff] hover:bg-[#f5f4fe] text-[15px] border w-full"
                                      onClick={() =>
                                        buttonValue(btnText.trim())
                                      }
                                    >
                                      {btnText.trim()}
                                    </button>
                                  ))}
                              </div>
                            ) : null}
                            {message.message.includes('@tile') ? (
                              <div
                                className={`grid  ${minimized ? ' grid-cols-2' : ' grid-cols-4'} gap-2 justify-end max-w-[675px] ml-6`}
                              >
                                {message.message
                                  .split('@tile')[1]
                                  .trim()
                                  .split(',')
                                  .map((btnText, index) => (
                                    <button
                                      key={index}
                                      title={btnText.trim()}
                                      disabled={mIdx !== messages?.length - 1}
                                      className="px-2 py-1 rounded-md text-[#5c51ff] hover:bg-[#f5f4fe] text-[15px] border w-full"
                                      onClick={() =>
                                        buttonValue(btnText.trim())
                                      }
                                    >
                                      {btnText.trim()}
                                    </button>
                                  ))}
                              </div>
                            ) : null}
                          </div>
                          <div ref={ref} />
                        </div>
                      ))}
                      {isLoading ? (
                        <span className="chatloader" ref={loaderRef}></span>
                      ) : null}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}{' '}
          <form
            onSubmit={handleSubmit}
            className={`relative ${minimized ? 'lg:ml-0' : 'lg:ml-[350px]'} ${minimized ? 'py-2' : 'py-0'} ${minimized ? null : 'mt-8 mb-3'}`}
          >
            <div className="w-full relative flex">
              <input
                ref={inputRef}
                className="w-full border mx-2 pl-2 pr-10 py-3 rounded-md focus:outline-none placeholder-gray-400 text-md font-sans"
                id="sendingText"
                type="text"
                placeholder="Please type your question"
                name="message"
                autoComplete="off"
                disabled={isLoading || isLoadingImage}
              />

              {showModal && (
                <PopoverModal
                  title={'Upload Customer Card'}
                  description={'Uploaded Files'}
                  file={file}
                  showModal={showModal}
                  isLoading={isLoading}
                  isLoadingImage={isLoadingImage}
                  onCloseModal={() => {
                    setShowModal(false);
                    setFile(null);
                    setIsOpenCam(false);
                    setChatboxState(true);
                  }}
                  onSubmit={() => {
                    if (file === null) {
                      toast.error('Cannot submit with empty file', {
                        theme: 'colored',
                        position: 'top-right',
                        toastId: 'Empty File',
                      });
                      return;
                    }
                    handleImageSubmit();
                    setShowModal(false);
                    setIsOpenCam(false);
                    setChatboxState(true);
                  }}
                  canSubmit={false}
                >
                  {() => (
                    <>
                      <div className=" flex w-full">
                        <DragAndDrop
                          className="h-10  text-xs  w-[50%]  "
                          accept="image/png, image/jpeg"
                          onChange={(e) => changeHandler(e)}
                          multiple={true}
                        />
                        <IoIosCamera
                          className="mt-3"
                          size={45}
                          onClick={() => setIsOpenCam((prev) => !prev)}
                        />
                      </div>

                      {isOpenCam && (
                        <div className=" bg-slate-200/55 shadow-md rounded-lg overflow-hidden mt-3 mx-auto w-[50%] p-2">
                          <div className="relative aspect-w-16 aspect-h-9">
                            <Webcam
                              audio={false}
                              ref={webcamRef}
                              screenshotFormat="image/jpeg"
                              width={640}
                              height={480}
                              className="object-cover"
                              videoConstraints={{ facingMode }}
                            />
                          </div>
                          <div className="flex justify-around my-2">
                            <IoMdCamera size={45} onClick={capture} />

                            <IoIosSwap size={45} onClick={toggleFacingMode} />
                          </div>
                        </div>
                      )}

                      {file && (
                        <div className=" mt-3 mx-auto">
                          <img
                            src={file?.media?.data || file?.data}
                            alt={file?.media?.name || file?.fileName}
                            className="w-[50%] rounded-lg h-[50%] mx-auto"
                          />
                          <div className="flex justify-between w-[50%] mx-auto">
                            <p>{file.fileName}</p>
                            <p
                              className="text-red-600 hover:underline cursor-pointer"
                              onClick={() => setFile(null)}
                            >
                              Remove
                            </p>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </PopoverModal>
              )}

              <button disabled={isLoading || isLoadingImage}>
                <IoMdAttach
                  onClick={() => {
                    setFile(null);
                    setShowModal(true);
                  }}
                  className="absolute right-12 top-1/2 -translate-y-1/2"
                  size={'20px'}
                  color="gray"
                />
              </button>
              <button disabled={isLoading || isLoadingImage}>
                <FiSend
                  type="submit"
                  className="absolute right-5 top-1/2 -translate-y-1/2"
                  size={'20px'}
                  color="gray"
                />
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default Chatbox;
