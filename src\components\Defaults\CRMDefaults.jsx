import { InfoTooltip } from '../global/components/InfoTooltip';

const CRMDefaults = ({ defaults, setDefaults }) => {
  return (
    <div className="py-2">
      <div>
        <h3 className="text-gray-subHeading mb-4">CRM Defaults :</h3>
      </div>
      <div className="flex flex-col mb-8">
        <div className="flex gap-x-4 items-center">
          <input
            type="checkbox"
            checked={defaults?.projectDefaults?.disableAssignUserLeads || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  disableAssignUserLeads: e.target.checked,
                },
              }));
            }}
          />
          <div className="flex gap-x-2 items-center">
            <label className=" text-sm font-medium text-gray-700">
              Disable Assign User in CRM{' '}
            </label>
            <InfoTooltip
              position="top"
              width="200px"
              id="downtimeType"
              isHtml={true}
              content="This Checkbox helps to Disable the Assign User into CRM"
            />
          </div>
        </div>
        {/* show all leads in kanban checkbox */}
        <div className="flex gap-x-4 items-center">
          <input
            type="checkbox"
            checked={defaults?.projectDefaults?.showAllLeadsInKanban || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  showAllLeadsInKanban: e.target.checked,
                },
              }));
            }}
          />
          <div className="flex gap-x-2 items-center">
            <label className=" text-sm font-medium text-gray-700">
              Show All Leads in Kanban{' '}
            </label>
            <InfoTooltip
              position="top"
              width="200px"
              id="downtimeType"
              isHtml={true}
              content="This Checkbox helps to Show All Leads in Kanban"
            />
          </div>
        </div>
      </div>
      <div className=" border-[1px]"></div>
    </div>
  );
};

export default CRMDefaults;
