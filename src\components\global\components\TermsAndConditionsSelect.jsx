import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
// import { FaEye } from 'react-icons/fa';
import { MdDelete } from 'react-icons/md';
// import { useSearchParams } from 'react-router-dom';
import { BiHide, BiShow } from 'react-icons/bi';
import { TiDocument } from 'react-icons/ti';
import { toast } from 'react-toastify';
import { ReactComponent as Doc } from '../../../assets/svgs/documentprevious.svg';
import { getDecodedHTML } from '../../../helperFunction.js';
import { useUpdateDefaultsMutation } from '../../../slices/defaultsApiSlice.js';
import { Store } from '../../../store/Store';
import { customConfirm } from '../../../utils/customConfirm.js';
import Label from '../../v2/global/components/Label';
import RichTextDescription from '../../v3/global/components/rich-text-description.jsx';
import Input from './Input';
import Modal from './Modal';
import MultiSelect from './MultiSelect.jsx';

const initialTermAndCondition = {
  terms: '',
  description: '',
  page: [],
};

const TermsAndConditionsSelect = ({
  isMobile,
  isTablet,
  selectedTermAndCondition,
  setSelectedTermAndCondition,
  isEdit = false,
  editValue = [],
  page = [],
  isSetAsDefault,
  setformData,
  formData,
}) => {
  // const [searchParam] = useSearchParams();
  const [modalOpen, setModalOpen] = useState(false);
  const [isDefaultCheck, setIsDefaultCheck] = useState(false);
  const [newTermAndCondition, setNewTermAndCondition] = useState(
    initialTermAndCondition
  );

  const [editedTermAndCondition, setEditedTermAndCondition] = useState(
    initialTermAndCondition
  );

  useEffect(() => {
    if (page) {
      setEditedTermAndCondition(() => ({
        ...editedTermAndCondition,
        page: page,
      }));

      setNewTermAndCondition(() => ({
        ...newTermAndCondition,
        page: page,
      }));
    }
  }, [page]); // eslint-disable-line

  const [openSingleTerm, setOpenSingleTerm] = useState(false);
  const [selectedTermIndex, setSelectedTermIndex] = useState(null);
  const [defaultTermIndex, setDefaultTermIndex] = useState(null);

  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const [defaultTermAndCondition, setDefaultTermsAndConditions] = useState();

  const [updateDefaults, { isLoading: isUpdateLoading }] =
    useUpdateDefaultsMutation();
  useEffect(() => {
    if (defaultParam) {
      setDefaultTermsAndConditions(defaultParam?.termsAndConditions);
    }
  }, [defaultParam]);

  const handleSubmit = useCallback(async () => {
    try {
      const updatedTermsAndConditions = [
        ...defaultTermAndCondition,
        newTermAndCondition,
      ];
      setDefaultTermsAndConditions(updatedTermsAndConditions);
      await updateDefaults({
        ...defaultParam,
        termsAndConditions: updatedTermsAndConditions,
      });
      toast.success('Terms and Condition Added Successfully');
      setNewTermAndCondition(initialTermAndCondition);
      setModalOpen(false);
    } catch (error) {
      toast.error('Failed to add Terms and Condition');
    }
  }, [
    defaultTermAndCondition,
    newTermAndCondition,
    updateDefaults,
    defaultParam,
  ]);

  const getTermText = (term) => {
    const terms = term?.value?.terms || term?.terms;
    return terms?.length > 30 ? terms.slice(0, 30) + '...' : terms || '-';
  };

  useEffect(() => {
    if (isSetAsDefault && isDefaultCheck) {
      const handleSetDefaultTerms = async () => {
        try {
          const updatedData = defaultTermAndCondition.map((terms) => {
            const isSelected = selectedTermAndCondition.some(
              (term) => term.value.terms === terms.terms
            );
            if (isSelected) {
              const isDefault = terms.page.includes(page);
              const updatedPages = isDefault
                ? terms.page.filter((val) => val !== page)
                : [...new Set([...terms.page, page])];
              return {
                ...terms,
                page: updatedPages,
              };
            }
            return terms;
          });

          await updateDefaults({
            ...defaultParam,
            termsAndConditions: updatedData,
          });
          setDefaultTermsAndConditions(updatedData);
        } catch (error) {
          toast.error('Failed to update default terms');
        }
      };

      handleSetDefaultTerms();
    }
  }, [
    isSetAsDefault,
    isDefaultCheck,
    selectedTermAndCondition,
    defaultTermAndCondition,
    page,
    updateDefaults,
    defaultParam,
  ]);

  useEffect(() => {
    if (!defaultParam || isEdit) return;

    const findDefaultTerms = defaultParam?.termsAndConditions?.filter((terms) =>
      terms?.page?.includes(page)
    );

    if (findDefaultTerms.length > 0) {
      setSelectedTermAndCondition(
        findDefaultTerms.map((term) => ({
          value: term,
          label: term.terms,
        }))
      );
    } else {
      setSelectedTermAndCondition((prev) => prev || []);
    }
  }, [defaultParam, page, isEdit, setSelectedTermAndCondition]);

  useEffect(() => {
    if (isEdit && editValue.length && defaultParam) {
      const matchingTerms = defaultParam?.termsAndConditions?.filter(
        (defaultTerm) =>
          editValue.some((editTerm) => editTerm?.terms === defaultTerm?.terms)
      );
      setSelectedTermAndCondition((prev) => {
        const seen = new Set(prev.map((term) => term.label));
        const uniqueTerms = matchingTerms
          .filter((term) => !seen.has(term.terms))
          .map((term) => ({
            label: term.terms,
            value: term,
          }));
        return [...prev, ...uniqueTerms];
      });
    }
  }, [isEdit, editValue, setSelectedTermAndCondition, defaultParam]);

  const handleChange = useCallback((field, value) => {
    setNewTermAndCondition((prev) => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  const openModal = useCallback(
    (index) => {
      setOpenSingleTerm(true);
      setSelectedTermIndex(index);
      const selectedTerm = selectedTermAndCondition[index]?.value;
      const defaultTermIndex = defaultTermAndCondition.findIndex(
        (term) =>
          term.terms === selectedTerm.terms &&
          term.description === selectedTerm.description
      );
      if (defaultTermIndex !== -1) {
        setDefaultTermIndex(defaultTermIndex);
      }
      setEditedTermAndCondition({
        terms: selectedTerm.terms,
        description: selectedTerm.description,
        page: selectedTerm.page,
      });
    },
    [selectedTermAndCondition, defaultTermAndCondition]
  );

  const closeModal = useCallback(() => {
    setOpenSingleTerm(false);
    setSelectedTermIndex(null);
    setEditedTermAndCondition(initialTermAndCondition);
  }, []);

  const handleEdit = useCallback(async () => {
    if (selectedTermIndex !== null) {
      try {
        const updatedTermsAndConditions = [...defaultTermAndCondition];
        updatedTermsAndConditions[defaultTermIndex] = editedTermAndCondition;
        setDefaultTermsAndConditions(updatedTermsAndConditions);
        await updateDefaults({
          ...defaultParam,
          termsAndConditions: updatedTermsAndConditions,
        });
        const allSelected = [...selectedTermAndCondition];
        allSelected[selectedTermIndex].value = editedTermAndCondition;
        setSelectedTermAndCondition(allSelected);
        toast.success('Terms and Condition Edited Successfully');
        closeModal();
      } catch (error) {
        toast.error('Failed to edit Terms and Condition');
      }
    }
  }, [
    selectedTermIndex,
    defaultTermIndex,
    editedTermAndCondition,
    defaultTermAndCondition,
    updateDefaults,
    defaultParam,
    selectedTermAndCondition,
    setSelectedTermAndCondition,
    closeModal,
  ]);

  const handleDelete = useCallback(
    async (term, index) => {
      const result = await customConfirm(
        'Are you sure you want to delete this term and condition?',
        'Delete Term and Condition'
      );
      if (!result) return;
      try {
        const updatedTermsAndConditions = [...defaultTermAndCondition];
        const updatedWithPageFilter = updatedTermsAndConditions.map((el) => {
          if (el.page.includes(page[0])) {
            return {
              ...el,
              page: el.page.filter((p) => p !== page[0]),
            };
          }
          return el;
        });

        const filteredTerm = updatedWithPageFilter.filter(
          (el) => el.terms !== term.label
        );
        setDefaultTermsAndConditions(updatedTermsAndConditions);

        await updateDefaults({
          ...defaultParam,
          termsAndConditions: filteredTerm,
        });
        const updatedSelectedTerms = [...selectedTermAndCondition];
        updatedSelectedTerms.splice(index, 1);
        setSelectedTermAndCondition(updatedSelectedTerms);
        toast.success('Terms and Condition Deleted Successfully');
      } catch (error) {
        toast.error('Failed to delete Terms and Condition');
      }
    },
    [
      defaultTermAndCondition,
      updateDefaults,
      defaultParam,
      selectedTermAndCondition,
      setSelectedTermAndCondition,
      page,
    ]
  );

  const multiSelectOptions = useMemo(
    () =>
      defaultTermAndCondition
        ?.filter((el) => el.page?.includes(page[0]) || el?.page?.length === 0)
        .map((el) => ({
          value: {
            terms: el.terms,
            description: el.description,
            page: el.page,
          },
          label: el.terms,
        })),
    [defaultTermAndCondition, page]
  );

  const isOptionSelected = useCallback(
    (option) => {
      return selectedTermAndCondition?.some(
        (selectedTerm) =>
          (selectedTerm?.value?.terms || selectedTerm?.terms) ===
          option?.value?.terms
      );
    },
    [selectedTermAndCondition]
  );

  const handleMultiSelectChange = useCallback(
    (selectedOptions) => {
      const updatedSelection = selectedOptions?.target?.value.map((option) => ({
        label: option.label,
        value: {
          terms: option.value.terms,
          description: option.value.description,
          page: option.value.page || [],
        },
      }));
      setSelectedTermAndCondition(updatedSelection);
    },
    [setSelectedTermAndCondition]
  );
  return (
    <>
      {modalOpen && (
        <Modal
          isMobile={isMobile}
          isTablet={isTablet}
          title={'Add Terms And Conditions'}
          svg={<Doc className="h-8 w-8" />}
          onCloseModal={() => setModalOpen(false)}
          onSubmit={handleSubmit}
          btnIsLoading={isUpdateLoading}
        >
          {() => (
            <div className="flex flex-col">
              <div className="terms-container my-3">
                <div className="terms-input flex items-center gap-x-3">
                  <div className="w-full">
                    <Label>Terms And Condition</Label>
                    <Input
                      type="text"
                      id="editTermsAndConditions"
                      name="editTermsAndConditions"
                      value={newTermAndCondition.terms}
                      placeholder="Enter Value"
                      onChange={(e) => handleChange('terms', e.target.value)}
                    />
                  </div>
                </div>
                <div className="description">
                  <Label>Description</Label>
                  <RichTextDescription
                    value={getDecodedHTML(newTermAndCondition.description)}
                    onChange={(value) => handleChange('description', value)}
                  />
                </div>
              </div>
            </div>
          )}
        </Modal>
      )}
      {openSingleTerm && (
        <Modal
          title={'Edit Term and Condition'}
          onCloseModal={closeModal}
          onSubmit={handleEdit}
        >
          {() => (
            <div className="flex flex-col">
              <div className="terms-container my-3">
                <div className="terms-input flex items-center gap-x-3">
                  <div className="w-full">
                    <Label>Terms And Condition</Label>
                    <Input
                      type="text"
                      id="editTermsAndConditions"
                      name="editTermsAndConditions"
                      placeholder="Enter Value"
                      value={editedTermAndCondition.terms}
                      onChange={(e) =>
                        setEditedTermAndCondition((prevState) => ({
                          ...prevState,
                          terms: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>
                <div className="description">
                  <Label>Description</Label>
                  <RichTextDescription
                    value={getDecodedHTML(editedTermAndCondition.description)}
                    onChange={(value) =>
                      setEditedTermAndCondition((prevState) => ({
                        ...prevState,
                        description: value,
                      }))
                    }
                  />
                </div>
              </div>
            </div>
          )}
        </Modal>
      )}
      <div>
        <MultiSelect
          closeMenuOnSelect
          AddOption="+ Add New Entry"
          handleAddFunction={() => {
            setModalOpen(true);
          }}
          options={multiSelectOptions}
          value={multiSelectOptions?.filter(isOptionSelected)}
          onChange={handleMultiSelectChange}
          className="w-1/2 text-ellipsis"
          isMulti
        />
        <div className="w-full mt-4">
          <div className="flex flex-wrap gap-3">
            {Array.isArray(selectedTermAndCondition) &&
              selectedTermAndCondition?.map((term, idx) => (
                <span
                  key={idx}
                  className="bg-yellow-200 text-yellow-800 px-2 py-1 w-fit items-center rounded-md text-sm flex gap-2"
                >
                  {getTermText(term)}
                  {formData?.termsAndConditionsHide?.[
                    term?.value?.terms || term?.terms
                  ] ? (
                    <BiHide
                      onClick={() => {
                        setformData &&
                          setformData((prev) => {
                            return {
                              ...prev,
                              termsAndConditionsHide: {
                                ...prev?.termsAndConditionsHide,
                                [term?.value?.terms || term?.terms]:
                                  !prev?.termsAndConditionsHide?.[
                                    term?.value?.terms || term?.terms
                                  ] || false,
                              },
                            };
                          });
                      }}
                    />
                  ) : (
                    <BiShow
                      onClick={() => {
                        setformData &&
                          setformData((prev) => {
                            return {
                              ...prev,
                              termsAndConditionsHide: {
                                ...prev?.termsAndConditionsHide,
                                [term?.value?.terms || term?.terms]:
                                  !prev?.termsAndConditionsHide?.[
                                    term?.value?.terms || term?.terms
                                  ] || false,
                              },
                            };
                          });
                      }}
                    />
                  )}
                  <TiDocument
                    onClick={() => openModal(idx)}
                    className="cursor-pointer"
                  />
                  <MdDelete
                    onClick={() => handleDelete(term, idx)}
                    className="cursor-pointer"
                  />
                </span>
              ))}
          </div>
        </div>
        <div className="set-default flex items-center justify-end gap-x-3">
          <input
            type="checkbox"
            checked={isDefaultCheck}
            onChange={(e) => setIsDefaultCheck(e.target.checked)}
          />
          <Label>Set as default</Label>
        </div>
      </div>
    </>
  );
};

export default React.memo(TermsAndConditionsSelect);
