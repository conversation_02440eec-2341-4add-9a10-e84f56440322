import { TrashIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import ImportIcon from '../../assets/images/inward.png';
import { ReactComponent as Close } from '../../assets/svgs/close.svg';
import { ReactComponent as CSV } from '../../assets/svgs/csvlogo.svg';
import { ReactComponent as Upload } from '../../assets/svgs/upload.svg';
import {
  useDeleteDocumentMutation,
  useQueryMastersQuery,
  useUploadDocumentMutation,
} from '../../slices/documentApiSlice';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import DragAndDrop from '../global/components/DragAndDrop';
// import { InfoPopup } from '../global/components/InfoPopup';
import { useMediaQuery } from 'react-responsive';
import { mobileWidth } from '../../helperFunction';
import { InfoTooltip } from '../global/components/InfoTooltip';
import Pagination from '../global/components/Pagination';
import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import { convertToJson } from './utils';

function AddAndManage() {
  const [file, setFile] = useState({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [type, setType] = useState('job');
  const isMobile = useMediaQuery({ query: mobileWidth });

  const [searchParams, setSearchParams] = useSearchParams({
    page: 1,
    limit: PAGINATION_LIMIT,
  });

  const page = +searchParams?.get('page');
  const limit = +searchParams?.get('limit');

  const {
    data: queryData = {},
    isLoading: isLoadingQuery,
    isFetching: isFetchingQuery,
  } = useQueryMastersQuery(
    { page, limit },
    { skip: !page || !limit, refetchOnMountOrArgChange: true }
  );
  const { results: allFiles = [], totalPages, totalResults } = queryData;

  const [uploadDocument, { isLoading: isLoadingUpload }] =
    useUploadDocumentMutation();

  const [deleteDocument] = useDeleteDocumentMutation();

  const readFile = () => {
    const reader = new FileReader();

    reader.onload = async (evt) => {
      const bstr = evt.target.result;
      const wb = XLSX.read(bstr, { type: 'binary' });
      const payload = {
        name: file.name,
        sheets: [],
        type,
      };
      for (let i = 0; i < wb.SheetNames.length; i++) {
        const wsname = wb.SheetNames[i];
        const ws = wb.Sheets[wsname];

        const data = XLSX.utils.sheet_to_csv(ws, { header: 1 });
        payload.sheets.push({
          name: wsname,
          data: convertToJson(data),
        });
      }

      const res = await uploadDocument({ data: payload }).unwrap();
      if (res) {
        setFile({});
      }
    };
    reader.readAsBinaryString(file);
  };

  const handleDelete = async (id, name) => {
    if (
      await customConfirm(`Are you sure you want to delete ${name}?`, 'delete')
    ) {
      try {
        deleteDocument({ id });
      } catch (err) {
        toast.error(err?.response?.data?.message || err.message, {
          theme: 'colored',
          position: 'top-right',
        });
      }
    }
  };

  const openModal = () => {
    setFile({});
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };
  return (
    <div className="w-full ">
      {/* drag and drop section */}
      <div className="w-full gap-[2rem] flex flex-row justify-between">
        <div className="flex justify-end items-center w-full mt-[-10px]">
          <div className="flex items-center  ">
            <Button
              type="button"
              onClick={openModal}
              className="px-6 w-[6rem] h-7 text-xs"
            >
              <img
                src={ImportIcon}
                alt="Import Icon"
                className="w-5 h-5 object-contain relative"
              />
              Import
            </Button>
          </div>
        </div>
      </div>

      {/* Pop up modal */}
      {isModalOpen && (
        <div className="fixed top-0 left-0 w-screen h-screen  bg-black/10 flex justify-center items-center">
          <div className="flex flex-col justify-center items-center bg-white relative rounded-xl shadow-xl p-5  w-[90%] sm:w-[90%] md:w-[60%] lg:w-[46%]">
            {/* 1st column */}
            {/* Modal Header  */}
            <div className="flow-root mb-5 border-b w-[100%]">
              <h4 className="float-left">Upload File</h4>
              <button
                onClick={closeModal}
                className="float-right close-btn py-1 px-4 rounded"
              >
                <Close />
              </button>
            </div>

            {/* 2nd column */}
            {/* Drag and Drop section to import file */}
            <DragAndDrop
              onChange={setFile}
              svg={<Upload />}
              fileType="CSV"
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            />

            {/* 3rd column */}
            {/* Imported file name and logo */}
            {file?.name?.length > 0 ? (
              <div className="w-full flex mt-8 items-center justify-between gap-x-5">
                <CSV className="w-36" />
                <p className="text-black font-semibold">{file.name}</p>
                <Select
                  value={type}
                  onChange={(e) => setType(e.target.value)}
                  options={[
                    { label: 'Job', value: 'job' },
                    { label: 'Quotation', value: 'quotation' },
                    { label: 'Product', value: 'product' },
                  ]}
                />
              </div>
            ) : null}

            {/* 4th column */}
            {/* Cancel and Import button */}
            <div className="flex flex-row mt-10 w-full justify-between">
              {/* Cancel Button */}
              <section className="">
                <Button
                  onClick={() => {
                    closeModal();
                  }}
                  color="white"
                  textColor="text-black"
                  className="border border-zinc-500  !h-7 !text-xs"
                >
                  <Close className="w-4 h-4" />
                  Cancel
                </Button>
              </section>

              {/* Import Button */}
              <section className="ml">
                <Button
                  isLoading={isLoadingUpload}
                  onClick={() => {
                    // First Check that if Any file exist or not if not than show warning
                    if (file instanceof File) {
                      readFile();
                    } else toast.warn('First Select File');
                    closeModal();
                  }}
                  className="ml-4 !px-2 !text-xs !h-7"
                >
                  <img
                    src={ImportIcon}
                    alt="Import Icon"
                    className="w-3 h-3 md:w-4 md:h-4 object-contain relative"
                  />
                  Import
                </Button>
              </section>
            </div>
          </div>
        </div>
      )}

      {/* saved masters section */}
      {isLoadingQuery ? (
        <Spinner />
      ) : (
        <>
          {allFiles.length > 0 ? (
            <div className="w-full bg-white mt-5 ">
              <p className="py-1 px-12 text-gray-500 border-b font-medium bg-slate-100">
                MASTERS
              </p>
              {allFiles && allFiles.length > 0 ? (
                <div className="w-full">
                  {allFiles.map((file, fIdx) => (
                    <div className="w-full border-b py-3 px-8" key={fIdx}>
                      {/* {isFetchingQuery ? (
                        <div className="absolute top-0 left-0 w-full h-full bg-black/5"></div>
                      ) : null} */}
                      <div className="flex justify-between items-center">
                        <h4
                          className={`px-3 py-0 pb-2  ${
                            isFetchingQuery ? 'text-gray-200' : 'text-gray-400'
                          }`}
                        >
                          {file.name}
                        </h4>
                        <div
                          className="flex justify-center items-center px-2 py-1 rounded-md bg-red-primary hover:bg-red-hover hover:cursor-pointer"
                          onClick={() => handleDelete(file.id, file.name)}
                        >
                          <TrashIcon className="w-5 h-5 text-white my-auto" />
                        </div>
                      </div>
                      {file.sheets && file.sheets.length > 0 ? (
                        <div className="grid grid-cols-2 md:grid-cols-3 md:w-[25rem] gap-x-2  mt-3 gap-y-2 xs:w-[18rem] my-0 ml-3 mb-1">
                          {file.sheets.map((sheet, sIdx) => {
                            let content = '';
                            if (sheet.name === 'DROPDOWN') {
                              content =
                                "This page empowers you to seamlessly manage your job template's dropdown parameters. It's your go-to place for adding, editing, or deleting master data. <br /> Remember, the dropdown names here should precisely match the job template parameter names to ensure accurate alignment.";
                            } else if (sheet.name === 'RELATION') {
                              content =
                                "This page is your control center for crafting powerful relations between multiple parameters. Whether it's linking Client IDs to Client Names or other dynamic connections, you can easily add, edit, or delete relations here.<br/> For example, by creating a relation like this: <br />Relation 1:<br />Parameter1(Autofill) : Paramter2 = (Value under Paramter2)?(Paramter1 Dropdown value number)<br />Client Name : Client ID=CL01?1<br />You ensure that when CL01 is selected as the Client ID, Client Name will be automatically filled with 'Client 1.'<br />Simplify data relationships and automate your processes with ease on the Relation Sheet Master page. (Video or take a tour a button)";
                            } else if (sheet.name === 'FORM1') {
                              content =
                                "This page empowers you to define the parameters for your job templates, ensuring precision and customization.<br />Here's how you can set types for parameters to shape the input field behavior:<br /><ul><li><b>DATE:</b> Choose this for date-related parameters, and you'll have a convenient date picker.</li><li><b>NUMERIC:</b> Select this to restrict entries to numeric values only.</li><li><b>ALPHANUMERIC:</b> Opt for this to allow both letters and numbers in the input.</li><li><b>FORMULA:</b> Use this for parameters that depend on calculations, just like A1 + A2.</li><li><b>CHECKBOX:</b> Enable this for checkbox-style entries.</li></ul>Download the Excel file, fill in your details, and tailor your job parameters effortlessly with the specified types.<br /><b>Note: Follow the rules specified in the Excel Template.</b>";
                            }
                            return (
                              <Link
                                key={sIdx}
                                className={`flex justify-center items-center gap-[5px] text-xs py-1 mr-3  rounded-md shadow-md  ${
                                  isFetchingQuery
                                    ? 'text-gray-200 bg-blue-disabled'
                                    : 'text-white bg-blue-primary'
                                }`}
                                to={
                                  !isMobile &&
                                  `/settings/database/importmasters/${sheet.id}`
                                }
                              >
                                {sheet.name}
                                <InfoTooltip
                                  position="bottom"
                                  id={sheet?.id}
                                  width="400px"
                                  isHtml={true}
                                  content={content}
                                ></InfoTooltip>
                              </Link>
                            );
                          })}
                        </div>
                      ) : (
                        <p className="p-8 text-center"></p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="p-8 text-center"></p>
              )}
            </div>
          ) : (
            <></>
          )}
          <Pagination
            page={page}
            limit={limit}
            totalPages={totalPages}
            totalResults={totalResults}
            setPage={(e) =>
              setSearchParams(
                (prev) => {
                  prev.set('page', e);
                  return prev;
                },
                { replace: true }
              )
            }
            setLimit={(e) =>
              setSearchParams(
                (prev) => {
                  prev.set('limit', e);
                  return prev;
                },
                { replace: true }
              )
            }
          />
        </>
      )}
    </div>
  );
}

export default AddAndManage;
