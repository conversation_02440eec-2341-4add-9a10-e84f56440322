import {
  AudioOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  FileTextOutlined,
  LinkOutlined,
  OrderedListOutlined,
  PictureOutlined,
  TableOutlined,
} from '@ant-design/icons';
import { Button, Checkbox, Tooltip } from 'antd';

const getFieldIcon = (type) => {
  switch (type) {
    case 'assets':
    case 'media':
      return <PictureOutlined />;
    case 'hyperlink':
      return <LinkOutlined />;
    case 'dropdown':
    case 'multiCheckbox':
      return <OrderedListOutlined />;
    case 'work order':
    case 'bom':
    case 'forms-create':
      return <FileTextOutlined />;
    case 'table':
      return <TableOutlined />;
    case 'audio':
      return <AudioOutlined />;
    default:
      return <EyeOutlined />;
  }
};

const renderTruncatedText = (text) => {
  if (!text) return '-';

  const stringValue = String(text);
  if (stringValue?.length > 25) {
    return (
      <Tooltip title={stringValue}>
        <div style={{ textAlign: 'center' }}>
          {`${stringValue.slice(0, 25)}...`}
        </div>
      </Tooltip>
    );
  }
  return <div style={{ textAlign: 'center' }}>{stringValue}</div>;
};

const handleExistOrNot = (value) => {
  return value || value?.length > 0 ? (
    <Button
      type="link"
      className="text-green-500"
      icon={<CheckCircleOutlined />}
      style={{ textAlign: 'center' }}
    >
      Exist
    </Button>
  ) : (
    <Button
      type="link"
      className="text-red-500"
      icon={<CloseCircleOutlined />}
      style={{ textAlign: 'center' }}
    >
      Not Added
    </Button>
  );
};

const renderFieldValue = (column, columnName, handleModalOpen) => {
  const value = column?.value;
  const type = column?.type;

  switch (type) {
    case 'assets':
    case 'hyperlink':
    case 'dropdown':
    case 'work order':
    case 'multiCheckbox':
    case 'bom':
    case 'table':
    case 'media':
    case 'audio':
    case 'forms-create':
      return value && value?.length > 0 ? (
        <div style={{ textAlign: 'center' }}>
          <Button
            type="link"
            icon={getFieldIcon(type)}
            onClick={(e) => handleModalOpen(e, value, columnName, type)}
          >
            View
          </Button>
        </div>
      ) : (
        '-'
      );
    case 'form management-create':
      return handleExistOrNot(value);
    case 'checkbox':
      return (
        <div style={{ textAlign: 'center' }}>
          <Checkbox checked={value} disabled />
        </div>
      );
    case 'date':
    case 'select':
    case 'string':
    case 'number':
      return renderTruncatedText(value);
    default:
      return <div style={{ textAlign: 'center' }}>-</div>;
  }
};
const renderFieldValueForSideBar = (column, columnName, handleModalOpen) => {
  const value = column?.value;
  const type = column?.type;

  switch (type) {
    case 'assets':
    case 'dropdown':
    case 'work order':
    case 'multiCheckbox':
    case 'bom':
      return value && Array.isArray(value)
        ? value.map((val) => val?.label || val)?.join(', ')
        : '-';
    case 'hyperlink':
      return value && Array.isArray(value)
        ? value.map((val, index) => (
            <a
              key={index}
              target="_blank"
              rel="noopener noreferrer"
              href={val?.label || val}
              style={{
                marginRight: '8px',
                color: 'blue',
                textDecoration: 'underline',
              }} // Optional styling
            >
              {val?.label || val}
            </a>
          ))
        : '-';

    case 'table':
    case 'media':
    case 'audio':
    case 'forms-create':
      return value && value?.length > 0 ? (
        <div style={{ textAlign: 'center' }}>
          <Button
            type="link"
            icon={getFieldIcon(type)}
            onClick={(e) => handleModalOpen(e, value, columnName, type)}
          >
            View
          </Button>
        </div>
      ) : (
        '-'
      );
    case 'form management-create':
      return handleExistOrNot(value);
    case 'checkbox':
      return (
        <div style={{ textAlign: 'center' }}>
          <Checkbox checked={value} disabled />
        </div>
      );
    case 'date':
    case 'string':
    case 'number':
      return renderTruncatedText(value);
    default:
      return <div style={{ textAlign: 'center' }}>-</div>;
  }
};

const sanitizeColName = (name) => {
  return name?.replace(/\s+/g, '_').replace(/\./g, '');
};

export {
  getFieldIcon,
  renderFieldValue,
  renderFieldValueForSideBar,
  sanitizeColName,
};
