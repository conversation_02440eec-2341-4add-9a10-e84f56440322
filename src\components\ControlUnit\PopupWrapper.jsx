function PopupWrapper({
  children,
  setOpenPopup,
  width = 'w-[66%]',
  height = 'h-3/5',
}) {
  return (
    <div className="fixed top-0 left-0 w-full h-full z-10 bg-black/20 flex flex-col">
      <div className="w-full h-1/5" onClick={() => setOpenPopup(false)}></div>
      <div className={`${width} ${height} flex mx-auto`}>
        <section
          className="w-[10%] h-full"
          onClick={() => setOpenPopup(false)}
        ></section>
        <section className="w-4/5 h-full bg-white rounded">{children}</section>
        <section
          className="w-[10%] h-full"
          onClick={() => setOpenPopup(false)}
        ></section>
      </div>
      <div className="w-full h-1/5" onClick={() => setOpenPopup(false)}></div>
    </div>
  );
}

export default PopupWrapper;
