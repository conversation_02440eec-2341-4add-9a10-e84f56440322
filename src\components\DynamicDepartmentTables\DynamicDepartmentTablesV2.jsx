import { useEffect, useState, useContext } from 'react';
import { useSearchParams } from 'react-router-dom';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import { Store } from '../../store/Store';
import * as XLSX from 'xlsx';
import Button from '../global/components/Button';
import Header from '../global/components/Header';
import Pagination from '../global/components/Pagination';
import Table from '../global/components/Table';
import HistorySidebar from '../Kanban/HistorySidebar';
import DynamicDepartmentRow from './DynamicDepartmentRow';

import { useQueryDepartmentRowsQuery } from '../../slices/departmentRowApiSlice';
import { useGetAllTilesQuery } from '../../slices/orderApiSlice';
import Spinner from '../global/components/Spinner';

const DynamicDepartmentTablesV2 = ({ data }) => {
  const [searchParams, setSearchParams] = useSearchParams({
    page: 1,
    limit: PAGINATION_LIMIT,
  });

  const { data: tiles } = useGetAllTilesQuery();
  const [rows, setRows] = useState([]);
  const [historySidebar, setHistorySidebar] = useState({
    open: false,
    steps: [],
  });

  const {
    defaults: {
      defaultParam: {
        projectDefaults: { taskIdLabel },
      },
    },
  } = useContext(Store);

  const [page, setPage] = useState(1); //eslint-disable-line
  const [limit, setLimit] = useState(25); //eslint-disable-line
  const { _id: depColId, departmentChildNav: childNav, columns } = data;
  const {
    data: depColData = {},
    isLoading: isLoadingGet,
    // isFetching: isFetchingGet,
  } = useQueryDepartmentRowsQuery(
    {
      page,
      limit,
      depColId,
    },
    // { skip: !page || !limit || !depColId, refetchOnMountOrArgChange: true }
    { refetchOnMountOrArgChange: true }
  );
  const { totalPages, totalResults } = depColData;

  const setSteps = (taskId) => {
    let chosenTask = tiles?.find((elem) => elem?.taskId === taskId);
    if (chosenTask) {
      setHistorySidebar({
        open: true,
        steps: chosenTask?.steps,
        orderId: chosenTask?._id,
      });
    }
  };

  useEffect(() => {
    if (depColData?.results) {
      let val = [];
      if (searchParams.get('kanban') === 'true' && columns) {
        val?.push({});
        setRows(val);
        return;
      }
      for (let i of depColData?.results) {
        if (i?.taskId) {
          val?.push({
            ...i?.data,
            taskId: i?.taskId,
            isUpdate: i?._id,
            createdAt: i?.createdAt,
          });
        } else {
          val?.push({ ...i?.data, isUpdate: i?._id, createdAt: i?.createdAt });
        }
      }
      setRows(val?.reverse());
    }
  }, [depColData, columns, searchParams]);

  const deleteRow = (id, isIndex) => {
    if (!isIndex) {
      setRows((prev) => prev?.filter((elem) => elem?.isUpdate !== id));
    } else {
      setRows((prev) => prev?.filter((elem, index) => index !== id));
    }
  };

  const exportToExcel = () => {
    // Format data for Excel
    const formattedData = rows?.map((row) => {
      const formattedRow = {};

      data?.columns?.forEach((column) => {
        let value = row?.[column.name]?.value;
        const type = column?.type;

        switch (type) {
          case 'checkbox':
            value = value ? 'True' : 'False';
            break;

          case 'bom':
          case 'multiCheckbox':
          case 'assets':
          case 'dropdown':
          case 'work order':
            value =
              Array.isArray(value) && value.length > 0
                ? value?.map((val) => val?.label).join(' ,')
                : 'Not Exists';
            break;

          case 'forms':
          case 'form management':
            value =
              Array.isArray(value) && value.length > 0 ? 'Exist' : 'Not Exist';
            break;

          case 'string':
          case 'number':
          case 'select':
            value = value ? value : '';
            break;

          case 'media':
          case 'audio':
            value = value && value.length > 0 ? 'Exist' : 'Not Exist';
            break;

          case 'date':
            value = value ? value : '';
            break;

          case 'hyperlink':
            value =
              Array.isArray(value) && value.length > 0
                ? value?.join(' ,')
                : 'Not Exist';
            break;

          default:
            value = '-';
            break;
        }

        // Assign formatted value to the row based on the column name
        formattedRow[column.name] = value;
      });

      return formattedRow;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet 1');
    const sheetName = childNav?.cname + '.xlsx';
    XLSX.writeFile(wb, sheetName);
  };

  return (
    <>
      <div className="w-full flex justify-between">
        <Header
          title={childNav?.cname}
          description={`Manage ${childNav?.cname} here`}
          hasInfoPopup={false}
          classNames="w-full"
        />
        {/* <div className="flex gap-3">
          <Button
            onClick={() => {
              setOpenFileModal(true);
            }}
            // disabled={!sharedFiles?.length}
            className="w-full !h-7"
            color="green"
          >
            Files
          </Button>
        </div> */}
        <Button color="green" onClick={exportToExcel} className="min-w-[8rem]">
          Export to Excel
        </Button>
      </div>
      <HistorySidebar sidebar={historySidebar} setSidebar={setHistorySidebar} />
      {isLoadingGet ? (
        <Spinner />
      ) : (
        <div className="w-full overflow-x-scroll overflow-y-scroll">
          <Table className="w-full overflow-x-auto mb-44">
            <Table.Head className="w-full">
              <Table.Th>#</Table.Th>
              <Table.Th className={'whitespace-nowrap'}>
                {taskIdLabel || 'Task ID'}
              </Table.Th>
              <Table.Th>Date</Table.Th>
              {data?.columns?.map((column) => {
                return (
                  <Table.Th className="text-center" key={column?.name}>
                    {column?.name}{' '}
                    {column?.isMandatory && (
                      <span className="text-red-500">*</span>
                    )}
                  </Table.Th>
                );
              })}
              <Table.Th></Table.Th>
              <Table.Th></Table.Th>
            </Table.Head>
            <Table.Body className="w-full">
              {rows?.map((val, idx) => {
                return (
                  <Table.Row className={`w-full`} key={idx}>
                    <DynamicDepartmentRow
                      idx={idx}
                      columns={data?.columns}
                      data={data}
                      row={val}
                      deleteRow={deleteRow}
                      setSteps={setSteps}
                      willFlash={
                        searchParams.get('kanban') === 'true' && idx === 0
                      }
                    />
                  </Table.Row>
                );
              })}
            </Table.Body>
          </Table>
          <Button
            onClick={() => {
              setRows((prev) => [...prev, {}]);
            }}
            className="!bg-transparent !text-blue-500 !p-0 !py-2"
          >
            + Add More
          </Button>
          <Pagination
            totalPages={totalPages}
            totalResults={totalResults}
            page={page}
            limit={limit}
            setPage={(val) =>
              setSearchParams(
                (prev) => {
                  prev.set('page', val);
                  return prev;
                },
                { replace: true }
              )
            }
            setLimit={(val) =>
              setSearchParams(
                (prev) => {
                  prev.set('limit', val);
                  return prev;
                },
                { replace: true }
              )
            }
          />
        </div>
      )}
    </>
  );
};

export default DynamicDepartmentTablesV2;
