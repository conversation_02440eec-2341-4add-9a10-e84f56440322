import React, { useRef } from 'react'; //eslint-disable-line
import Webcam from 'react-webcam';

const CaptureCamera = ({
  setImageInfo,
  setOpenCamera,
  capturedImage,
  setpdf,
}) => {
  const webcamRef = useRef(null);

  const videoConstraints = {
    facingMode: { ideal: 'environment' }, // Use back camera on mobile devices
  };

  const captureCamera = () => {
    const imageSrc = webcamRef.current.getScreenshot(); // Capture the image
    if (imageSrc) {
      const newImage = {
        name: `${Date.now()}.png`,
        type: 'image/png',
        data: imageSrc, // Base64 string of the image
      };
      setImageInfo((prev) => [...prev, newImage]); // Update the captured images
    }
  };

  const deleteImage = (index) => {
    const updatedImages = capturedImage.filter((_, i) => i !== index);
    setImageInfo(updatedImages);
  };

  const saveToFile = () => {
    setpdf((prev) => [...prev, ...capturedImage]); // Append captured images to pdf state
    setOpenCamera(false); // Close the camera
  };

  return (
    <div>
      {/* Webcam Stream */}
      <div className="flex gap-1 mt-4 h-full bg-white !z-[99999999]">
        <Webcam
          audio={false}
          ref={webcamRef}
          screenshotFormat="image/png"
          videoConstraints={videoConstraints}
          className="border-2 rounded-md border-gray-300"
          width={1000}
          height={1600}
        />
      </div>

      <div className="flex justify-between items-center mt-4">
        <button
          onClick={captureCamera}
          className="bg-green-500 h-7 text-white px-2 py-1 text-[12px] rounded-md"
        >
          Capture Image
        </button>
        <button
          onClick={saveToFile}
          className="bg-green-500 h-7 text-white px-2 py-1 text-[12px] rounded-md"
        >
          Save
        </button>
      </div>

      {/* Display Captured Images */}
      <div className="mt-4 h-96 overflow-y-auto border border-gray-300 p-2">
        {capturedImage.map((image, index) => (
          <div key={index} className="relative mb-4">
            <img
              src={image.data}
              alt={image.name}
              className="border-2 border-gray-300 rounded-md"
              style={{ maxWidth: '100%', height: 'auto' }}
            />
            <button
              onClick={() => deleteImage(index)}
              className="absolute top-0 right-0 m-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs"
            >
              x
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CaptureCamera;
