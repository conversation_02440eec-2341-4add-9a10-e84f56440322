import { Modal } from 'antd';
import { useEffect, useRef } from 'react';

const FullScreenModal = ({
  ShowModal,
  setShowModal,
  children,
  title,
  onClose,
  onOk,
}) => {
  const dialogRef = useRef();
  useEffect(() => {
    if (ShowModal) {
      dialogRef?.current?.show();
    } else {
      dialogRef?.current?.close();
    }
  }, [ShowModal]);

  const submitModal = () => {
    if (onOk) {
      onOk();
      setShowModal(false);
    } else {
      setShowModal(false);
    }
  };

  const closeModal = () => {
    if (onClose) {
      onClose();
    } else {
      setShowModal(false);
    }
  };

  return (
    <>
      <Modal
        title={title}
        centered
        open={ShowModal}
        onOk={submitModal}
        onCancel={closeModal}
        width="100%"
        styles={{
          body: {
            maxHeight: '80vh',
            overflowY: 'auto',
          },
        }}
      >
        {children}
      </Modal>
    </>
  );
};

export default FullScreenModal;
