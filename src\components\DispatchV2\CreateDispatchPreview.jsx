import { useEffect, useState } from 'react';
import { Badge, Button, Checkbox, InputNumber, Table } from 'antd';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { renderFieldsBasedOnType } from '../../helperFunction';
import { useDeleteRequestV2BySOMutation } from '../../slices/dispatchRequestV2ApiSlice';
import {
  useCreateDispatchV2Mutation,
  useGetLatestDispatchQuery,
  useUpdateDispatchV2Mutation,
} from '../../slices/dispatchV2ApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice';
import Select from '../global/components/Select';
import UploadButton from '../UploadButton';
import MediaPreviewModal from './MediaPreviewModal';
import usePrefixIds from '../../hooks/usePrefixIds';
import { Eye } from 'lucide-react';
import CUAutoStockOutModal from '../ControlUnit/CUAutoStockOutModal';
import { useAddOrderMutation } from '../../slices/outPageApiSlice';
import {
  useAddInPageMutation,
  useCreateGrnMutation,
} from '../../slices/inPageApiSlice';
import ShortUniqueId from 'short-unique-id';

const CreateDispatchPreview = () => {
  const { state } = useLocation();
  const params = new URLSearchParams(location.search);
  const soId = params.get('soId');
  const id = params.get('id');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [formData, setFormData] = useState(state || { salesOrders: [] });
  const [openMediaModal, setOpenMediaModal] = useState(false);
  const [mediaData, setMediaData] = useState([]);
  const uid = new ShortUniqueId();

  // Stock Out Related
  const [isAutoStockOut, setIsAutoStockOut] = useState(false);
  const [openStockOutModal, setOpenStockOutModal] = useState(false);
  const [stockOutItem, setStockOutItem] = useState([]);

  // ID and Template Related States
  const [additionalFields, setAdditionalFields] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);

  // Queries
  const [createDispatch, { isLoading: creatingDispatch }] =
    useCreateDispatchV2Mutation();
  const [editDispatch, { isLoading: edittingDispatch }] =
    useUpdateDispatchV2Mutation();
  const { data: latestDispatch } = useGetLatestDispatchQuery();
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [_deleteDispatchRequest] = useDeleteRequestV2BySOMutation(); // Will Do Later
  const [addOrder, { isError }] = useAddOrderMutation();
  const [createGRN] = useCreateGrnMutation();
  const [addEntry] = useAddInPageMutation();

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'dispatchId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
  });

  useEffect(() => {
    const getCols = async () => {
      const path = '/dispatch/dashboard';
      getTemplates({ path });
    };
    getCols();
  }, [getTemplates]);

  useEffect(() => {
    const setIdFormatFunc = () => {
      if (!latestDispatch || !latestDispatch?.additionalFields) {
        if (templatesData) {
          const defaultTemplate = templatesData?.find((template) =>
            template.name.startsWith('Default')
          );
          setAdditionalFields(defaultTemplate);
          setSelectedTemplate(defaultTemplate);
        }
      } else {
        const templateParamsId =
          searchParams.get('templateId') === 'undefined'
            ? null
            : searchParams.get('templateId');
        if (latestDispatch) {
          const templateToUse = templatesData?.find((template) => {
            return (
              template?._id ===
              (templateParamsId
                ? templateParamsId
                : latestDispatch?.additionalFields?._id)
            );
          });
          setSelectedTemplate(templateToUse);
          setAdditionalFields(templateToUse);
        }
      }
    };
    if (id !== 'null' && id) {
      const templateToUse = templatesData?.find((template) => {
        return template?._id === formData?.additionalFields?._id;
      });
      setSelectedTemplate(templateToUse);
      setAdditionalFields(formData?.additionalFields);
    } else {
      setIdFormatFunc();
    }
  }, [
    searchParams,
    latestDispatch,
    id,
    templatesData,
    formData?.additionalFields,
  ]);

  const handleInputChangeForTemplate = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setTemplateDropDownModal(true);
      setDropdownIdx(idx);
    } else {
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: prev.templateData?.map((el) => {
          if (el.fieldName === fieldName) {
            return {
              ...el,
              fieldValue,
            };
          } else {
            return el;
          }
        }),
      }));
    }
  };

  const initialStockInDataForWaste = {
    isReturn: false,
    batchNo: '',
    lotNo: -1,
    lotNoFormat: {},
    manufacturingDate: Date.now(),
    workerId: null,
    vendor: null,
    store: null,
    storeArea: '',
    inTime: Date.now(),
    inName: '',
    totalWeight: 0,
    unitWeight: 0,
    wieghtForClac: 0,
    uomForCalc: 0,
    inspectionData: {},
    attachments: [],
    inspectionFormFilled: false,
    comment: '',
  };

  const handleQuantityChange = (salesOrderId, itemId, value) => {
    setFormData((prevData) => {
      const updatedSalesOrders = prevData?.salesOrders?.map((so) => {
        if (so.salesOrder === salesOrderId) {
          return {
            ...so,
            items: so.items.map((item) =>
              item.itemId === itemId
                ? {
                    ...item,
                    dispatchQuantity: Math.min(
                      value || 0,
                      item.availableQuantity
                    ),
                  }
                : item
            ),
          };
        }
        return so;
      });
      return { ...prevData, salesOrders: updatedSalesOrders };
    });
  };

  const mediaHandler = (e, itemId) => {
    for (let i in e) {
      let fileName = e[i].name;
      let fileType = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          name: fileName,
          type: fileType,
          data: url,
        };
        setFormData((prevData) => {
          const updatedSalesOrders = prevData?.salesOrders?.map((so) => {
            return {
              ...so,
              items: so?.items?.map((item) =>
                item?.itemId === itemId
                  ? {
                      ...item,
                      media: [...(item?.media || []), data],
                    }
                  : item
              ),
            };
          });
          return { ...prevData, salesOrders: updatedSalesOrders };
        });
      });
    }
  };

  const handleDeleteMedia = (itemId, idx) => {
    setFormData((prevData) => {
      const updatedSalesOrders = prevData?.salesOrders?.map((so) => {
        return {
          ...so,
          items: so?.items?.map((item) =>
            item?.itemId === itemId
              ? {
                  ...item,
                  media: item?.media?.filter((_, index) => index !== idx),
                }
              : item
          ),
        };
      });
      return { ...prevData, salesOrders: updatedSalesOrders };
    });
    setMediaData((prevData) => ({
      ...prevData,
      media: prevData?.media?.filter((_, index) => index !== idx),
    }));
  };

  const columns = [
    {
      title: 'Item Details',
      dataIndex: 'details',
      key: 'details',
      render: (_, record) => {
        const item = formData.salesOrders
          .find((so) => so.salesOrder === record.salesOrderId)
          ?.items.find((i) => i.itemId === record.itemId);
        return item?.details || record.details;
      },
    },
    {
      title: 'Available Quantity',
      dataIndex: 'availableQuantity',
      key: 'availableQuantity',
    },
    {
      title: 'Dispatch Quantity',
      key: 'dispatchQuantity',
      render: (_, record) => {
        const item = formData.salesOrders
          .find((so) => so.salesOrder === record.salesOrderId)
          ?.items.find((i) => i.itemId === record.itemId);

        return (
          <InputNumber
            disabled={record.disableItem}
            min={0}
            max={record.availableQuantity}
            value={item?.dispatchQuantity || 0}
            onChange={(value) => {
              if (value > record.availableQuantity) {
                toast.error(
                  'Dispatch quantity cannot be more than available quantity.'
                );
                return;
              }
              handleQuantityChange(record.salesOrderId, record.itemId, value);
            }}
          />
        );
      },
    },
    {
      title: 'Media',
      key: 'media',
      render: (_, record) => {
        const item = formData.salesOrders
          ?.find((data) => data?.salesOrder === record.salesOrderId)
          ?.items?.find((i) => i?.itemId === record.itemId);
        return (
          <div className="flex gap-x-2 items-center">
            <UploadButton
              accept="*"
              multiple
              onChange={(e) => mediaHandler(e, record.itemId)}
            />
            {item?.media && item?.media?.length > 0 && (
              <Badge
                color="blue"
                className="cursor-pointer"
                count={item?.media?.length}
                onClick={() => {
                  setOpenMediaModal(true);
                  setMediaData(item);
                }}
              />
            )}
          </div>
        );
      },
    },
  ];

  const handleCreateDispatch = async () => {
    const isDispatchValid = formData?.salesOrders?.some((so) =>
      so?.items?.some((item) => item?.dispatchQuantity > 0)
    );

    const isComplete = formData?.salesOrders?.every((so) =>
      so?.items?.every(
        (item) => item?.availableQuantity === item?.dispatchQuantity
      )
    );

    if (!isDispatchValid) {
      toast.error('Please enter valid dispatch quantities.');
      return;
    }
    const data = {
      ...formData,
      additionalFields,
      idData: idCompData?.dataToReturn,
    };
    const res = await createDispatch(data);
    if (!res?.error) {
      toast.success('Dispatch created successfully!');
      if (soId && isComplete) {
        // Handle Delete Request if Comes From Dispatch Request
        // await deleteDispatchRequest({soId})
      }
      if (isAutoStockOut && stockOutItem?.length > 0) {
        let finalItem = stockOutItem;
        addOrder({
          items: finalItem || [],
          chalaanNo: uid.rnd(15),
          workerId: null,
          stockoutReq: null,
        })
          .unwrap()
          .then(() => {
            if (isError) {
              toast.error('Could not checkout', {
                theme: 'colored',
                position: 'top-right',
              });
              return;
            } else {
              toast.success('Stocked Out successfully');
            }
          });

        const itemToStockIn = [];
        finalItem?.map((item) => {
          if (item?.product || item?.part) {
            const wasteQuantity = item?.wastage || 0;
            if (wasteQuantity > 0) {
              itemToStockIn?.push({
                ...initialStockInDataForWaste,
                inventory: {
                  type: item?.type,
                  id: item?.product || item?.part,
                  quantity: wasteQuantity,
                  name: item?.name,
                  isScrap: true,
                },
                selectedUOM: item?.uom,
              });
            }
          }
        });

        const initialGrnData = {
          delivery_address: '',
          delivery_challan: '',
          checked_by: [],
          vehicle_no: '',
          remark: '',
          type: 'inhouse',
          files: [],
          po_id: null,
          invoiceDetails: [],
          grnId: uid.rnd(10),
        };
        createGRN({ data: initialGrnData })
          .unwrap()
          .then((grn) => {
            const grnid = grn?._id;
            Promise.all(
              itemToStockIn?.map((item) => {
                return addEntry({ ...item, grnid }).unwrap();
              })
            );
          });
      }
      navigate('/dispatch/dashboard');
    }
  };

  const handleEditDispatch = async () => {
    const isDispatchValid = formData?.salesOrders?.some((so) =>
      so?.items?.some((item) => item?.dispatchQuantity > 0)
    );
    if (!isDispatchValid) {
      toast.error('Please enter valid dispatch quantities.');
      return;
    }
    const data = {
      ...formData,
      additionalFields,
    };
    const res = await editDispatch({
      id,
      data,
    });
    if (!res?.error) {
      toast.success('Dispatch updated successfully!');
      navigate('/dispatch/dashboard');
    }
  };
  const dataSource = formData?.salesOrders?.flatMap((so) =>
    so?.items?.map((item) => ({
      salesOrderId: so?.salesOrder,
      itemId: item?.itemId,
      originalId: item?.originalId,
      availableQuantity: item?.availableQuantity,
      details: item?.details,
      disableItem: item?.disableItem,
    }))
  );

  const itemsTOBeStockedOut = formData?.salesOrders?.flatMap((so) =>
    so?.items?.map((item) => ({
      item: item?.originalId,
      units: item?.dispatchQuantity,
    }))
  );

  const isDispatchValid = formData?.salesOrders?.some((so) =>
    so?.items?.some((item) => item?.dispatchQuantity > 0)
  );

  return (
    <div className="p-12 bg-white min-h-screen">
      <div className="flex justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Review Dispatch</h1>
          <p className="text-gray-500 mt-1">
            Review the selected items and quantities before creating the
            dispatch.
          </p>
        </div>
        <Button
          onClick={() => {
            if (id) {
              navigate(`/dispatch/dashboard/createDispatch/?id=${id}`);
            } else if (soId) {
              navigate(`/dispatch/dashboard/createDispatch/?soId=${soId}`);
            } else {
              navigate('/dispatch/dashboard/createDispatch');
            }
          }}
        >
          Back
        </Button>
      </div>

      <div className="flex flex-col mt-4">
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">
            Choose Template:
          </label>
          <Select
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            options={templatesData?.map((template) => ({
              value: template,
              name: template.name,
            }))}
            onChange={(e) => {
              if (selectedTemplate === e.target.value) {
                return;
              }
              setAdditionalFields(e.target.value);
              setSelectedTemplate(e.target.value);
              if (selectedTemplate?.idIndex === e.target.value.idIndex) {
                return;
              }
            }}
            value={selectedTemplate}
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium  text-gray-700">
            Dispatch Id
          </label>
          <div className="border-2 rounded-md p-2 bg-gray-100 m-0">
            {!(id !== 'null' && id) ? (
              <IdGenComp {...idCompData} />
            ) : (
              <span>{formData?.dispatchId}</span>
            )}
          </div>
        </div>
      </div>

      <CUAutoStockOutModal
        openModal={openStockOutModal}
        setOpenModal={setOpenStockOutModal}
        rawMaterials={itemsTOBeStockedOut}
        setStockOutItem={setStockOutItem}
        stockInQty={1}
        setIsCUAutoStockOut={setIsAutoStockOut}
        fromDispatch={true}
      />

      <MediaPreviewModal
        openMediaModal={openMediaModal}
        setOpenMediaModal={setOpenMediaModal}
        mediaData={mediaData}
        setMediaData={setMediaData}
        handleDeleteMedia={handleDeleteMedia}
      />
      <div className="flex justify-self-end gap-x-2 items-center">
        <Checkbox
          checked={isAutoStockOut}
          disabled={!isDispatchValid || (id !== 'null' && id)}
          onChange={(e) => {
            setIsAutoStockOut(e.target.checked);
            setOpenStockOutModal(e.target.checked);
          }}
        />
        <label className="text-gray-800 font-semibold text-sm">
          Auto Stock Out
        </label>
        <Eye
          size={20}
          cursor={'pointer'}
          onClick={() => setOpenStockOutModal(true)}
        />
      </div>
      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="itemId"
        pagination={false}
        scroll={{ x: 800 }}
        className="shadow-sm my-6"
      />

      {/* Template Fields */}
      {additionalFields?.templateData?.length > 0 && (
        <div className="w-full mt-5">
          <div className="w-full">
            {/* ANCHOR */}
            {renderFieldsBasedOnType(
              additionalFields,
              handleInputChangeForTemplate,
              templateDropDownModal,
              setTemplateDropDownModal,
              setAdditionalFields,
              newOptionStatus,
              setNewOptionStatus,
              dropdownIdx,
              setDropdownIdx,
              searchParams
            )}
          </div>
        </div>
      )}

      <div className="flex justify-self-end mt-4">
        <Button
          type="primary"
          disabled={!isDispatchValid}
          loading={creatingDispatch || edittingDispatch}
          onClick={() => {
            if (id !== 'null' && id) {
              handleEditDispatch();
            } else {
              handleCreateDispatch();
            }
          }}
        >
          {id !== 'null' && id ? 'Edit Dispatch' : 'Create Dispatch'}
        </Button>
      </div>
    </div>
  );
};

export default CreateDispatchPreview;
