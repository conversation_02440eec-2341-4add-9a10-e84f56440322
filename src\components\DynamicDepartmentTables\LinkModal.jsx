import { useState } from 'react';
import { toast } from 'react-toastify';

import { Button } from 'antd';
import Modal from 'antd/es/modal/Modal';
import { default as ButtonOptiwise } from '../global/components/Button';
import Input from '../global/components/Input';
import Table from '../global/components/Table';

import { isValidUrl } from '../../helperFunction';

const LinkModal = ({ open, setOpen, value, changeHandler }) => {
  const [links, setLinks] = useState(value || []);

  const closeModal = () => {
    setOpen(false);
  };

  const addLink = () => {
    setLinks((prev) => [...prev, '']);
  };

  const handleChange = (e, index) => {
    setLinks((prev) => [
      ...prev?.slice(0, index),
      e.target.value,
      ...prev?.slice(index + 1),
    ]);
  };

  const handleSubmit = () => {
    changeHandler(links);
    closeModal();
  };

  const handleVisit = (url) => {
    if (isValidUrl(url)) {
      window.open(url, '_blank').focus();
    } else {
      toast.error('Invalid URL');
    }
  };

  return (
    <Modal
      title="Fill Forms"
      width={700}
      centered
      open={open}
      onCancel={closeModal}
      footer={[
        <Button //eslint-disable-line
          type="primary"
          onClick={addLink}
          className="absolute left-[20px]"
        >
          Add
        </Button>,
        <Button key="back" onClick={closeModal}>
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          className="bg-[#14BA6D]"
          onClick={handleSubmit}
        >
          Submit
        </Button>,
      ]}
    >
      <div>
        <Table>
          <Table.Head>
            <Table.Th>#</Table.Th>
            <Table.Th>Link</Table.Th>
            <Table.Th></Table.Th>
          </Table.Head>
          <Table.Body>
            {links?.map((elem, index) => {
              return (
                <Table.Row key={index}>
                  <Table.Td>{index + 1}</Table.Td>
                  <Table.Td>
                    <Input
                      value={links?.[index]}
                      placeholder="Enter Link"
                      onChange={(e) => handleChange(e, index)}
                    />
                  </Table.Td>
                  <Table.Td className="flex items-center gap-2">
                    <ButtonOptiwise onClick={() => handleVisit(elem)}>
                      Visit
                    </ButtonOptiwise>
                    <ButtonOptiwise
                      className="bg-red-500"
                      onClick={() => {
                        setLinks((prev) =>
                          prev?.filter((elem, idx) => idx !== index)
                        );
                      }}
                    >
                      Delete
                    </ButtonOptiwise>
                  </Table.Td>
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
      </div>
    </Modal>
  );
};

export default LinkModal;
