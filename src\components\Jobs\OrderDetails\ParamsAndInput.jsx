import { useState } from 'react';
import { DebounceInput } from 'react-debounce-input';
import Select from '../../global/components/Select';

const ParamsAndInput = ({
  gKey,
  handleGoalsDataChange,
  singleGoalsData,
  LinkFieldOptions,
  linkFieldData,
  setLinkFieldData,
  projectStatus = '',
  isEdit = false,
}) => {
  const [showInputs, setShowInput] = useState(false);
  const [showHoverEffect, setShowHoverEffect] = useState(false);

  const isFormula = gKey === 'Number of Batches' || gKey === 'Total Material';

  const isDisabled =
    isFormula ||
    (projectStatus !== 'notStarted' && gKey === 'Batch Size' && isEdit);

  return (
    <div className="font-semibold text-black text-sm">
      <div className="flex justify-between items-center h-8 mb-1">
        <label>
          {gKey}
          {(gKey === 'Order Quantity' || gKey === 'Batch Size') && (
            <span className="text-xl text-red-500 -mt-1">*</span>
          )}
        </label>
        {!isFormula && !isDisabled && (
          <>
            {showInputs ? (
              <Select
                value={linkFieldData?.[gKey] || ''}
                onBlur={() => setShowInput(false)}
                onChange={(e) =>
                  setLinkFieldData((prev) => ({
                    ...prev,
                    [gKey]: e.target.value,
                  }))
                }
                options={[
                  { label: 'None', value: '' },
                  ...(LinkFieldOptions?.map((field) => ({
                    value: `${field?.sheetName}-${field.name}`,
                    label: `${field?.sheetName}-${field.name}`,
                  })) || []),
                ]}
              />
            ) : (
              <div
                className="flex relative justify-center items-center h-8 w-8 aspect-square  rounded-full text-white bg-blue-primary hover:cursor-pointer"
                onDoubleClick={() => setShowInput(true)}
                onMouseEnter={() => setShowHoverEffect(true)}
                onMouseLeave={() => setShowHoverEffect(false)}
              >
                {linkFieldData?.[gKey] ? 1 : 0}
                {showHoverEffect && linkFieldData?.[gKey] && (
                  <div className="absolute bg-blue-primary px-3 py-1 mt-1 z-20 shadow-basic rounded-lg top-full right-0">
                    {linkFieldData?.[gKey]}
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>

      <DebounceInput
        debounceTimeout={400}
        type="number"
        min={1}
        name={gKey}
        value={singleGoalsData?.[gKey]}
        onChange={handleGoalsDataChange}
        readOnly={linkFieldData?.[gKey]}
        className={`border text-left text-black outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full`}
        disabled={isDisabled}
      />
    </div>
  );
};

export default ParamsAndInput;
