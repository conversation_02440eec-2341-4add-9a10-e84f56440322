import { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useReactToPrint } from 'react-to-print';
import { toast } from 'react-toastify';
import { generateDateString } from '../../../helperFunction';
import useHeaderAndFooter from '../../../hooks/useHeaderAndFooter';
import useSize from '../../../hooks/useSize';
import { useLazyGetAllCuProjectsQuery } from '../../../slices/CuProjectAPiSlice';
import { useLazyGetDeviceDataByMachineQuery } from '../../../slices/deviceDataApiSlice';
import { useGetALlDeviceKipsQuery } from '../../../slices/deviceKpiApiSlice';
import { useGetAllMachinesQuery } from '../../../slices/machineApiSlice';
import { Store } from '../../../store/Store';
import Button from '../../global/components/Button';
import ExportButton from '../../global/components/ExportButton';
import NewSelect from '../../global/components/NewSelect';
import GenerateIcon from './../../../assets/images/generate.png';
import DowntimeReport from './DowntimeReport';
import Header from '../../global/components/Header';

const Downtime = () => {
  const [machine, setMachine] = useState({});
  const [date, setDate] = useState(generateDateString());
  const [oldDate, setOldDate] = useState('');
  const [deviceDatas, setDeviceDatas] = useState([]);
  const [oldDeviceDatas, setOldDeviceDatas] = useState([]);
  const [data, setData] = useState({});
  const [oldData, setOldData] = useState({});
  const [details, setDetails] = useState([]);
  const [oldDetails, setOldDetails] = useState([]);
  const [idleTime, setIdleTime] = useState([]);
  const [oldIdleTime, setOldIdleTime] = useState([]);
  const [responsiveDetails, setResponsiveDetails] = useState({
    isPositive: null,
    percentage: 0,
    containerWidth: 0,
  });
  const [panelDetails, setPanelDetails] = useState({
    old: { batch: 0, downTime: 0, upTime: 0 },
    new: { batch: 0, downTime: 0, upTime: 0 },
  });

  const [getAllCuProjects, { isLoading: isLoadingCus }] =
    useLazyGetAllCuProjectsQuery();

  const { data: kpiData = {} } = useGetALlDeviceKipsQuery();
  const { deviceKpis = [] } = kpiData;

  const { data: macData = {} } = useGetAllMachinesQuery();
  const { machines = [] } = macData;

  const [getDeviceDataByMachine] = useLazyGetDeviceDataByMachineQuery();

  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const { header, footer } = useHeaderAndFooter({});

  useEffect(() => {
    const testDate = new Date(
      new Date(date).setDate(new Date(date).getDate() - 1)
    );

    if (testDate.getDay() === 0) {
      const temp = new Date(
        new Date(generateDateString(testDate)).setDate(
          new Date(generateDateString(testDate)).getDate() - 1
        )
      );

      setOldDate(generateDateString(temp));
    } else {
      setOldDate(generateDateString(testDate));
    }
  }, [date]);

  const ref = useRef(null);

  const size = useSize(ref);

  useEffect(() => {
    if (size?.width && data?.width) {
      const containerWidth = size.width;
      const graphWidth = data.width;
      // calculate increase or decrease in pixels
      const diff = containerWidth - graphWidth;
      // calculate percentage of increase or decrease in  with respect to graph width
      const percentage = (diff / graphWidth) * 100;

      if (diff > 0) {
        // if diff is greater
        setResponsiveDetails({ isPositive: true, percentage, containerWidth });
      } else {
        // if diff is lesser
        setResponsiveDetails({
          isPositive: false,
          percentage: percentage * -1,
          containerWidth,
        });
      }
    }
  }, [data, size]);

  const newKpi = defaultParam?.heatMapThresholds?.find(
    (item) => item.machine === machine._id
  );

  useEffect(() => {
    const datasArry = [data, oldData];
    datasArry.forEach((data, dIdx) => {
      let tempIdle = [];

      if (
        data?.dates?.length > 0 &&
        deviceDatas?.length > 0 &&
        deviceKpis?.length > 0
      ) {
        data.dates.forEach((date) => {
          const hr = date.getHours();
          const mn = date.getMinutes();
          let filteredDeviceDatas = [];
          if (dIdx === 0) {
            filteredDeviceDatas = deviceDatas.filter((item) => {
              const deviceDataDate = new Date(item.data.createdAt);
              const ddhr = deviceDataDate.getHours();
              const ddmn = deviceDataDate.getMinutes();

              return ddhr === hr && ddmn === mn && item?.type === 'kpi';
            });
          } else {
            filteredDeviceDatas = oldDeviceDatas.filter((item) => {
              const deviceDataDate = new Date(item.data.createdAt);
              const ddhr = deviceDataDate.getHours();
              const ddmn = deviceDataDate.getMinutes();

              return ddhr === hr && ddmn === mn && item?.type === 'kpi';
            });
          }

          tempIdle.push(...filteredDeviceDatas);
        });
      }

      if (dIdx === 0) {
        setIdleTime(tempIdle);
      } else {
        setOldIdleTime(tempIdle);
      }
    });
  }, [data, deviceDatas, deviceKpis, newKpi, oldData, oldDeviceDatas]);

  useEffect(() => {
    if (defaultParam) {
      // generate graph axis, width and intervals data
      const customShift = defaultParam?.customShiftTimings?.find(
        (cst) => cst.date === date
      );

      setData(
        getTimeRanges(
          customShift?.start || defaultParam.shiftStart,
          customShift?.stop || defaultParam.shiftStop,
          date
        )
      );
      if (oldDate) {
        const customShift = defaultParam?.customShiftTimings?.find(
          (cst) => cst.date === oldDate
        );
        setOldData(
          getTimeRanges(
            customShift?.start || defaultParam.shiftStart,
            customShift?.stop || defaultParam.shiftStop,
            oldDate
          )
        );
      }
    }
  }, [defaultParam, date, oldDate]);

  const getTimeRanges = (startTime, stopTime, date) => {
    if (startTime) {
      let dates = [];
      let intervals = [];

      const start = startTime.split(':');
      const stop = stopTime.split(':');

      const startDate = new Date(
        new Date(date).setHours(+start[0], +start[1], 0, 0)
      );
      const stopDate = new Date(
        new Date(date).setHours(+stop[0], +stop[1], 0, 0)
      );

      [...Array((stopDate - startDate) / 60000 || 0)].forEach((i, idx) => {
        let h = idx / 60 + +start[0];
        let m = (idx % 60) + +start[1];
        if (m === 0) {
          intervals = [
            ...intervals,
            new Date(new Date(date).setHours(h, m, 0, 0)),
          ];
        }
        dates = [...dates, new Date(new Date(date).setHours(h, m, 0, 0))];
      });

      dates = [...dates, stopDate];

      const width = dates.length;

      return { startDate, stopDate, dates, intervals, width };
    }
  };

  const generateHandler = async () => {
    if (!machine?._id || !date) {
      toast.error('Please select machine and date', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Please select machine and date',
      });
      return;
    }

    const [res1, res2, res3] = await Promise.all([
      getDeviceDataByMachine(
        { query: { mac: machine?._id, date } },
        false
      ).unwrap(),
      getAllCuProjects({}, false).unwrap(),
      getDeviceDataByMachine(
        {
          query: {
            mac: machine?._id,
            date: oldDate,
          },
        },
        false
      ).unwrap(),
    ]);

    // get saved deviceData according to selected machine

    if (res1) {
      setDeviceDatas(res1.deviceDatas);
    }

    if (res3) {
      setOldDeviceDatas(res3.deviceDatas);
    }

    // generate graph details according to selected machine
    if (res2) {
      const cuProjects = res2.cuProjects;

      const dates = [date, oldDate];

      dates.forEach((date, dIdx) => {
        const startDate = new Date(date).setHours(0, 0, 0, 0);
        const endDate = new Date(date).setHours(23, 59, 59, 59);
        let tempDetails = [];
        cuProjects.forEach((cuPro) => {
          cuPro?.machineAndOperator?.forEach((mao) => {
            /**
             * check if selected date is not greater than todays date
             * check if machine start time is smaller than todays shift end
             * check if machine stoptime is greater than shift start
             * if yes then push the data
             */

            if (
              new Date(data.startDate) <=
                new Date().setHours(23, 59, 59, 999) &&
              new Date(mao.startTime) <= endDate &&
              new Date(mao?.stopTime || data?.stopDate) >= startDate &&
              mao?.machine?._id === machine?._id
            ) {
              tempDetails.push({
                batchInfo: cuPro?.isMultiProcess
                  ? cuPro?.subProcessData
                  : cuPro.batchInfo,
                project: cuPro.project,
                errorMessages: cuPro.errorMessages,
                mao,
              });
            }
          });
        });

        if (dIdx === 0) {
          setDetails(tempDetails);
        } else {
          setOldDetails(tempDetails);
        }
      });
    }
  };

  const getReportTitle = () => {
    const tempDate = new Date()
      .toLocaleString('en-IN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })
      .replaceAll(',', '')
      .replaceAll(' ', '_');
    return `Report-${tempDate}`;
  };

  const printRef = useRef(null);

  const reactToPrintContent = useCallback(() => {
    return printRef.current;
  }, []);

  const handlePrint = useReactToPrint({
    content: reactToPrintContent,
    documentTitle: getReportTitle(),
    removeAfterPrint: true,
    bodyClass: `p-0 m-0 mx-10`,
    // print: (data)=>{
    //   // const iframe = document.getElementById('my-iframe'); // Replace with the actual ID of your iframe
    //   // const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
    //   const printPromise = new Promise((resolve)=>{
    //       html2canvas(data.body).then((canvas) => {
    //       const imgData = canvas.toDataURL('image/png'); // Convert canvas to base64 image data

    //       const pdf = new jsPDF();
    //       pdf.addImage(imgData, 'PNG', 0, 0, pdf.internal.pageSize.getWidth(), pdf.internal.pageSize.getHeight());
    //       pdf.save('iframe_content.pdf');
    //       resolve();
    //     });
    //   })
    //   return printPromise;
    // }
  });

  return (
    <div className="w-full h-full ">
      <div className="flex justify-between items-center w-full mb-6">
        <div>
          <div className="flex gap-[5px] items-center">
            <Header
              title="Downtime"
              description=""
              infoTitle="Welcome to the Downtime Page"
              infoDesc="Get a comprehensive overview of machine events and performance on our Analytics Downtime Page. The Analytics Downtime Page provides a comprehensive overview of
                machine events, offering insights into start and stop times,
                pauses, errors, downtime, uptime, idle periods, and cycle times.  It empowers users with valuable data for improved
                decision-making and optimizing overall operational efficiency."
              paras={['']}
            />
          </div>
        </div>
        {/* export button has a separate component that handle print pdf or csv */}
        <ExportButton customPrintFunc={handlePrint} />
      </div>

      <div className="w-full flex justify-between items-center gap-2">
        <div className="w-full flex gap-x-8 text-gray-primary items-center">
          <section className="w-1/4 flex flex-col">
            <label className="mb-1 font-semibold text-gray-primary">
              Machine
            </label>
            <NewSelect
              value={machine._id}
              onChange={(e) =>
                setMachine(machines?.find((mac) => mac._id === e.target.value))
              }
              options={machines?.map((mac) => ({
                name: `${mac.machineName} (${mac.machineId})`,
                value: mac._id,
              }))}
              placeholder="Select machine"
            />
          </section>
          <section className="w-1/4 flex flex-col">
            <label className="mb-1 font-semibold text-gray-primary">Date</label>
            <input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className="w-full text-black border outline-[#4085ed80] py-2 pl-4 pr-4 rounded-lg"
            />
          </section>
        </div>

        <Button
          isLoading={isLoadingCus}
          type="button"
          onClick={generateHandler}
          color="orange"
          className="rounded-[8px] w-[138px] h-[32px]"
        >
          <img
            src={GenerateIcon}
            alt="Generate Icon"
            className="w-5 h-5 object-contain relative"
          />
          Generate
        </Button>

        {/* <Button type="button" className="w-1/5" onClick={handlePrint}>
          Export
        </Button> */}
      </div>
      <div ref={printRef} id="custom-print">
        {header()}
        <DowntimeReport
          ref={{
            contentRef: ref,
          }}
          machine={machine}
          panelDetails={panelDetails}
          date={date}
          data={data}
          details={details}
          responsiveDetails={responsiveDetails}
          idleTime={idleTime}
          setPanelDetails={setPanelDetails}
          newKpi={newKpi}
          oldDate={oldDate}
          oldData={oldData}
          oldDetails={oldDetails}
          oldIdleTime={oldIdleTime}
          deviceDatas={deviceDatas}
        />
        {footer()}
      </div>

      {/* <div id="pdf-content" ref={printRef}>
      <HeatMapPanel machineId={machine?._id} panelDetails={panelDetails} />

      <div
        ref={ref}
        className={`mx-auto flex flex-col justify-center items-center text-sm transition-[width] ease-in-out duration-1000 ${
          machine?._id ? 'w-[90%]' : 'w-0'
        }`}
      >
        {machine?._id && date && (
          <>
            <HeatMap
              data={data}
              details={details}
              responsiveDetails={responsiveDetails}
              idleTime={idleTime}
              isFor={'new'}
              setPanelDetails={setPanelDetails}
              kpiParam={newKpi?.kpi}
            />
            <DowntimeTable
              data={data}
              details={details}
              idleTime={idleTime}
              isFor={'new'}
              setPanelDetails={setPanelDetails}
              kpiParam={newKpi?.kpi}
            />
            {oldDate && (
              <>
                <div className="w-full hidden">
                  <HeatMap
                    data={oldData}
                    details={oldDetails}
                    responsiveDetails={responsiveDetails}
                    idleTime={oldIdleTime}
                    isFor={'old'}
                    setPanelDetails={setPanelDetails}
                    kpiParam={newKpi?.kpi}
                  />
                  <DowntimeTable
                    data={oldData}
                    details={oldDetails}
                    idleTime={oldIdleTime}
                    isFor={'old'}
                    setPanelDetails={setPanelDetails}
                    kpiParam={newKpi?.kpi}
                  />
                </div>
              </>
            )}
          </>
        )}
      </div>
      </div> */}
    </div>
  );
};

export default Downtime;
