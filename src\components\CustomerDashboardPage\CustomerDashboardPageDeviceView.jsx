import { Disclosure } from '@headlessui/react';
import { useEffect, useState } from 'react';
import Marquee from 'react-fast-marquee';
import {
  IoChevronDownSharp,
  IoConstruct,
  IoSpeedometerSharp,
  IoTabletLandscape,
} from 'react-icons/io5';
import { useNavigate, useOutletContext } from 'react-router-dom';
import {
  convertSecsToHrsAndMins,
  getIotDeviceData,
} from '../../helperFunction';
import ProgressBar from '../global/components/ProgressBar';
import Table from '../global/components/Table';
import Toggle from '../global/components/Toggle';
import Tooltip from '../global/components/ToolTip';
import CustomerDashboardCurrentGraph from './CustomerDashboardCurrentGraph';

function CardView({
  row,
  rowData,
  handleActions,
  iotDeviceData,
  calculatedData,
  isDaily,
  dataToggle,
}) {
  const {
    isActive,
    isOnline,
    activeHours,
    maintenanceHours,
    needsMaintenance,
    energyCost,
    totalEnergyCost,
    isOverload,
    idleHours,
    prodCount,
    averageCurrent,
  } = calculatedData;

  const colorClass = {
    background: isActive ? 'bg-[#E3FFE3]' : 'bg-[#FCD9D9]',
    highlight: isActive ? 'bg-[#77DD77]' : 'bg-[#ED4040]',
    text: isActive ? 'text-[#77DD77]' : 'text-[#ED4040]',
    progress: isActive ? '#77DD77' : '#ED4040',
    online: isOnline ? 'bg-[#77DD77]' : 'bg-[#ED4040]',
  };

  return (
    <div
      className={`w-full border rounded-md shadow-md overflow-hidden bg-white flex flex-col`}
    >
      <div className="w-full flex justify-evenly text-2xl py-3">
        <Tooltip text={'Overload'} minWidth={'w-fit'}>
          <IoSpeedometerSharp
            className={isOverload ? 'pulse-indicator' : 'text-gray-300'}
          />
        </Tooltip>
        <Tooltip text={'Maintenance'} minWidth={'w-fit'}>
          <IoConstruct
            className={needsMaintenance ? 'pulse-indicator' : 'text-gray-300'}
          />
        </Tooltip>
        <Tooltip text={'Device Status'} minWidth={'w-fit'}>
          <IoTabletLandscape
            className={isOnline ? 'text-green-500' : 'text-red-600'}
          />
        </Tooltip>
      </div>

      <div className="flex flex-col items-center justify-center mb-2">
        <p className="text-3xl font-semibold text-[#343]">
          {convertSecsToHrsAndMins(activeHours)}
        </p>

        <p className="text-xs text-[#aaa]">
          {isDaily ? 'Active Hours' : 'Total Active Hours'}
        </p>

        <section className="w-full px-10">
          <ProgressBar
            max={
              +(isDaily
                ? row?.device?.dailyThreshold
                : row?.device?.maintenanceThreshold) || 100
            }
            progress={
              isDaily
                ? row?.device?.dailyThreshold
                  ? activeHours || 0
                  : 0
                : row?.device?.maintenanceThreshold
                  ? maintenanceHours || 0
                  : 0
            }
            progressColor={colorClass?.progress}
          />
        </section>
      </div>

      <Marquee
        className={`h-8 text-lg font-semibold text-white ${colorClass.highlight}`}
      >
        {row?.machineName}&nbsp;({row?.device?.deviceId})
      </Marquee>

      <div
        className={`flex-1 relative text-sm font-medium ${colorClass.background}`}
      >
        <div className="grid grid-cols-2 gap-x-3 gap-y-1 p-3">
          <div className="w-full">
            <p className="text-gray-700">Machine Health</p>
            <p>{needsMaintenance ? 'Needs Maintenance' : 'Normal'}</p>
          </div>
          <div className="w-full">
            <p className="text-gray-700">Operating load</p>
            <p>{isOverload ? 'Overload' : 'Normal'}</p>
          </div>
          <div className="w-full">
            <p className="text-gray-700">Idle Hours</p>
            <p>{convertSecsToHrsAndMins(idleHours || 0)}</p>
          </div>
          <div className="w-full">
            <p className="text-gray-700">Energy Cost</p>
            <p>{isDaily ? energyCost : totalEnergyCost}</p>
          </div>
          <div className="w-full">
            <p className="text-gray-700">Production Count</p>
            <p>{prodCount || 0}</p>
          </div>
          <div className="w-full">
            <p className="text-gray-700">OEE</p>
            <p>{'100%'}</p>
          </div>
        </div>

        <Disclosure>
          {({ open }) => (
            <>
              <Disclosure.Button
                className={`w-full px-3 py-1 text-white flex justify-between items-center border-b border-white ${colorClass.highlight}`}
              >
                Data
                <IoChevronDownSharp className={open ? 'rotate-180' : ''} />
              </Disclosure.Button>
              <Disclosure.Panel>
                <div className="p-3">
                  <p className="flex justify-between items-center w-full text-gray-700">
                    Average Current:
                    <span className="text-xl text-black">{averageCurrent}</span>
                  </p>
                  <div className={`flex justify-center mb-2`}>
                    {rowData?.cts?.map((ct, cIdx) => (
                      <div
                        key={ct?._id}
                        className={`px-5 border-black ${cIdx + 1 === rowData?.cts?.length ? '' : 'border-r'}`}
                      >
                        <p className="text-gray-700 text-center">{ct?.name}</p>
                        <p className="text-gray-700 text-center">
                          {iotDeviceData?.[ct?.topic] || 0}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </Disclosure.Panel>
            </>
          )}
        </Disclosure>
        <Disclosure>
          {({ open }) => (
            <>
              <Disclosure.Button
                className={`w-full px-3 py-1 text-white  flex justify-between items-center border-b border-white ${colorClass.highlight}`}
              >
                Controls
                <IoChevronDownSharp className={open ? 'rotate-180' : ''} />
              </Disclosure.Button>
              <Disclosure.Panel>
                <div className="flex w-full p-3 gap-3">
                  {rowData?.toggles?.map((tog) => (
                    <section
                      key={tog?._id}
                      className="w-full flex justify-between "
                    >
                      <p className="text-gray-700">{tog?.name}</p>
                      <Toggle
                        checked={iotDeviceData?.[tog?.topic] === '1' || false}
                        name="relayState"
                        onChange={(e) => handleActions(tog, e, 'toggle')}
                      />
                    </section>
                  ))}
                </div>
              </Disclosure.Panel>
            </>
          )}
        </Disclosure>
        <Disclosure>
          {({ open }) => (
            <>
              <Disclosure.Button
                className={`w-full px-3 py-1 text-white flex justify-between items-center ${colorClass.highlight}`}
              >
                Graph
                <IoChevronDownSharp className={open ? 'rotate-180' : ''} />
              </Disclosure.Button>
              <Disclosure.Panel>
                <CustomerDashboardCurrentGraph
                  deviceData={row}
                  dataToggle={dataToggle}
                />
              </Disclosure.Panel>
            </>
          )}
        </Disclosure>
      </div>
    </div>
  );
}

function CustomerDashboardPageDeviceView({
  row,
  idx,
  handleActions,
  view,
  referenceDate,
  dataToggle,
  setOpenSideBar,
}) {
  const navigate = useNavigate();

  const [rowData, setRowData] = useState({
    toggles: [],
    cts: [],
    additionalFields: [],
    activeHours: '-',
    maintenanceHours: '-',
  });
  const [hovered, setHovered] = useState(false);

  const { iotDeviceData = {} } = useOutletContext();

  const isDaily = dataToggle === 'daily';

  const {
    isActive,
    isOnline,
    activeHours,
    maintenanceHours,
    needsMaintenance,
    energyCost,
    totalEnergyCost,
    isOverload,
    idleHours,
    averageCurrent,
    prodCount,
  } = getIotDeviceData(iotDeviceData, row?.device, referenceDate);

  useEffect(() => {
    if (!row?.device?.fields.length) return;

    const toggles = [];
    const cts = [];
    const additionalFields = [];

    for (let i = 0; i < row?.device?.fields.length; i++) {
      const field = row?.device?.fields?.[i];

      if (field?.type === 'toggle') {
        toggles.push(field);
      } else if (field?.name?.startsWith('CT ')) {
        cts.push(field);
      } else {
        additionalFields.push(field);
      }
    }

    setRowData((prev) => ({ ...prev, toggles, cts, additionalFields }));
  }, [row?.device?.fields]);

  if (view === 'card')
    return (
      <CardView
        row={row}
        rowData={rowData}
        handleActions={handleActions}
        idx={idx}
        iotDeviceData={iotDeviceData}
        isDaily={isDaily}
        dataToggle={dataToggle}
        calculatedData={{
          isActive,
          isOnline,
          activeHours,
          maintenanceHours,
          needsMaintenance,
          energyCost,
          totalEnergyCost,
          isOverload,
          idleHours,
          prodCount,
          averageCurrent,
        }}
      />
    );

  if (view === 'table')
    return (
      <Table.Row
        onClick={() => {
          if (!hovered) setOpenSideBar(idx);
        }}
      >
        <Table.Td>{idx + 1}</Table.Td>
        <Table.Td>
          {row?.machineName || '-'}{' '}
          <span
            className="link-hover"
            onClick={() => navigate(row?.device?.deviceId)}
          >
            Show Logs
          </span>
        </Table.Td>
        <Table.Td className={isActive ? '!text-green-600' : '!text-red-600'}>
          {isActive ? 'Active' : 'Inactive'}
        </Table.Td>
        <Table.Td className={isOnline ? '!text-green-600' : '!text-red-600'}>
          {isOnline ? 'Online' : 'Offline'}
        </Table.Td>
        <Table.Td>{averageCurrent}</Table.Td>
        <Table.Td>{isOverload ? 'Overload' : 'Normal'}</Table.Td>
        <Table.Td>
          {isDaily
            ? convertSecsToHrsAndMins(activeHours)
            : convertSecsToHrsAndMins(maintenanceHours)}
        </Table.Td>
        <Table.Td>{convertSecsToHrsAndMins(idleHours || 0)}</Table.Td>
        <Table.Td>{isDaily ? energyCost : totalEnergyCost}</Table.Td>
        <Table.Td
          onMouseOver={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
        >
          <div className="flex gap-6">
            {rowData?.toggles?.map((tog) => (
              <section key={tog?._id} className="w-full justify-between">
                <p className="text-gray-700 mb-1">{tog?.name}</p>
                <Toggle
                  checked={iotDeviceData?.[tog?.topic] === '1' || false}
                  name="relayState"
                  onChange={(e) => handleActions(tog, e, 'toggle')}
                />
              </section>
            ))}
          </div>
        </Table.Td>
      </Table.Row>
    );

  return <>Invalid View</>;
}

export default CustomerDashboardPageDeviceView;
