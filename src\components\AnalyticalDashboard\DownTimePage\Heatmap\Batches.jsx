import { useState } from 'react';

const Batches = ({ batch }) => {
  const [hovered, setHovered] = useState(false);

  return (
    <>
      {hovered && (
        <div
          className="absolute shadow-basic bg-green-dark-primary text-white rounded-xl bottom-[110%]"
          style={{
            left: batch.left || 0,
            width: batch.width > 200 ? batch.width : 200,
          }}
        >
          <h4 className="px-5 py-1 border-b text-center">
            {`Batch ${batch.batchInfo.batchNo}${
              batch?.batchInfo?.process ? ` (${batch?.batchInfo?.process})` : ''
            }`}
          </h4>
          <div className="grid grid-cols-3 text-sm px-5 py-2 gap-y-1 gap-x-2">
            <span>Project:</span>
            <span className="col-span-2">{batch.project.id}</span>
            <span>Target:</span>
            <span className="col-span-2">
              {batch.batchInfo?.['Batch Size']}
            </span>
          </div>
        </div>
      )}
      <section
        className="bg-green-dark-primary hover:shadow-basic text-white rounded overflow-hidden flex justify-center items-center h-full p-1 hover:cursor-pointer hover:z-[100]"
        style={{ left: batch.left || 0, width: batch.width || 0 }}
        onMouseOver={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        id="batch-block"
      >
        {batch.batchInfo.batchNo}
      </section>
    </>
  );
};

export default Batches;