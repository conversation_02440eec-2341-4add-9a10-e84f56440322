import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useCreateStockoutReqMutation } from '../../slices/outPageApiSlice';
import { useGetAllPartsForOptionsQuery } from '../../slices/partApiSlice';
import { useLazyGetProductPagesQuery } from '../../slices/productApiSlice';
import { useLazyGetSubAssemblyPagesQuery } from '../../slices/subAssemblySlice';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import Label from '../v2/global/components/Label';

const StockOutRequest = ({ setShowModal }) => {
  const [inventoryType, setInventoryType] = useState('Part');
  const [data, setData] = useState([]);
  const [Deadline, setDeadline] = useState('');
  const [SelectedItem, setSelectedItem] = useState([]);

  const { data: partsData } = useGetAllPartsForOptionsQuery();
  const [getProducts, { data: productsData }] = useLazyGetProductPagesQuery();
  const [getsubAssemblies, { data: subAssemblyData }] =
    useLazyGetSubAssemblyPagesQuery();

  const [CreateStockOutRequest] = useCreateStockoutReqMutation();
  const user = JSON.parse(localStorage.getItem('user'));

  const handleStockoutQty = (e, idx) => {
    const tmp = SelectedItem?.map((item, index) => {
      if (index === idx) {
        return {
          ...item,
          stockoutqty: +e.target.value,
        };
      } else {
        return item;
      }
    });
    setSelectedItem(tmp);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!Deadline) {
      return toast.error('Deadline Is Required');
    }
    const body = [];
    for (let i = 0; i < SelectedItem?.length; i++) {
      const item = SelectedItem[i];
      if (!item?.stockoutqty) {
        return toast.error('StockOut Quantity Is Required');
      }
      body.push({
        [item?.type]: item?._id,
        stockOutQty: item?.stockoutqty,
        deadline: Deadline,
        createdBy: user?.user?.name,
      });
    }
    const res = await CreateStockOutRequest(body).unwrap();
    toast.success(res?.message);
    setShowModal(false);
    setData([]);
    setDeadline('');
  };

  const addItems = () => {
    return (
      <div>
        <Select
          autoFocus
          closeMenuOnSelect
          options={[
            { value: 'Part', label: 'Parts' },
            { value: 'Product', label: 'Products' },
            { value: 'SubAssembly', label: 'SubAssemblies' },
          ]}
          value={inventoryType}
          onChange={(e) => {
            setInventoryType(e.target.value);
          }}
          className="w-1/2 text-ellipsis"
        />

        <div className="mt-3">
          <MultiSelect
            placeholder={`Select ${inventoryType}`}
            closeMenuOnSelect
            onChange={(e) => {
              setSelectedItem(e.target.value);
            }}
            value={SelectedItem}
            className="w-60 text-ellipsis"
            options={[
              ...(data?.map((e) => {
                return {
                  ...e,
                  value: e,
                  label: e?.name,
                };
              }) || []),
            ]}
          />
        </div>
      </div>
    );
  };

  useEffect(() => {
    switch (inventoryType) {
      case 'Part': {
        const addType = partsData?.map((part) => {
          return {
            ...part,
            type: part?.type === 'PartVariant' ? 'partVariant' : 'part',
          };
        });

        setData(addType);
        break;
      }

      case 'Product': {
        const addType = productsData?.results?.map((product) => {
          return { ...product, type: 'product' };
        });
        setData(addType);
        break;
      }

      case 'SubAssembly': {
        const addType = subAssemblyData?.results?.map((subAssembly) => {
          return { ...subAssembly, type: 'subassembly' };
        });
        setData(addType);
        break;
      }

      default:
        setData([]);
    }
  }, [inventoryType, partsData, productsData, subAssemblyData]);

  useEffect(() => {
    switch (inventoryType) {
      case 'Product':
        getProducts({ page: 1, limit: 10 });
        break;

      case 'SubAssembly':
        getsubAssemblies({ page: 1, limit: 10 });
        break;
    }
  }, [inventoryType, getProducts, getsubAssemblies]);

  return (
    <Modal
      title="Create Stock Out Request"
      onCloseModal={() => {
        setShowModal(false);
      }}
      onSubmit={(e) => handleSubmit(e)}
    >
      {() => {
        return (
          <section>
            {addItems()}
            <div className="input-wrapper mt-3">
              <Label>Deadline Date</Label>
              <Input
                type="date"
                value={Deadline}
                onChange={(e) => {
                  setDeadline(e.target.value);
                }}
              />
            </div>
            <div className="item-details mt-3">
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>Item</Table.Th>
                    <Table.Th>StockOut Quantity</Table.Th>
                    <Table.Th></Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {SelectedItem?.map((item, idx) => {
                    return (
                      <Table.Row key={item?._id}>
                        <Table.Td>{item?.name}</Table.Td>
                        <Table.Td>
                          <Input
                            value={item?.stockoutqty || 0}
                            className="!max-w-[15rem]"
                            onChange={(e) => {
                              handleStockoutQty(e, idx);
                            }}
                          />
                        </Table.Td>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </div>
          </section>
        );
      }}
    </Modal>
  );
};

export default StockOutRequest;
