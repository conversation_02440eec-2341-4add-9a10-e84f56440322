import { useEffect, useState } from 'react';
import Batches from './Batches';
import IdleTimeBlock from './IdleTimeBlock';
import Pointer from './Pointer';

const HeatMap = ({
  data,
  details,
  responsiveDetails,
  idleTime,
  isFor,
  setPanelDetails,
  kpiParam,
}) => {
  const [actionsTimeLine, setActionsTimeLine] = useState([]);
  const [batchesTimeline, setBatchesTimeline] = useState([]);
  // const [breakTimeTimeline, setBreakTimeTimeline] = useState([]);
  const [idleTimeTimeline, setIdleTimeTimeline] = useState([]);

  useEffect(() => {
    if (details) {
      // reset to prevent duplication
      setActionsTimeLine([]);
      setBatchesTimeline([]);
      details.forEach((detail) => {
        const maoData = detail?.mao;
        const batchData = detail?.batchInfo;
        const project = detail?.project;
        const errorMessages = detail.errorMessages;
        // generate machine actions data
        if (maoData) {
          let tempActions = [];

          // set tempActions to time and operator and get pixels according to time

          /**
           * for start time
           * check if container width is greater than left position of start time then add value to tempAction
           *  */
          if (responsiveDetails?.containerWidth >= getPx(maoData.startTime)) {
            tempActions = [
              {
                operator: maoData.operator[0],
                time: maoData.startTime,
                left: getPx(maoData.startTime),
              },
            ];
          }

          // for pause and resume time
          const pauseOp = maoData.operator.filter(
            (op) => op?.action === 'pause'
          );
          const resumeOp = maoData.operator.filter(
            (op) => op?.action === 'resume'
          );

          const pauseErrs = errorMessages.filter(
            (em) =>
              em?.machineAndOperatorId === maoData._id &&
              em.error?.action === 'pause'
          );

          maoData.pauseTime.forEach((t, tIdx) => {
            let newTemp = [];
            // if container width is greater than left postion of pause time then push to newTemp
            if (responsiveDetails?.containerWidth >= getPx(t)) {
              newTemp.push({
                operator: pauseOp[tIdx],
                time: t,
                left: getPx(t),
                errorMessage: pauseErrs[tIdx],
              });
            }

            // if container width is greater than left postion of resume time then push to newTemp
            if (
              responsiveDetails?.containerWidth >=
              getPx(maoData.resumeTime[tIdx])
            ) {
              newTemp.push({
                operator: resumeOp[tIdx],
                time: maoData.resumeTime[tIdx],
                left: getPx(maoData.resumeTime[tIdx]),
              });
            }

            // add values to temp action
            tempActions = [...tempActions, ...newTemp];
          });

          const stopErrs = errorMessages.filter(
            (em) =>
              em.machineAndOperatorId === maoData._id &&
              em.error.action === 'stop'
          );

          /**
           * for stop time
           * check if container width is greater than left position of stop time then add value to tempAction
           *  */
          if (responsiveDetails?.containerWidth >= getPx(maoData.stopTime)) {
            tempActions = [
              ...tempActions,
              {
                operator: maoData.operator[maoData.operator.length - 1],
                time: maoData.stopTime,
                left: getPx(maoData.stopTime),
                errorMessages: stopErrs,
                isRunning: maoData.stopTime,
              },
            ];
          }

          setActionsTimeLine((prev) => [...prev, ...tempActions]);
        }

        // generate batches data

        if (batchData && maoData && project) {
          let newStartTime = null;
          let newStopTime = null;

          // check if start time and stop time is same as than of shift

          const isStartDateSame =
            new Date(maoData?.startTime) >= new Date(data?.startDate);

          const isStopDateSame =
            new Date(maoData?.stopTime) <= new Date(data?.stopDate);

          if (!isStartDateSame) {
            // if not same then find first resume time that is >= shift start time
            const temp = maoData?.resumeTime?.find(
              (time) => new Date(time) >= new Date(data?.startDate)
            );

            // if resume time exists then set it as new start time or set new start time to shift start
            if (temp) {
              newStartTime = temp;
            } else {
              newStartTime = data?.startDate;
            }
          } else {
            // if same then set start time to new start time
            newStartTime = maoData?.startTime;
          }

          if (!isStopDateSame) {
            // if not same then find last pause time that is <= shift stop time

            const temp = maoData?.pauseTime?.findLast(
              (time) => new Date(time) <= new Date(data?.stopDate)
            );

            // if pause time exists and machine status equals pause then set it as new stop time or set new start time to shift start
            if (temp && maoData?.status === 'pause') {
              newStopTime = temp;
            } else {
              /**
               * if not exists or machine status no equal to pause
               * then check if current date is greater than shift stop
               * if greater then use shift stop date else use current date
               */
              newStopTime =
                new Date() > new Date(data?.stopDate)
                  ? data?.stopDate
                  : new Date();
            }
          } else {
            newStopTime = maoData?.stopTime;
          }

          const tempBatchData = {
            batchInfo: batchData,
            project,
            left: getPx(newStartTime),
            width: getPx(newStopTime) - getPx(newStartTime),
          };

          if (responsiveDetails?.containerWidth > getPx(newStartTime)) {
            // push data to batches timeline if containerWidth is greater than left of batch
            setBatchesTimeline((prev) => [...prev, tempBatchData]);
          }
        }
      });
    }
  }, [details, responsiveDetails, data]); // eslint-disable-line

  useEffect(() => {
    if (isFor) {
      setPanelDetails((prev) => ({
        ...prev,
        [isFor]: {
          ...(prev?.[isFor] || {}),
          batch: batchesTimeline.length,
        },
      }));
    }
  }, [batchesTimeline, isFor]); // eslint-disable-line

  useEffect(() => {
    if (idleTime && responsiveDetails && kpiParam) {
      const temp = idleTime?.map((it) => {
        let width = +it?.data?.[kpiParam.toUpperCase()];

        const addPxs = (responsiveDetails.percentage * width) / 100;

        if (responsiveDetails.isPositive) {
          // if diff is greater than 0 add pixels
          width = width + addPxs;
        } else {
          // if diff is less than 0 subtract pixels
          width = width - addPxs;
        }

        return {
          ...it,
          left: getPx(it.data.createdAt),
          width,
        };
      });

      setIdleTimeTimeline(temp);
    }
  }, [idleTime, responsiveDetails, kpiParam]); // eslint-disable-line

  const getPx = (t) => {
    if (responsiveDetails) {
      // actual pixels
      const pixels = (new Date(t) - data.startDate) / 60000;
      // additional pixel required according to percentage with respect to actual pixels
      const addPxs = (responsiveDetails.percentage * pixels) / 100;
      if (responsiveDetails.isPositive) {
        // if diff is greater than 0 add pixels
        return pixels + addPxs;
      } else {
        // if diff is less than 0 subtract pixels
        return pixels - addPxs;
      }
    }
    return 0;
  };

  return (
    <div className="w-full relative left-[20px]" id="heat-map">
      <div className={`w-full flex relative h-12 border border-black/30`}>
        <span className="absolute right-[100.5%] text-gray-600 text-xs top-1/2 -translate-y-1/2">
          Batches
        </span>
        {batchesTimeline?.map((batch, bIdx) => (
          <Batches key={bIdx} batch={batch} />
        ))}
      </div>

      <div className={`w-full border border-black/30 flex relative h-6 mt-4`}>
        <span className="absolute right-[100.5%] text-gray-600 text-xs top-1/2 -translate-y-1/2">
          Complete
        </span>
        {actionsTimeLine?.map((action, aIdx) => {
          if (!action.time) return null;
          return <Pointer key={aIdx} action={action} />;
        })}
      </div>

      <div className={`w-full border border-black/30 flex relative h-6 mt-4`}>
        <span className="absolute right-[100.5%] text-gray-600 text-xs top-1/2 -translate-y-1/2">
          Start/Stop
        </span>
        {actionsTimeLine?.map((action, aIdx) => {
          if (
            action.operator?.action === 'pause' ||
            action.operator?.action === 'resume' ||
            !action.time
          )
            return null;
          return <Pointer key={aIdx} action={action} showError />;
        })}
      </div>

      <div className={`w-full border border-black/30 flex relative h-6 mt-4`}>
        <span className="absolute right-[100.5%] text-gray-600 text-xs top-1/2 -translate-y-1/2">
          Pause/
          <br />
          Resume
        </span>
        {actionsTimeLine?.map((action, aIdx) => {
          if (
            action.operator?.action === 'start' ||
            action.operator?.action === 'stop' ||
            !action.time
          )
            return null;
          return <Pointer key={aIdx} action={action} showError />;
        })}
      </div>

      <div className={`w-full border border-black/30 flex relative h-6 mt-4`}>
        <span className="absolute right-[100.5%] text-gray-600 text-xs top-1/2 -translate-y-1/2">
          Idle/Breaks
        </span>
        {idleTimeTimeline?.map((data, dIdx) => (
          <IdleTimeBlock key={dIdx} data={data} kpiParam={kpiParam} />
        ))}
      </div>

      <div className={`w-full border-b-2 border-black/60 mt-2`}></div>
      <div className={`w-full relative flex justify-between`}>
        {data?.intervals?.map((int, iIdx) => {
          const hr = int.getHours();
          return (
            <p className="relative text-sm w-full h-5 mt-1" key={iIdx}>
              <span className="absolute -translate-x-1/2 left-0">
                <span className="relative">
                  {`${hr < 10 ? 0 : ''}${hr}:00`}
                  <span className="absolute left-1/2 -translate-x-1/2 -top-3/4 rotate-90">
                    -
                  </span>
                </span>
              </span>
            </p>
          );
        })}
        <span className="absolute text-sm right-0 mt-1 translate-x-1/2">
          <span className="relative">
            {(() => {
              const hr = data?.stopDate?.getHours();
              return `${hr < 10 ? 0 : ''}${hr || ''}:00`;
            })()}
            <span className="absolute left-1/2 -translate-x-1/2 -top-3/4 rotate-90">
              -
            </span>
          </span>
        </span>
      </div>
    </div>
  );
};

export default HeatMap;
