import RightSideBar from '../global/components/RightSidebar';
import {
  Table,
  Typography,
  Spin,
  Button,
  Tooltip,
  Tag,
  Modal,
  Empty,
  Space,
} from 'antd';
import { getLocalDate, handlePdf } from '../../helperFunction';
import {
  useGetGrnByIdQuery,
  useDeleteGrnMutation,
} from '../../slices/inPageApiSlice';
import { useState, useContext } from 'react';
import {
  EyeOutlined,
  FilePdfOutlined,
  LoadingOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import MediaPreviewModal from '../DispatchV2/MediaPreviewModal';
import { useLazyGetPdfQuery } from '../../slices/pdfApiSlice';
import ShowTemplateValues from '../v3/global/components/ShowTemplateValues';
import { customConfirm } from '../../utils/customConfirm';
import { Store } from '../../store/Store';
import { toast } from 'react-toastify';
import PreviewInspectionForm from '../../components/Forms/PreviewInspectionForm';
import { useGetCustomColumnsQuery } from '../../slices/customCoulmn.ApiSlice';

const { Title } = Typography;

// Consistent styling constants
const SECTION_TITLE_STYLE = {
  marginBottom: '16px',
  marginTop: '24px',
  display: 'flex',
  alignItems: 'center',
};

const SECTION_BADGE_STYLE =
  'text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full ml-2';

const TABLE_PROPS = {
  pagination: false,
  bordered: true,
  size: 'small',
  className: 'shadow-sm mb-6',
  scroll: { x: true },
};

const GRNSideBar = ({ openSideBar, setOpenSideBar, grnId }) => {
  const { data, isLoading } = useGetGrnByIdQuery({ id: grnId });
  const grnDetails = data?.grn || {};
  const { state, defaults } = useContext(Store);
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const [deleteGRN, { isLoading: isDeletingGRN }] = useDeleteGrnMutation();
  const { data: allCustomCols, isColumnsLoading } = useGetCustomColumnsQuery();
  const isPendingStockIn =
    defaults?.defaultParam?.projectDefaults?.pendingStockIn;

  const [openMediaModal, setOpenMediaModal] = useState(false);
  const [mediaData, setMediaData] = useState([]);
  const [openInspection, setOpenInspection] = useState(false);
  const [inspectionData, setInspectionData] = useState(null);

  // Define common column style for field names
  const fieldColumnStyle = {
    title: 'Field',
    dataIndex: 'field',
    key: 'field',
    className: 'font-medium text-gray-700',
    width: '40%',
  };

  // Define common column style for button actions
  const renderActionButton = (icon, onClick, disabled, tooltip) => (
    <Tooltip title={disabled ? `No ${tooltip}` : `View ${tooltip}`}>
      <Button
        icon={icon}
        type="link"
        size="small"
        onClick={onClick}
        disabled={disabled}
        className="whitespace-nowrap"
      />
    </Tooltip>
  );

  // Helper function to render file/media buttons consistently
  const renderFileButton = (files, tooltip = 'attachments') => {
    const isEmpty = !files?.length;
    return renderActionButton(
      <FilePdfOutlined />,
      () => {
        setOpenMediaModal(true);
        setMediaData(files || []);
      },
      isEmpty,
      tooltip
    );
  };

  const detailsColumns = [
    fieldColumnStyle,
    {
      title: 'Details',
      dataIndex: 'value',
      key: 'value',
    },
  ];

  const detailsData = [
    {
      key: '1',
      field: 'GRN ID',
      value: grnDetails?.id,
    },
    {
      key: '2',
      field: 'Date',
      value: getLocalDate(grnDetails?.createdAt),
    },
    {
      key: '3',
      field: 'Store',
      value: grnDetails?.store?.name,
    },
    {
      key: '4',
      field: 'Remark',
      value: grnDetails?.remark || 'N/A',
    },
    {
      key: '5',
      field: 'Type',
      value: <Tag color="blue">{grnDetails?.type || 'N/A'}</Tag>,
    },
    {
      key: '6',
      field: 'Files',
      value: (
        <Button
          onClick={() => {
            setOpenMediaModal(true);
            setMediaData(grnDetails?.files || []);
          }}
          disabled={!grnDetails?.files || !grnDetails?.files.length}
          icon={<EyeOutlined />}
          type="link"
          size="small"
        >
          {grnDetails?.files?.length > 0
            ? `View (${grnDetails?.files?.length})`
            : 'No files'}
        </Button>
      ),
    },
  ];

  const uniqueColumns = allCustomCols
    ?.filter((col) => col?.pageName === 'GRN')
    ?.map((col) => col?.columnName);

  const dynamicColumns = uniqueColumns?.map((colName) => ({
    title: <span className="whitespace-nowrap">{colName}</span>,
    dataIndex: colName,
    key: colName,
    render: (_, record) => {
      const matched = record?.customColumns?.find(
        (field) => field?.colName === colName
      );
      return <span className="whitespace-nowrap">{matched?.value || '-'}</span>;
    },
  }));

  const inpageTableColumns = [
    {
      title: <span className="whitespace-nowrap">Batch No.</span>,
      dataIndex: 'batchNo',
      key: 'batchNo',
      render: (_, record) => (
        <span className="font-medium whitespace-nowrap">
          {record?.batchNo
            ? `${record?.batchNo} (${record?.lotNo || '-'})`
            : '-'}
        </span>
      ),
    },
    {
      title: <span className="whitespace-nowrap">Name</span>,
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => (
        <span className="whitespace-nowrap">
          {record?.part?.name || record?.product?.name || '-'}
        </span>
      ),
    },
    {
      title: <span className="whitespace-nowrap">Quantity</span>,
      dataIndex: 'quantity',
      key: 'quantity',
      align: 'center',
      render: (_, record) => (
        <Tag color="blue" className="whitespace-nowrap">
          {record?.quantity || '-'}
        </Tag>
      ),
    },
    {
      title: <span className="whitespace-nowrap">Net Quantity</span>,
      dataIndex: 'netQuantity',
      key: 'netQuantity',
      align: 'center',
      render: (_, record) => (
        <Tag color="blue" className="whitespace-nowrap">
          {record?.netQuantity || '-'}
        </Tag>
      ),
    },
    {
      title: <span className="whitespace-nowrap">UOM</span>,
      dataIndex: 'uom',
      key: 'uom',
      render: (_, record) => (
        <span className="whitespace-nowrap">
          {record?.selectedUOM?.uom || '-'}
        </span>
      ),
    },
    {
      title: <span className="whitespace-nowrap">Store Area</span>,
      dataIndex: 'storeArea',
      key: 'storeArea',
      render: (_, record) => (
        <span className="whitespace-nowrap">{record?.storeArea || '-'}</span>
      ),
    },
    {
      title: <span className="whitespace-nowrap">Comment</span>,
      dataIndex: 'comment',
      key: 'comment',
      render: (_, record) => (
        <span className="whitespace-nowrap">{record?.comment || '-'}</span>
      ),
    },
    {
      title: <span className="whitespace-nowrap">Inspection</span>,
      dataIndex: 'inspection',
      key: 'inspection',
      render: (_, record) => {
        const noInspection = !record?.inspectionFormFilled;
        return renderActionButton(
          <EyeOutlined />,
          () => {
            setOpenInspection(true);
            setInspectionData(record);
          },
          noInspection,
          'inspection'
        );
      },
    },
    {
      title: <span className="whitespace-nowrap">Files</span>,
      dataIndex: 'files',
      key: 'files',
      render: (_, record) => renderFileButton(record?.attachments),
    },
    ...(isPendingStockIn
      ? [
          {
            title: <span className="whitespace-nowrap">Approved By</span>,
            dataIndex: 'approvedBy',
            key: 'approvedBy',
            width: 200,
            render: (_, record) => (
              <span className="whitespace-nowrap">
                {record?.approvedOrRejectBy?.name || '-'}
              </span>
            ),
          },
          {
            title: <span className="whitespace-nowrap">Approved At</span>,
            dataIndex: 'approvedAt',
            key: 'approvedAt',
            render: (_, record) => (
              <span className="whitespace-nowrap">
                {record?.approvedOrRejectDate
                  ? getLocalDate(record?.approvedOrRejectDate)
                  : '-'}
              </span>
            ),
          },
        ]
      : []),
    ...(dynamicColumns || []),
  ];

  const vendorData = [
    {
      key: 1,
      field: 'Name',
      value: grnDetails?.inpages?.[0]?.vendor?.name || '-',
    },
    {
      key: 2,
      field: 'Email',
      value:
        (grnDetails?.inpages?.[0]?.vendor?.email || [])
          .filter((email) => email?.trim())
          .join(', ') || '-',
    },
    {
      key: 3,
      field: 'Phone',
      value:
        (grnDetails?.inpages?.[0]?.vendor?.contact || [])
          .filter((contact) => contact?.trim())
          .join(', ') || '-',
    },
    {
      key: 4,
      field: 'Payment Term',
      value: grnDetails?.inpages?.[0]?.vendor?.paymentTerm || '-',
    },
  ];

  const baseInvoiceTableCols = [
    {
      title: <span className="whitespace-nowrap">Invoice ID</span>,
      dataIndex: 'invoiceId',
      key: 'invoiceId',
      render: (_, record) => (
        <span className="whitespace-nowrap">{record?.invoiceId || '-'}</span>
      ),
    },
    {
      title: <span className="whitespace-nowrap">Invoice Amount</span>,
      dataIndex: 'invoiceAmount',
      key: 'invoiceAmount',
      render: (_, record) => (
        <span className="whitespace-nowrap">
          {record?.invoiceAmount || '-'}
        </span>
      ),
    },
    {
      title: <span className="whitespace-nowrap">Invoice Comments</span>,
      dataIndex: 'invoiceComment',
      key: 'invoiceComment',
      render: (_, record) => (
        <span className="whitespace-nowrap">
          {record?.invoiceComment || '-'}
        </span>
      ),
    },
    {
      title: <span className="whitespace-nowrap">Files</span>,
      dataIndex: 'files',
      key: 'files',
      render: (_, record) => renderFileButton(record?.files),
    },
  ];

  const allUniqueColumns = allCustomCols
    ?.filter((col) => col?.pageName === 'grn/invoice')
    ?.map((col) => col?.columnName);

  const additionalColumns =
    allUniqueColumns?.map((item) => ({
      title: <span className="whitespace-nowrap">{item}</span>,
      dataIndex: item,
      key: item,
      render: (_, record) => (
        <span className="whitespace-nowrap">
          {record?.additionalFields?.find((field) => field?.name === item)
            ?.value || '-'}
        </span>
      ),
    })) || [];

  const invoiceTableCols = [
    ...baseInvoiceTableCols,
    ...(additionalColumns || []),
  ];

  const poData = [
    {
      key: 1,
      field: 'PO ID',
      value: grnDetails?.purchase_order?.poID || '-',
    },
    {
      key: 2,
      field: 'Delivery Date',
      value: grnDetails?.purchase_order?.deliveryDate
        ? getLocalDate(grnDetails?.purchase_order?.deliveryDate)
        : '-',
    },
    {
      key: 3,
      field: 'Vendor Name',
      value: grnDetails?.purchase_order?.vendor?.name || '-',
    },
  ];

  const deleteGRNHandler = async () => {
    if (!state?.user?.canDeleteInpage) {
      toast.error('You are not authorized to delete this Inpage');
      return;
    }
    const confirm = await customConfirm(
      'Are you sure you want to delete this GRN?',
      'error'
    );
    if (!confirm) return;
    const res = await deleteGRN({ id: grnId });
    if (!res?.error) {
      toast.success('Deleted successfully');
      setOpenSideBar(false);
    }
  };

  // Render "not found" content consistently
  const renderEmptyState = (message) => (
    <Empty
      image={Empty.PRESENTED_IMAGE_SIMPLE}
      description={message}
      className="my-6 bg-gray-50 py-8 rounded"
    />
  );

  const renderSectionTitle = (title, count = null) => (
    <Title level={5} style={SECTION_TITLE_STYLE} className="px-4">
      <span>{title}</span>
      {count !== null && (
        <span className={SECTION_BADGE_STYLE}>{count} Items</span>
      )}
    </Title>
  );

  return (
    <div>
      {openMediaModal && (
        <MediaPreviewModal
          setMediaData={setMediaData}
          mediaData={{ media: mediaData }}
          openMediaModal={openMediaModal}
          setOpenMediaModal={setOpenMediaModal}
        />
      )}

      {openInspection && (
        <Modal
          open={openInspection}
          onCancel={() => setOpenInspection(false)}
          title="Inspection"
          footer={null}
          width={800}
          centered
          styles={{
            body: {
              maxHeight: `calc(100vh - 150px)`,
              overflowY: 'auto',
            },
          }}
        >
          <PreviewInspectionForm
            inspectionData={inspectionData?.inspectionData}
            inspectionRefData={
              inspectionData?.inspectionRefData ||
              (inspectionData?.part
                ? inspectionData?.part?.defaultFormData
                : inspectionData?.product?.sideBarContent?.product
                    ?.defaultFormData) ||
              {}
            }
          />
        </Modal>
      )}

      <RightSideBar
        openSideBar={openSideBar}
        setOpenSideBar={setOpenSideBar}
        title="GRN Details"
        scale={950}
      >
        {isLoading || isColumnsLoading ? (
          <div className="flex justify-center items-center h-full">
            <Spin size="large" tip="Loading details..." />
          </div>
        ) : (
          <div className="px-4">
            {/* Action buttons - consistent placement and styling */}
            <div className="flex justify-end py-3 mb-6">
              <Space>
                <Tooltip title="Delete GRN">
                  <Button
                    icon={
                      isDeletingGRN ? (
                        <Spin
                          indicator={
                            <LoadingOutlined style={{ fontSize: 16 }} />
                          }
                        />
                      ) : (
                        <DeleteOutlined />
                      )
                    }
                    danger
                    onClick={deleteGRNHandler}
                    loading={isDeletingGRN}
                    size="small"
                  />
                </Tooltip>
                <Tooltip title="Download PDF">
                  <Button
                    loading={isFetchingPdf}
                    icon={!isFetchingPdf && <FilePdfOutlined />}
                    onClick={() => handlePdf(getPdf, grnId, 'grn', null)}
                    type="primary"
                    ghost
                    size="small"
                  />
                </Tooltip>
              </Space>
            </div>

            {/* GRN Details Table */}
            <Table
              columns={detailsColumns}
              dataSource={detailsData}
              {...TABLE_PROPS}
            />

            {/* Stock In Items Section */}
            {renderSectionTitle(
              'Stock In Items',
              grnDetails?.inpages?.length || 0
            )}
            {grnDetails?.inpages?.length > 0 ? (
              <Table
                columns={inpageTableColumns}
                dataSource={grnDetails?.inpages}
                {...TABLE_PROPS}
              />
            ) : (
              renderEmptyState('No Stock In Items available')
            )}

            {/* Invoice Details Section */}
            {renderSectionTitle(
              'Invoice Details',
              grnDetails?.invoiceDetails?.length || 0
            )}
            {grnDetails?.invoiceDetails?.length > 0 ? (
              <Table
                columns={invoiceTableCols}
                dataSource={grnDetails?.invoiceDetails}
                {...TABLE_PROPS}
              />
            ) : (
              renderEmptyState('No Invoice Details available')
            )}

            {/* Purchase Order Details Section */}
            {renderSectionTitle('Purchase Order Details')}
            {grnDetails?.purchase_order ? (
              <Table
                columns={detailsColumns}
                dataSource={poData}
                {...TABLE_PROPS}
              />
            ) : (
              renderEmptyState('No Purchase Order Details available')
            )}

            {/* Vendor Details Section */}
            {renderSectionTitle('Vendor Details')}
            {grnDetails?.inpages?.[0]?.vendor ? (
              <Table
                columns={detailsColumns}
                dataSource={vendorData}
                {...TABLE_PROPS}
              />
            ) : (
              renderEmptyState('No Vendor Details available')
            )}

            {/* Additional Fields Section */}
            {grnDetails?.additionalFields?.templateData?.length > 0 && (
              <>
                {renderSectionTitle('Additional Fields')}
                <div className="mb-6">
                  <ShowTemplateValues
                    template={grnDetails?.additionalFields?.templateData}
                    showTitle={false}
                  />
                </div>
              </>
            )}
          </div>
        )}
      </RightSideBar>
    </div>
  );
};

export default GRNSideBar;
