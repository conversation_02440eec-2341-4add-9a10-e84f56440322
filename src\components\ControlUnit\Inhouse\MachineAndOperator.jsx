import { Fragment, useContext, useState } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import { calculate } from '../../../calculateString';
import {
  useEditMachineAndOperatorMutation,
  useLazyGetCuProjectByIdQuery,
  useUpdateCuProjectMutation,
  useUpdateProcessGoalDataMutation,
} from '../../../slices/CuProjectNewApiSlice';
import { apiSlice } from '../../../slices/apiSlice';
import { useEditWorkOrderMutation } from '../../../slices/createPoApiSlice';
import { useUpdateCuNotificationMutation } from '../../../slices/cuNotificationApiSlice';
import { useEditGoalsTableMutation } from '../../../slices/goalsTableapiSlice';
import { useLazyGetMachineByIdQuery } from '../../../slices/machineApiSlice';
import { useCreateNotificationMutation } from '../../../slices/notificationApiSlice';
import { Store } from '../../../store/Store';
import { DEFAULT_MULTIPLIER_VALUE } from '../../../utils/Constant';
import {
  updateNextProcesses,
  updateWoItemProgress,
} from '../../../utils/updateNextProcess';
import { useUpdateWoItemProgressMutation } from '../../../slices/woItemProgressApiSlice';
import Button from '../../global/components/Button';
import PopupWrapper from '../PopupWrapper';
import StatusBar from '../StatusBar';
import ActionPopup from './ActionPopup';
import OperatorAndMachineDetails from './OperatorAnMachinesDetails';

const getDateTime = () => new Date();

function MachineAndOperator({
  data = {},
  cuProject,
  count,
  errorMessages,
  allEmployees,
  woId,
  getAllPo,
  selectedData,
  itemForJob,
  setCusForStatus,
  selectedCi,
}) {
  const dispatch = useDispatch();

  // const [isOpenInput, setIsOpenInput] = useState(false);
  const [manualCount, setManualCount] = useState('');
  const [popupFor, setPopupFor] = useState('');
  const [operatorPopup, setOperatorPopup] = useState(false);
  const [locationName, setLocationName] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [pauseMessage, setPauseMessage] = useState('');

  const { mqttClient, defaults, state } = useContext(Store);

  const defaultNoti = defaults?.defaultParam?.notificationDefaults;
  const batchSize =
    +cuProject?.batchInfo?.newBatchSize || +cuProject?.batchInfo['Batch Size'];
  const lastestCount = +data?.manualStopData || 0;
  const isManual = data?.machine?.isManual;

  const prevMachineWorkedOperator = data?.operator?.slice(-1)[0]?.user;
  const refreshUrl = '/IP/REFRESH/IOT/DASHBOARD/APP';

  const [updateProgress] = useUpdateWoItemProgressMutation();
  const [getMachineById] = useLazyGetMachineByIdQuery();
  const [getCuProjectById] = useLazyGetCuProjectByIdQuery();
  const [editMachineAndOperator] = useEditMachineAndOperatorMutation();
  const [updateCuProject] = useUpdateCuProjectMutation();
  const [editGoalsTable] = useEditGoalsTableMutation();
  const [createNotification] = useCreateNotificationMutation();
  const [updateCuNotification] = useUpdateCuNotificationMutation();
  const [updateProcessGoalData] = useUpdateProcessGoalDataMutation();
  const [editPo] = useEditWorkOrderMutation();

  let nextProcess = null;
  const flowid = cuProject?.flowId;
  const processArray = cuProject?.productionFlow?.processes;
  const currentIndex = processArray?.findIndex((item) => item?._id === flowid);

  if (currentIndex === processArray?.length - 1) {
    nextProcess = null;
  } else {
    nextProcess = processArray?.[currentIndex + 1];
  }

  // PAUSE HANDLER =========================================>>
  const pauseHandler = async (errorMessages) => {
    const newTime = getDateTime();
    if (manualCount > batchSize - +count) {
      toast.error(`Input value cannot increase ${batchSize - +count}`);
      return;
    }

    if (errorMessages.length === 1 && errorMessages[0] === '') {
      errorMessages = [];
    }

    const res = await editMachineAndOperator({
      id: data._id,
      data: {
        status: 'pause',
        pauseTime: newTime,
        operator: {
          action: 'pause',
          user: selectedEmployee || state.user._id,
          time: newTime,
          message: pauseMessage,
        },
        errorMessages:
          errorMessages?.length === 0
            ? []
            : [
                {
                  error: errorMessages,
                  machineAndOperatorId: data._id,
                  time: newTime,
                  operator: selectedEmployee || state.user._id,
                },
              ],
        machineId: data.machine._id,
        isManual: true,
        manualCount: manualCount + lastestCount,
      },
    }).unwrap();

    if (res) {
      // Update acitivity time for respective workOrder
      await editPo({
        id: woId,
        data: { latestActivityTime: Date.now(), activity: true },
      }).unwrap();

      if (isManual) {
        setManualCount(0);
      }
      if (mqttClient?.publish)
        mqttClient?.publish(
          '/IP/REFRESH/IOT/CONTROLUNIT/APP',
          `REFRESH_PREV_TILE`
        );
      const resp = await updateProcessGoalData({
        id: cuProject?.processGoalData?._id || cuProject?.processGoalData,
        data: {
          dataOnPause: {
            machineAndOperatorId: data._id,
            data: {
              'Batch Size': count,
            },
          },
        },
      }).unwrap();

      if (resp) {
        if (defaultNoti?.Pause) {
          const response = await createNotification({
            data: {
              machine: data.machine._id,
              createdAt: new Date(),
              maoId: data._id,
              cuProject: cuProject._id,
              type: 'pause',
            },
          }).unwrap();

          if (response) {
            if (mqttClient?.publish)
              mqttClient?.publish(refreshUrl, `ACTION_CHANGE`);
          }
        }
      }

      setOperatorPopup(false);
      setPopupFor('');
      setSelectedEmployee('');
      setPauseMessage('');
    }

    dispatch(apiSlice.util.invalidateTags(['CuProjectBatch']));
  };

  // RESUME HANDLER =======================================>>
  const resumeHandler = async () => {
    const newTime = getDateTime();
    // if (selectedEmployee === '') {
    //   toast.error(`Select operator to pause!`);
    //   return;
    // }
    const triggered = JSON.parse(localStorage.getItem('triggered')) || [];
    const res = await getMachineById({ id: data.machine._id }).unwrap();
    if (res.machine.status !== 'active') {
      const resp = await editMachineAndOperator({
        id: data._id,
        data: {
          status: 'active',
          resumeTime: newTime,
          machineId: data.machine._id,
          operator: {
            action: 'resume',
            user: selectedEmployee || state.user._id,
            time: newTime,
          },
        },
      }).unwrap();

      if (resp) {
        localStorage.setItem(
          'triggered',
          JSON.stringify(triggered.filter((t) => t !== data._id))
        );
        setOperatorPopup(false);
        setPopupFor('');
        setSelectedEmployee('');

        // Update acitivity time for respective workOrder
        await editPo({
          id: woId,
          data: { latestActivityTime: Date.now(), activity: true },
        }).unwrap();
      }
    } else {
      setOperatorPopup(false);
      setPopupFor('');
      setSelectedEmployee('');
      toast.error('Machine already active');
    }
    dispatch(apiSlice.util.invalidateTags(['CuProjectBatch']));
  };

  // STOP HANDLER =============================================================>>
  const stopHandler = async (errorMessages) => {
    if (data.status === 'pause') {
      toast.error(
        'Cannot stop project while paused, please resume and then stop.'
      );

      return;
    }
    let outSourcedQuantity =
      selectedData?.model?.outSourceJobs
        ?.find((el) => el?.processId === flowid)
        ?.batchData?.find(
          (el) => +el?.batchNo === +cuProject?.batchInfo?.batchNo
        )?.outQuantity || 0;

    if (manualCount > batchSize - +count) {
      toast.error(`Input value cannot increase ${batchSize - +count}`);
      return;
    }

    let calcStopCount = 0;

    const stopTime = getDateTime();

    const res = await editMachineAndOperator({
      id: data._id,
      data: {
        status: 'complete',
        stopTime,
        errorMessages: errorMessages
          .filter((val) => val !== '')
          .map((val) => ({
            error: val,
            machineAndOperatorId: data._id,
            time: stopTime,
            operator: selectedEmployee || state.user._id,
          })),
        machineId: data.machine._id,
        operator: {
          action: 'stop',
          user: selectedEmployee || state.user._id,
          time: stopTime,
        },
        manualCount: manualCount + lastestCount,
        calcStopCount,
      },
    }).unwrap();

    if (res) {
      let currentDate = new Date();
      setCusForStatus((prev) => {
        let currentCu = prev?.find(
          (cu) =>
            cu?.project === selectedCi?._id &&
            cu?.flowId === selectedData?.flow._id &&
            cu?.batchInfo?.batchNo === selectedData?.batchNo?.[selectedCi?._id]
        );
        let newCus = [];
        for (let i = 0; i < prev?.length; i++) {
          if (prev?.[i]?._id === currentCu?._id) {
            newCus.push({
              ...prev?.[i],
              status: 'complete',
              stopTime: currentDate,
              stopLocation: locationName,
            });
          } else {
            newCus.push(prev?.[i]);
          }
        }
        return newCus;
      });

      // Update acitivity time for respective workOrder
      await editPo({
        id: woId,
        data: { latestActivityTime: Date.now(), activity: true },
      }).unwrap();

      // create a Notification if stopped
      if (nextProcess) {
        const finaldata = {
          batchNo: cuProject.batchInfo.batchNo,
          createInputId: cuProject.project._id,
          index: currentIndex,
          action: 'stop',
        };

        await updateCuNotification({ data: finaldata }).unwrap();
      }

      if (defaultNoti?.Stop) {
        const response = await createNotification({
          data: {
            machine: data.machine._id,
            createdAt: new Date(),
            maoId: data._id,
            cuProject: cuProject._id,
            type: 'stop',
          },
        }).unwrap();

        if (response) {
          if (mqttClient?.publish)
            mqttClient?.publish(refreshUrl, `ACTION_CHANGE`);
        }
      }

      if (
        processArray?.[currentIndex + 1]?.processCategory === 'Outsource' &&
        currentIndex !== processArray?.length - 1
      ) {
        const response = await createNotification({
          data: {
            machine: data.machine._id,
            createdAt: new Date(),
            maoId: data._id,
            cuProject: cuProject._id,
            type: 'outsource',
          },
        }).unwrap();

        if (response) {
          if (mqttClient?.publish)
            mqttClient?.publish(refreshUrl, `OUT_SOURCE`);
        }
      }

      if (mqttClient?.publish)
        mqttClient?.publish(
          '/IP/REFRESH/IOT/CONTROLUNIT/APP',
          `REFRESH_PREV_TILE`
        );

      // get cuProject by id
      const checkCcuProject = await getCuProjectById({
        id: cuProject._id,
      }).unwrap();

      // check if every machineAndOperator is complete
      const check = checkCcuProject.cuProject.machineAndOperator.every(
        (mao) => mao.status === 'complete'
      );
      const manualMaos = checkCcuProject?.cuProject?.machineAndOperator?.filter(
        (mao) => mao?.machine?.isManual
      );

      let newCount = 0;

      manualMaos?.forEach((mao) => {
        if (mao?.manualStopData > 0) {
          newCount = newCount + mao?.manualStopData;
        } else {
          newCount =
            newCount + mao?.manualPauseData?.findLast((i) => i > -1) || 0;
        }
      });

      const newTempData = {
        ['Batch Size']: +newCount + +outSourcedQuantity,
      };

      if (check) {
        // if every machineAndOperator is complete set cuProject status to complete
        const update = await updateCuProject({
          id: checkCcuProject.cuProject._id,
          data: {
            status: 'complete',
            stopTime,
            stopLocation: locationName,
          },
        }).unwrap();
        if (update) {
          const newData = update?.cuProject;
          const allGoalsTables = newData?.project?.goalsTable;
          const productionFlow = newData?.productionFlow;
          const goalsTable = allGoalsTables?.find(
            (gt) => gt.flowId === newData?.flowId
          );

          const currentFlowIdIdx = productionFlow?.processes.findIndex(
            (pro) => pro._id === newData?.flowId
          );

          const { multiplier = DEFAULT_MULTIPLIER_VALUE } = goalsTable;

          if (multiplier?.startsWith('*')) {
            newTempData['Batch Size'] = calculate(
              `${newTempData['Batch Size']}${multiplier.replace('*', '/')}`
            );
          } else if (multiplier?.startsWith('/')) {
            newTempData['Batch Size'] = calculate(
              `${newTempData['Batch Size']}${multiplier.replace('/', '*')}`
            );
          }

          newTempData['Batch Size'] = Math.floor(newTempData?.['Batch Size']);

          // for process that is multiprocess
          if (newData?.isMultiProcess) {
            // modify tableData from updated cuProject
            const tableData = goalsTable?.tableData?.map((table) => {
              // run for every subprocess subprocess
              updateNextProcesses(
                productionFlow,
                newData,
                newTempData,
                currentFlowIdIdx,
                allGoalsTables,
                editGoalsTable
              );

              // modify the data of batch in goals table for stopped cu project
              if (table?.batchNo === newData?.batchInfo?.batchNo) {
                // variable to store the index of subprocess process that is stopped
                let stoppedIdx;
                return {
                  ...table,
                  status:
                    +newData?.subProcessIndex ===
                    table?.subProcessData?.length - 1
                      ? 'complete'
                      : table?.status,
                  subProcessData: table?.subProcessData?.map((item, iIdx) => {
                    if (iIdx === newData?.subProcessIndex) {
                      // update the stopped sub process status
                      stoppedIdx = iIdx;
                      return { ...item, status: 'complete' };
                    }

                    // update batch size of all the sub processes after stopped sub process
                    if (iIdx >= stoppedIdx && stoppedIdx >= 0) {
                      return {
                        ...item,
                        ['Batch Size']:
                          newTempData?.['Batch Size'] || item?.['Batch Size'],
                        newBatchSize: calculate(
                          `${
                            newTempData?.['Batch Size'] || item?.['Batch Size']
                          }${
                            goalsTable?.multiplier || DEFAULT_MULTIPLIER_VALUE
                          }`
                        ),
                      };
                    }

                    return item;
                  }),
                };
              }
              return table;
            });

            if (tableData.length > 0)
              await editGoalsTable({
                id: goalsTable._id,
                data: {
                  tableData,
                },
              }).unwrap();
          } else {
            // for process that is not multiprocess

            const tableData = goalsTable?.tableData?.map((table) => {
              // modify the data of batch in goals table for stopped cu project
              if (table?.batchNo === newData?.batchInfo?.batchNo) {
                return {
                  ...table,

                  status: 'complete',
                  outputSize: newTempData?.['Batch Size'],
                };
              }
              return table;
            });

            if (tableData.length > 0)
              await editGoalsTable({
                id: goalsTable._id,
                data: {
                  tableData,
                },
              }).unwrap();

            // TODO: add logic to update itemProgress

            updateNextProcesses(
              productionFlow,
              newData,
              newTempData,
              currentFlowIdIdx,
              allGoalsTables,
              editGoalsTable
            );
          }

          // Update PROGRESS
          // if current process is the last process then update woItemProgress
          if (currentFlowIdIdx === productionFlow?.processes?.length - 1) {
            await updateWoItemProgress(
              updateProgress,
              {
                indexId: itemForJob?._id,
                workOrder: woId,
              },
              {
                producedQuantity: newTempData?.['Batch Size'] || 0,
                cuProject: newData?._id,
              }
            );
          }

          newTempData['Batch Size'] = calculate(
            `${newTempData['Batch Size']}${multiplier}`
          );
          if (cuProject?.processGoalData) {
            await updateProcessGoalData({
              id: cuProject?.processGoalData?._id || cuProject?.processGoalData,
              data: {
                dataOnStop: {
                  machineAndOperatorId: data._id,
                  data: newTempData,
                },
              },
            }).unwrap();
          }
        }
      }
      setOperatorPopup(false);
      setPopupFor('');
      setSelectedEmployee('');
    }
    dispatch(apiSlice.util.invalidateTags(['CuProjectBatch']));
    setOperatorPopup(false);
    setPopupFor('');
    setSelectedEmployee('');
    getAllPo();
  };

  const handleSend = async () => {
    if (nextProcess?.processCategory === 'Outsource') {
      toast.success('Sent successfully');
      return;
    }

    if (manualCount) {
      // if (nextProcess?.processCategory === 'Outsource') {
      //   toast.success('Sent successfully');
      //   return;
      // }

      if (manualCount <= batchSize - +count) {
        try {
          const res = await editMachineAndOperator({
            id: data._id,
            data: {
              status: 'send',
              manualCount: manualCount + lastestCount,
              isManual,
              machineId: data.machine._id,
              // calcStopCount,
            },
          }).unwrap();

          if (res) {
            // const macandOperator = res.data.cuProject.machineAndOperator;
            // const macCnt = macandOperator.find(
            //   (item) => item.machine._id === currMid
            // );
            setManualCount(0);
            toast.success('Sent Successfully!');
            if (mqttClient?.publish)
              mqttClient?.publish(
                '/IP/REFRESH/IOT/CONTROLUNIT/APP',
                `REFRESH_PREV_TILE`
              );
          }
        } catch (error) {
          console.log(error); // eslint-disable-line
        }
      } else {
        toast.error(`Input value cannot increase ${batchSize - +count}`);
      }
    }
  };

  return (
    <>
      {operatorPopup && (
        <PopupWrapper setOpenPopup={() => setOperatorPopup(false)}>
          <ActionPopup
            popupFor={popupFor}
            setOperatorPopup={setOperatorPopup}
            errorMessages={errorMessages}
            setLocationName={setLocationName}
            locationName={locationName}
            setPauseMessage={setPauseMessage}
            pauseMessage={pauseMessage}
            prevEmployee={prevMachineWorkedOperator}
            allEmployees={allEmployees}
            selectedEmployee={selectedEmployee}
            setSelectedEmployee={setSelectedEmployee}
            submitFunction={
              popupFor === 'pause'
                ? pauseHandler
                : popupFor === 'resume'
                  ? resumeHandler
                  : stopHandler
            }
            cuProject={cuProject}
            itemForJob={itemForJob}
            inhOutput={+count + +manualCount}
          />
        </PopupWrapper>
      )}
      <div className="w-full rounded-lg border border-slate-300 py-2 mb-3 flex bg-white">
        <div className="w-full">
          <OperatorAndMachineDetails data={data} />
          <div className="px-4 pt-2 mt-2">
            <StatusBar
              id={cuProject?._id}
              type={'mac'}
              macId={data?.machine?._id}
            />
          </div>
          <Fragment>
            <section className="flex justify-center mt-2">
              <label
                htmlFor="macCount"
                className="ml-3 text-[14px]  md:text-3xl "
              >
                {' '}
                Unit Submitted : {data?.manualStopData}
              </label>
            </section>
            <section className="flex justify-evenly mt-2">
              <div className="inline-flex items-center">
                {/* <label className=" mr-2 text-xl ">
                  Submit Unit Produced :{' '}
                </label> */}
                {/* <div className="relative inline-block w-8 h-4 rounded-full cursor-pointer mt-1">
                  <input
                    id={data?._id}
                    type="checkbox"
                    checked={isOpenInput}
                    onChange={() => setIsOpenInput(!isOpenInput)}
                    className="absolute w-10 h-5 transition-colors duration-300 rounded-full appearance-none cursor-pointer peer bg-blue-gray-100 bg-white checked:bg-blue-500 peer-checked:border-blue-500 peer-checked:before:bg-blue-500"
                  />
                  <label
                    htmlFor={data?._id}
                    className="before:content[''] absolute top-[55.8%] -left-1 h-6 w-6 -translate-y-2/4 cursor-pointer rounded-full border border-blue-gray-100 bg-white shadow-md transition-all duration-300 before:absolute before:top-2/4 before:left-2/4 before:block before:h-10 before:w-10 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity hover:before:opacity-10 peer-checked:translate-x-full peer-checked:border-blue-500 peer-checked:before:bg-blue-500"
                  ></label>
                </div> */}
              </div>
              {/* {isOpenInput && ( */}
              <div className="flex w-full mt-4 mb-2 px-4">
                {/* <Input
                  type="number"
                  placeholder="Enter Count"
                  className="px-4 py-2 rounded-md text-lg w-[40%]"
                  value={manualCount}
                  onChange={(e) => {
                    setManualCount(+e.target.value);
                  }}
                  min={0}
                  max={batchSize - count}
                /> */}
                <input
                  type="number"
                  id="macCount"
                  placeholder="Submit Unit Produced"
                  value={manualCount}
                  onChange={(e) => {
                    setManualCount(+e.target.value);
                  }}
                  min={0}
                  max={batchSize - count}
                  className="px-4 rounded-l-md border-slate-200 w-full border-y-[1px] border-l-[1px] outline-slate-200 focus:border-slate-400 outline-solid"
                />
                <button
                  disabled={data.status === 'pause'}
                  onClick={() => {
                    if (manualCount > batchSize && isManual) {
                      toast.error(
                        `Added value is greater than batch size (${batchSize}) for ${data?.machine?.machineName}`
                      );
                      return;
                    } else {
                      handleSend();
                    }
                  }}
                  className={`px-4 py-2 border rounded-r-md text-white text-lg focus:outline-none ${
                    data.status === 'pause'
                      ? `bg-blue-disabled hover:cursor-not-allowed`
                      : `bg-blue-primary hover:bg-green-hover`
                  }`}
                >
                  ➤
                </button>
              </div>
              {/* )} */}
            </section>
          </Fragment>
          <div className="w-full flex justify-center mt-4 mb-2 pr-4">
            {data?.status !== 'complete' && (
              <div className="w-full flex justify-end gap-x-2">
                {/* <div className="flex flex-row justify-start ">
                  <Button
                    className="py-2 bg-yellow-500 !h-10 !text-xs"
                    color=""
                    width=""
                    type="button"
                    // onClick={() => {
                    //   setIsScan(true);
                    // }}
                  >
                    Scan QR
                  </Button>
                </div> */}
                {data?.status === 'active' ? (
                  <Button
                    color=""
                    onClick={() => {
                      if (manualCount > +batchSize && isManual) {
                        toast.error(
                          `Added value is greater than batch size (${batchSize}) for ${data?.machine?.machineName}`
                        );
                        return;
                      }
                      setOperatorPopup(true);
                      setPopupFor('pause');
                    }}
                    size="sm"
                    className="bg-green-500"
                  >
                    PAUSE
                  </Button>
                ) : (
                  <Button
                    size="sm"
                    color="blue"
                    onClick={() => {
                      setOperatorPopup(true);
                      setPopupFor('resume');
                      // resumeHandler();
                    }}
                  >
                    Resume
                  </Button>
                )}
                <Button
                  color=""
                  disabled={data?.status === 'pause'}
                  className={`bg-red-500 ${
                    data?.status === 'pause' ? 'line-through' : ''
                  }`}
                  size="sm"
                  onClick={() => {
                    if (manualCount > +batchSize && isManual) {
                      toast.error(
                        `Added value is greater than batch size (${batchSize}) for ${data?.machine?.machineName}`
                      );
                      return;
                    }
                    setOperatorPopup(true);
                    setPopupFor('stop');
                  }}
                >
                  STOP
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default MachineAndOperator;
