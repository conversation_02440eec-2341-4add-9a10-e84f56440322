import RightSidebar from '../global/components/RightSidebar';
import { Table, Tag, Typography, Space } from 'antd';
import {
  FileTextOutlined, // GRN ID
  TagOutlined, // Status
  ClockCircleOutlined, // Requested On
  UserOutlined, // Approved/Rejected By
  InboxOutlined, // Inventory Name
  NumberOutlined, // Requested Quantity
  BarcodeOutlined, // Batch No
  ShopOutlined, // Destination Store
  EnvironmentOutlined, // Store Area
  CommentOutlined, // Comment
  CalendarOutlined, // Last Updated
} from '@ant-design/icons';
import { getLocalDateTime } from '../../helperFunction';

const { Title } = Typography;

const PendingInpageSideBar = ({ openSideBar, setOpenSideBar, data }) => {
  const getStatusTagColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'gold';
      case 'approved':
        return 'green';
      case 'rejected':
        return 'red';
      default:
        return 'default';
    }
  };
  const columns = [
    {
      title: 'Field',
      dataIndex: 'field',
      key: 'field',
      width: '50%', // Half-width field column
      render: (text, record) => (
        <Space>
          <span style={{ color: record.color }}>{record.icon}</span>{' '}
          {/* Colored Icons */}
          {text}
        </Space>
      ),
    },
    {
      title: 'Details',
      dataIndex: 'value',
      key: 'value',
      render: (value) => (value ? value : '-'),
    },
  ];

  const tableData = [
    {
      key: '1',
      field: 'GRN ID',
      value: data?.grnid?.id || '-',
      icon: <FileTextOutlined />,
      color: '#1890ff', // Blue
    },
    {
      key: '2',
      field: 'Status',
      value: <Tag color={getStatusTagColor(data?.status)}>{data?.status}</Tag>,
      icon: <TagOutlined />,
      color: '#faad14', // Yellow
    },
    {
      key: '3',
      field: 'Requested On',
      value: getLocalDateTime(data?.createdAt) || '-',
      icon: <ClockCircleOutlined />,
      color: '#722ed1', // Purple
    },
    {
      key: '4',
      field: 'Approved/Rejected By',
      value: data?.approvedOrRejectBy?.name || '-',
      icon: <UserOutlined />,
      color: '#f5222d', // Red
    },
    {
      key: '5',
      field: 'Inventory Name',
      value: data?.inventory?.name || '-',
      icon: <InboxOutlined />,
      color: '#52c41a', // Green
    },
    {
      key: '6',
      field: 'Requested Quantity',
      value: <Tag color="blue">{data?.inventory?.quantity || 0}</Tag>,
      icon: <NumberOutlined />,
      color: '#1890ff', // Blue
    },
    {
      key: '7',
      field: 'Batch No',
      value: data?.batchNo || '-',
      icon: <BarcodeOutlined />,
      color: '#eb2f96', // Pink
    },
    {
      key: '8',
      field: 'Destination Store',
      value: data?.store?.name || '-',
      icon: <ShopOutlined />,
      color: '#fa541c', // Orange
    },
    {
      key: '9',
      field: 'Store Area',
      value: data?.storeArea || '-',
      icon: <EnvironmentOutlined />,
      color: '#13c2c2', // Cyan
    },
    {
      key: '10',
      field: 'Comment',
      value: data?.comment || '-',
      icon: <CommentOutlined />,
      color: '#ffc53d', // Gold
    },
    {
      key: '11',
      field: 'Last Updated',
      value: data?.updatedAt ? getLocalDateTime(data?.updatedAt) : '-',
      icon: <CalendarOutlined />,
      color: '#2f54eb', // Dark Blue
    },
  ];

  return (
    <RightSidebar
      openSideBar={openSideBar}
      setOpenSideBar={setOpenSideBar}
      scale={736}
      title={
        <Space>
          <InboxOutlined style={{ color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0 }}>
            Pending Stock Information
          </Title>
        </Space>
      }
    >
      <div className="p-4">
        <Table
          columns={columns}
          dataSource={tableData}
          pagination={false}
          bordered
          size="small"
        />
      </div>
    </RightSidebar>
  );
};

export default PendingInpageSideBar;
