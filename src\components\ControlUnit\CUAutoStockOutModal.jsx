import { useEffect, useMemo, useState } from 'react';
import { Modal, Table, InputNumber, Tooltip, Spin } from 'antd';
import { useGetInpagesforAutoStockoutMutation } from '../../slices/inPageApiSlice';
import { useGetAllPartsForOptionsQuery } from '../../slices/partApiSlice';
import Spinner from '../global/components/Spinner';
import { toast } from 'react-toastify';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice';
import Select from '../global/components/Select';
import { InfoCircleOutlined } from '@ant-design/icons';
import { customConfirm } from '../../utils/customConfirm';
import { useGetAllProductsForOptionsQuery } from '../../slices/productApiSlice';

// This Modal Uses Multiple Places For Example Dispatch , CU JOB Perform Changes There Also if You done any modifications here

const CUAutoStockOutModal = ({
  openModal,
  setOpenModal,
  setStockOutItem,
  rawMaterials,
  stockInQty,
  setIsCUAutoStockOut,
}) => {
  const [getInpagesforAutoStockout] = useGetInpagesforAutoStockoutMutation();
  const { data: dropdownsData } = useGetDropdownsQuery();
  const { data: partsData, isLoading: allPartsLoading } =
    useGetAllPartsForOptionsQuery();
  const { data: allProducts, isLoading: allProductsLoading } =
    useGetAllProductsForOptionsQuery();

  const allItems = useMemo(
    () => [...(partsData || []), ...(allProducts || [])],
    [partsData, allProducts]
  );
  const [items, setItems] = useState([]);

  useEffect(() => {
    const withWastage =
      rawMaterials?.map((item) => {
        const part = allItems?.find((part) => part.value === item?.item);
        const inventoryQty = part?.quantity || 0;
        const uom = part?.uom || '';
        return {
          ...item,
          wastage: 0,
          inventoryQty,
          uom,
          stockOutQty: item?.units * stockInQty,
          type: part?.type,
        };
      }) || [];
    setItems(withWastage);
  }, [rawMaterials, allItems, stockInQty]);

  const setWastageQuantity = (index, value) => {
    const updatedItems = items.map((item, idx) => {
      if (idx === index) {
        const calculatedStockOutQty = item?.units * stockInQty + value;
        if (calculatedStockOutQty > item.inventoryQty) {
          toast.error(
            `Stock Out Quantity (${calculatedStockOutQty}) cannot exceed inventory quantity of ${item.inventoryQty}`
          );
          return item;
        }

        return {
          ...item,
          wastage: value,
          stockOutQty: calculatedStockOutQty,
        };
      }
      return item;
    });

    setItems(updatedItems);
  };

  const handleUOM = (value, index) => {
    const updatedItems = items?.map((item, idx) => {
      if (idx === index) {
        return { ...item, uom: value };
      }
      return item;
    });
    setItems(updatedItems);
  };

  const handleSubmit = async () => {
    const invalidItems = items?.filter((item) => {
      const calculatedStockOutQty = item?.units * stockInQty + item.wastage;
      return calculatedStockOutQty > item.inventoryQty || item.wastage < 0;
    });

    if (invalidItems.length > 0) {
      toast.error('Please correct inventory quantities and wastage');
      return;
    }

    const fetchingItem = items?.map((item) => ({
      id: item?.item,
      type: item?.type,
    }));

    getInpagesforAutoStockout({ data: { item: fetchingItem } })
      .unwrap()
      .then((inpages) => {
        let stockOutArray = [];

        items?.forEach((odItem) => {
          delete odItem?._id;

          // Get all matching inpages for this odItem
          let matchingInpages = inpages?.filter(
            (inpage) =>
              inpage?.part === odItem?.item ||
              inpage?.product === odItem?.item ||
              inpage?.productVariant === odItem?.item ||
              inpage?.partVariant === odItem?.item
          );

          let tempArray = [];
          let totalAllocatedQty = 0;

          matchingInpages.forEach((inpage) => {
            if (totalAllocatedQty >= odItem?.stockOutQty) return; // Stop when allocation is complete

            let allocatedQty = Math.min(
              inpage.remainingQuantity,
              odItem?.stockOutQty - totalAllocatedQty
            );

            if (allocatedQty > 0) {
              tempArray.push({
                ...inpage,
                ...odItem,
                batchNo: inpage.batchNo, // Keep track of the batch being used
                quantity: allocatedQty,
                quantityTypeSet: allocatedQty,
                uom: { uom: odItem?.uom },
              });

              totalAllocatedQty += allocatedQty;
            }
          });

          // If total allocated quantity is less than required, show an error
          if (totalAllocatedQty < odItem?.stockOutQty) {
            toast.error(
              `Stock Out Quantity (${odItem?.stockOutQty}) exceeds available stock for item (${odItem?.item})`
            );
          }

          stockOutArray.push(...tempArray); // Merge into main array
        });
        setStockOutItem(() => stockOutArray);
      });

    setOpenModal(false);
  };

  const handleCancel = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to Cancel? if yes than No Items Will be Stocked Out',
      'error'
    );
    if (!confirm) return;
    setOpenModal(false);
    setIsCUAutoStockOut(false);
    setOpenModal(false);
    setStockOutItem(null);
  };

  if (allPartsLoading || allProductsLoading) return <Spinner />;

  const columns = [
    {
      title: 'Part Name',
      dataIndex: 'item',
      key: 'item',
      render: (_, record) => {
        const part = allItems?.find((part) => part.value === record?.item);
        return part?.name || 'Unknown Part';
      },
    },
    {
      title: 'Inventory Quantity',
      dataIndex: 'inventoryQty',
      key: 'inventoryQty',
      render: (_, record) => record.inventoryQty,
    },
    {
      title: 'Wastage',
      dataIndex: 'wastage',
      key: 'wastage',
      render: (_, record, index) => (
        <InputNumber
          max={record.inventoryQty}
          value={record.wastage}
          onChange={(value) => setWastageQuantity(index, value || 0)}
        />
      ),
    },
    {
      title: 'Calculated Stock Out Quantity',
      dataIndex: 'stockOutQty',
      key: 'stockOutQty',
      render: (_, record) => {
        const calculatedStockOutQty =
          record?.units * (stockInQty || 0) + (record?.wastage || 0);
        const exceedsStockOut =
          calculatedStockOutQty > (record?.inventoryQty || 0);

        return (
          <span>
            {calculatedStockOutQty}
            {exceedsStockOut && (
              <Tooltip title="Stock Out Quantity is more than Inventory Quantity. This item cannot be stocked out.">
                <InfoCircleOutlined className="ml-2 text-red-500" />
              </Tooltip>
            )}
          </span>
        );
      },
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      render: (_, record, index) => (
        <Select
          value={record?.uom}
          onChange={(e) => handleUOM(e.target.value, index)}
          options={dropdownsData?.dropdowns
            ?.find((e) => e.name === 'uom')
            ?.values?.map((e) => ({
              value: e,
              label: e,
            }))}
        />
      ),
    },
  ];

  return (
    <Modal
      title="Auto Stock Out"
      open={openModal}
      onCancel={handleCancel}
      okText={'Submit'}
      onOk={handleSubmit}
      width={800}
    >
      <p>This Modal Allows Us to Auto Stock Out the Raw Materials</p>
      <Table
        columns={columns}
        dataSource={items}
        rowKey={(record) => record.item}
        pagination={false}
        loading={{
          indicator: <Spin size="large" />,
          spinning: allPartsLoading || allProductsLoading,
        }}
      />
    </Modal>
  );
};

export default CUAutoStockOutModal;
