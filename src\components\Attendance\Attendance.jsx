import { useCallback, useEffect, useState } from 'react';
import { FormatDate } from '../../helperFunction';
import useHeaderAndFooter from '../../hooks/useHeaderAndFooter';
import { useLazyGetAllAttendanceQuery } from '../../slices/AttendanceApiSlice';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import { toCapitalize } from '../../utils/toCapitalize';
import ExportButton from '../global/components/ExportButton';
import Filter from '../global/components/Filter';
import Pagination from '../global/components/Pagination';
import Table from '../global/components/Table';
import Tooltip from '../global/components/ToolTip';
// no need exportbtn design handled in the exportButton component itself
// const exportBtn =
//   'bg-green-600 text-white rounded-l-md text-gray-button px-4 text-sm h-10 border-r border-gray-200 flex items-center gap-1 hover:bg-green-500 transition-all ease-in-out duration-300';

const Attendance = () => {
  const TableHeadings = [
    'Date',
    'Name',
    'Contractor Name',
    'Id',
    'SignIn',
    'SignOut',
    'Job Start',
    'Job End',
    'Total Working Hrs',
    'Status',
  ];

  const SelectOptions = [
    { label: 'Name', value: 'worker_name' },
    {
      label: 'Contractor Name',
      // passing string objects because react-select finds the selected option from the options list and shows the selected option to the user for that it compares the selected option in list of options and if the selected option is a object then even if two objects looks the same javascript will not return true when compairing two objects because non-primitive data types are compared by reference(their memory address).
      value: JSON.stringify({
        label: 'rfid.contractor_name',
        value: 'rfid.contractor_name',
        // path is important as it's used to tell on which field we should apply the filter in backend
        path: 'rfid.contractor_name',
      }),
    },
    { label: 'Id', value: 'worker_id' },
    { label: 'Status', value: 'status' },
  ];

  //   for sign in, sign out, job start and job end dates
  const FormateDateConfig_Sign_in = {
    hour: 'numeric',
    minute: 'numeric',
    seconds: 'numeric',
  };

  const [Page, setPage] = useState(1);
  const [Limit, setLimit] = useState(PAGINATION_LIMIT);
  const [WorkersAttendance, setWorkersAttendance] = useState([]);
  const [SelectedFilterHeading, setSelectedFilterHeading] = useState('');
  const [SelectedHeadingValue, setSelectedHeadingValue] = useState('');

  const [GetAttendance, AllAttendance] = useLazyGetAllAttendanceQuery();
  const [csvData, setCsvData] = useState([]);
  const [csvHeaders, setCsvHeaders] = useState([]);
  const { header, footer } = useHeaderAndFooter({});

  const handelGetAttendance = useCallback(
    (field_name = '', field_value = '') => {
      GetAttendance({
        page: Page,
        limit: Limit,
        field_name,
        field_value,
      });
    },
    [GetAttendance, Limit, Page]
  );

  useEffect(() => {
    handelGetAttendance();
  }, [GetAttendance, Page, Limit, handelGetAttendance]);

  useEffect(() => {
    if (!SelectedFilterHeading || SelectedHeadingValue) return;
    handelGetAttendance(SelectedFilterHeading, SelectedHeadingValue);
  }, [SelectedFilterHeading, SelectedHeadingValue, handelGetAttendance]);

  useEffect(() => {
    setWorkersAttendance(AllAttendance?.data?.attendance?.results);
  }, [AllAttendance]);

  useEffect(() => {
    let rows = WorkersAttendance;
    let columns = [
      { title: 'Date', field: 'attendance_date' },
      { title: 'Name', field: 'worker_name' },
      { title: 'Contractor Name', field: 'contractor_name' },
      { title: 'Id', field: 'worker_id' },
      { title: 'SignIn', field: 'signin' },
      { title: 'SignOut', field: 'signout' },
      { title: 'Job Start', field: 'job_start' },
      { title: 'Job End', field: 'job_end' },
      { title: 'Total Working Hrs', field: 'total_working_hrs' },
      { title: 'Status', field: 'status' },
    ];

    let headers = columns.map((col) => ({
      label: col.title,
      key: col.field,
    }));
    setCsvHeaders(headers);

    let temp = [];
    for (let i in rows) {
      let row = {};
      let e = rows[i];
      for (let j in columns) {
        let x = columns[j].title;
        if (x === 'Date' || x === 'SignIn' || x === 'SignOut') {
          row[columns[j].field] = FormatDate(e[columns[j].field], {
            hour: 'numeric',
            minute: 'numeric',
            seconds: 'numeric',
          });
        } else if (x === 'Job Start' || x === 'Job End') {
          row[columns[j].field] = FormatDate(e.attendance[columns[j].field], {
            hour: 'numeric',
            minute: 'numeric',
            seconds: 'numeric',
          });
        } else {
          row[columns[j].field] = e[columns[j].field];
        }
      }
      temp.push(row);
    }
    setCsvData(temp);
  }, [WorkersAttendance]);

  return (
    <section className="attendance-table-container">
      <div className="flex items-center gap-2">
        <div className="filter my-3 flex  justify-end overflow-x-hidden overflow-y-hidden items-center w-full ">
          <Filter
            headingfilteroptions={SelectOptions}
            secondfilterdata={AllAttendance?.data?.attendance?.results}
            setSelectedHeading={setSelectedFilterHeading}
            setSelectedValue={setSelectedHeadingValue}
          />
        </div>
        <div>
          {/* export button has a separate component that handle print pdf or csv */}
          {/* csv data and headers pass to generate csv */}
          <ExportButton csvData={csvData} csvHeaders={csvHeaders} />
        </div>
      </div>
      <div
        id="print-dailyAttandance"
        className="w-full overflow-x-scroll h-full"
      >
        {header()}
        <Table>
          <Table.Head>
            <Table.Row>
              {TableHeadings.map((heading) => {
                return <Table.Th key={heading}>{heading}</Table.Th>;
              })}
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {WorkersAttendance?.map((attendance) => {
              return (
                <Table.Row key={attendance?._id}>
                  <Table.Td>
                    {FormatDate(
                      attendance?.attendance_date,
                      FormateDateConfig_Sign_in
                    )}
                  </Table.Td>
                  <Table.Td className={'!min-w-[24rem] !w-[24rem]'}>
                    {attendance?.worker_name?.length > 60 ? (
                      <Tooltip text={attendance?.worker_name}>
                        {attendance?.worker_name?.slice(0, 60) + '...'}
                      </Tooltip>
                    ) : (
                      attendance?.worker_name
                    )}
                  </Table.Td>
                  <Table.Td>
                    {attendance?.rfid?.contractor_name || 'N/A'}
                  </Table.Td>
                  <Table.Td>{attendance?.worker_id}</Table.Td>
                  <Table.Td>
                    {FormatDate(
                      attendance?.signin,
                      FormateDateConfig_Sign_in
                    ) || 'N/A'}
                  </Table.Td>
                  <Table.Td>
                    {FormatDate(
                      attendance?.signout,
                      FormateDateConfig_Sign_in
                    ) || 'N/A'}
                  </Table.Td>
                  <Table.Td>
                    {FormatDate(
                      attendance?.attendance?.job_start,
                      FormateDateConfig_Sign_in
                    ) || 'N/A'}
                  </Table.Td>
                  <Table.Td>
                    {FormatDate(
                      attendance?.attendance?.job_end,
                      FormateDateConfig_Sign_in
                    ) || 'N/A'}
                  </Table.Td>
                  <Table.Td>{attendance?.total_working_hrs || 'N/A'}</Table.Td>
                  <Table.Td>{toCapitalize(attendance?.status)}</Table.Td>
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
        {footer()}
      </div>
      <Pagination
        page={Page}
        limit={Limit}
        setPage={setPage}
        setLimit={setLimit}
        totalPages={AllAttendance?.data?.attendance?.totalPages || ''}
        // getting the total result by multiplying the Attendance Array length by Limit
        totalResults={AllAttendance?.data?.attendance?.totalResults || ''}
      />
    </section>
  );
};

export default Attendance;
