import { toast } from 'react-toastify';
import { IndianRupee, Gauge, Timer, History } from 'lucide-react';
import Toggle from '../global/components/Toggle';

const MachineSelection = ({
  productionFlow = {},
  goalsTable = [],
  setGoalsTable,
  allMachines = [],
  machineSchedules = [],
}) => {
  const { processes = [] } = productionFlow;
  const inhouseProcesses = processes?.filter(
    (pro) => pro?.processCategory === 'Inhouse'
  );

  const updateSelectedMacsDetails = (
    selectedMacs = [],
    goalsTable = {},
    macs
  ) => {
    const { tableData, flowId } = goalsTable;

    const { isMultiProcess = false } =
      inhouseProcesses?.find((pro) => pro._id === flowId) || {};

    if (macs?.length > 0 && selectedMacs) {
      const filteredMacs = macs?.filter((i) => selectedMacs?.includes(i._id));

      let iph = 0;
      let cot = 0;

      filteredMacs?.forEach((mac) => {
        iph += mac?.itemsPerHour?.find((i) => i.isDefault)?.value || 0;
        cot += mac?.changeOverTime?.find((i) => i.isDefault)?.value || 0;
      });

      if (isMultiProcess) {
        return tableData?.map((tdt) => ({
          ...tdt,
          subProcessData: tdt?.subProcessData?.map((stdt) => ({
            ...stdt,
            itemsPerHour: iph,
            Speed: iph,
            changeOverTime: cot,
            Time: cot,
          })),
        }));
      } else {
        return tableData?.map((tdt) => ({
          ...tdt,
          itemsPerHour: iph,
          Speed: iph,
          changeOverTime: cot,
          Time: cot,
        }));
      }
    }

    return tableData;
  };

  const handleSingleToggle = (flowId, mac) => {
    setGoalsTable((prev) =>
      prev?.map((gt) => {
        if (gt.flowId === flowId) {
          let selectedMacs = [];
          if (gt?.selectedMachines?.find((i) => i === mac._id)) {
            const exists = machineSchedules.find(
              (item) => item.machineId === mac._id && item.start && item.stop
            );
            if (exists) {
              selectedMacs = gt.selectedMachines;
              toast.warn(
                `Machine ${mac.machineName} used for scheduling cannot deselect. To deselect machine remove all schedules first.`,
                { toastId: 'machinedeselect' }
              );
            } else {
              selectedMacs = gt?.selectedMachines?.filter((i) => i !== mac._id);
            }
          } else {
            selectedMacs = [...(gt?.selectedMachines || []), mac._id];

            // setMachineSchedules()
          }

          const tableData = updateSelectedMacsDetails(selectedMacs, gt, [mac]);

          return {
            ...gt,
            selectedMachines: selectedMacs,
            tableData,
          };
        }
        return gt;
      })
    );
  };

  const handleAllToggle = (flowId, mqttId, forAllProcess = false, val) => {
    setGoalsTable((prev) =>
      prev?.map((gt) => {
        const macs = forAllProcess
          ? allMachines?.filter((mac) => mac.mqtt === gt?.mqtt)
          : allMachines?.filter((mac) => mac.mqtt === mqttId);

        if (gt.flowId === flowId || forAllProcess) {
          let selectedMacs = [];
          if (
            gt?.selectedMachines?.length === macs?.length &&
            macs?.length > 0 &&
            !val
          ) {
            selectedMacs = [];
          } else {
            selectedMacs = macs?.map((i) => i._id);
          }

          const tableData = updateSelectedMacsDetails(selectedMacs, gt, macs);

          return {
            ...gt,
            selectedMachines: selectedMacs,
            tableData,
          };
        }

        return gt;
      })
    );
  };

  return (
    <div className="w-full">
      {/* Header Section */}
      <div className="flex items-center justify-between mb-6 bg-white rounded-lg p-4 shadow-sm">
        <h2 className="text-lg font-semibold text-gray-800">
          Machine Selection
        </h2>
        <div className="flex items-center gap-x-3">
          <span className="text-sm text-gray-600 font-medium">
            Select All Machines
          </span>
          <Toggle
            value={
              goalsTable?.reduce(
                (acc, curVal) => acc + curVal?.selectedMachines?.length || 0,
                0
              ) === allMachines?.length && allMachines?.length > 0
            }
            onChange={(e) =>
              handleAllToggle(null, null, true, e.target?.checked)
            }
          />
        </div>
      </div>

      {/* Processes Grid */}
      <div>
        {inhouseProcesses?.map((pro) => {
          const currentGt =
            goalsTable?.find((gt) => gt.flowId === pro._id) || {};
          const { selectedMachines = [] } = currentGt;
          const macs = allMachines?.filter((mac) => mac.mqtt === pro.mqtt._id);

          return (
            <div
              key={pro._id}
              className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden mb-3"
            >
              {/* Process Header */}
              <div className="px-4 py-3 bg-gray-50 border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <h3 className="font-semibold text-gray-800">
                    {pro.processName}
                  </h3>
                  <p className="text-sm text-gray-800 p-1 px-3 bg-gray-300 border rounded-md">
                    {selectedMachines?.length} of {macs?.length} selected
                  </p>
                  <div className="flex items-center gap-x-2">
                    <span className="text-xs text-gray-500">Select All</span>
                    <Toggle
                      value={
                        selectedMachines?.length === macs?.length &&
                        macs?.length > 0
                      }
                      onChange={() => handleAllToggle(pro._id, pro.mqtt._id)}
                    />
                  </div>
                </div>
              </div>

              {/* Machines List */}
              <div className="p-4 w-full overflow-hidden">
                {macs?.length === 0 ? (
                  <p className="text-sm text-gray-500 text-center py-2">
                    No machines available
                  </p>
                ) : (
                  <div className="flex w-full overflow-x-auto gap-4 p-3">
                    {macs?.map((mac) => (
                      <div
                        key={mac._id}
                        className="flex flex-col min-w-[200px] w-[18%] border rounded-xl shadow-sm hover:shadow-md p-2"
                      >
                        <div className="w-full flex justify-between items-center border-b pb-1">
                          <p className="flex flex-col font-medium text-gray-700">
                            <span className="truncate" title={mac?.machineName}>
                              {mac.machineName}
                            </span>
                            <span className="text-xs">({mac.machineId})</span>
                          </p>
                          <Toggle
                            value={
                              selectedMachines?.includes(mac?._id) || false
                            }
                            onChange={() => handleSingleToggle(pro._id, mac)}
                          />
                        </div>
                        <div className="text-sm grid grid-cols-2 gap-2 p-2">
                          <p className="flex w-full gap-2 items-center">
                            <Gauge className="size-5" />{' '}
                            {mac?.itemsPerHour.find((i) => i?.isDefault)
                              ?.value ||
                              mac?.itemsPerHour?.[0]?.value ||
                              '-'}{' '}
                            /hrs
                          </p>
                          <p className="flex w-full gap-2 items-center">
                            <History className="size-5" />{' '}
                            {mac?.changeOverTime?.find((i) => i?.isDefault)
                              ?.value ||
                              mac?.changeOverTime?.[0]?.value ||
                              '-'}{' '}
                            mins
                          </p>
                          <p className="flex w-full gap-2 items-center">
                            <IndianRupee className="size-5" />{' '}
                            {mac?.costPerHour || '-'}
                          </p>
                          <p className="flex w-full gap-2 items-center">
                            <Timer className="size-5" />{' '}
                            {mac?.maxCapacity || '-'} hrs
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MachineSelection;
