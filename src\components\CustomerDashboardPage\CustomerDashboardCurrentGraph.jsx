import { useEffect, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { Line, LineChart, Tooltip, XAxis, YAxis } from 'recharts';
import { useGetCurrentGraphForDeviceQuery } from '../../slices/iotDeviceDataApiSlice';

const padZero = (num) => {
  return num.toString().padStart(2, '0');
};

function CustomerDashboardCurrentGraph({ deviceData, dataToggle }) {
  const [newData, setNewData] = useState([]);

  const { iotDeviceData } = useOutletContext();

  const value = iotDeviceData?.[`/${deviceData?.device?.deviceId}/AVGCURRENT`];

  const { data: graphData } = useGetCurrentGraphForDeviceQuery(
    {
      id: deviceData?.device?._id,
      query: { interval: dataToggle },
    },
    { skip: !deviceData?.device?._id, refetchOnMountOrArgChange: true }
  );

  useEffect(() => {
    if (value) {
      const date = new Date();
      setNewData((prev) => [
        ...prev,
        {
          name: `${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date?.getSeconds())}`,
          value,
        },
      ]);
    }
  }, [value]);

  return (
    <div className="py-3 bg-white">
      {graphData?.length > 0 ? (
        <LineChart
          className="-ml-5"
          width={300}
          height={150}
          data={[...(graphData || []), ...(newData || [])]}
        >
          <Tooltip />
          <YAxis dataKey={null} />
          <XAxis dataKey={'name'} />
          <Line
            type="monotone"
            dataKey="value"
            stroke="#8884d8"
            strokeWidth={2}
          />
        </LineChart>
      ) : (
        <p className="text-center">No data to plot the graph</p>
      )}
    </div>
  );
}

export default CustomerDashboardCurrentGraph;
