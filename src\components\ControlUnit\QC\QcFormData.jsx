import { Tab, TabGroup, TabList } from '@headlessui/react';
import { useEffect, useState } from 'react';
import { useLazyGetCreateInputByIDQuery } from '../../../slices/createInputApiSlice';
import Table from '../../global/components/Table';
const selectedStyle =
  'px-4 py-2 text-sm font-medium  text-white bg-gray-400 outline-none w-full';
const nonSelectedStyle =
  'px-4 py-2 text-sm font-medium text-gray-900 bg-gray-300 outline-none w-full';

const QcFormData = ({ ci, isOpenSidebar }) => {
  const [activeBatch, setActiveBatch] = useState(null);
  const [sortedQcTypes, setSortedQcTypes] = useState([]);
  const [getCreateInputByID, { data: sideBarData }] =
    useLazyGetCreateInputByIDQuery();

  useEffect(() => {
    if (ci && isOpenSidebar) {
      getCreateInputByID({
        id: ci?._id,
      });
    }
  }, [ci, isOpenSidebar]); //eslint-disable-line

  useEffect(() => {
    if (sideBarData) {
      let cuProjects = sideBarData?.cuProjects;
      let qcTypes = cuProjects?.filter((item) => item?.type === 'QC');
      let sortedQcTypes = qcTypes?.sort((a, b) => {
        return a?.batchInfo?.batchNo - b?.batchInfo?.batchNo;
      });
      setSortedQcTypes(sortedQcTypes);
      setActiveBatch(sortedQcTypes?.[0]?.batchInfo?.batchNo);
    }
  }, [sideBarData]);
  return (
    <div className="w-full">
      <TabGroup>
        <TabList className={'w-full flex  rounded-lg'}>
          {sortedQcTypes?.map((item, idx) => {
            return (
              <Tab
                key={item?.id}
                className={({ selected }) =>
                  `${selected ? selectedStyle : nonSelectedStyle} ${idx === 0 && 'rounded-l-md'} ${idx === sortedQcTypes?.length - 1 && 'rounded-r-md'} `
                }
                onClick={() => setActiveBatch(+item?.batchInfo?.batchNo)}
              >
                Batch- {item?.batchInfo?.batchNo}
              </Tab>
            );
          })}
        </TabList>
      </TabGroup>
      <section className="mt-5 ">
        {activeBatch &&
        sortedQcTypes?.find(
          (item) => item?.batchInfo?.batchNo === activeBatch
        ) ? (
          <section>
            {(() => {
              const data = sortedQcTypes?.find(
                (item) => item?.batchInfo?.batchNo === activeBatch
              );

              return (
                <section>
                  <Table.Row className={'flex justify-between'}>
                    <Table.Td>Batch Name</Table.Td>
                    <Table.Td>{data?.batchInfo?.batchName}</Table.Td>
                  </Table.Row>
                  <Table.Row className={'flex justify-between'}>
                    <Table.Td>Batch No</Table.Td>
                    <Table.Td>{data?.batchInfo?.batchNo}</Table.Td>
                  </Table.Row>
                  <Table.Row className={'flex justify-between'}>
                    <Table.Td>Status</Table.Td>
                    <Table.Td>{(data?.status).toUpperCase()}</Table.Td>
                  </Table.Row>

                  <section className="mt-10 h-[55vh] overflow-auto">
                    <Table className="w-full">
                      <Table.Head className="w-full">
                        <Table.Row className="w-full">
                          <Table.Th className="w-1/3">Data</Table.Th>
                          <Table.Th className="w-1/3 text-center">
                            Value
                          </Table.Th>
                          <Table.Th className="w-1/3"></Table.Th>
                        </Table.Row>
                      </Table.Head>
                      <Table.Body className="w-full">
                        {data?.qcData?.formData?.map((currElem) => {
                          return Object.keys(currElem)?.map((val, iVal) => {
                            if (val === 'qr') return null;
                            if (val === 'Media' || val === 'Table') return null;
                            const isObject = typeof currElem === 'object';
                            return (
                              <>
                                {iVal === 0 && (
                                  <>
                                    <Table.Row className={'!bg-gray-500'}>
                                      <Table.Td></Table.Td>
                                      <Table.Td
                                        className={
                                          'flex justify-center items-center'
                                        }
                                      >
                                        {currElem?.['Quantity'] && (
                                          <p
                                            className={`text-lg font-semibold  w-full text-center  rounded-lg ${currElem?.['Quantity'] === 'Pass' ? ' text-green-600' : currElem?.['Quantity'] === 'Rework' ? 'text-yellow-600' : 'text-red-600'} `}
                                          >
                                            {currElem?.['Quantity']}
                                          </p>
                                        )}
                                      </Table.Td>
                                      <Table.Td></Table.Td>
                                    </Table.Row>
                                  </>
                                )}
                                {!(val === 'Quantity') && (
                                  <Table.Row key={iVal}>
                                    <Table.Td>{val}</Table.Td>
                                    <Table.Td className="capitalize text-center">
                                      {currElem[val]
                                        ? isObject
                                          ? currElem[val]?.value?.toString() ||
                                            '-'
                                          : currElem[val]?.toString()
                                        : '-'}
                                    </Table.Td>
                                  </Table.Row>
                                )}
                              </>
                            );
                          });
                        })}
                      </Table.Body>
                    </Table>
                  </section>
                </section>
              );
            })()}
          </section>
        ) : null}
      </section>
    </div>
  );
};

export default QcFormData;
