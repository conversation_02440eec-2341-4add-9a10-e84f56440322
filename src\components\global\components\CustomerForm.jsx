import { Button, Input, Modal } from 'antd';
import { useEffect, useState } from 'react';
import { MdDelete } from 'react-icons/md';
import { toast } from 'react-toastify';
import {
  useCreateCustomerFormMutation,
  useEditCustomerFormMutation,
} from '../../../slices/customerFormApiSlice';
import Select from './Select';
import Table from './Table';

const CustomerForm = ({
  isOpen,
  setIsOpen,
  setCustomerFormData,
  customerFormData = [],
  customerFormId,
}) => {
  const [optionModal, setOptionModal] = useState(false);
  const [selectedDropdown, setSelectedDropdown] = useState({
    elem: '',
    index: -1,
  });

  const [createCustomerForm] = useCreateCustomerFormMutation();
  const [editCustomerForm] = useEditCustomerFormMutation();

  const handleCancel = () => {
    setIsOpen(false);
  };

  const handleOk = () => {
    setIsOpen(false);
    submitHandler();
  };

  const handleDeleteFormElement = (index) => {
    setCustomerFormData((prev = []) =>
      prev.filter((_, itemIndex) => itemIndex !== index)
    );
  };

  const submitOptions = (options) => {
    setCustomerFormData((prev = []) => [
      ...prev.slice(0, selectedDropdown?.index),
      {
        ...prev[selectedDropdown?.index],
        fieldOptions: options,
      },
      ...prev.slice(selectedDropdown?.index + 1),
    ]);
  };

  const submitHandler = async () => {
    if (!customerFormData?.length) {
      toast.error('Please add at least one form field');
      return;
    }

    const hasEmptyFields = customerFormData.some(
      (field) => !field.fieldName || !field.fieldType
    );
    if (hasEmptyFields) {
      toast.error('Please fill all required fields');
      return;
    }

    const response =
      customerFormId?.length >= 24
        ? await editCustomerForm({
            id: customerFormId,
            data: customerFormData,
          }).unwrap()
        : await createCustomerForm(customerFormData).unwrap();

    if (response) {
      toast.success('Form saved successfully');
    }
  };

  return (
    <Modal
      title="Form Management"
      open={isOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={700}
      footer={[
        <Button key="back" color="red" variant="solid" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button
          color="blue"
          variant="solid"
          className="bg-blue-500 text-white"
          key="addRow"
          onClick={() => {
            setCustomerFormData((prev = []) => [
              ...(Array.isArray(prev) ? prev : []),
              {
                fieldName: '',
                fieldType: '',
              },
            ]);
          }}
        >
          Add Row
        </Button>,
        <Button key="submit" color="green" variant="solid" onClick={handleOk}>
          Submit
        </Button>,
      ]}
    >
      <DropdownOptionModal
        isOpen={optionModal}
        setIsOpen={setOptionModal}
        submitOptions={submitOptions}
        selectedDropdown={selectedDropdown}
      />
      <Table className="mb-[5rem]">
        <Table.Head>
          <Table.Th>Field Name</Table.Th>
          <Table.Th>Field Type</Table.Th>
          <Table.Th></Table.Th>
        </Table.Head>
        <Table.Body>
          {(Array.isArray(customerFormData) ? customerFormData : [])?.map(
            (elem, index) => (
              <Table.Row key={index}>
                <Table.Td>
                  {elem?.fieldType?.length > 0 ? (
                    <Input
                      placeholder="Enter Field Name"
                      value={elem?.fieldName}
                      onChange={(e) => {
                        setCustomerFormData((prev) => [
                          ...prev?.slice(0, index),
                          {
                            ...prev?.[index],
                            fieldName: e.target.value,
                          },
                          ...prev?.slice(index + 1),
                        ]);
                      }}
                    />
                  ) : (
                    <Input disabled={true} placeholder="Select a type first" />
                  )}
                </Table.Td>
                <Table.Td>
                  <Select
                    options={[
                      { label: 'Text', value: 'text' },
                      { label: 'Number', value: 'number' },
                      { label: 'Dropdown', value: 'dropdown' },
                      { label: 'Attachment', value: 'attachment' },
                      { label: 'Description', value: 'description' },
                    ]}
                    menuPosition="fixed"
                    style={{ width: '8rem' }}
                    value={elem?.fieldType}
                    onChange={(e) => {
                      setCustomerFormData((prev) => [
                        ...prev?.slice(0, index),
                        {
                          ...prev?.[index],
                          fieldType: e.target.value,
                        },
                        ...prev?.slice(index + 1),
                      ]);
                    }}
                    placeholder="Enter Field Type"
                  />
                </Table.Td>
                <Table.Td>
                  <div className="flex items-center justify-center gap-2">
                    {elem?.fieldType === 'dropdown' ? (
                      <Button
                        size="small"
                        type="primary"
                        onClick={() => {
                          setOptionModal(true);
                          setSelectedDropdown({ elem, index });
                        }}
                      >
                        + Add Options
                      </Button>
                    ) : (
                      <Button size="small" type="primary" disabled={true}>
                        + Add Options
                      </Button>
                    )}
                    <Button
                      size="small"
                      color="red"
                      variant="solid"
                      onClick={() => handleDeleteFormElement(index)}
                    >
                      Delete
                    </Button>
                  </div>
                </Table.Td>
              </Table.Row>
            )
          )}
        </Table.Body>
      </Table>
    </Modal>
  );
};

const DropdownOptionModal = ({
  isOpen,
  setIsOpen,
  submitOptions,
  selectedDropdown,
}) => {
  const [options, setOptions] = useState([]);

  useEffect(() => {
    if (selectedDropdown?.elem?.fieldOptions) {
      setOptions(selectedDropdown?.elem?.fieldOptions);
    }
  }, [selectedDropdown?.elem]);

  const handleCancel = () => {
    setIsOpen(false);
  };

  const handleOk = () => {
    if (!options.length) {
      toast.error('Please add at least one option');
      return;
    }

    const hasEmptyLabels = options.some((option) => !option.label);
    if (hasEmptyLabels) {
      toast.error('Please fill all option labels');
      return;
    }

    submitOptions(options);
    setIsOpen(false);
  };

  const toCamelCase = (str) => {
    return str
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]+(.)/g, (_, char) => char.toUpperCase());
  };

  return (
    <Modal
      title="Form Management"
      open={isOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={700}
      footer={[
        <Button key="back" color="red" variant="solid" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button
          color="blue"
          variant="solid"
          key="addOption"
          className="bg-blue-500 text-white"
          onClick={() => {
            setOptions((prev = []) => [...prev, { label: '', value: '' }]);
          }}
        >
          Add Option
        </Button>,
        <Button key="submit" color="green" variant="solid" onClick={handleOk}>
          Submit
        </Button>,
      ]}
    >
      <Table>
        <Table.Head>
          <Table.Th>Option Label</Table.Th>
          <Table.Th></Table.Th>
        </Table.Head>
        <Table.Body>
          {(Array.isArray(options) ? options : [])?.map((elem, index) => (
            <Table.Row key={index}>
              <Table.Td>
                <Input
                  value={elem?.label}
                  placeholder="Enter option"
                  onChange={(e) => {
                    setOptions((prev) => [
                      ...prev?.slice(0, index),
                      {
                        label: e.target.value,
                        value: toCamelCase(e.target.value),
                      },
                      ...prev?.slice(index + 1),
                    ]);
                  }}
                />
              </Table.Td>
              <Table.Td>
                <MdDelete
                  className="text-lg text-red-500 cursor-pointer hover:text-red-400"
                  onClick={() => {
                    setOptions((prev) =>
                      prev?.filter((_, elemIndex) => elemIndex !== index)
                    );
                  }}
                />
              </Table.Td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </Modal>
  );
};

export default CustomerForm;
