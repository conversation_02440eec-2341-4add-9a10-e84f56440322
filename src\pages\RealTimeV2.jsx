import {
  AppstoreOutlined,
  DeleteOutlined,
  ShoppingOutlined,
} from '@ant-design/icons';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import { GiInnerSelf } from 'react-icons/gi';
import { useMediaQuery } from 'react-responsive';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import CloseIcon from '../assets/images/remove.png';
import Header from '../components/global/components/Header.jsx';
import Pagination from '../components/global/components/Pagination';
import RightSidebar from '../components/global/components/RightSidebar';
import TablePopup from '../components/global/components/TablePopup.jsx';
import AdminAuthModal from '../components/realtime/AdminAuthModal.jsx';
import { csvHeaders, graphOptions } from '../components/realtime/Constant.js';
import CreateIndentModal from '../components/realtime/CreateIndentModal.jsx';
import QrModal from '../components/realtime/QrModal.jsx';
import RealtimeSidebarV2 from '../components/realtime/RealtimeSidebarV2.jsx';
import RealtimeTable from '../components/realtime/RealtimeTable.jsx';
import ScrapTable from '../components/realtime/ScrapTable.jsx';
import SelectablePartsExport from '../components/realtime/SelectablePartsExport';
import SelfTransferTable from '../components/realtime/SelfTransferTable.jsx';
import {
  getLocalDateTime,
  getPartVariantName,
  getProductVariantName,
  mobileWidth,
} from '../helperFunction.js';
import useDebounceValue from '../hooks/useDebounceValue.js';
import useHeaderAndFooter from '../hooks/useHeaderAndFooter.jsx';
import {
  useGetDropdownsQuery,
  useLazyGetDropDownsPagesQuery,
} from '../slices/dropdownApiSlice.js';
import {
  useCreateIndentMutation,
  useGetAllIndentQuery,
} from '../slices/indentApiSlice.js';
import { useUpdateInPageMutation } from '../slices/inPageApiSlice';
import { useGetAllPartsForOptionsQuery } from '../slices/partApiSlice.js';
import {
  useGetRealTimeFilterOptionsQuery,
  useLazyGetRealTimeCountsDataQuery,
  useLazyGetRealTimeDataQuery,
} from '../slices/transactionsApiSlice';
import { PAGINATION_LIMIT } from '../utils/Constant.js';

const RealTimeV2 = () => {
  const [selectedTab, setSelectedTab] = useState('part');
  const [type, setType] = useState('');
  const [getRealTimeData, { data: realTimeData }] =
    useLazyGetRealTimeDataQuery();
  const [getRealTimeCountsData, { data: realTimeCountsData }] =
    useLazyGetRealTimeCountsDataQuery();
  const { data: realtimeFilterOptions } = useGetRealTimeFilterOptionsQuery(
    {
      type: selectedTab,
    },
    { refetchOnMountOrArgChange: true }
  );
  const [selectedEntry, setSelectedEntry] = useState({ _id: '' }); //eslint-disable-line
  const [LQFilter] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [qrValue, setQrValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [csvData, setCsvData] = useState([]);
  const [adminEditData, setAdminEditData] = useState({});
  const [tableData, setTableData] = useState([]);
  const [showSelectableExport, setShowSelectableExport] = useState(false);
  const [getDropdownsPages] = useLazyGetDropDownsPagesQuery();
  const [indentModal, setIndentModal] = useState(false);
  const [_SearchParams] = useSearchParams({
    filter: '',
  });
  const { data: dropdownsData } = useGetDropdownsQuery();
  const { data: allParts = [] } = useGetAllPartsForOptionsQuery();
  const periodOptions = [
    { label: '1 Month', value: 30 },
    { label: '3 Months', value: 90 },
    { label: '6 Months', value: 180 },
    { label: '1 Year', value: 360 },
  ];
  const [limit, setLimit] = useState(25 || PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState('');
  const [totalResults, setTotalResults] = useState('');
  const [totalLowStock, setTotalLowStocks] = useState('NA');
  const [rerender, setRerender] = useState(false);
  const [searchterm, setsearchTerm] = useState('');
  const [rightBar, setRightBar] = useState(false);
  const [rightBarData, setRightBarData] = useState({});
  const [editable, setEditable] = useState(false);
  const [editedValue, setEditedValue] = useState();
  const [printType, setPrintType] = useState('QR');
  const [showAdminPopup, setShowAdminPopup] = useState(false);
  const [updateInPage] = useUpdateInPageMutation();
  const [adminDetails, setAdminDetails] = useState({
    email: '',
    password: '',
  });
  const isMobile = useMediaQuery({ query: mobileWidth });
  const [popupData, setPopupData] = useState({});
  const [selectedPartName, setSelectedPartName] = useState();
  const [period, setPeriod] = useState(90);
  const [globalUOM, setGlobalUOM] = useState('');
  const [filters, setFilters] = useState([]);
  const [showLowQuantity, setShowLowQuantity] = useState(false);

  const { header, footer } = useHeaderAndFooter({});

  const debounceSearch = useDebounceValue(searchterm || '');
  const handlePart = (e) => {
    if (selectedPartName !== e) {
      setSelectedPartName(e);
    }
    const element = tableData.find((el) => el.name === e);
    setType(element?.category);
  };

  const UOMOptions = [
    ...(dropdownsData?.dropdowns
      ?.find((e) => e.name === 'uom')
      ?.values?.map((el) => ({
        value: el,
        label: el,
      })) || []),
  ];

  useEffect(() => {
    getDropdownsPages({ page, limit });
  }, [limit, page, getDropdownsPages]);

  useEffect(() => {
    if (realTimeCountsData) {
      setTotalLowStocks(realTimeCountsData?.totalLowStock);
      setTotalPages(realTimeCountsData?.totalPages);
      setTotalResults(realTimeCountsData?.totalResults);
    }
  }, [realTimeCountsData]);

  useEffect(() => {
    if (selectedTab === 'scrap') return;
    setIsLoading(true);

    getRealTimeData({
      selectedTab,
      page,
      limit,
      filters,
      searchTerm: debounceSearch,
      showLowQuantity,
    })
      .unwrap()
      .then(() => {
        setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        toast.error(err?.data?.message, {
          theme: 'colored',
          position: 'top-right',
          toastId: err?.data?.message,
        });
        return;
      });
    getRealTimeCountsData({
      selectedTab,
      page,
      limit,
      filters,
      searchTerm: debounceSearch,
      showLowQuantity,
    });
  }, [
    selectedTab,
    rerender,
    getRealTimeData,
    page,
    limit,
    filters,
    debounceSearch,
    getRealTimeCountsData,
    showLowQuantity,
  ]);

  useEffect(() => {
    if (!realTimeData?.results) return;

    const temp = realTimeData.flatMap((result) => {
      return Object.values(result.batches || {}).flatMap((batches) => {
        return batches.map((e) => {
          return csvHeaders.reduce((row, { key: field }) => {
            switch (field) {
              case 'name':
                row.name = e?.part
                  ? e.part.name
                  : e?.partVariant
                    ? getPartVariantName(e.partVariant)
                    : e?.productVariant
                      ? getProductVariantName(e.productVariant)
                      : '';
                break;
              case 'batch':
                row.batch = e?.batchNo;
                break;
              case 'lotNo':
                row.lotNo = e?.lotNo;
                break;
              case 'location':
                row.location = e?.storeArea || '';
                break;
              case 'vendors':
                row.vendors = e?.vendor?.name || '';
                break;
              case 'uom':
                row.uom =
                  e?.part?.uom ||
                  e?.partVariant?.part?.uom ||
                  e?.product?.uom ||
                  e?.subAssembly?.uom;
                break;
              case 'total':
                row.total = e?.quantity;
                break;
              case 'net':
                row.net = e?.remainingQuantity;
                break;
              default:
                break;
            }
            return row;
          }, {});
        });
      });
    });

    setCsvData(temp);
  }, [realTimeData, selectedEntry]);

  const editQuantityHandler = (id) => {
    setEditable(id);
  };

  const submit = async (e) => {
    e.preventDefault();
    if (editedValue < 0) {
      toast.error('Entered value is less than 0');
      return;
    }
    updateInPage({
      inpage: {
        body: { ...adminEditData, remainingQuantity: editedValue },
        userDetails: adminDetails,
      },
      id: adminEditData._id,
    })
      .unwrap()
      .then(() => {
        setRerender((prev) => !prev);
        setEditable(false);
        setShowAdminPopup(false);
        toast.success('Batch updated successfully', {
          theme: 'colored',
          position: 'top-right',
        });
      })
      .catch((error) => {
        toast.error(error?.data?.message, {
          theme: 'colored',
          position: 'top-right',
        });
      });
  };

  const settingTableData = () => {
    setTableData(realTimeData);
  };

  useEffect(() => {
    if (realTimeData?.length > 0) {
      settingTableData();
    }
    // eslint-disable-next-line
  }, [realTimeData, LQFilter, selectedEntry]);

  const [clickedRow, setClickedRow] = useState(null);

  const [CreateIndent] = useCreateIndentMutation();
  const user = JSON.parse(localStorage.getItem('user')).user;
  const { data } = useGetAllIndentQuery({
    page: 1,
    limit: 5,
    filter_name: '',
    filter_value: '',
  });

  const [indentData, setIndentData] = useState({
    products: [
      {
        name: '',
        uom: '',
        quantity: 0,
        type: selectedTab === 'part' ? 'Part' : 'Product',
        vendor: '',
      },
    ],
    indent_no: 1,
    indent_date: new Date(),
    request_by: user?.name || user?.username,
    department: user?.name,
    delivery_date: Date.now(),
    remark: '',
    status: 'pending',
  });

  const handleCreateIndent = async () => {
    for (let i = 0; i < indentData?.products?.length; i++) {
      if (indentData?.products[i]?.quantity < 0) {
        toast.error('Indent Quantity can not be negative');
        return;
      }
    }
    const saveIndent = await CreateIndent({
      ...indentData,
      indent_no: data?.results[0]?.indent_no + 1 || 1,
    }).unwrap();
    if (saveIndent?.message) {
      toast.success(saveIndent?.message);
    }
    setIndentModal(false);
    setIndentData({
      products: [
        {
          name: '',
          uom: '',
          quantity: 0,
          type: selectedTab === 'part' ? 'Part' : 'Product',
          vendor: '',
        },
      ],
      indent_no: 1,
      indent_date: new Date(),
      request_by: user?.name || user?.username,
      department: user?.name,
      delivery_date: Date.now(),
      remark: '',
      status: 'pending',
    });
  };

  return (
    <>
      {isMobile && clickedRow && (
        <TablePopup
          onBack={() => setClickedRow(null)}
          isEdit={false}
          isDownload={false}
        >
          <div className="space-y-4 mt-5 !text-[12px]">
            <div className="w-full flex items-start justify-between gap-4">
              <label className="font-semibold">ITEM</label>
              <p className="max-w-[20ch] break-words">{clickedRow?.name}</p>
            </div>

            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">NET QUANTITY</label>
              <p>{clickedRow?.totalQuantity ?? '-'}</p>
            </div>

            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">BATCH NO</label>
              <p>
                {(clickedRow?.batches &&
                  clickedRow.batches[Object.keys(clickedRow.batches)[0]][0]
                    ?.batchNo) ||
                  '-'}
              </p>
            </div>

            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">LOT NO.</label>
              <p>
                {(clickedRow?.batches &&
                  clickedRow.batches[Object.keys(clickedRow.batches)[0]][0]
                    ?.lotNo) ||
                  '-'}
              </p>
            </div>

            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">UOM</label>
              <p>
                {(clickedRow?.batches &&
                  clickedRow?.batches[
                    Object.keys(clickedRow.batches || {})[0]
                  ][0]?.part?.uom) ||
                  '-'}
              </p>
            </div>

            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">CREATED AT</label>
              <p>
                {getLocalDateTime(
                  (clickedRow?.batches &&
                    clickedRow.batches[Object.keys(clickedRow.batches)[0]][0]
                      ?.createdAt) ||
                    '-'
                )}
              </p>
            </div>
          </div>
        </TablePopup>
      )}

      <AdminAuthModal
        showAdminPopup={showAdminPopup}
        setShowAdminPopup={setShowAdminPopup}
        submit={submit}
        adminDetails={adminDetails}
        setAdminDetails={setAdminDetails}
      />
      <CreateIndentModal
        indentModal={indentModal}
        setIndentModal={setIndentModal}
        setIndentData={setIndentData}
        indentData={indentData}
        selectedTab={selectedTab}
        user={user}
        handleCreateIndent={handleCreateIndent}
        data={data}
        UOMOptions={UOMOptions}
      />
      <QrModal
        showModal={showModal}
        setShowModal={setShowModal}
        setRightBar={setRightBar}
        popupData={popupData}
        rightBarData={rightBarData}
        qrValue={qrValue}
        printType={printType}
        setPrintType={setPrintType}
      />

      <RightSidebar
        openSideBar={rightBar}
        setOpenSideBar={(val) => {
          if (!val) setSelectedPartName();
          setRightBar(val);
          setPeriod(90);
        }}
        scale={980}
      >
        <RealtimeSidebarV2
          selectedPartName={selectedPartName}
          periodOptions={periodOptions}
          setPeriod={setPeriod}
          period={period}
          rightBarData={rightBarData}
          setRightBar={setRightBar}
          setShowModal={setShowModal}
          setQrValue={setQrValue}
          setPopupData={setPopupData}
          editable={editable}
          setEditedValue={setEditedValue}
          editQuantityHandler={editQuantityHandler}
          setShowAdminPopup={setShowAdminPopup}
          setAdminEditData={setAdminEditData}
          graphOptions={graphOptions}
          setEditable={setEditable}
          type={type}
          globalUOM={globalUOM}
        />
      </RightSidebar>
      <div>
        <div className="flex items-center justify-between w-full">
          <div className="flex flex-col">
            <div className="flex gap-[5px] items-center !min-w-[20rem]">
              <Header
                title="Real Time"
                description=""
                infoTitle="Welcome to Real Time Stock Page"
                infoDesc="Discover our Activity Logs page for a quick look at user actions and system events. Stay informed, search effortlessly, and ensure accountability with real-time updates."
                paras={[
                  'The Real-Time Stock Page serves as the hub for all your inventory planning needs. It provides a comprehensive overview of your inventory status, including details about batches for each item and transaction history for every item.',
                  'Additionally, you have the option to export complete reports for any part, empowering you with valuable insights for efficient inventory management. Simplify your inventory planning with our Real-Time Stock Page.',
                  'Efficiently manage your inventory with real-time insights, batch details, transaction history, and report exporting',
                ]}
              />
            </div>
          </div>

          {showSelectableExport && (
            <Button
              className={`py-1.5 bg-blue-primary text-white  rounded cursor-pointer h-fit min-w-[125px] text-center `}
              onClick={() => setShowSelectableExport((prev) => !prev)}
            >
              <img
                src={CloseIcon}
                alt="Close Icon"
                className="w-5 h-5 object-contain relative"
              />
              Close
            </Button>
          )}
        </div>
        {showSelectableExport ? (
          <>
            <SelectablePartsExport />
          </>
        ) : (
          <div className="mt-5 w-full">
            <section
              className="w-full overflow-x-scroll"
              id={printType === 'exportTable' ? 'print-only' : ''}
            >
              {header()}
              <div className=" !mb-3">
                <Button.Group>
                  <Button
                    type={selectedTab === 'part' ? 'primary' : 'default'}
                    onClick={() => setSelectedTab('part')}
                    icon={<AppstoreOutlined />}
                  >
                    Items
                  </Button>
                  <Button
                    type={selectedTab === 'product' ? 'primary' : 'default'}
                    onClick={() => setSelectedTab('product')}
                    icon={<ShoppingOutlined />}
                  >
                    FG Products
                  </Button>
                  <Button
                    type={
                      selectedTab === 'selfTransfer' ? 'primary' : 'default'
                    }
                    onClick={() => setSelectedTab('selfTransfer')}
                    icon={<GiInnerSelf />}
                  >
                    Self Transfer
                  </Button>
                  <Button
                    type={selectedTab === 'scrap' ? 'primary' : 'default'}
                    onClick={() => setSelectedTab('scrap')}
                    icon={<DeleteOutlined />}
                  >
                    Scrap
                  </Button>
                </Button.Group>
              </div>

              {selectedTab === 'scrap' ? (
                <ScrapTable selectedTab={selectedTab} />
              ) : selectedTab === 'selfTransfer' ? (
                <SelfTransferTable />
              ) : (
                <>
                  <RealtimeTable
                    isLoading={isLoading}
                    setsearchTerm={setsearchTerm}
                    tableData={realTimeData}
                    isMobile={isMobile}
                    setClickedRow={setClickedRow}
                    handlePart={handlePart}
                    getPartVariantName={getPartVariantName}
                    getProductVariantName={getProductVariantName}
                    setRightBar={setRightBar}
                    setRightBarData={setRightBarData}
                    setIndentModal={setIndentModal}
                    setIndentData={setIndentData}
                    data={data}
                    allParts={allParts}
                    realTimeData={realTimeData}
                    csvData={csvData}
                    csvHeaders={csvHeaders}
                    setPrintType={setPrintType}
                    setShowSelectableExport={setShowSelectableExport}
                    setGlobalUOM={setGlobalUOM}
                    globalUOM={globalUOM}
                    selectedTab={selectedTab}
                    totalLowStock={totalLowStock}
                    realtimeFilterOptions={realtimeFilterOptions}
                    searchterm={searchterm}
                    setFilters={setFilters}
                    showLowQuantity={showLowQuantity}
                    setShowLowQuantity={setShowLowQuantity}
                  />
                  <Pagination
                    limit={limit}
                    page={page}
                    totalPages={totalPages}
                    totalResults={totalResults}
                    setPage={setPage}
                    setLimit={setLimit}
                    className={`w-full`}
                  />
                </>
              )}
              {footer()}
            </section>
          </div>
        )}
      </div>
    </>
  );
};

export default RealTimeV2;
