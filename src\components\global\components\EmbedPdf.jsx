import { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
// import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
// import 'react-pdf/dist/esm/Page/TextLayer.css';

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.js',
  import.meta.url
).toString();

const EmbedPdf = ({ file }) => {
  const [numPages, setNumPages] = useState(1);

  return (
    <div className="flex flex-col items-center">
      <p className="!my-4">{file.name}</p>
      <Document
        file={file.data}
        onLoadSuccess={({ numPages }) => setNumPages(numPages)}
      >
        {[...Array(numPages)]?.map((_i, idx) => (
          <Page
            key={idx}
            pageNumber={idx + 1}
            renderAnnotationLayer={false}
            renderTextLayer={false}
          />
        ))}
      </Document>
    </div>
  );
};

export default EmbedPdf;
