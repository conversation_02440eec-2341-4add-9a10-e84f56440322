import { useEffect, useState } from 'react';
import { Bi<PERSON><PERSON>tArrow, BiRightArrow } from 'react-icons/bi';

const Carousel = ({ images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      handleNext();
    }, 5000);

    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePrev = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? images.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const handleNext = () => {
    const isLastSlide = currentIndex === images.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  return (
    <div className="relative w-full max-w-4xl mx-auto">
      <div className="relative overflow-hidden rounded-lg ">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {images.map((img, idx) => (
            <div
              key={idx}
              className="w-full h-96 flex-shrink-0 flex items-center justify-center bg-gray-100"
            >
              <img
                src={img}
                alt={`Slide ${idx}`}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          ))}
        </div>
      </div>
      <div className="absolute top-1/2 left-0 transform -translate-y-1/2 flex justify-between w-full px-4">
        <button
          onClick={handlePrev}
          className="bg-white text-gray-800 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-gray-500 rounded-full transition-colors duration-300 ease-in-out shadow-md"
        >
          <BiLeftArrow size={25} className="p-2" />
        </button>
        <button
          onClick={handleNext}
          className="bg-white text-gray-800 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-gray-500 rounded-full transition-colors duration-300 ease-in-out shadow-md"
        >
          <BiRightArrow size={25} className="p-2" />
        </button>
      </div>
    </div>
  );
};

export default Carousel;
