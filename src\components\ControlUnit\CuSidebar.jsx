import { useEffect, useState } from 'react';
// import { useNavigate } from 'react-router-dom';
import { useGetCuProjectByBatchQuery } from '../../slices/CuProjectAPiSlice';
import { useLazyQueryAllMachinesQuery } from '../../slices/machineApiSlice';
import { useGetMqttQuery } from '../../slices/mqttslice';
import { useLazyGetAllEmployeesQuery } from '../../slices/userApiSlice';
import CuDashboard from './CuDashboard';
import StartCu from './StartCu';

function CuSidebar({
  selectedData,
  getAllPo,
  selectedWo,
  // inhouseItems,
  itemForJob,
  setCusForStatus,
  selectedCi,
}) {
  const [cuProject, setCuProject] = useState({});
  const [refreshmachine, setRefreshMachine] = useState(false);

  const [getAllEmployees, { data: allEmployees }] =
    useLazyGetAllEmployeesQuery();

  useEffect(() => {
    getAllEmployees({}, false);
  }, [getAllEmployees]);

  let authoriedEmployees = [];
  allEmployees?.forEach((item) => {
    if (item?.processAccess?.find((e) => e?._id === selectedData?.flow?.mqtt)) {
      authoriedEmployees.push(item);
    }
  });

  const { isFetching, error, isSuccess, data } = useGetCuProjectByBatchQuery(
    {
      query: {
        mqtt: selectedData.flow.mqtt,
        project: selectedData.model._id,
        batchNo: +selectedData?.batchInfo?.batchNo,
        flowId: selectedData.flow._id,
        productionFlow: selectedData.productionFlow?._id,
        type: selectedData.flow.processCategory,
      },
    },
    {
      skip:
        !selectedData.model ||
        !selectedData.batchInfo ||
        !selectedData.flow ||
        !selectedData.productionFlow?._id ||
        !selectedData.flow,
      refetchOnMountOrArgChange: true,
    }
  );

  const { data: mqttData } = useGetMqttQuery(selectedData?.flow?.mqtt, {
    skip: !selectedData?.flow?.mqtt,
  });

  const [getMachines, { data: allMachines }] = useLazyQueryAllMachinesQuery();

  useEffect(() => {
    if (
      mqttData?.project &&
      selectedData?.flow?.processCategory === 'Inhouse'
    ) {
      getMachines({ id: mqttData?.project }, false);
    }
  }, [
    mqttData?.project,
    getMachines,
    selectedData?.flow?.processCategory,
    refreshmachine,
  ]);

  useEffect(() => {
    if (data) setCuProject(data?.cuProject || {});
  }, [data]);

  // if (isFetching) return <Spinner />;

  //TODO Later
  if (
    cuProject?.project?.isForceStopped ||
    selectedData?.model?.isForceStopped
  ) {
    return (
      <>
        <div className="w-full h-full flex justify-center items-center text-2xl sm:mt-[1%] mt-[50%] text-red-500">
          Job is Force Stopped
        </div>
      </>
    );
  }
  if (error?.data?.message === 'cuProject not found by batch SkIp$ToAst')
    return (
      <div className="overflow-auto  py-4">
        <StartCu
          selectedData={selectedData}
          setCuProject={setCuProject}
          allMachines={allMachines}
          mqttData={mqttData}
          allEmployees={authoriedEmployees}
          setRefreshMachine={setRefreshMachine}
          itemUsed={itemForJob}
          setCusForStatus={setCusForStatus}
          selectedCi={selectedCi}
        />
      </div>
    );

  if (isSuccess)
    if (
      selectedData?.flow?.processCategory === 'Outsource' &&
      cuProject?.isOutwarded
    ) {
      return (
        <>
          <div className="w-full h-full flex justify-center items-center text-2xl sm:mt-[1%] mt-[50%] text-green-500 ">
            <span className=" border-2 border-green-500 p-10 rounded-md bg-green-500/5 ">
              {' '}
              {cuProject?.outsourceStatus}
            </span>
          </div>
        </>
      );
    }
  return (
    <div
      className={`overflow-hidden ${isFetching ? 'animate-pulse pointer-events-none' : ''}`}
    >
      <CuDashboard
        cuProject={cuProject}
        selectedData={selectedData}
        allMachines={allMachines}
        allEmployees={authoriedEmployees}
        getAllPo={getAllPo}
        selectedWo={selectedWo}
        itemForJob={itemForJob}
        setCusForStatus={setCusForStatus}
        selectedCi={selectedCi}
      />
    </div>
  );
}

export default CuSidebar;
