import { Button, Table, Card, Typography, Tag } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useGetPaginatedPlanningQuery } from '../slices/purchasePlanningApiSlice';
import Pagination from '../components/global/components/Pagination';
import { useState } from 'react';
import { EyeOutlined, PlusOutlined } from '@ant-design/icons';
import Header from '../components/global/components/Header';

const { Text } = Typography;

const PurchasePlanning = () => {
  const navigate = useNavigate();
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const { data, isLoading } = useGetPaginatedPlanningQuery({
    page,
    limit,
  });

  const tableCols = [
    {
      title: 'Planning ID',
      dataIndex: 'poPlanningId',
      key: 'poPlanningId',
      render: (text) => (
        <Tag color="blue" className="font-mono">
          {text}
        </Tag>
      ),
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <Text className="font-semibold text-gray-800">{text}</Text>
      ),
    },
    {
      title: 'Base Quantity',
      dataIndex: 'baseQuantity',
      key: 'baseQuantity',
      render: (quantity) => (
        <Tag color="blue" className="font-medium">
          {quantity?.toLocaleString() || 0}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      dataIndex: 'view',
      key: 'view',
      align: 'center',
      width: 100,
      render: (_, record) => (
        <Button
          icon={<EyeOutlined />}
          type="primary"
          size="small"
          onClick={() => navigate(`/purchase/planning/edit/${record?._id}`)}
        />
      ),
    },
  ];

  return (
    <div className="min-h-screen">
      <div>
        <Header
          title="Purchase Planning"
          description=""
          infoTitle="Welcome to Purchase PLanning Dashboard"
          infoDesc=""
          paras={['']}
          classNames="mb-[0px]"
        />

        <Card className="px-12 py-4 mt-8">
          <div className="flex justify-between items-center mb-6">
            <div>
              <Text className="text-base text-gray-500">
                Total Records:{' '}
                <Text className="font-semibold">{data?.totalResults || 0}</Text>
              </Text>
            </div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/purchase/planning/create')}
            >
              Create Planning
            </Button>
          </div>

          <Table
            columns={tableCols}
            dataSource={data?.results}
            pagination={false}
            loading={isLoading}
            rowKey="_id"
            scroll={{ x: 'max-content' }}
            size="middle"
            bordered={false}
          />

          {data?.totalResults > 0 && (
            <Pagination
              page={page}
              setPage={setPage}
              limit={limit}
              setLimit={setLimit}
              totalResults={data?.totalResults}
              totalPages={data?.totalPages}
            />
          )}

          {data?.totalResults === 0 && !isLoading && (
            <div className="text-center py-12 text-gray-500">
              <Text className="text-base">
                No purchase planning records found. Create your first planning
                to get started.
              </Text>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default PurchasePlanning;
