import { EllipsisVerticalIcon } from '@heroicons/react/24/outline';
import { Dropdown } from 'antd';
import { unCamelCaseString } from '../../../helperFunction';

export const Head = ({ children, className, ...rest }) => {
  return (
    <thead
      className={`w-full text-[#5E6366] bg-[#F2F1FB] ${className}`}
      {...rest}
    >
      {children}
    </thead>
  );
};

export const Body = ({ children, className, ...rest }) => {
  return (
    <tbody
      className={`w-full !text-[12px] !text-[#000000] ${className}`}
      {...rest}
    >
      {children}
    </tbody>
  );
};

export const Row = ({
  children,
  className,
  isFetching,
  isClickable = false,
  ...rest
}) => {
  return (
    <tr
      className={`w-full leading-[14.52px] border-b border-[#F2F1FB] bg-white ${
        isFetching ? 'text-gray-400' : 'text-black'
      } ${isClickable ? 'hover:bg-slate-200 cursor-pointer' : ''} ${className}`}
      {...rest}
    >
      {children}
    </tr>
  );
};

export const Th = ({ children, className, ...rest }) => {
  return (
    <th
      className={`text-left px-3 sm:px-5 py-2 sm:py-3 border-b-2 bg-[#FBFAFF] text-[10px] sm:text-xs font-medium text-[#5E6366] uppercase tracking-wider ${className}`}
      {...rest}
    >
      {children}
    </th>
  );
};

export const Td = ({ children, className, ...rest }) => {
  return (
    <td
      className={`px-3 sm:px-5 py-2 sm:py-3 bg-white !text-[#000000] text-[10px] sm:text-[12px] font-medium ${className}`}
      {...rest}
    >
      {children}
    </td>
  );
};

const OptionsWrapper = ({ type, children, ...rest }) => {
  if (type === 'td') return <td {...rest}>{children}</td>;
  else if (type === 'th') return <th {...rest}>{children}</th>;
  else return null;
};

export const Options = ({
  className,
  type = 'td',
  onMouseOver,
  onMouseLeave,
  ...rest
}) => {
  const onFunctions =
    Object.entries(rest)?.filter(([key]) => key?.startsWith('on')) || [];

  const items = onFunctions.map(([name, func]) => ({
    key: name,
    label: unCamelCaseString(name?.replace('on', '').replaceAll('-', ' ')),
    onClick: func,
  }));

  return (
    <OptionsWrapper
      type={type}
      onMouseOver={onMouseOver}
      onMouseLeave={onMouseLeave}
      className={`py-2 text-xs text-right ${className} ${
        type === 'th'
          ? 'border-b-2 bg-[#FBFAFF] text-gray-700 last:rounded-tr-xl'
          : 'bg-[#FBFAFF]'
      }`}
    >
      <section className="relative inline-block">
        <Dropdown
          menu={{ items }}
          trigger={['click']}
          placement="bottomLeft"
          overlayStyle={{ minWidth: '100px' }}
        >
          <EllipsisVerticalIcon className="h-4 outline-none cursor-pointer" />
        </Dropdown>
      </section>
    </OptionsWrapper>
  );
};
const Table = ({ children, className, ...rest }) => {
  return (
    <div className="overflow-x-auto w-full">
      <table
        className={`min-w-full  font-inner text-[10px] sm:text-[12px] ${className}`}
        {...rest}
      >
        {children}
      </table>
    </div>
  );
};

Table.Head = Head;
Table.Body = Body;
Table.Row = Row;
Table.Th = Th;
Table.Td = Td;
Table.Options = Options;

export default Table;

// dont delete above commented code

// 2nd code

// import { EllipsisVerticalIcon } from '@heroicons/react/24/outline';
// import { useEffect, useRef, useState } from 'react';
// // import { unCamelCaseString } from '../../../helperFunction';

// export const Head = ({ children, className, ...rest }) => (
//   <thead
//     className={`w-full text-[#5E6366] bg-[#F2F1FB] ${className}`}
//     {...rest}
//   >
//     {children}
//   </thead>
// );

// export const Body = ({ children, className, ...rest }) => (
//   <tbody
//     className={`w-full !text-[12px] !text-[#000000] ${className}`}
//     {...rest}
//   >
//     {children}
//   </tbody>
// );

// export const Row = ({
//   children,
//   className,
//   isFetching,
//   isClickable = false,
//   ...rest
// }) => (
//   <tr
//     className={`w-full leading-[14.52px] border-b border-[#F2F1FB] ${
//       isFetching ? 'text-gray-400' : 'text-black'
//     } ${isClickable ? 'hover:bg-slate-200 cursor-pointer' : ''} ${className}`}
//     {...rest}
//   >
//     {children}
//   </tr>
// );

// export const Th = ({ children, className, ...rest }) => (
//   <th
//     className={`text-left px-3 sm:px-5 py-2 sm:py-3 border-b-2 bg-[#FBFAFF] text-[10px] sm:text-xs font-medium text-[#5E6366] uppercase tracking-wider ${className}`}
//     {...rest}
//   >
//     {children}
//   </th>
// );

// export const Td = ({ children, className, ...rest }) => (
//   <td
//     className={`px-3 sm:px-5 py-2 sm:py-3 bg-white !text-[#000000] text-[10px] sm:text-[12px] font-medium ${className}`}
//     {...rest}
//   >
//     {children}
//   </td>
// );

// const OptionsWrapper = ({ type, children, ...rest }) => {
//   if (type === 'td') return <td {...rest}>{children}</td>;
//   if (type === 'th') return <th {...rest}>{children}</th>;
//   return null;
// };

// export const Options = ({
//   className,
//   type = 'td',
//   onMouseOver,
//   onMouseLeave,
//   ...rest
// }) => {
//   const [expand, setExpand] = useState(false);
//   const [showAbove, setShowAbove] = useState(false);
//   const menuRef = useRef(null);
//   const buttonRef = useRef(null);
//   const containerRef = useRef(null);

//   const onFunctions =
//     Object.entries(rest)?.filter(([key]) => key?.startsWith('on')) || [];

//   useEffect(() => {
//     if (
//       expand &&
//       menuRef.current &&
//       buttonRef.current &&
//       containerRef.current
//     ) {
//       const menuRect = menuRef.current.getBoundingClientRect();
//       const buttonRect = buttonRef.current.getBoundingClientRect();
//       const containerRect = containerRef.current.getBoundingClientRect();
//       const tableRect = containerRef.current
//         .closest('table')
//         .getBoundingClientRect();
//       const windowHeight = window.innerHeight;

//       // Calculate space below and above
//       const spaceBelow = windowHeight - buttonRect.bottom; // eslint-disable-line
//       const spaceAbove = buttonRect.top - tableRect.top;

//       // Check if menu would overflow either the viewport or table bottom
//       const wouldOverflow =
//         buttonRect.bottom + menuRect.height > windowHeight ||
//         containerRect.bottom + menuRect.height > tableRect.bottom;

//       // If it would overflow and there's more space above, show above
//       setShowAbove(wouldOverflow && spaceAbove >= menuRect.height);
//     }
//   }, [expand]);

//   const formatMenuText = (str) => {
//     const cleaned = str.replace('on', '').replaceAll('-', ' ');
//     const words = cleaned.split(/(?=[A-Z])/);
//     return words
//       .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
//       .join(' ');
//   };

//   return (
//     <OptionsWrapper
//       type={type}
//       onMouseOver={onMouseOver}
//       onMouseLeave={onMouseLeave}
//       className={`px-5 py-2 text-xs text-right ${className} ${
//         type === 'th'
//           ? 'border-b-2 bg-[#FBFAFF] text-gray-700 last:rounded-tr-xl'
//           : 'bg-white'
//       }`}
//       tabIndex={1}
//       {...Object.fromEntries(
//         Object.entries(rest).filter(([key]) => !key?.startsWith('on'))
//       )}
//     >
//       <section ref={containerRef} className="relative inline-block">
//         <EllipsisVerticalIcon
//           ref={buttonRef}
//           className="h-4 outline-none cursor-pointer"
//           onClick={(e) => {
//             e.stopPropagation();
//             setExpand((prev) => !prev);
//           }}
//           onBlur={() => {
//             setTimeout(() => {
//               setExpand(false);
//             }, 200);
//           }}
//           tabIndex={1}
//         />
//         {expand && onFunctions?.length > 0 && (
//           <ul
//             ref={menuRef}
//             className={`absolute shadow-xl right-[50%] text-left bg-white rounded w-max z-50
//               ${showAbove ? 'bottom-full mb-1' : 'top-full mt-1'}`}
//             style={{
//               maxHeight: '200px',
//               overflowY: 'auto',
//             }}
//           >
//             {onFunctions?.map(([name, func], index) => {
//               const isFirst = index === 0;
//               const isLast = index === onFunctions.length - 1;
//               return (
//                 <li
//                   key={name}
//                   className={`cursor-pointer w-full text-gray-600 text-xs leading-3 tracking-normal py-3 px-3 hover:bg-blue-50 hover:text-blue-600
//                     ${isFirst ? 'rounded-t' : ''}
//                     ${isLast ? 'rounded-b' : ''}
//                     font-normal`}
//                   onClick={func}
//                 >
//                   {formatMenuText(name)}
//                 </li>
//               );
//             })}
//           </ul>
//         )}
//       </section>
//     </OptionsWrapper>
//   );
// };

// const Table = ({ children, className, ...rest }) => (
//   <div className="overflow-x-auto w-full">
//     <table
//       className={`min-w-full font-inner text-[10px] sm:text-[12px] ${className}`}
//       {...rest}
//     >
//       {children}
//     </table>
//   </div>
// );

// Table.Head = Head;
// Table.Body = Body;
// Table.Row = Row;
// Table.Th = Th;
// Table.Td = Td;
// Table.Options = Options;

// export default Table;
