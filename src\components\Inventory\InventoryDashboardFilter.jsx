import { FilterOutlined, RotateLeftOutlined } from '@ant-design/icons';
import { Badge, Button, DatePicker, Popover, Select } from 'antd';
import { useGetFilterOptionsForInventoryDashboardQuery } from '../../slices/transactionsApiSlice';
import MultiSelect from '../global/components/MultiSelect';

const { RangePicker } = DatePicker;
const InventoryDashboardFilter = ({ filters = {}, setFilters, type }) => {
  const { data: allData = {} } = useGetFilterOptionsForInventoryDashboardQuery({
    type,
  });
  const itemNameOptions = allData?.object?.map((item) => ({
    label: item?.label,
    value: item?.value,
  }));

  const timeOptions = [
    { value: 'All Time', label: 'All Time' },
    { value: 'Today', label: 'Today' },
    { value: 'Yesterday', label: 'Yesterday' },
    { value: 'Last 7 Days', label: 'Last 7 Days' },
    { value: 'Last 30 Days', label: 'Last 30 Days' },
    { value: 'Custom', label: 'Custom' },
  ];

  const onFilterChange = (key, value) => {
    setFilters({ ...filters, [key]: value });
  };

  const resetFilterValues = () => {
    setFilters({
      ...filters,
      itemNames: [],
      timePeriod: 'All Time',
      uoms: [],
      dateRange: [],
    });
  };

  // Calculate the number of active filters
  const activeFiltersCount =
    ((filters?.itemNames?.length || 0) > 0 ? 1 : 0) +
    ((filters?.uoms?.length || 0) > 0 ? 1 : 0) +
    (filters?.timePeriod &&
    filters.timePeriod !== 'All Time' &&
    filters.timePeriod !== 'Custom'
      ? 1
      : 0) +
    ((filters?.dateRange?.length || 0) > 0 ? 1 : 0);

  return (
    <Popover
      placement="bottomRight"
      content={
        <div className="flex flex-col gap-4 p-4 w-[320px]">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-medium text-gray-900">Filters</h3>
            <Button
              onClick={resetFilterValues}
              type="text"
              className="text-gray-500 hover:text-blue-600 flex items-center gap-1"
            >
              <RotateLeftOutlined className="w-3 h-3" />
              Reset all
            </Button>
          </div>
          <Select
            style={{ width: '100%' }}
            value={filters?.timePeriod || 'All Time'}
            onChange={(value) => onFilterChange('timePeriod', value)}
            options={timeOptions}
            placeholder="Select Time Period"
          />
          {type !== 'both' && (
            <MultiSelect
              style={{ width: '100%' }}
              value={filters?.itemNames || []}
              onChange={(e) => {
                onFilterChange('itemNames', e.target.value);
              }}
              options={itemNameOptions}
              placeholder="Select Item Names"
            />
          )}
          {filters?.timePeriod === 'Custom' && (
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates) => onFilterChange('dateRange', dates)}
              value={filters?.dateRange || null}
            />
          )}
        </div>
      }
      trigger={'click'}
    >
      <Badge count={activeFiltersCount}>
        <FilterOutlined className="text-xl cursor-pointer" />
      </Badge>
    </Popover>
  );
};

export default InventoryDashboardFilter;
