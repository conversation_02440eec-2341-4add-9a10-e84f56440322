import { ArrowLeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, DatePicker, Input } from 'antd';
import dayjs from 'dayjs';
import { useContext, useEffect, useMemo, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import SelectV2 from '../../../components/global/components/SelectV2';
import Textarea from '../../../components/global/components/Textarea';
import MasterDetails from '../../../components/MasterDetails';
import ProductFormatManager from '../../../components/ProductFormats/ProductFormatManager';
import StaticProductTable from '../../../components/ProductFormats/StaticProductTable';
import {
  mobileWidth,
  renderFieldsBasedOnType,
  tabletWidth,
} from '../../../helperFunction';
import usePrefixIds from '../../../hooks/usePrefixIds';
import { useGetCustomTypesQuery } from '../../../slices/AccountManagement/customTypesApiSlice';
import {
  useCreateVoucherMutation,
  useEditVoucherMutation,
  useGetVoucherByIdQuery,
} from '../../../slices/AccountManagement/voucherApiSlice';
import { useGetDropdownsQuery } from '../../../slices/dropdownApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice';
import {
  useGetAllVendorsForOptionsQuery,
  useGetVendorByIdQuery,
} from '../../../slices/vendorApiSlice';
import { Store } from '../../../store/Store';
import CustomTypesModal from '../global/CustomTypesModal';

const PurchaseVoucherForm = ({ props }) => {
  const { setOpenModal, editData, setEditData } = props;
  const [editVoucher] = useEditVoucherMutation();
  const { data: ledgerTypes, isLoading: isLedgerTypesLoading } =
    useGetCustomTypesQuery({ type: 'ledgerType' });
  const { data: vendors, isLoading: isVendorLoading } =
    useGetAllVendorsForOptionsQuery();
  const { data: voucher } = useGetVoucherByIdQuery(
    { id: editData?._id },
    { skip: editData?._id === undefined }
  );
  const isEditing = !!editData?._id;
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const { defaults: { defaultParam } = {} } = useContext(Store);
  const [createVoucher] = useCreateVoucherMutation();
  const { data: dropdownsData } = useGetDropdownsQuery();

  const [formData, setFormData] = useState({
    date: dayjs(),
    purchaseVoucherId: '',
    voucherType: 'purchaseVoucher',
    ledgerType: '',
    remark: '',
    vendor: '',
  });

  const [items, setItems] = useState(
    defaultParam?.projectDefaults?.showProductFormatTable || isEditing
      ? []
      : [
          {
            key: Date.now() + Math.random(),
            itemId: '',
            productName: '',
            uom: '',
            hsn: '',
            quantity: '',
            rate: '',
            discount: '',
            amount: 0,
            cgst: '',
            sgst: '',
            cgstAmount: 0,
            sgstAmount: 0,
            igst: '',
            igstAmount: 0,
            totalAmount: 0,
            color: '#FFFFFF',
          },
        ]
  );
  const [charges, setCharges] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [chargesVisibility, setChargesVisibility] = useState({});
  const [displayFormat, setDisplayFormat] = useState(null);

  const [additionalFields, setAdditionalFields] = useState(null);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'purchaseVoucherId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });
  const [openCustomTypeModal, setOpenCustomTypeModal] = useState(false);
  const [vendorLoading, setVendorLoading] = useState(false);
  const [vendor, setVendor] = useState(null);

  const { data: foundVendor } = useGetVendorByIdQuery(formData.vendor, {
    skip: !formData.vendor,
    refetchOnMountOrArgChange: false,
  });

  useEffect(() => {
    const path = '/accountmanagement/voucher/purchaseVoucher';
    getTemplates({ path });
  }, [getTemplates]);

  useEffect(() => {
    if (voucher?._id && voucher?.purchaseVoucherData?.additionalFields) {
      setAdditionalFields(voucher.purchaseVoucherData.additionalFields);
      setSelectedTemplate(voucher.purchaseVoucherData.additionalFields);
    } else if (templatesData && Array.isArray(templatesData)) {
      const defaultTemplate =
        templatesData.find((t) => t?.name?.startsWith('Default')) ||
        templatesData?.[0];
      if (defaultTemplate) {
        setAdditionalFields(defaultTemplate);
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, [
    voucher?._id,
    templatesData,
    voucher?.purchaseVoucherData?.additionalFields,
  ]);

  // Handle form field changes
  const handleFormFieldChange = (fieldName, fieldValue) => {
    if (fieldName === 'ledgerType' && fieldValue === 'addType') {
      setOpenCustomTypeModal(true);
      return;
    }

    if (fieldName === 'vendor' && fieldValue) {
      setVendorLoading(true);
    }

    setFormData((prev) => ({
      ...prev,
      [fieldName]: fieldValue,
    }));
  };

  const changeHandler = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  useEffect(() => {
    if (foundVendor?._id) {
      setVendorLoading(false);
      setVendor(foundVendor);
    } else if (foundVendor === null && formData.vendor) {
      setVendorLoading(false);
      setVendor(null);
    }
  }, [foundVendor, formData.vendor]);

  useEffect(() => {
    if (!formData.vendor) {
      setVendor(null);
      setVendorLoading(false);
    }
  }, [formData.vendor]);

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setDropdownIdx(idx);
      setTemplateDropDownModal(true);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: updatedAdditionalFields,
      }));
    }
  };

  const uomOptions = useMemo(() => {
    return (
      dropdownsData?.dropdowns
        ?.find((e) => e.name === 'uom')
        ?.values?.map((option) => ({
          label: option,
          value: option,
        })) || []
    );
  }, [dropdownsData]);

  // Prefill form data when editing (only run once when voucher data is loaded)
  useEffect(() => {
    if (voucher?._id && isEditing) {
      const voucherFormData = {
        date: voucher?.date ? dayjs(voucher.date) : null,
        purchaseVoucherId: voucher?.purchaseVoucherId || '',
        voucherType: voucher?.voucherType || 'purchaseVoucher',
        ledgerType: voucher?.purchaseVoucherData?.ledgerType?._id || '',
        remark: voucher?.remarks || '',
        vendor: voucher?.purchaseVoucherData?.vendor?._id || '',
      };

      setFormData(voucherFormData);

      // Set items if they exist
      if (
        voucher?.purchaseVoucherData?.items &&
        voucher.purchaseVoucherData.items.length > 0
      ) {
        setItems(voucher.purchaseVoucherData.items);
      }

      // Set other related data
      if (voucher?.purchaseVoucherData?.charges) {
        setCharges(voucher.purchaseVoucherData.charges);
      }
      if (voucher?.purchaseVoucherData?.columnVisibility) {
        setColumnVisibility(voucher.purchaseVoucherData.columnVisibility);
      }
      if (voucher?.purchaseVoucherData?.chargesVisibility) {
        setChargesVisibility(voucher.purchaseVoucherData.chargesVisibility);
      }
      if (voucher?.purchaseVoucherData?.productTableFormat) {
        setDisplayFormat(voucher.purchaseVoucherData.productTableFormat);
      }
      // Set additional fields if they exist
      if (voucher?.purchaseVoucherData?.additionalFields) {
        setAdditionalFields(voucher.purchaseVoucherData.additionalFields);
        setSelectedTemplate(voucher.purchaseVoucherData.additionalFields);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [voucher?._id, isEditing]);

  const productFormatData = useMemo(() => {
    const rv = voucher?.purchaseVoucherData || {};
    const pf = rv?.productTableFormat;
    const pfId = typeof pf === 'object' ? pf?._id : pf;
    return {
      productDetailsFromFormat: rv?.items || [],
      productChargesFromFormat: rv?.charges || {},
      productTableColumnHideStatus: rv?.columnVisibility || {},
      productTableChargesHideStatus: rv?.chargesVisibility || {},
      productTableFormat: pfId || null,
    };
  }, [voucher]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.date) {
      toast.error('Please select a date');
      return;
    }
    let transformedItems = [];
    for (let i of items) {
      let temp = {
        ...i,
      };
      transformedItems.push(temp);
    }

    let obj = {
      ...formData,
      date: formData.date ? formData.date.format('YYYY-MM-DD') : undefined,
      items: items,
      charges,
      columnVisibility,
      chargesVisibility,
      productTableFormat: displayFormat,
      additionalFields: additionalFields || null,
    };

    let res;
    if (editData?._id) {
      res = await editVoucher({ data: { updateData: obj, id: editData?._id } });
    } else {
      res = await createVoucher({
        data: { ...obj, voucherType: 'purchaseVoucher' },
      });
    }

    if (!res?.error) {
      setOpenModal(false);
      setFormData({
        date: null,
        purchaseVoucherId: '',
        voucherType: 'purchaseVoucher',
        ledgerType: '',
        remark: '',
        vendor: '',
      });
      setItems(
        defaultParam?.projectDefaults?.showProductFormatTable
          ? []
          : [
              {
                key: Date.now() + Math.random(),
                itemId: '',
                productName: '',
                uom: '',
                hsn: '',
                quantity: '',
                rate: '',
                discount: '',
                amount: 0,
                cgst: '',
                sgst: '',
                cgstAmount: 0,
                sgstAmount: 0,
                igst: '',
                igstAmount: 0,
                totalAmount: 0,
                color: '#FFFFFF',
              },
            ]
      );
      setCharges({});
      setColumnVisibility({});
      setChargesVisibility({});
      setDisplayFormat(null);
      toast.success(
        `Purchase Voucher ${editData?._id ? 'Updated' : 'Created'} successfully`
      );
    } else {
      toast.error(
        'Faced an error while creating voucher, please reload and try again.'
      );
    }
  };

  return (
    <>
      {openCustomTypeModal && (
        <CustomTypesModal
          type="ledgerType"
          openModal={openCustomTypeModal}
          setOpenModal={setOpenCustomTypeModal}
        />
      )}
      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setOpenModal(false);
                setEditData({});
                setFormData({
                  date: null,
                  purchaseVoucherId: '',
                  voucherType: 'purchaseVoucher',
                  ledgerType: '',
                  remark: '',
                  vendor: '',
                });
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEditing ? 'Edit' : 'Create'} Purchase Voucher
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEditing
                  ? 'Update purchase voucher information'
                  : 'Create a new purchase voucher'}
              </p>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Basic Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">
                  Purchase Voucher ID
                </label>
                {isEditing ? (
                  <div className="text-sm text-gray-900">
                    <Input
                      className="text-sm bg-gray-50"
                      disabled
                      value={formData?.purchaseVoucherId}
                    />
                  </div>
                ) : (
                  <IdGenComp {...idCompData} />
                )}
              </div>

              <div className="space-y-1">
                <label className="block mb-1 text-sm text-gray-500 font-medium">
                  Choose Template
                </label>
                <SelectV2
                  options={templatesData?.map((template) => ({
                    value: template?._id,
                    name: template?.name,
                  }))}
                  value={selectedTemplate?._id}
                  onChange={(e) => {
                    const template = templatesData.find(
                      (t) => t._id === e.target.value
                    );
                    if (selectedTemplate?._id === e.target.value) {
                      return;
                    }
                    setAdditionalFields(template);
                    setSelectedTemplate(template);
                  }}
                />
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">
                  Date
                </label>
                <DatePicker
                  format={'DD-MM-YYYY'}
                  className="text-sm placeholder:text-gray-400 w-full"
                  size="middle"
                  value={formData.date}
                  onChange={(date) => handleFormFieldChange('date', date)}
                />
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">
                  Ledger Type
                </label>
                <SelectV2
                  name="ledgerType"
                  placeholder="Select ledger type"
                  isLoading={isLedgerTypesLoading}
                  value={formData.ledgerType}
                  onChange={(e) =>
                    handleFormFieldChange('ledgerType', e.target.value)
                  }
                  options={[
                    { name: '+ Add Type', value: 'addType' },
                    ...(ledgerTypes?.map((item) => ({
                      name: item.name,
                      value: item._id,
                    })) || []),
                  ]}
                  className="text-sm"
                />
              </div>
            </div>
          </div>

          {/* Vendor & Purchase Order */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Vendor</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">
                  Select Vendor
                </label>
                <SelectV2
                  name="vendor"
                  placeholder="Select vendor"
                  isLoading={isVendorLoading}
                  value={formData.vendor}
                  onChange={(e) =>
                    handleFormFieldChange('vendor', e.target.value)
                  }
                  options={
                    vendors?.map((vendor) => ({
                      name: vendor.name,
                      value: vendor._id,
                    })) || []
                  }
                  className="text-sm"
                />
              </div>
            </div>
          </div>

          {/* Vendor Details */}
          {vendor && (
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Vendor Details
              </h3>
              <MasterDetails
                isLoading={vendorLoading}
                isMobile={isMobile}
                isTablet={isTablet}
                className="!text-gray-500 !text-sm"
                details={vendor || {}}
                setDetails={setVendor}
                excludedFields={[
                  'id',
                  '_id',
                  'logo',
                  '__v',
                  'profileId',
                  'createdAt',
                  'updatedAt',
                  'idFormat',
                  'isUsed',
                  'isHidden',
                  'lastUsed',
                  'additionalFields',
                  'name',
                  'idData',
                  'attachments',
                ]}
              />
            </div>
          )}

          {/* Item Details */}
          {(() => {
            const useFormatTable = isEditing
              ? !!voucher?.purchaseVoucherData?.productTableFormat
              : defaultParam?.projectDefaults?.showProductFormatTable;

            return (
              <div className="bg-white border border-gray-200 rounded-lg p-3">
                <h3 className="text-sm font-medium text-gray-700 mb-3">
                  {useFormatTable ? 'Product Format Table' : 'Product Table'}
                </h3>
                {useFormatTable ? (
                  <ProductFormatManager
                    input={items}
                    setInput={setItems}
                    charges={charges}
                    setCharges={setCharges}
                    columnVisibility={columnVisibility}
                    setColumnVisibility={setColumnVisibility}
                    chargesVisibility={chargesVisibility}
                    setChargesVisibility={setChargesVisibility}
                    displayFormat={displayFormat}
                    setDisplayFormat={setDisplayFormat}
                    isEdit={isEditing}
                    isCopy={false}
                    data={productFormatData}
                  />
                ) : (
                  <StaticProductTable
                    input={items}
                    setInput={setItems}
                    charges={charges}
                    setCharges={setCharges}
                    uomOptions={uomOptions}
                  />
                )}
              </div>
            );
          })()}

          {/* Template Details */}
          {additionalFields?.templateData?.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Template Details
              </h3>
              <section className="w-full">
                {renderFieldsBasedOnType(
                  additionalFields,
                  handleInputChange,
                  templateDropDownModal,
                  setTemplateDropDownModal,
                  setAdditionalFields,
                  newOptionStatus,
                  setNewOptionStatus,
                  dropdownIdx,
                  setDropdownIdx
                )}
              </section>
            </div>
          )}

          {/* Remarks */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Remarks</h3>
            <Textarea
              name="remark"
              rows={3}
              placeholder="Enter additional remarks or comments"
              className="text-sm resize-none"
              value={formData.remark}
              onChange={(e) => changeHandler(e)}
            />
          </div>

          {/* Footer Actions */}
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100 -mx-4 -mb-4 mt-4">
            <div className="flex items-center justify-end">
              <Button
                type="primary"
                htmlType="submit"
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                {isEditing ? 'Update' : 'Save'} Purchase Voucher
              </Button>
            </div>
          </div>
        </form>
      </div>
    </>
  );
};

export default PurchaseVoucherForm;
