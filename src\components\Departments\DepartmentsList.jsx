import { useDispatch } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { unCamelCaseString } from '../../helperFunction';
import { apiSlice } from '../../slices/apiSlice';
import {
  useDeleteDepartmentMutation,
  useGetDepartmentPagesQuery,
} from '../../slices/departmentApiSlice';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import { customConfirm } from '../../utils/customConfirm';
import Pagination from '../global/components/Pagination';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import Tooltip from '../global/components/ToolTip';

const DepartmentsList = ({ setEditData, setEditModal }) => {
  const dispatch = useDispatch();

  const [searchParams, setSearchParams] = useSearchParams({
    page: 1,
    limit: PAGINATION_LIMIT,
  });

  const page = +searchParams.get('page');
  const limit = +searchParams.get('limit');

  const {
    data: depsData = {},
    isLoading: isLoadingGet,
    isFetching: isFetchingGet,
  } = useGetDepartmentPagesQuery(
    { page, limit },
    { skip: !page || !limit, refetchOnMountOrArgChange: true }
  );
  const { results: departmentsData, totalPages, totalResults } = depsData;

  const [deleteDepartment] = useDeleteDepartmentMutation();

  const handleDelete = async (item) => {
    if (item.isDefault) {
      toast.error('Cannot delete default module');
      return;
    }

    const confirm = await customConfirm(
      `Are you sure you want to delete department: ${item.name}?`,
      'delete'
    );

    if (!confirm) return;

    const res = await deleteDepartment({ id: item._id }).unwrap();

    if (res) {
      toast.success(`Successfully deleted department: ${item.name}`);
      dispatch(apiSlice.util.invalidateTags(['Defaults', 'Departments']));
    }
  };

  return (
    <div className="w-full">
      {isLoadingGet ? (
        <Spinner />
      ) : (
        <>
          <div className="w-full overflow-x-scroll">
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Th>Name</Table.Th>
                  <Table.Th></Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                {departmentsData?.map((item) => (
                  <Table.Row key={item._id} isFetching={isFetchingGet}>
                    <Table.Td className={'md:!min-w-[24rem] md:!w-[24rem]'}>
                      {unCamelCaseString(item.name)?.length > 55 ? (
                        <Tooltip>
                          {unCamelCaseString(item.name)?.slice(0, 55) + '...'}
                        </Tooltip>
                      ) : (
                        unCamelCaseString(item.name)
                      )}
                    </Table.Td>
                    <Table.Options
                      className={'bg-white'}
                      onDelete={() => {
                        if (item?.isDefault) {
                          toast.error('Cannot delete default Departments');
                          return;
                        } else {
                          handleDelete(item);
                        }
                      }}
                      onEdit={() => {
                        setEditData(item);
                        setEditModal(true);
                      }}
                    />
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
          <Pagination
            totalPages={totalPages}
            totalResults={totalResults}
            page={page}
            limit={limit}
            setPage={(val) =>
              setSearchParams(
                (prev) => {
                  prev.set('page', val);
                  return prev;
                },
                { replace: true }
              )
            }
            setLimit={(val) =>
              setSearchParams(
                (prev) => {
                  prev.set('limit', val);
                  return prev;
                },
                { replace: true }
              )
            }
          />
        </>
      )}
    </div>
  );
};

export default DepartmentsList;
