import { useUpdateSalesInquiryMutation } from '../../slices/salesInquiryDashboardApiSlice';
import Input from '../global/components/Input';
import Table from '../global/components/Table';

import { useContext, useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import cross from '../../assets/images/cross.png';
import removeIcon from '../../assets/svgs/cancel.svg';
import { ReactComponent as JpgPng } from '../../assets/svgs/pdfsvg.svg';
import {
  dateOptions,
  generateDateString,
  renderFieldsBasedOnType,
} from '../../helperFunction';
import { useLazyGetAllcustomerQuery } from '../../slices/customerDataSlice';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice';
import { useGetProductsQuery } from '../../slices/productApiSlice';
import { Store } from '../../store/Store';
import Button from '../global/components/Button';
import DragAndDrop from '../global/components/DragAndDrop';
import MultiSelect from '../global/components/MultiSelect';
import RightSidebar from '../global/components/RightSidebar';
import Select from '../global/components/Select';
import Textarea from '../global/components/Textarea';
import Tooltip from '../global/components/ToolTip';
import { Label } from '../v2';
import EditButton from '../v3/global/components/EditButton';
import MediaMetaDataCard from '../v3/global/components/MediaMetaDataCard';
import MediaModal from '../v3/global/components/MediaModal';
import AddUomModal from '../v3/InventoryMasters/AddUomModal';
export default function EditSalesInquiryModal({
  salesInquiryData = [],
  setIsEdit,
}) {
  const {
    id,
    customerName,
    companyName,
    products,
    remark,
    _id,
    files,
    additionalFields: additonalData,
  } = salesInquiryData;
  const { state } = useContext(Store);
  const createdBy = state?.user?.name;

  const [salesInquiryRemark, setSalesInquiryRemark] = useState(remark);
  const [openAddUomModal, setOpenAddUomModal] = useState(false);
  const [SalesProductStatus, setSalesProductStatus] = useState(false);
  const [followUpDate, setFollowUpDate] = useState('');
  const [onEditSalesInquiry, { isLoading: isUpdateSILoading }] =
    useUpdateSalesInquiryMutation();
  const [customer, setCustomer] = useState(customerName);
  const [company, setCompany] = useState(companyName);
  const [salesInquiryProducts, setSalesInquiryProducts] = useState(
    products || []
  );
  // const [addProducts, setAddProducts] = useState(false);

  const [pdf, setpdf] = useState(files);
  const [removedPdf, setRemovedPdf] = useState([]);
  const { data: dropdownsData } = useGetDropdownsQuery();
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const location = useLocation();
  const pathRef = useRef(location.pathname);
  const [getCustomers] = useLazyGetAllcustomerQuery();
  const { data: productsRes } = useGetProductsQuery();
  const inventoryProducts = productsRes?.products || [];
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [ShowEditMetaData, setShowEditMetaData] = useState(false);
  const [ShowMetaDataSidebar, setShowMetaDataSidebar] = useState(false);
  const [additionalFields, setAdditionalFields] = useState(additonalData);
  const [selectedTemplate, setSelectedTemplate] = useState(additonalData);
  const [customers, setCustomers] = useState([]);

  const handleSetDate = (e) => {
    const day = +e.target.value;
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + day);
    setFollowUpDate(expiryDate);
  };

  function handleSubmitModal() {
    for (let i = 0; i < selectedProducts.length; i++) {
      selectedProducts[i].productName = selectedProducts[i]?.label || '';
      selectedProducts[i].quantity = selectedProducts[i]?.quantity ?? 0;
    }

    for (let i = 0; i < selectedProducts?.length; i++) {
      if (selectedProducts[i].quantity < 0) {
        toast.error('Product quantity can not be negative');
        return;
      }
    }

    for (let i = 0; i < salesInquiryProducts.length; i++) {
      if (!salesInquiryProducts[i].productName) {
        toast.error('Enter Product name');
        return;
      }
      if (!salesInquiryProducts[i].quantity) {
        toast.error('Enter Product Quantity');
        return;
      }

      if (salesInquiryProducts[i].quantity < 0) {
        toast.error('Product Quantity can not be negative');
        return;
      }

      if (!salesInquiryProducts[i].uom) {
        toast.error('Enter UOM');
        return;
      }
    }
    const dateToSend =
      followUpDate instanceof Date
        ? followUpDate.toISOString().slice(0, 10)
        : followUpDate;

    onEditSalesInquiry({
      customerName: customer,
      companyName: company,
      products: [...salesInquiryProducts, ...selectedProducts],
      remark: salesInquiryRemark,
      id: _id,
      files: pdf,
      removedPdf,
      createdBy,
      additionalFields,
      followUpDate: dateToSend,
    })
      .then(() => {
        setIsEdit(false);
        toast.success(`SI-${id} is edited succssfully.`);
      })
      .catch((err) => {
        console.error(err); //eslint-disable-line
        toast.error(
          `SI-${id} was not edited due to some error. (${err.message})`
        );
      });
  }

  function handleAddRow() {
    if (SalesProductStatus) {
      const editProducts =
        salesInquiryProducts[salesInquiryProducts.length - 1];
      if (!editProducts.productName) {
        toast.error('Enter the Product Name');
        return;
      }
      if (!editProducts.quantity) {
        toast.error('Enter the product quantity');
        return;
      }
      if (!editProducts.uom) {
        toast.error('Enter the UOM');
        return;
      }

      setSalesInquiryProducts([
        ...salesInquiryProducts,
        {
          productName: '',
          quantity: '',
          uom: '',
        },
      ]);
    } else {
      setSalesInquiryProducts([
        ...salesInquiryProducts,
        {
          productName: '',
          quantity: '',
          uom: '',
        },
      ]);
      setSalesProductStatus(true);
    }
  }

  /**
   * The function `changeHandler` takes in an event object and reads the files from the event, converts
   * them to data URLs, and adds them to an array called `pdf`.
   * @returns The function does not have a return statement.
   */
  const changeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name;
      let ftype = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          name: fname,
          type: ftype,
          data: url,
        };
        setpdf((prev) => [...prev, data]);
      });
    }
  };

  /**
   * The `removePdf` function removes a PDF item from a list based on its ID and updates the state
   * variables `pdf` and `removedPdf`.
   */
  const removePdf = (id) => {
    const filtered = pdf.filter((itemm) => itemm._id !== id);

    setpdf(filtered);
    setRemovedPdf([...removedPdf, id]);
  };

  function handleRemoveProduct(index) {
    let newProducts = salesInquiryProducts.filter((item, i) => i !== index);
    setSalesInquiryProducts(newProducts);
    setSalesProductStatus(false);
  }

  function handleRemoveSelectedProduct(index) {
    let newProducts = selectedProducts.filter((item, i) => i !== index);
    setSelectedProducts(newProducts);
  }

  // const handleInputChange = (fieldValue, fieldName) => {
  //   const updatedAdditionalFields = additionalFields?.templateData?.map(
  //     (field) => {
  //       if (field?.fieldName === fieldName) {
  //         return {
  //           ...field,
  //           fieldValue,
  //         };
  //       } else {
  //         return field;
  //       }
  //     }
  //   );
  //   setAdditionalFields((prev) => {
  //     return {
  //       ...prev,
  //       templateData: updatedAdditionalFields,
  //     };
  //   });
  // };

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    const updatedAdditionalFields = additionalFields?.templateData?.map(
      (field) => {
        if (field?.fieldName === fieldName) {
          return {
            ...field,
            fieldValue,
          };
        } else {
          return field;
        }
      }
    );
    setAdditionalFields((prev) => {
      return {
        ...prev,
        templateData: updatedAdditionalFields,
      };
    });
  };

  useEffect(() => {
    const getCols = async () => {
      const path = pathRef.current;
      getTemplates({ path });
    };
    getCols();
  }, [getTemplates]);

  useEffect(() => {
    if (salesInquiryData?.followUpDate) {
      setFollowUpDate(generateDateString(salesInquiryData?.followUpDate));
    }
  }, [salesInquiryData]);

  useEffect(() => {
    if (!additonalData || !templatesData) return;
    const findSelectedTemplate = templatesData?.find((tmeplate) => {
      return tmeplate?.name === additonalData?.name;
    });
    setSelectedTemplate(findSelectedTemplate);
  }, [additonalData, templatesData]);
  const SelectCustomer = (e) => {
    setCustomer(e.target.value);
    customers.filter((item) => {
      if (item.name === e.target.value) {
        setCompany(item.company_name);
      }
    });
  };
  useEffect(() => {
    const getCustomersData = async () => {
      const customersData = await getCustomers().unwrap();
      setCustomers(customersData?.customers);
    };
    getCustomersData();
  }, [getCustomers]);
  return (
    <>
      <Button
        textColor="text-gray-600"
        className="bg-white border-2 text-gray-600 mb-2 "
        onClick={() => {
          setIsEdit(false);
        }}
      >
        Back
      </Button>
      <RightSidebar
        openSideBar={ShowMetaDataSidebar}
        setOpenSideBar={setShowMetaDataSidebar}
      >
        {pdf?.map((file) => {
          return <MediaMetaDataCard file={file} key={file?.name} />;
        })}
      </RightSidebar>
      {openAddUomModal && (
        <AddUomModal
          setShowModal={setOpenAddUomModal}
          dropdowns={dropdownsData?.dropdowns}
        />
      )}
      {ShowEditMetaData && (
        <MediaModal
          FormData={pdf}
          setFormData={setpdf}
          setShowModal={setShowEditMetaData}
          ShowModal={ShowEditMetaData}
        />
      )}
      <div className=" rounded-new">
        <div className="pt-4">
          <div className="grid grid-cols-2 mt-4 mb-2 gap-4">
            <div className="w-full">
              <label className="block mb-1 text-sm text-gray-500 font-medium">
                Sales Inquiry ID:
              </label>
              <Input value={id} disabled={true} />
            </div>

            <div className="w-full">
              <label className="block mb-1 text-sm text-gray-500 font-medium">
                Customer Name:
              </label>
              <Select
                name="Customer Name"
                id="Customer Name"
                value={customer}
                onChange={(e) => SelectCustomer(e)}
                options={[
                  ...customers.map((customer) => ({
                    value: customer.name,
                    label: customer.name,
                  })),
                ]}
                placeholder="Customer Name"
                className={`${customer ? 'w-full' : '!w-[49.5%]'}`}
              />
            </div>

            <div className="w-full">
              <label className="block mb-1 text-sm text-gray-500 font-medium">
                Company Name:
              </label>
              <Input
                name="Company Name"
                id="Company Name"
                value={company}
                readOnly
                required
                placeholder="Company Name"
              />
            </div>

            <div className="w-full">
              <label className="block mb-1 text-sm text-gray-500 font-medium">
                Choose a template:
              </label>
              <Select
                options={templatesData?.map((template) => ({
                  value: template,
                  name: template.name,
                }))}
                onChange={(e) => {
                  setAdditionalFields(e.target.value);
                  setSelectedTemplate(e.target.value);
                }}
                value={selectedTemplate}
              />
            </div>

            {/* {additionalFields && (
              <div className="w-full mt-2">
                <div className="mb-1 w-full gap-3">
                  {renderFieldsBasedOnType(
                    additionalFields,
                    handleInputChange,
                    false,
                    false,
                    setAdditionalFields,
                    false,
                    false
                  )}
                </div>
              </div>
            )} */}
          </div>

          {additionalFields && (
            <div className="w-full">
              <div className="mb-1 w-full gap-3">
                {renderFieldsBasedOnType(
                  additionalFields,
                  handleInputChange,
                  false,
                  false,
                  setAdditionalFields,
                  false,
                  false
                )}
              </div>
            </div>
          )}

          <div className="w-1/2">
            <label>Follow Up:</label>
            {followUpDate === '+' ? (
              <Input
                type="date"
                className="w-full"
                placeholder="Follow Up Date"
                // value={followUpDate}
                onChange={(e) => {
                  setFollowUpDate(e.target.value);
                }}
              />
            ) : (
              <Select
                options={dateOptions}
                className="w-full"
                placeholder={
                  followUpDate
                    ? generateDateString(new Date(followUpDate))
                    : 'Select'
                }
                onChange={(e) => {
                  if (e.target.value === '+') {
                    setFollowUpDate(e.target.value);
                  } else {
                    handleSetDate(e);
                  }
                }}
              />
            )}
          </div>

          <div className="w-full">
            <div className="flex flex-col gap-x-2  mt-4 mb-4">
              <p className=" text-sm mb-2">Select Products:</p>
              <MultiSelect
                closeMenuOnSelect={false}
                options={inventoryProducts.map((product) => ({
                  value: product?._id,
                  label: product?.name,
                }))}
                value={selectedProducts}
                onChange={(e) => {
                  setSelectedProducts(
                    e.target.value.map((product) => {
                      let value = inventoryProducts.find(
                        (item) => item._id === product.value
                      );
                      return { ...product, uom: value?.uom };
                    })
                  );
                }}
              />
            </div>
            <div className="flex">
              <h3>Product Details</h3>
              <span className="text-red-500 text-xl">*</span>
            </div>
            <section className="pt-3 mt-1">
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>Product Name</Table.Th>
                    <Table.Th>Quantity</Table.Th>
                    <Table.Th>Uom</Table.Th>
                    <Table.Th></Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {salesInquiryProducts?.map((product, index) => {
                    return (
                      <Table.Row key={index}>
                        <>
                          <Tooltip
                            text={product?.productName}
                            maxWidth={'max-w-[40ch]'}
                            className={`${product.productName.length < 25 ? 'hidden' : ''}`}
                          >
                            <Table.Td>
                              <Input
                                name="Product Name"
                                id="Product Name"
                                className="w-[200px]"
                                value={product.productName}
                                onChange={
                                  (e) => {
                                    const temp = salesInquiryProducts.map(
                                      (item, idx) => {
                                        if (index === idx) {
                                          return {
                                            ...item,
                                            productName: e.target.value,
                                          };
                                        } else {
                                          return item;
                                        }
                                      }
                                    );
                                    setSalesInquiryProducts(temp);
                                  }

                                  // setEditProducts({
                                  //   ...editProducts,
                                  //   productName: e.target.value,
                                  // })
                                }
                                placeholder="Product Name"
                              />
                            </Table.Td>
                          </Tooltip>

                          <Table.Td>
                            <Input
                              name="Quantity"
                              id="Quantity"
                              className="w-[180px]"
                              value={product.quantity}
                              onChange={(e) => {
                                const temp = salesInquiryProducts.map(
                                  (item, idx) => {
                                    if (index === idx) {
                                      return {
                                        ...item,
                                        quantity: e.target.value,
                                      };
                                    } else {
                                      return item;
                                    }
                                  }
                                );
                                setSalesInquiryProducts(temp);
                              }}
                              placeholder="Quantity"
                              type="number"
                            />
                          </Table.Td>
                          <Table.Td>
                            <Select
                              placeholder="Select UOM"
                              className="w-[180px]"
                              menuPortalTarget={document.body}
                              onChange={(e) => {
                                if (e.target.value === '+') {
                                  setOpenAddUomModal(true);
                                } else {
                                  const temp = salesInquiryProducts.map(
                                    (item, idx) => {
                                      if (index === idx) {
                                        return {
                                          ...item,
                                          uom: e.target.value,
                                        };
                                      } else {
                                        return item;
                                      }
                                    }
                                  );
                                  setSalesInquiryProducts(temp);
                                }
                              }}
                              value={product.uom}
                              name="uom"
                              closeMenuOnSelect={true}
                              options={[
                                { label: '+Add new UOM', value: '+' },
                                ...(dropdownsData?.dropdowns
                                  ?.find((e) => e.name === 'uom')
                                  ?.values?.map((el, idx) => ({
                                    value: el,
                                    label: el,
                                    key: idx,
                                  })) || []),
                              ]}
                            />
                          </Table.Td>
                          <Table.Td
                            className="hover:cursor-pointer"
                            onClick={() => handleRemoveProduct(index)}
                          >
                            <img
                              src={removeIcon}
                              alt="remove"
                              className="w-5 h-5 object-contain relative"
                            />
                          </Table.Td>
                        </>
                      </Table.Row>
                    );
                  })}

                  {selectedProducts.map((product, index) => {
                    return (
                      <Table.Row key={index}>
                        <Table.Td>
                          {product?.label?.length > 30
                            ? product?.label.substring(0, 30) + '...'
                            : product?.label}
                        </Table.Td>
                        <Table.Td>
                          <Input
                            name="Quantity"
                            id="Quantity"
                            className="w-[180px]"
                            value={selectedProducts?.[index]?.quantity || 0}
                            onChange={(e) =>
                              setSelectedProducts((prev) => {
                                return prev.map((el, idx) => {
                                  if (idx === index)
                                    return { ...el, quantity: e.target.value };
                                  else return el;
                                });
                              })
                            }
                            placeholder="Quantity"
                            type="number"
                          />
                        </Table.Td>
                        <Table.Td>{product?.uom}</Table.Td>
                        <Table.Td
                          className="hover:cursor-pointer"
                          onClick={() => handleRemoveSelectedProduct(index)}
                        >
                          <img
                            src={removeIcon}
                            alt="remove"
                            className="w-5 h-5 object-contain relative"
                          />
                        </Table.Td>
                      </Table.Row>
                    );
                  })}

                  {/* <Table.Row>
                    <Table.Td>
                      <Input
                        name="Product Name"
                        id="Product Name"
                        className="w-[180px]"
                        value={editProducts.productName}
                        onChange={(e) =>
                          setEditProducts({
                            ...editProducts,
                            productName: e.target.value,
                          })
                        }
                        placeholder="Product Name"
                      />
                    </Table.Td>
                    <Table.Td>
                      <Input
                        name="Quantity"
                        id="Quantity"
                        className="w-[180px]"
                        value={editProducts.quantity}
                        onChange={(e) =>
                          setEditProducts({
                            ...editProducts,
                            quantity: e.target.value,
                          })
                        }
                        placeholder="Quantity"
                        type="number"
                      />
                    </Table.Td>
                    <Table.Td>
                      <Select
                        placeholder="Select UOM"
                        className="w-[180px]"
                        onChange={(e) => {
                          setEditProducts({
                            ...editProducts,
                            uom: e.target.value,
                          });
                        }}
                        value={editProducts.uom}
                        name="uom"
                        closeMenuOnSelect={true}
                        options={dropdownsData?.dropdowns
                          ?.find((e) => e.name === 'uom')
                          ?.values?.map((el, index) => ({
                            value: el,
                            label: el,
                            key: index,
                          }))}
                      />
                    </Table.Td>
                  </Table.Row> */}

                  <div
                    className="cursor-pointer text-blue-400 flex items-start ml-4 gap-2"
                    onClick={handleAddRow}
                  >
                    <span>Add More +</span>
                  </div>
                </Table.Body>
              </Table>
            </section>

            <div className="w-full mt-8">
              <label className="block mb-1 text-sm text-gray-500 font-medium">
                Remarks:
              </label>
              <Textarea
                name="salesInquiryRemark"
                id="salesInquiryRemark"
                value={salesInquiryRemark}
                onChange={(e) => setSalesInquiryRemark(e.target.value)}
                placeholder="Remark"
              />
            </div>

            <div className="flex flex-row gap-x-2 items-center mt-8 mb-4">
              <Label className="text-md">Created By:</Label>
              <p className="text-md">{createdBy || ''}</p>
            </div>

            <div className="mt-2 w-full col-span-2">
              <label className="mb-1 font-semibold text-[#b4b4b5]">
                Select File
              </label>

              <DragAndDrop
                accept="application/pdf"
                fileType="JPG/PNG"
                svg={<JpgPng className="h-16" />}
                onChange={(e) => changeHandler(e, 'project')}
                multiple
                className={`text-[#667085] !w-full`}
              />
            </div>
            <Table className={`mt-5`}>
              <Table.Head>
                <Table.Row>
                  <Table.Th>Files</Table.Th>
                  <Table.Th>Description</Table.Th>
                  <Table.Th>
                    <EditButton
                      onClick={() => {
                        setShowEditMetaData(true);
                      }}
                    />
                  </Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                {pdf?.map((item, idx) => {
                  return (
                    <Table.Row key={idx}>
                      <Table.Td>{item.name}</Table.Td>
                      {item?.description?.length > 20 ? (
                        <Table.Td>
                          <span className="break-words">
                            {item?.description?.slice(0, 20)}{' '}
                          </span>
                          <p
                            className="text-blue-500 cursor-pointer inline-block"
                            onClick={() => {
                              setShowMetaDataSidebar(true);
                            }}
                          >
                            Read More...
                          </p>
                        </Table.Td>
                      ) : (
                        <Table.Td>{item.description}</Table.Td>
                      )}
                      <Table.Td
                        onClick={() => {
                          removePdf(item._id);
                        }}
                        className="cursor-pointer"
                      >
                        <img src={cross} alt="Cross Icon" className="w-2 h-2" />
                      </Table.Td>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>

            <div className="flex justify-end">
              <Button
                className={`mt-2 flex justify-end`}
                isLoading={isUpdateSILoading}
                onClick={handleSubmitModal}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
