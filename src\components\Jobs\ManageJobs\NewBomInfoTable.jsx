import { Table } from 'antd';
import { useEffect, useState } from 'react';
import ItemModal from '../../v3/WorkOrder/ItemModal';

import { useGetAllPartsForOptionsQuery } from '../../../slices/partApiSlice';
import { useGetAllProductsForOptionsQuery } from '../../../slices/productApiSlice';

const NewBomInfoTable = ({ bomItemId, items, masterOrderQuantity }) => {
  const [record, setRecord] = useState({});
  const [orderQuantity, setOrderQuantity] = useState(0);
  const [rmToShow, setRMToShow] = useState({
    rawMaterials: {},
    orderQuantity: 0,
    open: false,
  });

  const { data: allParts = [] } = useGetAllPartsForOptionsQuery();
  const { data: allProducts = [] } = useGetAllProductsForOptionsQuery();

  useEffect(() => {
    if (items?.length > 0 && bomItemId?.length >= 24) {
      for (let i of items) {
        if (i?.itemId === bomItemId) {
          setRecord(i);
          break;
        }
      }
    }
  }, [bomItemId, items]);

  useEffect(() => {
    if (record?.itemId?.length >= 24 && masterOrderQuantity) {
      setOrderQuantity(record?.units * masterOrderQuantity);
    }
  }, [record, masterOrderQuantity]);

  const closeRMModal = () => {
    setRMToShow({
      rawMaterials: {},
      open: false,
    });
  };
  const subtractReserved = (data) => {
    if (!data?.quantity) return 0;
    const diff = data?.quantity - (data?.reserved || 0);
    return (diff <= 0 ? 0 : diff) || 0;
  };

  const getInStock = (record) => {
    let all = [...allParts, ...allProducts];
    let item = all?.find(
      (elem) => elem?.value === (record?._id || record?.value)
    );
    return {
      inStockToReserved: `${subtractReserved(item?.part) || 0}\xA0(${
        item?.quantity?.toFixed(2) || 0
      },\xA0${item?.reserved?.toFixed(2) || 0})`,
      inStock: item?.quantity?.toFixed(2) || 0,
    };
  };

  const bomColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => <span>{record?.name}</span>,
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (_, record) => <span>{record?.rowCategory?.toUpperCase()}</span>,
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
      render: (_, record) => <span>{record?.units}</span>,
    },
    {
      title: 'In Stock',
      dataIndex: 'inStock',
      key: 'inStock',
      render: (_, record) => (
        <span>{getInStock(record)?.inStockToReserved}</span>
      ),
    },
    {
      title: 'Required Stock',
      dataIndex: 'requiredStock',
      key: 'requiredStock',
      render: (_, record) => (
        <span>
          {Math.max(
            0,
            parseInt(record?.units) *
              (orderQuantity - getInStock(record)?.inStock)
          )}
        </span>
      ),
    },
    {
      title: '',
      dataIndex: 'rm',
      key: 'rm',
      render: (_, record) => (
        <span
          className={`${record?.rawMaterials?.length > 0 ? 'text-blue-500 underline cursor-pointer hover:text-blue-300' : 'text-gray-400'}`}
          onClick={() => {
            if (record?.rawMaterials?.length > 0) {
              setRMToShow({
                rawMaterials: record?.rawMaterials,
                orderQuantity: record?.units,
                open: true,
              });
            }
          }}
        >
          Show Raw Materials
        </span>
      ),
    },
  ];

  return (
    <>
      <ItemModal
        items={rmToShow?.rawMaterials}
        showModal={rmToShow?.open}
        orderQuantity={rmToShow?.orderQuantity}
        closeModal={closeRMModal}
        allParts={allParts}
        allProducts={allProducts}
        type="rm"
      />
      <Table
        columns={bomColumns}
        dataSource={record?.bom?.item}
        rowKey="_id"
        pagination={false}
        scroll={{ x: 'max-content' }}
        className="rounded-lg"
      />
    </>
  );
};

export default NewBomInfoTable;
