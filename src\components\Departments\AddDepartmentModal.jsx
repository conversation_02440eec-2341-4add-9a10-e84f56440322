import { useContext, useEffect, useState } from 'react';
import * as reactIcons from 'react-icons/bi';
import { IoSaveOutline } from 'react-icons/io5';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import { apiSlice } from '../../slices/apiSlice';
import {
  useAddDepartmentsMutation,
  useUpdateDepartmentsMutation,
} from '../../slices/departmentApiSlice';
import { useGetAllUsersQuery } from '../../slices/userApiSlice';
import { Store } from '../../store/Store';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import Toggle from '../global/components/Toggle';
import IconsModal from '../v3/Icons/IconsModal';

const initialInputData = {
  name: '',
  navs: [],
  deletedNavs: [],
  assignedUsers: [],
  deletedChildNavs: [],
};
const initialNavData = {
  name: '',
  slug: '',
  icon: '',
  isSettingPage: false,
  childNavs: [],
};
const initialChildNavData = '';

const AddDepartmentModal = ({ setOpenModal, editData, setEditData }) => {
  const dispatch = useDispatch();

  const [inputData, setInputData] = useState(initialInputData);
  const [navsData, setNavsData] = useState(initialNavData);
  const [childNavName, setChildNavName] = useState(initialChildNavData);
  const [selectNav, setSelectNav] = useState('');
  const [ShowIconModal, setShowIconModal] = useState(false);
  const [users, setUser] = useState([]);
  const [editChildNav, setEditChildNav] = useState(null);
  const [editChildnavName, setEditChildNavName] = useState('');

  const [addDepartment, { isLoading: isLoadingCreate }] =
    useAddDepartmentsMutation();
  const [editDepartment] = useUpdateDepartmentsMutation();
  const { data: userData, refetch } = useGetAllUsersQuery();
  const { state: user } = useContext(Store);

  useEffect(() => {
    if (userData) {
      const excludedSuperUser = userData.filter(
        (user) => user.role !== 'superuser'
      );
      setUser(excludedSuperUser);
    }

    if (user) {
      setInputData((prev) => ({
        ...prev,
        assignedUsers: [user?.user?._id],
      }));
    }
  }, [userData, user]);

  const navInputHandler = (e) => {
    const { name, value } = e.target;
    const objToSpread = {};

    if (name === 'name') {
      const isSettings = navsData?.isSettingPage || false;
      objToSpread.slug = value
        ? isSettings
          ? `/settings/${value.toLowerCase().replaceAll(' ', '')}`
          : `/${value.toLowerCase().replaceAll(' ', '')}`
        : '';
      objToSpread.childNavs = navsData?.childNavs.map((cn) => ({
        ...cn,
        cslug: isSettings
          ? `/settings/${value.toLowerCase().replaceAll(' ', '')}/${cn.cname
              .toLowerCase()
              .replaceAll(' ', '')}`
          : `/${value.toLowerCase().replaceAll(' ', '')}/${cn.cname
              .toLowerCase()
              .replaceAll(' ', '')}`,
      }));
    }

    if (name === 'isSettingPage') {
      objToSpread.slug = value
        ? `/settings${navsData?.slug}`
        : navsData?.slug?.replace('/settings', '');
      objToSpread.childNavs = navsData?.childNavs.map((cn) => ({
        ...cn,
        cslug: value
          ? `/settings/${value.toLowerCase().replaceAll(' ', '')}/${cn.cname
              .toLowerCase()
              .replaceAll(' ', '')}`
          : `/${value.toLowerCase().replaceAll(' ', '')}/${cn.cname
              .toLowerCase()
              .replaceAll(' ', '')}`,
      }));
    }

    setNavsData((prev) => ({
      ...prev,
      ...objToSpread,
      [name]: value,
    }));
  };

  const handleNavAddClick = () => {
    setInputData((prev) => ({
      ...prev,
      navs: [...(prev?.navs || []), navsData],
    }));
    setNavsData(initialNavData);
    setChildNavName(initialChildNavData);
  };

  const handleNavRemove = async (nav, navdata) => {
    const confirm = await customConfirm(
      'Are you sure you want to remove nav?',
      'delete'
    );
    if (!confirm) {
      return;
    }
    // if the nav has a _id then the nav is stored in the database so we will use this id and delete the nav from database
    if (navdata?._id) {
      setInputData((prev) => ({
        ...prev,
        navs: prev?.navs?.filter((item) => item.name !== nav),
        deletedNavs: [...prev.deletedNavs, navdata?._id],
      }));
    } else {
      setInputData((prev) => ({
        ...prev,
        navs: prev?.navs?.filter((item) => item.name !== nav),
      }));
    }
  };

  const childNavInputHandler = (e) => {
    const { value } = e.target;

    setChildNavName(value);
  };

  const handleChildNavAddClick = () => {
    if (!childNavName) {
      toast.error('Please enter a child nav name');
      return;
    }

    const isAlreadyExists = inputData?.navs?.some((nav) =>
      nav?.childNavs?.some(
        (childNav) =>
          childNav.cname.toLowerCase()?.trim() ===
          childNavName?.toLowerCase()?.trim()
      )
    );

    if (isAlreadyExists) {
      toast.error('Child navigation with this name already exists');
      return;
    }

    if (editChildNav) {
      toast.error('First Complete the Nav Editing');
      return;
    }

    setInputData((prev) => ({
      ...prev,
      navs: prev?.navs?.map((nav) => {
        if (nav.name === selectNav) {
          return {
            ...nav,
            childNavs: [
              ...(nav?.childNavs || []),
              {
                cname: childNavName,
                cslug: `${nav?.slug}/${childNavName
                  .toLowerCase()
                  .replaceAll(' ', '')}`,
              },
            ],
          };
        }
        return nav;
      }),
    }));
    // setNavsData((prev) => ({
    //   ...prev,
    //   childNavs: [...(prev?.childNavs || []), childNavName],
    // }));
    setChildNavName(initialChildNavData);
  };

  const handleChildNavRemove = async (nav, childnav, childnavData) => {
    const confirm = await customConfirm(
      'Are you sure you want to remove child nav?',
      'delete'
    );
    if (!confirm) {
      return;
    }
    if (childnavData?._id) {
      setInputData((prev) => ({
        ...prev,
        navs: prev?.navs?.map((n) => {
          if (n.name === nav) {
            return {
              ...n,
              childNavs: n?.childNavs?.filter((cn) => cn.cslug !== childnav),
            };
          }
          return n;
        }),
        deletedChildNavs: [...prev.deletedChildNavs, childnavData?._id],
      }));
    } else {
      setInputData((prev) => ({
        ...prev,
        navs: prev?.navs?.map((n) => {
          if (n.name === nav) {
            return {
              ...n,
              childNavs: n?.childNavs?.filter((cn) => cn.cslug !== childnav),
            };
          }
          return n;
        }),
      }));
    }
  };

  const handleChildNavToggle = (e, nav, childnav) => {
    setInputData((prev) => ({
      ...prev,
      navs: prev?.navs?.map((n) => {
        if (n.name === nav) {
          return {
            ...n,
            childNavs: n?.childNavs?.map((cn) => {
              if (cn.cslug === childnav) {
                return { ...cn, requiresApproval: e.target.checked };
              }
              return cn;
            }),
          };
        }
        return n;
      }),
    }));
  };

  const handleChildNavName = () => {
    const nav = editChildNav?.navName;
    const childnav = editChildNav?.cNavClug;
    if (!editChildnavName) {
      toast.error('Child nav name is required');
      return;
    }
    const isAlreadyExists = inputData?.navs?.some((nav) =>
      nav?.childNavs?.some(
        (childNav) =>
          childNav.cname.toLowerCase()?.trim() ===
          editChildnavName?.toLowerCase()?.trim()
      )
    );

    if (isAlreadyExists) {
      toast.error('Child navigation with this name already exists');
      return;
    }

    setInputData((prev) => ({
      ...prev,
      navs: prev?.navs?.map((n) => {
        if (n?.name === nav) {
          return {
            ...n,
            childNavs: n?.childNavs?.map((cn) => {
              if (cn?.cslug === childnav) {
                return {
                  ...cn,
                  cname: editChildnavName,
                  cslug: `${n?.slug}/${editChildnavName.toLowerCase().replaceAll(' ', '')}`,
                };
              }
              return cn;
            }),
          };
        }
        return n;
      }),
    }));
    setEditChildNav(null);
    setEditChildNavName('');
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (editData?._id) {
      // edit department
      editDepartment({ data: inputData, id: inputData?._id })
        .unwrap()
        .then(() => {
          refetch();
          toast.success('Department updates successfully');
          dispatch(apiSlice.util.invalidateTags(['Defaults', 'Departments']));
          setOpenModal(false);
        });
    } else {
      addDepartment({ data: inputData })
        .unwrap()
        .then(() => {
          refetch();
          toast.success('Department created successfully');
          dispatch(apiSlice.util.invalidateTags(['Defaults', 'Departments']));
          setOpenModal(false);
        });
    }
  };

  const handleAddIcon = (icon) => {
    const tmp = {
      ...navsData,
      icon,
    };
    setNavsData(tmp);
    setInputData((prev) => ({
      ...prev,
      navs: [...(prev?.navs || []), tmp],
    }));
    setNavsData(initialNavData);
    setChildNavName(initialChildNavData);
  };

  useEffect(() => {
    if (!editData?._id) return;
    // adding deleteNavs and deleteChildNavs Keys here to prevent getting (intermediate value is not iterable) error when add ing the deleted navs or childnavs id in the array
    if (userData) {
      const usersHavingAccess = userData.filter((user) =>
        user?.departments.includes(editData._id)
      );
      const actualUser = usersHavingAccess.filter(
        (currentUser) => currentUser?.role !== 'superuser'
      );

      setInputData({
        ...editData,
        deletedNavs: [],
        deletedChildNavs: [],
        assignedUsers: actualUser.map((user) => user?._id),
      });
    }
  }, [editData, userData]);

  return (
    <>
      <Modal
        title={'Add Department'}
        onCloseModal={() => {
          setOpenModal(false), setEditData({ name: '', navs: [] });
        }}
        pages={['Departments', 'Navs', 'Child\xA0Navs', 'Assign\xA0Users']}
        onAdd={{
          label: 'Add',
          func: [null, handleNavAddClick, handleChildNavAddClick],
          step: [1, 2, 3],
        }}
        onSubmit={handleSubmit}
        btnIsLoading={isLoadingCreate}
      >
        {({ step }) => (
          <>
            {step === 0 ? (
              <>
                <div className="flex flex-col w-full">
                  <label className="mb-1 font-semibold text-[#667085]">
                    Name{' '}
                    <span className="text-xl text-red-500 -mt-2 -ml-1">*</span>
                  </label>
                  <Input
                    value={inputData?.name || ''}
                    onChange={(e) =>
                      setInputData((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                  />
                </div>
              </>
            ) : step === 1 ? (
              <>
                <div className="grid grid-rows-1 gap-x-7 gap-y-3 grid-cols-1 md:grid-cols-2 mb-5">
                  <div className="flex flex-col w-full col-span-full">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Nav Name
                    </label>
                    <Input
                      name="name"
                      value={navsData?.name || ''}
                      onChange={navInputHandler}
                    />
                  </div>
                  <div className="flex flex-col w-full">
                    <Button
                      onClick={() => {
                        setShowIconModal(true);
                      }}
                      className="!bg-transparent !text-black !w-[8rem] !p-0 !mt-3"
                    >
                      + Add Icon
                    </Button>
                  </div>
                  <div className="flex flex-col w-full">
                    <label className="mb-2 font-semibold text-[#667085]">
                      Is Settings
                    </label>
                    <Toggle
                      name="isSettingPage"
                      value={navsData?.isSettingPage || false}
                      onChange={navInputHandler}
                    />
                  </div>
                </div>
                <Table>
                  <Table.Head>
                    <Table.Row>
                      <Table.Th>#</Table.Th>
                      <Table.Th>Name</Table.Th>
                      <Table.Th>Icon</Table.Th>
                      <Table.Th>Is Setting</Table.Th>
                      <Table.Th>Action</Table.Th>
                    </Table.Row>
                  </Table.Head>
                  <Table.Body>
                    {inputData?.navs?.map((nav, nIdx) => (
                      <Table.Row key={nav.slug}>
                        <Table.Td>{nIdx + 1}</Table.Td>
                        <Table.Td>{nav.name}</Table.Td>
                        <Table.Td>
                          <p className="h-6 w-6 [&_*]:w-full [&_*]:h-full">
                            {reactIcons?.[nav.icon]?.()}
                          </p>
                        </Table.Td>
                        <Table.Td>{nav?.isSettingPage ? 'Yes' : 'No'}</Table.Td>
                        <Table.Td>
                          <p
                            className="cursor-pointer hover:underline hover:text-red-500"
                            onClick={() => handleNavRemove(nav.name, nav)}
                          >
                            Remove
                          </p>
                        </Table.Td>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </>
            ) : step == 2 ? (
              <>
                <div className="grid grid-rows-1 gap-x-7 gap-y-3 grid-cols-1 md:grid-cols-2 mb-5">
                  <div className="flex flex-col w-full">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Select Nav
                    </label>
                    <Select
                      name="icon"
                      value={selectNav || ''}
                      onChange={(e) => setSelectNav(e.target.value)}
                      options={inputData?.navs?.map((opt) => ({
                        label: opt.name,
                        value: opt.name,
                      }))}
                    />
                  </div>
                  {selectNav && (
                    <div className="flex flex-col w-full">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Child Name
                      </label>
                      <Input
                        name="cname"
                        value={childNavName || ''}
                        onChange={childNavInputHandler}
                      />
                    </div>
                  )}
                </div>
                {inputData?.navs.map((nav) => (
                  <div key={nav.name} className="mb-3">
                    <p>{nav?.name}</p>
                    <Table>
                      <Table.Head>
                        <Table.Row>
                          <Table.Th>#</Table.Th>
                          <Table.Th>Child&nbsp;Nav</Table.Th>
                          <Table.Th>Requires&nbsp;Approval</Table.Th>
                          <Table.Th>Delete</Table.Th>
                          <Table.Th>Edit</Table.Th>
                        </Table.Row>
                      </Table.Head>
                      <Table.Body>
                        {nav.childNavs?.map((cnav, cIdx) => (
                          <Table.Row key={cnav?.cslug}>
                            <Table.Td>{cIdx + 1}</Table.Td>
                            <Table.Td className={'min-w-44'}>
                              {editChildNav?.cNavClug === cnav?.cslug ? (
                                <div className="flex gap-x-2 items-center">
                                  <Input
                                    type="text"
                                    value={editChildnavName}
                                    onChange={(e) =>
                                      setEditChildNavName(e.target.value)
                                    }
                                  />
                                  <span onClick={handleChildNavName}>
                                    <IoSaveOutline
                                      size={20}
                                      cursor={'pointer'}
                                    />
                                  </span>
                                </div>
                              ) : (
                                cnav.cname
                              )}
                            </Table.Td>
                            <Table.Td>
                              <input
                                type="checkbox"
                                checked={cnav?.requiresApproval || false}
                                onChange={(e) =>
                                  handleChildNavToggle(
                                    e,
                                    nav?.name,
                                    cnav?.cslug
                                  )
                                }
                              />
                            </Table.Td>
                            <Table.Td>
                              <span
                                className="cursor-pointer hover:text-red-500 hover:underline"
                                onClick={() =>
                                  handleChildNavRemove(
                                    nav?.name,
                                    cnav?.cslug,
                                    cnav
                                  )
                                }
                              >
                                Remove
                              </span>
                            </Table.Td>
                            <Table.Td>
                              <p
                                className="cursor-pointer hover:underline hover:text-red-500"
                                onClick={() => {
                                  setEditChildNav({
                                    open: true,
                                    navName: nav?.name,
                                    cNavClug: cnav?.cslug,
                                  });

                                  setEditChildNavName(cnav?.cname);
                                }}
                              >
                                Edit
                              </p>
                            </Table.Td>
                          </Table.Row>
                        ))}
                      </Table.Body>
                    </Table>
                  </div>
                ))}
              </>
            ) : (
              <>
                <MultiSelect
                  options={users?.map((user) => ({
                    label: `${user?.name} (${user?.role})`,
                    value: user?._id,
                  }))}
                  value={inputData?.assignedUsers || []}
                  onChange={(e) => {
                    const newVal = e?.target?.value?.map((vl) => vl?.value);
                    setInputData((prev) => ({
                      ...prev,
                      assignedUsers: newVal,
                    }));
                  }}
                />
              </>
            )}
          </>
        )}
      </Modal>
      {ShowIconModal && (
        <IconsModal
          setShowModal={setShowIconModal}
          onIconModalSubmit={handleAddIcon}
        />
      )}
    </>
  );
};

export default AddDepartmentModal;
