import { useState, useMemo, useContext, useEffect } from 'react';
import { useGetDocumentByTypeQuery } from '../../slices/documentApiSlice';
import Select from './components/Select';
import { Store } from '../../store/Store';
import Button from './components/Button';

function VariantsSelectBox({
  hierarchyIdx,
  variantOptions,
  selectedOptions,
  setSelectedOptions,
}) {
  const options = variantOptions[hierarchyIdx];
  const selectVal = selectedOptions[hierarchyIdx];

  const {
    defaults: {
      defaultParam: { projectDefaults: { variantLabels = [] } = {} } = {},
    } = {},
  } = useContext(Store);

  const handleSelect = (e) => {
    const { value } = e?.target;

    setSelectedOptions((prev) => {
      if (selectVal) {
        return prev?.map((el, idx) => {
          if (idx === hierarchyIdx) return value;
          return el;
        });
      } else {
        return [...prev, value];
      }
    });
  };

  return (
    <>
      <section>
        <label>
          {variantLabels?.[hierarchyIdx]
            ? variantLabels?.[hierarchyIdx]
            : `Variant ${hierarchyIdx}`}
        </label>
        <Select
          className={'mb-3'}
          options={options}
          value={selectVal}
          onChange={handleSelect}
        />
      </section>

      {selectVal && hierarchyIdx < variantOptions?.length - 1 ? (
        <VariantsSelectBox
          hierarchyIdx={hierarchyIdx + 1}
          variantOptions={variantOptions}
          selectedOptions={selectedOptions}
          setSelectedOptions={setSelectedOptions}
        />
      ) : null}
    </>
  );
}

export default function ProductMasterSelector({
  onSelect,
  autoSelect = false,
  hideName = false,
}) {
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [selectedSheet, setSelectedSheet] = useState('');

  const {
    defaults: {
      defaultParam: {
        projectDefaults: { variantLabels = [], enableHorizontalSelector } = {},
      } = {},
    } = {},
  } = useContext(Store);

  const { data: documentData } = useGetDocumentByTypeQuery({ type: 'product' });

  const options = useMemo(() => {
    const options = [];
    if (documentData) {
      for (let i = 0; i < documentData?.sheets.length; i++) {
        const sheet = documentData?.sheets[i];
        for (let ii = 0; ii < sheet?.data.length; ii++) {
          const row = sheet?.data[ii];
          if (row?.Product) {
            options.push({
              label:
                documentData?.sheets?.length > 1
                  ? `${row?.Product} - ${sheet?.name}`
                  : row?.Product,
              value: `${row?.Product}<>${sheet?.id}`,
            });
          }
        }
      }
    }

    return options;
  }, [documentData]);

  const variantOptions = useMemo(() => {
    const variantOptions = [];
    if (documentData?.sheets && documentData?.sheets?.length > 0) {
      const sheet = documentData?.sheets?.find(
        (sheet) => sheet?.id === selectedSheet
      );
      const keys = Object?.keys(sheet?.data?.[0] || {});
      for (let i = 0; i < sheet?.data.length; i++) {
        const row = sheet?.data[i];
        for (let ii = 0; ii < keys.length; ii++) {
          const key = keys[ii];
          if (key?.startsWith('Variant')) {
            const keyIndex = +key?.split('Variant')?.[1];
            if (!variantOptions?.[keyIndex]) {
              variantOptions[keyIndex] = [];
            }
            if (row?.[key])
              variantOptions?.[keyIndex]?.push({
                label: row?.[key],
                value: row?.[key],
              });
          }
        }
      }
    }
    return variantOptions;
  }, [documentData?.sheets, selectedSheet]);

  const selectedProduct =
    selectedOptions?.length === variantOptions?.length
      ? selectedOptions?.join(' ')
      : '';

  useEffect(() => {
    if (autoSelect && selectedProduct && onSelect) {
      onSelect(selectedProduct);
    }
    // eslint-disable-next-line
  }, [autoSelect, selectedProduct]);

  return (
    <div className="w-full">
      <div
        className={`w-full ${enableHorizontalSelector ? 'flex flex-wrap gap-3 items-center ' : ''}`}
      >
        <section>
          {' '}
          <label>{variantLabels?.[0] ? variantLabels?.[0] : 'Parent'}</label>
          <Select
            className={'mb-3'}
            options={options}
            value={
              selectedSheet && selectedOptions?.[0]
                ? `${selectedOptions?.[0]}<>${selectedSheet}`
                : ''
            }
            onChange={(e) => {
              const { value } = e?.target;
              const splitVal = value?.split('<>');
              setSelectedOptions([splitVal[0]]);
              setSelectedSheet(splitVal[1]);
            }}
          />
        </section>

        {selectedSheet && selectedOptions?.[0] && (
          <VariantsSelectBox
            hierarchyIdx={1}
            variantOptions={variantOptions}
            selectedOptions={selectedOptions}
            setSelectedOptions={setSelectedOptions}
          />
        )}
      </div>

      {!hideName && <p> Product Name: {selectedProduct}</p>}
      <div className="flex gap-5">
        {!autoSelect && (
          <Button
            disabled={!selectedProduct}
            onClick={() => onSelect(selectedProduct)}
          >
            Select
          </Button>
        )}
        <Button
          disabled={selectedOptions?.length === 0}
          onClick={() => {
            setSelectedOptions([]);
            setSelectedSheet('');
          }}
          color="red"
        >
          Reset
        </Button>
      </div>
    </div>
  );
}
