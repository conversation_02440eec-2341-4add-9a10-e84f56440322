import { useEffect, useState } from 'react';
import { IoCheckmarkSharp, IoCloseSharp, IoTrashSharp } from 'react-icons/io5';
import { useOutletContext } from 'react-router-dom';
import {
  camelCaseString,
  getIotDeviceData,
  unCamelCaseString,
} from '../../helperFunction';
import { useGetAllDeviceDashboardMachinesQuery } from '../../slices/deviceDashboardMachineApiSlice';
import Table from '../global/components/Table';
import DeviceDashboardCard from './DeviceDashboardCard';

function ShowIcon({ status }) {
  if (status) return <IoCheckmarkSharp className="text-green-600 text-xl" />;
  return <IoCloseSharp className="text-red-600 text-xl" />;
}

function DeviceDashboardAnalytics({ allCustomers }) {
  const [anayticsData, setAnayticsData] = useState({
    activeMachines: 0,
    totalMachines: 0,
    normalMachines: 0,
    needsMaintenance: 0,
    urgentMaintenance: 0,
    table: [],
    totalCustomers: 0,
    activeCustomers: 0,
  });
  const [selectedCard, setSelectedCard] = useState('');

  const { iotDeviceData, referenceDate } = useOutletContext();

  const { data: allMachines } = useGetAllDeviceDashboardMachinesQuery(
    {},
    { refetchOnMountOrArgChange: true }
  );

  useEffect(() => {
    const data = {
      activeMachines: 0,
      totalMachines: 0,
      normalMachines: 0,
      needsMaintenance: 0,
      urgentMaintenance: 0,
      table: [],
      totalCustomers: 0,
      activeCustomers: 0,
    };
    const tempActiveCustomers = [];
    if (allMachines && iotDeviceData && referenceDate && allCustomers) {
      for (let i = 0; i < allMachines?.length; i++) {
        const mac = allMachines?.[i];
        const div = mac?.devices?.[0];
        const cust = allCustomers?.find(
          (item) => item._id === mac?.deviceDashboardCustomerId
        );

        const { isActive, needsMaintenance, isOnline, isOverload } =
          getIotDeviceData(iotDeviceData, div, referenceDate);

        if (isActive) {
          if (!tempActiveCustomers?.includes(cust?._id)) {
            tempActiveCustomers.push(cust?._id);
          }
          data.activeMachines += 1;
        }

        if (needsMaintenance) {
          data.needsMaintenance += 1;
        } else {
          data.normalMachines += 1;
        }

        data.table.push({
          name: mac?.name,
          custName: cust?.name,
          online: isOnline,
          active: isActive,
          needsMaintenance: needsMaintenance,
          overload: isOverload,
        });
      }
    }

    data.totalMachines = allMachines?.length || 0;
    data.totalCustomers = allCustomers?.length || 0;
    data.activeCustomers = tempActiveCustomers?.length || 0;

    setAnayticsData(data);
  }, [iotDeviceData, referenceDate, allMachines, allCustomers]);

  const handleCardClick = (_, val) => {
    setSelectedCard(camelCaseString(val));
  };

  return (
    <div className="mt-8">
      <div className="grid responsive-grid-iot-analytics gap-5">
        {[
          'totalCustomers',
          'activeCustomers',
          'totalMachines',
          'activeMachines',
          'normalMachines',
          'needsMaintenance',
          'urgentMaintenance',
        ].map((item) => (
          <DeviceDashboardCard
            key={item}
            title={unCamelCaseString(item)}
            value={anayticsData?.[item]}
            onClick={handleCardClick}
            className={`cursor-pointer ${item === selectedCard ? 'border-orange-400' : ''}`}
          />
        ))}
        {selectedCard && (
          <DeviceDashboardCard
            title="Clear Filter"
            value={<IoTrashSharp className="mx-auto" />}
            className="cursor-pointer hover:bg-red-500"
            onClick={() => setSelectedCard('')}
          />
        )}
      </div>

      <div className="w-full overflow-x-scroll mt-5">
        <Table>
          <Table.Head>
            <Table.Row>
              <Table.Th>Machibe&nbsp;Name</Table.Th>
              <Table.Th>Customer&nbsp;Name</Table.Th>
              <Table.Th>Online</Table.Th>
              <Table.Th>Active</Table.Th>
              <Table.Th>Needs&nbsp;Maintenance</Table.Th>
              <Table.Th>Is&nbsp;Overload</Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {anayticsData?.table?.map((row, rIdx) => {
              if (selectedCard) {
                if (
                  (selectedCard === 'activeMachines' ||
                    selectedCard === 'activeCustomers') &&
                  !row?.active
                ) {
                  return null;
                } else if (
                  selectedCard === 'normalMachines' &&
                  row?.needsMaintenance
                ) {
                  return null;
                } else if (
                  (selectedCard === 'needsMaintenance' ||
                    selectedCard === 'urgentMaintenance') &&
                  !row?.needsMaintenance
                ) {
                  return null;
                }
              }

              return (
                <Table.Row key={rIdx}>
                  <Table.Td>{row?.name}</Table.Td>
                  <Table.Td>{row?.custName}</Table.Td>
                  <Table.Td>
                    <ShowIcon status={row?.online} />
                  </Table.Td>
                  <Table.Td>
                    <ShowIcon status={row?.active} />
                  </Table.Td>
                  <Table.Td>
                    <ShowIcon status={row?.needsMaintenance} />
                  </Table.Td>
                  <Table.Td>
                    <ShowIcon status={row?.overload} />
                  </Table.Td>
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
      </div>
    </div>
  );
}

export default DeviceDashboardAnalytics;
