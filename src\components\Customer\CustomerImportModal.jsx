import {
  FileExcelOutlined,
  UploadOutlined,
  DownloadOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
} from '@ant-design/icons';
import {
  Button,
  Modal,
  Steps,
  Table,
  Upload,
  Alert,
  Progress,
  Card,
  Badge,
  Typography,
  Space,
  Tooltip,
  Result,
} from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import { generateMultiplePrefixIds } from '../../helperFunction';
import {
  useAddManyCustomerMutation,
  useLazyGetAllcolumnsQuery,
} from '../../slices/customerDataSlice';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice';
import {
  useGetPrefixIdQuery,
  useUpdatePrefixIdMutation,
} from '../../slices/prefixIdApiSlice';
import ImportInstructions from '../global/ImportInstructions';

const { Title, Text } = Typography;

const CustomerImportModal = ({ openModal, setOpenModal }) => {
  const [importing, setImporting] = useState(false);
  const [previewData, setPreviewData] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [importProgress, setImportProgress] = useState(0);
  const [validationErrors, setValidationErrors] = useState([]);
  const [importStats, setImportStats] = useState({
    total: 0,
    valid: 0,
    invalid: 0,
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [fileName, setFileName] = useState('');

  const [addManyCustomers] = useAddManyCustomerMutation();
  const [getAllColumns, { data: columnsData, isLoading: columnsLoading }] =
    useLazyGetAllcolumnsQuery();
  const { data: allPrefixIds } = useGetPrefixIdQuery();
  const [updatePrefixIds] = useUpdatePrefixIdMutation();
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [format, setFormat] = useState({});

  useEffect(() => {
    getAllColumns();
  }, [getAllColumns]);

  useEffect(() => {
    const getCols = async () => {
      const path = '/settings/config/customer';
      getTemplates({ path });
    };
    getCols();
    if (templatesData) {
      const defaultTemplate = templatesData?.find((template) =>
        template.name.startsWith('Default')
      );
      setFormat(allPrefixIds?.customerId?.[defaultTemplate?.idIndex]);
    }
  }, [getTemplates, templatesData, allPrefixIds]);

  const validateCustomerData = (customer, index) => {
    const errors = [];

    if (!customer.name?.trim()) {
      errors.push(`Row ${index + 2}: Name is required`);
    }

    if (!customer.company_name?.trim()) {
      errors.push(`Row ${index + 2}: Company Name is required`);
    }

    if (customer.unique_id.length === 0) {
      errors.push(`Row ${index + 2}: At least one email is required`);
    }

    if (customer.unique_id.length > 5) {
      errors.push(`Row ${index + 2}: Maximum 5 emails allowed`);
    }

    if (customer.phone_no.length > 5) {
      errors.push(`Row ${index + 2}: Maximum 5 phone numbers allowed`);
    }

    if (customer.address.length > 3) {
      errors.push(`Row ${index + 2}: Maximum 3 shipping addresses allowed`);
    }

    if (customer.gstNumber.length > 2) {
      errors.push(`Row ${index + 2}: Maximum 2 GST numbers allowed`);
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    customer.unique_id.forEach((email) => {
      if (email && !emailRegex.test(email)) {
        errors.push(`Row ${index + 2}: Invalid email format "${email}"`);
      }
    });

    // Phone validation
    const phoneRegex = /^[0-9]{10,15}$/;
    customer.phone_no.forEach((phone) => {
      if (phone && !phoneRegex.test(phone.replace(/\s+/g, ''))) {
        errors.push(`Row ${index + 2}: Invalid phone number format "${phone}"`);
      }
    });

    return errors;
  };

  const handleExcelImport = async (file) => {
    setImporting(true);
    setImportProgress(0);
    setValidationErrors([]);
    setFileName(file.name);

    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          setImportProgress(25);
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          if (jsonData.length === 0) {
            throw new Error('Excel file is empty or has no valid data');
          }

          setImportProgress(50);

          const formattedData = jsonData.map((row, index) => {
            const customColumns = {};
            columnsData?.columns?.forEach((col) => {
              customColumns[col.column_name] = row[col.column_name] || '';
            });

            const getArrayFromString = (value, separator) => {
              if (!value) return [];
              return String(value)
                .split(separator)
                .map((item) => item.trim())
                .filter(Boolean);
            };

            return {
              name: row['Name']?.toString().trim() || '',
              company_name: row['Company Name']?.toString().trim() || '',
              unique_id: getArrayFromString(row['Email (max 5)'], ','),
              phone_no: getArrayFromString(row['Phone Numbers (max 5)'], ','),
              address: getArrayFromString(row['Shipping Address (max 3)'], '|'),
              gstNumber: getArrayFromString(row['GST Numbers (max 2)'], ','),
              billingAddress: getArrayFromString(row['Billing Address'], '|'),
              paymentTerm: row['Payment Term']?.toString().trim() || '',
              customColumns,
              rowIndex: index + 1,
            };
          });

          setImportProgress(75);

          // Validate all data
          const allErrors = [];
          const validData = [];

          formattedData.forEach((customer, index) => {
            const errors = validateCustomerData(customer, index);
            if (errors.length > 0) {
              allErrors.push(...errors);
            } else {
              validData.push(customer);
            }
          });

          setValidationErrors(allErrors);
          setImportStats({
            total: formattedData.length,
            valid: validData.length,
            invalid: formattedData.length - validData.length,
          });

          setPreviewData(validData);
          setImportProgress(100);
          setCurrentStep(1);

          if (allErrors.length > 0) {
            toast.warning(
              `${allErrors.length} validation errors found. Please review before importing.`
            );
          } else {
            toast.success('All data validated successfully!');
          }
        } catch (error) {
          toast.error(error.message || 'Failed to process Excel file');
          setValidationErrors([
            error.message || 'Failed to process Excel file',
          ]);
        } finally {
          setImporting(false);
        }
      };

      reader.onerror = () => {
        setImporting(false);
        toast.error('Failed to read the file');
      };

      reader.readAsArrayBuffer(file);
    } catch (error) {
      setImporting(false);
      toast.error('Failed to import file');
    }

    return false;
  };

  const handleConfirmImport = async () => {
    if (previewData.length === 0) {
      toast.warning('No valid data to import');
      return;
    }

    setIsProcessing(true);
    try {
      const { ids: prefixIds, latestIncrementedValue } =
        generateMultiplePrefixIds({
          format,
          length: previewData.length,
        });

      const data = previewData.map((customer, index) => ({
        ...customer,
        id: prefixIds[index],
      }));

      const res = await addManyCustomers({ data }).unwrap();

      if (res?.success === false) {
        throw new Error(res?.message || 'Failed to import customers');
      }

      if (res?.success) {
        const updatedPrefixIds = { ...allPrefixIds };
        const CustomerIdObject = { ...updatedPrefixIds.customerId[0] };

        Object.keys(CustomerIdObject).forEach((key) => {
          if (key.startsWith('Increment_')) {
            CustomerIdObject[key] = latestIncrementedValue + 1;
          }
        });

        const updatedCustomerId = allPrefixIds.customerId.map((item, index) => {
          if (index === 0) {
            return { ...item, ...CustomerIdObject };
          }
          return item;
        });

        await updatePrefixIds({
          data: {
            ...updatedPrefixIds,
            customerId: updatedCustomerId,
          },
        });

        toast.success(res?.message || 'Customers imported successfully!');
        setCurrentStep(2);
      }
    } catch (error) {
      toast.error(error.message || 'Failed to import customers');
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadTemplate = () => {
    try {
      const template = [
        {
          Name: 'John Doe',
          'Company Name': 'ABC Corp',
          'Email (max 5)': '<EMAIL>, <EMAIL>',
          'Phone Numbers (max 5)': '9876543210, 9876543211',
          'Shipping Address (max 3)': '123 Main St, Mumbai|456 Side St, Delhi',
          'GST Numbers (max 2)': '27AAPFU0939F1ZV, 29AAPFU0939F1ZV',
          'Billing Address': '789 Billing St, Chennai',
          'Payment Term': 'Net 30',
          ...columnsData?.columns?.reduce(
            (acc, col) => ({
              ...acc,
              [col.column_name]: 'Sample Value',
            }),
            {}
          ),
        },
      ];

      const ws = XLSX.utils.json_to_sheet(template);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Template');

      // Auto-size columns
      const range = XLSX.utils.decode_range(ws['!ref']);
      for (let C = range.s.c; C <= range.e.c; ++C) {
        let max = 0;
        for (let R = range.s.r; R <= range.e.r; ++R) {
          const cell = ws[XLSX.utils.encode_cell({ c: C, r: R })];
          if (cell && cell.v) {
            const cellLength = cell.v.toString().length;
            if (cellLength > max) max = cellLength;
          }
        }
        ws['!cols'] = ws['!cols'] || [];
        ws['!cols'][C] = { wch: Math.max(max + 2, 10) };
      }

      XLSX.writeFile(wb, 'customer_import_template.xlsx');
      toast.success('Template downloaded successfully!');
    } catch (error) {
      toast.error('Failed to download template');
    }
  };

  const resetModal = () => {
    setCurrentStep(0);
    setPreviewData([]);
    setValidationErrors([]);
    setImportStats({ total: 0, valid: 0, invalid: 0 });
    setImportProgress(0);
    setFileName('');
  };

  const handleModalClose = () => {
    resetModal();
    setOpenModal(false);
  };

  const previewColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      ellipsis: true,
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: 'Company',
      dataIndex: 'company_name',
      key: 'company_name',
      width: 150,
      ellipsis: true,
    },
    {
      title: 'Emails',
      dataIndex: 'unique_id',
      key: 'unique_id',
      width: 200,
      render: (emails) => (
        <Tooltip title={emails.join(', ')}>
          <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Badge count={emails.length} color="blue" />
            <Text ellipsis style={{ maxWidth: 150 }}>
              {emails.join(', ')}
            </Text>
          </span>
        </Tooltip>
      ),
    },
    {
      title: 'Phone Numbers',
      dataIndex: 'phone_no',
      key: 'phone_no',
      width: 200,
      render: (phones) => (
        <Tooltip title={phones.join(', ')}>
          <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Badge count={phones.length} color="green" />
            <Text ellipsis style={{ maxWidth: 150 }}>
              {phones.join(', ')}
            </Text>
          </span>
        </Tooltip>
      ),
    },
    {
      title: 'Shipping Addresses',
      dataIndex: 'address',
      key: 'address',
      width: 250,
      render: (addresses) => (
        <Tooltip title={addresses.join(' | ')}>
          <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Badge count={addresses.length} color="orange" />
            <Text ellipsis style={{ maxWidth: 200 }}>
              {addresses.join(' | ')}
            </Text>
          </span>
        </Tooltip>
      ),
    },
    {
      title: 'GST Numbers',
      dataIndex: 'gstNumber',
      key: 'gstNumber',
      width: 200,
      render: (gstNumbers) => (
        <Tooltip title={gstNumbers.join(', ')}>
          <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Badge count={gstNumbers.length} color="purple" />
            <Text ellipsis style={{ maxWidth: 150 }}>
              {gstNumbers.join(', ')}
            </Text>
          </span>
        </Tooltip>
      ),
    },
    {
      title: 'Payment Term',
      dataIndex: 'paymentTerm',
      key: 'paymentTerm',
      width: 150,
      ellipsis: true,
    },
    ...(columnsData?.columns?.map((col) => ({
      title: col.column_name,
      dataIndex: ['customColumns', col.column_name],
      key: col.column_name,
      width: 150,
      ellipsis: true,
    })) || []),
  ];

  return (
    <Modal
      title={
        <Space>
          <FileExcelOutlined />
          <span>Import Customers</span>
        </Space>
      }
      open={openModal}
      onCancel={handleModalClose}
      width={1200}
      centered
      styles={{
        body: {
          maxHeight: `calc(100vh - 150px)`,
          overflowY: 'auto',
        },
      }}
      footer={null}
      maskClosable={false}
    >
      <Steps
        current={currentStep}
        items={[
          { title: 'Upload' },
          { title: 'Preview' },
          { title: 'Complete' },
        ]}
        className="my-6 px-4"
      />
      {/* Step 1: Upload */}
      {currentStep === 0 && (
        <div className="space-y-6">
          {/* Instructions Card */}
          <ImportInstructions />

          {/* Format Guidelines */}
          <Card>
            <Title level={5}>Data Format Guidelines</Title>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Text strong>Separators:</Text>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>
                    • <Badge color="blue">Comma (,)</Badge> for emails, phones,
                    GST numbers
                  </li>
                  <li>
                    • <Badge color="green">Pipe (|)</Badge> for addresses
                  </li>
                </ul>
              </div>
              <div>
                <Text strong>Limits:</Text>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• Maximum 5 emails per customer</li>
                  <li>• Maximum 5 phone numbers per customer</li>
                  <li>• Maximum 3 shipping addresses per customer</li>
                  <li>• Maximum 2 GST numbers per customer</li>
                </ul>
              </div>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadTemplate}
              size="large"
              className="flex-1"
              loading={columnsLoading}
            >
              Download Template
            </Button>

            <Upload
              beforeUpload={handleExcelImport}
              accept=".xlsx,.xls"
              showUploadList={false}
              disabled={importing}
            >
              <Button
                icon={<UploadOutlined />}
                type="primary"
                size="large"
                className="flex-1"
                loading={importing}
              >
                {importing ? 'Processing...' : 'Upload Excel File'}
              </Button>
            </Upload>
          </div>

          {/* Progress Bar */}
          {importing && (
            <div className="mt-4">
              <Progress percent={importProgress} status="active" />
              <Text className="text-sm text-gray-500 mt-2">
                Processing {fileName}...
              </Text>
            </div>
          )}
        </div>
      )}

      {/* Step 2: Preview */}
      {currentStep === 1 && (
        <div className="space-y-4">
          {/* Import Statistics */}
          <Card>
            <Title level={5}>Import Summary</Title>

            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {importStats.valid}
                </div>
                <Text className="text-green-600">Valid Records</Text>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {importStats.invalid}
                </div>
                <Text className="text-red-600">Invalid Records</Text>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {importStats.total}
                </div>
                <Text className="text-blue-600">Total Records</Text>
              </div>
            </div>
          </Card>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <Alert
              type="warning"
              showIcon
              message={`${validationErrors.length} Validation Errors Found`}
              description={
                <div className="max-h-40 overflow-y-auto">
                  <ul className="mt-2 space-y-1">
                    {validationErrors.slice(0, 10).map((error, index) => (
                      <li key={index} className="text-sm">
                        {error}
                      </li>
                    ))}
                    {validationErrors.length > 10 && (
                      <li className="text-sm text-gray-500">
                        ... and {validationErrors.length - 10} more errors
                      </li>
                    )}
                  </ul>
                </div>
              }
              className="mb-4"
            />
          )}

          {/* Data Preview */}
          <Card>
            <div className="flex justify-between items-center mb-4">
              <Title level={5}>
                Data Preview ({previewData.length} records)
              </Title>
              <Text type="secondary">Only valid records will be imported</Text>
            </div>
            <Table
              columns={previewColumns}
              dataSource={previewData}
              scroll={{ x: 1200, y: 400 }}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} items`,
              }}
              size="small"
              bordered
            />
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => setCurrentStep(0)}
              size="large"
            >
              Back to Upload
            </Button>
            <Button
              type="primary"
              icon={<ArrowRightOutlined />}
              onClick={handleConfirmImport}
              loading={isProcessing}
              disabled={previewData.length === 0}
              size="large"
            >
              {isProcessing
                ? 'Importing...'
                : `Import ${previewData.length} Records`}
            </Button>
          </div>
        </div>
      )}

      {/* Step 3: Complete */}
      {currentStep === 2 && (
        <Result
          status="success"
          title="Import Completed Successfully!"
          subTitle={`Successfully imported ${previewData.length} customer records.`}
          extra={[
            <Button type="primary" key="close" onClick={handleModalClose}>
              Close
            </Button>,
            <Button key="import-more" onClick={resetModal}>
              Import More
            </Button>,
          ]}
        />
      )}
    </Modal>
  );
};

export default CustomerImportModal;
