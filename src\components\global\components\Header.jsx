import { HomeOutlined } from '@ant-design/icons';
// import { Breadcrumb } from 'antd';
import { useContext, useEffect, useState } from 'react';
import { IoHomeOutline } from 'react-icons/io5';
import { Link, useLocation } from 'react-router-dom';
import { Store } from '../../../store/Store';
import NavigationDropdown from '../../NavigationDropdown';
import { InfoPopup } from './InfoPopup';
import Navigation from './Navigation';
import SlidePanel from './SlidePanel';

const Header = ({
  title,
  description,
  hasInfoPopup = true,
  infoTitle,
  infoDesc,
  paras,
  className = '',
}) => {
  const [currentModule, setCurrentModule] = useState(null);
  const { defaults } = useContext(Store);
  const location = useLocation();
  const [isNavOpen, setIsNavOpen] = useState(false);

  useEffect(() => {
    if (defaults?.departments) {
      const allModules = defaults.departments.flatMap(
        (dep) =>
          dep.navs?.map((nav) => ({
            ...nav,
            subModules:
              nav.childNavs?.map((child) => ({
                name: child.cname,
                slug: child.cslug,
                icon: child.icon,
              })) || [],
          })) || []
      );

      const currentModuleData = allModules.find((module) =>
        module.subModules.some((subModule) =>
          location.pathname.startsWith(subModule.slug)
        )
      );

      setCurrentModule(currentModuleData);
    }
  }, [defaults?.departments, location]);

  const breadcrumbItems = [
    {
      title: (
        <Link to="/">
          <HomeOutlined />
        </Link>
      ),
    },
  ];

  if (currentModule) {
    breadcrumbItems.push({
      title: currentModule.name,
      menu: {
        items: currentModule.subModules.map((subModule, index) => ({
          key: index.toString(),
          label: <Link to={subModule.slug}>{subModule.name}</Link>,
        })),
      },
    });

    const currentSubModule = currentModule.subModules.find((sm) =>
      location.pathname.startsWith(sm.slug)
    );
    if (currentSubModule) {
      breadcrumbItems.push({
        title: currentSubModule.name,
      });
    }
  }

  return (
    <div className={`w-fit mb-1 ${className}`}>
      {/* <Breadcrumb className={'text-xs md:text-sm'} items={breadcrumbItems} /> */}
      <SlidePanel isOpen={isNavOpen} onClose={() => setIsNavOpen(false)}>
        <Navigation
          modules={defaults?.departments}
          currentPath={location.pathname}
        />
      </SlidePanel>

      <div className="flex gap-[5px] items-center">
        <button
          onClick={() => setIsNavOpen(true)}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded-full"
        >
          <IoHomeOutline className="w-5 h-5" />
        </button>
        <div className="flex gap-[5px] items-center">
          <p className="!font-semibold !xs:text-2xl !sm:text-xl !md:text-2xl lg:text-2xl">
            {title}
          </p>
          {hasInfoPopup && (
            <InfoPopup title={infoTitle} description={infoDesc}>
              {paras?.map((para, pIdx) => (
                <p key={pIdx} className="mb-3">
                  {para}
                </p>
              ))}
            </InfoPopup>
          )}
          <NavigationDropdown />
        </div>
      </div>
      <p className="text-[#676464] text-sm">{description}</p>
    </div>
  );
};

export default Header;
