import { Button, DatePicker, Select, Space } from 'antd';
import dayjs from 'dayjs';
import { RotateCcw } from 'lucide-react';
import { useEffect, useState } from 'react';

const { RangePicker } = DatePicker;

/**
 * @param {Object} props
 * @param {Function} props.onFilterChange - Callback when filter changes with {timePeriod, dateRange} as parameters
 * @param {Object} props.initialValues - Initial values for the filter {timePeriod, dateRange}
 * @param {Array} props.timeOptions - Custom time period options
 * @param {boolean} props.showReset - Whether to show reset button
 * @param {string} props.className - Additional class names
 */
const TimeRangeFilter = ({
  onFilterChange,
  initialValues = { timePeriod: 'All Time', dateRange: null },
  timeOptions,
  showReset = true,
  className = '',
}) => {
  const [filters, setFilters] = useState({
    timePeriod: initialValues.timePeriod || 'All Time',
    dateRange: initialValues.dateRange || null,
  });

  const defaultTimeOptions = [
    { value: 'All Time', label: 'All Time' },
    { value: 'Today', label: 'Today' },
    { value: 'Yesterday', label: 'Yesterday' },
    { value: 'Last 7 Days', label: 'Last 7 Days' },
    { value: 'Last 30 Days', label: 'Last 30 Days' },
    { value: 'Custom', label: 'Custom' },
  ];

  const options = timeOptions || defaultTimeOptions;

  useEffect(() => {
    let calculatedDateRange = null;

    switch (filters.timePeriod) {
      case 'Today':
        calculatedDateRange = [dayjs().startOf('day'), dayjs().endOf('day')];
        break;
      case 'Yesterday':
        calculatedDateRange = [
          dayjs().subtract(1, 'day').startOf('day'),
          dayjs().subtract(1, 'day').endOf('day'),
        ];
        break;
      case 'Last 7 Days':
        calculatedDateRange = [
          dayjs().subtract(6, 'day').startOf('day'),
          dayjs().endOf('day'),
        ];
        break;
      case 'Last 30 Days':
        calculatedDateRange = [
          dayjs().subtract(29, 'day').startOf('day'),
          dayjs().endOf('day'),
        ];
        break;
      case 'Custom':
        calculatedDateRange = filters.dateRange;
        break;
      default:
        calculatedDateRange = null;
    }
    let formattedDateRange = null;
    if (
      calculatedDateRange &&
      calculatedDateRange.length === 2 &&
      calculatedDateRange[0] &&
      calculatedDateRange[1]
    ) {
      formattedDateRange = [
        calculatedDateRange[0].format('DD-MM-YYYY'),
        calculatedDateRange[1].format('DD-MM-YYYY'),
      ];
    }

    if (onFilterChange) {
      onFilterChange({
        timePeriod: filters.timePeriod,
        dateRange: formattedDateRange,
      });
    }
  }, [filters, onFilterChange]);

  const handleTimePeriodChange = (value) => {
    setFilters((prev) => ({
      ...prev,
      timePeriod: value,
      // Clear date range if not custom
      dateRange: value === 'Custom' ? prev.dateRange : null,
    }));
  };

  const handleDateRangeChange = (dates) => {
    setFilters((prev) => ({
      ...prev,
      dateRange: dates,
    }));
  };

  const resetFilters = () => {
    setFilters({
      timePeriod: 'All Time',
      dateRange: null,
    });
  };

  return (
    <Space className={`time-range-filter ${className}`} size="small" wrap>
      <Select
        value={filters.timePeriod}
        onChange={handleTimePeriodChange}
        options={options}
        style={{ minWidth: 120 }}
      />

      {filters.timePeriod === 'Custom' && (
        <RangePicker
          value={filters.dateRange}
          onChange={handleDateRangeChange}
          allowClear={true}
        />
      )}

      {showReset && (
        <Button
          onClick={resetFilters}
          type="text"
          icon={<RotateCcw size={14} />}
          className="text-gray-500 hover:text-blue-600"
        >
          Reset
        </Button>
      )}
    </Space>
  );
};

export default TimeRangeFilter;
