import Table from '../global/components/Table';

const AdditionalFieldPreview = ({ data }) => {
  const renderField = (key, value, type, completeValue) => {
    switch (type) {
      case 'Date':
        return (
          <input
            className="p-1 border border-gray-300 rounded mt-2"
            type="date"
            value={value}
            disabled
          />
        );
      case 'Check':
        return (
          <input
            className="mr-1 h-4 w-4 border border-gray-300 rounded mt-2"
            type="checkbox"
            checked={value}
            disabled
          />
        );
      case 'String':
        return (
          <input
            className="p-1 border border-gray-300 rounded mt-2"
            type="text"
            value={value}
            disabled
          />
        );
      case 'MultiCheckbox':
        return (
          <div className="flex flex-wrap">
            {Object.entries(completeValue)?.map(([optionKey, optionValue]) => {
              if (optionKey === 'type') return null;
              return (
                <label
                  key={optionKey}
                  className="flex items-center mr-2 mb-2 mt-2"
                >
                  <input
                    className="mr-1 h-4 w-4 border border-gray-300 rounded"
                    type="checkbox"
                    checked={optionValue}
                    disabled
                  />
                  {optionKey}
                </label>
              );
            })}
          </div>
        );
      case 'Table':
        return (
          <div className="w-full mt-2">
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Th className={`text-center`}>Sr. No.</Table.Th>
                  {completeValue?.columns?.map((col, colIndex) => (
                    <Table.Th key={colIndex} className={`text-center`}>
                      {col?.columnName}
                    </Table.Th>
                  ))}
                </Table.Row>
              </Table.Head>
              <Table.Body>
                {Object?.keys(completeValue?.rowData)?.map((row, rowIndex) => (
                  <Table.Row
                    key={rowIndex}
                    className={`text-center overflow-auto`}
                  >
                    <Table.Td className={`text-center`}>
                      {rowIndex + 1}
                    </Table.Td>
                    {completeValue?.rowData?.[row]?.map((item, colIndex) => (
                      <Table.Td key={colIndex} className={`text-center`}>
                        {item?.value}
                      </Table.Td>
                    ))}
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full mt-6">
      {Object.entries(data)?.map(([key, value]) => (
        <div key={key} className="flex items-center w-full">
          <label className="w-[30%]  mr-2">{key}:</label>
          {renderField(key, value.value || null, value.type, value)}
        </div>
      ))}
    </div>
  );
};

export default AdditionalFieldPreview;
