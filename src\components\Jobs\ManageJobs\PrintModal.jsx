import { Modal, Card, Collapse } from 'antd';
import { useGetCreateInputByIDQuery } from '../../../slices/createInputApiSlice';
import Table from '../../global/components/Table';
import { useLazyGetPdfQuery } from '../../../slices/pdfApiSlice';
import JobForm from '../../v3/global/components/JobForm';
import Marquee from 'react-fast-marquee';
import pdf from '../../../assets/images/pdf.png';

import { handlePdf } from '../../../helperFunction';

const PrintModal = ({ setQrData, qrData, isOpenFor, setIsOpenFor }) => {
  const { data: ciData = {}, isLoading } = useGetCreateInputByIDQuery({
    id: qrData?._id,
  });

  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();

  const renderJobInstructions = () => {
    let allData = ciData?.alldata || [];

    return (
      <>
        {allData?.map((_, tabIdx) => {
          return (
            <div className="w-full " key={tabIdx}>
              <JobForm createInput={ciData} />
            </div>
          );
        })}
      </>
    );
  };

  const renderMediaFiles = () => {
    let imageURLs = ciData?.imageURLs;

    let filteredProcesses = [];
    ciData?.productionFlow?.processes?.forEach((pro) => {
      const exists = filteredProcesses?.find((i) => i?._id === pro?.mqtt?._id);
      const isOutSourceProcess = pro?.processCategory === 'Outsource';
      if (!exists && !isOutSourceProcess) {
        filteredProcesses.push(pro?.mqtt);
      }
    });

    return (
      <div className="space-y-4">
        {filteredProcesses.map((process) => {
          return (
            imageURLs?.[process?._id]?.length > 0 && (
              <div>
                <h3 className="text-sm mb-2">{process.process}</h3>
                <div key={process._id} className="flex flex-wrap gap-4">
                  {imageURLs?.[process._id]?.map((el, idX) => (
                    <section
                      key={idX}
                      className="p-2 border rounded-md w-[170px] flex flex-col justify-between"
                    >
                      <section className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer ">
                        <img
                          className="w-full aspect-video object-contain"
                          src={el?.type === 'application/pdf' ? pdf : el?.data}
                          alt=""
                        />
                      </section>
                      <Marquee className="w-full overflow-hidden">
                        {el.name}
                      </Marquee>
                    </section>
                  ))}
                </div>
              </div>
            )
          );
        })}
        {imageURLs?.project?.length > 0 && (
          <div>
            <h3 className="text-sm mb-2">Project</h3>
            <div className="flex flex-wrap gap-4">
              {imageURLs?.project?.map((el, idX) => (
                <section
                  key={idX}
                  className="p-2 border rounded-md w-[170px] flex flex-col justify-between"
                >
                  <section className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer ">
                    <img
                      className="w-full aspect-video object-contain"
                      src={el?.type === 'application/pdf' ? pdf : el?.data}
                      alt=""
                    />
                  </section>
                  <Marquee className="w-full overflow-hidden">
                    {el.name}
                  </Marquee>
                </section>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <Modal
        confirmLoading={isLoading || isFetchingPdf}
        centered={true}
        onOk={() => {
          handlePdf(getPdf, ciData?._id, 'jobSummary');
        }}
        okText={'Print'}
        width={'80%'}
        height={'80%'}
        open={isOpenFor === 'printJob'}
        onCancel={() => {
          setIsOpenFor('');
          setQrData('');
        }}
      >
        <Card
          title={'Job Summary'}
          size="small"
          className="w-full mt-9 h-[65vh] overflow-y-scroll"
        >
          <div className="flex items-center justify-center overflow-y-scroll">
            <div className="flex flex-col w-full">
              <h4 className=" font-medium text-gray-700 my-2 text-center ">
                Model Name: {ciData?.modelName}
              </h4>
              {/* Basic Info */}
              <div className=" w-full pl-2">
                <Table>
                  <Table.Head>
                    <Table.Row>
                      <Table.Th>Job Id</Table.Th>
                      <Table.Th>Work Order Name</Table.Th>
                      <Table.Th>Job No</Table.Th>
                    </Table.Row>
                  </Table.Head>
                  <Table.Body>
                    <Table.Row>
                      <Table.Td>{ciData?.id}</Table.Td>
                      <Table.Td>{ciData?.createPo?.name}</Table.Td>
                      <Table.Td>{ciData?.projectNo}</Table.Td>
                    </Table.Row>
                  </Table.Body>
                </Table>
              </div>
              {/* Process and batch Info */}
              <section className="pl-2">
                <Table>
                  <Table.Head>
                    <Table.Row>
                      <Table.Th>Batch Name</Table.Th>
                      <Table.Th>Batch No</Table.Th>
                      <Table.Th>Batch Size</Table.Th>
                      <Table.Th>Process Name</Table.Th>
                      <Table.Th>Items Per Hour</Table.Th>
                      <Table.Th>Change Over Time</Table.Th>
                    </Table.Row>
                  </Table.Head>
                  <Table.Body>
                    {ciData?.goalsTable?.map((goalData) => {
                      const Processname =
                        ciData?.productionFlow?.processes?.find(
                          (process) => process._id === goalData.flowId
                        );
                      return goalData?.tableData?.map((eachgoalData) => {
                        return (
                          <Table.Row key={eachgoalData?.batchNo}>
                            <Table.Td>{eachgoalData?.batchName}</Table.Td>
                            <Table.Td>{eachgoalData?.batchNo}</Table.Td>
                            <Table.Td>
                              {eachgoalData?.newBatchSize ||
                                eachgoalData?.['Batch Size']}
                            </Table.Td>
                            <Table.Td>{Processname?.processName}</Table.Td>
                            <Table.Td>{eachgoalData?.itemsPerHour}</Table.Td>
                            <Table.Td>{eachgoalData?.changeOverTime}</Table.Td>
                          </Table.Row>
                        );
                      });
                    })}
                  </Table.Body>
                </Table>
              </section>

              {/* Job instructions info/ input screen */}
              <section className="my-2 ml-2 ">
                <div className="">
                  <Collapse
                    className="-z-0"
                    onChange={() => {}}
                    items={[
                      {
                        key: '1',
                        label: 'Job Instructions',
                        children: renderJobInstructions(),
                      },
                      {
                        key: '2',
                        label: 'Attachments',
                        children: renderMediaFiles(),
                      },
                    ]}
                  />
                </div>
              </section>
            </div>
          </div>
        </Card>
      </Modal>
    </>
  );
};

export default PrintModal;
