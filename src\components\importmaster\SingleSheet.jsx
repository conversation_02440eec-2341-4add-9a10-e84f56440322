import { useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  useGetDocumentByIdQuery,
  useUpdateExcelSheetMutation,
} from '../../slices/documentApiSlice';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Pagination from '../global/components/Pagination';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';

const SingleSheet = () => {
  const navigate = useNavigate();
  const { id: sheetId } = useParams();

  const [sheetName, setSheetName] = useState('');
  const [sheetData, setSheetData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [refInputField, setRefInputField] = useState('');
  const [refNameAndIndex, setRefNameAndIndex] = useState({
    name: '',
    index: null,
  });
  const [isFormDirty, setIsFormDirty] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(PAGINATION_LIMIT);

  const tableRef = useRef(null);

  const totalPages = Math.ceil(sheetData?.length / limit);

  const {
    data: docData = {},
    isFetching: isFetchingGet,
    isSuccess: isSuccessGet,
    isLoading: isLoadingGet,
  } = useGetDocumentByIdQuery(
    { id: sheetId },
    { skip: !sheetId, refetchOnMountOrArgChange: true }
  );
  const { document = {} } = docData;

  const isProductMaster = document?.type === 'product';

  const [updateExcelSheet, { isLoading: isLoadingUpdate }] =
    useUpdateExcelSheetMutation();

  useEffect(() => {
    if (document && isSuccessGet && !isFetchingGet) {
      setSheetName(document.name);
      setSheetData(document.data);
      setColumns(Object.keys(document.data[0] || {}));
    }
  }, [document, isFetchingGet, isSuccessGet]);

  const handleEdit = (e, column, dIdx) => {
    // sets the popup to trigger if try to navigate without saving
    setIsFormDirty(true);

    // handle input change
    setSheetData((prev) =>
      prev.map((item, iIdx) => {
        if (iIdx === dIdx) {
          return { ...prev[dIdx], [column]: e.target.value };
        }
        return item;
      })
    );
  };

  const handleAddEntry = () => {
    // adds new entry at end of list

    let tempEntry = {};
    columns.forEach((col) => (tempEntry[col] = ''));
    setSheetData((prev) => [...prev, tempEntry]);
    setPage(totalPages);
    toast.success('Entry added successfully', {
      theme: 'colored',
      position: 'top-right',
      toastId: 'Entry added successfully',
    });
  };

  const updateSheet = async () => {
    // updates the sheet in database

    const res = await updateExcelSheet({
      id: sheetId,
      data: sheetData,
    }).unwrap();
    if (res) {
      // reset popup to be able to navigate without propmt

      setIsFormDirty(false);
      setTimeout(() => {
        navigate('/settings/database/importmasters');
      }, 100);
    }
  };
  const handleAddColumn = () => {
    // Create a copy of the data with a new column in every record
    const newData = sheetData.map((record) => {
      let newColumnName = '';
      if (isProductMaster) {
        const lastColumnName = Object.keys(record).pop()?.split('Variant'); // Get the name of the last column
        // const columnNumber = parseInt(lastColumnName?.[1], 10); // Extract the number
        newColumnName = `Variant${+lastColumnName?.[1] + 1}`;
      } else {
        const lastColumnName = Object.keys(record).pop()?.split(' '); // Get the name of the last column
        // const columnNumber = parseInt(lastColumnName?.[1], 10); // Extract the number
        newColumnName = `${lastColumnName?.[0]} ${+lastColumnName?.[1] + 1}`; // Increment the number
      }
      return { ...record, [newColumnName]: '' }; // Add the new column with an empty string
    });

    setSheetData(newData); // Update the state with the new data
    setColumns(Object.keys(newData[0]));

    setTimeout(() => {
      tableRef.current.scrollLeft = tableRef?.current?.scrollWidth;
    }, 100);
  };

  return (
    <>
      <div className="w-full text-sm relative">
        <div className="w-full flex justify-between gap-x-8 mb-5">
          <Button
            className="!h-7"
            onClick={() =>
              navigate(
                '/settings/create/jobtemplate?selected_tab=import_master'
              )
            }
          >
            Back
          </Button>
          <div className="flex gap-2">
            <Button className="!h-7" onClick={handleAddEntry}>
              Add Entry
            </Button>
            <Button
              className="!h-7"
              isLoading={isLoadingUpdate}
              onClick={updateSheet}
              disabled={!isFormDirty}
              color="green-dark"
            >
              Save Master
            </Button>
            {(sheetName === 'DROPDOWN' ||
              sheetName === 'RELATION' ||
              isProductMaster) && (
              <Button
                className="!h-7"
                color="green-dark"
                onClick={handleAddColumn}
              >
                Add Column
              </Button>
            )}
          </div>
        </div>

        <Input
          type="text"
          disabled={!refNameAndIndex.name}
          value={refInputField}
          className="mb-5 text-lg rounded-xl sticky -top-8 z-30"
          onChange={(e) => {
            setRefInputField(e.target.value);
            handleEdit(e, refNameAndIndex.name, refNameAndIndex.index);
          }}
        />
        {isLoadingGet ? (
          <Spinner />
        ) : (
          <>
            <div ref={tableRef} className="w-full h-3/4 overflow-x-scroll">
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>#</Table.Th>
                    {columns &&
                      columns.map((column) => (
                        <Table.Th key={column}>{column}</Table.Th>
                      ))}
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {sheetData &&
                    sheetData
                      .slice((page - 1) * limit, page * limit)
                      .map((data, dIdx) => (
                        <Table.Row key={dIdx}>
                          <Table.Td className="min-w-[50px">
                            {sheetData.indexOf(data) + 1}
                          </Table.Td>
                          {columns &&
                            columns.map((column, cIdx) => (
                              <Table.Td key={cIdx}>
                                <input
                                  className="outline-none"
                                  value={data[column]}
                                  onChange={(e) => {
                                    handleEdit(
                                      e,
                                      column,
                                      sheetData.indexOf(data)
                                    );
                                    setRefInputField(e.target.value);
                                  }}
                                  onFocus={() => {
                                    setRefInputField(data[column]);
                                    setRefNameAndIndex({
                                      name: column,
                                      index: sheetData.indexOf(data),
                                    });
                                  }}
                                  title={data[column]}
                                />
                              </Table.Td>
                            ))}
                        </Table.Row>
                      ))}
                </Table.Body>
              </Table>
            </div>
            <Pagination
              page={page}
              limit={limit}
              setPage={setPage}
              setLimit={setLimit}
              totalResults={sheetData?.length}
              totalPages={totalPages}
            />
          </>
        )}
      </div>
    </>
  );
};

export default SingleSheet;
