import React, { useState } from 'react';
import Button from '../../components/global/components/Button';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { MAX_CHAR_ALLOWED } from '../../utils/Constant';
import { getstrLen } from '../../utils/Getstrlen';
import Input from '../global/components/Input';
// import { Store } from '../../store/Store';
import { camelCaseString } from '../../helperFunction';
// import { inventoryTypes } from '../components/v3/InventoryMasters/inventoryConstants';
import { TrashIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { inventoryTypes } from '../../components/v3/InventoryMasters/inventoryConstants';
import { customConfirm } from '../../utils/customConfirm';

export default function AssetDefaults({ defaults, setDefaults }) {
  const [name, setName] = useState('');
  const [updateDefaults, { isLoading: isUpdateDefaultsLoading }] =
    useUpdateDefaultsMutation();

  // console.log("Defaults", defaults)
  const handleInput = (e) => {
    setName(e.target.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (getstrLen(name) > MAX_CHAR_ALLOWED) {
      toast.error(`Column name cannot exceeds ${MAX_CHAR_ALLOWED} characters`, {
        position: 'top-right',
        theme: 'colored',
        toastId: 'name len error',
      });
      return;
    }
    if (name === '') {
      toast.error('Column name cannot be empty');
      return;
    }
    const exist = defaults?.assetColumn?.find((item) => {
      return item.title.toLowerCase() === name.toLowerCase();
    });
    if (exist) {
      toast.warning('Same column cannot be added', {
        position: toast.POSITION.TOP_RIGHT,
      });
      setName('');
      return;
    }

    // console.log("defaults", defaults)

    const data = [
      ...defaults?.assetColumn,
      {
        field: camelCaseString(name),
        title: name,
        isUsed: false,
        // this field is required to differentiate between static and custom columns so that we can show the custom columns in bulk edit
        customColumn: true,
      },
    ];
    const key =
      inventoryTypes.ASSET === 'asset'
        ? 'assetColumn'
        : inventoryTypes.ASSET === 'store'
          ? 'storeColumns'
          : inventoryTypes.ASSET === 'dropdowns'
            ? 'dropdownColumn'
            : inventoryTypes.ASSET === 'vendor'
              ? 'vendorColumns'
              : '';

    await updateDefaults({ [key]: data }).unwrap();
    setName('');

    toast.success('Column added successfully!');
  };

  const handleColDelete = async (index, isUsed) => {
    // console.log("isUsed", isUsed)
    if (isUsed) {
      const userConfirmed = await customConfirm(
        'Are you sure? Data will be deleted! '
      );

      if (userConfirmed) {
        setDefaults((prev) => ({
          ...prev,
          assetColumn: prev.assetColumn.filter((_, idx) => idx !== index),
        }));
      }
    } else {
      const userConfirmed = await customConfirm('Are you sure?');
      if (userConfirmed) {
        setDefaults((prev) => ({
          ...prev,
          assetColumn: prev.assetColumn.filter((_, idx) => idx !== index),
        }));
      }
    }
  };

  // console.log("defaults", defaults?.assetColumn)

  return (
    <>
      <div className="w-full">
        <div className="flex gap-3">
          <Input
            type="text"
            value={name}
            placeholder="Please enter name"
            onChange={handleInput}
            disabled={isUpdateDefaultsLoading}
          />
          <Button onClick={handleSubmit} isLoading={isUpdateDefaultsLoading}>
            Add
          </Button>
        </div>
        <ol className="mt-4">
          {defaults?.assetColumn?.map((val, index) => (
            <React.Fragment key={index}>
              <div className="w-full md:w-[270px] my-1  h-auto flex justify-between">
                <span className="ml-2 text-sm">
                  {index + 1}
                  {'.'}
                </span>
                <li className="text-sm ml-3 w-full">{val.title}</li>
                {val.field !== 'name' && (
                  <TrashIcon
                    className="cursor-pointer text-red-500 hover:fill-red-900 w-6 h-6"
                    onClick={() => handleColDelete(index, val.isUsed)}
                  />
                )}
              </div>
              <hr className="w-[310px]" key={`hr-${index}`} />
            </React.Fragment>
          ))}
        </ol>
      </div>
    </>
  );
}
