import useFieldsAndTitle from '../../../hooks/useFieldsAndTitle';

const CheckBox = ({ data, state, horIdx, verIdx, type }) => {
  const [_horizontalFields, verticalFields, title] = useFieldsAndTitle(data);
  const { inputData, handleChange, renderState } = state;

  return (
    <>
      {renderState && (
        <input
          key={data.Parameter + title[horIdx] + verIdx.toString()}
          id={title[horIdx]}
          type="checkbox"
          className={`w-fit hover:cursor-pointer mx-auto${
            verticalFields.length > 1 && verticalFields.length - 1 !== verIdx
              ? ' mb-2'
              : ''
          }`}
          placeholder={type}
          value={title?.[horIdx] || ''}
          name={data.Parameter}
          onChange={handleChange}
          checked={inputData?.[data.Parameter]?.value?.includes(title[horIdx])}
        />
      )}
    </>
  );
};

export default CheckBox;
