import { useState } from 'react';
import Input from '../global/components/Input';
import MultiSelect from '../global/components/MultiSelect';
import SelectV2 from '../global/components/SelectV2';
import { useGetNestedRowsQuery } from '../../slices/bulkEditApiSlice';
import { ChevronRight, CornerDownRight } from 'lucide-react';
import { Skeleton } from 'antd';

const getOptionsArray = (col, optionsMap) => {
  const { options, fetchOptions, optionsMapType } = col;
  if (options) {
    return options;
  } else if (fetchOptions) {
    if (optionsMapType === '1') {
      return optionsMap[col?.field]?.[0]?.[fetchOptions.select].map((v) => ({
        label: v,
        value: v,
      }));
    }
    return optionsMap[col?.field]?.map((opt) => ({
      label: opt?.[fetchOptions?.select],
      value: opt._id,
    }));
  }
  return [];
};

function RenderField({
  row,
  col,
  options,
  editData,
  setEditData,
  optionsLoading,
}) {
  const inputHandler = (e) => {
    const { value, type } = e.target;
    setEditData((prev) => ({
      ...prev,
      [row?._id]: {
        ...(prev?.[row?._id] || {}),
        [col.field]:
          type === 'number'
            ? +value
            : type === 'multiselect'
              ? value?.map((v) => v?.value)
              : value,
        _id: row?._id,
      },
    }));
  };

  const value = editData?.[row?._id]?.[col?.field] || row?.[col?.field];

  switch (col.type) {
    case 'select':
      return (
        <SelectV2
          menuPosition="fixed"
          menuPlacement="auto"
          options={options}
          value={value}
          onChange={inputHandler}
          isLoading={optionsLoading}
        />
      );
    case 'multiselect':
      return (
        <MultiSelect
          options={options}
          value={value}
          onChange={inputHandler}
          isLoading={optionsLoading}
        />
      );
    case 'text':
    case 'number':
      return <Input value={value} type={col.type} onChange={inputHandler} />;
    case 'readOnly':
      return <p>{value}</p>;
    default:
      return <p>Invalid Type</p>;
  }
}

function TableRow({
  columns,
  row,
  rIdx,
  optionsMap,
  editData,
  setEditData,
  rowModel = 'Part',
  isNested,
  newRows,
  rowsFetching,
  optionsLoading,
}) {
  const [expand, setExpand] = useState(false);
  const { data: allChildrens, isLoading: nestedRowLoading } =
    useGetNestedRowsQuery(
      { model: rowModel, parentId: row?._id },
      {
        skip:
          !rowModel ||
          row?.variantIndex != 0 ||
          row?.children?.length === 0 ||
          !expand,
      }
    );

  const nestedRows = newRows || allChildrens;
  return (
    <>
      <tr className={rowsFetching ? 'animate-pulse' : ''}>
        <td className="border border-gray-300 p-1 px-2">
          <p
            className="flex items-center gap-1"
            style={{ marginLeft: `${8 * row?.variantIndex || 0}px` }}
          >
            {row?.variantIndex > 0 && <CornerDownRight className="h-5 w-5" />}
            {row?.children?.length > 0 ? (
              <ChevronRight
                onClick={() => setExpand((prev) => !prev)}
                className={`w-5 h-5 aspect-square cursor-pointer hover:bg-gray-200 rounded-full ${expand ? 'rotate-90' : ''}`}
              />
            ) : (
              <span className="w-5 aspect-square" />
            )}
            {rIdx + 1}
          </p>
        </td>
        {columns?.map((col) => (
          <td key={col.label} className="border border-gray-300 p-1 px-2">
            <RenderField
              row={row}
              col={col}
              options={getOptionsArray(col, optionsMap)}
              editData={editData}
              setEditData={setEditData}
              optionsLoading={optionsLoading}
            />
          </td>
        ))}
      </tr>
      {expand && (
        <>
          {nestedRowLoading ? (
            <tr>
              <td colSpan={columns?.length + 1}>
                <Skeleton active title={false} />
              </td>
            </tr>
          ) : (
            <>
              {nestedRows
                ?.filter((nRow) => nRow?.parent === row?._id)
                ?.map((nRow, nRIdx) => (
                  <TableRow
                    key={nRow?._id}
                    rIdx={nRIdx}
                    columns={columns}
                    row={nRow}
                    optionsMap={optionsMap}
                    editData={editData}
                    setEditData={setEditData}
                    isNested={isNested}
                    newRows={nestedRows}
                    rowsFetching={rowsFetching}
                    optionsLoading={optionsLoading}
                  />
                ))}
            </>
          )}
        </>
      )}
    </>
  );
}

export default function BulkEditTable({
  columns,
  rows,
  optionsMap,
  editData,
  setEditData,
  isNested,
  rowsLoading,
  rowsFetching,
  optionsLoading,
}) {
  return (
    <div className="overflow-x-auto">
      <table className="table-fixed w-full border border-gray-300">
        <thead>
          <tr className="bg-gray-200 text-left">
            <th className="border border-gray-300 px-4 py-2">#</th>
            {columns?.map((col) => (
              <th key={col.label} className="border border-gray-300 px-4 py-2">
                {col.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rowsLoading ? (
            <tr>
              <td colSpan={columns?.length + 1}>
                <Skeleton active title={false} paragraph={{ rows: 10 }} />
              </td>
            </tr>
          ) : (
            <>
              {rows?.length === 0 && (
                <tr>
                  <td
                    colSpan={columns?.length + 1}
                    className="py-1 text-center"
                  >
                    No items Found
                  </td>
                </tr>
              )}
              {rows?.map((row, rIdx) => (
                <TableRow
                  key={row?._id}
                  rIdx={rIdx}
                  columns={columns}
                  row={row}
                  optionsMap={optionsMap}
                  editData={editData}
                  setEditData={setEditData}
                  isNested={isNested}
                  rowsFetching={rowsFetching}
                  optionsLoading={optionsLoading}
                />
              ))}
            </>
          )}
        </tbody>
      </table>
    </div>
  );
}
