import { TrashIcon } from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { MAX_CHAR_ALLOWED } from '../../utils/Constant';
import { getstrLen } from '../../utils/Getstrlen';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import Input from '../v2/global/components/Input';

export default function BomDefaults({ defaults }) {
  const [name, setName] = useState('');

  const [updateDefaults, { isLoading: isUpdateDefaultsLoading }] =
    useUpdateDefaultsMutation();

  const handleColDelete = async (index, isUsed) => {
    if (isUsed) {
      const userConfirmed = await customConfirm(
        'Are you sure? Data will be deleted!',
        'delete'
      );

      if (userConfirmed) {
        let data = defaults?.bomColumns?.filter((_, i) => i !== index);

        await updateDefaults({ bomColumns: data });

        toast.success('Column deleted successfully');
      }
    } else {
      const userConfirmed = await customConfirm('Are you sure?', 'delete');

      if (userConfirmed) {
        let data = defaults?.bomColumns?.filter((_, i) => i !== index);

        await updateDefaults({ bomColumns: data });

        toast.success('Column deleted successfully');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (getstrLen(name) > MAX_CHAR_ALLOWED) {
      toast.error(`Column name cannot exceeds ${MAX_CHAR_ALLOWED} characters`, {
        position: 'top-right',
        theme: 'colored',
        toastId: 'name len error',
      });
      return;
    }

    if (name === '') {
      toast.error('Column name cannot be empty');
      return;
    }

    const exist = defaults?.bomColumns?.find((item) => {
      return item.title.toLowerCase() === name.toLowerCase();
    });

    if (exist) {
      toast.warning('Same column cannot be added', {
        position: toast.POSITION.TOP_RIGHT,
        toastId: 'warning',
      });
      setName('');
      return;
    }

    const data = [
      ...defaults?.bomColumns,
      {
        field: name.toLowerCase(),
        title: name,
        isUsed: false,
        customColumn: true,
      },
    ];

    await updateDefaults({ bomColumns: data }).unwrap();
    setName('');
    toast.success('Column added successfully!');
  };

  return (
    <div className="w-full">
      <div className="flex gap-3 mb-4">
        <Input
          type="text"
          placeholder="Please enter name"
          onChange={(e) => setName(e.target.value)}
          value={name}
          disabled={isUpdateDefaultsLoading}
        />
        <Button onClick={handleSubmit} isLoading={isUpdateDefaultsLoading}>
          Add
        </Button>
      </div>
      <ol>
        {defaults?.bomColumns?.map((val, index) => (
          <React.Fragment key={index}>
            <div className="w-full md:w-[270px] my-1  h-auto flex justify-between">
              <span className="ml-2 text-sm">
                {index + 1}
                {'.'}
              </span>
              <li className="text-sm ml-3 w-full">{val.title}</li>
              <TrashIcon
                className="cursor-pointer text-red-500 hover:fill-red-900 w-6 h-6"
                onClick={() => handleColDelete(index, val.isUsed)}
              />
            </div>
            <hr className="w-[310px]" key={`hr-${index}`} />
          </React.Fragment>
        ))}
      </ol>
    </div>
  );
}
