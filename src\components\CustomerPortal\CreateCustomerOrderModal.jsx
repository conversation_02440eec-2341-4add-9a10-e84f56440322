import {
  Badge,
  Button,
  Card,
  Divider,
  Form,
  Image,
  Input,
  Modal,
  Space,
  Spin,
  Table,
  Typography,
} from 'antd';
import { Loader2 } from 'lucide-react';
import { MdAdd, MdAttachFile, MdDelete } from 'react-icons/md';
import { useGetDefaultsQuery } from '../../slices/defaultsApiSlice';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice';
import { useGetAllProductsForOptionsQuery } from '../../slices/productApiSlice';
import Select from '../global/components/Select';
import ProductFormatManager from '../ProductFormats/ProductFormatManager';
import UploadButton from '../UploadButton';

const { Title, Text } = Typography;

const CreateCustomerOrderModal = ({
  openModal,
  handleCancel,
  submitHandler,
  isFormLoading,
  form,
  products,
  setProducts,
  formData,
  setFormData,
  attachments,
  attachmentChangeHandler,
  setOpenMediaModal,
  setMediaData,
  input,
  setInput,
  charges,
  setCharges,
  chargesVisibility,
  setChargesVisibility,
  columnVisibility,
  setColumnVisibility,
  displayFormat,
  setDisplayFormat,
}) => {
  const { data: allProducts, isLoading: allProductsLoading } =
    useGetAllProductsForOptionsQuery();
  const { data: dropdowns } = useGetDropdownsQuery();
  const { data: defaults } = useGetDefaultsQuery();
  const projectDefaults = defaults?.defaultParam?.projectDefaults || {};
  const renderBasicFormElements = (elements) => {
    return elements
      .filter((elem) => elem.fieldType !== 'attachment')
      .map((elem) => (
        <Form.Item
          key={elem.id}
          label={<span className="font-medium">{elem.fieldName}</span>}
          className="mb-2"
        >
          {elem.fieldType === 'dropdown' ? (
            <Select
              options={elem.fieldOptions}
              value={formData?.[elem.fieldName]}
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  [elem.fieldName]: e.target.value,
                }));
              }}
              className="w-full rounded border border-gray-300"
            />
          ) : (
            <Input
              type={elem.fieldType === 'number' ? 'number' : 'text'}
              value={formData?.[elem.fieldName]}
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  [elem.fieldName]: e.target.value,
                }));
              }}
              className="rounded border border-gray-300"
            />
          )}
        </Form.Item>
      ));
  };

  const renderAttachments = (elements) => {
    const attachmentFields = elements.filter(
      (elem) => elem.fieldType === 'attachment'
    );
    if (attachmentFields.length === 0) return null;

    return (
      <Card
        title={<span className="font-semibold">Attachments</span>}
        className="mt-6 border border-gray-200"
      >
        <Space direction="vertical" className="w-full p-4">
          {attachmentFields.map((elem) => (
            <div
              key={elem.id}
              className="flex items-center justify-between p-3 bg-gray-50 rounded"
            >
              <Space>
                <MdAttachFile size={20} />
                <Text>{elem.fieldName}</Text>
                {attachments?.[elem.fieldName]?.length > 0 && (
                  <Badge count={attachments[elem.fieldName].length} />
                )}
              </Space>
              <Space>
                {attachments?.[elem.fieldName]?.length > 0 && (
                  <Button
                    type="link"
                    onClick={() => {
                      setOpenMediaModal(true);
                      setMediaData({
                        media: attachments[elem.fieldName],
                        fieldName: elem.fieldName,
                      });
                    }}
                    className="text-blue-600"
                  >
                    Preview Files
                  </Button>
                )}
                <UploadButton
                  type="file"
                  accept={'image/*'}
                  fileType="JPG/PNG"
                  multiple={true}
                  onChange={(e) => attachmentChangeHandler(e, elem.fieldName)}
                  className="border border-blue-500 text-blue-500"
                />
              </Space>
            </div>
          ))}
        </Space>
      </Card>
    );
  };

  const columns = [
    {
      key: '#',
      dataIndex: '#',
      title: '#',
      width: 60,
      render: (_, __, index) => <Text>{index + 1}</Text>,
    },
    {
      key: 'itemName',
      dataIndex: 'itemName',
      title: 'Item Name',
      render: (_, record, index) => (
        <Space size="middle">
          {record?.item &&
            !record?.isManual &&
            allProducts?.find((p) => p.value === record.item)?.thumbNail
              ?.data && (
              <Image
                src={
                  allProducts.find((p) => p.value === record.item).thumbNail
                    ?.data
                }
                alt="Product"
                width={48}
                height={48}
                preview={false}
                className="rounded object-cover"
              />
            )}
          {record?.isManual ? (
            <Input
              className="w-full min-w-[20rem] rounded border border-gray-300"
              placeholder="Enter item name"
              value={record?.manualEntry}
              onChange={(e) => {
                setProducts((prev) => [
                  ...prev?.slice(0, index),
                  {
                    ...prev[index],
                    isManual: true,
                    manualEntry: e.target.value,
                    item: null,
                  },
                  ...prev?.slice(index + 1),
                ]);
              }}
            />
          ) : (
            <Select
              className="w-full min-w-[20rem] rounded border border-gray-300"
              menuPosition="fixed"
              options={[
                { label: '+ Manual Entry', value: 'manualEntry' },
                ...(allProducts?.map((elem) => ({
                  label: elem?.name,
                  value: elem?.value,
                })) || []),
              ]}
              loading={allProductsLoading}
              value={record?.item}
              onChange={(e) => {
                if (e.target.value === 'manualEntry') {
                  setProducts((prev) => [
                    ...prev?.slice(0, index),
                    {
                      ...prev[index],
                      isManual: true,
                      manualEntry: '',
                      item: '',
                      uom: '-',
                    },
                    ...prev?.slice(index + 1),
                  ]);
                } else {
                  const product = allProducts?.find(
                    (elem) => elem?.value === e.target.value
                  );
                  setProducts((prev) => [
                    ...prev?.slice(0, index),
                    {
                      item: e.target.value,
                      uom: product?.uom || '-',
                      isManual: false,
                      manualEntry: null,
                    },
                    ...prev?.slice(index + 1),
                  ]);
                }
              }}
            />
          )}
        </Space>
      ),
      width: 300,
    },
    {
      key: 'orderQuantity',
      dataIndex: 'orderQuantity',
      title: 'Order Quantity',
      width: 150,
      render: (_, record, index) => (
        <Input
          type="number"
          placeholder="Enter quantity"
          value={record?.orderQuantity}
          onChange={(e) => {
            setProducts((prev) => [
              ...prev?.slice(0, index),
              { ...prev?.[index], orderQuantity: e.target.value },
              ...prev?.slice(index + 1),
            ]);
          }}
          className="rounded border border-gray-300"
        />
      ),
    },
    {
      key: 'uom',
      dataIndex: 'uom',
      title: 'UOM',
      width: 100,
      render: (_, record, index) => {
        if (record?.isManual) {
          return (
            <Select
              className="w-full rounded border border-gray-300"
              menuPosition="fixed"
              options={
                dropdowns?.dropdowns
                  ?.find((d) => d.name === 'uom')
                  ?.values?.map((v) => ({ label: v, value: v })) || []
              }
              value={record?.uom}
              onChange={(e) => {
                setProducts((prev) => [
                  ...prev?.slice(0, index),
                  { ...prev[index], uom: e.target.value },
                  ...prev?.slice(index + 1),
                ]);
              }}
            />
          );
        }
        return <span>{record?.uom}</span>;
      },
    },
    {
      key: 'actions',
      dataIndex: 'actions',
      width: 60,
      render: (_, __, index) => (
        <Button
          type="text"
          danger
          icon={<MdDelete size={18} className="text-red-500" />}
          onClick={() =>
            setProducts((prev) => prev?.filter((_, i) => i !== index))
          }
          className="hover:bg-red-50 rounded-full"
        />
      ),
    },
  ];

  return (
    <Modal
      title={<Title level={4}>Create Order</Title>}
      open={openModal}
      onOk={submitHandler}
      okText="Create"
      onCancel={handleCancel}
      width={1000}
      centered
      styles={{
        body: { maxHeight: '70vh', overflowY: 'auto' },
        content: {
          background: '#fff',
          borderRadius: '12px',
        },
      }}
    >
      {isFormLoading ? (
        <div className="flex justify-center py-8">
          <Spin indicator={<Loader2 />} />
        </div>
      ) : (
        <Form layout="vertical" className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            {renderBasicFormElements(form?.form || [])}
          </div>
          {renderAttachments(form?.form || [])}
          <Divider />
          {!projectDefaults?.showProductFormatTable && (
            <div className="flex justify-between items-center mb-4">
              <Title level={5}>Order Items</Title>
              <Button
                type="primary"
                icon={<MdAdd size={18} />}
                onClick={() => setProducts((prev) => [...prev, { item: '' }])}
                className="bg-blue-600 hover:bg-blue-700 border-none rounded flex items-center"
              >
                Add Item
              </Button>
            </div>
          )}
          {projectDefaults?.showProductFormatTable ? (
            <ProductFormatManager
              input={input}
              setInput={setInput}
              charges={charges}
              setCharges={setCharges}
              chargesVisibility={chargesVisibility}
              setChargesVisibility={setChargesVisibility}
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
              displayFormat={displayFormat}
              setDisplayFormat={setDisplayFormat}
            />
          ) : (
            <Table
              columns={columns}
              dataSource={products}
              rowKey="_id"
              pagination={false}
              scroll={{ x: 'max-content' }}
              className="rounded overflow-hidden"
              rowClassName="hover:bg-gray-50"
            />
          )}
        </Form>
      )}
    </Modal>
  );
};

export default CreateCustomerOrderModal;
