import { useEffect, useState } from 'react';
import { FormatDate } from '../../../helperFunction';
import {
  useGetCiForSelectableExportMutation,
  useLazyGetCreateInputByIDQuery,
} from '../../../slices/createInputApiSlice';
import {
  // useLazyGetPoByIdQuery,
  useLazyGetPoByIdForJobQuery,
} from '../../../slices/createPoApiSlice';
import CostingPreview from '../../ManageJobs/CostingPreview';
import JobAssetsTable from '../../ManageJobs/JobAssetsTable';
import Spinner from '../../global/components/Spinner';
import { TabButton, TabContainer } from '../../global/components/TabContainer';
import Table from '../../global/components/Table';
import JobForm from '../../v3/global/components/JobForm';
import AssignedAssemblyBom from '../AssignedAssemblyBom';
import NewBomInfoTable from './NewBomInfoTable';
import ProjectPanel from './ProjectPanel';

const SideBarComponent = ({
  sideBarDataId,
  // projectPanelData,
  // setProjectPanelData,
}) => {
  const [getCiForSelectableExport, { data: ciRes = [] }] =
    useGetCiForSelectableExportMutation();
  const [getJobById] = useLazyGetCreateInputByIDQuery();
  const [sideBarData, setSideBarData] = useState({});
  const [projectPanelData, setProjectPanelData] = useState({});

  const createInput = ciRes[0] || {};

  const [SelectedTab, setSelectedTab] = useState('job planning');

  // const [getPoById, { data: workOrder }] = useLazyGetPoByIdQuery();
  const [getPoForJobById, { data: workOrder }] = useLazyGetPoByIdForJobQuery();

  useEffect(() => {
    if (sideBarData?.po) {
      (async () => {
        // await getPoById({ id: sideBarData?.po?._id }).unwrap();
        await getPoForJobById({ id: sideBarData?.po?._id }).unwrap();
      })();
    }
  }, [sideBarData?.po, getPoForJobById]);

  useEffect(() => {
    if (!sideBarData?.id) return;
    (async () => {
      await getCiForSelectableExport({
        ids: sideBarData?.id,
        query: {
          jobDetails: true,
          data: true,
          qcData: true,
        },
      }).unwrap();
    })();
  }, [sideBarData, getCiForSelectableExport]);

  useEffect(() => {
    const getJob = async () => {
      if (sideBarDataId) {
        let jobData = await getJobById({ id: sideBarDataId }).unwrap();
        if (jobData) {
          let job = {
            data: jobData,
          };
          setProjectPanelData(job);
          setSideBarData({
            jobId: job?.data?.id,
            jobNo: job?.data?.projectNo,
            name: job?.data?.name,
            modelName: job?.data?.modelName, // Keep modelName as it is
            orderIDs: job?.data?.orderIDs,
            id: job?.data?._id,
            po: job?.data?.createPo,
          });
        }
      }
    };
    getJob();
  }, [sideBarDataId, getJobById]);

  return (
    <>
      {sideBarData?.id ? (
        <>
          <h2 className=" font-medium text-gray-700 my-2">
            Model Name: {sideBarData.modelName}
          </h2>
          <div className="mt-4">
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Th>Job Id</Table.Th>
                  <Table.Th>Work Order Name</Table.Th>
                  <Table.Th>Job No</Table.Th>
                  <Table.Th>Name</Table.Th>
                  <Table.Th>Job Size</Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                <Table.Row>
                  <Table.Td>{sideBarData?.jobId}</Table.Td>
                  <Table.Td>{projectPanelData?.data?.createPo?.name}</Table.Td>
                  <Table.Td>{sideBarData.jobNo}</Table.Td>
                  <Table.Td>{sideBarData.name}</Table.Td>
                  <Table.Td>
                    {projectPanelData?.data?.goalsData?.[
                      Object.keys(projectPanelData?.data?.goalsData)[0]
                    ]?.['Order Quantity'] || ''}
                  </Table.Td>
                </Table.Row>
              </Table.Body>
            </Table>
          </div>

          <TabContainer className="!mt-5 w-[90%] md:w-[85%] overflow-x-auto md:overflow-x-hidden">
            <TabButton
              onClick={() => {
                setSelectedTab('job planning');
              }}
              isactive={SelectedTab === 'job planning'}
            >
              Job Planning
            </TabButton>
            <TabButton
              onClick={() => {
                setSelectedTab('job form');
              }}
              isactive={SelectedTab === 'job form'}
            >
              Job Form
            </TabButton>
            <TabButton
              onClick={() => {
                setSelectedTab('bom');
              }}
              isactive={SelectedTab === 'bom'}
            >
              Bom
            </TabButton>
            <TabButton
              onClick={() => {
                setSelectedTab('asset');
              }}
              isactive={SelectedTab === 'asset'}
            >
              Assets
            </TabButton>
            <TabButton
              onClick={() => {
                setSelectedTab('access control');
              }}
              isactive={SelectedTab === 'access control'}
            >
              Access Control
            </TabButton>
            <TabButton
              onClick={() => {
                setSelectedTab('jobCosting');
              }}
              isactive={SelectedTab === 'jobCosting'}
            >
              Job Cost
            </TabButton>
          </TabContainer>
          {SelectedTab === 'bom' && (
            <section className="pt-3">
              {projectPanelData?.data?.workOrderRef === 'WorkOrder' ? (
                <NewBomInfoTable
                  bomItemId={projectPanelData?.data?.bomItemId}
                  items={sideBarData?.po?.items}
                  masterOrderQuantity={sideBarData?.po?.orderQuantity}
                />
              ) : (
                <>
                  {sideBarData?.po?.bom && (
                    <AssignedAssemblyBom po={workOrder} className={'!p-0'} />
                  )}
                </>
              )}
            </section>
          )}
          {SelectedTab === 'job form' && <JobForm createInput={createInput} />}
          {SelectedTab === 'job planning' && (
            <>
              <section>
                <Table>
                  <Table.Head>
                    <Table.Row>
                      <Table.Th>Batch No</Table.Th>
                      <Table.Th>Batch Size</Table.Th>
                      <Table.Th>Process Name</Table.Th>
                      <Table.Th>Items Per Hour</Table.Th>
                      <Table.Th>Change Over Time</Table.Th>
                      <Table.Th>Start Date</Table.Th>
                      <Table.Th>Stop Date</Table.Th>
                    </Table.Row>
                  </Table.Head>
                  <Table.Body>
                    {createInput?.goalsTable?.map((goalData) => {
                      const Processname =
                        createInput?.productionFlow?.processes?.find(
                          (process) => process._id === goalData.flowId
                        );
                      return goalData?.tableData?.map((eachgoalData) => {
                        return (
                          <Table.Row key={eachgoalData?.batchNo}>
                            <Table.Td>{eachgoalData?.batchNo}</Table.Td>
                            <Table.Td>
                              {eachgoalData?.newBatchSize ||
                                eachgoalData?.['Batch Size']}
                            </Table.Td>
                            <Table.Td>{Processname?.processName}</Table.Td>
                            <Table.Td>{eachgoalData?.itemsPerHour}</Table.Td>
                            <Table.Td>{eachgoalData?.changeOverTime}</Table.Td>
                            <Table.Td>
                              {FormatDate(eachgoalData?.startDate)}
                            </Table.Td>
                            <Table.Td>
                              {FormatDate(eachgoalData?.stopDate)}
                            </Table.Td>
                          </Table.Row>
                        );
                      });
                    })}
                  </Table.Body>
                </Table>
              </section>
            </>
          )}
          {SelectedTab === 'asset' && (
            <JobAssetsTable job_id={sideBarData?.id} />
          )}
          {SelectedTab === 'access control' && (
            <ProjectPanel
              data={projectPanelData.data}
              sameModelName={projectPanelData.sameModelName}
              setProjectPanelData={setProjectPanelData}
              // setFilteredData={setFilteredData}
            />
          )}

          {SelectedTab === 'jobCosting' && (
            <CostingPreview createInput={createInput} />
          )}
        </>
      ) : (
        <Spinner />
      )}
    </>
  );
};

export default SideBarComponent;
