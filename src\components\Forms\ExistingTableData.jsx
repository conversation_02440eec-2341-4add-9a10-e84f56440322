import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Divider,
  Input,
  Modal,
  Select,
  Space,
  Typography,
} from 'antd';
import { useEffect } from 'react';
import { toast } from 'react-toastify';

const { Text, Title } = Typography;

const ExistingFormData = ({
  existingFormData,
  setExistingFormData,
  FormCategoryType,
  setFormInfomation,
  setFormInspection,
  setDepartmentFormData,
}) => {
  useEffect(() => {
    // Initialize tableOptions if not present
    if (
      !existingFormData?.tableOptions &&
      existingFormData?.fieldType === 'Table'
    ) {
      setExistingFormData((prev) => ({
        ...prev,
        tableOptions: { column: [], row: [] },
      }));
    }

    // Initialize dropdownOptions if not present
    if (
      !existingFormData?.fieldOptions &&
      (existingFormData?.fieldType === 'DropDown' ||
        existingFormData?.fieldType === 'MultiSelect')
    ) {
      setExistingFormData((prev) => ({
        ...prev,
        fieldOptions: [],
      }));
    }

    // Initialize multiCheckboxOptions if not present
    if (
      !existingFormData?.labelArray &&
      existingFormData?.fieldType === 'MultiCheckbox'
    ) {
      setExistingFormData((prev) => ({
        ...prev,
        labelArray: [],
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [existingFormData]);

  const handleFieldChange = (key, value) => {
    setExistingFormData((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const addColumn = () => {
    const newColumn = { columnName: '', columnType: 'string' };
    handleFieldChange('tableOptions', {
      ...existingFormData.tableOptions,
      column: [...(existingFormData.tableOptions?.column || []), newColumn],
    });
  };

  const addRow = () => {
    handleFieldChange('tableOptions', {
      ...existingFormData.tableOptions,
      row: [...(existingFormData.tableOptions?.row || []), ''],
    });
  };

  const deleteColumn = (index) => {
    const updatedColumns = existingFormData.tableOptions.column.filter(
      (_, idx) => idx !== index
    );
    handleFieldChange('tableOptions', {
      ...existingFormData.tableOptions,
      column: updatedColumns,
    });
  };

  const deleteRow = (index) => {
    const updatedRows = existingFormData.tableOptions.row.filter(
      (_, idx) => idx !== index
    );
    handleFieldChange('tableOptions', {
      ...existingFormData.tableOptions,
      row: updatedRows,
    });
  };
  const handleCancel = () => {
    setExistingFormData(null);
  };

  const saveData = () => {
    const updateFormFields = (prevData) => {
      return prevData.map((field) => {
        if (
          field.fieldType === existingFormData?.fieldType &&
          field.fieldName === existingFormData?.fieldName
        ) {
          switch (field.fieldType) {
            case 'DropDown':
            case 'MultiSelect':
              return {
                ...field,
                fieldOptions: existingFormData.fieldOptions,
                isMandatory: existingFormData.isMandatory,
              };
            case 'Table':
              return {
                ...field,
                tableOptions: {
                  ...existingFormData.tableOptions,
                  columns: existingFormData?.tableOptions?.column?.length,
                  rows: existingFormData?.tableOptions?.row?.length,
                },
                isMandatory: existingFormData.isMandatory,
              };
            case 'MultiCheckbox':
              return {
                ...field,
                labelArray: existingFormData.labelArray,
                isMandatory: existingFormData.isMandatory,
              };
            default:
              return field;
          }
        }
        return field;
      });
    };

    switch (FormCategoryType) {
      case 'QC':
        setFormInfomation((prev) => updateFormFields(prev));
        break;
      case 'INSPECTION':
        setFormInspection((prev) => updateFormFields(prev));
        break;
      case 'DEPARTMENT':
        setDepartmentFormData((prev) => updateFormFields(prev));
        break;
      default:
        toast.error('Invalid FormCategoryType');
    }
    setExistingFormData(null);
  };

  const renderField = (field) => {
    switch (field.fieldType) {
      case 'DropDown':
        return (
          <div className="mb-4">
            <div className="flex items-center gap-3">
              <Title level={5}>Dropdown Configuration</Title>

              <Checkbox
                checked={field.isMandatory}
                onChange={(e) => {
                  handleFieldChange('isMandatory', e.target.checked);
                }}
              >
                <Text strong>Mandatory</Text>
              </Checkbox>
            </div>
            <div className="grid grid-cols-2 gap-2">
              {/* Dropdown Options */}
              {field?.fieldOptions?.map((option, idx) => (
                <div key={idx} className="flex items-center gap-3">
                  <Input
                    value={option.label}
                    onChange={(e) => {
                      const updatedOptions = [...field.fieldOptions];
                      updatedOptions[idx].label = e.target.value;
                      updatedOptions[idx].value = e.target.value; // Sync label and value
                      handleFieldChange('fieldOptions', updatedOptions);
                    }}
                    placeholder="Option Label"
                    style={{ flex: 1 }}
                  />
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => {
                      const updatedOptions = field.fieldOptions.filter(
                        (_, index) => index !== idx
                      );
                      handleFieldChange('fieldOptions', updatedOptions);
                    }}
                  />
                </div>
              ))}
            </div>
            <div className="w-fit mt-2">
              <Button
                type="dashed"
                icon={<PlusOutlined />}
                onClick={() => {
                  const newOption = { label: '', value: '' };
                  handleFieldChange('fieldOptions', [
                    ...field.fieldOptions,
                    newOption,
                  ]);
                }}
                block
              >
                Add Option
              </Button>
            </div>
          </div>
        );

      case 'MultiSelect':
        return (
          <div className="mb-4">
            <div className="flex items-center gap-3">
              <Title level={5}>MultiSelect Configuration</Title>

              <Checkbox
                checked={field.isMandatory}
                onChange={(e) => {
                  handleFieldChange('isMandatory', e.target.checked);
                }}
              >
                <Text strong>Mandatory</Text>
              </Checkbox>
            </div>
            <div className="grid grid-cols-2 gap-2">
              {/* MultiSelect Options */}
              {field?.fieldOptions?.map((option, idx) => (
                <div key={idx} className="flex items-center gap-3">
                  <Input
                    value={option.label}
                    onChange={(e) => {
                      const updatedOptions = [...field.fieldOptions];
                      updatedOptions[idx].label = e.target.value;
                      updatedOptions[idx].value = e.target.value; // Sync label and value
                      handleFieldChange('fieldOptions', updatedOptions);
                    }}
                    placeholder="Option Label"
                    className="w-full"
                  />
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => {
                      const updatedOptions = field.fieldOptions.filter(
                        (_, index) => index !== idx
                      );
                      handleFieldChange('fieldOptions', updatedOptions);
                    }}
                  />
                </div>
              ))}
            </div>
            <div className="w-fit mt-2">
              <Button
                type="dashed"
                icon={<PlusOutlined />}
                onClick={() => {
                  const newOption = { label: '', value: '' };
                  handleFieldChange('fieldOptions', [
                    ...field.fieldOptions,
                    newOption,
                  ]);
                }}
                block
              >
                Add Option
              </Button>
            </div>
          </div>
        );

      case 'MultiCheckbox':
        return (
          <div className="mb-4 ">
            <div className="flex items-center gap-3">
              <Title level={5}>MultiCheckbox Configuration</Title>

              <Checkbox
                checked={field.isMandatory}
                onChange={(e) => {
                  handleFieldChange('isMandatory', e.target.checked);
                }}
              >
                <Text strong>Mandatory</Text>
              </Checkbox>
            </div>
            <div className="grid grid-cols-2 gap-2">
              {/* MultiCheckbox Options */}
              {field.labelArray.map((label, idx) => (
                <div key={idx} className="flex items-center gap-3">
                  <Input
                    value={label}
                    onChange={(e) => {
                      const updatedLabels = [...field.labelArray];
                      updatedLabels[idx] = e.target.value;
                      handleFieldChange('labelArray', updatedLabels);
                    }}
                    placeholder="Checkbox Label"
                    style={{ flex: 1 }}
                  />
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    className="justify-self-start"
                    onClick={() => {
                      const updatedLabels = field.labelArray.filter(
                        (_, index) => index !== idx
                      );
                      handleFieldChange('labelArray', updatedLabels);
                    }}
                  />
                </div>
              ))}
            </div>
            <div className="w-fit mt-2">
              <Button
                type="dashed"
                icon={<PlusOutlined />}
                onClick={() => {
                  const newLabel = '';
                  handleFieldChange('labelArray', [
                    ...field.labelArray,
                    newLabel,
                  ]);
                }}
                block
              >
                Add Checkbox
              </Button>
            </div>
          </div>
        );

      case 'Table':
        return (
          <div className="mb-4">
            {/* isMandatory checkbox  */}
            <div className="flex items-center gap-3">
              <Title level={5}>Table Configuration</Title>

              <Checkbox
                checked={field.isMandatory}
                onChange={(e) => {
                  handleFieldChange('isMandatory', e.target.checked);
                }}
              >
                <Text strong>Mandatory</Text>
              </Checkbox>
            </div>
            <div>
              {/* Table Dimensions */}
              <Space>
                <Text strong>Columns:</Text>
                <Input
                  type="number"
                  value={field?.tableOptions?.column?.length || 0}
                  disabled
                  style={{ width: 100 }}
                />
                <Text strong>Rows:</Text>
                <Input
                  type="number"
                  value={field?.tableOptions?.row?.length || 0}
                  disabled
                  style={{ width: 100 }}
                />
              </Space>

              {/* Column Configuration */}
              <Divider orientation="left">Column Settings</Divider>
              <Space
                direction="vertical"
                size="small"
                style={{ width: '100%' }}
              >
                {field?.tableOptions?.column?.map((col, idx) => (
                  <div
                    key={idx}
                    className="flex items-center gap-3 mb-2"
                    style={{ display: 'flex', alignItems: 'center' }}
                  >
                    <Input
                      placeholder="Column Name"
                      value={col.columnName}
                      onChange={(e) => {
                        const updatedColumns = field?.tableOptions?.column?.map(
                          (column, index) =>
                            index === idx
                              ? {
                                  ...column,
                                  columnName: e.target.value,
                                }
                              : column
                        );
                        handleFieldChange('tableOptions', {
                          ...field.tableOptions,
                          column: updatedColumns,
                        });
                      }}
                      style={{ flex: 1 }}
                    />
                    <Select
                      value={col.columnType}
                      onChange={(e) => {
                        const updatedColumns = field?.tableOptions?.column?.map(
                          (column, index) =>
                            index === idx
                              ? {
                                  ...column,
                                  columnType: e,
                                  nestedColumns: ['Min', 'Max'],
                                }
                              : column
                        );
                        handleFieldChange('tableOptions', {
                          ...field.tableOptions,
                          column: updatedColumns,
                        });
                      }}
                      style={{ width: 150 }}
                    >
                      <Select.Option value="string">String</Select.Option>
                      <Select.Option value="number">Number</Select.Option>
                      <Select.Option value="date">Date</Select.Option>
                      <Select.Option value="Min-Max">Min-Max</Select.Option>
                    </Select>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => deleteColumn(idx)}
                    />
                  </div>
                ))}
                <Button
                  type="dashed"
                  icon={<PlusOutlined />}
                  onClick={addColumn}
                  block
                >
                  Add Column
                </Button>
              </Space>

              {/* Row Configuration */}
              <Divider orientation="left">Row Labels</Divider>
              <Space
                direction="vertical"
                size="small"
                style={{ width: '100%' }}
              >
                {field?.tableOptions?.row?.map((rowLabel, idx) => (
                  <div
                    key={idx}
                    className="flex items-center gap-3 mb-2"
                    style={{ display: 'flex', alignItems: 'center' }}
                  >
                    <Input
                      value={rowLabel}
                      onChange={(e) => {
                        const updatedRows = [...field.tableOptions.row];
                        updatedRows[idx] = e.target.value;
                        handleFieldChange('tableOptions', {
                          ...field.tableOptions,
                          row: updatedRows,
                        });
                      }}
                      style={{ flex: 1 }}
                    />
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => deleteRow(idx)}
                    />
                  </div>
                ))}
                <Button
                  type="dashed"
                  icon={<PlusOutlined />}
                  onClick={addRow}
                  block
                >
                  Add Row
                </Button>
              </Space>
            </div>
          </div>
        );

      default:
        return <Text type="danger">Unsupported field type</Text>;
    }
  };

  if (!existingFormData || Object.keys(existingFormData).length === 0) {
    return <Text type="warning">No data to display</Text>;
  }

  return (
    <Modal
      open={Object.keys(existingFormData).length > 0}
      onCancel={handleCancel}
      centered
      styles={{ body: { maxHeight: 'calc(100vh - 150px)', overflowY: 'auto' } }}
      footer={
        <Button type="primary" onClick={saveData}>
          Save Configuration
        </Button>
      }
    >
      {renderField(existingFormData)}
    </Modal>
  );
};

export default ExistingFormData;
