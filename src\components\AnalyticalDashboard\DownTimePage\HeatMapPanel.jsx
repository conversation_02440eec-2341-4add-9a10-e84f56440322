import { useState, useEffect } from 'react';
import {
  handlePercentDeicmalValue,
  convertToMinsAndSecs,
} from '../../../helperFunction';

const HeatMapPanel = ({ machineId, panelDetails }) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    if (panelDetails) {
      setData([]);
      ['batch', 'downTime', 'upTime'].forEach((param) => {
        const val = panelDetails?.new?.[param];
        const oldVal = panelDetails?.old?.[param];
        let compare = {};

        if (val > oldVal) {
          const diff = val - oldVal;
          const percentage = (diff / val) * 100;

          compare.isGreaterOrEqual = true;
          compare.percentage = handlePercentDeicmalValue(percentage);
        } else if (val < oldVal) {
          const diff = oldVal - val;
          const percentage = (diff / oldVal) * 100;

          compare.isGreaterOrEqual = false;
          compare.percentage = handlePercentDeicmalValue(percentage);
        } else {
          compare.isGreaterOrEqual = false;
          compare.percentage = 0;
        }

        setData((prev) => [
          ...prev,
          {
            param: param.toLowerCase(),
            val,
            compare,
          },
        ]);
      });
    }
  }, [panelDetails]);

  return (
    <div className="w-full bg-white rounded-2xl shadow-low flex justify-between px-8 py-4 mt-5 mb-10">
      {!machineId ? (
        <p className="w-full text-center">Please select machine</p>
      ) : (
        <>
          {data?.map((item, idx) => {
            const check = item.param === 'downtime';

            return (
              <section key={idx} className="w-full flex flex-col items-center">
                <h3 className="font-semibold text-lg">
                  Total
                  <span className="capitalize">{` ${item.param}${
                    item.param.includes('time') ? ' (mins)' : 'es'
                  }`}</span>
                </h3>
                <p className="text-5xl font-bold text-black">
                  {item.param.includes('time')
                    ? convertToMinsAndSecs(item.val)
                    : item.val}
                  {item.compare.percentage ? (
                    <span
                      className={`ml-3 text-lg rounded px-2 py-0.5 ${
                        item.compare.isGreaterOrEqual
                          ? check
                            ? 'bg-[#F28585] text-red-primary'
                            : 'bg-[#b6f285] text-green-600'
                          : check
                          ? 'bg-[#b6f285] text-green-600'
                          : 'bg-[#F28585] text-red-primary'
                      }`}
                    >
                      {`${item.compare.percentage}%`}
                    </span>
                  ) : null}
                </p>
              </section>
            );
          })}
        </>
      )}
    </div>
  );
};
export default HeatMapPanel;