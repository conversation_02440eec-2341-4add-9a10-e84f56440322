import { useEffect, useRef } from 'react';
import { IoCloseSharp, IoPencilSharp } from 'react-icons/io5';
import Marquee from 'react-fast-marquee';
import pdf from '../../../assets/images/pdf.png';

const MediaComp = ({
  item,
  idx,
  onClickFunc,
  removeFileHandler,
  renameFileHandler,
  className = '',
  scrollIntoView = false,
}) => {
  const ref = useRef(null);

  useEffect(() => {
    if (scrollIntoView) {
      ref.current.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center',
      });
    }
  }, [ref, scrollIntoView]);

  return (
    <section
      ref={ref}
      className={`p-2 border rounded-md w-[170px] flex flex-col justify-between relative ${className}`}
    >
      {removeFileHandler && (
        <button
          type="button"
          onClick={() => removeFileHandler(idx, item._id)}
          className="absolute right-1 top-1 bg-red-500 outline-none opacity-75 text-white hover:opacity-100 rounded px-2 py-1"
        >
          <IoCloseSharp className="h-4 w-4" />
        </button>
      )}
      <section
        className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer "
        onClick={() => {
          onClickFunc(item);
        }}
      >
        <img
          className="w-[150px] aspect-video object-contain"
          src={item?.type === 'application/pdf' ? pdf : item?.data}
          alt=""
        />
      </section>

      <section className="flex justify-between items-center text-sm mt-2">
        <Marquee>{item.name}</Marquee>
        {renameFileHandler && (
          <>
            <button
              type="button"
              onClick={() => renameFileHandler({ ...item, index: idx })}
              className="outline-none text-yellow-400 hover:text-white hover:bg-yellow-400 rounded px-2 py-1"
            >
              <IoPencilSharp className="h-4 w-4" />
            </button>
          </>
        )}
      </section>
    </section>
  );
};

export default MediaComp;
