import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

function NewInput({
  type = 'text',
  value,
  onChange,
  placeholder = 'Enter Value',
  required = false,
  disabled = false,
  readOnly = false,
  className,
  // defaultValue = '',
  innerRef,
  inputClassName,
  ...rest
}) {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className={`relative rounded-lg bg-white ${className}`}>
      <input
        type={showPassword ? 'text' : type}
        value={value}
        onChange={onChange}
        required={required}
        placeholder={placeholder}
        // defaultValue={defaultValue}
        disabled={disabled}
        readOnly={readOnly}
        ref={innerRef}
        autoComplete="off"
        // className="w-[249px] px-3 bg-white border border-[#CFD0D7] rounded-[8px] text-[rgba(26, 32, 61, 0.3)] text-sm leading-5 h-[39px] outline-[#005EEC] placeholder:text-[12px]"
        className={`w-full py-2 pl-4 pr-4 bg-transparent relative outline-[#4085ed80] rounded-lg flex justify-between items-center border text-sm text-black ${
          type === 'round' ? 'rounded-[50vh]' : 'rounded-[5px]'
        } ${inputClassName}`}
        {...rest}
      />
      {type === 'password' && (
        <>
          {showPassword ? (
            <EyeIcon
              className="absolute top-1/2 -translate-y-1/2 right-[5%] hover:cursor-pointer text-gray-primary h5 w-5"
              onClick={() => setShowPassword(false)}
            />
          ) : (
            <EyeSlashIcon
              className="absolute top-1/2 -translate-y-1/2 right-[5%] hover:cursor-pointer text-gray-primary h5 w-5"
              onClick={() => setShowPassword(true)}
            />
          )}
        </>
      )}
    </div>
  );
}

export default NewInput;
