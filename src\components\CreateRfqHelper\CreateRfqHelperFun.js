import { toast } from 'react-toastify';
import { generatePrefixId } from '../../helperFunction';

/*
deleteColumn function is used to handle the deletion of a column in a table. It can handle both predefined columns and custom columns. If the column is a predefined column, it directly calls the deleteCustomCol function to delete the column from the database. If the column is a custom column, it removes the column from the local state using the setColumnInputs function.
*/

export const deleteColumn = async ({
  id,
  idx,
  columnInputs,
  setColumnInputs,
  setShowCustomColumnModal,
  deleteCustomCol,
}) => {
  if (id) {
    if (columnInputs?.length === 1) {
      setShowCustomColumnModal(false);
    }
    const deletedCol = await deleteCustomCol({ id });
    if (deletedCol?.data) {
      toast.success('Column Deleted');
    }
  } else {
    const allInputs = [...columnInputs];
    allInputs.splice(idx, 1);
    setColumnInputs(allInputs);
  }
};
/*
 handleSetDate function is used to calculate a new date by adding a specified number of days to the current date. It then updates two state variables: one representing the selected date and another representing the delivery date. This function could be used in scenarios where a user needs to select a date, and the application needs to calculate a corresponding delivery date based on the selected date and a specified number of days.
*/
export const handleSetDate = (e, setDateValue, setDeliveryDate) => {
  const day = +e.target.value;
  const deliveryDate = new Date();
  deliveryDate.setDate(deliveryDate.getDate() + day);
  setDateValue(deliveryDate);
  setDeliveryDate(deliveryDate);
};

/*
The purpose of this function is to extract and format relevant company details, such as name, address, contact number, and GST number, based on the company details and selected company details. The formatted string can be used for displaying or processing the company information in the application.
*/
export const getViewCompanyDetails = (
  companyDetails,
  companySelectedDetails
) => {
  if (!companyDetails || !companySelectedDetails) return '';

  const compAddress = companyDetails.address?.find(
    (i) => i._id === companySelectedDetails.selectedDetails?.address
  );
  const compContact = companyDetails.contactNumber?.find(
    (i) => i._id === companySelectedDetails.selectedDetails?.contact
  );

  return `${companyDetails.name || ''}\n${compAddress?.street || ''} ${compAddress?.city || ''} ${
    compAddress?.state || ''
  } ${compAddress?.postalCode || ''} ${compAddress?.country || ''}\n${
    compContact?.number || ''
  } ${companyDetails.gstNumber || ''}`;
};

/*
the handleHideColumns function toggles the visibility of a column by updating the state variable with a new object that has the same properties as the previous state, except for the property corresponding to the type parameter. The value of this property is flipped (from 
true to false or vice versa), effectively toggling the visibility of the column.
 */
export const handleHideColumns = (type, setProductHideColumnStatus) => {
  setProductHideColumnStatus((prev) => {
    return {
      ...prev,
      [type]: !prev?.[type] || false,
    };
  });
};

/*
similary handleHideCustomColumns function toggles the visibility of a custom column by updating the state variable with a new object that has the same properties as the previous state, except for the property corresponding to the type parameter. The value of this property is flipped (from true to false or vice versa), effectively toggling the visibility of the custom column.
 */
export const handleHideCustomColumns = (
  type,
  setProductHideCustomColumnStatus
) => {
  setProductHideCustomColumnStatus((prev) => {
    return {
      ...prev,
      [type]: !prev?.[type] || false,
    };
  });
};
/*
The handleInputChange function is used to update the state with the new value entered by the user in the input field. It takes three parameters: idx (the index of the item in the state array), val (the new value entered by the user), and setItems (a function to update the state).
 */
export const handleInputChange = (idx, val, setItems) => {
  setItems((prev) => {
    return prev.map((el, ind) => {
      if (ind === idx) {
        return { ...el, manualEntry: val };
      } else {
        return el;
      }
    });
  });
};
/*
The handelpartChange function is used to handle changes in the product selection dropdown. If the user selects the '+' option, it resets the manualEntry field for that item. Otherwise, it updates the item field with the selected value from the data array. The function takes four parameters: e (the event object), idx (the index of the item in the state array), setItems (a function to update the state), and data (the array of available product options).
*/
export const handelpartChange = (e, idx, setItems, data) => {
  if (e.target.value === '+') {
    setItems((prev) =>
      prev.map((item, ind) => {
        if (ind === idx) {
          return { ...item, manualEntry: '' };
        }
        return item;
      })
    );
  } else {
    const selectedItem = data.find((el) => el.value === e.target.value);

    setItems((prev) =>
      prev.map((el, ind) => {
        if (ind === idx) {
          return { item: selectedItem };
        }
        return el;
      })
    );
  }
};
/*
The handleDelete function is used to remove an item from the state array based on the provided id. If the isEdit parameter is true, it filters the items array to remove the item with the matching _id. Otherwise, it filters the items array to remove the item with the matching item.value. The function also updates the itemTypes array to match the filtered items array.
*/
export const handleDelete = (id, setItems, isEdit, itemTypes, setItemTypes) => {
  setItems((prevItems) => {
    const filteredItems = prevItems.filter((item) => {
      if (isEdit) {
        return item?._id !== id;
      } else {
        return item?.item?.value !== id;
      }
    });

    // Update itemTypes to match the filtered items
    const filteredItemTypes = itemTypes.filter((_, index) => {
      if (isEdit) {
        return prevItems[index]?._id !== id;
      } else {
        return prevItems[index]?.item?.value !== id;
      }
    });

    setItemTypes(filteredItemTypes);
    return filteredItems;
  });
};

/*
The saveRfq function is responsible for saving the RFQ (Request for Quotation) data. It performs various validations, formats the data, and then calls the appropriate API (createRFQ or updateRFQ) to save the RFQ. The function also handles various scenarios such as setting default values, updating defaults, and navigating to the appropriate page after successful save.

*/

export const saveRfq = async ({
  setIsSetAsDefault,
  quantity,
  items,
  uom,
  isEdit,
  selectedTermAndCondition,
  terms,
  deliveryDate,
  deliveryAddress,
  paymentTerm,
  comments,
  pdf,
  user,
  vendor,
  companySelectedDetails,
  productHideColumnStatus,
  productHideCustomColumnStatus,
  id,
  createRFQ,
  updateRFQ,
  searchParams,
  createDepOrder,
  defaultParam,
  updateDefaults,
  dispatch,
  navigate,
  isCopy,
  state,
  termsAndConditionsHide,
  rfqId,
}) => {
  setIsSetAsDefault(true);
  // Validate quantity (ensure no negative values)
  if (quantity.some((q) => +q < 0)) {
    toast.error('Product quantity cannot be negative');
    return;
  }
  const local = items.map((item, index) => ({
    item: item?.manualEntry ? null : item?.item || item,
    quantity: quantity[index] || 0,
    uom: uom[index] || item?.uom,
    customColumns: item?.customColumns,
    manualEntry: item?.manualEntry,
  }));
  // Process terms and conditions based on edit status and selection
  let updatedTerms = [];
  if (isEdit) {
    updatedTerms = Array.isArray(selectedTermAndCondition)
      ? selectedTermAndCondition.map((term) => term?.value)
      : [];
  } else if (selectedTermAndCondition?.length === 0) {
    updatedTerms = Array.isArray(terms) ? terms.map((term) => term?.value) : [];
  } else {
    updatedTerms = Array.isArray(selectedTermAndCondition) //this part code used to when t&c are edit during creation of rfq, that time the t&c are available in selectedTermAndCondition
      ? selectedTermAndCondition.map((term) => term?.value)
      : [];
  }
  //seperating the part of data that can send in both time isEdit and not isEdit
  const commonData = {
    deliveryDate: deliveryDate ? new Date(deliveryDate) : '',
    deliveryAddress,
    paymentTerm,
    termsAndConditions: updatedTerms,
    comments,
    attachments: pdf,
    approvedBy: user?.name || user?.username,
    vendor,
    termsAndConditionsHide,
    ...companySelectedDetails,
  };
  let data = {};
  if (!isEdit) {
    const itemTypes = {
      part: ['Part', 'part'],
      partVariants: ['PartVariant'],
      product: ['Product'],
      productVariants: ['ProductVariant'],
    };

    const mapItem = ({ item, uom, quantity, customColumns }) => ({
      item: item?.value?.value || item?.value || null,
      uom,
      quantity,
      customColumns,
    });
    const manualEntryItems = local.filter((item) => item?.manualEntry);

    const filterAndMapItems = (types) =>
      local.filter((item) => types.includes(item?.item?.type)).map(mapItem);

    data = {
      ...commonData,
      part: [...filterAndMapItems(itemTypes.part), ...manualEntryItems],
      partVariants: filterAndMapItems(itemTypes.partVariants),
      product: filterAndMapItems(itemTypes.product),
      productVariants: filterAndMapItems(itemTypes.productVariants),
      poStatus: 'pending',
      productHideColumnStatus,
      customColumnsHideStatus: productHideCustomColumnStatus,
      idData: rfqId,
    };
  } else {
    const createFilteredArray = (items, field) => {
      const filterConditions = {
        part: (item) =>
          (item?.item?.type === 'Part' ||
            (item?.item?.variants?.length === 0 &&
              !['Inhouse Finished Goods', 'Outsource Finished Goods'].includes(
                item?.item?.category
              ))) &&
          (item?.item?.value || item?.item?._id),

        partVariants: (item) =>
          (item?.item?.type === 'PartVariant' || item?.item?.part) &&
          (item?.item?.value || item?.item?._id),

        product: (item) =>
          ['Inhouse Finished Goods', 'Outsource Finished Goods'].includes(
            item?.item?.category
          ) &&
          item?.item?._id &&
          !item?.item?.product,

        productVariants: (item) =>
          ['Inhouse Finished Goods', 'Outsource Finished Goods'].includes(
            item?.item?.product?.category
          ) && item?.item?.product?.productVariants?.length > 0,
      };

      const mapItem = ({ item, uom, quantity, customColumns }) => ({
        item: item?._id || item?.value,
        uom,
        quantity,
        customColumns,
      });

      return items.filter(filterConditions[field]).map(mapItem);
    };
    const manualEntryItems = local.filter((item) => item?.manualEntry);
    data = {
      ...commonData,
      part: [...createFilteredArray(local, 'part'), ...manualEntryItems],
      product: createFilteredArray(local, 'product'),
      productHideColumnStatus,
      customColumnsHideStatus: productHideCustomColumnStatus,
      // partVariants: createFilteredArray(local, 'partVariants'),
      // productVariants: createFilteredArray(local, 'productVariants'),
    };
  }
  // console.log("local", local);
  // console.log("data", data);
  // return;
  if (items.length <= 0) {
    toast.error('Enter the product details');
    return;
  }
  let rfq;
  if (id === 'new' || id === 'indent' || isCopy) {
    rfq = await createRFQ({ data }).unwrap();
  } else {
    await updateRFQ({ data, id });
  }
  // Handle Kanban-related logic
  const kanban = searchParams.get('kanban') === 'true';
  const orderId = searchParams.get('orderId');
  const navigateParams = {
    department: searchParams.get('department'),
    id: rfq?.created?._id,
    refType: searchParams.get('refType'),
    page: searchParams.get('page'),
    taskId: searchParams.get('taskId'),
    index: searchParams.get('index'),
    orderId,
  };
  // Create dependent order if not in Kanban mode
  if (!kanban) {
    let obj = {
      objRef: rfq?.created?._id,
      currentDepartment: 'purchase',
      refKey: 'RequestForQuotation',
      currentPage: 'Request for Quotation',
      userId: state?.user?._id,
      taskId: generatePrefixId(defaultParam?.prefixIds?.['taskId']),
    };

    await createDepOrder({
      data: obj,
    });

    let temp = defaultParam;
    let taskIdFormat = defaultParam?.prefixIds?.['taskId'];
    for (let type of Object.keys(taskIdFormat || {})) {
      let elem = type.substring(0, type.indexOf('_'));
      if (elem === 'Increment') {
        const updatedDefaults = {
          ...temp,
          prefixIds: {
            ...temp.prefixIds,
            taskId: {
              ...temp.prefixIds['taskId'],
              [type]: parseInt(taskIdFormat[type]) + 1,
            },
          },
        };

        temp = updatedDefaults;
      }
    }

    await updateDefaults(temp);
  }
  // Update Kanban board if in Kanban mode
  if (kanban) {
    let time = new Date();
    dispatch({
      type: 'ADD_CARD',
      payload: {
        data: {
          taskId: searchParams.get('taskId'),
          stepPage: 'Request for Quotation',
          updatedAt: time?.toDateString(),
        },
        currentColumn: 'Request for Quotation',
      },
    });
  }
  // Prepare navigation parameters
  const filteredParams = Object.fromEntries(
    Object.entries(navigateParams).filter(([_, value]) => value !== null)
  );

  const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;
  // Navigate based on Kanban mode
  if (kanban) {
    navigate(navigateStr);
  } else {
    navigate('/purchase/requestforquotation');
  }
  // Show success toast based on the operation type
  if (id === 'new' || id === 'indent') {
    toast.success('Request For Quotation Created Successfully');
  } else if (isCopy) {
    toast.success('Request For Quotation Copied Successfully');
  } else {
    toast.success('Request For Quotation Edited Successfully');
  }
  return rfq;
};
