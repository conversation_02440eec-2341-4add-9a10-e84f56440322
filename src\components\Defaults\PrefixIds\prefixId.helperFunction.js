export const getIdDate = () => {
  const da = new Date();

  let y = da?.getFullYear();
  let m = da?.getMonth() + 1;
  let d = da?.getDate();
  y = `${y}`;
  m = `${m < 10 ? '0' : ''}${m}`;
  d = `${d < 10 ? '0' : ''}${d}`;
  return [y, m, d];
};

export const generateIdObject = (obj) => {
  const idObj = {};
  const dropdownObj = {};
  for (const key in obj || {}) {
    const [splitKey] = key?.split('_');
    if (key !== 'isUsed') {
      if (splitKey === 'Dropdown') {
        idObj[key] = '';
        dropdownObj[key] = obj[key];
      } else if (splitKey === 'DD-MM-YY') {
        const [y, m, d] = getIdDate();
        idObj[key] = `${d}-${m}-${y}`;
      } else if (splitKey === 'MM-YY') {
        const [y, m] = getIdDate();
        idObj[key] = `${m}-${y}`;
      } else {
        idObj[key] = obj[key] || '';
      }
    }
  }
  return [idObj, dropdownObj];
};

export const generateIdFromObject = (prefix, userIdData) => {
  const [y, m, d] = getIdDate();
  let str = '';

  const tempData = Object?.entries(prefix || {});

  for (let i = 0; i < tempData.length; i++) {
    const [type, tVal] = tempData[i];
    const [splitType] = type?.split('_');

    if (type === 'isUsed') continue;

    switch (splitType) {
      case 'Increment':
        str = `${str}${tVal}`;
        break;
      case 'DD-MM-YY':
        str = `${str}${d}-${m}-${y}`;
        break;
      case 'MM-YY':
        str = `${str}${m}-${y}`;
        break;
      default:
        str = `${str}${
          userIdData?.[type] ||
          userIdData?.[splitType] ||
          (splitType !== 'Dropdown' ? tVal : '') ||
          ' '
        }`;
        break;
    }
  }
  return str;
};

export const generateIdForJobBatch = (prefix, userIdData, incVal) => {
  const [y, m, d] = getIdDate();
  let str = '';

  const tempData = Object?.entries(prefix || {});

  for (let i = 0; i < tempData.length; i++) {
    const [type, tVal] = tempData[i];
    const [splitType] = type?.split('_');

    if (type === 'isUsed') continue;

    switch (splitType) {
      case 'Increment':
        str = `${incVal}`;
        break;
      case 'DD-MM-YY':
        str = `${str}${d}-${m}-${y}`;
        break;
      case 'MM-YY':
        str = `${str}${m}-${y}`;
        break;
      default:
        str = `${str}${
          userIdData?.[type] ||
          userIdData?.[splitType] ||
          (splitType !== 'Dropdown' ? tVal : '') ||
          ' '
        }`;
        break;
    }
  }
  return str;
};
