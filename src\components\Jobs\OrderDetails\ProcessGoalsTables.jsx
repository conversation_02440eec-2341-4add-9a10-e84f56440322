import GoalsTable from './GoalsTable';

const ProcessGoalsTables = ({
  processes = [],
  goalsTables,
  setGoalsTables,
  detailsStep,
  isEdit = false,
  createTemplate,
  additionalIdData,
  setBatchIdData,
}) => {
  const process = processes?.[detailsStep - 1] || {};
  const goalsTable = goalsTables?.find((i) => i.flowId === process._id);

  return (
    <div className="w-full mt-5">
      {!createTemplate && (
        <GoalsTable
          process={process}
          goalsTable={goalsTable}
          setGoalsTables={setGoalsTables}
          isEdit={isEdit}
          setBatchIdData={setBatchIdData}
          additionalIdData={additionalIdData}
        />
      )}
    </div>
  );
};

export default ProcessGoalsTables;
