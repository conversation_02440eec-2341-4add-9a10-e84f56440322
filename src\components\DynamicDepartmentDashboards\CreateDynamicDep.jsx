import { Modal } from 'antd';
import { CircleCheck } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  useAddDepartmentRowMutation,
  useEditDepartmentRowMutation,
} from '../../slices/departmentRowApiSlice';
import RenderDepFields from './RenderDepFields';
import { sanitizeColName } from './DynamicDepTableUtilityFunc';
const CreateDynamicDep = ({
  openModal,
  setOpenModal,
  columns,
  data,
  editData,
}) => {
  const [formData, setFormData] = useState({});
  const [deletedMedia, setDeletedMedia] = useState([]);
  const [deletedAudio, setDeletedAudio] = useState([]);
  const [deletedForms, setDeletedForms] = useState([]);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [addDepartmentRow] = useAddDepartmentRowMutation();
  const [editDepartmentRow] = useEditDepartmentRowMutation();

  useEffect(() => {
    if (editData) {
      setFormData({
        ...editData?.data,
        taskId: editData?.taskId,
        isUpdate: editData?._id,
      });
    }
  }, [editData]);

  const handleSubmit = async () => {
    for (const column of columns) {
      if (
        column?.isMandatory &&
        !formData?.[sanitizeColName(column?.name)]?.value
      ) {
        toast.error(`${column?.name} is required`);
        return;
      }
    }

    if (editData) {
      let id = formData?.isUpdate;
      let transformedformData = formData;
      delete transformedformData.isUpdate;
      for (let i of Object.keys(transformedformData)) {
        if (transformedformData?.[i]?.type === 'media') {
          let mediaArray = [];
          for (let j of transformedformData?.[i]?.value) {
            if (j?.data) mediaArray?.push(j);
          }
          transformedformData = {
            ...transformedformData,
            [i]: {
              type: 'media',
              value: mediaArray,
            },
          };
        }
      }
      const editedRow = await editDepartmentRow({
        id,
        data: {
          row: transformedformData,
          deletedMedia,
          deletedForms,
          deletedAudio,
        },
      }).unwrap();
      if (editedRow) {
        toast.success('Department Row Updated');
        setOpenModal(false);
        setFormData({});
        const kanban = searchParams.get('kanban') === 'true';
        const orderId = searchParams.get('orderId');
        const navigateParams = {
          department: searchParams.get('department'),
          id: editedRow?._id,
          refType: searchParams.get('refType'),
          page: searchParams.get('page'),
          taskId: searchParams.get('taskId'),
          orderId,
          index: searchParams.get('index'),
        };

        const filteredParams = Object.fromEntries(
          Object.entries(navigateParams).filter(([_, value]) => value !== null)
        );

        const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;

        if (kanban) {
          navigate(navigateStr);
        }
      }
    } else {
      let row = {
        department: data?.department?._id,
        departmentNav: data?.departmentNav?._id,
        departmentChildNav: data?.departmentChildNav?._id,
        departmentColumn: data?._id,
        data: formData,
      };
      const newRow = await addDepartmentRow({
        data: row,
      }).unwrap();
      if (newRow) {
        toast.success('Department Row Added');
        setOpenModal(false);
        setFormData({});
        const kanban = searchParams.get('kanban') === 'true';
        const orderId = searchParams.get('orderId');
        const navigateParams = {
          department: searchParams.get('department'),
          id: newRow?._id,
          refType: searchParams.get('refType'),
          page: searchParams.get('page'),
          taskId: searchParams.get('taskId'),
          orderId,
          index: searchParams.get('index'),
        };

        const filteredParams = Object.fromEntries(
          Object.entries(navigateParams).filter(([_, value]) => value !== null)
        );

        const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;
        if (kanban) {
          navigate(navigateStr);
        }
      }
    }
  };

  return (
    <div>
      <Modal
        title="Create Row"
        open={openModal}
        onCancel={() => setOpenModal(false)}
        width={1800}
        okText={editData ? 'Update' : 'Submit'}
        onOk={handleSubmit}
        centered
        styles={{
          body: {
            maxHeight: `calc(100vh - 150px)`,
            overflowY: 'auto',
          },
        }}
      >
        <div className=" py-10">
          <p>
            Add a new row to the table. Please provide the necessary information
            for each column.
          </p>
          <div className="grid grid-cols-3 gap-4 w-full mt-4 py-10">
            {columns?.map((column, idx) => (
              <div key={idx}>
                <label className="text-sm font-semibold text-gray-800 flex gap-x-1 mb-2">
                  {column?.name}
                  {column?.isMandatory && (
                    <span className="text-red-500">*</span>
                  )}
                  {formData?.[sanitizeColName(column?.name)]?.value?.length >
                    0 && <CircleCheck size={18} color="green" />}
                </label>
                <RenderDepFields
                  column={column}
                  rowData={formData}
                  setRowData={setFormData}
                  index={idx}
                  setDeletedAudio={setDeletedAudio}
                  setDeletedMedia={setDeletedMedia}
                  setDeletedForms={setDeletedForms}
                  deletedAudio={deletedAudio}
                  deletedMedia={deletedMedia}
                  deletedForms={deletedForms}
                />
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CreateDynamicDep;
