import { InfoTooltip } from '../global/components/InfoTooltip';
import { Input, Select } from 'antd';

const KanbanDefaults = ({ defaults, setDefaults }) => {
  return (
    <div className="py-2">
      <div>
        <h3 className="text-gray-subHeading mb-4"><PERSON><PERSON><PERSON> Defaults :</h3>
      </div>
      <div className="grid grid-cols-3">
        <div className="flex flex-col mb-8">
          <div className="flex gap-x-4 items-center">
            <input
              type="checkbox"
              checked={
                defaults?.projectDefaults?.multipleOrdersCreation || false
              }
              onChange={(e) => {
                setDefaults((prev) => ({
                  ...prev,
                  projectDefaults: {
                    ...prev.projectDefaults,
                    multipleOrdersCreation: e.target.checked,
                  },
                }));
              }}
            />
            <div className="flex gap-x-2 items-center">
              <label className=" text-sm font-medium text-gray-700">
                Multiple Orders Creation
              </label>
              <InfoTooltip
                position="top"
                width="200px"
                id="downtimeType"
                isHtml={true}
                content="This is for Multiple Tiles In Kanban From Sales Order"
              />
            </div>
          </div>
          <div className="flex gap-x-4 items-center">
            <input
              type="checkbox"
              checked={
                defaults?.projectDefaults?.shiftToSingleDepartment || false
              }
              onChange={(e) => {
                setDefaults((prev) => ({
                  ...prev,
                  projectDefaults: {
                    ...prev.projectDefaults,
                    shiftToSingleDepartment: e.target.checked,
                  },
                }));
              }}
            />
            <div className="flex gap-x-2 items-center">
              <label className=" text-sm font-medium text-gray-700">
                Shift Tiles to Single Department{' '}
              </label>
              <InfoTooltip
                position="top"
                width="200px"
                id="downtimeType"
                isHtml={true}
                content="This Checkbox Enable to Shift a Tile to a Single Department Only"
              />
            </div>
          </div>
        </div>
        <div className="w-full px-10 ">
          <p className=" text-sm font-medium text-gray-700">
            Kanban Default View
          </p>
          <Select
            className="w-full "
            size="medium"
            value={defaults?.projectDefaults?.defaultKanbanView}
            onChange={(val) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  defaultKanbanView: val,
                },
              }));
            }}
            options={[
              {
                label: 'Kanban',
                value: 'kanban',
              },
              {
                label: 'Tile',
                value: 'tile',
              },
              {
                label: 'Table',
                value: 'table',
              },
            ]}
          />
        </div>
        <div className="w-full px-10 ">
          <p className=" text-sm font-medium text-gray-700">Task ID Label</p>
          <Input
            type="text"
            value={defaults?.projectDefaults?.taskIdLabel || ''}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  taskIdLabel: e.target.value,
                },
              }));
            }}
          />
        </div>
      </div>
      <div className=" border-[1px]"></div>
    </div>
  );
};

export default KanbanDefaults;
