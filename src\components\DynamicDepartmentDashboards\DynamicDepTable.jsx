import { Checkbox, Modal, Table, Typography } from 'antd';
import { useEffect, useState, useContext } from 'react';
import { FaLock } from 'react-icons/fa';
import { getLocalDateTime } from '../../helperFunction';
import { useQueryDepartmentRowsQuery } from '../../slices/departmentRowApiSlice';
import Pagination from '../global/components/Pagination';
import RightSidebar from '../global/components/RightSidebar';
import Spinner from '../global/components/Spinner';
import DynamicDepSideBar from './DynamicDepSideBar';
import {
  renderFieldValue,
  sanitizeColName,
} from './DynamicDepTableUtilityFunc';
import ViewDepFields from './ViewDepFields';
import { Store } from '../../store/Store';

const { Text, Title } = Typography;

const DynamicDepTable = ({
  depColId,
  columns,
  checkedRowIds,
  setCheckedRowIds,
  data,
  setExportAbleData,
}) => {
  const [page, setPage] = useState(1);
  const [rows, setRows] = useState([]);
  const [limit, setLimit] = useState(25);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalData, setModalData] = useState({
    title: '',
    data: null,
    type: '',
  });
  const [openSideBar, setOpenSideBar] = useState(false);
  let userId = JSON.parse(localStorage.getItem('user'))?.user?._id;

  const {
    defaults: {
      defaultParam: {
        projectDefaults: { taskIdLabel },
      },
    },
  } = useContext(Store);

  const { data: depColData = {}, isLoading: isDepartmentLoading } =
    useQueryDepartmentRowsQuery(
      {
        page,
        limit,
        depColId,
      },
      { refetchOnMountOrArgChange: true }
    );

  useEffect(() => {
    if (depColData && depColData?.results) {
      let val = [];
      const data = depColData.results.map((i) => {
        val.push(i);
        if (i?.taskId) {
          return { ...i?.data, taskId: i?.taskId, isUpdate: i?._id };
        } else {
          return { ...i?.data, isUpdate: i?._id };
        }
      });
      setExportAbleData(data?.reverse() || []);
      setRows(val?.reverse() || []);
    }
  }, [depColData]); // eslint-disable-line

  const handleModalOpen = (e, value, title, type) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setIsModalOpen(true);
    setModalData({ title, data: value, type });
  };

  const handleCheckBoxChange = (e, row) => {
    if (e.target.checked) {
      setCheckedRowIds((prev) => [...prev, row._id]);
    } else {
      setCheckedRowIds((prev) => prev.filter((id) => id !== row._id));
    }
  };

  const handleGlobalCheck = (e) => {
    if (e.target.checked) {
      setCheckedRowIds(depColData?.results.map((row) => row._id));
    } else {
      setCheckedRowIds([]);
    }
  };

  const handleRowClick = (record) => {
    setSelectedRow(record);
    setOpenSideBar(true);
  };

  const tableColumns = [
    {
      title: (
        <div style={{ textAlign: 'center' }}>
          <Checkbox
            checked={
              checkedRowIds.length > 0 &&
              checkedRowIds.length === depColData?.results?.length
            }
            indeterminate={
              checkedRowIds.length > 0 &&
              checkedRowIds.length < depColData?.results?.length
            }
            onChange={(e) => handleGlobalCheck(e)}
          />
        </div>
      ),
      dataIndex: 'checkbox',
      align: 'center',
      render: (_, row) => (
        <Checkbox
          checked={checkedRowIds.includes(row._id)}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) => handleCheckBoxChange(e, row)}
        />
      ),
      width: 50,
      fixed: 'left',
    },
    {
      title: (
        <div style={{ textAlign: 'center' }}>{taskIdLabel || 'Task ID'}</div>
      ),
      dataIndex: 'taskId',
      key: 'taskId',
      align: 'center',
      render: (text, row) => (
        <Text type="link">{row?.taskId?.taskId || '-'}</Text>
      ),
    },
    {
      title: <div style={{ textAlign: 'center' }}>Date</div>,
      dataIndex: 'createdAt',
      key: 'createdAt',
      align: 'center',
      render: (text, row) => (
        <div className="flex flex-col text-[12px]">
          <p>
            {getLocalDateTime(row?.createdAt).split(',')[0]} {/* Date */}
          </p>
          <p className="whitespace-nowrap">
            {getLocalDateTime(row?.createdAt).split(',')[1]} {/* Time */}
          </p>
        </div>
      ),
    },
    ...columns?.map((column) => {
      let columnUsers = column?.users;
      let isVisible = true;
      if (column?.users?.length !== 0) {
        isVisible = columnUsers?.includes(userId);
      }
      return {
        title: <div style={{ textAlign: 'center' }}>{column?.name}</div>,
        dataIndex: sanitizeColName(column?.name),
        key: column._id,
        align: 'center',
        render: (_, row) => {
          let col = row?.data?.[sanitizeColName(column?.name)];
          if (!col) {
            col = row?.data?.[column?.name];
          }
          if (isVisible) {
            return renderFieldValue(
              col,
              sanitizeColName(column?.name),
              handleModalOpen
            );
          } else {
            return <FaLock size={20} color="gray" className="m-auto" />;
          }
        },
      };
    }),
  ];

  if (isDepartmentLoading) return <Spinner />;

  return (
    <div className="mt-4">
      <RightSidebar
        openSideBar={openSideBar}
        setOpenSideBar={setOpenSideBar}
        title="Row Details"
      >
        <DynamicDepSideBar
          data={data}
          selectedRow={selectedRow}
          columns={columns}
          setOpenSideBar={setOpenSideBar}
        />
      </RightSidebar>
      <Table
        dataSource={rows || []}
        columns={tableColumns}
        rowKey={(row) => row._id}
        pagination={false}
        scroll={{ x: 'max-content' }}
        size="middle"
        onRow={(record) => ({
          onClick: () => handleRowClick(record),
          style: { cursor: 'pointer' },
        })}
        className="ant-table-hover"
      />

      <Pagination
        limit={limit}
        page={page}
        totalPages={depColData?.totalPages}
        totalResults={depColData?.totalResults}
        setPage={setPage}
        setLimit={setLimit}
        className="w-full"
      />

      <Modal
        title={
          <Title level={4} style={{ textAlign: 'center' }}>
            {modalData?.title}
          </Title>
        }
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width={800}
        onRow={(record) => ({
          onClick: () => handleRowClick(record),
          className: 'cursor-pointer hover:bg-gray-50',
        })}
        styles={{
          body: {
            maxHeight: `calc(100vh - 150px)`,
            overflowY: 'auto',
          },
        }}
      >
        <div className="py-4">
          <ViewDepFields data={modalData?.data} type={modalData?.type} />
        </div>
      </Modal>
    </div>
  );
};

export default DynamicDepTable;
