import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { useEffect, useRef, useState } from 'react';

function NewSelect({
  value,
  onChange,
  name = '',
  options,
  placeholder = 'Please select a value',
  disabled = false,
  className,
  required = false,
  type,
  id,
}) {
  const [expand, setExpand] = useState({ state: false, position: 'bottom' });
  const [searchBar, setSearchBar] = useState('');
  const [displayValue, setDisplayValue] = useState(
    options?.find((option) => option.value === value)?.name || ''
  );

  const inputRef = useRef(null);

  useEffect(() => {
    setDisplayValue(
      options?.find((option) => option.value === value)?.name || ''
    );
  }, [value, options]);
  return (
    <div
      className={`w-full relative outline-none rounded-lg flex justify-between items-center border text-black ${className} ${
        disabled ? 'pointer-events-none' : ''
      } ${
        type === 'round'
          ? 'rounded-[50vh] bg-white'
          : type === 'border'
            ? `border-b-2 border-red-primary bg-gray-primary`
            : 'bg-white'
      }`}
    >
      <input
        ref={inputRef}
        disabled={disabled}
        autoComplete="off"
        required={required}
        onBlur={() =>
          setTimeout(() => {
            setExpand({ state: false, position: 'bottom' });
          }, 300)
        }
        onFocus={() => {
          const position = inputRef?.current?.getBoundingClientRect();
          const diff = window?.innerHeight - position?.bottom;
          setExpand({ state: true, position: diff > 120 ? 'bottom' : 'top' });
        }}
        className={`w-full py-2 pl-4 pr-2 outline-[#4085ed80] bg-transparent rounded-lg`}
        value={displayValue}
        onChange={(e) => {
          setSearchBar(e.target.value);
          setDisplayValue(e.target.value);
        }}
        placeholder={placeholder}
        id={id}
      />

      {expand?.state ? (
        <ChevronUpIcon
          onClick={() => {
            // setExpand((prev) => ({ ...prev, state: !prev?.state }));
            inputRef.current.blur();
          }}
          className={`absolute top-1/2 right-2 -translate-y-1/2 h-5/6 rounded-md w-4 ${
            type === 'border' ? 'bg-gray-primary' : 'bg-white'
          }`}
        />
      ) : (
        <ChevronDownIcon
          onClick={() => {
            // setExpand((prev) => !prev);
            inputRef.current.focus();
          }}
          className={`absolute top-1/2 right-2 -translate-y-1/2 h-5/6 rounded-md w-4 ${
            type === 'border' ? 'bg-gray-primary' : 'bg-white'
          }`}
        />
      )}
      {expand?.state && options && (
        <ul
          className={`absolute z-10 left-0 text-left bg-white border border-gray-200 shadow-md outline-none rounded-lg py-1 px-1 text-xs max-h-[400%] w-full overflow-y-scroll ${
            expand?.position === 'bottom' ? 'top-[110%]' : 'bottom-[110%]'
          }`}
        >
          {options && options?.length > 0 ? (
            options?.map((option, oIdx) => {
              if (
                searchBar &&
                !option?.name?.toLowerCase()?.includes(searchBar?.toLowerCase())
              ) {
                return null;
              }
              return (
                <li
                  key={oIdx}
                  onClick={() => {
                    onChange({ target: { name, value: option.value } });
                    setSearchBar('');
                  }}
                  className={`py-1 my-[1px] px-1 hover:cursor-pointer hover:bg-blue-light hover:text-blue-primary rounded-lg ${
                    option.hidden && 'hidden'
                  } ${
                    option.disabled &&
                    'pointer-events-none line-through text-gray-400'
                  } ${
                    option.value === value && 'bg-blue-light text-blue-primary'
                  }`}
                >
                  {option.name}
                </li>
              );
            })
          ) : (
            <li className="py-1 px-2 pointer-events-none text-gray-400">
              No Options Available
            </li>
          )}
        </ul>
      )}
    </div>
  );
}

export default NewSelect;
