import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';

/**
 *
 * @param {object} props
 * @param {{name:string,modified:boolean}[]} props.tabs List of tabs
 * @param {boolean} props.showChecks Show checks for each steps
 * @param {function} props.children Function that renders jsx
 * @param {function} props.handleTabClick Function runs on tab click
 * @returns JSX VerticalTab component
 */
const VerticalTabs = ({
  children,
  tabs = [],
  showChecks = false,
  handleTabClick,
  createTemplate,
}) => {
  const [step, setStep] = useState(0);

  useEffect(() => {
    setStep(createTemplate ? 1 : 0);
  }, [createTemplate]);

  return (
    <div className="w-full h-full  bg-white">
      <div className="h-full flex  overflow-x-scroll">
        {tabs?.map((tab, tIdx) => (
          <section
            key={tIdx}
            title={tab?.name}
            className={`w-full border-b px-3 lg:px-6 py-1.5 hover:cursor-pointer flex ${
              step === (createTemplate ? tIdx + 1 : tIdx)
                ? 'bg-blue-primary text-white'
                : 'bg-white text-gray-900 hover:bg-blue-light hover:text-gray-800'
            }`}
            onClick={() => {
              setStep(createTemplate ? tIdx + 1 : tIdx);
              if (handleTabClick && showChecks) handleTabClick(tIdx);
            }}
          >
            <p className="w-[90%]">{tab?.name}</p>
            {showChecks && (
              <span className="w-[10%]">
                {tab?.modified ? (
                  <CheckIcon className="text-green-primary" />
                ) : (
                  <XMarkIcon className="text-red-700" />
                )}
              </span>
            )}
          </section>
        ))}
      </div>
      <div className="flex h-full border-l-2">
        {children({ step, setStep })}
      </div>
    </div>
  );
};

export default VerticalTabs;
