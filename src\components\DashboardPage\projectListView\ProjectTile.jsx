import { useEffect, useRef, useState } from 'react';
import {
  CircularProgressbarWithChildren,
  buildStyles,
} from 'react-circular-progressbar';
import { calculateValueForViews } from '../../../helperFunction';
import Select from '../../global/components/Select';

const ProjectTile = ({
  createInput,
  cuProjects,
  mqtts,
  selectFilters,
  projects,
  setSelectOptions,
  values,
}) => {
  const [selectFlowProcess, setSelectFlowProcess] = useState(
    createInput?.productionFlow?.processes?.[0]?._id || ''
  );
  const [machineList, setMachineList] = useState([]);
  const [cuProjectsList, setCuProjectsList] = useState([]);
  const [progress, setProgress] = useState(0);

  const goalsData = createInput?.goalsData?.[selectFlowProcess];

  const productionFlow = createInput?.productionFlow;

  const flowProcess = productionFlow?.processes?.find(
    (pro) => pro._id === selectFlowProcess
  );

  const tileRef = useRef(null);

  useEffect(() => {
    if (cuProjects && selectFlowProcess) {
      const tempList = [];

      const temp = [];

      cuProjects.forEach((cuProject) => {
        if (cuProject?.flowId !== selectFlowProcess) return;
        if (selectFilters) {
          if (
            cuProject?.machineAndOperator?.find(
              (mao) => mao?.status === selectFilters
            )
          ) {
            temp.push(cuProject);
          }
        } else {
          temp.push(cuProject);
        }
        cuProject.machineAndOperator.forEach((mao) => {
          // gets list of all machines used in project
          if (mao?.machine?.machineId) {
            if (
              tempList?.find((item) => item.id === mao.machine.machineId) ||
              mao?.status !== selectFilters
            )
              return;
            tempList.push({
              id: mao.machine.machineId,
              name: mao.machine.machineName,
            });
          }
        });
      });

      setCuProjectsList(temp);

      setMachineList(tempList);
    }
  }, [selectFlowProcess, cuProjects, selectFilters]);

  // useEffect(() => {
  //   if (createInput) {
  //     const batchString = 'Batch Size';
  //     let sum = 0;
  //     const noOfBatches =
  //       createInput?.goalsData?.[selectFlowProcess]?.['Number of Batches'];

  //     const complete = createInput?.cuProjects.filter(
  //       (cuPro) => cuPro?.status === 'complete'
  //     );

  //     complete?.forEach((cuProject) => {
  //       const batchSize = cuProject?.batchInfo?.[batchString];
  //       const count =
  //         cuProject?.processGoalData?.dataOnStop?.[0]?.data?.[batchString];

  //       sum = sum + count / batchSize;
  //     });

  //     const notComplete = createInput?.cuProjects.filter(
  //       (cuPro) => cuPro?.status !== 'complete'
  //     );

  //     notComplete?.forEach((cuProject) => {
  //       const batchSize = cuProject?.batchInfo?.[batchString];

  //       const project = projects?.find(
  //         (pro) => pro?.projectId?._id === cuProject?.mqtt
  //       );

  //       const param = project?.processGoalView?.parameters?.find(
  //         (param) => param.name === batchString
  //       );

  //       const machineList = cuProject?.machineAndOperator?.map(
  //         (mao) => mao?.machine?.machineId
  //       );

  //       if (param?.formula && values && machineList) {
  //         const calculatedValue = calculateValueForViews(
  //           param?.formula,
  //           values,
  //           machineList
  //         );

  //         sum = sum + calculatedValue / batchSize;
  //       }
  //     });

  //     setProgress((sum / noOfBatches) * 100 || 0);
  //   }
  // }, [createInput, selectFlowProcess, values, projects]);

  useEffect(() => {
    if (createInput) {
      const batchString = 'Batch Size';
      let sum = 0;
      const noOfBatches =
        createInput?.goalsData?.[selectFlowProcess]?.['Number of Batches'];

      let complete = [];
      let notComplete = [];

      // loop over every cuProject in createInput
      createInput?.cuProjects?.forEach((cuPro) => {
        if (cuPro?.status === 'complete') {
          // if project is complete

          if (cuPro?.isMultiProcess) {
            // if project is multi process

            // check if project already exists in complete array
            const exists = complete.find(
              (item) =>
                item.flowId === cuPro.flowId &&
                item.batchInfo.batchNo === cuPro.batchInfo.batchNo
            );
            // if not exists then find last subprocess project and push to complete array
            if (!exists) {
              const temp = createInput?.cuProjects?.findLast(
                (item) =>
                  item.flowId === cuPro.flowId &&
                  item.batchInfo.batchNo === cuPro.batchInfo.batchNo &&
                  item.status === 'complete'
              );

              complete.push(temp);
            }
          } else {
            // if project is not multi process push to complete array
            complete.push(cuPro);
          }
        } else {
          // if project is not complete

          if (cuPro?.isMultiProcess) {
            // if project is multi process

            // check if project already exists in complete array
            const exists = notComplete.find(
              (item) =>
                item.flowId === cuPro.flowId &&
                item.batchInfo.batchNo === cuPro.batchInfo.batchNo
            );

            // if not exists then find last subprocess project and push to notComplete array
            if (!exists) {
              const temp = createInput?.cuProjects?.findLast(
                (item) =>
                  item.flowId === cuPro.flowId &&
                  item.batchInfo.batchNo === cuPro.batchInfo.batchNo &&
                  item.status !== 'complete'
              );
              notComplete.push(temp);
            }

            // check if projects exists in complete array
            const exists2 = complete.find(
              (item) =>
                item.flowId === cuPro.flowId &&
                item.batchInfo.batchNo === cuPro.batchInfo.batchNo &&
                item.status === 'complete'
            );

            // if exists remove from project from complete array
            if (exists2) {
              complete = complete?.filter(
                (item) =>
                  !(
                    item.flowId === exists2.flowId &&
                    item.batchInfo.batchNo === exists2.batchInfo.batchNo
                  )
              );
            }
          } else {
            // if project is not multi process push to not complete array
            notComplete.push(cuPro);
          }
        }
      });

      // get table data of first goals table in creatinput to get batch size
      const tableData = createInput?.goalsTable?.[0]?.tableData;

      complete?.forEach((cuProject) => {
        const batchSize = tableData?.find(
          (item) => item.batchNo === cuProject?.batchInfo?.batchNo
        )?.[batchString];

        const count =
          +cuProject?.processGoalData?.dataOnStop?.[0]?.data?.[batchString];

        sum = sum + count / batchSize;
      });

      notComplete?.forEach((cuProject) => {
        const batchSize = tableData?.find(
          (item) => item.batchNo === cuProject?.batchInfo?.batchNo
        )?.[batchString];

        const project = projects?.find(
          (pro) => pro?.projectId?._id === cuProject?.mqtt
        );

        const param = project?.processGoalView?.parameters?.find(
          (param) => param.name === batchString
        );

        const machineList = cuProject?.machineAndOperator?.map(
          (mao) => mao?.machine?.machineId
        );

        if (param?.formula && values && machineList) {
          const calculatedValue = calculateValueForViews(
            param?.formula,
            values,
            machineList
          );

          sum = sum + calculatedValue / batchSize;
        }
      });

      setProgress((sum / noOfBatches) * 100 || 0);
    }
  }, [createInput, selectFlowProcess, values, projects]);

  return (
    <div
      ref={tileRef}
      onClick={() => {
        setSelectOptions({
          createInput,
          machineList,
          cuProjects: cuProjectsList,
          flowId: selectFlowProcess,
          mqtt: mqtts?.find((item) => item?._id === flowProcess?.mqtt?._id),
          project: projects?.find(
            (item) => item?.projectId?._id === flowProcess?.mqtt?._id
          ),
        });
      }}
      className="w-full aspect-[65/33] border-l-4 border-red-primary flex gap-x-3 px-4 items-center bg-white rounded-new mb-3 last:mb-0"
    >
      <section className="flex flex-col justify-start h-full w-3/5 py-3">
        <p className="text-[1.13rem] font-bold mb-1">{createInput.id}</p>
        <Select
          type="border"
          value={selectFlowProcess}
          onChange={(e) => {
            setSelectFlowProcess(e.target.value);
            setTimeout(() => {
              tileRef?.current?.click();
            }, 100);
          }}
          className={`w-full mb-1`}
          options={productionFlow?.processes?.map((pro) => ({
            name: pro.processName,
            value: pro._id,
          }))}
        />
        <p className="flex justify-start text-[0.63rem]">
          <span className="text-[0.63rem] italic">Active Machines :</span>
          <span className="flex flex-col">
            {machineList.map((mac) => (
              <span className="font-medium ml-1" key={mac.id}>
                {mac.name}
              </span>
            ))}
          </span>
        </p>
      </section>
      <section className="w-2/5  aspect-square">
        <CircularProgressbarWithChildren
          value={progress}
          strokeWidth={5}
          styles={buildStyles({
            rotation: 0.25,
            strokeLinecap: 'butt',
            pathTransitionDuration: 0.5,
            pathColor: `#D25050`,
            trailColor: '#fff',
          })}
        >
          <p className="text-[1.25rem] font-bold">
            {Math.round(progress > 100 ? 100 : progress)}%
          </p>
          <p className="text-[0.63rem]">{`Target : ${goalsData?.['Order Quantity']}`}</p>
        </CircularProgressbarWithChildren>
      </section>
    </div>
  );
};
export default ProjectTile;
