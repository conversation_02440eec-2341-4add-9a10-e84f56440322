import { Modal } from 'antd';
import Input from '../global/components/Input';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import Textarea from '../global/components/Textarea';
import { Label } from '../v2';

const EditIndent = ({
  openModal,
  setOpenModal,
  setSearchParams,
  handelEditIndent,
  isEditIndentLoading,
  EditData,
  setEditData,
  DropdownValue,
  handelpartChange,
  setSearchQuery,
  Suggestedparts,
  getCorrespondingConversionFactor,
  dateConfig,
  FormatDate,
  deliveryDateOptions,
  generateDateString,
  handleSetDate,
  DateValue,
  setDateValue,
}) => {
  const handleCancel = () => {
    setSearchParams(
      (prev) => {
        prev.set('id', ''); // Remove the id parameter
        prev.delete('isCopy'); // Remove the isCopy parameter
        return prev;
      },
      { replace: true }
    );
    setOpenModal(false);
  };

  const conversion = getCorrespondingConversionFactor(EditData?.uom, EditData);
  return (
    <Modal
      title="Edit Indent"
      open={openModal}
      onCancel={handleCancel}
      onOk={handelEditIndent}
      confirmLoading={isEditIndentLoading}
    >
      <section>
        <div className="product-preview">
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Th>Product name</Table.Th>
                <Table.Th>uom</Table.Th>
                <Table.Th>quantity</Table.Th>
                <Table.Th>Delivery date</Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              <Table.Row>
                <Table.Td>
                  <div className="max-w-[40ch] break-words">
                    {EditData.product_name}
                  </div>
                </Table.Td>
                <Table.Td>
                  <div style={{ whiteSpace: 'nowrap' }}>{EditData?.uom}</div>
                  {conversion && (
                    <span
                      style={{ whiteSpace: 'nowrap' }}
                      className=" mt-2 text-gray-500"
                    >
                      {conversion.conversionValue +
                        '-' +
                        conversion.conversionUnit}
                    </span>
                  )}
                </Table.Td>
                <Table.Td>{EditData.quantity}</Table.Td>
                <Table.Td>
                  {FormatDate(EditData.delivery_date, dateConfig)}
                </Table.Td>
              </Table.Row>
            </Table.Body>
          </Table>
        </div>
        <Label>
          Edit Products
          <Select
            value={DropdownValue}
            onInputChange={(e) => setSearchQuery(e)}
            options={Suggestedparts?.map((part) => {
              return {
                name: part?.name,
                value: part?.name,
              };
            })}
            onChange={handelpartChange}
            id="search-input"
          />
        </Label>
        <Label>
          Delivery Date
          {/* <Input
                       type="date"
                       onChange={(e) => {
                         setEditData((prev) => {
                           return {
                             ...prev,
                             delivery_date: e.target.value,
                           };
                         });
                       }}
                     /> */}
          {DateValue === '+' ? (
            <Input
              type="date"
              className="w-full"
              id="deliveryDate"
              name="deliveryDate"
              placeholder="Delivery Date"
              value={EditData?.delivery_date}
              onChange={(e) => {
                setEditData((prev) => {
                  return {
                    ...prev,
                    delivery_date: e.target.value,
                  };
                });
              }}
            />
          ) : (
            <Select
              options={deliveryDateOptions}
              className="w-full"
              placeholder={
                EditData?.delivery_date
                  ? generateDateString(new Date(EditData?.delivery_date))
                  : 'Select'
              }
              onChange={(e) => {
                if (e.target.value === '+') {
                  setEditData((prev) => {
                    return {
                      ...prev,
                      delivery_date: '',
                    };
                  });
                  setDateValue(e.target.value);
                } else {
                  handleSetDate(e);
                }
              }}
            />
          )}
        </Label>
        <Label>
          Quantity
          <Input
            type="number"
            value={EditData.quantity}
            onChange={(e) => {
              setEditData((prev) => {
                return {
                  ...prev,
                  quantity: e.target.value,
                };
              });
            }}
          />
        </Label>
        <Label>
          Remark
          <Textarea
            value={EditData.remark}
            placeholder="Add Remark"
            onChange={(e) => {
              setEditData((prev) => {
                return {
                  ...prev,
                  remark: e.target.value,
                };
              });
            }}
          />
        </Label>
      </section>
    </Modal>
  );
};

export default EditIndent;
