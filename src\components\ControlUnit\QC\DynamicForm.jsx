import { useState } from 'react';
import QRCode from 'react-qr-code';
import QrReader from 'react-qr-scanner';
import SwitchIcon from '../../../assets/images/create.png';
import { ReactComponent as Preview } from '../../../assets/svgs/preview.svg';
import PreviewPopUp from '../../PreviewPop';
import Button from '../../global/components/Button';
import Input from '../../global/components/Input';
import Table from '../../global/components/Table';
import DragAndDrop from '../DragAndDrop';
import Select from '../../global/components/Select';

const TrComponent = ({ label, option, setFormData, formData }) => {
  const handleChange = (e) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: {
        ...(prev?.[name] || {}),
        value: prev?.[name]?.value
          ? prev?.[name]?.value?.find((itm) => itm === value)
            ? prev?.[name]?.value?.filter((item) => item !== value)
            : [...prev[name].value, value]
          : [value],
      },
    }));
  };

  return (
    <>
      <div className="flex flex-row justify-between">
        <label htmlFor={option} className="inline-flex items-center mr-4">
          <input
            type="checkbox"
            id={option}
            checked={formData?.[label]?.value?.find((item) => item === option)}
            name={label}
            value={option}
            onChange={handleChange}
            className="mr-2 leading-tight"
          />
          <span className="text-sm">{option}</span>
        </label>
      </div>
    </>
  );
};

const DynamicForm = ({
  formDetail,
  formData,
  setFormData,
  generatedQrData,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenQR, setIsOpenQR] = useState(false);
  const [isScan, setIsScan] = useState(false);
  const [currField, setCurrField] = useState('');
  const [readFromWebCam, setReadFromWebCam] = useState(true);
  const [qrScannerValue, setQrScannerValue] = useState('');

  const refData = formDetail.refData;

  const formFields = formDetail?.formId?.formData;

  let formswithTable = formFields?.filter(
    (item) => item.fieldType !== 'MultiCheckbox'
  );

  const formFieldNoRadio = formswithTable?.filter(
    (item) => item.fieldType !== 'Table'
  );

  const formFieldRadio = formFields?.filter(
    (item) => item.fieldType === 'MultiCheckbox'
  );

  const tableFields = formFields?.filter((item) => item.fieldType === 'Table');

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: {
        ...(prev?.[name] || {}),
        value: type === 'checkbox' ? checked : value,
      },
    }));
  };

  const changeHandler = (e, fieldName) => {
    for (let i in e) {
      const fr = new FileReader();
      if (i === 'length') return;
      let name = e[i].name;
      let type = e[i].type;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;

        setFormData((prev) => ({
          ...prev,
          [fieldName]: {
            ...(prev?.[fieldName] || {}),
            media: [
              ...(prev?.[fieldName]?.media || []),
              {
                name: name,
                type: type,
                data: url,
              },
            ],
          },
        }));
      });
    }
  };

  const handleScan = async (scanData) => {
    if (scanData && scanData !== '') {
      setQrScannerValue(JSON.parse(scanData?.text) || '');
      setIsScan(false);
    }
  };

  const removeImgHandler = (url) => {
    // setFormMedia((prev) => prev.filter((item) => item?.media?.data !== url));
    setFormData((prev) => ({
      ...prev,
      [currField]: {
        ...prev?.[currField],
        media: prev?.[currField]?.media?.filter((item) => item.data !== url),
      },
    }));
  };

  return (
    <div className="mt-2 mb-2">
      {isOpen && (
        <PreviewPopUp isOpen={isOpen} setIsOpen={setIsOpen}>
          <div className="px-8 mt-[6%] ">
            <h3 className="mb-1  text-lg">Uploaded Images </h3>
            <div className="flex w-full gap-x-5 overflow-y-scroll">
              {formData?.[currField]?.media?.map((item, uIdx) => (
                <section key={uIdx} className="p-2 border rounded-md w-fit">
                  <img
                    src={item?.data}
                    alt=""
                    className="hover:cursor-pointer min-w-[180px] max-w-[250px]"
                  />

                  <section className="flex justify-between items-center text-sm mt-2">
                    <span className="px-2">{item?.name}</span>
                    <button
                      onClick={() => removeImgHandler(item?.data)}
                      className="outline-none text-red-primary hover:text-white hover:bg-red-primary rounded px-2 py-1"
                    >
                      Remove
                    </button>
                  </section>
                </section>
              ))}
            </div>
          </div>
        </PreviewPopUp>
      )}
      {isOpenQR && (
        <PreviewPopUp
          isOpen={isOpenQR}
          setIsOpen={setIsOpenQR}
          className="sm:w-[30%] sm:h-[50%] h-[40%]"
          postion="sm:top-[28%] top-[32%] sm:right-[36.5%] right-[13%]"
        >
          <div className="px-8 mt-[6%] ">
            <h3 className="mb-3 justify-center items-center flex text-3xl mt-5">
              Scan QR{' '}
            </h3>
            <div className="flex items-center justify-center mt-[10%]">
              <QRCode
                title="None"
                value={JSON.stringify(generatedQrData) || ''}
                size={200}
              />
            </div>
          </div>
        </PreviewPopUp>
      )}
      {isScan && (
        <PreviewPopUp
          isOpen={isScan}
          setIsOpen={setIsScan}
          className="w-[50%] h-[58%] "
          postion="top-[25%] right-[27%]"
        >
          <div className="px-8 mt-[6%] ">
            {readFromWebCam && (
              <h3 className="mb-1 justify-center items-center flex text-md mt-5">
                Scanning....{' '}
              </h3>
            )}
            <div className="w-full h-4/5 flex flex-col justify-between items-center mt-4 ">
              {readFromWebCam ? (
                <QrReader
                  onScan={(e) => handleScan(e)}
                  onError={(err) => console.log(err)} // eslint-disable-line
                  style={{ height: '100%', width: '70%' }}
                  constraints={{
                    video: { facingMode: 'environment' },
                  }}
                />
              ) : (
                <Input
                  className="mt-[40%]"
                  value={qrScannerValue}
                  onChange={(e) => setQrScannerValue(e.target.value)}
                />
              )}

              <Button
                className={'mt-6'}
                onClick={() => setReadFromWebCam((prev) => !prev)}
              >
                <img
                  src={SwitchIcon}
                  alt="Switch Icon"
                  className="w-5 h-5 object-contain relative"
                />
                Switch
              </Button>
            </div>
          </div>
        </PreviewPopUp>
      )}
      <Table className="w-full">
        <Table.Head>
          <Table.Row>
            <Table.Th className="!text-[10px] !px-2 md:!px-5 md:!text-[12px]">
              Name
            </Table.Th>
            <Table.Th className="!text-[10px] !px-2 md:!px-5 md:!text-[12px]">
              Value
            </Table.Th>
            <Table.Th className="!text-[10px] !px-2 md:!px-5 md:!text-[12px]">
              Input
            </Table.Th>
            <Table.Th className="!text-[10px] !px-2 md:!px-5 md:!text-[12px]">
              Add File
            </Table.Th>
          </Table.Row>
        </Table.Head>

        <Table.Body className="w-9/12">
          {formFieldNoRadio
            ?.filter?.((field) => field.fieldType !== 'Table')
            ?.map((field, index) => {
              let fName = field.fieldName;
              return (
                <Table.Row key={index}>
                  <Table.Td className="!text-[10px] !px-2 md:!px-5 md:!text-[12px]">
                    <label
                      htmlFor={field.fieldName}
                      className="block text-gray-700 text-sm font-bold mb-2"
                    >
                      {field.fieldName}
                    </label>
                  </Table.Td>
                  <Table.Td className="!text-[10px] !px-2 md:!px-5 md:!text-[12px]">
                    {(() => {
                      if (field.fieldType === 'Date') {
                        let condition = fName + '-Condition';

                        const temp =
                          refData?.[condition] === 'LT'
                            ? 'Before'
                            : refData?.[condition] === 'GT'
                              ? 'After'
                              : '';

                        const lbl =
                          temp || '' + ' ' + (refData?.[fName] || '-');
                        return (
                          <label
                            htmlFor={field.fieldName}
                            className="block text-gray-700 text-sm font-bold mb-2"
                          >
                            {lbl || '-'}{' '}
                          </label>
                        );
                      } else if (field.fieldType === 'Range') {
                        let fmin = fName + '-Min';
                        let fmax = fName + '-Max';
                        const lbl =
                          (refData?.[fmin] || 'NA') +
                          '-' +
                          (refData?.[fmax] || 'NA');

                        return (
                          <label
                            htmlFor={field.fieldName}
                            className="block text-gray-700 text-sm font-bold mb-2"
                          >
                            {lbl || '-'}{' '}
                          </label>
                        );
                      } else if (field.fieldType === 'Range Threshold') {
                        let fmin = fName + '-Main';
                        let fmax = fName + '-Thres';
                        const lbl =
                          (refData?.[fmin] || 'NA') +
                          ' +/- ' +
                          (refData?.[fmax] || 'NA');

                        return (
                          <label
                            htmlFor={field.fieldName}
                            className="block text-gray-700 text-sm font-bold mb-2"
                          >
                            {lbl || '-'}{' '}
                          </label>
                        );
                      } else if (field.fieldType === 'String') {
                        return (
                          <label
                            htmlFor={field.fieldName}
                            className="block text-gray-700 text-sm font-bold mb-2"
                          >
                            {refData?.[field.fieldName] || '-'}
                          </label>
                        );
                      } else if (field.fieldType === 'Check') {
                        if (refData?.[fName] === true) {
                          return (
                            <label
                              htmlFor={field.fieldName}
                              className="block text-gray-700 text-sm font-bold mb-2"
                            >
                              True
                            </label>
                          );
                        } else {
                          return (
                            <label
                              htmlFor={field.fieldName}
                              className="block text-gray-700 text-sm font-bold mb-2"
                            >
                              False
                            </label>
                          );
                        }
                      } else if (field.fieldType === 'DropDown') {
                        return (
                          <label
                            htmlFor={field.fieldName}
                            className="block text-gray-700 text-sm font-bold mb-2"
                          >
                            {refData?.[field.fieldName] || '-'}
                          </label>
                        );
                      } else {
                        return (
                          <label
                            htmlFor={field.fieldName}
                            className="block text-gray-700 text-sm font-bold mb-2"
                          >
                            False
                          </label>
                        );
                      }
                    })()}
                    <label
                      htmlFor={field.fieldName}
                      className="block text-gray-700 text-sm font-bold mb-2"
                    ></label>
                  </Table.Td>
                  <Table.Td className="!text-[10px] !px-2 md:!px-5 md:!text-[12px]">
                    {field.fieldType === 'Date' && (
                      <div className="flex ">
                        <input
                          type="date"
                          required={refData?.[field.fieldName + 'Man']}
                          name={field.fieldName}
                          id={field.fieldName}
                          value={formData[field.fieldName]?.value || ''}
                          onChange={handleChange}
                          className="sm:w-[60%] w-[100%]  shadow appearance-none border rounded py-2 lg:px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />
                      </div>
                    )}

                    {field.fieldType === 'Range' && (
                      <div className="flex ">
                        <input
                          type="number"
                          required={refData?.[field.fieldName + 'Man']}
                          name={field.fieldName}
                          id={field.fieldName}
                          value={formData[field.fieldName]?.value || ''}
                          onChange={handleChange}
                          className="sm:w-[60%] w-[100%]  shadow appearance-none border rounded py-2 lg:px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />
                      </div>
                    )}

                    {field.fieldType === 'Range Threshold' && (
                      <div className="flex ">
                        <input
                          type="number"
                          required={refData?.[field.fieldName + 'Man']}
                          name={field.fieldName}
                          id={field.fieldName}
                          value={formData[field.fieldName]?.value || ''}
                          onChange={handleChange}
                          className="sm:w-[60%] w-[100%] shadow appearance-none border rounded py-2 lg:px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />
                      </div>
                    )}

                    {field.fieldType === 'Check' && (
                      <div className="flex ml-14">
                        <input
                          type="checkbox"
                          required={refData?.[field.fieldName + 'Man']}
                          name={field.fieldName}
                          id={field.fieldName}
                          checked={formData[field.fieldName]?.value || false}
                          onChange={handleChange}
                          className="mr-2 leading-tight  h-10"
                        />
                      </div>
                    )}
                    {field.fieldType === 'String' && (
                      <div className="flex ">
                        <input
                          type="text"
                          required={refData?.[field.fieldName + 'Man']}
                          name={field.fieldName}
                          id={field.fieldName}
                          value={formData[field.fieldName]?.value || ''}
                          onChange={handleChange}
                          className="sm:w-[60%] w-[100%] shadow appearance-none border rounded py-2 lg:px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />
                      </div>
                    )}

                    {field.fieldType === 'DropDown' && (
                      <div className="flex ">
                        <Select
                          required={refData?.[field.fieldName + 'Man']}
                          name={field.fieldName}
                          id={field.fieldName}
                          value={formData[field.fieldName]?.value || ''}
                          onChange={handleChange}
                          options={field.fieldOptions}
                          className="sm:w-[60%] w-[100%]"
                        />
                      </div>
                    )}
                  </Table.Td>

                  <Table.Td className="!text-[10px] !px-2 md:!px-5 md:!text-[12px]">
                    <div className="flex">
                      <DragAndDrop
                        className="h-8 "
                        accept="image/png, image/jpeg"
                        fileType="JPG/PNG"
                        onChange={(e) => changeHandler(e, fName, index)}
                        multiple
                      />
                      <Preview
                        className="mt-2 ml-2 w-4"
                        onClick={() => {
                          setIsOpen(!isOpen);
                          setCurrField(fName);
                        }}
                      />
                    </div>
                  </Table.Td>
                </Table.Row>
              );
            })}
        </Table.Body>
      </Table>
      {refData?.GenerateQR && (
        <div className="flex flex-row justify-start mt-3">
          <span className="text-md font-semibold mt-2 sm:mr-[23%] mr-[15%] ml-5">
            Generate QR :
          </span>
          <Button
            className="py-2 bg-yellow-700"
            color="green"
            width="sm:w-[20%]"
            type="button"
            onClick={() => {
              setIsOpenQR(true);
            }}
          >
            Show QR
          </Button>
        </div>
      )}
      {refData?.ScanQR && (
        <div className="flex flex-row justify-start mt-3">
          <span className="text-md font-semibold mt-2 mr-[28%] ml-5">
            Scan QR :
          </span>
          <Button
            className="py-2 bg-yellow-500"
            color=""
            width="w-[20%]"
            type="button"
            onClick={() => {
              setIsScan(true);
            }}
          >
            Scan QR
          </Button>
        </div>
      )}
      {formFieldRadio?.map((item, idx) => {
        return (
          <div className="mt-4 flex flex-col" key={idx}>
            <h3 className="pl-6 font-semibold">{item.fieldName} :</h3>
            <div className="ml-[20%] w-[70%] mt-3 ">
              <div className="flex flex-row justify-between ">
                {item.labelArray?.map((itm, i) => {
                  return (
                    <TrComponent
                      className=""
                      option={itm}
                      key={i}
                      label={item.fieldName}
                      formData={formData}
                      setFormData={setFormData}
                      // arr={arr}
                      // temArr={temArr}
                      // settemArr={settemArr}
                    />
                  );
                })}
                <div className="flex w-14 ">
                  <DragAndDrop
                    // className="h-8"
                    className="px-[30%] h-8  text-xs"
                    accept="image/png, image/jpeg"
                    fileType="JPG/PNG"
                    onChange={(e) =>
                      changeHandler(
                        e,
                        item?.fieldName,
                        formFieldNoRadio?.length + idx
                      )
                    }
                    multiple
                  />
                </div>
                <Preview
                  className="w-4 mt-2"
                  onClick={() => {
                    setIsOpen(!isOpen);
                    setCurrField(item?.fieldName);
                  }}
                />
              </div>
            </div>
          </div>
        );
      })}

      {tableFields?.map((field, idx) => {
        return (
          <div className="mt-2 flex flex-col overflow-auto" key={idx}>
            <div className="mt-3">
              <div className="w-full col-span-2">
                <div className="overflow-x-scroll pb-4 col-span-2">
                  <label className="block text-sm font-medium text-gray-700">
                    {field.fieldName}
                  </label>
                  <Table key={idx}>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th className={`text-center`}>Sr. No.</Table.Th>
                        {field?.tableOptions?.column?.map((col, colIndex) => (
                          <Table.Th key={colIndex} className={`text-center`}>
                            {col?.columnName}
                          </Table.Th>
                        ))}
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {Array.from({
                        length:
                          field?.tableOptions?.rows ||
                          0 + formData?.[field?.fieldName]?.newRowNo ||
                          0,
                      }).map((_, bodyRowIndex) => (
                        <Table.Row key={bodyRowIndex}>
                          <Table.Td className={`text-center`}>
                            {formData?.[field?.fieldName]?.newRowNo ? (
                              <>
                                <Input
                                  className={'min-w-24 w-full'}
                                  type="text"
                                  value={
                                    formData?.[field?.fieldName]?.row[
                                      bodyRowIndex
                                    ] || ''
                                  }
                                  onChange={(e) => {
                                    setFormData((prev) => {
                                      return {
                                        ...prev,
                                        [field?.fieldName]: {
                                          ...prev[field?.fieldName],
                                          row: prev?.[
                                            field?.fieldName
                                          ]?.row?.map((itm, index) => {
                                            if (index === bodyRowIndex) {
                                              return e.target.value;
                                            } else {
                                              return itm;
                                            }
                                          }),
                                        },
                                      };
                                    });
                                  }}
                                ></Input>
                              </>
                            ) : (
                              formData?.[field?.fieldName]?.row[bodyRowIndex] ||
                              bodyRowIndex + 1
                            )}
                          </Table.Td>
                          {field?.tableOptions?.column?.map((col, colIndex) => {
                            const rowValues =
                              formData?.[field?.fieldName]?.rowData?.[
                                bodyRowIndex + 1
                              ]?.[colIndex]?.value || '';

                            if (col?.columnType === 'string') {
                              return (
                                <Table.Td
                                  key={colIndex}
                                  className={`text-center`}
                                >
                                  <Input
                                    type="string"
                                    onChange={(e) => {
                                      setFormData((prev) => ({
                                        ...prev,
                                        [field?.fieldName]: {
                                          ...prev[field?.fieldName],
                                          ['rowData']: {
                                            ...prev[field?.fieldName][
                                              'rowData'
                                            ],
                                            [bodyRowIndex + 1]: prev?.[
                                              field?.fieldName
                                            ]?.['rowData']?.[
                                              bodyRowIndex + 1
                                            ]?.map((itm, index) =>
                                              index !== colIndex
                                                ? itm
                                                : {
                                                    ...itm,
                                                    value: e.target.value,
                                                    type: 'string',
                                                  }
                                            ),
                                          },
                                        },
                                      }));
                                    }}
                                    value={
                                      formData[field?.fieldName]?.['rowData']?.[
                                        bodyRowIndex
                                      ]?.value || rowValues
                                    }
                                    className={`min-w-24 w-full`}
                                  />
                                </Table.Td>
                              );
                            } else if (col?.columnType === 'number') {
                              return (
                                <Table.Td
                                  key={colIndex}
                                  className={`text-center`}
                                >
                                  <Input
                                    type="number"
                                    onChange={(e) => {
                                      setFormData((prev) => ({
                                        ...prev,
                                        [field?.fieldName]: {
                                          ...prev[field?.fieldName],
                                          ['rowData']: {
                                            ...prev[field?.fieldName][
                                              'rowData'
                                            ],
                                            [bodyRowIndex + 1]: prev?.[
                                              field?.fieldName
                                            ]?.['rowData']?.[
                                              bodyRowIndex + 1
                                            ]?.map((itm, index) =>
                                              index !== colIndex
                                                ? itm
                                                : {
                                                    ...itm,
                                                    value: e.target.value,
                                                    type: 'number',
                                                  }
                                            ),
                                          },
                                        },
                                      }));
                                    }}
                                    value={
                                      formData[field?.fieldName]?.['rowData']?.[
                                        bodyRowIndex
                                      ]?.value || rowValues
                                    }
                                    className={`min-w-24 w-full`}
                                  />
                                </Table.Td>
                              );
                            } else if (col?.columnType === 'date') {
                              return (
                                <Table.Td
                                  key={colIndex}
                                  className={`text-center`}
                                >
                                  <Input
                                    type="date"
                                    onChange={(e) => {
                                      setFormData((prev) => ({
                                        ...prev,
                                        [field?.fieldName]: {
                                          ...prev[field?.fieldName],
                                          ['rowData']: {
                                            ...prev[field?.fieldName][
                                              'rowData'
                                            ],
                                            [bodyRowIndex + 1]: prev?.[
                                              field?.fieldName
                                            ]?.['rowData']?.[
                                              bodyRowIndex + 1
                                            ]?.map((itm, index) =>
                                              index !== colIndex
                                                ? itm
                                                : {
                                                    ...itm,
                                                    value: e.target.value,
                                                    type: 'date',
                                                  }
                                            ),
                                          },
                                        },
                                      }));
                                    }}
                                    value={
                                      formData[field?.fieldName]?.['rowData']?.[
                                        bodyRowIndex
                                      ]?.value || rowValues
                                    }
                                    className={`min-w-24 w-full`}
                                  />
                                </Table.Td>
                              );
                            } else if (col?.columnType === 'checkbox') {
                              return (
                                <Table.Td
                                  key={colIndex}
                                  className={`text-center`}
                                >
                                  <Input
                                    type="checkbox"
                                    value={
                                      formData[field?.fieldName]?.['rowData']?.[
                                        bodyRowIndex
                                      ]?.value
                                    }
                                    onChange={(e) => {
                                      setFormData((prev) => ({
                                        ...prev,
                                        [field?.fieldName]: {
                                          ...prev[field?.fieldName],
                                          ['rowData']: {
                                            ...prev[field?.fieldName][
                                              'rowData'
                                            ],
                                            [bodyRowIndex + 1]: prev?.[
                                              field?.fieldName
                                            ]?.['rowData']?.[
                                              bodyRowIndex + 1
                                            ]?.map((itm, index) =>
                                              index !== colIndex
                                                ? itm
                                                : {
                                                    ...itm,
                                                    value: e.target.value,
                                                    type: 'checkbox',
                                                  }
                                            ),
                                          },
                                        },
                                      }));
                                    }}
                                    className={`min-w-24 w-full`}
                                  />
                                </Table.Td>
                              );
                            } else {
                              return (
                                <Table.Td key={colIndex}>
                                  <span>Unknown Type</span>
                                </Table.Td>
                              );
                            }
                          })}
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </div>

                <p
                  className={`mt-2 ml-5 ${field?.tableOptions?.rows > 0 ? 'hidden' : ''} text-blue-700 hover:text-blue-500`}
                  onClick={() => {
                    let newRowNo = formData?.[field?.fieldName]?.newRowNo || 0;
                    setFormData((prev) => ({
                      ...prev,
                      [field?.fieldName]: {
                        ...prev[field?.fieldName],
                        row: [...prev?.[field?.fieldName]?.row, newRowNo + 1],
                        newRowNo: prev?.[field?.fieldName]?.newRowNo + 1,
                        ['rowData']: {
                          ...prev[field?.fieldName]['rowData'],
                          [newRowNo + 1]: prev?.[
                            field?.fieldName
                          ]?.columns?.map((item) => ({
                            value: '',
                            type: item?.columnType,
                          })),
                        },
                      },
                    }));
                  }}
                >
                  + Add Row
                </p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default DynamicForm;
