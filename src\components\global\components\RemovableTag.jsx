const RemovableTag = ({ children, onRemove }) => {
  return (
    <span className="px-3 py-1 text-[14px] bg-slate-200 mx-1 my-2 rounded-sm text-blue-500 font-semibold relative">
      {children}
      <button
        className="absolute -top-3 w-[20px] h-[20px] text-[10px] bg-red-500 text-white rounded-full"
        onClick={onRemove}
      >
        X
      </button>
    </span>
  );
};

export default RemovableTag;
