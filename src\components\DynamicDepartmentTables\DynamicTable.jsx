import { DeleteOutlined, SaveOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON> } from 'antd';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import Pagination from '../global/components/Pagination';
import Table from '../global/components/Table';
import TdComponent from './TdComponent';

const DynamicTable = ({
  columns,
  isFetchingGet,
  DepartmentData,
  HighLight,
  calculateIndexForPagination,
  page,
  limit,
  requiresApproval,
  setSteps,
  searchParams,
  setShowRightSideBar,
  setRowMediaData,
  setDepartmentData,
  assets,
  boms,
  setShowModal,
  setSelectedRow,
  setShowFormModal,
  setForm,
  setBomData,
  setShowBom,
  setFormManagementModal,
  setShowCommentModal,
  setSelectedBom,
  setSelectedCol,
  setMediaType,
  setShowHyperLink,
  setFormType,
  allPos,
  setShowFillForm,
  handleStatus,
  handleSaveData,
  handleRowDelete,
  isLoadingDepAdd,
  isLoadingEditDep,
  selectedIndx,
  isDeleteRowLoading,
  totalPages,
  totalResults,
  setSearchParams,
  setSelectedIdx,
  setShown,
  setShow,
}) => {
  return (
    <div className="flex flex-col">
      <Table>
        <Table.Head>
          <Table.Row>
            <Table.Th className="border-r-2 border-gray-200 ">#</Table.Th>
            <Table.Th className={'border-r-2 border-gray-200'}>
              Task Id
            </Table.Th>
            {columns?.map((col) => (
              <Table.Th className="border-r-2 border-gray-200" key={col._id}>
                {col.name}
              </Table.Th>
            ))}
            {requiresApproval && (
              <Table.Th className={'border-r-2 border-gray-200'}>
                Status
              </Table.Th>
            )}
            <Table.Th className="border-r-2 border-gray-200">Action</Table.Th>
            <Table.Th className="border-r-2 border-gray-200">Delete</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {DepartmentData?.map((row, rIdx) => (
            <Table.Row
              key={row?._id}
              isFetching={isFetchingGet}
              className={
                // highlighing the last row when user navigates to this page form the kanban page
                `${HighLight && rIdx === 0 ? '!bg-slate-300' : '!bg-white'} 
                            !overflow-x-scroll`
              }
            >
              <Table.Td>
                {calculateIndexForPagination(
                  page,
                  limit,
                  DepartmentData.length - rIdx - 1
                ) || DepartmentData.length - rIdx}
              </Table.Td>
              <Table.Td
                className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                onClick={() => {
                  if (row?.taskId?.taskId) {
                    setSteps(row?.taskId?.taskId);
                  } else {
                    setSteps(searchParams?.get('taskID'));
                  }
                }}
              >
                {rIdx === 0 && searchParams.get('taskID')
                  ? searchParams.get('taskID')
                  : row?.taskId?.customTaskId
                    ? `${row?.taskId?.customTaskId}(${row?.taskId?.taskId})`
                    : row?.taskId?.taskId || '-'}
              </Table.Td>
              {columns?.map((col) => (
                <TdComponent
                  key={col._id}
                  col={col}
                  row={row}
                  setShowRightSideBar={setShowRightSideBar}
                  setRowMediaData={setRowMediaData}
                  setDepartmentData={setDepartmentData}
                  DepartmentData={DepartmentData}
                  assets={assets}
                  boms={boms}
                  idx={rIdx}
                  showModal={setShowModal}
                  setSelectedRow={setSelectedRow}
                  setShowFormModal={setShowFormModal}
                  setForm={setForm}
                  setBomData={setBomData}
                  setShowBom={setShowBom}
                  setFormManagementModal={setFormManagementModal}
                  setShowModal={setShowCommentModal}
                  setSelectedBom={setSelectedBom}
                  setSelectedCol={setSelectedCol}
                  setMediaType={setMediaType}
                  setShowHyperLink={setShowHyperLink}
                  setFormType={setFormType}
                  allPos={allPos}
                  setShowFillForm={setShowFillForm}
                />
              ))}
              {requiresApproval && (
                <Table.Td className="flex gap-3">
                  {!row?.status ? (
                    <>
                      <Button
                        onClick={() => handleStatus(row, 'approved')}
                        color="green"
                      >
                        Approve
                      </Button>
                      <Button
                        onClick={() => handleStatus(row, 'rejected')}
                        color="red"
                      >
                        Reject
                      </Button>
                    </>
                  ) : (
                    <p
                      className={`capitalize px-3 py-1.5 rounded-full text-white  ${row?.status === 'approved' ? 'bg-green-500' : 'bg-red-500'}`}
                    >
                      {row?.status}
                    </p>
                  )}
                </Table.Td>
              )}

              <Table.Td className="!min-w-[2px]">
                {/* ANCHOR */}
                {(isLoadingDepAdd || isLoadingEditDep) &&
                selectedIndx === rIdx ? (
                  <Spin />
                ) : (
                  <div className="!p-0 !px-1 items-center flex !py-2 !w-fit">
                    <SaveOutlined
                      className="text-green-500 text-xl cursor-pointer"
                      onClick={() => {
                        handleSaveData(row);
                      }}
                      onMouseEnter={() => {
                        {
                          setSelectedIdx(rIdx);
                          setShow(true);
                        }
                      }}
                      data-tooltip-id={`tooltip-${rIdx}`}
                      onMouseLeave={() => setShow(false)}
                    />
                    <ReactTooltip
                      id={`tooltip-${rIdx}`}
                      place="bottom"
                      content="Save"
                    />
                  </div>
                )}
              </Table.Td>
              <Table.Td className={'!min-w-[2px]'}>
                {isDeleteRowLoading && selectedIndx === rIdx ? (
                  <Spin />
                ) : (
                  <div className="!p-0 !px-1 !py-2 !w-fit ">
                    <DeleteOutlined
                      className="text-red-500 text-xl cursor-pointer"
                      data-tooltip-id={`delete-${rIdx}`}
                      onClick={() => handleRowDelete(row._id, rIdx)}
                      onMouseEnter={() => {
                        setSelectedIdx(rIdx);
                        setShown(true);
                      }}
                      onMouseLeave={() => setShown(false)}
                    />
                    <ReactTooltip
                      id={`delete-${rIdx}`}
                      place="bottom"
                      content="Delete"
                    />
                  </div>
                )}
              </Table.Td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>

      <div className="">
        <Pagination
          totalPages={totalPages}
          totalResults={totalResults}
          page={page}
          limit={limit}
          setPage={(val) =>
            setSearchParams(
              (prev) => {
                prev.set('page', val);
                return prev;
              },
              { replace: true }
            )
          }
          setLimit={(val) =>
            setSearchParams(
              (prev) => {
                prev.set('limit', val);
                return prev;
              },
              { replace: true }
            )
          }
        />
      </div>
    </div>
  );
};

export default DynamicTable;
