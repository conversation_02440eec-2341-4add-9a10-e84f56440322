import { useEffect, useState } from 'react';
import {
  <PERSON>,
  AreaChart,
  Bar,
  <PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  YAxis,
} from 'recharts';
import Card from '../../components/global/components/Card';
import { getRandomNumber } from '../../helperFunction';
import DeviceDashboardCard from '../DeviceDashboard/DeviceDashboardCard';

const colorArray = [
  '#8884d8',
  '#82ca9d',
  '#FF6633',
  '#FFFF99',
  '#00B3E6',
  '#E6B333',
  '#FF33FF',
  '#3366E6',
  '#999966',
  '#99FF99',
  '#B34D4D',
  '#80B300',
  '#809900',
  '#E6B3B3',
  '#6680B3',
  '#66991A',
  '#FF99E6',
  '#CCFF1A',
  '#FF1A66',
  '#E6331A',
  '#33FFCC',
  '#66994D',
  '#B366CC',
  '#4D8000',
  '#B33300',
  '#CC80CC',
  '#66664D',
  '#FFB399',
  '#991AFF',
  '#E666FF',
  '#4DB3FF',
  '#1AB399',
  '#E666B3',
  '#33991A',
  '#CC9999',
  '#B3B31A',
  '#00E680',
  '#4D8066',
  '#809980',
  '#E6FF80',
  '#1AFF33',
  '#999933',
  '#FF3380',
  '#CCCC00',
  '#66E64D',
  '#4D80CC',
  '#9900B3',
  '#E64D66',
  '#4DB380',
  '#FF4D4D',
  '#99E6E6',
  '#6666FF',
];

const DeviceDashboardLogAnalytics = () => {
  const [data, setData] = useState([]);
  const [pieData, setPieData] = useState([]);

  useEffect(() => {
    const tempData = [];
    const total = { active: 0, idle: 0 };
    [...Array(24)].forEach((_, idx) => {
      const randNum = getRandomNumber(24);
      const active = randNum === idx ? 0 : getRandomNumber(60);
      const idle = 60 - active;
      total.active += active;
      total.idle += idle;

      tempData.push({
        name: `${idx > 9 ? '' : 0}${idx}`,
        Active: active,
        Idle: idle,
        Status: active > 0 ? 1 : 0,
        Energy: (active / 60) * 20,
      });
    });

    setPieData([
      {
        name: 'Active',
        value: Math.round(total?.active / 60),
      },
      {
        name: 'Idle',
        value: Math.round(total?.idle / 60),
      },
    ]);

    setData(tempData);
  }, []);

  return (
    <section>
      <div className="grid responsive-grid-iot-analytics gap-5">
        <DeviceDashboardCard
          title="Active working hours"
          value={pieData?.[0]?.value}
        />
        <DeviceDashboardCard title="Idle Time" value={pieData?.[1]?.value} />
        <DeviceDashboardCard title="Machine Status" value={'On'} />
        <DeviceDashboardCard title="Machine Health" value={'Normal'} />
      </div>
      <div className="grid md:grid-cols-2 items-center gap-4 mt-4">
        <Card className="flex-1 w-full">
          <h2>Active vs Idle</h2>
          <ResponsiveContainer height={270} width="100%">
            <BarChart data={data} className="flex-1 w-full h-full">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey={'Active'} fill={colorArray?.[0]} />
              <Bar dataKey={'Idle'} fill={colorArray?.[1]} />
            </BarChart>
          </ResponsiveContainer>
        </Card>
        <Card className="flex-1 w-full">
          <h2>Sensor Activity</h2>
          <ResponsiveContainer height={270} width="100%">
            <AreaChart
              data={data}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis dataKey="name" />
              <YAxis />
              <CartesianGrid strokeDasharray="3 3" />
              <Tooltip />
              <Area
                type="monotone"
                dataKey={'Status'}
                stroke="#8884d8"
                fillOpacity={1}
                fill="url(#colorUv)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>
      </div>
      <div className="mt-6 grid md:grid-cols-2 items-center gap-4">
        <Card className="flex-1 w-full">
          <h2>Energy Cost</h2>
          <ResponsiveContainer height={270} width="100%">
            <AreaChart
              data={data}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis dataKey="name" />
              <YAxis />
              <CartesianGrid strokeDasharray="3 3" />
              <Tooltip />
              <Area
                type="monotone"
                dataKey={'Energy'}
                stroke="#8884d8"
                fillOpacity={1}
                fill="url(#colorUv)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>
        <Card className="flex-1 w-full">
          <h2>Opretional hours vs Downtime</h2>
          <ResponsiveContainer width="100%" height={270}>
            <PieChart>
              <Pie
                dataKey="value"
                isAnimationActive={false}
                data={pieData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                fill="#8884d8"
                label
              />
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>
    </section>
  );
};

export default DeviceDashboardLogAnalytics;
