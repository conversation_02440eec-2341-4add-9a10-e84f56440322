import { useEffect, useState } from 'react';
import AddIcon from '../../assets/images/add.png';
import { useGetALlDeviceKipsQuery } from '../../slices/deviceKpiApiSlice';
import Button from '../global/components/Button';
import Select from '../global/components/Select';

const HeatMapThresholds = ({ defaults, setDefaults }) => {
  const [machines, setMachines] = useState([]);
  const [tempDevices, setTempDevices] = useState([]);
  const [devices, setDevices] = useState([]);
  const [kpis, setKpis] = useState([]);
  const [data, setData] = useState({
    machine: '',
    device: '',
    kpi: '',
  });

  const { data: kpiData = {} } = useGetALlDeviceKipsQuery();
  const { deviceKpis = [] } = kpiData;

  useEffect(() => {
    deviceKpis.forEach((kpi) => {
      // sets machine
      setMachines((prev) => {
        const exists = prev.find((item) => item._id === kpi.machine._id);
        if (exists) {
          return prev;
        }
        return [...prev, kpi.machine];
      });
      setTempDevices((prev) => {
        const exists = prev.find((item) => item._id === kpi.device._id);
        if (exists) {
          return prev;
        }
        return [...prev, kpi.device];
      });
    });
  }, [deviceKpis]);

  useEffect(() => {
    if (deviceKpis?.length > 0) {
      if (data?.machine) {
        setDevices([]);
        deviceKpis.forEach((kpi) => {
          const check = kpi.machine._id === data.machine;
          if (!check) return;
          setDevices((prev) => {
            const exists = prev.find((item) => item._id === kpi.device._id);
            if (exists) {
              return prev;
            }
            return [...prev, kpi.device];
          });
        });
      }
      if (data?.device) {
        deviceKpis.forEach((kpi) => {
          const check = kpi.device._id === data.device;
          if (!check) return;
          const test = kpi.device.kpis
            .filter((item) => item.type === 'topic')
            .map((item) => ({ name: item.name, value: item.name }));
          setKpis(test);
        });
      }
    }
  }, [data, deviceKpis]);

  const inputHandler = (e) => {
    setData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const addHandler = () => {
    const exists = defaults?.heatMapThresholds?.find(
      (item) =>
        item.machine === data.machine &&
        item.device === data.device &&
        item.kpi === data.kpi
    );
    setDefaults((prev) => {
      if (exists) return prev;
      return {
        ...prev,
        heatMapThresholds: [...prev.heatMapThresholds, data],
      };
    });
    setData({
      machine: '',
      device: '',
      kpi: '',
    });
  };

  const deleteHandler = (item) => {
    setDefaults((prev) => ({
      ...prev,
      heatMapThresholds: prev.heatMapThresholds.filter(
        (data) =>
          !(
            data.machine === item.machine &&
            data.device === item.device &&
            data.kpi === item.kpi
          )
      ),
    }));
  };

  return (
    <div className=" bg-white">
      <h3 className="text-gray-subHeading">Heatmap Thresholds</h3>

      {defaults?.heatMapThresholds?.length > 0 && (
        <table className="w-1/2 mx-auto mt-2">
          <thead>
            <tr>
              <th className="text-left">Machine</th>
              <th className="text-left">Device</th>
              <th className="text-left">Kpi</th>
              <th className="text-left">Action</th>
            </tr>
          </thead>
          <tbody>
            {defaults.heatMapThresholds?.map((item, iIdx) => (
              <tr key={iIdx}>
                <td>
                  {machines?.find((mac) => mac._id === item.machine)?.machineId}
                </td>
                <td>
                  {
                    tempDevices?.find((div) => div._id === item.device)
                      ?.deviceId
                  }
                </td>
                <td>{item.kpi}</td>
                <td>
                  <button
                    onClick={() => deleteHandler(item)}
                    className="outline-none hover:bg-red-primary hover:text-white text-red-primary px-2 py-0.5 rounded-lg"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      <div className="mt-2 flex flex-col md:flex-row md:items-center justify-between gap-4 md:gap-x-5">
        <Select
          className={''}
          value={data?.machine}
          onChange={inputHandler}
          name="machine"
          options={machines?.map((mac) => ({
            name: mac.machineId,
            value: mac._id,
          }))}
        />
        <Select
          value={data?.device}
          onChange={inputHandler}
          name="device"
          options={devices?.map((div) => ({
            name: div.deviceId,
            value: div._id,
          }))}
        />
        <Select
          value={data?.kpi}
          onChange={inputHandler}
          name="kpi"
          options={kpis}
        />
        <Button
          onClick={addHandler}
          type="button"
          disabled={!data.device || !data.machine || !data.kpi}
          className={'w-1/5 !h-8 !text-xs'}
          icon={
            <img
              src={AddIcon}
              alt="Add Icon"
              className="object-contain relative  w-4 h-4"
            />
          }
        >
          Add
        </Button>
      </div>
    </div>
  );
};

export default HeatMapThresholds;
