import { Drawer } from 'antd';

const SlidePanel = ({ isOpen, onClose, children }) => {
  return (
    <Drawer
      placement="left"
      onClose={onClose}
      open={isOpen}
      width={400}
      className="navigation-drawer"
      mask={{ background: 'rgba(0, 0, 0, 0.3)' }}
      style={{
        top: '3.3rem',
        position: 'absolute',
        height: 'calc(100vh - 3.3rem)',
      }}
      closable={false}
    >
      {children}
    </Drawer>
  );
};
export default SlidePanel;
