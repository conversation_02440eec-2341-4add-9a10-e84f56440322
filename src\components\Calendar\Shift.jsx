import SaveIcon from '../../assets/images/save_proceed.png';
import Button from '../global/components/Button';
import Input from '../global/components/Input';

const Shift = ({ defaults, setDefaults, submitHandler }) => {
  const inputHandler = (e) => {
    setDefaults((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  return (
    <div className="w-full mb-5">
      <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
        <div className="flex flex-col my-5">
          <label className="mb-1 font-semibold text-[#667085]">
            Shift Start{' '}
          </label>
          <Input
            type="time"
            name="shiftStart"
            id="shiftStart"
            value={defaults.shiftStart}
            onChange={inputHandler}
            className={`w-full`}
          />
        </div>

        <div className="flex flex-col my-5">
          <label className="mb-1 font-semibold text-[#667085]">
            Shift Stop{' '}
          </label>
          <Input
            type="time"
            name="shiftStop"
            id="shiftStop"
            value={defaults.shiftStop}
            onChange={inputHandler}
            className={`w-full`}
          />
        </div>
      </div>
      <div className="w-full flex justify-end">
        <Button
          className="text-center text-sm bg-green-600 hover:brightness-105 text-white rounded-[8px] p-4"
          onClick={submitHandler}
        >
          <img
            src={SaveIcon}
            alt="Save Icon"
            className="w-5 h-5 object-contain relative"
          />
          Save
        </Button>
      </div>
    </div>
  );
};

export default Shift;
