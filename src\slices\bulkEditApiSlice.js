import { PAGINATION_LIMIT } from '../utils/Constant';
import { apiSlice } from './apiSlice';

const baseRoute = '/v1/bulkedit';

const bulkEditApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getRows: builder.query({
      query: ({
        page = 1,
        limit = PAGINATION_LIMIT,
        model = '',
        isNested = false,
      }) =>
        `${baseRoute}?model=${model}&page=${page}&limit=${limit}&isNested=${isNested}`,
    }),
    getNestedRows: builder.query({
      query: ({ model = '', parentId = '' }) =>
        `${baseRoute}/nested?model=${model}&parentId=${parentId}`,
    }),
    getOptions: builder.mutation({
      query: ({ data }) => ({
        url: baseRoute,
        method: 'PATCH',
        body: data,
      }),
    }),
  }),
});

export const { useGetRowsQuery, useGetOptionsMutation, useGetNestedRowsQuery } =
  bulkEditApiSlice;
