import { Button, Checkbox, Dropdown, Input } from 'antd';
import { BookText, Eye, FileAudio, Link, Table2, Upload } from 'lucide-react';
import { useState } from 'react';
import { FaLock } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { useGetBomsForWoOptionsQuery } from '../../slices/assemblyBomApiSlice';
import { useGetAllAssetsForOptionsQuery } from '../../slices/assetApiSlice';
import { useGetPosForOptionsQuery } from '../../slices/createPoApiSlice';
import Attachments from '../DynamicDepartmentTables/Attachments';
import AudioAttachments from '../DynamicDepartmentTables/AudioAttachments';
import FormManagementFillModal from '../DynamicDepartmentTables/FormManagementFillModal';
import FormManagementModal from '../DynamicDepartmentTables/FormManagementModal';
import LinkModal from '../DynamicDepartmentTables/LinkModal';
import TableConfigModal from '../DynamicDepartmentTables/TableConfigModal';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import { sanitizeColName } from './DynamicDepTableUtilityFunc';

const RenderDepFields = ({
  column,
  rowData,
  setRowData,
  index,
  setDeletedMedia,
  setDeletedAudio,
  setDeletedForms,
  deletedAudio,
  deletedMedia,
  deletedForms,
}) => {
  const [audioModal, setAudioModal] = useState(false);
  const [createFormModal, setCreateFormModal] = useState(false);
  const [fillFormModal, setFillFormModal] = useState(false);
  const [linkModal, setLinkModal] = useState(false);
  const [mediaModal, setMediaModal] = useState(false);
  const [openTableModals, setOpenTableModals] = useState([]);
  let userId = JSON.parse(localStorage.getItem('user'))?.user?._id;

  //  Queries
  const { data: posForOptions } = useGetPosForOptionsQuery();
  const { data: boms } = useGetBomsForWoOptionsQuery();
  const { data: assets } = useGetAllAssetsForOptionsQuery();

  const navigate = useNavigate();

  const handleView = (link, id) => {
    navigate(`${link}?id=${id}&depEdit=true&depPath=${location.pathname}`);
  };

  const handleWorkOrderView = ({ key }) => {
    handleView('/jobs/workorder', key);
  };

  const toggleModal = (index, state) => {
    setOpenTableModals((prev) => {
      const newModals = [...prev];
      newModals[index] = state;
      return newModals;
    });
  };

  const changeHandler = (column, value, isMedia) => {
    const colName = sanitizeColName(column?.name);
    if (isMedia) {
      setRowData((prev) => ({
        ...prev,
        [colName]: {
          value: [
            ...(prev?.[sanitizeColName(column?.name)]?.value || []),
            value,
          ],
          type: column?.type,
          originalKey: column?.name, // We Are Saving Original Key to Show The Originbal Name Of Column on UI also in PDF
        },
      }));
    } else {
      setRowData((prev) => ({
        ...prev,
        [colName]: {
          value,
          type: column?.type,
          originalKey: column?.name, // We Are Saving Original Key to Show The Originbal Name Of Column on UI also in PDF
        },
      }));
    }
  };

  const formFillChangeHandler = (value) => {
    changeHandler(
      {
        ...column,
        type: `${column?.type}-${column?.formType}`,
      },
      value
    );
  };

  const formCreateChangeHandler = (value) => {
    changeHandler(
      {
        ...column,
        type: `${column?.type}-${column?.formType}`,
      },
      value
    );
  };

  const linkChangeHandler = (val) => {
    changeHandler(column, val);
  };
  const audioChangeHandler = (value) => {
    changeHandler(column, value);
  };
  let columnUsers = column?.users;
  let isVisible = true;
  if (column?.users?.length !== 0) {
    isVisible = columnUsers?.includes(userId);
  }
  let items = [];
  let selectedItems = [];
  if (isVisible) {
    if (column?.type === 'work order') {
      let rowVal = rowData?.[sanitizeColName(column?.name)]?.value;
      let ids = rowVal?.map((elem) => (elem?.value ? elem?.value : elem));
      if (posForOptions) {
        for (let i of posForOptions) {
          if (ids?.includes(i?._id)) {
            selectedItems?.push({
              label: i?.name,
              value: i?._id,
            });
            items?.push({
              label: i?.name,
              key: i?._id,
            });
          }
        }
      }
    }
    switch (column?.type) {
      case 'table':
        return (
          <div>
            {openTableModals?.[index] && (
              <TableConfigModal
                openModal={openTableModals?.[index]}
                setOpenModal={(state) => toggleModal(index, state)}
                tableConfig={column?.tableConfig}
                changeHandler={changeHandler}
                column={column}
                rowData={rowData}
              />
            )}
            <div className="flex gap-x-1 items-center">
              <Button
                className="w-full"
                icon={<Table2 size={15} />}
                onClick={() => toggleModal(index, true)}
              >
                Fill Table
              </Button>
              {/* <FaCircleCheck
              className={`${rowData?.[sanitizeColName(column?.name)]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
            /> */}
            </div>
          </div>
        );
      case 'number':
        return (
          <div>
            <Input
              value={rowData?.[sanitizeColName(column?.name)]?.value}
              type="number"
              placeholder="Enter a number"
              onChange={(e) => {
                changeHandler(column, e.target.value);
              }}
            />
          </div>
        );
      case 'string':
        return (
          <div>
            <Input
              value={rowData?.[sanitizeColName(column?.name)]?.value}
              placeholder="Enter a string"
              onChange={(e) => {
                changeHandler(column, e.target.value);
              }}
            />
          </div>
        );
      case 'checkbox':
        return (
          <div className="float-left">
            <Checkbox
              checked={rowData?.[sanitizeColName(column?.name)]?.value}
              type="checkbox"
              onChange={(e) => {
                changeHandler(column, e.target.checked);
              }}
            />
          </div>
        );

      case 'select':
        return (
          <div>
            <Select
              value={rowData?.[sanitizeColName(column?.name)]?.value}
              onChange={(e) => {
                changeHandler(column, e.target.value);
              }}
              options={column?.values?.map((elem) => ({
                label: elem,
                value: elem,
              }))}
            />
          </div>
        );

      case 'multiCheckbox':
        return (
          <div>
            <MultiSelect
              value={rowData?.[sanitizeColName(column?.name)]?.value}
              onChange={(e) => {
                const updatedValues = e.target.value;
                changeHandler(column, updatedValues);
              }}
              options={posForOptions
                ?.map((elem) => ({
                  label: elem?.name,
                  value: elem?._id,
                }))
                ?.reverse()}
            />
          </div>
        );
      case 'dropdown':
        return (
          <div>
            <MultiSelect
              value={rowData?.[sanitizeColName(column?.name)]?.value || []}
              onChange={(e) => {
                changeHandler(column, e.target.value);
              }}
              options={column?.values?.map((elem) => ({
                label: elem,
                value: elem,
              }))}
            />
          </div>
        );
      case 'media':
        return (
          <div>
            <Attachments
              mediaModal={mediaModal}
              setMediaModal={setMediaModal}
              changeHandler={changeHandler}
              column={column}
              rowData={rowData}
              setDeletedMedia={setDeletedMedia}
              deletedMedia={deletedMedia}
            />
            <div className="flex gap-x-1 items-center">
              <Button
                className={`w-full`}
                onClick={() => {
                  setMediaModal(true);
                }}
                icon={<Upload size={15} />}
              >
                Add Media
              </Button>
              {/* <FaCircleCheck
              className={`${rowData?.[sanitizeColName(column?.name)]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
            /> */}
            </div>
          </div>
        );
      case 'bom':
        return (
          <div>
            <MultiSelect
              value={rowData?.[sanitizeColName(column?.name)]?.value || []}
              onChange={(e) => {
                changeHandler(column, e.target.value);
              }}
              options={boms
                ?.map((elem) => ({
                  label: elem?.name,
                  value: elem?._id,
                }))
                ?.reverse()}
            />
          </div>
        );
      case 'assets':
        return (
          <div>
            <MultiSelect
              value={rowData?.[sanitizeColName(column?.name)]?.value || []}
              onChange={(e) => {
                changeHandler(column, e.target.value);
              }}
              options={assets
                ?.map((elem) => ({
                  label: elem?.name,
                  value: elem?._id,
                }))
                ?.reverse()}
            />
          </div>
        );
      case 'date':
        return (
          <div>
            <Input
              type="date"
              value={rowData?.[sanitizeColName(column?.name)]?.value}
              onChange={(e) => {
                changeHandler(column, e.target.value);
              }}
            />
          </div>
        );
      case 'forms':
        return (
          <div>
            <FormManagementFillModal
              open={fillFormModal}
              setOpen={setFillFormModal}
              changeHandler={formFillChangeHandler}
              value={rowData?.[sanitizeColName(column?.name)]?.value}
              form={column?.form}
            />
            <div className="flex gap-x-1 items-center">
              <Button
                className="w-full"
                icon={<BookText size={15} />}
                onClick={() => setFillFormModal(true)}
              >
                Fill Form
              </Button>
              {/* <FaCircleCheck
              className={`${rowData?.[sanitizeColName(column?.name)]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
            /> */}
            </div>
          </div>
        );
      case 'form management':
        if (column?.formType === 'create') {
          return (
            <div>
              <FormManagementModal
                formType="create"
                open={createFormModal}
                setOpen={setCreateFormModal}
                changeHandler={formCreateChangeHandler}
                column={column}
                value={rowData?.[sanitizeColName(column?.name)]?.value}
                deletedForms={deletedForms}
                setDeletedForms={setDeletedForms}
              />
              <div className="flex gap-x-1 items-center">
                <Button
                  className="w-full"
                  icon={<BookText size={15} />}
                  onClick={() => setCreateFormModal(true)}
                >
                  Create Form
                </Button>
                {/* <FaCircleCheck
                className={`${rowData?.[sanitizeColName(column?.name)]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
              /> */}
              </div>
            </div>
          );
        } else {
          return (
            <div>
              <FormManagementFillModal
                open={fillFormModal}
                setOpen={setFillFormModal}
                changeHandler={formFillChangeHandler}
                value={rowData?.[sanitizeColName(column?.name)]?.value}
              />
              <div className="flex gap-x-1 items-center">
                <Button
                  className="w-full"
                  onClick={() => setFillFormModal(true)}
                >
                  Fill Form
                </Button>
                {/* <FaCircleCheck
                className={`${rowData?.[sanitizeColName(column?.name)]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
              /> */}
              </div>
            </div>
          );
        }
      case 'hyperlink':
        return (
          <div>
            <LinkModal
              open={linkModal}
              setOpen={setLinkModal}
              value={rowData?.[sanitizeColName(column?.name)]?.value}
              changeHandler={linkChangeHandler}
            />
            <div className="flex gap-x-1 items-center">
              <Button
                className="w-full"
                icon={<Link size={15} />}
                onClick={() => setLinkModal(true)}
              >
                Add Links
              </Button>
              {/* <FaCircleCheck
              className={`${rowData?.[sanitizeColName(column?.name)]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'} text-xl`}
            /> */}
            </div>
          </div>
        );
      case 'audio':
        return (
          <div>
            <AudioAttachments
              mediaModal={audioModal}
              setMediaModal={setAudioModal}
              changeHandler={audioChangeHandler}
              column={column}
              rowData={rowData}
              setDeletedMedia={setDeletedAudio}
              deletedMedia={deletedAudio}
            />
            <div className="flex gap-x-1 items-center">
              <Button
                className="w-full"
                icon={<FileAudio size={15} />}
                onClick={() => {
                  setAudioModal(true);
                }}
              >
                Add Audio
              </Button>
              {/* <FaCircleCheck
              className={`${rowData?.[sanitizeColName(column?.name)]?.value?.length > 0 ? 'opacity-100 text-green-500' : 'text-slate-500'}  text-xl`}
            /> */}
            </div>
          </div>
        );
      case 'work order':
        return (
          <div>
            <div className="flex gap-x-1 items-center">
              <MultiSelect
                value={selectedItems}
                onChange={(e) => {
                  changeHandler(column, e.target.value);
                }}
                options={posForOptions
                  ?.map((elem) => ({
                    label: elem?.name,
                    value: elem?._id,
                  }))
                  ?.reverse()}
              />
              <Dropdown menu={{ items, onClick: handleWorkOrderView }}>
                <Button
                  type={
                    rowData?.[sanitizeColName(column?.name)]?.value?.length > 0
                      ? 'primary'
                      : 'default'
                  }
                  disabled={
                    rowData?.[sanitizeColName(column?.name)]?.value?.length > 0
                      ? false
                      : true
                  }
                  icon={<Eye size={20} />}
                />
              </Dropdown>
            </div>
          </div>
        );
    }
  } else {
    return <FaLock size={20} color="gray" />;
  }
};

export default RenderDepFields;
