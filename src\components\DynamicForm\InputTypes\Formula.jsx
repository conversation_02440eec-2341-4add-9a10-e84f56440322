import useFieldsAndTitle from '../../../hooks/useFieldsAndTitle';

const Formula = ({ data, state, horIdx, verIdx, type }) => {
  const [horizontalFields, verticalFields, title] = useFieldsAndTitle(data);
  const { inputData, renderState } = state;

  return (
    <>
      {renderState && (
        <div
          className={`flex border rounded items-center text-gray-700 leading-nomal w-full ${
            verticalFields.length > 1 && verticalFields.length - 1 !== verIdx
              ? ' mb-2'
              : ''
          }`}
        >
          <input
            key={data.Parameter + title[horIdx] + verIdx.toString()}
            type="number"
            readOnly
            name={
              horizontalFields.length > 1 && verticalFields.length > 1
                ? `${data.Parameter}-${title[horIdx]}${verIdx.toString()}-hv`
                : horizontalFields.length > 1
                ? `${data.Parameter}-${title[horIdx]}-h`
                : verticalFields.length > 1
                ? `${data.Parameter}-${verIdx.toString()}-v`
                : data.Parameter
            }
            value={
              (horizontalFields.length > 1 && verticalFields.length > 1
                ? inputData?.[data.Parameter]?.value?.[
                    title[horIdx] + verIdx.toString()
                  ]
                : horizontalFields?.length > 1
                ? inputData?.[data.Parameter]?.value?.[title[horIdx]]
                : verticalFields?.length > 1
                ? inputData?.[data.Parameter]?.value?.[verIdx]
                : inputData?.[data.Parameter]?.value) || ''
            }
            className={`appearance-none rounded py-0.5 px-3 text-sm text-gray-700 leading-nomal focus:outline-none focus:ring-2 ${
              data.Units !== '' ? ' w-4/5' : ' w-full border'
            } `}
            placeholder={type}
          />
          {data.Units && (
            <span className="text-md w-1/5 h-fit font-normal text-sm text-center">
              {data.Units}
            </span>
          )}
        </div>
      )}
    </>
  );
};

export default Formula;
