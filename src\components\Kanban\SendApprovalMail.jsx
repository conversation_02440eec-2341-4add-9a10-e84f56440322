import { useState } from 'react';
import SendMail from '../v3/global/components/SendMail';
import { useSendPurchaseOrderMutation } from '../../slices/purchaseOrderApiSlice';
import { toast } from 'react-toastify';
import { useSendQuotationMutation } from '../../slices/quotationApiSlice';
import { useSendSalesOrderMutation } from '../../slices/salesOrderSlices';

const SendApprovalMail = ({
  selectedtab,
  data,
  setShowEmailModal,
  isMobile,
  isTablet,
}) => {
  const [mailData, setMailData] = useState({
    receiver: '',
    body: '',
    subject: '',
    input: {},
    attachments: [],
  });
  const [SendingMail, setSendingMail] = useState(false);
  const [sendMail] = useSendPurchaseOrderMutation();
  const [sendQuotationMail] = useSendQuotationMutation();
  const [sendSalesOrder] = useSendSalesOrderMutation();

  const handleSendMail = async () => {
    const fd = new FormData();
    fd.append('receiver', mailData?.receiver);
    fd.append('subject', mailData?.subject);
    fd.append('body', mailData?.body);
    if (mailData?.attachments?.length !== 0) {
      mailData?.attachments?.forEach((file) => {
        fd.append('attachments', file);
      });
    }
    setSendingMail(true);
    switch (selectedtab) {
      case 'purchaseOrders': {
        fd.append(
          'input',
          JSON.stringify({
            productDetails: data?.items,
            data: data,
            vendor: data?.vendor,
            po: data,
          })
        );
        await sendMail(fd)
          .unwrap()
          .finally(() => {
            setSendingMail(false);
          });
        setShowEmailModal(false);
        break;
      }
      case 'salesQuotations': {
        fd.append('input', JSON.stringify(data));
        await sendQuotationMail(fd)
          .unwrap()
          .finally(() => {
            setSendingMail(false);
          });
        setShowEmailModal(false);
        break;
      }
      case 'salesOrders': {
        fd.append('input', JSON.stringify(data));
        await sendSalesOrder(fd);
        setSendingMail(false);
        setShowEmailModal(false);
        break;
      }
    }
    toast.success('Mail Sent Successfully');
  };

  return (
    <SendMail
      isMobile={isMobile}
      isTablet={isTablet}
      mailData={mailData}
      setMailData={setMailData}
      setShowEmailModal={setShowEmailModal}
      SendingMail={SendingMail}
      handleSendmail={handleSendMail}
    />
  );
};

export default SendApprovalMail;
