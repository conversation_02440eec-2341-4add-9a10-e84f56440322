import { DebounceInput } from 'react-debounce-input';
import useFieldsAndTitle from '../../../hooks/useFieldsAndTitle';

const Date = ({ data, state, horIdx, verIdx, type, isDisabled }) => {
  const [horizontalFields, verticalFields, title] = useFieldsAndTitle(data);
  const { inputData, handleChange, renderState } = state;

  return (
    <>
      {renderState && (
        <DebounceInput
          debounceTimeout={500}
          key={data.Parameter + title[horIdx] + verIdx.toString()}
          type="date"
          name={
            horizontalFields.length > 1 && verticalFields.length > 1
              ? `${data.Parameter}-${title[horIdx]}${verIdx.toString()}-hv`
              : horizontalFields.length > 1
              ? `${data.Parameter}-${title[horIdx]}-h`
              : verticalFields.length > 1
              ? `${data.Parameter}-${verIdx.toString()}-v`
              : data.Parameter
          }
          value={
            (horizontalFields.length > 1 && verticalFields.length > 1
              ? inputData?.[data.Parameter]?.value?.[
                  title[horIdx] + verIdx.toString()
                ]
              : horizontalFields.length > 1
              ? inputData?.[data.Parameter]?.value?.[title[horIdx]]
              : verticalFields.length > 1
              ? inputData?.[data.Parameter]?.value?.[verIdx]
              : inputData?.[data.Parameter]?.value) || ''
          }
          onChange={handleChange}
          className={`appearance-none border rounded w-full py-0.5 px-3 text-sm text-gray-700 leading-nomal focus:outline-none  focus:ring-2 w-full${
            verticalFields.length > 1 && verticalFields.length - 1 !== verIdx
              ? ' mb-2'
              : ''
          }`}
          placeholder={type}
          disabled={isDisabled}
        />
      )}
    </>
  );
};

export default Date;
