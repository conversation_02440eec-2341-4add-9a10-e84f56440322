import { useContext, useEffect } from 'react';
import {
  generateGoalsData,
  generateGoalsTable,
  isObjectEmpty,
} from '../../../helperFunction';
import { Store } from '../../../store/Store';
import Input from '../../global/components/Input';
import Toggle from '../../global/components/Toggle';
import { MultiplierModal } from './MultiplierModal';
import ParamsAndInput from './ParamsAndInput';

export const subtractReserved = (data) => {
  if (!data?.quantity) return 0;
  const diff = data?.quantity - (data?.reserved || 0);
  return (diff <= 0 ? 0 : diff) || 0;
};

const Details = ({
  selectedtemplateId = '',
  selectProductionFlows = '',
  isTemplateStart = false,
  goalsData,
  setGoalsData,
  LinkFieldOptions,
  linkFieldData,
  setLinkFieldData,
  productionFlow,
  multiProcessTablesData,
  goalsTables,
  setGoalsTables,
  projectStatus,
  isEdit,
  jobPlanningDetails,
  setJobPlanningDetails,
  selectedPoData,
  selectedPo,
  bomAndAssemblyItem,
  openMultiplierModal,
  setOpenMultiplierModal,
  assemblyItem,
}) => {
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const singleGoalsData = goalsData?.[Object.keys(goalsData || {})?.[0]];

  const goalsKeys = Object.keys(singleGoalsData || {})?.filter(
    (i) => i !== 'Speed' && i !== 'Time'
  );

  const totalChildren = (children = []) => {
    const totalChildren = [];

    const recurse = (children) => {
      children.forEach((child) => {
        totalChildren.push(child);
        if (child.children) {
          recurse(child.children);
        }
      });
    };

    recurse(children);
    return totalChildren;
  };

  const generateChildToParent = (currentItem, totalChildrenArray) => {
    let childToParent = [];

    let currItem = currentItem;
    childToParent.push(currentItem);
    while (currItem?.parent !== null) {
      let prevItem = totalChildrenArray?.find(
        (item) => item?._id === currItem?.parent
      );
      childToParent = [...childToParent, prevItem];
      currItem = prevItem;
    }

    return childToParent;
  };

  useEffect(() => {
    const data = selectedPoData;

    if (!isObjectEmpty(data)) {
      setJobPlanningDetails((prev) => ({
        ...prev,
        deadline: data?.deadline?.split('T')[0],
      }));
      let orderQuantity = 0;

      if (data?.type === 'Assembly') {
        const filteredBOM = data?.bom?.find(
          (item) => item?._id === bomAndAssemblyItem?.correspondingBom
        );

        orderQuantity = data?.orderQuantity?.[filteredBOM?._id] || 0;

        let totalChildrenArray = totalChildren(filteredBOM?.children) || [];
        let currentItem = totalChildrenArray?.find(
          (item) => item?._id === bomAndAssemblyItem?.assemblyItem?._id
        );

        if (currentItem && filteredBOM) {
          let childToParent = generateChildToParent(currentItem, [
            ...totalChildrenArray,
            filteredBOM,
          ]);

          for (let i = childToParent.length - 2; i >= 0; i--) {
            let item = childToParent[i];
            if (item?.manualEntry) {
              orderQuantity = orderQuantity * item?.units;
            } else {
              orderQuantity =
                (orderQuantity - subtractReserved(item)) * item?.units;
            }
          }
        }
      } else if (data?.type === 'Inhouse') {
        setJobPlanningDetails((prev) => ({
          ...prev,
          deadline: data?.deadline?.split('T')[0],
        }));
        const filteredItem = data?.inhouse?.find(
          (item) => item?._id === bomAndAssemblyItem?.assemblyItem?._id
        );
        if (filteredItem) {
          orderQuantity = data?.inhouseOrderQuantity || 0;
          if (filteredItem?.manualEntry) {
            orderQuantity = orderQuantity * filteredItem?.units;
          } else {
            orderQuantity =
              (orderQuantity - subtractReserved(filteredItem.part)) *
              filteredItem?.units;
          }
        }
      } else {
        setJobPlanningDetails((prev) => ({
          ...prev,
          deadline: data?.deadline?.split('T')[0],
        }));
        const filteredItem = data?.items?.find(
          (item) =>
            item?._id === bomAndAssemblyItem?.assemblyItem?._id || assemblyItem
        );

        if (filteredItem) {
          orderQuantity = data?.orderQuantity || 0;
          if (filteredItem?.manualEntry) {
            orderQuantity = orderQuantity * filteredItem?.units;
          } else {
            orderQuantity =
              (orderQuantity - subtractReserved(filteredItem.itemId)) *
              filteredItem?.units;
          }
        }
      }

      const tempGoalsData = generateGoalsData(
        goalsData,
        'Order Quantity',
        orderQuantity,
        false
      );

      setGoalsData(tempGoalsData);
    }
  }, [selectedPo, selectedPoData, bomAndAssemblyItem, assemblyItem]); // eslint-disable-line

  const handleGoalsDataChange = (e) => {
    const { name, value } = e.target;

    const tempGoalsData = generateGoalsData(
      goalsData,
      name,
      value,
      jobPlanningDetails?.isSingleBatch
    );

    const tempGoalsTables = generateGoalsTable(
      tempGoalsData,
      productionFlow,
      defaultParam,
      multiProcessTablesData,
      goalsTables
    );

    if (isEdit) {
      setGoalsTables((prev) =>
        prev.map((i, idx) => ({ ...i, ...(tempGoalsTables?.[idx] || {}) }))
      );
    } else {
      setGoalsTables(tempGoalsTables);
    }

    setGoalsData(tempGoalsData);
  };

  useEffect(() => {
    if (isTemplateStart && !jobPlanningDetails?.isSingleBatch) {
      handleSingleBatchToggle();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTemplateStart, selectProductionFlows, selectedtemplateId]);

  const handleSingleBatchToggle = () => {
    if (!jobPlanningDetails?.isSingleBatch) {
      const tempGoalsData = generateGoalsData(
        goalsData,
        'Order Quantity',
        +singleGoalsData?.['Order Quantity'] || 0,
        true
      );

      const tempGoalsTables = generateGoalsTable(
        tempGoalsData,
        productionFlow,
        defaultParam,
        multiProcessTablesData,
        goalsTables
      );

      if (isEdit) {
        setGoalsTables((prev) =>
          prev.map((i, idx) => ({
            ...i,
            ...(tempGoalsTables?.[idx] || {}),
          }))
        );
      } else {
        setGoalsTables(tempGoalsTables);
      }

      setGoalsData(tempGoalsData);
    }

    setJobPlanningDetails((prev) => ({
      ...prev,
      isSingleBatch: !prev?.isSingleBatch,
    }));
  };

  return (
    <>
      {openMultiplierModal && (
        <MultiplierModal
          onCloseModal={setOpenMultiplierModal}
          productionFlow={productionFlow}
          goalsTables={goalsTables}
          setGoalsTables={setGoalsTables}
        />
      )}

      {isTemplateStart ? (
        <>
          <div className="mt-2 ">
            <div className=" grid  grid-cols-2 gap-3">
              <section className="flex flex-col  ">
                <label className="text-gray-900 mb-2 font-semibold">
                  Deadline:
                </label>
                <Input
                  disabled={isEdit && projectStatus !== 'notStarted'}
                  type="date"
                  value={jobPlanningDetails?.deadline}
                  onChange={(e) =>
                    setJobPlanningDetails((prev) => ({
                      ...prev,
                      deadline: e.target.value,
                    }))
                  }
                />
              </section>

              <div className="flex mt-3">
                <section className="flex flex-col w-full items-center md:items-start ">
                  <label className="text-gray-900 mb-2 font-semibold ">
                    Single Batch:
                  </label>
                  <Toggle
                    disabled={isEdit && projectStatus !== 'notStarted'}
                    className="mt-1.5"
                    value={jobPlanningDetails?.isSingleBatch}
                    onChange={handleSingleBatchToggle}
                  />
                </section>
                <section className="flex flex-col w-full">
                  <label className="text-gray-900 mb-2 font-semibold">
                    Sequential:
                  </label>
                  <Toggle
                    disabled={isEdit && projectStatus !== 'notStarted'}
                    className="mt-1.5"
                    value={jobPlanningDetails?.isSequential}
                    onChange={() =>
                      setJobPlanningDetails((prev) => ({
                        ...prev,
                        isSequential: !prev?.isSequential,
                      }))
                    }
                  />
                </section>
              </div>
            </div>

            <div
              className={`w-full grid  gap-1 mt-7 ${jobPlanningDetails?.isSingleBatch ? 'grid-cols-2' : 'grid-cols-4'} `}
            >
              {goalsKeys?.map((gKey, gIdx) => {
                if (
                  (gKey === 'Number of Batches' ||
                    gKey === 'Max Size of Batch' ||
                    gKey === 'Batch Size') &&
                  jobPlanningDetails?.isSingleBatch
                )
                  return null;
                return (
                  <ParamsAndInput
                    key={gIdx}
                    gKey={gKey}
                    handleGoalsDataChange={handleGoalsDataChange}
                    singleGoalsData={singleGoalsData}
                    LinkFieldOptions={LinkFieldOptions}
                    linkFieldData={linkFieldData}
                    setLinkFieldData={setLinkFieldData}
                    projectStatus={projectStatus}
                    isEdit={isEdit}
                  />
                );
              })}
            </div>
          </div>
        </>
      ) : (
        <div className="w-full h-full">
          <div className="w-full grid grid-cols-2 md:grid-cols-3 gap-4 px-8 mt-5">
            <section className="flex flex-col w-full">
              <label className="text-gray-900 mb-2 font-semibold">
                Deadline:
              </label>
              <Input
                disabled={isEdit && projectStatus !== 'notStarted'}
                type="date"
                value={jobPlanningDetails?.deadline}
                onChange={(e) =>
                  setJobPlanningDetails((prev) => ({
                    ...prev,
                    deadline: e.target.value,
                  }))
                }
              />
            </section>
            <section className="flex flex-col w-full items-center md:items-start ">
              <label className="text-gray-900 mb-2 font-semibold ">
                Single Batch:
              </label>
              <Toggle
                disabled={isEdit && projectStatus !== 'notStarted'}
                className="mt-1.5"
                value={jobPlanningDetails?.isSingleBatch}
                onChange={handleSingleBatchToggle}
              />
            </section>
            <section className="flex flex-col w-full">
              <label className="text-gray-900 mb-2 font-semibold">
                Sequential:
              </label>
              <Toggle
                disabled={isEdit && projectStatus !== 'notStarted'}
                className="mt-1.5"
                value={jobPlanningDetails?.isSequential}
                onChange={() =>
                  setJobPlanningDetails((prev) => ({
                    ...prev,
                    isSequential: !prev?.isSequential,
                  }))
                }
              />
            </section>
          </div>
          <div className="w-full grid grid-cols-4 gap-4 px-8">
            {goalsKeys?.map((gKey, gIdx) => {
              if (
                (gKey === 'Batch Size' || gKey === 'Max Size of Batch') &&
                jobPlanningDetails?.isSingleBatch
              )
                return null;
              return (
                <ParamsAndInput
                  key={gIdx}
                  gKey={gKey}
                  handleGoalsDataChange={handleGoalsDataChange}
                  singleGoalsData={singleGoalsData}
                  LinkFieldOptions={LinkFieldOptions}
                  linkFieldData={linkFieldData}
                  setLinkFieldData={setLinkFieldData}
                  projectStatus={projectStatus}
                  isEdit={isEdit}
                />
              );
            })}
          </div>
        </div>
      )}
    </>
  );
};

export default Details;
