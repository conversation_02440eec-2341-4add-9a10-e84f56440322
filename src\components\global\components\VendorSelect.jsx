import { useState } from 'react';
import useDebounceValue from '../../../hooks/useDebounceValue';
import { useGetAllVendorsForOptionsQuery } from '../../../slices/vendorApiSlice';
import MultiSelect from './MultiSelect';

const VendorSelect = ({
  preferredVendors,
  changeHandler,
  inputClass,
  value,
}) => {
  const [VendorSearch, setVendorSearch] = useState('');
  const debounceVendorSearch = useDebounceValue(VendorSearch);
  const { data: vendorsData } = useGetAllVendorsForOptionsQuery(
    debounceVendorSearch || '',
    {
      refetchOnMountOrArgChange: true,
    }
  );

  return (
    <>
      <MultiSelect
        value={[vendorsData?.find((elem) => elem?.value === value)] || []}
        placeholder="Select Vendors"
        onChange={changeHandler}
        name="vendors"
        closeMenuOnSelect={false}
        preferredOptions={
          vendorsData
            ?.filter((elem) => preferredVendors?.includes(elem?.value))
            ?.map((e) => ({ value: e.value, label: e.name })) || []
        }
        options={
          vendorsData
            ?.filter((elem) => !preferredVendors?.includes(elem?.value))
            ?.map((e) => ({ value: e.value, label: e.name })) || []
        }
        onSearch={setVendorSearch}
        doSearch={false}
        className={`${inputClass} focus:ring-blue-500`}
        isSingle={true}
        enableSelectAll={false}
        closeOnSelect={true}
      />
    </>
  );
};

export default VendorSelect;
