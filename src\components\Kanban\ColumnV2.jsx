import { useContext, useEffect, useState } from 'react';
import { CiFilter } from 'react-icons/ci';
import {
  IoIosAddCircle,
  IoMdArrowDropleftCircle,
  IoMdArrowDroprightCircle,
  IoMdPricetags,
} from 'react-icons/io';
import { MdArchive, MdOutlineArchive, MdViewAgenda } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Tooltip } from 'react-tooltip';
import {
  generatePrefixId,
  mobileWidth,
  tabletWidth,
} from '../../helperFunction';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
// import { useGetAllDepartmentalOrderQuery } from '../../slices/kanbanApiSlice';
import { useMediaQuery } from 'react-responsive';
import { useEditUserMutation } from '../../slices/userApiSlice';
import { Store } from '../../store/Store';
import Button from '../global/components/Button';
import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import { Input, Label } from '../v2';
import CardV2 from './CardV2';
import { renderCard } from './cardRendererV2';

function Column({
  index = 0,
  column,
  kanbanDataColumnWise,
  // refetch,
  adminView = false,
  dep = '',
  setHistorySidebar,
  setInitialScrollIndex,
}) {
  // const { data: kanbanAllData = [] } = useGetAllDepartmentalOrderQuery();
  const [editUser] = useEditUserMutation();
  const [showArchive, setShowArchive] = useState(false);
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const {
    state,
    dispatch,
    defaults: { defaultParam },
  } = useContext(Store);
  const navigate = useNavigate();
  const [deletedTags, setDeletedTags] = useState([]);
  const [colCheck, setColCheck] = useState({
    minimized: state?.collapsedColumns?.[column?.page?.label],
    disabled: false,
    hidden: false,
  });
  const [allInputs, setAllInputs] = useState({
    isCustomKanbanIdModalOpen: false,
    customKanbanId: '',
  });
  const [updateDefaults] = useUpdateDefaultsMutation();
  const [showFilter, setShowFilter] = useState(false);
  const [applyFilter, setApplyFilter] = useState(false);
  const [allTags, setAllTags] = useState([]);
  const [showTagModal, setShowTagModal] = useState(false);
  const [tagInputVal, setTagInputVal] = useState('');
  const [node, setNode] = useState(null);
  const [selectedTags, setSelectedTags] = useState([]);
  const [actualDeletedTags, setActualDeletedTags] = useState([]);

  const { state: { user } = {}, defaults } = useContext(Store);
  // const isUserAllowed = useMemo(
  //   () =>
  //     user?.departments?.findIndex((el) => el === column?.id) < 0
  //       ? false
  //       : true,
  //   [user, column]
  // );
  // useEffect(() => {
  //   // runs only for non admin users
  //   if (!adminView && user) {
  // if (user?.role === 'superuser') {
  //   setColCheck({ minimized: false, disabled: false, hidden: false });
  // } else if (!column?.id) {
  //   /**
  //    * the column object has field id which is same as _id of departemt. So here we check if the user object has access to department
  //    * by checking it in user.departments array and set the minimzed and disabled state acordingly.
  //    */

  //     //   setColCheck({
  //     //     minimized: true,
  //     //     disabled: true,
  //     //     hidden: false,
  //     //   });
  //     // } else if (!isUserAllowed) {
  //     //   setColCheck((prev) => ({
  //     //     ...prev,
  //     //     hidden: defaults?.defaultParam?.projectDefaults?.enableMinimizedView
  //     //       ? false
  //     //       : true,
  //     //   }));
  //     // }

  //     if (user?.role === 'superuser') {
  //       setColCheck({ minimized: false, disabled: false, hidden: false });
  //     } else if (!column?.id) {
  //       setColCheck((prev) => ({
  //         ...prev,
  //         hidden: false,
  //         minimized: true,
  //       }));
  //     } else {
  //       if (!isUserAllowed) {
  //         setColCheck((prev) => ({
  //           ...prev,
  //           hidden: defaults?.defaultParam?.projectDefaults?.enableMinimizedView
  //             ? false
  //             : true,
  //           disabled: true,
  //           minimized: true,
  //         }));
  //       } else {
  //         setColCheck({
  //           disabled: false,
  //           minimized: false,
  //           hidden: false,
  //         });
  //       }
  //     }
  //   }
  // }, [
  //   column?.id,
  //   adminView,
  //   user,
  //   defaults?.defaultParam?.projectDefaults?.enableMinimizedView,
  //   isUserAllowed,
  // ]);
  useEffect(() => {
    // Function to handle window click
    const handleWindowClick = () => {
      // Update state to false when window is clicked
      setShowFilter(false);
    };

    // Add event listener to window
    document.addEventListener('click', handleWindowClick);

    // Clean up the event listener when component unmounts
    return () => {
      document.removeEventListener('click', handleWindowClick);
    };
  }, []);

  useEffect(() => {
    const nodes = defaultParam?.departmentFlow?.nodeStructure?.nodes;
    if (nodes) {
      nodes.forEach((node, idx) => {
        const { data } = node;
        if (data && data?.selectedPage) {
          if (data?.selectedPage[0]?.label === column?.page?.label) {
            // Set the node (Column) data and its index
            setNode({ data: data, idx: idx });
            setAllTags(data?.tags);
          }
        }
      });
    }
  }, [defaultParam, column?.page?.label]);

  const handleTagsSubmit = async () => {
    const data = node?.data;
    const nodeIndex = node?.idx;
    const dataWithTags = {
      ...data,
      tags: allTags,
    };
    if (nodeIndex !== -1) {
      const updatedNodes = [
        ...defaultParam?.departmentFlow?.nodeStructure?.nodes,
      ];
      // Update the node which data > field > selectedpagefield > label match with the current Column label and change data field
      updatedNodes[nodeIndex] = {
        ...updatedNodes[nodeIndex],
        data: dataWithTags,
      };
      // Update the Default Param with the new Data field which have tags now
      const updatedDefaultParam = {
        ...defaultParam,
        departmentFlow: {
          ...defaultParam?.departmentFlow,
          nodeStructure: {
            ...defaultParam?.departmentFlow?.nodeStructure,
            nodes: updatedNodes,
          },
        },
      };
      const res = await updateDefaults(updatedDefaultParam);
      if (res?.data?.defaultParam) {
        toast.success('Tags Added SuccessFully');
        setActualDeletedTags(deletedTags);
      }
    }
    setAllTags([]);
    setShowTagModal(false);
  };
  const deleteTag = (idx, tag) => {
    setAllTags((prev) => prev.filter((tag, ind) => ind !== idx));
    setDeletedTags((prev) => [...prev, tag]);
  };

  const onChange = async () => {
    await editUser({
      id: state?.user?._id,
      data: { collapsedColumns: state?.collapsedColumns },
    });
  };

  const handleMinimize = (value) => {
    dispatch({
      type: 'COLLAPSE_COLUMN',
      payload: {
        name: column?.page?.label,
        collapseState: value,
      },
    });
  };

  useEffect(() => {
    setColCheck({
      ...colCheck,
      minimized: state?.collapsedColumns?.[column?.page?.label],
    });
    onChange();
  }, [state?.collapsedColumns]); //eslint-disable-line

  const getHeaderText = () => {
    const replaceSpaces = colCheck?.minimized
      ? (text) => text.replace(/ /g, '\xA0')
      : (text) => text;
    if (adminView) {
      return replaceSpaces(dep?.toUpperCase() ?? '');
    } else {
      return column?.name
        ? column.name
        : replaceSpaces(column?.page?.label?.toUpperCase() ?? '');
    }
  };

  return (
    <>
      <Tooltip
        id="AddEntry"
        style={{
          backgroundColor: '#1e293b',
          color: 'white',
          fontWeight: 'normal',
          padding: '12px',
          fontSize: '12px',
          maxWidth: '200px',
          zIndex: '1000',
          borderRadius: '6px',
          lineHeight: '20px',
        }}
        arrowColor="transparent"
      >
        <p>Add Entry</p>
      </Tooltip>
      <Tooltip
        id="CollapseColumn"
        style={{
          backgroundColor: '#1e293b',
          color: 'white',
          fontWeight: 'normal',
          padding: '12px',
          fontSize: '12px',
          maxWidth: '200px',
          zIndex: '1000',
          borderRadius: '6px',
          lineHeight: '20px',
        }}
        arrowColor="transparent"
      >
        <p>Collapse</p>
      </Tooltip>
      <Tooltip
        id="ExpandColumn"
        style={{
          backgroundColor: '#1e293b',
          color: 'white',
          fontWeight: 'normal',
          padding: '12px',
          fontSize: '12px',
          maxWidth: '200px',
          zIndex: '1000',
          borderRadius: '6px',
          lineHeight: '20px',
        }}
        arrowColor="transparent"
      >
        <p>Expand</p>
      </Tooltip>
      <Tooltip
        id="PageNavigator"
        style={{
          backgroundColor: '#1e293b',
          color: 'white',
          fontWeight: 'normal',
          padding: '12px',
          fontSize: '12px',
          maxWidth: '200px',
          zIndex: '1000',
          borderRadius: '6px',
          lineHeight: '20px',
        }}
        arrowColor="transparent"
      >
        <p>Go To Page</p>
      </Tooltip>
      <Tooltip
        id="ShowArchive"
        style={{
          backgroundColor: '#1e293b',
          color: 'white',
          fontWeight: 'normal',
          padding: '12px',
          fontSize: '12px',
          maxWidth: '200px',
          zIndex: '1000',
          borderRadius: '6px',
          lineHeight: '20px',
        }}
        arrowColor="transparent"
      >
        Hide Archive
      </Tooltip>
      <Tooltip
        id="HideArchive"
        style={{
          backgroundColor: '#1e293b',
          color: 'white',
          fontWeight: 'normal',
          padding: '12px',
          fontSize: '12px',
          maxWidth: '200px',
          zIndex: '1000',
          borderRadius: '6px',
          lineHeight: '20px',
        }}
        arrowColor="transparent"
      >
        Show Archive
      </Tooltip>
      <Tooltip
        id="CreateTags"
        style={{
          backgroundColor: '#1e293b',
          color: 'white',
          fontWeight: 'normal',
          padding: '12px',
          fontSize: '12px',
          maxWidth: '200px',
          zIndex: '1000',
          borderRadius: '6px',
          lineHeight: '20px',
        }}
        arrowColor="transparent"
      >
        Create Tags
      </Tooltip>
      <Tooltip
        id="FilterTags"
        style={{
          backgroundColor: '#1e293b',
          color: 'white',
          fontWeight: 'normal',
          padding: '12px',
          fontSize: '12px',
          maxWidth: '200px',
          zIndex: '1000',
          borderRadius: '6px',
          lineHeight: '20px',
        }}
        arrowColor="transparent"
      >
        Filter Tags
      </Tooltip>
      {showTagModal && (
        <Modal
          title={'Add Custom Tags'}
          description={`Admin can add custom tags`}
          onCloseModal={() => setShowTagModal(false)}
          onSubmit={handleTagsSubmit}
        >
          {() => {
            return (
              <>
                <Label>Enter Tag</Label>
                <Input
                  placeholder="Enter Tag"
                  value={tagInputVal}
                  onChange={(e) => setTagInputVal(e.target.value)}
                />
                <div className="flex gap-4 my-4">
                  <Button
                    onClick={() => {
                      const trimmedtag = tagInputVal.trim();
                      if (trimmedtag !== '') {
                        setAllTags((prev) => {
                          if (prev) {
                            return [...prev, trimmedtag];
                          } else {
                            return [trimmedtag];
                          }
                        });
                        setTagInputVal('');
                      }
                    }}
                  >
                    + Add
                  </Button>
                  <Button onClick={() => setAllTags([])}>Clear All Tags</Button>
                </div>
                <span className="font-semibold text-gray-400">All Tags</span>
                <div>
                  {allTags?.map((tag, idx) => (
                    <div key={idx} className="flex w-1/4 py-2 justify-between">
                      <p>{tag}</p>
                      <label
                        onClick={() => deleteTag(idx, tag)}
                        className="cursor-pointer"
                      >
                        ✖️
                      </label>
                    </div>
                  ))}
                </div>
              </>
            );
          }}
        </Modal>
      )}
      {allInputs.isCustomKanbanIdModalOpen && (
        <Modal
          modalWidth={isMobile ? '100%' : isTablet ? '80%' : '50%'}
          modalLeft={isMobile ? '!0%' : isTablet ? '15%' : '35%'}
          modelRight={isMobile ? '0%' : isTablet ? '44%' : '25%'}
          title="Add Custom Task Id"
          description="You can enter your custom task id"
          onCloseModal={() =>
            setAllInputs((prev) => ({
              ...prev,
              isCustomKanbanIdModalOpen: false,
            }))
          }
          onSubmit={(e) => {
            e.preventDefault();
            if (!allInputs.customKanbanId) {
              toast.error('Invalid custom task id');
              return;
            }
            column?.page.value === '/purchase/indent/createindent'
              ? navigate('/purchase/indent')
              : navigate(
                  `${column?.page?.value}?taskId=${encodeURI(allInputs.customKanbanId)}&kanban=true&department=${column?.title}&page=${column?.page?.label}&refType=${column?.page?.label}&index=${index}`
                );
          }}
        >
          {() => {
            return (
              <>
                <Label>Enter Task ID</Label>
                <Input
                  placeholder="Enter Task ID"
                  value={allInputs.customKanbanId}
                  onChange={(e) =>
                    setAllInputs((prev) => ({
                      ...prev,
                      customKanbanId: e.target.value,
                    }))
                  }
                />
              </>
            );
          }}
        </Modal>
      )}

      {showFilter && (
        <div
          onClick={() => setShowFilter(false)}
          className="fixed  inset-0 flex items-center justify-center z-100 bg-gray-900 bg-opacity-50"
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="flex flex-col justify-around elative w-[420px]  bg-white border border-gray-200 shadow-md rounded-lg p-2"
          >
            <section className="w-full flex flex-col">
              <div className="w-full flex justify-between py-2">
                <label
                  className="mb-1"
                  style={{ fontSize: '13px', fontWeight: 'bold' }}
                  htmlFor="projectId"
                >
                  Apply Filter
                </label>
                <label
                  onClick={() => setShowFilter(false)}
                  className="cursor-pointer"
                >
                  ❌
                </label>
              </div>
              <MultiSelect
                onChange={(e) => {
                  setSelectedTags(e.target.value), setApplyFilter(false);
                }}
                value={selectedTags}
                options={allTags?.map((tag) => ({ label: tag, value: tag }))}
              />
            </section>
            <div className="flex items-center gap-4 justify-end">
              <Button
                className="mt-4 !py-1"
                onClick={() => {
                  setApplyFilter(true);
                  setShowFilter(false);
                  toast.success('Filter Applied');
                }}
              >
                Apply Filter
              </Button>
              <Button
                className="mt-4 !py-1"
                onClick={() => {
                  setSelectedTags([]);
                  setApplyFilter(false);
                  setShowFilter(false);
                  toast.success('Filters Reset');
                }}
              >
                Reset
              </Button>
            </div>
          </div>
        </div>
      )}
      <div
        className={`flex flex-col px-[6px] py-2 rounded-md border !shadow-md ${colCheck?.minimized ? 'w-10' : 'w-[236px]'} ${colCheck?.disabled ? 'bg-gray-100' : 'bg-white'} ${colCheck?.hidden ? 'hidden' : ''} h-[80vh] overflow-x-hidden no-scrollbar overflow-y-scroll`}
      >
        <div
          className={`flex !justify-between items-center ${colCheck?.minimized ? 'flex-col-reverse' : 'flex-row'}`}
        >
          <div className="flex items-center gap-1 mb-1">
            <p
              className={` !text-[12px] text-gray-600 font-semibold ${colCheck?.minimized ? 'rotate-90 mt-32 ml-6 ' : ''}`}
            >
              {getHeaderText()}
            </p>
            <p className="px-[4px] text-[12px] mt-[1px]  bg-blue-400 rounded text-white mr-[5px]">
              {column?.cards?.length}
            </p>
          </div>
          {colCheck?.minimized ? (
            <IoMdArrowDroprightCircle
              className="w-4 h-4 text-orange-600 cursor-pointer hover:text-orange-700 transition"
              onClick={() => {
                handleMinimize(false);
              }}
              data-tooltip-id="ExpandColumn"
              data-tooltip-place="bottom"
            />
          ) : (
            <div className="flex gap-[2px] mt-[-2px]">
              {showArchive ? (
                <MdArchive
                  className="w-4 h-4 text-blue-600 cursor-pointer hover:text-blue-700 transition"
                  onClick={() => {
                    setShowArchive(!showArchive);
                  }}
                  data-tooltip-id="ShowArchive"
                  data-tooltip-place="bottom"
                />
              ) : (
                <MdOutlineArchive
                  className="w-4 h-4 text-blue-600 cursor-pointer hover:text-blue-700 transition"
                  onClick={() => {
                    setShowArchive(!showArchive);
                  }}
                  data-tooltip-id="HideArchive"
                  data-tooltip-place="bottom"
                />
              )}
              <IoMdArrowDropleftCircle
                className="w-4 h-4 text-orange-600 cursor-pointer hover:text-orange-700 transition"
                onClick={() => {
                  handleMinimize(true);
                }}
                data-tooltip-id="CollapseColumn"
                data-tooltip-place="bottom"
              />
              {column?.page?.label !== 'Completed' &&
                (user?.role === 'admin' || user?.role === 'superuser') && (
                  <IoMdPricetags
                    className="w-4 h-4 text-blue-600 cursor-pointer hover:text-blue-700 transition"
                    onClick={() => {
                      setShowTagModal(true);
                      setActualDeletedTags([]);
                      setDeletedTags([]);
                    }}
                    data-tooltip-id="CreateTags"
                    data-tooltip-place="bottom"
                  />
                )}
              {column?.page?.label !== 'Completed' && (
                <div
                  className="relative"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  {selectedTags?.length > 0 && applyFilter && (
                    <div className="h-5 w-5 text-sm font-semibold top-[-6px] rounded-[50%] absolute left-3 bg-red-500 text-white flex items-center justify-center">
                      {selectedTags?.length}
                    </div>
                  )}
                  <CiFilter
                    className="w-4 h-4 text-blue-600 cursor-pointer hover:text-blue-700 transition mt-[-1px]"
                    onClick={() => setShowFilter((prev) => !prev)}
                    data-tooltip-id="FilterTags"
                    data-tooltip-place="bottom"
                  />
                </div>
              )}
              {!adminView && column?.page?.label !== 'Completed' && (
                <IoIosAddCircle
                  className={`w-4 h-4 text-blue-600 transition ${
                    colCheck?.disabled ||
                    kanbanDataColumnWise?.[column?.page?.label]?.[
                      kanbanDataColumnWise?.[column?.page?.label]?.length - 1
                    ]?.isAdding
                      ? 'cursor-not-allowed hover:text-blue-400'
                      : 'cursor-pointer hover:text-blue-700'
                  }`}
                  onClick={() => {
                    if (
                      !kanbanDataColumnWise?.[column?.page?.label]?.[
                        kanbanDataColumnWise?.[column?.page?.label]?.length - 1
                      ]?.isAdding
                    ) {
                      let taskId = generatePrefixId(
                        defaultParam?.prefixIds?.['taskId']
                      );
                      if (
                        !colCheck?.disabled &&
                        defaults?.defaultParam?.projectDefaults
                          ?.allowCustomKanbanId
                      ) {
                        setAllInputs((prev) => ({
                          ...prev,
                          isCustomKanbanIdModalOpen: true,
                        }));
                      } else if (
                        !colCheck?.disabled &&
                        !defaults?.defaultParam?.projectDefaults
                          ?.allowCustomKanbanId
                      ) {
                        column?.page.value === '/purchase/indent/createindent'
                          ? navigate('/purchase/indent')
                          : navigate(
                              `${column?.page?.value}?kanban=true&department=${column?.title}&page=${column?.page?.label}&index=${index}&refType=${column?.page?.label}&taskID=${taskId}&orderId=${''}`
                            );
                      }
                    }
                  }}
                  data-tooltip-id="AddEntry"
                  data-tooltip-place="bottom"
                />
              )}
              {column?.page?.label !== 'Completed' && (
                <MdViewAgenda
                  className={`w-4 h-4 text-blue-600 transition cursor-pointer hover:text-blue-700 !pr-[2px]`}
                  onClick={() => {
                    navigate(`${column?.page?.value}`);
                  }}
                  data-tooltip-id="PageNavigator"
                  data-tooltip-place="bottom"
                />
              )}
            </div>
          )}
        </div>

        {!colCheck?.minimized && (
          <>
            <hr />
            <div
              className={`flex flex-col mt-2 gap-y-2 h-[70vh] overflow-y-scroll no-scrollbar overflow-x-hidden min ${
                colCheck?.disabled ? 'pointer-events-none' : ''
              }`}
            >
              {!adminView &&
                showArchive &&
                kanbanDataColumnWise?.[column?.page?.label]
                  .filter((card) => card?.archive?.includes(user._id))
                  ?.slice()
                  ?.reverse()
                  ?.map((card, idx) => {
                    const uniqueId = `${card?.taskId}-${idx}`;
                    return (
                      <CardV2
                        key={idx}
                        colId={column.id}
                        card={card}
                        column={column}
                        adminView={adminView}
                        setHistorySidebar={setHistorySidebar}
                        setInitialScrollIndex={setInitialScrollIndex}
                        index={index}
                        deletedTags={actualDeletedTags}
                        tooltipIds={{
                          taskIdTooltip: `taskIdTooltip-${uniqueId}`,
                          stepIdTooltip: `stepIdTooltip-${uniqueId}`, // Add more tooltip IDs as needed
                        }}
                      />
                    );
                  })}
              {!adminView &&
              !showArchive &&
              selectedTags.length > 0 &&
              applyFilter
                ? kanbanDataColumnWise?.[column?.page?.label]
                    .filter(
                      (card) =>
                        !card?.archive?.includes(user._id) &&
                        selectedTags.some((tag) =>
                          card.tags.includes(tag.value)
                        )
                    )
                    ?.slice()
                    ?.reverse()
                    ?.map((card, idx) => {
                      const uniqueId = `${card?.taskId}-${idx}`;
                      return (
                        <CardV2
                          key={idx}
                          colId={column.id}
                          card={card}
                          // refetch={refetch}
                          column={column}
                          adminView={adminView}
                          setHistorySidebar={setHistorySidebar}
                          setInitialScrollIndex={setInitialScrollIndex}
                          index={index}
                          deletedTags={actualDeletedTags}
                          tooltipIds={{
                            taskIdTooltip: `taskIdTooltip-${uniqueId}`,
                            stepIdTooltip: `stepIdTooltip-${uniqueId}`, // Add more tooltip IDs as needed
                          }}
                        />
                      );
                    })
                : !adminView &&
                  !showArchive &&
                  kanbanDataColumnWise?.[column?.page?.label]
                    .filter((card) => !card?.archive?.includes(user._id))
                    ?.slice()
                    ?.reverse()
                    ?.map((card, idx) => {
                      const uniqueId = `${card?.taskId}-${idx}`;
                      return (
                        <CardV2
                          key={idx}
                          colId={column.id}
                          card={card}
                          // refetch={refetch}
                          column={column}
                          adminView={adminView}
                          setHistorySidebar={setHistorySidebar}
                          setInitialScrollIndex={setInitialScrollIndex}
                          index={index}
                          deletedTags={actualDeletedTags}
                          tooltipIds={{
                            taskIdTooltip: `taskIdTooltip-${uniqueId}`,
                            stepIdTooltip: `stepIdTooltip-${uniqueId}`, // Add more tooltip IDs as needed
                          }}
                        />
                      );
                    })}
              {adminView &&
                column?.map((card) =>
                  card.cards.map((item, idx) => {
                    const getStep = () => {
                      for (let step of item?.steps) {
                        if (card?.page?.label === step?.stepPage) {
                          return step;
                        }
                      }
                      return undefined;
                    };
                    return (
                      <div
                        className="px-3 py-2 bg-white rounded-md border overflow-hidden flex flex-col gap-y-1 relative"
                        key={idx}
                      >
                        {renderCard({
                          data: getStep(),
                          firstStep: item?.steps?.[0],
                          updatedAt: item?.updatedAt,
                          column: card,
                          taskId: item?.taskId,
                          allowCustomKanbanId:
                            defaults?.defaultParam?.projectDefaults
                              ?.allowCustomKanbanId,
                          customTaskId: item?.customTaskId,
                        })}
                      </div>
                    );
                  })
                )}
            </div>
          </>
        )}
      </div>
    </>
  );
}

export default Column;
