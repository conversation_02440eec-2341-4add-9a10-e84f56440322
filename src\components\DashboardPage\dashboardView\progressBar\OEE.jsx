import { useEffect, useState } from 'react';
import {
  CircularProgressbarWithChildren,
  buildStyles,
} from 'react-circular-progressbar';
import {
  calculateValueForViews,
  generateDateStringProduction,
} from '../../../../helperFunction';

const OEE = ({ mao, defaultParam, deviceDatas, selectOptions, values }) => {
  const [oee, setOee] = useState(0);

  const parameter = selectOptions?.project?.processGoalView?.parameters?.find(
    (param) => param?.name === 'Batch Size'
  );

  useEffect(() => {
    if (mao && defaultParam && deviceDatas && values && parameter) {
      // initial shift details
      let shiftStart = defaultParam?.shiftStart?.split(':');
      let shiftStop = defaultParam?.shiftStop?.split(':');

      const customShift = defaultParam?.customShiftTimings?.find(
        (cst) => cst.date === generateDateStringProduction(new Date())
      );

      // if custom shift exists change initial shift details
      if (customShift) {
        shiftStart = customShift?.start?.split(':');
        shiftStop = customShift?.stop?.split(':');
      }

      // initial start date for device data filtering
      let startDateQuery = null;

      const checkDate = new Date().toLocaleDateString();

      if (mao?.resumeTime?.length < 1) {
        const check =
          new Date(mao?.startTime).toLocaleDateString() === checkDate;
        if (check) {
          startDateQuery = mao?.startTime;
        }
      } else {
        const check = mao?.resumeTime?.find(
          (rt) => new Date(rt).toLocaleDateString() === checkDate
        );

        if (check) {
          startDateQuery = check;
        }
      }

      if (startDateQuery) {
        let startCount = 0;

        const batchSize = calculateValueForViews(parameter?.formula, values, [
          mao?.machine.machineId,
        ]);

        mao?.machine?.devices?.forEach((device) => {
          const firstCount = deviceDatas?.find(
            (item) =>
              item?.machine === mao?.machine._id &&
              item?.device === device &&
              +new Date(item?.data?.createdAt) > +new Date(startDateQuery)
          );

          if (firstCount) {
            startCount = startCount + +firstCount?.data?.COUNT;
          }
        });

        const goodCount = batchSize - startCount;

        const shiftStartTime = new Date().setHours(
          +shiftStart[0],
          +shiftStart[1],
          0,
          0
        );
        const shiftStopTime = new Date().setHours(
          +shiftStop[0],
          +shiftStop[1],
          0,
          0
        );

        let plannedDowntime = 0;

        defaultParam?.plannedDowntimes?.forEach((bt) => {
          const splitTime = bt.time?.split(':');

          const time = new Date(
            new Date().setHours(+splitTime[0], +splitTime[1], 0, 0)
          );

          if (time > shiftStartTime && time < shiftStopTime) {
            plannedDowntime = plannedDowntime + +bt.duration;
          }
        });

        const plannedProdTime =
          (shiftStopTime - shiftStartTime) / 60000 - plannedDowntime;

        const idealCycleTime =
          +defaultParam?.idealProductionTimes?.find(
            (ict) => ict.machine === mao?.machine?._id
          )?.time || 0;

        const tempOee = (goodCount * idealCycleTime) / plannedProdTime;

        setOee(Math.round(tempOee > 100 ? 100 : tempOee));
      }

      //
    }
  }, [mao, defaultParam, deviceDatas, parameter, values]);

  return (
    <CircularProgressbarWithChildren
      value={oee}
      strokeWidth={8}
      styles={buildStyles({
        rotation: 0.25,
        strokeLinecap: 'butt',
        pathTransitionDuration: 0.5,
        pathColor: `#71A5DE`,
        trailColor: '#fff',
      })}
    >
      <p>{oee}%</p>
    </CircularProgressbarWithChildren>
  );
};
export default OEE;
