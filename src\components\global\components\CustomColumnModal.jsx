import { FaRegTrashAlt } from 'react-icons/fa';
import { toast } from 'react-toastify';
import {
  useAddCustomColumnMutation,
  useDeleteCustomColumnMutation,
  useEditCustomColumnMutation,
} from '../../../slices/customCoulmn.ApiSlice';
import Input from './Input';
import Modal from './Modal';
import Table from './Table';
/*
  CustomColumnModal - A component for managing custom columns,
  including adding, editing, and deleting column fields within a modal. 
 */
const CustomColumnModal = ({
  showCustomColumnModal, //Determines if the modal should be displayed
  setShowCustomColumnModal, //Function to set the visibility of the modal
  onClose, //Function to handle closing the modal
  columnInputs, //List of column input fields
  setColumnInputs, //Function to update the list of column inputs
  pageName = '', //The name of the page where the custom column is used
}) => {
  const [editCustomColumns] = useEditCustomColumnMutation(); // Hook to handle editing custom columns
  const [addCustomColumns] = useAddCustomColumnMutation(); // Hook to handle adding new custom columns
  const [deleteCustomCol] = useDeleteCustomColumnMutation(); // Hook to handle deleting custom columns

  /*
  Deletes a column based on its ID or index.
  */
  const deletCols = async (id, idx) => {
    if (id) {
      // If there is only one column left, close the modal
      if (columnInputs?.length === 1) setShowCustomColumnModal(false);
      const deletedCol = await deleteCustomCol({ id }); // Delete the column via mutation
      if (deletedCol?.data) {
        toast.success('Column Deleted');
      }
    } else {
      const allInputs = [...columnInputs]; // Clone column inputs
      allInputs.splice(idx, 1); // Remove column by index
      setColumnInputs(allInputs);
    }
  };
  /*
   Handles the submission of the modal, either editing existing columns or adding new ones.
  */
  const handleSubmitColumnModal = async () => {
    if (columnInputs.length === 0) return;
    if (columnInputs.length === 1 && columnInputs[0] === '') {
      toast.error('Please Enter value');
      return;
    }
    if (columnInputs.length !== 0) {
      await editCustomColumns(columnInputs); // Edit existing columns
    }
    const res = await addCustomColumns({
      columnInputs,
      pageName,
    });
    // Add new columns
    if (res?.data) {
      toast.success('Column Added SuccessFully');
    }
    setShowCustomColumnModal(false);
  };
  return showCustomColumnModal ? (
    <Modal
      title="Add Custom Column"
      onCloseModal={onClose}
      onSubmit={handleSubmitColumnModal}
    >
      {() => (
        <>
          <div>
            <Table>
              {/* table header  */}
              <Table.Head>
                <Table.Row>
                  <Table.Th>#</Table.Th>
                  <Table.Th>Options</Table.Th>
                  <Table.Th>Delete</Table.Th>
                </Table.Row>
              </Table.Head>
              {/* table body  */}
              <Table.Body>
                {columnInputs?.map((input, idx) => (
                  <Table.Row key={idx}>
                    <Table.Td>{idx + 1}</Table.Td>
                    <Table.Td>
                      <Input
                        type="text"
                        value={input?.columnName}
                        onChange={(e) => {
                          const allInputs = [...columnInputs];
                          if (typeof allInputs[idx] !== 'object') {
                            allInputs[idx] = e.target.value;
                          } else {
                            allInputs[idx] = {
                              ...allInputs[idx],
                              columnName: e.target.value,
                            };
                          }
                          setColumnInputs(allInputs);
                        }}
                      />
                    </Table.Td>
                    <Table.Td>
                      {/* delete icon for deleting column  */}
                      <FaRegTrashAlt
                        className="cursor-pointer"
                        onClick={() => deletCols(input?._id, idx)}
                      />
                    </Table.Td>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
          <div>
            {/* add field button for adding new column field */}
            <p
              className="text-blue-500 cursor-pointer"
              onClick={() => setColumnInputs((prev) => [...prev, ''])}
            >
              Add Field+
            </p>
          </div>
        </>
      )}
    </Modal>
  ) : null;
};

export default CustomColumnModal;
