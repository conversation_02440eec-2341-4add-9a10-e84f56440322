import { useEffect } from 'react';

const autosave = document.getElementById('autosave-label');

export default function useAutoSave({ timer = 5000, fn }) {
  useEffect(() => {
    const interval = setInterval(async () => {
      if (fn && typeof fn === 'function') {
        autosave.classList.remove('hidden');
        await fn();
        autosave.classList.add('hidden');
      }
    }, timer);

    return () => clearInterval(interval);
  }, [timer, fn]);
}
