import { useEffect, useState } from 'react';

export const useGetYearsMonthsWeeks = (year) => {
  const todayDate = new Date();
  const [months, setMonths] = useState([]);
  const [years, setYears] = useState([]);
  const [weeks, setWeeks] = useState([]);

  const isLeapYear = (year) => {
    return new Date(`${year}`).getFullYear() % 4 === 0;
  };

  useEffect(() => {
    setMonths([
      { name: 'January', days: 31 },
      {
        name: 'February',
        days: isLeapYear(year || todayDate.getFullYear()) ? 29 : 28,
      },
      { name: 'March', days: 31 },
      { name: 'April', days: 30 },
      { name: 'May', days: 31 },
      { name: 'June', days: 30 },
      { name: 'July', days: 31 },
      { name: 'August', days: 31 },
      { name: 'September', days: 30 },
      { name: 'October', days: 31 },
      { name: 'November', days: 30 },
      { name: 'December', days: 31 },
    ]);
  }, [year]); //eslint-disable-line

  useEffect(() => {
    setWeeks([
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ]);

    let temp = [];
    // current year + 5 year shown
    for (let i = 2020; i <= todayDate.getFullYear() + 5; i++) {
      temp.push(i);
    }

    setYears(temp);
  }, []); //eslint-disable-line

  return {
    weeks: weeks,
    months: months,
    years: years,
  };
};
