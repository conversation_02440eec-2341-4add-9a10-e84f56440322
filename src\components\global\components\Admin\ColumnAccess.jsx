import { Checkbox, Typography } from 'antd';
import {
  QUOTATION_FIELDS,
  SALES_INQUIRY_FIELDS,
  SALES_ORDER_FIELDS,
} from '../../../../utils/Constant';

const { Title, Text } = Typography;

const ColumnAccess = ({ inputData, setInputData }) => {
  const handleSelectAll = (pageType, event) => {
    event.preventDefault();
    event.stopPropagation();

    const fieldsMap = {
      salesInquiry: SALES_INQUIRY_FIELDS,
      quotation: QUOTATION_FIELDS,
      salesOrder: SALES_ORDER_FIELDS,
    };

    const availableFields = fieldsMap[pageType]
      .filter((field) => {
        if (pageType === 'quotation') {
          return field.value !== 'quotation/productDetails/hideFinancialData';
        }
        if (pageType === 'salesOrder') {
          return field.value !== 'salesOrder/productDetails/hide';
        }
        return true;
      })
      .map((field) => field.value);

    const isAllSelected = availableFields.every((field) =>
      inputData?.columnAccess?.includes(field)
    );

    const otherPageSelections = inputData?.columnAccess?.filter(
      (field) => !field.startsWith(pageType)
    );

    setInputData((prev) => ({
      ...prev,
      columnAccess: isAllSelected
        ? [...otherPageSelections]
        : [
            ...new Set([
              ...(otherPageSelections || []),
              ...(availableFields || []),
            ]),
          ],
    }));
  };

  const handleCheckboxChange = (fieldValue, checked) => {
    setInputData((prev) => {
      if (checked) {
        return {
          ...prev,
          columnAccess: [
            ...new Set([...(prev?.columnAccess || []), fieldValue]),
          ],
        };
      } else {
        return {
          ...prev,
          columnAccess: prev.columnAccess.filter((i) => i !== fieldValue),
        };
      }
    });
  };

  const renderFieldSection = (title, fields, pageType) => (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        <button
          onClick={(event) => handleSelectAll(pageType, event)}
          className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
        >
          {fields
            .filter((field) => {
              if (pageType === 'quotation') {
                return (
                  field.value !== 'quotation/productDetails/hideFinancialData'
                );
              }
              if (pageType === 'salesOrder') {
                return field.value !== 'salesOrder/productDetails/hide';
              }
              return true;
            })
            .every((field) => inputData?.columnAccess?.includes(field.value))
            ? 'Deselect All'
            : 'Select All'}
        </button>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {fields.map((elem) => (
          <label
            className="flex items-center space-x-3 py-2 px-3 rounded-md hover:bg-gray-50 transition-colors cursor-pointer"
            key={elem?.value}
          >
            <Checkbox
              checked={inputData?.columnAccess?.includes(elem?.value)}
              onChange={(e) => {
                e.stopPropagation();
                handleCheckboxChange(elem?.value, e.target.checked);
              }}
            />
            <span className="text-sm text-gray-700">{elem?.label}</span>
          </label>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <Title level={4} className="!mb-2">
          Column Access
        </Title>
        <Text type="secondary">
          Select the stores that you wish to grant access to.
        </Text>
      </div>
      <div className="space-y-2">
        {renderFieldSection(
          'Sales Inquiry Fields',
          SALES_INQUIRY_FIELDS,
          'salesInquiry'
        )}
        {renderFieldSection('Quotation Fields', QUOTATION_FIELDS, 'quotation')}
        {renderFieldSection(
          'Sales Order Fields',
          SALES_ORDER_FIELDS,
          'salesOrder'
        )}
      </div>
    </div>
  );
};

export default ColumnAccess;
