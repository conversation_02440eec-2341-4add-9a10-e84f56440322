import { apiSlice } from './apiSlice';

const baseRoute = '/v1/purchaseplanning';

const purchasePlanningApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    createPurchasePlanning: builder.mutation({
      query: (data) => ({
        url: baseRoute,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['PurchasePlanning'],
    }),
    getPurchasePlanning: builder.query({
      query: (id) => `${baseRoute}/${id}`,
      providesTags: ['PurchasePlanning'],
    }),
    updatePurchasePlanning: builder.mutation({
      query: ({ id, data }) => ({
        url: `${baseRoute}/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['PurchasePlanning'],
    }),
    createPOForPlanning: builder.mutation({
      query: (data) => ({
        url: `${baseRoute}/po`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['PurchasePlanning'],
    }),
    getPaginatedPlanning: builder.query({
      query: ({ page, limit }) =>
        `${baseRoute}/planning?page=${page}&limit=${limit}`,
      providesTags: ['PurchasePlanning'],
    }),
    deletePlanningItem: builder.mutation({
      query: ({ itemId, planningDocId }) => ({
        url: `${baseRoute}/delete/${planningDocId}`,
        method: 'POST',
        body: { itemId },
      }),
      invalidatesTags: ['PurchasePlanning'],
    }),
    updatePartOrProductVendorDetails: builder.mutation({
      query: ({ id, data }) => ({
        url: `${baseRoute}/vendor/${id}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['PurchasePlanning'],
    }),
  }),
});

export const {
  useCreatePurchasePlanningMutation,
  useGetPurchasePlanningQuery,
  useUpdatePurchasePlanningMutation,
  useCreatePOForPlanningMutation,
  useGetPaginatedPlanningQuery,
  useDeletePlanningItemMutation,
  useUpdatePartOrProductVendorDetailsMutation,
} = purchasePlanningApiSlice;
