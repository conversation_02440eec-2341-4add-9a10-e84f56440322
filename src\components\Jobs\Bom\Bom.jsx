import { useState } from 'react';
import RemoveIcon from '../../../assets/images/remove.png';
import { useGetAssetsQuery } from '../../../slices/assetApiSlice';
import Button from '../../global/components/Button';
import MultiSelect from '../../global/components/MultiSelect';
import Select from '../../global/components/Select';
import Table from '../../global/components/Table';

const Bom = ({
  data,
  setData,
  orderQty = 1,
  processes = [],
  products = [],
  parts = [],
  subAssemblies = [],
}) => {
  const [selected, setSelected] = useState('Products');
  const { data: assetData } = useGetAssetsQuery();

  const options =
    selected === 'Products'
      ? products?.map((pro) => ({
          name: pro.name,
          value: pro._id,
        }))
      : selected === 'Parts'
        ? parts?.items?.map((part) => ({
            name: part.name,
            value: part._id,
          }))
        : selected === 'SubAssemblies'
          ? subAssemblies?.items?.map((sub) => ({
              name: sub.name,
              value: sub._id,
            }))
          : assetData?.map((asset) => ({
              name: asset.name,
              value: asset._id,
            }));

  const renderTable = (data, option) => {
    return (
      <Table>
        <Table.Head>
          <Table.Row>
            <Table.Th>#</Table.Th>
            <Table.Th>Part</Table.Th>
            <Table.Th>Bom Qty</Table.Th>
            <Table.Th>Order Qty</Table.Th>
            <Table.Th className={'w-1/2'}>Process</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {data !== null &&
            data.map((item, iIdx) => (
              <Table.Row key={iIdx}>
                <Table.Td>{iIdx + 1}</Table.Td>
                <Table.Td>{item?.detail?.name}</Table.Td>
                <Table.Td>{item?.bomQty}</Table.Td>
                <Table.Td>{item?.orderQty}</Table.Td>
                <Table.Td>
                  <MultiSelect
                    closeOnSelect
                    options={processes?.map((pro) => ({
                      label: pro?.mqtt?.process,
                      value: pro.mqtt._id,
                    }))}
                    value={data?.[iIdx]?.process || []}
                    onChange={(e) => {
                      setData((prev) => ({
                        ...prev,
                        edited: true,
                        [option]: prev?.[option]?.map((i, idx) => {
                          if (idx === iIdx) {
                            return {
                              ...i,
                              process: e.target.value?.map((v) => v.value),
                            };
                          } else {
                            return i;
                          }
                        }),
                      }));
                    }}
                  />
                </Table.Td>
              </Table.Row>
            ))}
        </Table.Body>
      </Table>
    );
  };

  return (
    <div className="w-full px-5 py-3">
      {!data?._id && (
        <>
          <div className="justify-end flex">
            <Button
              color="red"
              onClick={() =>
                setData({
                  productId: [],
                  bomData: [],
                  partsId: [],
                  partsBomData: [],
                  subAssembliesId: [],
                  subAssembliesBomData: [],
                  assetsId: [],
                  assetsBomData: [],
                })
              }
              className="justify-end"
              disabled={!data?.productId}
            >
              <img
                src={RemoveIcon}
                alt="Remove Icon"
                className="w-5 h-5 object-contain relative"
              />
              Remove BOM
            </Button>
          </div>

          {/* <label>Products</label>
          <Input type='checkbox' onChange={() => setSelected('Products')} checked={selected === 'Products'} />
          <label>Parts</label>
          <Input type='checkbox' onChange={() => setSelected('Parts')} checked={selected === 'Parts'} />
          <label>SubAssemblies</label>
          <Input type='checkbox' onChange={() => setSelected('SubAssemblies')} checked={selected === 'SubAssemblies'} /> */}
          <label className="mt-3 font-bold">Select Type</label>
          <Select
            onChange={(e) => setSelected(e.target.value)}
            value={selected}
            options={[
              { value: 'Products', label: 'Products' },
              { value: 'Parts', label: 'Parts' },
              { value: 'SubAssemblies', label: 'SubAssemblies' },
              { value: 'Assets', label: 'Assets' },
            ]}
          />
          <section className="flex flex-col mt-3">
            <label>Select Items</label>
            <Select
              className="bottom-0"
              value={data?.productId}
              options={options}
              onChange={(e) => {
                let newProductId = data?.productId;
                let newBomData = data?.bomData;
                let newPartsId = data?.partsId;
                let newPartsBomData = data?.partsBomData;
                let newSubAssembliesId = data?.subAssembliesId;
                let newSubAssembliesBomData = data?.subAssembliesBomData;
                let newAssetsId = data?.assetsId;
                let newAssetsBomData = data?.assetsBomData;
                let bomArr = [];

                if (
                  selected === 'Products' &&
                  !data?.productId.includes(e.target.value)
                ) {
                  newProductId.push(e.target.value);

                  const product = products.find(
                    (pro) => pro._id === e.target.value
                  );

                  product?.parts?.forEach((part) => {
                    bomArr.push({
                      detail: { part: part.id._id, name: part?.name },
                      bomQty: part.quantity,
                      orderQty: part.quantity * orderQty,
                      process: [],
                    });
                  });

                  product?.subAssemblies?.forEach((sub) => {
                    sub.id.parts?.forEach((part) => {
                      bomArr.push({
                        detail: {
                          part: part.id,
                          subAssembly: sub.id._id,
                          name: sub.name + ' - ' + part?.name,
                        },
                        bomQty: part.quantity,
                        orderQty: part.quantity * orderQty,
                        process: [],
                      });
                    });
                  });
                  newBomData = data.bomData.concat(bomArr);
                } else if (
                  selected === 'Parts' &&
                  !data?.partsId.includes(e.target.value)
                ) {
                  newPartsId.push(e.target.value);

                  const part = parts.items.find(
                    (part) => part._id === e.target.value
                  );
                  bomArr.push({
                    detail: { part: part?._id, name: part?.name },
                    bomQty: 1,
                    orderQty: orderQty,
                    process: [],
                  });
                  newPartsBomData = data.partsBomData.concat(bomArr);
                } else if (
                  selected === 'SubAssemblies' &&
                  !data?.subAssembliesId.includes(e.target.value)
                ) {
                  newSubAssembliesId.push(e.target.value);
                  subAssemblies?.items?.forEach((sub) => {
                    sub?.parts?.forEach((part) => {
                      bomArr.push({
                        detail: {
                          part: part.id,
                          subAssembly: sub._id,
                          name: sub.name + ' - ' + part?.name,
                        },
                        bomQty: part.quantity,
                        orderQty: part.quantity * orderQty,
                        process: [],
                      });
                    });
                  });
                  newSubAssembliesBomData =
                    data.subAssembliesBomData.concat(bomArr);
                } else {
                  newAssetsId.push(e.target.value);
                  const asset = assetData.find((a) => a._id === e.target.value);
                  bomArr.push({
                    detail: { part: asset?._id, name: asset?.name },
                    bomQty: 1,
                    orderQty: 1,
                    process: [],
                  });
                  newAssetsBomData = data.assetsBomData.concat(bomArr);
                }

                setData({
                  productId: newProductId,
                  bomData: newBomData,
                  partsId: newPartsId,
                  partsBomData: newPartsBomData,
                  subAssembliesId: newSubAssembliesId,
                  subAssembliesBomData: newSubAssembliesBomData,
                  assetsId: newAssetsId,
                  assetsBomData: newAssetsBomData,
                });
              }}
            />
          </section>
        </>
      )}

      {data ? (
        <section className="mt-3">
          <h2>Product</h2>
          {renderTable(data?.bomData, 'bomData')}
          <h2>Parts</h2>
          {renderTable(data?.partsBomData, 'partsBomData')}
          <h2>SubAssemblies</h2>
          {renderTable(data?.subAssembliesBomData, 'subAssembliesBomData')}
          <h2>Assets</h2>
          {renderTable(data?.assetsBomData, 'assetsBomData')}
        </section>
      ) : null}
    </div>
  );
};

export default Bom;
