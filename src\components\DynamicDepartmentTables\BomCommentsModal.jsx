import React from 'react';
import {} from // useEditBomCommentMutation,

// useLazyGetBomCommentByIdQuery,
'../../slices/assemblyBomCommentApiSlice';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import Table from '../global/components/Table';
// import { toast } from 'react-toastify';

const BomCommentsModal = ({
  // bom,
  setshowModal,
  handleAddComment,
  bomData,
  // setBomData,
}) => {
  return (
    <Modal
      title="Bom Comments awdaw"
      onCloseModal={() => {
        setshowModal(false);
      }}
      onSubmit={() => {
        // handleUpdate();
        // setshowModal(false)
      }}
    >
      {() => {
        return (
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Th>SR NO.</Table.Th>
                <Table.Th>category</Table.Th>
                <Table.Th>Item Name</Table.Th>
                <Table.Th>Units</Table.Th>
                <Table.Th>Subitem</Table.Th>
                <Table.Th>Comment</Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {bomData?.productList?.map((item, idx) => (
                <React.Fragment key={idx}>
                  <Table.Row key={idx}>
                    <Table.Td>{idx}</Table.Td>
                    <Table.Td>{item?.category}</Table.Td>
                    <Table.Td>{item?.part?.name}</Table.Td>
                    <Table.Td>{item?.units}</Table.Td>
                    <Table.Td>No</Table.Td>
                    <Table.Td>
                      <Input
                        type="text"
                        placeholder="Enter comments"
                        onChange={(e) => handleAddComment(item, e.target.value)}
                        value={item?.comment}
                      />
                    </Table.Td>
                  </Table.Row>
                  {item?.subItems?.map((subItem, i) => (
                    <Table.Row key={i} className={`!bg-slate-200`}>
                      <Table.Td>{i + 1}</Table.Td>
                      <Table.Td>{subItem?.category}</Table.Td>
                      <Table.Td>{subItem?.part?.name}</Table.Td>
                      <Table.Td>{subItem?.units}</Table.Td>
                      <Table.Td>Yes</Table.Td>
                      <Table.Td>
                        <Input
                          type="text"
                          placeholder="Enter comments"
                          onChange={(e) =>
                            handleAddComment(subItem, e.target.value)
                          }
                          value={subItem?.comment}
                        />
                      </Table.Td>
                    </Table.Row>
                  ))}
                </React.Fragment>
              ))}
            </Table.Body>
          </Table>
        );
      }}
    </Modal>
  );
};

export default BomCommentsModal;
