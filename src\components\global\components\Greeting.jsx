import { useContext } from 'react';
import { ReactComponent as Person } from '../../../assets/svgs/person.svg';
import { Store } from '../../../store/Store';

const Greeting = () => {
  const {
    state: { user },
  } = useContext(Store);

  return (
    <div className="relative w-full aspect-[325/136] bg-white rounded-new mb-5">
      <Person className="absolute -right-12 -bottom-[3px] h-[150%] aspect-square" />
      <div className="w-full h-full flex flex-col justify-center px-4">
        <p className="text-[1.5rem] font-bold text-blue-primary">{`Hello ${
          user?.name?.split(' ')?.[0]
        }!`}</p>
        <p className="text-black/40 text-[.815rem]">It's good to see you</p>
      </div>
    </div>
  );
};
export default Greeting;
