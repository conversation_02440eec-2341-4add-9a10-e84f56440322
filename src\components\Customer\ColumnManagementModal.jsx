import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Input, Modal, Space, Switch, Tooltip } from 'antd';
import { useState } from 'react';
import { toast } from 'react-toastify';

const ColumnManagementModal = ({
  visible,
  onClose,
  onAdd,
  onDelete,
  existingColumns,
}) => {
  const [columnName, setColumnName] = useState('');
  const [isMandatory, setIsMandatory] = useState(false);

  const handleSubmit = () => {
    if (!columnName.trim()) {
      toast.error('Column name is required');
      return;
    }
    onAdd(columnName, isMandatory);
    setColumnName('');
    setIsMandatory(false);
  };

  return (
    <Modal
      title={<h3 className="text-xl font-semibold">Manage Columns</h3>}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <div className="mb-6 bg-gray-50 p-4 rounded-lg">
        <h4 className="text-base font-medium mb-3 text-gray-700">
          Add New Column
        </h4>
        <Space className="w-full" direction="vertical" size="middle">
          <div className="flex gap-2 w-full">
            <Input
              placeholder="Enter column name"
              value={columnName}
              onChange={(e) => setColumnName(e.target.value)}
              className="flex-1"
              size="large"
            />
            <Switch
              checked={isMandatory}
              onChange={setIsMandatory}
              checkedChildren="Required"
              unCheckedChildren="Optional"
              className="min-w-[90px]"
            />
            <Button
              type="primary"
              onClick={handleSubmit}
              icon={<PlusOutlined />}
            >
              Add
            </Button>
          </div>
        </Space>
      </div>

      <div className="bg-white rounded-lg">
        <h4 className="text-base font-medium mb-4 text-gray-700">
          Existing Columns
        </h4>
        <div className="max-h-[300px] overflow-y-auto">
          {existingColumns?.map((column) => (
            <div
              key={column._id}
              className="flex justify-between items-center p-3 mb-2 border border-gray-100 rounded-md hover:bg-gray-50 transition-all duration-200"
            >
              <div className="flex flex-col">
                <span className="font-medium">{column.column_name}</span>
                <span
                  className={`text-xs ${column.mandatory ? 'text-blue-500' : 'text-gray-500'}`}
                >
                  {column.mandatory ? 'Required Field' : 'Optional Field'}
                </span>
              </div>
              {!column.isDefault && (
                <Tooltip title="Delete Column">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => onDelete(column._id)}
                    className="hover:bg-red-50"
                  />
                </Tooltip>
              )}
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
};

export default ColumnManagementModal;
