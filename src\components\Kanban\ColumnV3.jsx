import { useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Store } from '../../store/Store';
import CardV2 from './CardV2';
import ColumnOptions from './ColumnOptions';

import useDebounceValue from '../../hooks/useDebounceValue';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { useGetCardsByColumnQuery } from '../../slices/kanbanApiSlice';
import Modal from '../global/components/Modal';
import { Input, Label } from '../v2';
import ApplyFilter from './ApplyFilter';
import TagModal from './TagModal';

const ColumnV3 = ({
  isMobile,
  isTablet,
  index,
  column,
  adminView = false,
  setInitialScrollIndex,
  setHistorySidebar,
  dep = '',
  FilterHeadingvalue,
  Filtervalue,
  searchTerm,
  searchField,
}) => {
  const { state, defaults, socket } = useContext(Store);
  const user = state?.user;
  const navigate = useNavigate();
  const debounceSearch = useDebounceValue(searchTerm || '', 400);

  const [limit, setLimit] = useState(4);
  const [page, setPage] = useState(1); //eslint-disable-line
  const isMounted = useRef(false);

  const { data: cardsData, isLoading } = useGetCardsByColumnQuery(
    {
      columnName: column?.label,
      page,
      limit,
      filter_name: FilterHeadingvalue,
      filter_value: Filtervalue,
      debounceSearch,
      searchField,
    },
    { skip: !page || !limit, refetchOnMountOrArgChange: true }
  );

  const [updateDefaults] = useUpdateDefaultsMutation();

  const [cards, setCards] = useState([]);
  const [colCheck, setColCheck] = useState({
    minimized: user?.[column?.label],
    disabled: false,
    hidden: false,
  });
  const [showArchive, setShowArchive] = useState(false);
  const [showTagModal, setShowTagModal] = useState(false);
  const [actualDeletedTags, setActualDeletedTags] = useState([]);
  const [deletedTags, setDeletedTags] = useState([]);
  const [allInputs, setAllInputs] = useState({
    isCustomKanbanIdModalOpen: false,
    customKanbanId: '',
  });
  const [showFilter, setShowFilter] = useState(false);
  const [applyFilter, setApplyFilter] = useState(false);
  const [allTags, setAllTags] = useState([]);
  const [selectedTags, setSelectedTags] = useState([]);

  // const getHeaderText = () => {
  //   const replaceSpaces = colCheck?.minimized
  //     ? (text) => text.replace(/ /g, '\xA0')
  //     : (text) => text;
  //   if (adminView) {
  //     return replaceSpaces(dep?.toUpperCase() ?? '');
  //   } else {
  //     return column?.label
  //       ? column.label
  //       : replaceSpaces(column?.label?.toUpperCase() ?? '');
  //   }
  // };

  const getHeaderText = () => {
    const replaceSpaces = colCheck?.minimized
      ? (text) => text.replace(/ /g, '\xA0') // Replace spaces with non-breaking spaces if minimized
      : (text) => text; // Otherwise, leave text unchanged

    if (adminView) {
      return replaceSpaces(dep?.toUpperCase() ?? '');
    } else {
      // Use replaceSpaces for non-minimized column labels to prevent wrapping in minimized view
      const colName =
        defaults?.defaultParam?.departmentFlow?.nodeStructure?.nodes?.find(
          (node) => node?.data?.selectedPage?.[0]?.label === column.label
        )?.data?.name;
      return colName
        ? replaceSpaces(colName)
        : replaceSpaces(column?.label || '');
    }
  };

  useEffect(() => {
    if (!socket) return;
    if (!isMounted.current && state?.user && column?.label) {
      isMounted.current = true;
      socket.on('kanban', (data) => {
        if (
          data?.payload?.newOrder?.currentPage?.includes(column?.label) &&
          data?.payload?.newOrder?.profileId === state?.user?.profileId &&
          data?.payload?.user !== state?.user?._id
        ) {
          setCards((prev) => [data?.payload?.newOrder, ...prev]);
        }
        if (
          data?.payload?.pageRemovedFrom === column?.label &&
          data?.payload?.newOrder?.profileId === state?.user?.profileId &&
          data?.payload?.user !== state?.user?._id
        ) {
          setCards((prev) =>
            prev?.filter((elem) => elem?._id !== data?.payload?.newOrder?._id)
          );
        }
      });
    }
  }, [socket, state, column?.label]);

  useEffect(() => {
    return () => {
      setCards([]);
    };
  }, []);

  useEffect(() => {
    if (cardsData) {
      if (page === 1) {
        setCards(cardsData?.results);
      } else {
        setCards((prev) => [...prev, ...cardsData?.results]);
      }
    }
  }, [cardsData]); //eslint-disable-line

  useEffect(() => {
    if (column) {
      setAllTags(column?.data?.tags);
    }
  }, [column]);

  const handleTagsSubmit = async (allTags) => {
    const data = column?.data;
    const nodeIndex = column?.index;
    const dataWithTags = {
      ...data,
      tags: allTags,
    };
    if (nodeIndex !== -1) {
      const updatedNodes = [
        ...defaults?.defaultParam?.departmentFlow?.nodeStructure?.nodes,
      ];
      // Update the node which data > field > selectedpagefield > label match with the current Column label and change data field
      updatedNodes[nodeIndex] = {
        ...updatedNodes[nodeIndex],
        data: dataWithTags,
      };
      // Update the Default Param with the new Data field which have tags now
      const updatedDefaultParam = {
        ...defaults?.defaultParam,
        departmentFlow: {
          ...defaults?.defaultParam?.departmentFlow,
          nodeStructure: {
            ...defaults?.defaultParam?.departmentFlow?.nodeStructure,
            nodes: updatedNodes,
          },
        },
      };
      const res = await updateDefaults(updatedDefaultParam);
      if (res) {
        setShowTagModal(false);
      }
      if (res?.data?.defaultParam) {
        toast.success('Tags Added SuccessFully');
        // setAllTags([]);
        setActualDeletedTags(deletedTags);
      }
    }
  };
  const deleteTag = (idx, tag) => {
    setAllTags((prev) => prev.filter((tag, ind) => ind !== idx));
    setDeletedTags((prev) => [...prev, tag]);
  };

  const getShortForm = (val) => {
    val = val + ' ';
    let words = val?.split(' ');
    let shortForm = '';
    for (let i of words) {
      shortForm = shortForm + i?.charAt(0);
    }
    return shortForm;
  };

  return (
    <>
      {allInputs.isCustomKanbanIdModalOpen && (
        <Modal
          modalWidth={isMobile ? '100%' : isTablet ? '80%' : '50%'}
          modalLeft={isMobile ? '!0%' : isTablet ? '15%' : '35%'}
          modelRight={isMobile ? '0%' : isTablet ? '44%' : '25%'}
          title="Add Custom Task Id"
          description="You can enter your custom task id"
          onCloseModal={() =>
            setAllInputs((prev) => ({
              ...prev,
              isCustomKanbanIdModalOpen: false,
            }))
          }
          onSubmit={(e) => {
            e.preventDefault();
            if (!allInputs.customKanbanId) {
              toast.error('Invalid custom task id');
              return;
            }
            column?.page?.value === '/purchase/indent/createindent'
              ? navigate('/purchase/indent')
              : navigate(
                  `${column?.value}?taskId=${encodeURI(allInputs.customKanbanId)}&kanban=true&department=${column?.data?.selectedDepartment?.name}&page=${column?.label}&refType=${column?.label}&index=${index}`
                );
          }}
        >
          {() => {
            return (
              <>
                {defaults?.defaultParam?.projectDefaults
                  ?.projectBasedCustomTaskId ? (
                  <div>
                    <div>
                      <Label>Enter Project ID</Label>
                      <Input
                        placeholder="Enter Task ID"
                        // value={allInputs.customKanbanId}
                        onChange={(e) => {
                          let year = new Date().getFullYear();
                          let yearLastDigits = year % 100;
                          let currentMonth = new Date().getMonth() + 1;
                          let prefix = 'WB';
                          // let increment = defaults?.defaultParam?.taskId || 0;
                          let shortForm = getShortForm(e.target.value);
                          setAllInputs((prev) => ({
                            ...prev,
                            customKanbanId: `${prefix}${shortForm}${yearLastDigits}${currentMonth}`,
                          }));
                        }}
                      />
                    </div>
                    <div className="mt-2">
                      <Label>Generated Task ID:</Label>
                      <Input
                        value={allInputs.customKanbanId}
                        disabled={true}
                        placeholder="Please enter project name"
                      />
                    </div>
                  </div>
                ) : (
                  <div>
                    <div>
                      <Label>Enter Project ID</Label>
                      <Input
                        placeholder="Enter Task ID"
                        value={allInputs.customKanbanId}
                        onChange={(e) => {
                          setAllInputs((prev) => ({
                            ...prev,
                            customKanbanId: e.target.value,
                          }));
                        }}
                      />
                    </div>
                  </div>
                )}
              </>
            );
          }}
        </Modal>
      )}
      {showFilter && (
        <ApplyFilter
          setShowFilter={setShowFilter}
          setApplyFilter={setApplyFilter}
          setSelectedTags={setSelectedTags}
          selectedTags={selectedTags}
          allTags={allTags}
        />
      )}
      <TagModal
        key={column?.label}
        openModal={showTagModal}
        setOpenModal={setShowTagModal}
        handleTagsSubmit={handleTagsSubmit}
        deleteTag={deleteTag}
        setAllTags={setAllTags}
        allTags={allTags}
      />
      <div
        className={`flex flex-col px-[6px] py-2 rounded-md border !shadow-md ${colCheck?.minimized ? 'w-10' : 'w-[316px]'} ${colCheck?.disabled ? 'bg-gray-100' : 'bg-white'} ${colCheck?.hidden ? 'hidden' : ''} h-[80vh] overflow-x-hidden no-scrollbar overflow-y-scroll`}
      >
        <div
          className={`flex !justify-between items-center ${colCheck?.minimized ? 'flex-col-reverse' : 'flex-row'}`}
        >
          <div className="flex items-center gap-1 mb-1">
            <p
              className={` !text-[12px] text-gray-600 font-semibold ${colCheck?.minimized ? 'rotate-90 mt-32 ml-6 ' : ''}`}
            >
              {getHeaderText()}
            </p>
            <p className="text-[10px] text-white bg-blue-300 pb-[3px] pt-[2px] box-border px-[5px] rounded-full text-center">
              {cardsData?.totalResults}
            </p>
          </div>
          <ColumnOptions
            index={index}
            colCheck={colCheck}
            setColCheck={setColCheck}
            column={column}
            showArchive={showArchive}
            setShowArchive={setShowArchive}
            setShowFilter={setShowFilter}
            setActualDeletedTags={setActualDeletedTags}
            setDeletedTags={setDeletedTags}
            selectedTags={selectedTags}
            adminView={adminView}
            applyFilter={applyFilter}
            setAllInputs={setAllInputs}
            setShowTagModal={setShowTagModal}
          />
        </div>
        {!colCheck?.minimized && (
          <>
            <hr />
            <div
              className={`flex flex-col mt-2 gap-y-2 h-[70vh] overflow-y-scroll no-scrollbar overflow-x-hidden min ${
                colCheck?.disabled ? 'pointer-events-none' : ''
              }`}
            >
              {isLoading ? (
                <>
                  {[0, 1, 2, 3]?.map((elem) => {
                    return (
                      <div
                        key={elem}
                        className="max-w-sm animate-pulse shadow-md text-xs px-2 py-3 bg-white rounded-xl border flex flex-col gap-y-1 !w-[100%]"
                      >
                        <div className="flex items-center justify-between">
                          <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-700 w-7 mb-4"></div>
                          <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                        </div>
                        <div className="h-2.5 bg-gray-200 rounded-full dark:bg-gray-700 w-24 mb-4"></div>
                        <div className="flex items-center my-auto gap-2">
                          <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-700 w-14"></div>
                          <div className="h-5 bg-gray-200 rounded-full dark:bg-gray-700 w-14"></div>
                        </div>
                      </div>
                    );
                  })}
                </>
              ) : showArchive ? (
                cards
                  ?.filter((card) => card?.archive?.includes(user?._id))
                  ?.slice()
                  ?.map((card, idx) => {
                    const uniqueId = `${card?.taskId}-${idx}`;
                    return (
                      <CardV2
                        key={idx}
                        columnNumber={index}
                        colId={column.id}
                        card={card}
                        column={column}
                        adminView={adminView}
                        setInitialScrollIndex={setInitialScrollIndex}
                        index={index}
                        deletedTags={actualDeletedTags}
                        tooltipIds={{
                          taskIdTooltip: `taskIdTooltip-${uniqueId}`,
                          stepIdTooltip: `stepIdTooltip-${uniqueId}`, // Add more tooltip IDs as needed
                        }}
                        setHistorySidebar={setHistorySidebar}
                      />
                    );
                  })
              ) : (
                cards
                  ?.filter((card) => !card?.archive?.includes(user?._id))
                  ?.slice()
                  ?.map((card, idx) => {
                    const uniqueId = `${card?.taskId}-${idx}`;
                    return (
                      <CardV2
                        key={idx}
                        columnNumber={index}
                        colId={column.id}
                        card={card}
                        column={column}
                        adminView={adminView}
                        setInitialScrollIndex={setInitialScrollIndex}
                        index={index}
                        deletedTags={actualDeletedTags}
                        tooltipIds={{
                          taskIdTooltip: `taskIdTooltip-${uniqueId}`,
                          stepIdTooltip: `stepIdTooltip-${uniqueId}`, // Add more tooltip IDs as needed
                        }}
                        setHistorySidebar={setHistorySidebar}
                      />
                    );
                  })
              )}
              {page !== cardsData?.totalPages &&
                cardsData?.results?.length !== 0 && (
                  <p
                    className="py-1 px-2 text-sm text-center my-2 mx-auto w-fit text-slate-500 border-[1px] rounded-[10px] border-solid border-slate-500 cursor-pointer hover:bg-slate-500 hover:text-white"
                    onClick={() => setLimit((prev) => prev + 4)}
                  >
                    Load More
                  </p>
                )}
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default ColumnV3;
