import {
  DownloadOutlined,
  FilterOutlined,
  RotateLeftOutlined,
} from '@ant-design/icons';
import {
  Bad<PERSON>,
  Button,
  DatePicker,
  Divider,
  Popover,
  Select,
  Space,
} from 'antd';
import { useGetFilterOptionsForInventoryDashboardQuery } from '../../slices/transactionsApiSlice';
import MultiSelect from '../global/components/MultiSelect';

const { RangePicker } = DatePicker;

const InventoryDashboardFilterAndExport = ({
  filters,
  setFilters,
  type,
  fetchAndExport,
  isCsvLoading,
  csvData,
}) => {
  const { data: allData = {} } = useGetFilterOptionsForInventoryDashboardQuery({
    type,
  });

  const itemNameOptions = allData?.object?.map((item) => ({
    label: item?.label,
    value: item?.value,
  }));

  const timeOptions = [
    { value: 'All Time', label: 'All Time' },
    { value: 'Today', label: 'Today' },
    { value: 'Yesterday', label: 'Yesterday' },
    { value: 'Last 7 Days', label: 'Last 7 Days' },
    { value: 'Last 30 Days', label: 'Last 30 Days' },
    { value: 'Custom', label: 'Custom' },
  ];

  const onFilterChange = (key, value) => {
    setFilters({ ...filters, [key]: value });
  };

  const resetFilterValues = () => {
    setFilters({
      ...filters,
      itemNames: [],
      timePeriod: 'All Time',
      uoms: [],
      dateRange: [],
    });
  };

  const activeFiltersCount =
    (filters.itemNames?.length > 0 ? 1 : 0) +
    (filters.uoms?.length > 0 ? 1 : 0) +
    (filters.timePeriod !== 'All Time' && filters.timePeriod !== 'Custom'
      ? 1
      : 0) +
    (filters.dateRange?.length > 0 ? 1 : 0);

  const FilterAndExportContent = () => (
    <div className="w-[360px]">
      <div className="p-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Filters & Export
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {activeFiltersCount}{' '}
              {activeFiltersCount === 1 ? 'filter' : 'filters'} applied
            </p>
          </div>
          <Button
            onClick={resetFilterValues}
            type="text"
            icon={<RotateLeftOutlined />}
            className="text-gray-500 hover:text-blue-600"
          >
            Reset all
          </Button>
        </div>

        {/* Filter Section */}
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Time Period
            </label>
            <Select
              className="w-full"
              value={filters?.timePeriod}
              onChange={(value) => onFilterChange('timePeriod', value)}
              options={timeOptions}
              placeholder="Select Time Period"
            />
          </div>

          {filters?.timePeriod === 'Custom' && (
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Date Range
              </label>
              <RangePicker
                className="w-full"
                onChange={(dates) => onFilterChange('dateRange', dates)}
                value={filters?.dateRange}
              />
            </div>
          )}

          {type !== 'both' && (
            <>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Item Names
                </label>
                <MultiSelect
                  className="w-full"
                  value={filters?.itemNames}
                  onChange={(e) => onFilterChange('itemNames', e.target.value)}
                  options={itemNameOptions}
                  placeholder="Select Item Names"
                />
              </div>
            </>
          )}
        </div>

        <Divider className="my-6" />

        {/* Export Section */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">Export Options</h4>
          <Space direction="vertical" className="w-full">
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={() => fetchAndExport('filtered')}
              loading={isCsvLoading}
              className="w-full"
              // disable when filter set as only "All Time" and no item names or uoms selected
              disabled={
                filters.timePeriod === 'All Time' &&
                !filters.itemNames.length &&
                !filters.uoms.length
              }
            >
              Export Filtered Data ({csvData?.length || 0} records)
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => fetchAndExport('all')}
              loading={isCsvLoading}
              className="w-full"
            >
              Export All Data
            </Button>
          </Space>
        </div>
      </div>
    </div>
  );

  return (
    <Popover
      placement="bottomRight"
      content={<FilterAndExportContent />}
      trigger="click"
    >
      <Badge count={activeFiltersCount} offset={[-5, 5]}>
        <Button type="primary" icon={<FilterOutlined />}>
          <span>Filter & Export</span>
        </Button>
      </Badge>
    </Popover>
  );
};

export default InventoryDashboardFilterAndExport;
