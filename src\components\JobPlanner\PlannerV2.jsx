import { LeftOutlined } from '@ant-design/icons';
import { But<PERSON> } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { convertToHrsAndMins, padZero } from '../../helperFunction';
import { useLazyGetCreateInputByIDQuery } from '../../slices/createInputApiSlice';
import { useEditManyGoalsTableMutation } from '../../slices/goalsTableapiSlice';
import { useGetAllMachineScheduleByCiQuery } from '../../slices/jobScheduleApiSlice';
import { useGetAllLocationsQuery } from '../../slices/locationApiSlice';
import { useGetMachinesByMqttMutation } from '../../slices/machineApiSlice';
import { useGetShiftsForMachinesMutation } from '../../slices/machineShiftApiSlice';
import { useGetAllUserByProcessAccessMutation } from '../../slices/userApiSlice';
import Header from '../global/components/Header';
import Spinner from '../global/components/Spinner';
import BatchCard from './BatchCard';
import MachineSelection from './MachineSelection';

const PlannerV2 = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [goalsTable, setGoalsTable] = useState([]);
  const [machineSchedules, setMachineSchedules] = useState([]);
  const [planningStatus, setPlanningStatus] = useState('unScheduled');
  const [step, setStep] = useState(0);

  const [getCreateInputByID, { isFetching: isFetchingGet, data: createInput }] =
    useLazyGetCreateInputByIDQuery();

  const [editManyGoalsTable, { isLoading: isLoadingEdit }] =
    useEditManyGoalsTableMutation();

  const { productionFlow, jobPlanningDetails = {} } = createInput || {};
  const { isSequential } = jobPlanningDetails;

  const [getMachinesByMqtt, { data: allMachines }] =
    useGetMachinesByMqttMutation();

  const [getShiftsForMachines, { data: customMachineTimes }] =
    useGetShiftsForMachinesMutation();

  const { data: machineSchedulesForJob } = useGetAllMachineScheduleByCiQuery(
    { id },
    { skip: !id, refetchOnMountOrArgChange: true }
  );

  const { data: allLocations } = useGetAllLocationsQuery();

  const [getAllUserByProcessAccess, { data: usersForAssigning = [] }] =
    useGetAllUserByProcessAccessMutation();

  const selectedMachinesDepArray = (goalsTable || [])
    ?.flatMap((i) => i?.selectedMachines)
    ?.join(',');

  useEffect(() => {
    if (machineSchedulesForJob && allMachines) {
      const generateData = (
        gt,
        tData,
        tempBatch,
        subProcess = null,
        subProcessIndex = null
      ) => {
        const tempObj = {};

        for (let i = 0; i < gt?.selectedMachines?.length; i++) {
          const macId = gt?.selectedMachines[i];
          const mac = machineMap?.[macId];

          let exist = null;

          for (let ii = 0; ii < machineSchedulesForJob.length; ii++) {
            const item = machineSchedulesForJob[ii];
            if (
              item?.flowId === gt?.flowId &&
              item?.batchNo === tempBatch + 1 &&
              (subProcess ? subProcessIndex === item?.subProcessIndex : true)
            ) {
              tempObj[item?.machineId] = item?.target;
              if (item.machineId === macId) exist = item;
            }
          }

          const total = Object.values(tempObj).reduce((a, c) => a + c, 0);

          const batchSize = tData?.newBatchSize || tData?.['Batch Size'] || 0;

          const difference = batchSize - total;

          let target = 0;

          if (total === 0) {
            target = batchSize / gt?.selectedMachines?.length;
          } else if (difference > 0) {
            target = Math.ceil(
              difference /
                (gt?.selectedMachines?.length - Object.keys(tempObj)?.length)
            );
          }

          if (total + target > batchSize) {
            target -= 1;
          }

          tempObj[macId] = target;

          if (!exist) {
            const calc = target / mac?.speed || 0;

            const [hh, mm] = convertToHrsAndMins(calc);
            arr.push({
              batchNo: tempBatch + 1,
              speed: mac?.speed,
              status: 'notStarted',
              machineId: macId,
              flowId: gt?.flowId,
              target,
              hour: `${hh}:${mm}`,
              subProcessIndex,
              subProcess,
              type: 'Inhouse',
            });
          }
        }
      };

      const arr = [];
      const machineMap = allMachines.reduce((acc, curVal) => {
        acc[curVal?._id] = {
          ...curVal,
          speed:
            curVal?.itemsPerHour?.find((i) => i?.isDefault)?.value ||
            curVal?.itemsPerHour?.[0]?.value ||
            0,
          time:
            curVal?.changeOverTime?.find((i) => i?.isDefault)?.value ||
            curVal?.changeOverTime?.[0]?.value ||
            0,
        };
        return acc;
      }, {});

      for (let i = 0; i < goalsTable.length; i++) {
        const gt = goalsTable[i];
        for (let ii = 0; ii < gt?.tableData.length; ii++) {
          const tData = gt?.tableData[ii];

          if (tData?.subProcesses && tData?.subProcesses?.length > 0) {
            for (
              let subProcessIndex = 0;
              subProcessIndex < tData?.subProcesses.length;
              subProcessIndex++
            ) {
              const subProcess = tData?.subProcesses[subProcessIndex];
              generateData(gt, tData, ii, subProcess, subProcessIndex);
            }
          }

          generateData(gt, tData, ii);
        }
      }

      setMachineSchedules([...(machineSchedulesForJob || []), ...arr]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [machineSchedulesForJob, allMachines, selectedMachinesDepArray]);

  useEffect(() => {
    const uniqueIds = new Set();

    const ids = productionFlow?.processes
      ?.filter((pro) => {
        uniqueIds.add(pro?.mqtt?._id);
        return (
          pro?.processCategory === 'Inhouse' || pro?.processCategory === 'QC'
        );
      })
      ?.map((i) => i?.mqtt?._id || i?.mqtt);

    getAllUserByProcessAccess({ ids: [...uniqueIds] });

    getMachinesByMqtt({ data: { ids } })
      .unwrap()
      .then((res) => {
        if (res?.length > 0) {
          const macIds = res.map((i) => i._id);

          getShiftsForMachines({ data: { macIds } });
        }
      });
  }, [
    getMachinesByMqtt,
    productionFlow,
    getShiftsForMachines,
    getAllUserByProcessAccess,
  ]);

  useEffect(() => {
    if (id)
      getCreateInputByID({ id })
        .unwrap()
        .then((res) => setGoalsTable(res.goalsTable));
  }, [id, getCreateInputByID]);

  useEffect(() => {
    if (goalsTable) {
      let tempArr = [];
      goalsTable?.forEach((gt) => {
        gt.tableData?.forEach((tdt) => {
          tempArr.push(tdt?.startDate && tdt?.stopDate ? 'p' : 'u');
        });
      });

      const isUnPlanned = tempArr.every((i) => i === 'u');
      const isPlanned = tempArr.every((i) => i === 'p');

      setPlanningStatus(
        isUnPlanned ? 'unScheduled' : isPlanned ? 'scheduled' : 'partial'
      );
    }
  }, [goalsTable]);

  const editHandler = () => {
    if (step === 0) {
      setStep(1);
      return;
    }

    const filtered = machineSchedules
      ?.filter((i) =>
        i?.isRemoved
          ? true
          : i?.start &&
            i?.stop &&
            (i?._id ? i?.isEdited : true) &&
            (i?.type === 'QC' ? i?.assignedUsers?.length > 0 : true)
      )
      ?.map((i) => {
        if (i?.type === 'QC') return i;
        const [hh = 0, mm = 0] = i?.hour?.split(':');
        return { ...i, hour: `${padZero(+hh)}:${padZero(+mm)}` };
      });

    editManyGoalsTable({
      data: {
        goalsTable,
        planningStatus,
        createInputId: id,
        machineSchedules: filtered,
      },
    })
      .unwrap()
      .then(() => {
        toast.success('Job planning sucessful', { toastId: 'sucess' });
        navigate('/jobs/jobplanner');
      });
  };

  const allBatches =
    createInput?.goalsTable?.[0]?.tableData?.map((_i, idx) => idx) || [];

  return (
    <div className="">
      <div className="px-2 sm:px-4 lg:px-6 pb-16">
        <Header
          title="Planner"
          description="Schedule your jobs here"
          hasInfoPopup={false}
        />
        <Button
          onClick={() => navigate(-1)}
          type="primary"
          icon={<LeftOutlined />}
        >
          Back
        </Button>
      </div>

      {/* Progress Steps */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <nav className="flex items-center justify-center" aria-label="Progress">
          <ol className="flex items-center space-x-5">
            {['Machines', 'Planning'].map((page, index) => (
              <li key={page} className="flex items-center">
                <button
                  onClick={() => setStep(index)}
                  className={`${
                    index <= step
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-500'
                  } rounded-full h-8 w-8 flex items-center justify-center text-sm font-medium`}
                >
                  {index + 1}
                </button>
                <span
                  className={`ml-4 text-sm font-medium ${
                    index <= step ? 'text-blue-500' : 'text-gray-500'
                  }`}
                >
                  {page}
                </span>
                {index < 1 && <div className="ml-4 h-0.5 w-16 bg-gray-200" />}
              </li>
            ))}
          </ol>
        </nav>
      </div>

      {/* Main Content */}
      <div className="px-2 sm:px-4 lg:px-6 pb-16">
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">
              {step === 0 ? 'Select Machines' : 'Batch Planning'}
            </h2>
            <div className="flex space-x-4">
              {step > 0 && (
                <Button
                  onClick={() => setStep(0)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Back
                </Button>
              )}
              <Button
                isLoading={isLoadingEdit}
                onClick={() => editHandler()}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                {step === 0 ? 'Save and Proceed' : 'Submit'}
              </Button>
            </div>
          </div>

          <div className="px-6 py-4">
            {isFetchingGet ? (
              <div className="flex justify-center py-12">
                <Spinner className="h-8 w-8 text-blue-600" />
              </div>
            ) : (
              <div className="overflow-x-auto">
                {step === 0 ? (
                  <MachineSelection
                    productionFlow={productionFlow}
                    goalsTable={goalsTable}
                    setGoalsTable={setGoalsTable}
                    allMachines={allMachines}
                    machineSchedules={machineSchedules}
                  />
                ) : (
                  <main className="px-2 sm:px-4 lg:px-6">
                    <div className="space-y-4">
                      {allBatches?.map((batch) => {
                        const batchData = goalsTable?.map((gt) => {
                          return { ...gt?.tableData?.[batch] };
                        });
                        return (
                          <BatchCard
                            key={batch}
                            batchData={batchData}
                            productionFlow={productionFlow}
                            goalsTable={goalsTable}
                            setGoalsTable={setGoalsTable}
                            machineSchedules={machineSchedules}
                            setMachineSchedules={setMachineSchedules}
                            allMachines={allMachines}
                            isSequential={isSequential}
                            customMachineTimes={customMachineTimes}
                            allLocations={allLocations}
                            usersForAssigning={usersForAssigning}
                          />
                        );
                      })}
                    </div>
                  </main>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlannerV2;
