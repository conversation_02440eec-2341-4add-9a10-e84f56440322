import { Modal, Table, Tag, Input, Button } from 'antd';
import { SendOutlined } from '@ant-design/icons';
import { useState, useEffect } from 'react';
import ShortUniqueId from 'short-unique-id';
import { toast } from 'react-toastify';
import { useAddStockRequestMutation } from '../../../slices/stockRequestApiSlice';
import { useGetSelectedPartsMutation } from '../../../slices/partApiSlice';
import { customConfirm } from '../../../utils/customConfirm';

const GetStockStatus = ({ rawMaterials, goalsData }) => {
  const usersStoreAccess = JSON.parse(localStorage.getItem('user'))?.user
    ?.storeAccess;

  const [stockInfo, setStockInfo] = useState([]);
  const [isOpenModal, setIsOpenModal] = useState(false);

  const uid = new ShortUniqueId();

  const [getSelectedParts] = useGetSelectedPartsMutation();

  const [addStockRequest] = useAddStockRequestMutation();

  let keys = Object.keys(goalsData || {});
  let ordQty = goalsData?.[keys?.[0]]?.['Order Quantity'] || 0;

  useEffect(() => {
    if (rawMaterials?.length > 0) {
      let stockDetails = [];
      let rmIds =
        rawMaterials?.map((rm) => rm?.item || rm?.part || rm?.value) || [];
      const fetchData = async () => {
        const data = await getSelectedParts({ data: { ids: rmIds } }).unwrap();
        if (data) {
          let orderQuantity = goalsData?.[keys[0]]?.['Order Quantity'] || 0;
          data?.forEach((item, index) => {
            let parentUid = uid.rnd(10);
            let requiredStock =
              +orderQuantity * (rawMaterials?.[index]?.units || 0);
            let netQty = 0;
            let storeData = item?.stores
              ?.map((store) => {
                if (usersStoreAccess?.includes(store?._id)) {
                  let storeQuantity =
                    item?.storeWiseQuantity?.find(
                      (s) => (s?.store || s?.store?._id) === store?._id
                    )?.remainingQuantity || 0;
                  netQty += storeQuantity;
                  return {
                    storeName: store.name,
                    id: store?._id,
                    storeQuantity,
                    requiredStock,
                    isAccesible: true,
                    uid: uid.rnd(10),
                    parentUid,
                  };
                }
                return null;
              })
              .filter(Boolean);

            stockDetails.push({
              key: index,
              storesInfo: storeData,
              item: item,
              name: item?.name || '',
              isDeficient: netQty < requiredStock,
              freeStock: netQty,
              requiredStock,
              uid: parentUid,
            });
          });
          setStockInfo(stockDetails);
        }
      };
      fetchData();
    }
  }, [rawMaterials, getSelectedParts, goalsData]); // eslint-disable-line

  const sendStockRequestHandler = async (record) => {
    let selectedItem = stockInfo?.find((s) => s?.uid === record?.parentUid);
    let selectedStore = selectedItem?.storesInfo?.find(
      (s) => s?.uid === record?.uid
    );

    if (selectedStore?.requestedStock <= 0) {
      toast.error('Requested Stock should be greater than 0');
      return;
    }

    const confirm = await customConfirm(
      `Are you sure you want to send stock request? Requested Stock: ${selectedStore?.requestedStock} of ${selectedItem?.item?.name || ''} to ${selectedStore?.storeName || ''}`,
      'success'
    );
    if (!confirm) {
      return;
    }
    if (confirm) {
      let data = {
        item: selectedItem?.item?._id || selectedItem?.item,
        itemName: selectedItem?.item?.name,
        itemType: 'Part',
        quantity: selectedStore?.requestedStock,
        storeRequestedFor: selectedStore?.id,
        comment: '',
        //  enum => raised, fulfilled, rejected
        status: 'raised',
        raisedBy: JSON.parse(localStorage.getItem('user'))?.user?._id,
      };

      try {
        const res = await addStockRequest(data).unwrap();
        if (!res.error) {
          toast.success(
            `Stock Request Sent for ${selectedStore?.storeName || ''} of ${selectedItem?.item?.name || ''}`
          );
        }
      } catch (error) {
        console.log(error); // eslint-disable-line
      }
    }
  };

  const expandedRowRender = (record) => (
    <Table
      columns={[
        { title: 'Store Name', dataIndex: 'storeName', key: 'storeName' },
        {
          title: 'Available Stock',
          dataIndex: 'storeQuantity',
          key: 'storeQuantity',
        },

        {
          title: 'Request Stock',

          render: (_, record) => {
            return (
              <Input
                type="number"
                min={0}
                defaultValue={0}
                style={{ width: '60%' }}
                onChange={(e) => {
                  let value = e.target.value;

                  let temp = [...stockInfo];
                  let index = temp?.findIndex(
                    (s) => s?.uid === record?.parentUid
                  );
                  temp[index] = {
                    ...temp[index],
                    storesInfo: [
                      ...temp[index]?.storesInfo?.map((s) => {
                        if (s?.uid === record?.uid) {
                          return {
                            ...s,
                            requestedStock: +value,
                          };
                        }
                        return s;
                      }),
                    ],
                  };
                  setStockInfo(temp);
                }}
                value={record?.requestedStock}
                addonAfter={
                  <Button
                    icon={<SendOutlined />}
                    size="small"
                    onClick={() => {
                      sendStockRequestHandler(record);
                    }}
                  />
                }
              />
            );
          },
        },
      ]}
      dataSource={record.storesInfo}
      pagination={false}
    />
  );

  return (
    <>
      <Modal
        open={isOpenModal}
        onCancel={() => setIsOpenModal(false)}
        title="Stock Status"
        footer={null}
        width={700}
        centered
      >
        <section className="h-[55vh] overflow-y-scroll mt-10">
          <Table
            columns={[
              { title: 'Name', dataIndex: 'name', key: 'name' },
              {
                title: 'Available Stock',
                dataIndex: 'freeStock',
                key: 'freeStock',
              },
              {
                title: 'Required Stock',
                dataIndex: 'requiredStock',
                key: 'requiredStock',
              },
              {
                title: 'Status',
                dataIndex: 'isDeficient',
                key: 'isDeficient',
                render: (isDeficient, record) => (
                  <Tag color={!isDeficient ? 'green' : 'red'}>
                    {!isDeficient
                      ? 'In Stock'
                      : record?.freeStock > 0
                        ? 'Low Stock'
                        : 'Out of Stock'}
                  </Tag>
                ),
              },
            ]}
            dataSource={stockInfo}
            pagination={false}
            expandable={{
              expandedRowRender: expandedRowRender,
            }}
            // expandedRowRender={expandedRowRender}
            // defaultExpandAllRows
          />
        </section>
      </Modal>
      {ordQty > 0 && (
        <p className="text-md text-black font-bold flex justify-center items-center mt-6 ml-2">
          <Tag
            onClick={() => setIsOpenModal(true)}
            color={
              !stockInfo?.some((item) => item?.isDeficient) ? 'green' : 'red'
            }
          >
            {!stockInfo?.some((item) => item?.isDeficient)
              ? 'In Stock '
              : 'Low Stock '}
          </Tag>
        </p>
      )}
    </>
  );
};

export default GetStockStatus;
