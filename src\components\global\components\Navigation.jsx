import { Collapse, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import AllIcons from '../../v2/global/sidebar/icons';

const Navigation = ({ modules }) => {
  const { Panel } = Collapse;
  const location = useLocation();
  const [moduleNavs, setModuleNavs] = useState([]);
  const [settingNavs, setSettingNavs] = useState([]);

  useEffect(() => {
    if (modules) {
      const allNavs = modules.flatMap((dept) => dept.navs);

      const moduleItems = [];
      const settingItems = [];

      allNavs.forEach((nav) => {
        if (
          nav.childNavs?.some((child) => child.cslug?.startsWith('/setting'))
        ) {
          settingItems.push(nav);
        } else {
          moduleItems.push(nav);
        }
      });

      setModuleNavs(moduleItems);
      setSettingNavs(settingItems);
    }
  }, [modules]);

  const NavigationContent = ({ items }) => (
    <Collapse
      className="bg-white border-0 overflow-y-auto"
      expandIconPosition="end"
      accordion
    >
      {items?.map((nav) => {
        const NavIcon = AllIcons[nav.icon] || AllIcons.dashboard;
        const isActive = nav.childNavs?.some((child) =>
          location.pathname.startsWith(child.cslug)
        );

        return (
          <Panel
            key={nav.id}
            header={
              <div className="flex items-center gap-2 py-1">
                <NavIcon
                  className={`text-lg ${isActive ? 'text-blue-600' : 'text-gray-600'}`}
                />
                <span
                  className={`font-medium ${isActive ? 'text-blue-600' : 'text-gray-700'}`}
                >
                  {nav.name}
                </span>
              </div>
            }
            className={`
                            border-0 border-b border-gray-100 
                            ${isActive ? 'bg-blue-50' : 'bg-white'}
                        `}
          >
            <div className="pl-4">
              {nav.childNavs?.map((child) => {
                const ChildIcon = AllIcons[child.icon] || AllIcons.primary;
                const isChildActive = location.pathname.startsWith(child.cslug);

                return (
                  <Link
                    key={child.id}
                    to={child.cslug}
                    className={`
                                            flex items-center gap-2 px-4 py-2.5 rounded-lg mb-1
                                            transition-all duration-200
                                            ${
                                              isChildActive
                                                ? 'bg-blue-100 text-blue-600 font-medium'
                                                : 'text-gray-600 hover:bg-gray-100'
                                            }
                                        `}
                  >
                    <ChildIcon
                      className={`text-lg ${isChildActive ? 'text-blue-600' : ''}`}
                    />
                    <span>{child.cname}</span>
                  </Link>
                );
              })}
            </div>
          </Panel>
        );
      })}
    </Collapse>
  );

  const items = [
    {
      key: 'modules',
      label: (
        <div className="flex items-center gap-2">
          <AllIcons.dashboard className="text-lg" />
          <span>Modules</span>
        </div>
      ),
      children: <NavigationContent items={moduleNavs} />,
    },
    {
      key: 'settings',
      label: (
        <div className="flex items-center gap-2">
          <AllIcons.setUp className="text-lg" />
          <span>Settings</span>
        </div>
      ),
      children: <NavigationContent items={settingNavs} />,
    },
  ];

  return (
    <Tabs
      defaultActiveKey="modules"
      items={items}
      className="nav-tabs"
      animated={{ tabPane: true }}
      centered
    />
  );
};
export default Navigation;
