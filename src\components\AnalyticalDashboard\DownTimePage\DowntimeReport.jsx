import React from 'react';
import DowntimeTable from './DowntimeTable';
import HeatMapPanel from './HeatMapPanel';
import HeatMap from './Heatmap/HeatMap';

/* eslint-disable */
const DowntimeReport = React.forwardRef((props, ref) => {
  /* eslint-enable */

  const {
    machine,
    panelDetails,
    date,
    data,
    details,
    responsiveDetails,
    idleTime,
    setPanelDetails,
    newKpi,
    oldDate,
    oldData,
    oldDetails,
    oldIdleTime,
    deviceDatas,
  } = props;
  const { contentRef } = ref;

  return (
    <div>
      <HeatMapPanel machineId={machine?._id} panelDetails={panelDetails} />

      <div
        ref={contentRef}
        className={`mx-auto flex flex-col justify-center items-center text-sm transition-[width] ease-in-out duration-1000 ${
          machine?._id ? 'w-[90%]' : 'w-0'
        }`}
      >
        {machine?._id && date && (
          <>
            <HeatMap
              data={data}
              details={details}
              responsiveDetails={responsiveDetails}
              idleTime={idleTime}
              isFor={'new'}
              setPanelDetails={setPanelDetails}
              kpiParam={newKpi?.kpi}
            />
            <DowntimeTable
              data={data}
              details={details}
              idleTime={idleTime}
              isFor={'new'}
              setPanelDetails={setPanelDetails}
              kpiParam={newKpi?.kpi}
              deviceDatas={deviceDatas}
            />
            {oldDate && (
              <>
                <div className="w-full hidden">
                  <HeatMap
                    data={oldData}
                    details={oldDetails}
                    responsiveDetails={responsiveDetails}
                    idleTime={oldIdleTime}
                    isFor={'old'}
                    setPanelDetails={setPanelDetails}
                    kpiParam={newKpi?.kpi}
                  />
                  <DowntimeTable
                    data={oldData}
                    details={oldDetails}
                    idleTime={oldIdleTime}
                    isFor={'old'}
                    setPanelDetails={setPanelDetails}
                    kpiParam={newKpi?.kpi}
                    deviceDatas={deviceDatas}
                  />
                </div>
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
});

export default DowntimeReport;
