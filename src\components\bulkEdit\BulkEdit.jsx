import { useEffect, useState } from 'react';
import { Button, Modal, Pagination } from 'antd';
import { PencilIcon } from 'lucide-react';
import BulkEditTable from './BuldEditTable';
import {
  useGetOptionsMutation,
  useGetRowsQuery,
} from '../../slices/bulkEditApiSlice';
import { toast } from 'react-toastify';

export default function BulKEdit({
  rowModel,
  columns = [],
  isNested = false,
  handleSubmit,
  submitLoading,
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editData, setEditData] = useState({});
  const [page, setPage] = useState(1);

  const [getOptions, { data: optionsMap = {}, isLoading: optionsLoading }] =
    useGetOptionsMutation();
  const {
    data: rowsData = {},
    isLoading: rowsLoading,
    isFetching: rowsFetching,
  } = useGetRowsQuery(
    { page, limit: 50, model: rowModel, isNested },
    { skip: !rowModel || !isModalOpen, refetchOnMountOrArgChange: true }
  );
  const { results: rows, totalResults } = rowsData;

  useEffect(() => {
    if (columns?.length > 0 && isModalOpen) {
      let newCols = [];
      for (let i = 0; i < columns.length; i++) {
        const col = columns[i];
        if (col?.fetchOptions) {
          newCols.push({ ...col.fetchOptions, field: col.field });
        }
      }
      if (newCols?.length > 0) {
        getOptions({ data: { columns: newCols } });
      }
    }
  }, [columns, getOptions, isModalOpen]);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    const data = Object.values(editData);
    if (data?.length === 0) {
      toast.warn('No changes detected, please edit before submiting');
      return;
    }
    if (handleSubmit) {
      const res = await handleSubmit(data);
      if (res) setIsModalOpen(false);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <Button type="text" onClick={showModal}>
        <PencilIcon className="h-4 outline-none cursor-pointer" />
      </Button>
      <Modal
        title="Bulk Edit"
        closable={{ 'aria-label': 'Custom Close Button' }}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={'90%'}
        footer={[
          <div className="flex items-center justify-between w-full" key={1}>
            <Pagination
              simple
              onChange={(val) => setPage(val)}
              total={totalResults}
              pageSize={50}
              showSizeChanger={false}
            />
            <Button
              key="submit"
              type="primary"
              loading={submitLoading}
              onClick={handleOk}
            >
              Submit
            </Button>
          </div>,
        ]}
      >
        <div className="max-h-[70vh] overflow-y-auto">
          {isModalOpen && (
            <>
              <BulkEditTable
                columns={columns}
                rows={rows}
                optionsMap={optionsMap}
                editData={editData}
                setEditData={setEditData}
                isNested={isNested}
                rowsLoading={rowsLoading}
                rowsFetching={rowsFetching}
                optionsLoading={optionsLoading}
              />
            </>
          )}
        </div>
      </Modal>
    </>
  );
}
