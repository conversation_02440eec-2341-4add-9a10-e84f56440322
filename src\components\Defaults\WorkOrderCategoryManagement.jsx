import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Select from '../global/components/Select';
import React, { useState } from 'react';
import { TrashIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { customConfirm } from '../../utils/customConfirm';

const WorkOrderCategoryManagement = ({ defaults }) => {
  const [workOrderCategory, setWorkOrderCategory] = useState({
    label: '',
    value: '',
    isUsed: false,
    isDefault: false,
  });
  const [isEdit, setIsEdit] = useState(false);
  const [editIndex, setEditIndex] = useState(null);

  const [updateDefaults, { isLoading: isUpdateDefaultsLoading }] =
    useUpdateDefaultsMutation();

  const handleColDelete = async (index, isUsed) => {
    if (isUsed) {
      const userConfirmed = await customConfirm(
        'Are you sure want to delete used category ?',
        'delete'
      );

      if (userConfirmed) {
        let data = defaults?.woCategories?.filter((_, i) => i !== index);

        await updateDefaults({ woCategories: data });

        toast.success('Column deleted successfully');
      }
    } else {
      const userConfirmed = await customConfirm('Are you sure?', 'delete');

      if (userConfirmed) {
        let data = defaults?.woCategories?.filter((_, i) => i !== index);

        await updateDefaults({ woCategories: data });

        toast.success('Column deleted successfully');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!workOrderCategory.value && !workOrderCategory.label) {
      toast.error('Please fill all the fields');
      return;
    }

    if (isEdit) {
      let data = defaults?.woCategories?.map((item, index) => {
        if (index === editIndex) {
          return {
            ...item,
            label: workOrderCategory.label,
            value: workOrderCategory.value,
            isUsed: workOrderCategory.isUsed,
            isDefault: false,
          };
        }
        return item;
      });
      await updateDefaults({ ...defaults, woCategories: data }).unwrap();
      setIsEdit(false);
      setEditIndex(null);
      setWorkOrderCategory({
        label: '',
        value: '',
        isUsed: false,
        isDefault: false,
      });
      toast.success('Column updated successfully!');
      return;
    }

    const exist = defaults?.woCategories?.find((item) => {
      return (
        item.label.toLowerCase() === workOrderCategory?.label?.toLowerCase()
      );
    });

    if (exist) {
      toast.warning('Same column cannot be added', {
        position: toast.POSITION.TOP_RIGHT,
        toastId: 'warning',
      });
      setWorkOrderCategory({
        label: '',
        value: '',
        isUsed: false,
        isDefault: false,
      });
      return;
    }

    const data = [
      ...(defaults?.woCategories || []),
      {
        label: workOrderCategory?.label,
        value: workOrderCategory?.value,
        isUsed: workOrderCategory?.isUsed || false,
        isDefault: false,
      },
    ];

    await updateDefaults({ ...defaults, woCategories: data }).unwrap();
    setWorkOrderCategory({
      label: '',
      value: '',
      isUsed: false,
      isDefault: false,
    });
    toast.success('Column added successfully!');
  };

  return (
    <div>
      <div className="w-full">
        <div className="flex gap-3 mb-4">
          <div>
            <label>Enter Field Name</label>{' '}
            <span className="text-xl text-red-500 -mt-1 -ml-1">*</span>
            <Input
              type="text"
              placeholder="Please enter name"
              onChange={(e) =>
                setWorkOrderCategory((prev) => ({
                  ...prev,
                  label: e.target.value,
                }))
              }
              value={workOrderCategory?.label}
              disabled={isUpdateDefaultsLoading}
            />
          </div>
          <div>
            <label>Enter Field Type</label>{' '}
            <span className="text-xl text-red-500 -mt-1 -ml-1">*</span>
            <Select
              value={workOrderCategory?.value}
              menuPlacement="top"
              options={[
                { value: 'Inhouse', label: 'Inhouse' },
                { value: 'Inventory', label: 'Inventory' },
                { value: 'Outsource', label: 'Outsource' },
              ]}
              onChange={(e) => {
                setWorkOrderCategory((prev) => ({
                  ...prev,
                  value: e.target.value,
                }));
              }}
            />
          </div>

          <Button
            onClick={handleSubmit}
            isLoading={isUpdateDefaultsLoading}
            className={'mt-7'}
          >
            Add
          </Button>
        </div>
        <ol>
          {defaults?.woCategories?.map((val, index) => (
            <React.Fragment key={index}>
              <div className="w-full md:w-[270px] my-1  h-auto flex justify-between">
                <span className="ml-2 text-sm">
                  {index + 1}
                  {'.'}
                </span>
                <li
                  className="text-sm ml-3 w-full underline text-blue-500"
                  onClick={() => {
                    if (val.isDefault) {
                      toast.error('Cannot edit, Default!!');
                      return;
                    }
                    setIsEdit(true);
                    setEditIndex(index);
                    setWorkOrderCategory(val);
                  }}
                >
                  {val.label}
                </li>
                {!val.isDefault && (
                  <TrashIcon
                    className="cursor-pointer text-red-500 hover:fill-red-900 w-6 h-6"
                    onClick={() => handleColDelete(index, val.isUsed)}
                  />
                )}
              </div>
              <hr className="w-[310px]" key={`hr-${index}`} />
            </React.Fragment>
          ))}
        </ol>
      </div>
    </div>
  );
};

export default WorkOrderCategoryManagement;
