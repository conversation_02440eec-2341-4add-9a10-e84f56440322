import {
  BellOutlined,
  EditOutlined,
  MailOutlined,
  MinusCircleOutlined,
  PlusOutlined,
  SoundOutlined,
  WhatsAppOutlined,
} from '@ant-design/icons';
import { Button, Form, Input, Modal, Space, Tag } from 'antd';
import { useEffect } from 'react';
import { useGetQuotationStatusesQuery } from '../../../slices/quotationApiSlice';
import { useGetUsersForOptionsQuery } from '../../../slices/userApiSlice';
import MultiSelect from '../../global/components/MultiSelect';
import SelectV2 from '../../global/components/SelectV2';

const pages = ['Quotation', 'Sales Order', 'Assembly Dashboard'];

const triggerEvents = [
  {
    value: 'Status Change',
    label: 'When Status Updates',
    description:
      'Trigger reminder immediately when quotation/order status changes',
  },
  {
    value: 'Status Delay',
    label: 'When Status Overdue',
    description:
      'Trigger reminder when status remains unchanged for specified days',
  },
  {
    value: 'Low Stock',
    label: 'When Low Stock',
    description:
      'Trigger reminder when low stock is detected for specified days',
  },
];

const channels = [
  {
    name: 'Optiwise Push Notification',
    value: 'push',
  },
  // {
  //   name: 'Email',
  //   value: 'email',
  // },
  {
    name: 'WhatsApp',
    value: 'whatsapp',
  },
];
const defaultStatuses = ['pending', 'approved', 'rejected'];

const delayNumbers = Array.from({ length: 30 }, (_, i) => i + 1);
const getChannelIcon = (type) => {
  const icons = {
    email: <MailOutlined />,
    whatsapp: <WhatsAppOutlined />,
    push: <SoundOutlined />,
  };
  return icons[type] || <BellOutlined />;
};

const ReminderCreationModal = ({
  form,
  showForm,
  editingReminder,
  handleCancelEdit,
  handleSubmitReminder,
  isSubmitting,
}) => {
  const { data: statuses } = useGetQuotationStatusesQuery();
  const { data: users } = useGetUsersForOptionsQuery();

  useEffect(() => {
    if (editingReminder && showForm) {
      const formValues = {
        ...editingReminder,
        channel: editingReminder.channel,
        whatsappTimes: editingReminder.whatsappSettings?.times,
        whatsappInterval: editingReminder.whatsappSettings?.interval,
      };

      // Populate contact information fields
      if (editingReminder.contactInformation) {
        Object.entries(editingReminder.contactInformation).forEach(
          ([key, values]) => {
            formValues[key] = values;
          }
        );
      }

      form.setFieldsValue(formValues);
    } else if (!editingReminder && showForm) {
      form.resetFields();
    }
  }, [editingReminder, showForm, form]);

  const onFinish = (values) => {
    if (Array.isArray(values.statuses)) {
      values.statuses = values.statuses.map((s) =>
        typeof s === 'object' && s !== null && 'value' in s ? s.value : s
      );
    }

    const contactInformation = {};
    Object.keys(values).forEach((key) => {
      if (/_/.test(key) && values[key]) {
        if (Array.isArray(values[key])) {
          const filteredValues = values[key].filter(Boolean);
          if (filteredValues.length > 0) {
            contactInformation[key.toLowerCase()] = filteredValues;
          }
        }
        delete values[key];
      }
    });
    if (Object.keys(contactInformation).length > 0) {
      values.contactInformation = contactInformation;
    }

    if (values.employees && Array.isArray(values.employees)) {
      values.employees = values.employees.map((emp) =>
        typeof emp === 'object' && emp !== null && 'value' in emp
          ? emp.value
          : emp
      );
    }

    handleSubmitReminder(values);
  };

  return (
    <Modal
      title={editingReminder ? 'Edit Reminder' : 'Create Reminder'}
      open={showForm}
      onCancel={handleCancelEdit}
      footer={null}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          whatsappTimes: 1,
          whatsappInterval: 1,
          sendToCustomer: false,
          status: 'active',
          priority: 'medium',
        }}
      >
        <Form.Item name="channel" label="channel" rules={[{ required: true }]}>
          <SelectV2
            mode="multiple"
            placeholder="Select channel"
            options={channels.map((c) => ({ label: c.name, value: c.value }))}
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prev, curr) => prev.channel !== curr.channel}
        >
          {({ getFieldValue }) => {
            const selectedchannel = getFieldValue('channel') || [];
            const hasPushNotification = selectedchannel.includes('push');

            return hasPushNotification ? (
              <Form.Item
                name="employees"
                label="Select Employees for Push Notification"
                rules={[
                  {
                    required: true,
                    message: 'Please select at least one employee',
                  },
                ]}
              >
                <MultiSelect
                  placeholder="Select Employees"
                  options={(users || []).map((emp) => ({
                    label: emp.name || emp.email,
                    value: emp._id,
                  }))}
                />
              </Form.Item>
            ) : null;
          }}
        </Form.Item>
        <div className="grid grid-cols-2 gap-4">
          <Form.Item name="page" label="Page" rules={[{ required: true }]}>
            <SelectV2
              placeholder="Select Page"
              options={pages.map((m) => ({ label: m, value: m }))}
            />
          </Form.Item>
          <Form.Item
            name="triggerEvent"
            label="Event"
            rules={[{ required: true }]}
          >
            <SelectV2
              placeholder="Select Event"
              options={triggerEvents.map((e) => ({
                label: e.label,
                value: e.value,
              }))}
            />
          </Form.Item>
        </div>
        <Form.Item
          noStyle
          shouldUpdate={(prev, curr) => prev.triggerEvent !== curr.triggerEvent}
        >
          {({ getFieldValue }) => {
            const triggerEvent = getFieldValue('triggerEvent');
            const selectedEvent = triggerEvents.find(
              (e) => e.value === triggerEvent
            );
            return selectedEvent ? (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <p className="text-sm text-blue-700">
                  {selectedEvent.description}
                </p>
              </div>
            ) : null;
          }}
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prev, curr) => prev.triggerEvent !== curr.triggerEvent}
        >
          {({ getFieldValue }) => {
            const triggerEvent = getFieldValue('triggerEvent');
            return triggerEvent === 'Status Change' ? (
              <Form.Item
                name="statuses"
                label="Statuses"
                rules={[{ required: true }]}
              >
                <MultiSelect
                  placeholder="Select Statuses"
                  options={[
                    ...defaultStatuses,
                    ...(statuses?.status || []),
                  ].map((s) => ({ label: s, value: s }))}
                />
              </Form.Item>
            ) : triggerEvent === 'Status Delay' ? (
              <div className="grid grid-cols-2 gap-4">
                <Form.Item
                  name="statuses"
                  label="Statuses"
                  rules={[{ required: true }]}
                >
                  <MultiSelect
                    placeholder="Select Statuses"
                    options={[
                      ...defaultStatuses,
                      ...(statuses?.status || []),
                    ].map((s) => ({ label: s, value: s }))}
                  />
                </Form.Item>
                <Form.Item
                  name="delayNumber"
                  label="Delay (Days)"
                  rules={[{ required: true }]}
                >
                  <SelectV2
                    placeholder="Select delay"
                    options={delayNumbers.map((n) => ({ label: n, value: n }))}
                  />
                </Form.Item>
              </div>
            ) : triggerEvent === 'Low Stock' ? (
              <Form.Item
                name="delayNumber"
                label="Stock Alert (Days)"
                tooltip="Notify you when stock is running low and may impact production for the next X days"
                rules={[{ required: true }]}
              >
                <SelectV2
                  placeholder="Select delay"
                  options={delayNumbers.map((n) => ({ label: n, value: n }))}
                />
              </Form.Item>
            ) : null;
          }}
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prev, curr) =>
            prev.channel !== curr.channel || prev.statuses !== curr.statuses
          }
        >
          {({ getFieldValue }) => {
            const selectedchannelRaw = getFieldValue('channel');
            const selectedchannel = Array.isArray(selectedchannelRaw)
              ? selectedchannelRaw.filter((channel) => channel !== 'push')
              : selectedchannelRaw && selectedchannelRaw !== 'push'
                ? [selectedchannelRaw]
                : [];
            const statusesValue = getFieldValue('statuses');
            const selectedStatuses = Array.isArray(statusesValue)
              ? statusesValue.map((s) =>
                  typeof s === 'object' ? s.value || s.label || s : s
                )
              : statusesValue
                ? [
                    typeof statusesValue === 'object'
                      ? statusesValue.value ||
                        statusesValue.label ||
                        statusesValue
                      : statusesValue,
                  ]
                : [];
            const triggerEvent = getFieldValue('triggerEvent');

            return (
              <>
                {(triggerEvent === 'Status Change' ||
                  triggerEvent === 'Status Delay') &&
                  selectedStatuses.length > 0 &&
                  selectedchannel.length > 0 && (
                    <div className="space-y-4">
                      <h4 className="font-medium">
                        Contact Information by Status
                      </h4>
                      {selectedStatuses.map((status) => (
                        <div key={status} className="border p-3 rounded">
                          <h5 className="font-medium mb-3">
                            <Tag color="blue">{status}</Tag>
                          </h5>

                          {selectedchannel.map((channel) => (
                            <div key={`${status}_${channel}`} className="mb-4">
                              <label className="block text-sm font-medium mb-2">
                                {getChannelIcon(channel)}{' '}
                                {channel === 'email'
                                  ? 'Emails'
                                  : 'Phone Numbers'}{' '}
                                for {channel}
                              </label>
                              <Form.List name={`${status}_${channel}`}>
                                {(fields, { add, remove }) => (
                                  <>
                                    {fields.map(
                                      ({ key, name, ...restField }) => (
                                        <Space
                                          key={key}
                                          className="flex mb-2"
                                          align="baseline"
                                        >
                                          <Form.Item
                                            {...restField}
                                            name={name}
                                            className="mb-0 flex-1"
                                          >
                                            <Input
                                              placeholder={
                                                channel === 'email'
                                                  ? 'Enter email address'
                                                  : 'Enter phone number'
                                              }
                                              type={
                                                channel === 'email'
                                                  ? 'email'
                                                  : 'tel'
                                              }
                                            />
                                          </Form.Item>
                                          <MinusCircleOutlined
                                            onClick={() => remove(name)}
                                          />
                                        </Space>
                                      )
                                    )}
                                    <Button
                                      type="dashed"
                                      onClick={() => add()}
                                      icon={<PlusOutlined />}
                                      className="w-full"
                                    >
                                      Add{' '}
                                      {channel === 'email'
                                        ? 'Email'
                                        : 'Phone Number'}
                                    </Button>
                                  </>
                                )}
                              </Form.List>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  )}
              </>
            );
          }}
        </Form.Item>
        <div className="flex justify-end gap-2 pt-4">
          <Button onClick={handleCancelEdit}>Cancel</Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={isSubmitting}
            icon={editingReminder ? <EditOutlined /> : <PlusOutlined />}
          >
            {editingReminder ? 'Update' : 'Create'}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ReminderCreationModal;
