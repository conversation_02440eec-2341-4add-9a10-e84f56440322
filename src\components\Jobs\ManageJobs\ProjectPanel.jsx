import { Switch } from '@headlessui/react';
import { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { apiSlice } from '../../../slices/apiSlice';
import { useHiddenListMutation } from '../../../slices/createInputApiSlice';
import { Store } from '../../../store/Store';
import Table from '../../global/components/Table';

function MyToggle({ isEnabled, updateHandler, projects, data }) {
  const [enabled, setEnabled] = useState(true);

  useEffect(() => {
    let check = false;
    projects?.forEach((item) => {
      if (data.hidden.includes(item.id) === false) {
        check = true;
      }
    });
    setEnabled(check);
  }, [projects]); // eslint-disable-line

  useEffect(() => {
    if (isEnabled !== null) {
      setEnabled(isEnabled);
    }
  }, [isEnabled]);

  return (
    <Switch
      checked={enabled}
      onChange={setEnabled}
      onClick={() => {
        updateHand<PERSON>();
      }}
      className={`bg-transparent relative inline-flex h-6 w-11 items-center shadow-[0_0_1px_1px_#AAAAAA] rounded-full`}
    >
      <span className="sr-only">Enable notifications</span>
      <span
        className={`${
          enabled
            ? 'translate-x-6 bg-blue-primary'
            : 'translate-x-1 bg-gray-400'
        } inline-block h-4 w-4 transform rounded-full transition`}
      />
    </Switch>
  );
}

const ProjectPanel = ({ data }) => {
  const dispatch = useDispatch();

  const [projects, setProjects] = useState([]);
  const [masterToggle, setMasterToggle] = useState(null);

  const [updateHiddenList] = useHiddenListMutation();

  const {
    mqttClient,
    refreshParams: { refreshUrl, refreshPayload },
  } = useContext(Store);

  const productionFlow = data?.productionFlow;

  const allToggler = (status) => {
    if (!status) {
      projects?.map(async (item) => {
        const res = await updateHiddenList({
          id: data._id,
          mode: 'add',
          data: {
            mqttId: item._id,
          },
        }).unwrap();
        if (res) {
          dispatch(apiSlice.util.invalidateTags(['CreateInputs']));
          if (mqttClient?.publish)
            mqttClient?.publish(refreshUrl, refreshPayload);
        }
      });
    } else {
      projects.map(async (item) => {
        const res = await updateHiddenList({
          id: data._id,
          mode: 'remove',
          data: {
            mqttId: item._id,
          },
        }).unwrap();
        if (res) {
          dispatch(apiSlice.util.invalidateTags(['CreateInputs']));

          if (mqttClient?.publish)
            mqttClient?.publish(refreshUrl, refreshPayload);
        }
      });
    }
  };

  useEffect(() => {
    setProjects([]);
    if (productionFlow?.processes) {
      productionFlow.processes.map((pro) => {
        setProjects((prev) => {
          const exists = prev.find((item) => item?._id === pro?.mqtt?._id);
          if (!exists) return [...prev, pro.mqtt];
          return prev;
        });
      });
    }
  }, [productionFlow?.processes]);

  const updateCreateInput = async (item) => {
    if (!data?.hidden?.includes(item._id)) {
      const res = await updateHiddenList({
        id: data._id,
        mode: 'add',
        data: {
          mqttId: item._id,
        },
      }).unwrap();
      if (res) {
        dispatch(apiSlice.util.invalidateTags(['CreateInputs']));
        if (data.hidden.length + 1 === projects.length) {
          setMasterToggle(false);
        } else {
          setMasterToggle(true);
        }
        if (mqttClient?.publish)
          mqttClient?.publish(refreshUrl, refreshPayload);
      }
    } else {
      const res = await updateHiddenList({
        id: data._id,
        mode: 'remove',
        data: {
          mqttId: item._id,
        },
      });
      if (res) {
        setMasterToggle(true);
        dispatch(apiSlice.util.invalidateTags(['CreateInputs']));
        if (mqttClient?.publish)
          mqttClient?.publish(refreshUrl, refreshPayload);
      }
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-start gap-1 gap-x-2 items-center my-3">
        <h3 className=" font-medium">Production Flow</h3>
        <MyToggle
          isEnabled={masterToggle}
          updateHandler={() => {
            allToggler(!masterToggle);
            setMasterToggle((prev) => !prev);
          }}
          projects={projects}
          data={data}
        />
      </div>
      <div className="w-full ">
        <Table>
          <Table.Head>
            <Table.Row>
              <Table.Th>Process</Table.Th>
              <Table.Th></Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {projects?.map((pro) => (
              <Table.Row key={pro?._id}>
                <Table.Td>{pro.process}</Table.Td>
                <Table.Td>
                  <MyToggle
                    isEnabled={!data?.hidden?.includes(pro._id)}
                    updateHandler={() => {
                      updateCreateInput(pro);
                    }}
                  />
                </Table.Td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>
    </div>
  );
};

export default ProjectPanel;
