import { Popover, Transition } from '@headlessui/react';
import { Fragment, useContext, useEffect, useState } from 'react';
// import { TiDelete } from 'react-icons/ti';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import PausedIcon from '../../../assets/images/paused.png';
import StartIcon from '../../../assets/images/started.png';
import StoppedIcon from '../../../assets/images/stopped.png';
// import { BellIcon } from '../../../assets/svgs/v2';
import { getInitials, getLocalDateTime } from '../../../helperFunction';
import {
  useArchiveNotificationMutation,
  useDeleteAllNotificationsMutation,
  // useDeleteNotificationMutation,
  useGetAllNotificationQuery,
  useMarkAllAsReadMutation,
  useMarkAsReadMutation,
} from '../../../slices/notificationApiSlice';
import { DepartmentStore } from '../../../store/DepartmentStore';

const Notifications = ({ user, showSideBar }) => {
  const { data: notiData = {}, refetch } = useGetAllNotificationQuery();
  const { notifications = [] } = notiData;
  const [markAsRead] = useMarkAsReadMutation();
  const [isTrueCalender, setIsTrueCalender] = useState(false); // eslint-disable-line
  const [markAllAsRead] = useMarkAllAsReadMutation();
  // const [deleteNotification] = useDeleteNotificationMutation();
  const [deleteAllNotifications] = useDeleteAllNotificationsMutation();
  const [archiveNotification] = useArchiveNotificationMutation();
  const [notificationDot, setNotificationDot] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const { notificationsRefresh, setChatAppOpen, setSenderId } =
    useContext(DepartmentStore);
  const [activeTab, setActiveTab] = useState('All');
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const navigate = useNavigate();

  const markAsReadHandler = async (id) => {
    await markAsRead({ id, data: { user: user._id } }).unwrap();
  };

  const markAllAsReadHandler = async () => {
    await markAllAsRead({ data: { user: user._id } }).unwrap();
  };
  const atLeastOneMatch = notifications.some(
    (noti) => noti.profileId === user.profileId
  );
  useEffect(() => {
    if (notifications) {
      const unreadNotifications = notifications.filter(
        (noti) => !noti?.read?.includes(user._id)
      );
      const myNoti = unreadNotifications?.filter(
        (noti) => noti.forUser === user._id
      );
      setUnreadCount(myNoti.length);
      if (activeTab === 'All') {
        setFilteredNotifications(notifications);
      } else {
        setFilteredNotifications(unreadNotifications);
      }
    }
  }, [notifications, activeTab, user._id]);

  useEffect(() => {
    // (async () => {
    //   await refetch();
    // })();
    const notificationDot = notifications?.find((noti) => {
      if (noti?.type !== 'shiftKanbanColumnNotify') {
        if (user._id === noti?.forUser) {
          setShowDelete(true);
        }
        return !noti?.read?.includes(user?._id) && user._id === noti?.forUser;
      } else if (noti?.type === 'shiftKanbanColumnNotify') {
        setShowDelete(true);
        return true;
      }
    });
    setNotificationDot(notificationDot ? true : false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetch, notificationsRefresh, user?._id]);

  // const deleteNotificationFunc = async (id) => {
  //   const res = await deleteNotification({ id });
  //   if (res) {
  //     toast.success('Notification Deleted ');
  //   }
  // };
  const deleteAllNotificationsFunc = async () => {
    //Passing Current User Id
    const res = await deleteAllNotifications({ userId: user._id });
    if (res) {
      toast.success('All Notification Deleted');
    }
    setShowDelete(false);
  };
  const archiveNotificationFunc = async (id) => {
    const res = await archiveNotification({ id, data: { user: user._id } });
    if (res) {
      toast.success('All Notification Archived');
    }
  };

  return (
    <Popover
      as="div"
      className={`relative z-10 aspect-square w-fit flex justify-center ${
        showSideBar ? 'h-3/5' : 'h-1/5 max-h-[25px] mx-auto'
      }`}
    >
      <Popover.Button className="outline-none relative">
        <div
          alt="Bell Icon"
          className="w-5 h-5 object-contain relative hover:bg-gray-200 rounded-full "
          onMouseEnter={() => setIsTrueCalender(true)}
          onMouseLeave={() => setIsTrueCalender(false)}
        >
          <div className="relative flex items-center group">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="w-5 h-5"
            >
              <g clipPath="url(#clip0_1254_6052)">
                <path
                  d="M7 0.5C8.13778 0.5 9.22896 0.951981 10.0335 1.75651C10.838 2.56104 11.29 3.65222 11.29 4.79C11.29 9.56 13.03 10.5 13.5 10.5H0.5C0.98 10.5 2.71 9.55 2.71 4.79C2.71 3.65222 3.16198 2.56104 3.96651 1.75651C4.77104 0.951981 5.86222 0.5 7 0.5V0.5Z"
                  stroke="#000001"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M5.5 12.33C5.58644 12.662 5.7806 12.9559 6.05205 13.1658C6.32351 13.3756 6.6569 13.4894 7 13.4894C7.3431 13.4894 7.67649 13.3756 7.94795 13.1658C8.2194 12.9559 8.41356 12.662 8.5 12.33"
                  stroke="#000001"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </g>
              <defs>
                <clipPath id="clip0_1254_6052">
                  <rect width="14" height="14" fill="white" />
                </clipPath>
              </defs>
            </svg>

            {notificationDot ? (
              <span className="absolute h-2 w-2 top-0 right-0 bg-red-500 rounded-full" />
            ) : (
              ''
            )}
            <span className="absolute top-[2rem] !right-[-40px] transform -translate-x-1/2 mt-2 w-max px-2 py-1 text-xs text-white bg-black rounded opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              Notification
            </span>
          </div>
        </div>
      </Popover.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Popover.Panel className="-left-[390px]  border-[1px] top-10 absolute min-w-[400px] rounded-lg overflow-y-scroll shadow-2xl no-scrollbar bg-white  h-[400px]">
          <div>
            <section className="w-full px-5 pt-5 mb-1 border-b shadow-md   text-black bg-white-200">
              <div className="flex justify-between mb-4 items-center">
                <p className="font-semibold text-gray-800">Notifications</p>
                <div className="flex gap-3">
                  {notificationDot && (
                    <button
                      className="text-[12px] text-blue-600 font-normal outline-none hover:cursor-pointer hover:underline"
                      onClick={markAllAsReadHandler}
                    >
                      Mark all as read
                    </button>
                  )}
                  {showDelete && (
                    <button
                      className="text-[12px] text-blue-600 font-normal outline-none hover:cursor-pointer hover:underline"
                      onClick={deleteAllNotificationsFunc}
                    >
                      Clear All
                    </button>
                  )}
                </div>
              </div>
              <div className="flex gap-5 text-sm mt-2 ml-2">
                <span
                  className={`cursor-pointer flex items-center pb-2 gap-1 ${activeTab === 'All' ? 'font-semibold  text-blue-600 border-b-2 border-blue-500' : ''}`}
                  onClick={() => setActiveTab('All')}
                >
                  All
                  {unreadCount > 0 && (
                    <span className="mx-1 text-black bg-blue-100 text-xs text-center flex justify-center items-center rounded-full w-5 h-5">
                      {unreadCount}
                    </span>
                  )}
                </span>
                <span
                  className={`cursor-pointer pb-2 ${activeTab === 'Unread' ? 'font-semibold  text-blue-600 border-b-2 border-blue-500' : ''}`}
                  onClick={() => setActiveTab('Unread')}
                >
                  Unread
                </span>
              </div>
            </section>
            {!atLeastOneMatch && (
              <p className="text-center text-gray-primary py-2 text-sm">
                No new notifications
              </p>
            )}
          </div>

          {filteredNotifications?.map((noti, ind) => {
            const isTrigger = noti?.type === 'trigger';
            const isOutsource = noti?.type === 'outsource';
            const isStop = noti?.type === 'stop';
            const isPause = noti?.type === 'pause';
            const isStart = noti?.type === 'start';
            const isScheduled = noti?.type === 'scheduled';

            const isDepartmentOrderNotification =
              noti?.type === 'departmentalOrder';
            const idAssignUserNotification =
              noti?.type === 'departmentalOrderAssignUser';
            const isNewKanbanTileColumn =
              noti?.type === 'shiftKanbanColumnNotify';
            const isChatNotification = noti?.type === 'chatnotification';
            const isCronJobNotification = noti?.type === 'cronJobNotification';

            return (
              <div key={ind}>
                {isDepartmentOrderNotification && noti?.forUser === user._id ? (
                  <div
                    key={noti._id}
                    className={`flex w-full border-b-2 cursor-pointer py-4 px-4 ${
                      !noti?.read?.includes(user?._id) ? 'bg-gray-100' : ''
                    } hover:bg-gray-50`}
                  >
                    <div className="w-1/4 relative">
                      {!noti?.read?.includes(user?._id) && (
                        <div className="absolute top-0 left-0 text-blue-500 w-2 h-2 rounded-full bg-blue-500"></div>
                      )}
                      <div className="bg-pink-200 w-8 h-8 rounded-full flex justify-center items-center">
                        <p className="text-pink-800">
                          {getInitials(noti?.senderName)}
                        </p>
                      </div>
                    </div>
                    <div className="w-full">
                      <div className="flex items-center">
                        <p
                          className={`w-full text-gray-700 text-[12px] ${
                            noti?.read?.includes(user?._id)
                              ? 'font-light'
                              : 'font-semibold'
                          }`}
                          onClick={() => {
                            markAsReadHandler(noti._id);
                          }}
                        >
                          {' '}
                          Reminder for {noti?.data?.taskId}
                        </p>
                        {/* <span
                          className="text-sm"
                          onClick={() => deleteNotificationFunc(noti._id)}
                        >
                          <TiDelete className="text-red-500" />
                        </span> */}
                      </div>
                      <p className="w-80 break-all  text-sm">
                        {noti?.data?.reminderRemarks}
                      </p>
                      <p className="text-[10px] mt-[2px] text-gray-700 flex justify-between">
                        <span>{getLocalDateTime(noti?.createdAt)}</span>
                      </p>
                    </div>
                  </div>
                ) : isChatNotification && noti?.forUser === user._id ? (
                  <div
                    key={noti._id}
                    className={`flex w-full border-b-2 cursor-pointer py-4 px-4 ${
                      !noti?.read?.includes(user?._id) ? 'bg-gray-100' : ''
                    } hover:bg-gray-50`}
                  >
                    <div className="w-1/4 relative">
                      {!noti?.read?.includes(user?._id) && (
                        <div className="absolute top-0 left-0 text-blue-500 w-2 h-2 rounded-full bg-blue-500"></div>
                      )}
                      <div className="bg-yellow-200 w-8 h-8 rounded-full flex justify-center items-center">
                        <p className="text-yellow-800">
                          {getInitials(noti?.senderName)}
                        </p>
                      </div>
                    </div>
                    <div className="w-full">
                      <div className="flex items-center">
                        <p
                          className={`w-full text-gray-700 text-[12px] ${
                            noti?.read?.includes(user?._id)
                              ? 'font-normal'
                              : 'font-semibold'
                          }`}
                          onClick={() => {
                            markAsReadHandler(noti._id);
                            setChatAppOpen(true);
                            setSenderId(noti?.data?.senderId);
                          }}
                        >
                          <span className="font-semibold">
                            {noti?.senderName + ' '}
                          </span>
                          send you a message
                        </p>
                        {/* <span
                          className="text-sm"
                          onClick={() => deleteNotificationFunc(noti._id)}
                        >
                          <TiDelete className="text-red-500" />
                        </span> */}
                      </div>
                      <p className="w-80 break-all text-sm">
                        {noti?.data?.reminderRemarks}
                      </p>
                      <p className="text-[10px] mt-[2px] text-gray-700 flex justify-between">
                        <span>{getLocalDateTime(noti?.createdAt)}</span>
                      </p>
                    </div>
                  </div>
                ) : idAssignUserNotification && noti?.forUser === user._id ? (
                  <div
                    key={noti._id}
                    className={`flex w-full border-b-2 cursor-pointer py-4 px-4 ${
                      !noti?.read?.includes(user?._id) ? 'bg-gray-100' : ''
                    } hover:bg-gray-50`}
                  >
                    <div className="w-1/4 relative">
                      {!noti?.read?.includes(user?._id) && (
                        <div className="absolute top-0 left-0 text-blue-500 w-2 h-2 rounded-full bg-blue-500"></div>
                      )}
                      <div className="bg-green-200 w-8 h-8 rounded-full flex justify-center items-center">
                        <p className="text-green-800">
                          {getInitials(noti?.senderName)}
                        </p>
                      </div>
                    </div>
                    <div className="w-full">
                      <div className="flex items-center">
                        <p
                          className={`w-full text-gray-700 text-[12px] ${
                            noti?.read?.includes(user?._id)
                              ? 'font-normal'
                              : 'font-semibold'
                          }`}
                          onClick={() => {
                            markAsReadHandler(noti._id);
                          }}
                        >
                          <span className="font-semibold">
                            {noti?.senderName + ' '}
                          </span>
                          assigned you a card with ID {noti?.data?.taskId}
                        </p>
                        {/* <span
                          className="text-sm"
                          onClick={() => deleteNotificationFunc(noti._id)}
                        >
                          <TiDelete className="text-red-500" />
                        </span> */}
                      </div>
                      <p className="w-80 break-all text-sm">
                        {noti?.data?.reminderRemarks}
                      </p>
                      <p className="text-[10px] mt-[2px] text-gray-700 flex justify-betwee">
                        <span>{getLocalDateTime(noti?.createdAt)}</span>
                      </p>
                    </div>
                  </div>
                ) : isStop || isPause || isStart || isOutsource || isTrigger ? (
                  <div
                    key={noti._id}
                    className={`flex w-full border-b-2  ${
                      noti?.read?.includes(user?._id) ? 'bg-slate-100' : ''
                    } hover:bg-slate-100`}
                    onClick={() => {
                      markAsReadHandler(noti._id);
                    }}
                  >
                    <div
                      className={`pl-4 flex justify-center items-center ${
                        noti?.read?.includes(user?._id) ? 'bg-slate-100' : ''
                      }`}
                    >
                      {isStop && (
                        <img
                          src={StoppedIcon}
                          alt="Stopped Icon"
                          className="w-16 h-16 object-contain relative"
                        />
                      )}
                      {isPause && (
                        <img
                          src={PausedIcon}
                          alt="Paused Icon"
                          className="w-16 h-16 object-contain relative"
                        />
                      )}
                      {isStart && (
                        <img
                          src={StartIcon}
                          alt="Started Icon"
                          className="w-16 h-16 object-contain relative"
                        />
                      )}
                    </div>
                    <div
                      className={`px-4 py-3 last:border-none w-full text-sm text-gray-700 ${
                        noti?.read?.includes(user?._id) ? 'bg-slate-100' : ''
                      }`}
                    >
                      {isPause && (
                        <p
                          className={`w-full ${
                            noti?.read?.includes(user?._id)
                              ? 'font-normal'
                              : 'font-bold'
                          }`}
                        >
                          {`${noti?.cuProject?.project?.id} Batch-${noti?.cuProject?.batchInfo?.batchNo} is Paused`}
                        </p>
                      )}

                      {isStop && (
                        <p
                          className={`w-full ${
                            noti?.read?.includes(user?._id)
                              ? 'font-normal'
                              : 'font-bold'
                          }`}
                        >
                          {`${noti?.cuProject?.project?.id} Batch-${noti?.cuProject?.batchInfo?.batchNo} is Stopped`}
                        </p>
                      )}

                      {isStart && (
                        <p
                          className={`w-full ${
                            noti?.read?.includes(user?._id)
                              ? 'font-normal'
                              : 'font-bold'
                          }`}
                        >
                          {`${noti?.cuProject?.project?.id} Batch-${noti?.cuProject?.batchInfo?.batchNo} is Started`}
                        </p>
                      )}

                      {isOutsource && (
                        <Link to="/outward">
                          <p
                            className={`w-full ${
                              noti?.read?.includes(user?._id)
                                ? 'font-normal'
                                : 'font-bold'
                            }`}
                          >
                            {`${noti?.cuProject?.project?.id} Batch-${noti?.cuProject?.batchInfo?.batchNo} is ready for Outsource`}
                          </p>
                        </Link>
                      )}

                      {!isStop && !isPause && !isOutsource && !isStart && (
                        <>
                          {isTrigger ? (
                            <p
                              className={`w-full ${
                                noti?.read?.includes(user?._id)
                                  ? 'font-normal'
                                  : 'font-bold'
                              }`}
                            >
                              {`${noti?.cuProject?.project?.id} Batch-${noti?.cuProject?.batchInfo?.batchNo} (${noti?.machine?.machineId}) has exceeded time limit of ${noti.pauseError?.timer} mins`}
                            </p>
                          ) : (
                            <p
                              className={`w-full ${
                                noti?.read?.includes(user?._id)
                                  ? 'font-normal'
                                  : 'font-bold'
                              }`}
                            >{`${noti?.kpi?.name} (${noti?.machine?.machineId} - ${
                              noti?.device?.deviceId
                            }) has ${
                              noti?.kpi?.condition === 'LT'
                                ? 'gone below'
                                : noti?.kpi?.condition === 'GT'
                                  ? 'gone above'
                                  : noti?.kpi?.condition === 'ET'
                                    ? 'reached'
                                    : ''
                            } ${noti?.kpi?.value}`}</p>
                          )}
                        </>
                      )}
                      <p className="text-[10px] flex justify-between">
                        <span>{getLocalDateTime(noti?.createdAt)}</span>
                      </p>
                    </div>
                  </div>
                ) : isNewKanbanTileColumn ? (
                  <div
                    key={noti._id}
                    className={`flex w-full border-b-2 cursor-pointer py-4 px-4  ${
                      Object.keys(user?.collapsedColumns)?.includes(
                        noti?.data?.page
                      )
                        ? 'font-semibold'
                        : 'hidden'
                    } ${noti?.archive?.includes(user?._id) ? 'hidden' : ''} hover:bg-gray-50`}
                  >
                    <div className="w-1/4 relative">
                      {!noti?.read?.includes(user?._id) && (
                        <div className="absolute top-0 left-0 text-blue-500 w-2 h-2 rounded-full bg-blue-500"></div>
                      )}
                      <div className="bg-purple-200 w-8 h-8 rounded-full flex justify-center items-center">
                        <p className="text-purple-800">
                          {getInitials(noti?.senderName)}
                        </p>
                      </div>
                    </div>
                    <div className="w-full flex">
                      <div className="flex flex-col justify-between w-3/4">
                        <p
                          className={`w-full mr-4 mb-2 leading-none ${
                            Object.keys(user?.collapsedColumns)?.includes(
                              noti?.data?.page
                            )
                              ? 'font-semibold'
                              : 'hidden'
                          } ${
                            noti?.read?.includes(user?._id)
                              ? '!font-normal'
                              : ''
                          }`}
                          onClick={() => {
                            markAsReadHandler(noti._id);
                            navigate(
                              `/primary/kanban?index=${noti.data.index}`
                            );
                          }}
                        >
                          Card with ID {noti?.data?.order?.taskId} for{' '}
                          {noti?.data?.page} assigned to you
                        </p>
                        <span className="text-xs mt-[2px] font-semibold">
                          {getLocalDateTime(noti?.createdAt)}
                        </span>
                      </div>
                      <p className="text-[11px] flex flex-col justify-between text-right">
                        <span
                          className="text-xs font-normal !text-blue-500 hover:underline"
                          onClick={() => archiveNotificationFunc(noti._id)}
                        >
                          {/* ❌ */}
                          Clear
                        </span>
                        <span className="text-xs font-normal !text-blue-500 hover:underline">
                          Mark as Read
                        </span>
                      </p>
                    </div>
                  </div>
                ) : isCronJobNotification ? (
                  <div
                    key={noti._id}
                    className={`flex w-full border-b-2 cursor-pointer py-4 px-4 ${
                      Object.keys(user?.collapsedColumns)?.includes(
                        noti?.data?.page
                      )
                        ? 'font-bold'
                        : 'hidden'
                    } hover:bg-slate-100`}
                  >
                    <div className="w-1/4 relative">
                      {!noti?.read?.includes(user?._id) && (
                        <div className="absolute top-0 left-0 text-blue-500 w-2 h-2 rounded-full bg-blue-500"></div>
                      )}
                      <div className="bg-orange-200 w-8 h-8 rounded-full flex justify-center items-center">
                        <p className="text-orange-800">
                          {getInitials(noti?.senderName)}
                        </p>
                      </div>
                    </div>
                    <div className="w-full flex">
                      <div className="flex flex-col justify-between w-3/4">
                        <p
                          className={`w-full mr-4 mb-2 leading-none ${
                            Object.keys(user?.collapsedColumns)?.includes(
                              noti?.data?.page
                            )
                              ? 'font-semibold'
                              : 'hidden'
                          } ${
                            noti?.read?.includes(user?._id)
                              ? '!font-normal'
                              : ''
                          }`}
                          onClick={() => {
                            markAsReadHandler(noti._id);
                            navigate(
                              `/primary/kanban?index=${noti.data.index}`
                            );
                          }}
                        >
                          Card with ID {noti?.data?.order?.taskId} for{' '}
                          {noti?.data?.page} assigned to you
                        </p>
                        <span className="text-xs mt-[2px] font-semibold">
                          {getLocalDateTime(noti?.createdAt)}
                        </span>
                      </div>
                      <p className="text-[11px] flex flex-col justify-between text-right">
                        <span
                          className="text-xs font-normal !text-blue-500 hover:underline"
                          onClick={() => archiveNotificationFunc(noti._id)}
                        >
                          {/* ❌ */}
                          Clear
                        </span>
                        <span className="text-xs font-normal !text-blue-500 hover:underline">
                          Mark as Read
                        </span>
                      </p>
                    </div>
                  </div>
                ) : isScheduled ? (
                  <>
                    <p
                      className={`w-full ml-5 text-sm ${
                        noti?.read?.includes(user?._id)
                          ? 'font-normal'
                          : 'font-bold'
                      }`}
                    >
                      {`${noti?.data?.message}`}
                    </p>
                    <hr />
                  </>
                ) : null}
              </div>
            );
          })}
        </Popover.Panel>
      </Transition>
    </Popover>
  );
};

export default Notifications;
