import Input from '../global/components/Input';

const TrComponent = ({ setlabel, i, label }) => {
  return (
    <>
      <div className="flex flex-col  mt-2">
        <label className="mb-1 font-semibold text-[#667085]">Label Name </label>
        <Input
          // value={label[i] || ''}
          onChange={(e) => {
            let temp = label;
            temp[i] = e.target.value;
            setlabel(temp);
          }}
          placeholder="Label"
        />
      </div>
    </>
  );
};
export default TrComponent;
