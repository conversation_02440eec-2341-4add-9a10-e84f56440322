import { Fragment, useEffect, useState } from 'react';
import {
  CircularProgressbarWithChildren,
  buildStyles,
} from 'react-circular-progressbar';
import { ReactComponent as Calendar } from '../../../assets/svgs/calendar.svg';
import { ReactComponent as CircleArrowLeft } from '../../../assets/svgs/circle_arrow_left.svg';
import { ReactComponent as CircleArrowRight } from '../../../assets/svgs/circle_arrow_right.svg';
import { ReactComponent as Clock } from '../../../assets/svgs/clock.svg';
import {
  calculateValueForViews,
  getLocalDateTime,
} from '../../../helperFunction';
import { useLazyGetAllCuProjectsQuery } from '../../../slices/CuProjectAPiSlice';

const LiveBatch = ({ projects, values }) => {
  const [currentProject, setCurrentProject] = useState(0);

  const [getAllCuProjects, { data: cuData = {} }] =
    useLazyGetAllCuProjectsQuery();
  const { cuProjects = [] } = cuData;

  useEffect(() => {
    getAllCuProjects({ query: 'active' }, false);
  }, [getAllCuProjects]);

  return (
    <div className="w-full mb-3">
      <p className="font-bold text-[1.25rem] mb-3">Your Live Batch</p>
      <div className="w-full aspect-[325/146] rounded-new bg-white flex justify-around items-center px-2">
        {cuProjects?.map((cuProject, cIdx) => {
          if (cIdx !== currentProject) return null;

          const machineList = cuProject?.machineAndOperator?.map(
            (mao) => mao?.machine?.machineId
          );

          const project = projects?.find(
            (project) => project?.projectId?._id === cuProject?.mqtt
          );

          const batchParam = project?.processGoalView?.parameters?.find(
            (param) => param.name === 'Batch Size'
          );

          let calculatedValue = 0;

          if (batchParam?.formula && values && machineList) {
            calculatedValue = calculateValueForViews(
              batchParam?.formula,
              values,
              machineList
            );
          }

          let batchInfo = cuProject?.project?.goalsTable
            ?.find((gt) => gt?.flowId === cuProject?.flowId)
            ?.tableData?.find(
              (item) => item?.batchNo === cuProject?.batchInfo?.batchNo
            );

          if (cuProject?.isMultiProcess) {
            batchInfo = cuProject?.project?.goalsTable
              ?.find((gt) => gt?.flowId === cuProject?.flowId)
              ?.tableData?.find(
                (item) => item?.batchNo === cuProject?.batchInfo?.batchNo
              )
              ?.subProcessData?.find(
                (i, idx) => idx === cuProject.subProcessIndex
              );
          }

          const percentage =
            Math.round((calculatedValue / batchInfo?.['Batch Size']) * 100) ||
            0;

          const localDateTime = getLocalDateTime(
            new Date(cuProject?.startTime)
          )?.split(', ');

          return (
            <Fragment key={cuProject._id}>
              <section className="flex flex-col justify-center w-[45%]">
                <p className="text-[1rem] font-bold">
                  {`Batch ${cuProject?.batchInfo?.batchNo}`}
                  {cuProject?.isMultiProcess ? (
                    <span className="text-[0.8rem]">{` (${batchInfo?.process})`}</span>
                  ) : (
                    ''
                  )}
                </p>
                <p className="text-[0.815rem] font-medium text-black/40">
                  {cuProject?.project?.id}
                </p>
                <p className="text-[0.815rem] font-medium flex items-center">
                  <Calendar className="mr-1" />
                  {localDateTime?.[0]?.replaceAll('/', '-')}
                </p>
                <p className="text-[0.815rem] font-medium flex items-center">
                  <Clock className="mr-1" />
                  {`${localDateTime?.[1]?.slice(
                    0,
                    5
                  )} ${localDateTime?.[1]?.slice(9, 11)}`}
                </p>
              </section>
              <section className="h-[58%] aspect-square">
                <CircularProgressbarWithChildren
                  value={percentage}
                  counterClockwise
                  styles={buildStyles({
                    rotation: 0.25,
                    strokeLinecap: 'butt',
                    pathTransitionDuration: 0.5,
                    pathColor: `#005EEC`,
                    textColor: '#005EEC',
                    // 	trailColor: '#d6d6d6',
                    // 	backgroundColor: '#3e98c7',
                  })}
                >
                  <p className="text-[1.13rem] font-bold text-blue-primary">
                    {percentage > 100 ? 100 : percentage}%
                  </p>
                </CircularProgressbarWithChildren>
              </section>
            </Fragment>
          );
        })}
        <section className="flex h-full items-end pb-2">
          <section className="flex gap-x-1 ">
            <CircleArrowLeft
              className="cursor-pointer"
              onClick={() => {
                setCurrentProject((prev) => {
                  if (prev < 1) {
                    return cuProjects?.length - 1;
                  }
                  return prev - 1;
                });
              }}
            />
            <CircleArrowRight
              className="cursor-pointer"
              onClick={() => {
                setCurrentProject((prev) => {
                  if (prev > cuProjects?.length - 2) {
                    return 0;
                  }
                  return prev + 1;
                });
              }}
            />
          </section>
        </section>
      </div>
    </div>
  );
};
export default LiveBatch;
