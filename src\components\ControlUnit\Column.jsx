import { Modal as Andt<PERSON><PERSON><PERSON>, But<PERSON> } from 'antd';
import { useContext, useEffect, useState } from 'react';
import Marquee from 'react-fast-marquee';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import download from '../../assets/images/download.png';
import pdf from '../../assets/images/pdf.png';
import Table from '../../components/global/components/Table';
import {
  getPartVariantName,
  getProductVariantName,
} from '../../helperFunction';
import Modal from '../../pages/Modal';
import { apiSlice } from '../../slices/apiSlice';
import { useLazyGetBomByIdQuery } from '../../slices/assemblyBomApiSlice';
import { useUpdateCiForForceStopMutation } from '../../slices/createInputApiSlice';
import { useLazyGetMediaByIdQuery } from '../../slices/mediaSlice';
import { useGetAllPartsForOptionsQuery } from '../../slices/partApiSlice';
import { useGetAllProductsForOptionsQuery } from '../../slices/productApiSlice';
import { Store } from '../../store/Store';
import { customConfirm } from '../../utils/customConfirm';
import EllipsisText from '../global/components/EllipsesText';
import PdfViewer from '../global/components/PdfViewer';
import Tooltip from '../global/components/ToolTip';
import RenderAdditionalField from '../Templates/RenderAdditionalField';
import ItemModalV3 from '../v3/WorkOrder/ItemModal';
import JobStockInModal from './JobStockInModal';
import StatusBar from './StatusBar';

function Column({
  columnType = false,
  title,
  jobItem = '',
  children,
  workorder,
  mediaIds = null,
  selectedData,
  index,
  itemId,
  item,
  setShowOutSrc,
  ci,
  setSelectedData,
  selectedWo = {},
  setIsOpenSidebar,
  setSideBarData,
  getWO,
}) {
  const {
    defaults: { defaultParam },
  } = useContext(Store);
  const dispatch = useDispatch();
  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [getBomById] = useLazyGetBomByIdQuery();
  const [bomToShow, setBomToShow] = useState({
    bom: {},
    open: false,
  });
  const [rmToShow, setRMToShow] = useState({
    rawMaterials: [],
    assets: [],
    orderQuantity: 0,
    open: false,
  });

  const { data: allParts = [] } = useGetAllPartsForOptionsQuery();
  const { data: allProducts = [] } = useGetAllProductsForOptionsQuery();
  const [updateCiForForceStop, { isLoading: isLoadingStop }] = //eslint-disable-line
    useUpdateCiForForceStopMutation();

  let stopMethod = defaultParam?.projectDefaults?.defaultJobStopType || 'none';

  const closeBomModal = () => {
    setBomToShow({
      bom: {},
      open: false,
    });
  };

  const closeRMModal = () => {
    setRMToShow({
      rawMaterials: [],
      open: false,
    });
  };

  useEffect(() => {
    if (ci) {
      let notStartedBatches = [];
      let itemTobeStockedOutPre = [];
      let activeBatchesInfo = [];
      let isNotStartedArr = [];
      let noOfBatches = ci?.goalsTable?.[0]?.tableData?.length;
      let batchesWithProcessInfo = Array.from(
        { length: noOfBatches },
        (_, i) => i + 1
      )?.map((i) => {
        return {
          batchNo: i,
          process: ci?.goalsTable?.map((j) => {
            return {
              flowId: j?.flowId,
              mqttId: j?.mqtt?._id,
              batchInfo: j?.tableData?.find((k) => k?.batchNo === i),
              status:
                j?.tableData?.find((k) => k?.batchNo === i)?.status || 'NA',
              correspondingCuProject:
                ci?.cuProjects?.find(
                  (cuProject) =>
                    cuProject?.batchInfo?.batchNo === i &&
                    cuProject?.flowId === j?.flowId
                ) || 'NA',
            };
          }),
        };
      });

      batchesWithProcessInfo?.forEach((batch) => {
        let completedProcesses = batch?.process?.filter(
          (process) => process?.status === 'complete'
        );

        let act = batch?.process?.filter((pro) => pro?.status === 'start');

        let isNotStr = batch?.process?.filter(
          (process) => process?.status === 'NA'
        );

        if (isNotStr?.length === batch?.process?.length) {
          isNotStartedArr.push(batch?.batchNo);
        }

        // to prevent force stop when any batch is active if lenght >0
        if (act?.length > 0) activeBatchesInfo.push(batch);

        if (batch?.process?.length !== completedProcesses?.length) {
          let processesArray = batch?.process;

          let lastCompletedProcess = processesArray
            ?.slice()
            .reverse()
            .find((process) => process?.status === 'complete');

          if (lastCompletedProcess) {
            itemTobeStockedOutPre.push(lastCompletedProcess);
          } else {
            notStartedBatches.push(batch);
          }
        }
      });

      if (isNotStartedArr?.length === batchesWithProcessInfo?.length) {
        setIsNotStarted(true);
      }
      setActiveBatches(() => activeBatchesInfo);
      setItemTobeStockedOut(() => itemTobeStockedOutPre);
    }
  }, [ci, selectedData]);

  const [isOpenModal, setIsOpenModal] = useState(false);
  const [mediaData, setMediaData] = useState(null);
  const [activeIdx, setActiveIdx] = useState(null);
  const [showFullScreenModal, setShowFullScreenModal] = useState(false);
  const [media, setMedia] = useState(null);
  const [itemData, setItemData] = useState(null);
  const [showItem, setShowItem] = useState(false);
  const [showForceStop, setShowForceStop] = useState(false);
  const [itemTobeStockedOut, setItemTobeStockedOut] = useState([]);
  const [activeBatches, setActiveBatches] = useState([]);
  const [isNotStarted, setIsNotStarted] = useState(false);

  const reset = () => {
    setShowForceStop(false);
    setIsNotStarted(false);
    setItemTobeStockedOut([]);
    setActiveBatches([]);
    getWO();
  };

  useEffect(() => {
    if (mediaIds && activeIdx === index) {
      Promise.all(
        mediaIds?.map(async (itm) => {
          const temp = await getMediaById({ id: itm }).unwrap();
          return temp.media;
        })
      ).then((res) => setMediaData(res));
    }
  }, [selectedData, mediaIds, activeIdx]); //eslint-disable-line

  const updateCiForForceStopFxn = async () => {
    let id = ci?._id;
    const res = await updateCiForForceStop({
      id: id,
      data: {
        isForceStopped: true,
      },
    });
    if (res) {
      reset();
      toast.success('Job is force stopped!');
      dispatch(apiSlice.util.invalidateTags(['ForceStop']));
    }
  };

  return (
    <>
      {showForceStop && (
        <JobStockInModal
          setShowForceStop={setShowForceStop}
          productFromCu={item}
          method="forceStop"
          itemStockOutData={itemTobeStockedOut}
          reset={reset}
          updateCiForForceStop={updateCiForForceStopFxn}
        />
      )}
      {showFullScreenModal && (
        <div
          className="fixed top-0 left-0 flex justify-between items-center w-screen h-screen bg-black/10 z-[999999]"
          onClick={() => {
            if (media?.type !== 'application/pdf')
              setShowFullScreenModal(false);
          }}
        >
          <>
            {media?.type === 'application/pdf' ? (
              <PdfViewer
                file={media?.data}
                name={media?.name}
                closeClick={(e) => {
                  e.preventDefault();
                  setShowFullScreenModal(false);
                }}
              />
            ) : (
              <div className="flex items-center justify-center w-full h-[70%]">
                <img
                  className="h-[100%] aspect-video object-contain"
                  src={media?.data}
                  alt="image"
                />
              </div>
            )}
          </>
        </div>
      )}
      {isOpenModal && (
        <Modal
          handleClose={() => {
            setIsOpenModal(false);
            setActiveIdx(null);
            setMediaData(null);
          }}
          className="w-[60%] h-[80%]"
        >
          <div className="w-full h-full mt-10 ml-2">
            <div className="flex gap-4 flex-wrap overflow-scroll">
              {mediaData?.map((item, uIdx) => (
                <section
                  key={uIdx}
                  className="p-2 border rounded-md w-[170px] flex flex-col justify-between"
                >
                  <section
                    className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer "
                    onClick={() => {
                      setMedia(item);
                      setShowFullScreenModal(true);
                    }}
                  >
                    <img
                      className="w-[150px] aspect-video object-contain"
                      src={item?.type === 'application/pdf' ? pdf : item?.data}
                      alt=""
                    />
                  </section>

                  <section className="flex justify-between items-center text-sm mt-2">
                    <Marquee className="w-">{item.name}</Marquee>
                    {item?.type !== 'application/pdf' && (
                      <a
                        href={item?.data}
                        download={item?.name}
                        className=" bg-red-500/60 ml-2"
                      >
                        <img
                          src={download}
                          alt="Download Icon"
                          className="w-5 h-5 object-contain relative"
                        />
                      </a>
                    )}
                  </section>
                </section>
              ))}
            </div>
          </div>
        </Modal>
      )}
      {showItem && (
        <AndtModal
          title="Material Details"
          onCancel={() => {
            setShowItem(false);
            setItemData(null);
          }}
          isSubmitRequired={false}
          canSubmit={false}
          centered
          open={showItem}
          footer={null}
          width="60%"
          styles={{
            body: {
              height: '80%',
              overflowY: 'auto',
            },
          }}
        >
          <section>
            {selectedWo?.type === 'Assembly' && (
              <p className="flex items-center justify-between font-semibold">
                Item Name :{' '}
                <span className="font-normal">{itemData?.name}</span>{' '}
              </p>
            )}
            <p className="flex items-center justify-between font-semibold">
              Item :{' '}
              <span className="font-normal">
                {itemData?.category?.toUpperCase()}
              </span>{' '}
            </p>
            <p className="flex items-center justify-between font-semibold">
              Part/Product Name :{' '}
              <EllipsisText
                text={
                  itemData?.part?.name ||
                  itemData?.product?.name ||
                  item?.itemId?.name ||
                  itemData?.manualEntry ||
                  (itemData?.partVariant
                    ? getPartVariantName(itemData?.partVariant)
                    : getProductVariantName(itemData?.productVariant))
                }
                className="font-normal"
              />
            </p>

            <p className="flex items-center justify-between font-semibold">
              Item Unit : <span className="font-normal">{itemData?.units}</span>{' '}
            </p>
          </section>
          <section>
            {selectedWo?.additionalFields?.templateData?.length > 0 && (
              <RenderAdditionalField
                additionalFields={selectedWo?.additionalFields}
              />
            )}
          </section>
          {selectedWo?.items === undefined ? (
            <section className=" mt-3">
              {itemData?.category === 'inhouse' ? (
                <>
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>Name</Table.Th>
                        <Table.Th>Type</Table.Th>
                        <Table.Th>Units</Table.Th>
                        <Table.Th></Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {itemData?.rawMaterials?.map((data, dIdx) => (
                        <Table.Row key={dIdx}>
                          <Table.Td>{data?.item?.name}</Table.Td>
                          <Table.Td>{(data?.partType).toUpperCase()}</Table.Td>
                          <Table.Td>{data?.units}</Table.Td>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </>
              ) : (
                <>
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>Name</Table.Th>
                        <Table.Th>Type</Table.Th>
                        <Table.Th>Units</Table.Th>
                        <Table.Th></Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {itemData?.children?.map((data, dIdx) => (
                        <Table.Row key={dIdx}>
                          <Table.Td>{data?.name}</Table.Td>
                          <Table.Td>{(data?.category).toUpperCase()}</Table.Td>
                          <Table.Td>{data?.units}</Table.Td>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </>
              )}
            </section>
          ) : (
            <section className="flex items-center justify-between font-semibold">
              <ItemModalV3
                items={bomToShow?.bom}
                showModal={bomToShow?.open}
                orderQuantity={bomToShow?.orderQuantity}
                closeModal={closeBomModal}
                allParts={allParts}
                allProducts={allProducts}
                type="bom"
              />
              <ItemModalV3
                items={rmToShow?.rawMaterials}
                assets={rmToShow?.assets}
                showModal={rmToShow?.open}
                orderQuantity={rmToShow?.orderQuantity}
                closeModal={closeRMModal}
                allParts={allParts}
                allProducts={allProducts}
                type="rm"
              />
              <p>BOM/RM Details</p>
              <Button
                type="primary"
                onClick={() => {
                  if (itemData?.bom?.item?.length > 0) {
                    setBomToShow({
                      bom: itemData?.bom,
                      orderQuantity:
                        itemData?.units * selectedWo?.orderQuantity,
                      open: true,
                    });
                  }
                  if (itemData?.rawMaterials?.length > 0) {
                    setRMToShow({
                      rawMaterials: itemData?.rawMaterials || [],
                      assets: itemData?.assetData || [],
                      orderQuantity: itemData?.units,
                      open: true,
                    });
                  }
                }}
              >
                Show
              </Button>
            </section>
          )}
        </AndtModal>
      )}
      <div
        className={`flex flex-col  !w-full   overflow-x-auto gap-y-3 md:px-[14px]  md:gap-y-5 md:w-full rounded-md border-[1px] border-slate-200 border-solid bg-white ${columnType ? 'h-[82vh]' : 'h-[75vh]'}`}
      >
        <div className={`bg-white sticky w-full justify-between`}>
          <div className="w-full flex justify-between pt-4 pl-2 border-b-[1px] border-slate-300 pb-2">
            <h3 className="text-[18px] leading-none font-medium flex items-center gap-3 w-full  ">
              {title?.length > 15 ? (
                <Tooltip
                  text={title}
                  maxWidth={'!max-w-[500px]'}
                  minWidth={'!min-w-[250px]'}
                >
                  {title?.slice(0, 15) + '...'}
                </Tooltip>
              ) : (
                title || '-'
              )}
            </h3>
            {workorder && (
              <span className="flex justify-center items-center px-[8px] text-[12px] bg-blue-500 text-white rounded-[8px]">
                {workorder}
              </span>
            )}
            {!workorder && (
              <button
                //TODO Later
                disabled={ci?.isForceStopped}
                title="Force Stop will stop the current job and will not allow to restart."
                onMouseOver={(e) => {
                  e.currentTarget.setAttribute(
                    'data-tooltip',
                    'Force Stop will stop the current job and will not allow to restart.'
                  );
                }}
                onMouseOut={(e) => {
                  e.currentTarget.removeAttribute('data-tooltip');
                }}
                onClick={async () => {
                  if (ci?.isForceStopped) {
                    toast.error('Job is already force stopped');
                    // reset();
                    return;
                  }
                  if (isNotStarted) {
                    toast.error('Job is not started yet');
                    // reset();
                    return;
                  }

                  if (activeBatches.length > 0) {
                    toast?.error(
                      'Please stop all active ongoing job to force stop'
                    );
                    // reset();
                    return;
                  }
                  const confirm = await customConfirm(
                    'Are you sure you want to force stop this job?',
                    'stop'
                  );
                  if (!confirm) return;
                  if (stopMethod === 'manual') {
                    if (activeBatches.length > 0) {
                      toast?.error(
                        'Please stop all active ongoing job to force stop'
                      );
                      // reset();
                      return;
                    }

                    if (itemTobeStockedOut?.length === 0) {
                      updateCiForForceStopFxn();
                    } else if (itemTobeStockedOut?.length > 0) {
                      setShowForceStop(true);
                      // const res = await updateCiForForceStop({
                      //   id: ci?._id,
                      //   data: {
                      //     isForceStopped: true,
                      //   },
                      // });
                      // if (res) {
                      //   dispatch(apiSlice.util.invalidateTags(['ForceStop']));
                      //   setShowForceStop(true);
                      // }
                    }
                  } else if (stopMethod === 'none') {
                    updateCiForForceStopFxn();
                    // const res = await updateCiForForceStop({
                    //   id: ci?._id,
                    //   data: {
                    //     isForceStopped: true,
                    //   },
                    // });
                    // if (res) {
                    //   dispatch(apiSlice.util.invalidateTags(['ForceStop']));
                    //   reset();
                    //   toast.success('Job is force stopped!');
                    // }
                  } else {
                    toast.error('Method under Development!');
                  }
                }}
                type="button"
                className="text-[12px] px-[5px] border-[2px] border-[#ef3737] py-[2px] rounded-[8px] text-nowrap text-[#ee6666] "
              >
                Force Stop
              </button>
            )}
          </div>
          {!workorder && (
            <h6 className="text-black my-2">
              {/* {jobItem} */}
              {jobItem?.length > 30 ? (
                <Tooltip
                  text={jobItem}
                  maxWidth={'!max-w-[500px]'}
                  minWidth={'!min-w-[250px]'}
                >
                  {jobItem?.slice(0, 30) + '...'}
                </Tooltip>
              ) : (
                jobItem || '-'
              )}
            </h6>
          )}

          {!workorder && <StatusBar id={ci?._id} type={'job'} />}

          {!workorder && (
            <div className="flex gap-2 mt-2">
              <button
                type="button"
                onClick={() => {
                  if (selectedWo?.type !== 'Assembly') {
                    let item = {};
                    if (selectedWo?.inhouse) {
                      item = selectedWo?.inhouse?.find(
                        (item) =>
                          item?._id === itemId ||
                          item?.manualEntry === ci?.assemblyManualEntry
                      );
                    } else {
                      item = selectedWo?.items?.find(
                        (item) =>
                          item?._id === itemId ||
                          item?.manualEntry === ci?.assemblyManualEntry
                      );
                    }
                    setShowItem(true);
                    setItemData(item);
                  } else {
                    if (itemId) {
                      getBomById({ id: itemId }).then((res) => {
                        setShowItem(true);
                        setItemData(res.data);
                      });
                    }
                  }
                }}
                className="text-[12px]  py-[3px] rounded-[8px] text-[#549feffc]"
              >
                Info
              </button>
              |
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedData((prev) => ({
                    ...prev,
                    model: ci,
                    // flow: flow,
                    productionFlow: ci?.productionFlow,
                  }));

                  setShowOutSrc(true);
                }}
                disabled={ci?.isForceStopped}
                className="text-[12px]  py-[3px] rounded-[8px] text-[#549feffc] "
              >
                Outsource
              </button>
              |
              <button
                type="button"
                className="text-[12px]  py-[3px] rounded-[8px] text-[#549feffc] "
                onClick={() => {
                  setIsOpenSidebar(true);
                  setSideBarData(ci);
                }}
              >
                Qc Data
              </button>
            </div>
          )}
        </div>

        <div
          className={`flex ${columnType ? 'flex-col' : 'flex-row'} gap-x-2 md:flex-col md:gap-y-4 h-[72vh] relative overflow-y-auto no-scrollbar`}
        >
          {children}
        </div>
      </div>
    </>
  );
}

export default Column;
