import { FileExcelOutlined, SearchOutlined } from '@ant-design/icons';
import { Alert, Button, Input, Table, Tooltip } from 'antd';
import { useState } from 'react';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import useDebounceValue from '../../hooks/useDebounceValue';
import { useGetQuotationStatusesQuery } from '../../slices/quotationApiSlice';
import { useGetQuotationsStatusTimelineDataQuery } from '../../slices/salesDashboardApiSlice';
import Pagination from '../global/components/Pagination';
import RightSidebar from '../global/components/RightSidebar';
import QuotationGlobalSideBar from '../SalesOrderManagement/Quotation/QuotationGlobalSideBar';

const defaultStatuses = ['pending', 'approved', 'rejected', 'completed'].map(
  (s) => s.toLowerCase()
);
const colors = {
  pending: 'bg-yellow-200 text-yellow-600',
  rejected: 'bg-red-200 text-red-600',
  approved: 'bg-[#DCF0DD] text-[#0F6A2E]',
};
const getStatusColor = (status) => {
  const normalizedStatus = status?.toLowerCase();
  if (colors[normalizedStatus]) return colors[normalizedStatus];
  return 'bg-fuchsia-200 text-fuchsia-700';
};

const StatusTimelineMetrics = ({ dateRange, selectedCustomer }) => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [openSideBar, setOpenSideBar] = useState(false);
  const [selectedQuotation, setSelectedQuotation] = useState(null);

  const debouncedSearchTerm = useDebounceValue(searchTerm, 500);

  const {
    data: statuses,
    isLoading: statusesLoading,
    error: statusesError,
  } = useGetQuotationStatusesQuery();

  const {
    data: timelineData,
    isLoading: timelineLoading,
    error: timelineError,
    refetch: refetchTimeline,
  } = useGetQuotationsStatusTimelineDataQuery({
    dateRange,
    customerId: selectedCustomer,
    searchTerm: debouncedSearchTerm,
    limit,
    page,
  });

  const availableStatuses = statuses?.status
    ? [...statuses.status, ...defaultStatuses].map((s) => s.toLowerCase())
    : [...defaultStatuses];

  const quotationsData = timelineData?.data || [];
  const totalPages = timelineData?.totalPages || 0;
  const totalResults = timelineData?.totalResults || 0;

  const exportToCSV = () => {
    try {
      if (!quotationsData || quotationsData.length === 0) {
        toast.warning('No data available to export');
        return;
      }

      const exportData = quotationsData.map((item, index) => {
        const row = {
          'SR NO': index + 1,
          'Quote ID': item.quoteId || '-',
          Company: item.companyName || '-',
          'Current Status': item.currentStatus || '-',
        };

        availableStatuses.forEach((status) => {
          row[status] = item.statusTimeline?.[status] || '-';
        });

        return row;
      });

      const worksheet = XLSX.utils.json_to_sheet(exportData);

      const colWidths = [];
      exportData.forEach((row) => {
        Object.keys(row).forEach((key, i) => {
          const cellLength = String(row[key]).length;
          colWidths[i] = Math.max(colWidths[i] || 0, cellLength, key.length);
        });
      });

      worksheet['!cols'] = colWidths.map((width) => ({
        width: width + 2,
      }));

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Status Timeline');

      const currentDate = new Date().toISOString().split('T')[0];
      const filename = `quotation_status_timeline_${currentDate}.xlsx`;

      XLSX.writeFile(workbook, filename);
    } catch (error) {
      toast.error('Failed to export data');
    }
  };
  const buildColumns = () => {
    const columns = [
      {
        title: 'Quote ID',
        dataIndex: 'quoteId',
        key: 'quoteId',
        width: 120,
        fixed: 'left',
        render: (text, record) => (
          <span
            className="cursor-pointer text-blue-400 font-semibold"
            onClick={() => {
              setSelectedQuotation(record._id);
              setOpenSideBar(true);
            }}
          >
            {text}
          </span>
        ),
      },
      {
        title: 'Company',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 120,
        fixed: 'left',
        render: (text) => (
          <span style={{ color: '#1890ff', fontWeight: 500 }}>{text}</span>
        ),
      },
      {
        title: 'Status',
        dataIndex: 'currentStatus',
        key: 'currentStatus',
        width: 120,
        fixed: 'left',
        render: (status) => (
          <span
            className={`${getStatusColor(status)} px-3 py-1 rounded-full font-medium whitespace-nowrap shadow-sm text-xs uppercase tracking-wide text-center`}
          >
            {status}
          </span>
        ),
      },
    ];

    availableStatuses?.forEach((status) => {
      columns.push({
        title: status.charAt(0).toUpperCase() + status.slice(1),
        dataIndex: ['statusTimeline', status],
        key: status,
        width: 130,
        render: (_, record) => {
          const statusDate = record.statusTimeline?.[status.toLowerCase()];
          if (!statusDate) {
            return <span style={{ color: '#d9d9d9' }}>-</span>;
          }
          return (
            <span
              style={{
                color: '#1890ff',
                fontSize: '13px',
              }}
            >
              {statusDate}
            </span>
          );
        },
      });
    });

    return columns;
  };

  const isLoading = statusesLoading || timelineLoading;
  const hasError = statusesError || timelineError;

  if (hasError) {
    return (
      <Alert
        message="Error Loading Formats"
        description="Failed to load quotation formats. Please try again."
        type="error"
        showIcon
        action={
          <Button
            onClick={() => refetchTimeline()}
            type="text"
            className="text-red-600 hover:text-red-800"
          >
            Retry
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="w-full sm:w-auto">
          <Input
            placeholder="Search by Quote ID or Company..."
            prefix={<SearchOutlined />}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full sm:w-80"
            allowClear
          />
        </div>
        <div className="w-full sm:w-auto">
          <Tooltip title="Export status timeline data to Excel">
            <Button
              type="primary"
              icon={<FileExcelOutlined />}
              onClick={exportToCSV}
              className="bg-green-600 hover:bg-green-700"
              disabled={!quotationsData || quotationsData.length === 0}
            />
          </Tooltip>
        </div>
      </div>

      {/* Table */}
      <Table
        columns={buildColumns()}
        dataSource={quotationsData}
        pagination={false}
        scroll={{ x: 'max-content' }}
        size="middle"
        bordered
        loading={isLoading}
        rowKey={(record) => record._id || record.key}
      />

      {/* Pagination */}
      <Pagination
        limit={limit}
        page={page}
        totalPages={totalPages}
        totalResults={totalResults}
        setPage={setPage}
        setLimit={setLimit}
        className="w-full"
      />
      <RightSidebar
        openSideBar={openSideBar}
        setOpenSideBar={setOpenSideBar}
        title="Quotation Details"
      >
        <QuotationGlobalSideBar id={selectedQuotation} fromKanban={false} />
      </RightSidebar>
    </div>
  );
};

export default StatusTimelineMetrics;
