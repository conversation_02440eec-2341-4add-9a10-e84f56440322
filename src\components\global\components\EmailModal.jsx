import { PlusCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useRef, useState } from 'react';
import { MdOutlineAttachFile } from 'react-icons/md';
import BreadCrumbs from './BreadCrumbs';
import Button from './Button';
const EmailFormWrapper = ({
  children,
  isForm = true,
  step,
  pages,
  current,
  onSubmit,
  nextClickHandler,
}) => {
  if (isForm) {
    return (
      <form
        onSubmit={(e) => {
          e.preventDefault();
          if (step === pages?.length - 1 || pages?.length < 2) {
            onSubmit(e, current);
          } else {
            nextClickHandler();
          }
        }}
        className="w-full h-[95%]"
      >
        {children}
      </form>
    );
  } else {
    return <div className="w-full h-full">{children}</div>;
  }
};

const EmailModal = ({
  children,
  accept,
  multiple,
  title,
  svg,
  pages = [],
  onNextClick,
  onSubmit,
  onCloseModal,
  onAdd,
  canSubmit = true,
  isSubmitRequired = true,
  btnIsLoading = false,
  canAddValue = false,
  onChange,
  formData = [],
  setFormData,
  isChangeInAssembly = false,
  setValueCount,
  modalWidth = '50%',
}) => {
  const [step, setStep] = useState(0);
  const current = { step, page: pages?.[step], setStep };
  const inputRef = useRef();
  const nextClickHandler = () => {
    if (onNextClick) {
      onNextClick({ ...current, setStep });
    } else {
      setStep((prev) => prev + 1);
    }
  };
  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0] && !multiple) {
      onChange(e.target.files[0]);
    } else if (e.target.files && multiple) {
      onChange(e.target.files);
    }
    e.target.value = '';
  };
  const onClick = () => {
    inputRef.current.click();
  };

  return (
    <div className="fixed top-0 left-0 w-screen h-screen z-[200] bg-black/20">
      <div className="w-full h-full">
        <div className="w-full h-[13.2%]" onClick={() => onCloseModal(false)} />
        <div className="w-full h-[78.5%] flex">
          <div className="w-[24%] h-full" onClick={() => onCloseModal(false)} />

          <div
            className={`w-[${modalWidth}] h-full bg-white rounded-md overflow-hidden`}
          >
            <div className="relative w-full h-[10%] bg-[#eff0ff] flex justify-between items-center pl-8 px-14">
              <XMarkIcon
                className="w-4 h-4 absolute top-[15%] right-[2%] cursor-pointer"
                onClick={() => onCloseModal(false)}
              />

              <section className="flex items-center w-[45%] gap-x-4">
                {svg ? svg : null}
                <section className="">
                  <h3 className="font-semibold text-lg">{title}</h3>
                </section>
              </section>
              <section className="w-[60%] overflow-x-scroll no-scrollbar">
                {/* Click for breadcrumbs has been disabled as it did not have required checks */}
                <BreadCrumbs
                  pages={pages}
                  step={step}
                  // setStep={setStep}
                />
              </section>
            </div>
            <EmailFormWrapper
              isForm={canSubmit}
              step={step}
              pages={pages}
              current={current}
              onSubmit={onSubmit}
              nextClickHandler={nextClickHandler}
            >
              <>
                <div className="w-full h-[85%] px-2 pt-1 pb-0 overflow-y-scroll">
                  {children(current)}
                </div>
                <div
                  className={`h-[10%] w-full flex flex-row items-center justify-between  ${
                    isSubmitRequired && 'border-t'
                  }`}
                >
                  {canAddValue && (
                    <Button
                      onClick={() => {
                        const updatedValues = formData?.values
                          ? [...formData.values, '']
                          : [''];
                        setFormData((prev) => ({
                          ...prev,
                          values: updatedValues,
                        }));
                        setValueCount((prev) => {
                          return prev + 1;
                        });
                      }}
                      type="button"
                      // className={`text-center text-sm bg-blue-primary hover:bg-blue-hover text-white rounded-[8px] w-[200px] h-[32px]`}
                    >
                      <PlusCircleIcon height={20} width={20} />
                    </Button>
                  )}

                  <div
                    className={`flex items-center p-5 ${
                      onAdd && onAdd?.step?.includes(step) ? 'w-3/4' : 'w-full'
                    }`}
                  >
                    {isSubmitRequired && (
                      <Button
                        isLoading={btnIsLoading}
                        type={canSubmit ? 'submit' : 'button'}
                        onClick={(e) => {
                          if (!canSubmit && onSubmit) {
                            onSubmit(e, current);
                          }
                        }}
                        className={
                          isChangeInAssembly
                            ? `rounded-lg w-[100px] h-[32px] bg-[#14BA6D] hover:bg-[#21d17f]`
                            : `
                          rounded-lg w-[100px] h-[32px] bg-green-600 hover:brightness-105 ${
                            step + 1 === pages?.length || pages?.length === 0
                              ? ''
                              : 'hidden'
                          }`
                        }
                        color=""
                      >
                        {isChangeInAssembly ? 'Make Copy and Proceed' : 'Send'}
                      </Button>
                    )}

                    <div className="ml-4 flex w-[80%] gap-x-3 items-center">
                      <div className="">
                        <span
                          onClick={onClick}
                          title="Attachments"
                          className="w-4 cursor-pointer"
                        >
                          <MdOutlineAttachFile />
                        </span>
                        <input
                          ref={inputRef}
                          type="file"
                          className="hidden"
                          accept={accept}
                          onChange={handleChange}
                          multiple={multiple}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </>
            </EmailFormWrapper>
          </div>
          <div className="w-[24%] h-full" onClick={() => onCloseModal(false)} />
        </div>
        <div className="w-full h-[8.3%]" onClick={() => onCloseModal(false)} />
      </div>
    </div>
  );
};

export default EmailModal;
