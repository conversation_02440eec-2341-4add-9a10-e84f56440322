import { useEffect, useState } from 'react';
import Header from '../components/global/components/Header';
import Input from '../components/global/components/Input';
import SelectV2 from '../components/global/components/SelectV2';
import usePrefixIds from '../hooks/usePrefixIds';
import BomTable from '../components/bom/BomTable';
import {
  INITIAL_BOM_DATA,
  INITIAL_INPUT_DATA,
} from '../components/bom/bomConstants';
import {
  useCreateBomMutation,
  useEditBomMutation,
  useGetBomsForEditQuery,
} from '../slices/assemblyBomApiSlice';
import Button from '../components/global/components/Button';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useGetAllProductsForOptionsQuery } from '../slices/productApiSlice';
import { deleteFormDB, fetchFromDB, saveToDB, STORE_NAMES } from '../db/db';
import useAutoSave from '../hooks/useAutoSave';

export default function ManageBomForm() {
  const navigate = useNavigate();
  const { mode, id } = useParams();

  const [inputData, setInputData] = useState(INITIAL_INPUT_DATA);
  const [boms, setBoms] = useState([]);
  const [deletedMedias, setDeletedMedias] = useState([]);
  const [deletedBoms, setDeletedBoms] = useState([]);

  const isEdit = mode === 'edit';

  const { data: bomsData } = useGetBomsForEditQuery(
    { id },
    { skip: !id || !mode, refetchOnMountOrArgChange: true }
  );
  const [createBom] = useCreateBomMutation();
  const [editBom] = useEditBomMutation();

  const { data: allProducts } = useGetAllProductsForOptionsQuery();

  const { IdGenComp, idCompData } = usePrefixIds({ idFor: 'assemblyBomId' });

  const bomsToMap = boms.filter((bom) => bom.variantIndex === 1);

  useAutoSave({
    async fn() {
      await Promise.all([
        saveToDB(STORE_NAMES.autosaveForm, 'bom-autosave-inputdata', inputData),
        saveToDB(STORE_NAMES.autosaveForm, 'bom-autosave-boms', boms),
      ]);
    },
  });

  useEffect(() => {
    if (bomsData) {
      const topBom = INITIAL_INPUT_DATA;
      const bomArray = [];
      bomsData.forEach((bom) => {
        if (!bom.topBomId) {
          topBom.name = bom.name;
          topBom.bomProduct = bom.bomProduct;
          topBom.bomId = bom.bomId;
        } else {
          bomArray.push(bom);
        }
      });
      setInputData(topBom);
      setBoms(bomArray);
    }
  }, [bomsData]);

  useEffect(() => {
    if (mode) return;
    (async () => {
      const [ipData, bomData] = await Promise.all([
        fetchFromDB(STORE_NAMES.autosaveForm, 'bom-autosave-inputdata'),
        fetchFromDB(STORE_NAMES.autosaveForm, 'bom-autosave-boms'),
      ]);

      setInputData(ipData || INITIAL_INPUT_DATA);
      setBoms(bomData || []);
    })();
  }, [mode]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    setInputData((prev) => {
      const data = { ...prev, [name]: value };
      return data;
    });
  };

  const handleAddItem = () => {
    setBoms((prev) => [
      ...prev,
      {
        ...INITIAL_BOM_DATA,
        variantIndex: 1,
        rowId: (bomsToMap?.length + 1).toString(),
        tempId: Date.now().toString(),
      },
    ]);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!inputData?.name) {
      toast.error('Name is required', { toastId: 'noname' });
      return;
    }

    if (!inputData?.bomProduct) {
      toast.error('Product is required', { toastId: 'noproduct' });
      return;
    }

    if (boms?.length === 0) {
      toast.error('Please add atleast one item', { toastId: 'nobom' });
      return;
    }

    let res;

    if (isEdit) {
      res = await editBom({
        id,
        bomObj: {
          ...inputData,
          boms,
          deletedMedias,
          deletedBoms,
        },
      });
    } else {
      res = await createBom({
        ...inputData,
        idData: idCompData?.dataToReturn,
        boms,
      }).unwrap();
    }

    if (res) {
      await Promise.all([
        deleteFormDB(STORE_NAMES.autosaveForm, 'bom-autosave-inputdata'),
        deleteFormDB(STORE_NAMES.autosaveForm, 'bom-autosave-boms'),
      ]);
      toast.success(!isEdit ? 'Bom created' : 'Bom updated');
      navigate('/jobs/bom');
    }
  };

  return (
    <div>
      <div className="flex w-full items-center justify-between mb-5">
        <Header title={`${isEdit ? 'Edit' : 'Create'} Bom`} />
        <Button onClick={() => navigate(-1)}>Go Back</Button>
      </div>

      <div className="w-full bg-white px-5 py-3 md:w-10/12 mx-auto">
        <div className="w-full ">
          <label>BOM ID:</label>
          {isEdit ? (
            <span className="text-gray-900">{inputData?.bomId || '-'}</span>
          ) : (
            <IdGenComp {...idCompData} />
          )}
        </div>
        <section className="grid md:grid-cols-2 gap-2 mt-2">
          <div className={'md:w-3/4 w-full'}>
            <label>Name</label>
            <Input
              placeholder="Enter Name"
              name="name"
              value={inputData?.name}
              onChange={handleInputChange}
            />
          </div>
          <div className={`md:w-3/4 w-full justify-self-end`}>
            <label>Product</label>
            <SelectV2
              placeholder="Select Product"
              name="bomProduct"
              value={inputData?.bomProduct}
              onChange={handleInputChange}
              options={allProducts}
            />
          </div>
        </section>

        <div className="mt-3">
          <label>Assembly Bom</label>
          <BomTable
            boms={boms}
            setBoms={setBoms}
            allProducts={allProducts}
            setDeletedMedias={setDeletedMedias}
            setDeletedBoms={setDeletedBoms}
          />
        </div>
        <div className="flex w-full items-center justify-between mt-3 px-5">
          <Button onClick={handleAddItem} disabled={!inputData?.bomProduct}>
            Add Item
          </Button>
          <Button onClick={handleSubmit}>Submit</Button>
        </div>
      </div>
    </div>
  );
}
