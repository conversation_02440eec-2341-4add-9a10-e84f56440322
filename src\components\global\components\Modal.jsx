import {
  MinusCircleIcon,
  PlusCircleIcon,
  TruckIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useCallback, useMemo, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { mobileWidth, tabletWidth } from '../../../helperFunction';
import useOutsideClick from '../../../hooks/useOutsideClick';
import AddIcon from './../../../assets/images/add.png';
import BackIcon from './../../../assets/images/back.png';
import NextIcon from './../../../assets/images/next.png';
import SubmitIcon from './../../../assets/images/submit.png';
import BreadCrumbs from './BreadCrumbs';
import Button from './Button';
import FullScreenModalMobileView from './FullScreenModalMobileView';

const FormWrapper = ({
  children,
  isForm = true,
  step,
  pages,
  current,
  onSubmit,
  nextClickHandler,
}) => {
  if (isForm) {
    return (
      <form
        onSubmit={(e) => {
          e.preventDefault();
          if (step === pages?.length - 1 || pages?.length < 2) {
            onSubmit(e, current);
          } else {
            nextClickHandler();
          }
        }}
        className="w-full h-[82.5%]"
      >
        {children}
      </form>
    );
  } else {
    return <div className="w-full h-[82.5%]">{children}</div>;
  }
};

// origional Modal

const Modal = ({
  className,
  children,
  title,
  svg,
  description,
  pages = [],
  onNextClick,
  onBackClick,
  onSubmit,
  onCloseModal,
  onAdd,
  canSubmit = true,
  indexStartFromZero = true,
  isSubmitRequired = true,
  btnIsLoading = false,
  nextIsLoading = false,
  isBackButton = false,
  canAddValue = false,
  canRemoveValue = false,
  formData = [],
  setFormData,
  isChangeInAssembly = false,
  setValueCount,
  modalWidth = '55%',
  modalHeight = '75%',
  canDispatch = false,
  onDispatch,
  isConfirmOpen = false,
  zIndex,
  submitText = 'Submit',
}) => {
  const [step, setStep] = useState(0);
  const current = useMemo(
    () => ({ step, page: pages?.[step], setStep }),
    [step, pages]
  );
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const modalRef = useOutsideClick(onCloseModal, isConfirmOpen);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const nextClickHandler = useCallback(() => {
    if (onNextClick) {
      onNextClick({ ...current, setStep });
    } else {
      setStep((prev) => prev + 1);
    }
  }, [onNextClick, current, setStep]);

  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();
      if (isSubmitting) return;

      setIsSubmitting(true);
      if (onSubmit) {
        onSubmit(e, current);
      }

      setTimeout(() => {
        setIsSubmitting(false);
      }, 1000);
    },
    [onSubmit, current, isSubmitting]
  );

  const overlayVariants = {
    hidden: { opacity: 0, backdropFilter: 'blur(0px)' },
    visible: {
      opacity: 1,
      backdropFilter: 'blur(2px)',
      transition: { duration: 0.3 },
    },
    exit: {
      opacity: 0,
      backdropFilter: 'blur(0px)',
      transition: { duration: 0.2 },
    },
  };

  return (
    <AnimatePresence>
      {isMobile ? (
        <FullScreenModalMobileView
          current={current}
          step={step}
          setStep={setStep}
          nextClickHandler={nextClickHandler}
          className={className}
          title={title}
          svg={svg}
          description={description}
          pages={pages}
          onNextClick={onNextClick}
          onBackClick={onBackClick}
          onSubmit={onSubmit}
          onCloseModal={onCloseModal}
          onAdd={onAdd}
          canSubmit={canSubmit}
          indexStartFromZero={indexStartFromZero}
          isSubmitRequired={isSubmitRequired}
          btnIsLoading={btnIsLoading}
          nextIsLoading={nextIsLoading}
          isBackButton={isBackButton}
          canAddValue={canAddValue}
          canRemoveValue={canRemoveValue}
          formData={formData}
          setFormData={setFormData}
          isChangeInAssembly={isChangeInAssembly}
          setValueCount={setValueCount}
          canDispatch={canDispatch}
          onDispatch={onDispatch}
        >
          {children}
        </FullScreenModalMobileView>
      ) : (
        <>
          <motion.div
            className="fixed inset-0 bg-black/40 z-40"
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={onCloseModal}
            style={{
              backdropFilter: 'blur(2px)',
            }}
          />
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={`fixed top-0 left-0 w-screen h-screen z-[100] bg-black/20 ${className} ${zIndex ? `z-[${zIndex}]` : 'z-[1001]'}`}
          >
            <div className="w-full h-full flex justify-center items-center">
              <motion.div
                ref={modalRef}
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                className={`w-[${isMobile ? '100%' : isTablet ? '80%' : modalWidth}] h-[${isMobile ? '100%' : isTablet ? '80%' : modalHeight}] bg-white rounded-lg overflow-hidden shadow-2xl`}
              >
                <div className="relative w-full h-[18.5%] bg-[#eff0ff] flex justify-between items-center pl-8 px-14">
                  <XMarkIcon
                    className="w-6 h-6 absolute top-[15%] right-[2%] cursor-pointer"
                    onClick={() => onCloseModal(false)}
                  />

                  <section className="flex items-center w-[45%] gap-x-4">
                    {svg ? svg : null}
                    <section className="">
                      <h3 className="font-semibold !text-sm md:!text-lg">
                        {title}
                      </h3>
                      <p className="font-semi-bold text-xs text-[#919487]">
                        {description}
                      </p>
                    </section>
                  </section>
                  <section className="w-[60%] overflow-x-scroll no-scrollbar">
                    <BreadCrumbs pages={pages} step={step} />
                  </section>
                </div>
                <FormWrapper
                  isForm={canSubmit}
                  step={step}
                  pages={pages}
                  current={current}
                  onSubmit={onSubmit}
                  nextClickHandler={nextClickHandler}
                >
                  <>
                    <div className="w-full h-[85%] px-8 py-5 overflow-y-auto">
                      {children(current)}
                    </div>
                    <div
                      className={`h-[15%] w-full flex flex-row items-center justify-between px-8 ${
                        isSubmitRequired && 'border-t border-gray-200'
                      }`}
                    >
                      {canAddValue && (
                        <Button
                          onClick={() => {
                            const updatedValues = formData?.values
                              ? [...formData.values, '']
                              : [''];
                            setFormData((prev) => ({
                              ...prev,
                              values: updatedValues,
                            }));
                            setValueCount((prev) => prev + 1);
                          }}
                          type="button"
                          className="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-2 transition-colors"
                        >
                          <PlusCircleIcon height={24} width={24} />
                        </Button>
                      )}

                      {canRemoveValue && (
                        <div className="justify-start flex relative">
                          <Button
                            onClick={() => {
                              if (formData?.values?.length > 0) {
                                const updatedValues = formData.values.slice(
                                  0,
                                  -1
                                );
                                setFormData((prev) => ({
                                  ...prev,
                                  values: updatedValues,
                                }));
                              }
                              setValueCount((prev) => prev - 1);
                            }}
                            type="button"
                            className="bg-red-500 hover:bg-red-600 text-white rounded-full p-2 transition-colors ml-4"
                          >
                            <MinusCircleIcon height={24} width={24} />
                          </Button>
                        </div>
                      )}
                      {onAdd && onAdd?.step?.includes(step) && (
                        <div className="max-w-max items-center flex justify-start">
                          <Button
                            type="button"
                            className="rounded-full bg-green-500 hover:bg-green-600 text-white px-6 py-2 text-sm transition-colors"
                            onClick={
                              onAdd.func[indexStartFromZero ? step : step - 1]
                            }
                          >
                            <img
                              src={AddIcon}
                              alt="Add Icon"
                              className="w-5 h-5 object-contain inline-block mr-2"
                            />
                            {onAdd.label}
                          </Button>
                        </div>
                      )}

                      <div
                        className={`flex items-center gap-x-4 justify-end ${
                          onAdd && onAdd?.step?.includes(step)
                            ? 'w-3/4'
                            : 'w-full'
                        }`}
                      >
                        {(isBackButton || step > 0) && (
                          <Button
                            type="button"
                            className="rounded-full bg-gray-600 hover:bg-gray-400 text-gray-800 px-6 py-2 transition-colors"
                            onClick={() => {
                              if (onBackClick) {
                                onBackClick({ ...current, setStep });
                              } else {
                                setStep((prev) => prev - 1);
                              }
                            }}
                          >
                            <img
                              src={BackIcon}
                              alt="Back Icon"
                              className="w-5 h-5 object-contain inline-block mr-2"
                            />
                            Back
                          </Button>
                        )}
                        {canDispatch && (
                          <Button
                            onClick={onDispatch}
                            className="rounded-full bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 transition-colors"
                          >
                            <TruckIcon className="w-5 h-5 inline-block mr-2" />
                            Dispatch
                          </Button>
                        )}

                        {isSubmitRequired && (
                          <Button
                            isLoading={btnIsLoading}
                            type={canSubmit ? 'submit' : 'button'}
                            onClick={handleSubmit}
                            className={
                              isChangeInAssembly
                                ? `rounded-full w-[180px] bg-green-500 hover:bg-green-600 text-white px-6 py-2 transition-colors`
                                : `rounded-full w-[138px] bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 transition-colors ${
                                    step + 1 === pages?.length ||
                                    pages?.length === 0
                                      ? ''
                                      : 'hidden'
                                  }`
                            }
                          >
                            <img
                              src={SubmitIcon}
                              alt="Submit Icon"
                              className="w-5 h-5 object-contain inline-block mr-2"
                            />
                            {isChangeInAssembly
                              ? 'Make Copy and Proceed'
                              : submitText}
                          </Button>
                        )}

                        {pages?.length !== 0 && step + 1 !== pages?.length && (
                          <Button
                            type="button"
                            className="rounded-full bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 transition-colors"
                            isLoading={nextIsLoading}
                            onClick={nextClickHandler}
                          >
                            <img
                              src={NextIcon}
                              alt="Next Icon"
                              className="w-5 h-5 object-contain inline-block mr-2"
                            />
                            Next
                          </Button>
                        )}
                      </div>
                    </div>
                  </>
                </FormWrapper>
              </motion.div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export const GridWrapper = ({ children, className = '' }) => {
  return (
    <div
      className={`grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2 ${className}`}
    >
      {children}
    </div>
  );
};

export const LabelAndField = ({
  children,
  label,
  htmlFor,
  className = '',
  labelClassName = '',
}) => {
  return (
    <div className={`flex flex-col my-5 w-full ${className}`}>
      <label
        htmlFor={htmlFor}
        className={`mb-1 font-semibold text-[#667085] ${labelClassName}`}
      >
        {label}
      </label>
      {children}
    </div>
  );
};

export const Spacer = ({ text = '', className = '' }) => {
  return (
    <div
      className={`col-span-full text-center text-xs pt-[1px] bg-gray-400 relative text-[#667085]  ${className}`}
    >
      {text && (
        <span className="absolute left-1/2 -translate-x-1/2 bg-white -top-[7px] px-2">
          {text}
        </span>
      )}
    </div>
  );
};

export default Modal;
