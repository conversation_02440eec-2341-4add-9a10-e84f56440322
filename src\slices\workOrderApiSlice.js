// import { apiSlice } from './apiSlice';

// const baseRoute = '/v1/workorder';

// const workOrderApiSlice = apiSlice.injectEndpoints({
//   endpoints: (builder) => ({
//     getScrollBasedWorkOrders: builder.query({
//       query: ({
//         cursor,
//         limit = 15,
//         search,
//         sortBy = 'latestActivityTime',
//         sortOrder = 'desc',
//       }) => {
//         const params = new URLSearchParams();
//         if (cursor) params.append('cursor', cursor);
//         if (limit) params.append('limit', limit);
//         if (search) params.append('search', search);
//         if (sortBy) params.append('sortBy', sortBy);
//         if (sortOrder) params.append('sortOrder', sortOrder);

//         return `${baseRoute}/scrolledBasedWorkOrders?${params.toString()}`;
//       },
//     }),
//   }),
// });

// export const {
//   useGetScrollBasedWorkOrdersQuery,
//   useLazyGetScrollBasedWorkOrdersQuery,
// } = workOrderApiSlice;

// V2

import { apiSlice } from './apiSlice';

const baseRoute = '/v1/workorder';

const workOrderApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getScrollBasedWorkOrders: builder.query({
      query: ({
        cursor,
        limit = 15,
        search,
        sortBy = 'latestActivityTime',
        sortOrder = 'desc',
        direction = 'down',
      }) => {
        const params = new URLSearchParams();

        if (cursor) {
          const jsonCursor =
            typeof cursor === 'string' ? cursor : JSON.stringify(cursor);
          params.set('cursor', jsonCursor);
        }

        params.set('limit', limit);
        params.set('sortBy', sortBy);
        params.set('sortOrder', sortOrder);
        params.set('direction', direction);

        if (search) params.set('search', search);

        return `${baseRoute}/scrolledBasedWorkOrders?${params.toString()}`;
      },
    }),
  }),
});

export const {
  useGetScrollBasedWorkOrdersQuery,
  useLazyGetScrollBasedWorkOrdersQuery,
} = workOrderApiSlice;
