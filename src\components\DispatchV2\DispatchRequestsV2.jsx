import { Button, Card, Checkbox, Progress } from 'antd';
import { Trash2 } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { getLocalDate } from '../../helperFunction';
import {
  useDeleteManyRequestsMutation,
  useGetAllDispatchRequestV2Query,
} from '../../slices/dispatchRequestV2ApiSlice';
import { useGetAvailableQuantityQuery } from '../../slices/dispatchV2ApiSlice';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import { customConfirm } from '../../utils/customConfirm';
import Filter from '../global/components/Filter';
import Pagination from '../global/components/Pagination';
import Spinner from '../global/components/Spinner';
import TransportIndents from './TransportIndents';

const DispatchRequestsV2 = () => {
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const navigate = useNavigate();
  const [FilterHeadingvalue, setFilterHeadingvalue] = useState('');
  const [Filtervalue, setFilterValue] = useState('');
  const [checkedIds, setCheckedIds] = useState([]);
  const [activeTab, setActiveTab] = useState('dispatch'); // 'dispatch' or 'transport'

  // Queries
  const { data: totalDispatchedItems } = useGetAvailableQuantityQuery();
  const [deleteManyRequests] = useDeleteManyRequestsMutation();
  const { data: allRequests, isLoading } = useGetAllDispatchRequestV2Query({
    page: page,
    limit: limit,
    debounceSearch: '',
    fieldName: FilterHeadingvalue,
    fieldValue: Filtervalue,
  });

  const handleCheckboxChange = (checked, id) => {
    setCheckedIds((prev) =>
      checked ? [...prev, id] : prev.filter((itemId) => itemId !== id)
    );
  };

  const getSoProgressValue = (SoId) => {
    const matchSO = totalDispatchedItems?.find(
      (item) => item.salesOrder === SoId
    );
    let totalDispatchedItemsQuantity = 0;
    matchSO?.items?.forEach(
      (item) => (totalDispatchedItemsQuantity += item?.totalDispatchQuantity)
    );
    let originalItemsQuantity = 0;
    const order = allRequests?.results?.find(
      (item) => item.salesOrderId?._id === SoId
    )?.salesOrderId;
    order?.products?.forEach(
      (item) => (originalItemsQuantity += item?.quantity)
    );
    return (totalDispatchedItemsQuantity / originalItemsQuantity) * 100 || 0;
  };

  const handleDeleteIds = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to delete',
      'error'
    );
    if (!confirm) return;
    if (checkedIds.length === 0) {
      toast.error('Please select dispatch requests to delete.');
      return;
    }
    try {
      await deleteManyRequests({
        ids: checkedIds,
      });
      toast.success('Dispatch requests deleted successfully.');
      setCheckedIds([]);
    } catch (error) {
      toast.error('Failed to delete dispatch requests.');
    }
  };

  const filterOptions = [
    { name: 'Date', value: 'createdAt' },
    {
      name: 'Sales Order',
      value: JSON.stringify({
        label: ['salesOrderId.salesOrderID', 'salesOrderId.salesOrderID'],
        value: ['salesOrderId._id', 'salesOrderId._id'],
        path: 'salesOrderId',
      }),
    },
  ];

  return (
    <div>
      <div className="flex gap-2 mt-4">
        <Button
          type={activeTab === 'dispatch' ? 'primary' : 'default'}
          onClick={() => setActiveTab('dispatch')}
        >
          Dispatch
        </Button>
        <Button
          type={activeTab === 'transport' ? 'primary' : 'default'}
          onClick={() => setActiveTab('transport')}
        >
          Transport Indents
        </Button>
      </div>
      {activeTab === 'dispatch' && (
        <div>
          <div className="flex justify-self-end">
            <Filter
              className={'mt-1 '}
              headingfilteroptions={filterOptions}
              secondfilterdata={allRequests?.results}
              setSelectedHeading={setFilterHeadingvalue}
              setSelectedValue={setFilterValue}
            />
            <Button
              danger
              icon={<Trash2 className="h-4 w-4" />}
              className="ml-4"
              onClick={handleDeleteIds}
              disabled={checkedIds.length === 0}
              type="primary"
            >
              Delete
            </Button>
          </div>

          {/* Display all dispatch requests */}
          {allRequests?.results?.length === 0 && (
            <p className="text-center bg-white px-4 py-6 text-sm mb-4">
              No dispatch requests found.
            </p>
          )}
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="flex flex-col gap-y-2 my-2">
              {allRequests?.results?.map((request, idx) => {
                const percentage = getSoProgressValue(
                  request?.salesOrderId?._id
                );
                const isChecked = checkedIds.includes(request?._id);
                return (
                  <Card key={idx} className="hover:bg-gray-100 cursor-pointer">
                    <div className="flex md:flex-row flex-col md:justify-between md:gap-y-0 gap-y-4 px-4 py-6 items-center">
                      <div className="flex gap-x-4">
                        <Checkbox
                          checked={isChecked}
                          onChange={(e) =>
                            handleCheckboxChange(e.target.checked, request?._id)
                          }
                        />
                        <div className="flex flex-col">
                          <span>
                            <span className="font-semibold text-gray-700">
                              Sales Order ID: &nbsp;
                            </span>
                            {request?.salesOrderId?.salesOrderID}
                          </span>
                          <span>
                            <span className="font-semibold text-gray-700">
                              Date: &nbsp;
                            </span>
                            {getLocalDate(request?.createdAt)}
                          </span>
                        </div>
                        <Progress
                          type="circle"
                          percent={Math.min(percentage, 100)}
                          size={50}
                          strokeWidth={10}
                          format={(percent) => (
                            <span className="text-[10px] font-semibold">
                              {percent?.toFixed(2)}%
                            </span>
                          )}
                        />
                      </div>
                      <Button
                        type="primary"
                        onClick={() => {
                          if (
                            request?.salesOrderId?.salesOrderStatus?.toLowerCase() !==
                            'approved'
                          ) {
                            toast.info(
                              'Only approved sales order can be dispatched'
                            );
                            return;
                          }
                          navigate(
                            `/dispatch/dashboard/createDispatch/?soId=${request?.salesOrderId?._id}`
                          );
                        }}
                      >
                        + Create
                      </Button>
                    </div>
                  </Card>
                );
              })}
            </div>
          )}
          <Pagination
            limit={limit}
            page={page}
            totalPages={allRequests?.totalPages}
            totalResults={allRequests?.totalResults}
            setPage={setPage}
            setLimit={setLimit}
            className={`w-full`}
          />
        </div>
      )}
      {activeTab === 'transport' && (
        <div>
          <TransportIndents />
        </div>
      )}
    </div>
  );
};

export default DispatchRequestsV2;
