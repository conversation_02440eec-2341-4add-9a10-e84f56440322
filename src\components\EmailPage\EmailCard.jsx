import { useEffect, useState } from 'react';
import { RiDeleteBin6Line, RiStarLine, RiStarSFill } from 'react-icons/ri';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FormatDate } from '../../helperFunction';
import {
  useAddFlagMutation,
  useMoveToFoldersMutation,
} from '../../slices/EmailApiSlice';
import { getstrLen } from '../../utils/Getstrlen';
import Spinner from '../global/components/Spinner';
const EmailCard = ({
  sendername,
  subject,
  date,
  selectmail,
  isActive,
  parameter,
  mail,
  setMailUid,
  setDraftMailDetails,
  setShowDraftModal,
  params,
  isSeen,
}) => {
  const [senderTitle, setSenderTitle] = useState('');
  const [backgroundColor, setBackgroundColor] = useState('');
  const [StarredLoading, setIsStarredLoadingLoading] = useState(false);
  const [Loading, setIsLoading] = useState(false);
  const [addFlag] = useAddFlagMutation();
  const [moveToBin] = useMoveToFoldersMutation();
  const dateConfig = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
  };
  const [SearchParams] = useSearchParams({
    tab: '',
  });
  // Map to store colors for each sender title
  const senderColorMap = {};

  const handleAddStarred = async () => {
    setIsStarredLoadingLoading(true);
    const flag = '\\Flagged';
    const data = await addFlag({
      emailId: mail?.uid,
      flag,
      mailbox: SearchParams.get('tab'),
    }).unwrap();
    setIsStarredLoadingLoading(false);
    if (data?.data[0]?.flags?.includes(flag)) {
      toast.success('Message Starred');
    } else {
      toast.success('Message UnStarred');
    }
  };
  const handleAddToBin = async () => {
    setIsLoading(true);
    const data = await moveToBin({
      emailId: mail?.uid,
      mailbox: SearchParams.get('tab'),
    }).unwrap();

    if (data?.data) {
      setMailUid(mail?.uid);
      setIsLoading(false);
      toast.success('Message Sent To Bin');
    }
  };
  const handleClick = () => {
    selectmail();
  };
  const handleSendDraftMail = () => {
    if (params?.get('tab') === 'drafts') {
      setDraftMailDetails(mail);
      setShowDraftModal(true);
    }
  };
  useEffect(() => {
    const senderTitleVal = sendername[0].charAt(0);
    setSenderTitle(senderTitleVal);

    // Generate a unique color based on the sender title
    if (!senderColorMap[senderTitleVal]) {
      // If color not assigned yet, generate a random color and store it
      senderColorMap[senderTitleVal] = `hsl(${Math.random() * 360}, 70%, 80%)`;
    }

    setBackgroundColor(senderColorMap[senderTitleVal]);
    //eslint-disable-next-line
  }, [sendername, parameter]);
  return (
    <>
      <div
        className={`main-email-card h-[6rem] overflow-hidden  ${
          isActive ? 'bg-gray-200' : 'bg-white'
        } rounded-[4px] cursor-pointer flex hover:bg-gray-50 justify-between p-2 transform shadow-md transition-all duration-300`}
        onClick={handleClick}
      >
        <div className="py-4 pl-4 w-[80%]" onClick={handleSendDraftMail}>
          <div className="flex items-center">
            <div
              className={`w-10 h-10 p-4 rounded-full flex items-center justify-center`}
              style={{ backgroundColor }}
            >
              <span className="text-gray-600 text-lg font-semibold">
                {senderTitle}
              </span>
            </div>
            <div className="ml-4 w-full">
              <p
                className={`font-semibold text-xs ${
                  sendername
                    ?.toString()
                    ?.toLowerCase()
                    ?.includes(parameter?.toLowerCase()) &&
                  parameter?.length !== 0
                    ? 'bg-yellow-200'
                    : ''
                } ${isSeen === 'seen' && 'opacity-[0.5]'}`}
              >
                {sendername}
              </p>
              {/* {console.log('Subject is' + subject)} */}
              <p className="text-[10px] text-slate-600">
                {(getstrLen(subject) > 20
                  ? `${subject?.slice(0, 50)}`
                  : subject?.toString()
                )
                  ?.split(' ')
                  ?.map((word, index) => (
                    <span
                      key={index}
                      className={
                        word
                          ?.toString()
                          ?.toLowerCase()
                          ?.includes(parameter?.toLowerCase()?.substring(0)) &&
                        parameter?.length >= 3
                          ? 'bg-yellow-200'
                          : ''
                      }
                    >
                      {word}{' '}
                    </span>
                  ))}
                {getstrLen(subject) > 20 && '...'}
              </p>
            </div>
          </div>
        </div>
        <div className="flex flex-col items-end justify-between">
          <div className="flex w-full justify-around">
            {Loading ? (
              <span className="">
                <Spinner size={4} />
              </span>
            ) : (
              <span
                className="w-fit text-gray-400 hover:text-[#E81127] cursor-pointer"
                title="Delete"
                onClick={handleAddToBin}
              >
                <RiDeleteBin6Line />
              </span>
            )}
            {StarredLoading ? (
              <span className="">
                <Spinner size={4} />
              </span>
            ) : (
              <span
                className="w-fit cursor-pointer text-yellow-400 hover:text-yellow-500"
                title="Starred"
                onClick={handleAddStarred}
              >
                {mail?.flags?.includes('\\Flagged') ? (
                  <RiStarSFill />
                ) : (
                  <RiStarLine />
                )}
              </span>
            )}
          </div>

          <span className="text-[8px] w-12 ">
            {FormatDate(date, dateConfig)
              .split(',')
              .map((word, index) => (
                <span key={index}>
                  {word.trim()}
                  <br />
                </span>
              ))}
          </span>
        </div>
      </div>
    </>
  );
};

export default EmailCard;
