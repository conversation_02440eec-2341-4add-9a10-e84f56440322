import { Badge, Table, Tag, Tooltip } from 'antd';
import { Download, Edit2 } from 'lucide-react';
import { useContext, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getLocalDate, handlePdf } from '../../helperFunction';
import { useLazyGetPdfQuery } from '../../slices/pdfApiSlice';
import { Store } from '../../store/Store';
import Spinner from '../global/components/Spinner';
import ShowTemplateValues from '../v3/global/components/ShowTemplateValues';
import MediaPreviewModal from './MediaPreviewModal';

const DispatchSideBar = ({ selectedDispatch, totalDispatchedItems }) => {
  const [getPdf, { isFetching: isLoading }] = useLazyGetPdfQuery();
  const [openMediaModal, setOpenMediaModal] = useState(false);
  const [mediaData, setMediaData] = useState([]);
  const navigate = useNavigate();
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const downloadPDF = async () => {
    const dispatchId = selectedDispatch?._id;
    await handlePdf(getPdf, dispatchId, 'dispatchV2');
  };

  const getAvailableQuantity = (salesOrderId, productId, quantity) => {
    const matchingSO = totalDispatchedItems?.find(
      (item) => item?.salesOrder === salesOrderId
    );
    const currentItemTotalDispatched = matchingSO?.items?.find(
      (item) => item?.itemId === productId
    )?.totalDispatchQuantity;
    const availableQuantity = quantity - (currentItemTotalDispatched || 0);
    return availableQuantity > 0 ? availableQuantity : 0;
  };

  const itemColumns = [
    {
      title: 'Item Name',
      dataIndex: 'details',
      key: 'details',
      width: '40%',
      render: (details) => {
        return details?.length > 10 ? (
          <Tooltip title={details}>
            <span>{details}</span>
          </Tooltip>
        ) : (
          <span>{details}</span>
        );
      },
    },
    {
      title: 'Dispatch Qty',
      dataIndex: 'dispatchQuantity',
      key: 'dispatchQuantity',
      align: 'center',

      render: (qty) => (
        <Tag color="blue" className="px-3 py-1">
          {qty}
        </Tag>
      ),
    },
    {
      title: 'Media',
      key: 'media',
      align: 'center',
      render: (_, record) => {
        return record?.media?.length === 0 ? (
          'N/A'
        ) : (
          <Badge
            color="blue"
            className="cursor-pointer"
            count={record?.media?.length || 0}
            onClick={() => {
              setOpenMediaModal(true);
              setMediaData(record);
            }}
          />
        );
      },
    },
    {
      title: 'Sales Order Id',
      key: 'order',
      align: 'center',
      render: (record) => {
        const { salesOrderName } = record;
        return salesOrderName?.length > 10 ? (
          <Tooltip title={salesOrderName}>
            <span>{salesOrderName}</span>
          </Tooltip>
        ) : (
          <span>{salesOrderName}</span>
        );
      },
    },
  ];
  return (
    <div>
      <MediaPreviewModal
        openMediaModal={openMediaModal}
        setOpenMediaModal={setOpenMediaModal}
        mediaData={mediaData}
        setMediaData={setMediaData}
      />
      <div className="p-4" key={selectedDispatch?.updatedAt}>
        <div className="mb-6">
          <header className="flex justify-between items-center">
            <h3 className="text-lg font-semibold mb-2">Dispatch Details</h3>
            <div className="flex gap-x-4">
              <Tooltip title={'Download PDF'}>
                {isLoading ? (
                  <Spinner className="w-6 h-6" />
                ) : (
                  <Download
                    size={20}
                    onClick={downloadPDF}
                    className="cursor-pointer"
                  />
                )}
              </Tooltip>
              <Tooltip title={'Edit'}>
                <Edit2
                  size={20}
                  onClick={() =>
                    navigate(
                      `/dispatch/dashboard/createDispatch/?id=${selectedDispatch?._id}`
                    )
                  }
                  className="cursor-pointer"
                />
              </Tooltip>
            </div>
          </header>
          <div className="space-y-2 bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between">
              <span className="text-gray-600">Dispatch Id:</span>
              <span>{selectedDispatch?.dispatchId}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Status:</span>
              <Tag
                color={
                  selectedDispatch?.status === 'completed' ? 'green' : 'gold'
                }
              >
                {selectedDispatch?.status?.toUpperCase()}
              </Tag>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-600">Created:</span>
              <span>{getLocalDate(selectedDispatch?.createdAt)}</span>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3">Item Details</h3>
          <Table
            dataSource={selectedDispatch?.salesOrders?.flatMap((so, soIndex) =>
              so?.items?.map((item, itemIndex) => {
                const originalQuantity = so?.salesOrder?.products?.find(
                  (p) => p?._id === item?.itemId
                )?.quantity;
                return {
                  ...item,
                  soIndex,
                  itemIndex,
                  availableQuantity: getAvailableQuantity(
                    so?.salesOrder?._id,
                    item?.itemId,
                    originalQuantity
                  ),
                  originalQuantity,
                  salesOrderName: so?.salesOrder?.salesOrderID,
                };
              })
            )}
            columns={itemColumns}
            pagination={false}
            rowKey="itemId"
            size="small"
            className="bg-gray-50"
            bordered
          />
        </div>

        {selectedDispatch?.additionalFields?.templateData?.length > 0 && (
          <div className="mt-6">
            <ShowTemplateValues
              template={selectedDispatch?.additionalFields?.templateData}
              defaultParam={defaultParam}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default DispatchSideBar;
