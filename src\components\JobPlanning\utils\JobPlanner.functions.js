import { useState } from 'react';

const HoverTooltip = ({ jobs }) => {
  const [show, setShow] = useState(false);

  return (
    <div className="relative w-fit text-center">
      {jobs.length > 1 && (
        <span
          className="text-sky-500 underline ml-1 cursor-pointer"
          onMouseEnter={() => {
            setShow(true);
          }}
          onMouseLeave={() => {
            setShow(false);
          }}
        >
          {`+${jobs?.length - 1} others`}
        </span>
      )}
      {show && (
        <span className="absolute right-[0rem] top-[1.5rem] z-10 bg-slate-300 py-1 min-w-[11rem]">
          {jobs.map((el, idx) => {
            if (idx !== 0) {
              return <>{`${el?.name} (${el?.id})`}</>;
            }
          })}
        </span>
      )}
    </div>
  );
};

export const filterJobsBasedOnDateTile = (day, month, jobsObj) => {
  let jobs;
  if (jobsObj.length === undefined) {
    jobs = jobsObj.data;
  } else {
    jobs = jobsObj;
  }
  let res = [];
  if (jobs) {
    for (let el of jobs) {
      let startDate = new Date(el?.startDate);
      let endDate = new Date(el?.endDate);

      if (startDate?.getMonth() !== endDate?.getMonth()) {
        if (startDate?.getMonth() === month) {
          if (day >= startDate?.getDate()) {
            res.push(el);
          }
        }
        if (endDate?.getMonth() === month) {
          if (day <= endDate?.getDate()) {
            res.push(el);
          }
        }
      } else {
        if (
          day >= startDate?.getDate() &&
          day <= endDate?.getDate() &&
          month >= startDate?.getMonth() &&
          month <= endDate?.getMonth()
        ) {
          res.push(el);
        }
      }
    }
  }
  return res;
};

export const filterUnscheduledJobsBasedOnDate = (
  day,
  month,
  uncheduledJobs,
  jobs
) => {
  let res = [];
  if (uncheduledJobs) {
    for (let el of uncheduledJobs) {
      let startDate = new Date(el?.startTime);
      let endDate = new Date(el?.stopTime || new Date());
      if (startDate?.getMonth() !== endDate?.getMonth()) {
        if (startDate?.getMonth() === month) {
          if (day >= startDate?.getDate()) {
            res.push(el);
          }
        }
        if (endDate?.getMonth() === month) {
          if (day <= endDate?.getDate()) {
            res.push(el);
          }
        }
      } else {
        if (
          day >= startDate?.getDate() &&
          day <= endDate?.getDate() &&
          month >= startDate?.getMonth() &&
          month <= endDate?.getMonth()
        ) {
          res.push(el);
        }
      }
    }
  }
  let temp = [];
  for (let index = 0; index < res.length; index++) {
    let check = true;
    let el = res[index];
    for (let elem of jobs) {
      if (elem?.createInput?.id === el?.project?.id) {
        check = false;
        break;
      }
    }
    if (check) {
      let isDuplicate = false;
      for (let i = 0; i < temp.length; i++) {
        let elem = temp[i];
        if (el?.project?.id === elem?.project.id) {
          isDuplicate = true;
          temp[i] = {
            ...el,
            machineAndOperator: [
              ...el?.machineAndOperator,
              ...elem?.machineAndOperator,
            ],
          };
          break;
        }
      }
      if (!isDuplicate) {
        temp.push(el);
      }
    }
  }
  return temp;
};

export const getMachine = (job) => {
  let machines = [];
  let cuProjects = job?.createInput?.cuProjects || [job]; // when called from scheduledJobs[createInput Object] then the former holds cuProjects, when called from unscheduled jobs[cuProjects object] then a singular object is recieved as param hence the latter assignment.
  if (cuProjects) {
    for (let project of cuProjects) {
      for (let machine of project?.machineAndOperator) {
        machines.push({
          name: machine?.machine?.machineName,
          id: machine?.machine?.machineId,
        });
      }
    }
    const duplicatesRemoved = machines.reduce((accumulator, current) => {
      if (!accumulator.find((item) => item.id === current.id)) {
        accumulator.push(current);
      }
      return accumulator;
    }, []);
    return {
      component: (
        <>
          <span className="flex">
            {duplicatesRemoved.length > 0
              ? `${duplicatesRemoved?.[0]?.name} (${duplicatesRemoved?.[0]?.id})`
              : `- ( - )`}
            {duplicatesRemoved?.length > 1 && (
              <HoverTooltip jobs={duplicatesRemoved} />
            )}
          </span>
        </>
      ),
      machines: duplicatesRemoved,
    };
  }
};

export const getWorker = (job) => {
  let workers = [];
  let cuProjects = job?.createInput?.cuProjects || [job]; // same reason as in getMachine()
  if (cuProjects) {
    for (let project of cuProjects) {
      for (let elem of project?.machineAndOperator) {
        for (let operator of elem?.operator) {
          workers.push({
            name: operator?.user?.name,
            id: operator?.user?.employeeId,
          });
        }
      }
    }
    const duplicatesRemoved = workers.reduce((accumulator, current) => {
      if (!accumulator.find((item) => item.id === current.id)) {
        accumulator.push(current);
      }
      return accumulator;
    }, []);
    return {
      component: (
        <>
          <span className="flex">
            {duplicatesRemoved.length > 0
              ? `${duplicatesRemoved?.[0]?.name} (${duplicatesRemoved?.[0]?.id})`
              : `- ( - )`}
            <HoverTooltip jobs={duplicatesRemoved} />
          </span>
        </>
      ),
      workers: duplicatesRemoved,
    };
  }
};

export const getTotalMachines = (scheduled, unscheduled) => {
  let totalMachines = [];
  if (scheduled) {
    for (let elem of scheduled) {
      let num = getMachine(elem);
      totalMachines = [...totalMachines, ...num.machines];
    }
  }
  if (unscheduled) {
    for (let elem of unscheduled) {
      let num = getMachine(elem);
      totalMachines = [...totalMachines, ...num.machines];
    }
  }
  const duplicatesRemoved = totalMachines.reduce((accumulator, current) => {
    if (!accumulator.find((item) => item.id === current.id)) {
      accumulator.push(current);
    }
    return accumulator;
  }, []);
  return duplicatesRemoved.length;
};

export const getTotalWorkers = (scheduled, unscheduled) => {
  let totalWorkers = [];
  if (scheduled) {
    for (let elem of scheduled) {
      let num = getWorker(elem);
      totalWorkers = [...totalWorkers, ...num.workers];
    }
  }
  if (unscheduled) {
    for (let elem of unscheduled) {
      let num = getWorker(elem);
      totalWorkers = [...totalWorkers, ...num.workers];
    }
  }
  const duplicatesRemoved = totalWorkers.reduce((accumulator, current) => {
    if (!accumulator.find((item) => item.id === current.id)) {
      accumulator.push(current);
    }
    return accumulator;
  }, []);
  return duplicatesRemoved.length;
};
