import { Button, DatePicker, Input, Modal } from 'antd';
import dayjs from 'dayjs'; // For formatting dates
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Select from '../global/components/Select';
import Table from '../global/components/Table';

const TableConfigModal = ({
  openModal,
  setOpenModal,
  tableConfig,
  changeHandler,
  column,
  rowData,
}) => {
  const initializeTableData = () => {
    const tempRowData = rowData?.[column?.name]?.value;
    if (tempRowData && tempRowData.length > 0) {
      return tempRowData;
    }

    return Array.from({ length: tableConfig?.rows || 0 }, () =>
      tableConfig?.columns?.reduce((acc, column) => {
        acc[column.name] = column.value === 'date' ? null : '';
        return acc;
      }, {})
    );
  };

  const [tableData, setTableData] = useState(initializeTableData);

  // Update tableData when rowData changes
  useEffect(() => {
    if (openModal) {
      setTableData(initializeTableData());
    }
  }, [rowData, openModal]); // eslint-disable-line

  const handleCellChange = (rowIndex, columnName, value) => {
    setTableData((prevData) =>
      prevData.map((row, index) =>
        index === rowIndex ? { ...row, [columnName]: value } : row
      )
    );
  };

  const handleAddRow = () => {
    const newRow = tableConfig?.columns?.reduce((acc, column) => {
      acc[column.name] = column.value === 'date' ? null : ''; // Initialize based on column type
      return acc;
    }, {});
    setTableData([...tableData, newRow]); // Append the new row
  };

  const handleDeleteRow = (rowIndex) => {
    const updatedData = tableData.filter((_, index) => index !== rowIndex);
    setTableData(updatedData);
  };

  const handleSubmit = () => {
    const isFirstFieldEmpty = tableData?.some((data) => {
      const firstKey = Object.keys(data)[0];
      return !(data?.[firstKey]?.value || data[firstKey])?.trim();
    });

    if (isFirstFieldEmpty) {
      toast.error('First Field is Empty, Please Fill or remove the row');
      return;
    }
    changeHandler(column, tableData, false); // Pass updatedRowData to changeHandler
    setOpenModal(false);
  };

  return (
    <div>
      <Modal
        title={'Set Table Configurations'}
        width={1200}
        open={openModal}
        onCancel={() => setOpenModal(false)}
        okText={'Submit'}
        footer={[
          <Button key="add-row" onClick={handleAddRow} type="primary">
            Add Row
          </Button>,
          <Button key="submit" onClick={handleSubmit}>
            Submit
          </Button>,
        ]}
        centered
        styles={{
          body: {
            maxHeight: `calc(100vh - 150px)`,
            overflowY: 'auto',
          },
        }}
      >
        <div className="mb-4">
          <span className="font-bold">Total Rows: {tableData.length}</span>
        </div>
        <div>
          <Table className={'mb-44'}>
            <Table.Head>
              <Table.Row>
                {tableConfig?.columns?.map((column) => (
                  <Table.Th key={column?.name} className={'text-center'}>
                    {column?.name}
                  </Table.Th>
                ))}
                <Table.Th className={'text-center'}>Actions</Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {tableData?.map((row, rowIndex) => (
                <Table.Row key={rowIndex}>
                  {tableConfig?.columns?.map((column, idx) => (
                    <Table.Td key={idx} style={{ flex: 1, padding: '0.5rem' }}>
                      {column.value === 'text' && (
                        <Input
                          type="text"
                          className="min-w-[10rem]"
                          placeholder="Enter text"
                          value={row[column.name]}
                          onChange={(e) =>
                            handleCellChange(
                              rowIndex,
                              column.name,
                              e.target.value
                            )
                          }
                        />
                      )}
                      {column.value === 'number' && (
                        <Input
                          type="number"
                          className="min-w-[10rem]"
                          placeholder="Enter number"
                          value={row[column.name]}
                          onChange={(e) =>
                            handleCellChange(
                              rowIndex,
                              column.name,
                              e.target.value
                            )
                          }
                        />
                      )}
                      {column.value === 'date' && (
                        <DatePicker
                          className="min-w-[10rem]"
                          placeholder="Select date"
                          value={
                            row[column.name] ? dayjs(row[column.name]) : null
                          }
                          onChange={(date) =>
                            handleCellChange(
                              rowIndex,
                              column.name,
                              date ? date.format('YYYY-MM-DD') : null
                            )
                          }
                          style={{ width: '100%' }}
                        />
                      )}
                      {column.value === 'dropdown' && (
                        <Select
                          className="min-w-[10rem]"
                          placeholder="Select"
                          value={row?.[column?.name]}
                          onChange={(e) =>
                            handleCellChange(
                              rowIndex,
                              column.name,
                              e.target.value
                            )
                          }
                          options={column?.options?.map((option) => ({
                            label: option,
                            value: option,
                          }))}
                        />
                      )}
                      {column.value === 'department' && (
                        <Select
                          className="min-w-[10rem]"
                          placeholder="Select"
                          value={column?.options?.find(
                            (option) =>
                              option?.value === row?.[column?.name]?.value
                          )}
                          onChange={(e) =>
                            handleCellChange(
                              rowIndex,
                              column.name,
                              e.target.value
                            )
                          }
                          options={column?.options?.map((option) => ({
                            label: option?.label,
                            value: option,
                          }))}
                        />
                      )}
                    </Table.Td>
                  ))}
                  <Table.Td
                    style={{
                      flex: 0.5,
                      textAlign: 'center',
                      padding: '0.5rem',
                    }}
                  >
                    <Button
                      type="text"
                      danger
                      onClick={() => handleDeleteRow(rowIndex)}
                    >
                      Delete
                    </Button>
                  </Table.Td>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
      </Modal>
    </div>
  );
};

export default TableConfigModal;
