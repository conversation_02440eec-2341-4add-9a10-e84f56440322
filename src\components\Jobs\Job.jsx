import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { Disclosure } from '@headlessui/react';
import { Switch, Checkbox } from 'antd';
import { ChevronRight } from 'lucide-react';
import HoverButton from '../Ui/HoverButton';
import {
  Fragment,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { IoChevronDownSharp, IoChevronUpSharp } from 'react-icons/io5';
import * as XLSX from 'xlsx';
import { ReactComponent as Export } from '../../assets/svgs/export.svg';
import { ReactComponent as Import } from '../../assets/svgs/import.svg';
import {
  convertToJson,
  generateGoalsData,
  generateGoalsTable,
} from '../../helperFunction';
import { useGetMastersByIdMutation } from '../../slices/documentApiSlice';
import { useLazyGetMediaByIdQuery } from '../../slices/mediaSlice';
import { useGetAllTemplatesQuery } from '../../slices/templateApiSlice';
import { Store } from '../../store/Store';
import Button from '../global/components/Button';
import { InfoTooltip } from '../global/components/InfoTooltip';
import Select from '../global/components/Select';
import AssemblyForms from './AssemblyForms/AssemblyForms';
import AssemblyItem from './AssemblyItem';
import AssignedAssemblyBom from './AssignedAssemblyBom';
import DetailsPage from './DetailsPage';
import CreateJobDetails from './JobDetails/CreateJobDetails';
import EditJobDetails from './JobDetails/EditJobDetails';
import Tabs from './JobDetails/Tabs';
import Media from './Media/Media';
import JobQcFrom from './QcForms/JobQcFrom';
import QcForms from './QcForms/QcForms';
import RMAddModal from './Rm/RMAddModal';
import RMTable from './Rm/RMTable';
import Rm from './Rm/Rm';
import ItemBomRmDetails from '../v3/WorkOrder/ItemBomRmDetails';
import TemplateMultiplier from './TemplateMultiplier';

const DisclosureButton = ({ open, title, children, tooltipParas = [] }) => {
  const [disableButton, setDisableButton] = useState(false);

  return (
    <Disclosure.Button
      disabled={disableButton}
      as={`div`}
      className={`w-full flex justify-between items-center px-8 py-2 text-black font-bold bg-slate-200 cursor-pointer ${open ? '' : 'border-b border-gray-300'}`}
    >
      <div className="flex items-center gap-x-3">
        <p className="text-lg">{title}</p>
        {tooltipParas?.length ? (
          <InfoTooltip
            id={title}
            position="right"
            className="whitespace-pre-line"
          >
            {tooltipParas}
          </InfoTooltip>
        ) : null}
      </div>

      <div className="flex items-center gap-x-5">
        <div
          className="flex items-center gap-x-5"
          onMouseOver={() => setDisableButton(true)}
          onMouseLeave={() => setDisableButton(false)}
        >
          {children}
        </div>
        {open ? <IoChevronUpSharp /> : <IoChevronDownSharp />}
      </div>
    </Disclosure.Button>
  );
};
const DisclosurePanel = ({ children }) => {
  return <Disclosure.Panel as={`div`}>{children}</Disclosure.Panel>;
};

const Job = ({
  selectedtemplateId,
  createTemplateValue,
  setIsTemplateStart,
  isTemplateStart,
  inputScreens,
  selectInputs,
  inputScreen,
  setSelectInputs,
  productionFlows,
  selectProductionFlows,
  setSelectProductionFlows,
  productionFlow,
  pos,
  selectedPoData,
  isLoadingPos,
  selectedPo,
  setselectedPo,
  formData,
  setFormData,
  goalsData,
  setGoalsData,
  goalsTables,
  setGoalsTables,
  multiProcessTablesData,
  setMultiProcessTablesData,
  processGoals,
  linkFieldData,
  setLinkFieldData,
  imageURLs,
  setImageURLs,
  modalData,
  setModalData,
  allQCData,
  setAllQCData,
  allAssemblyData,
  setAllAssemblyData,
  onSubmit,
  isLoading = false,
  workOrderId,
  setWorkOrderId,
  allQCForms,
  allAssemblyForms,
  setAssemblyItem,
  jobDetails,
  setRemovedMedia,
  projectStatus,
  assemblyItem,
  assemblyItemType,
  setAssemblyItemType,
  isEdit = false,
  jobPlanningDetails,
  setJobPlanningDetails,
  createTemplate,
  rmTableData,
  setRmTableData,
  templateName,
  setTemplateName,
  setTemplateDetails,
  templateDetails,
  setAssemblyItemToUpdate,
  bomAndAssemblyItem,
  setBomAndAssemblyItem,
  addJobDetails,
  setAddJobDetails,
  jobQCData,
  setJobQCData,
  setModelIdData,
  setJobIdData,
  setBatchIdData,
  additionalIdData,
  setAdditionalIdData,
}) => {
  const [LinkFieldOptions, setLinkFieldOptions] = useState([]);
  const [qr, setqr] = useState('');
  const [activeTab, setActiveTab] = useState('bom');
  const [isRMModalOpen, setIsRMModalOpen] = useState(false);
  const [SelectedBOM, setSelectedBOM] = useState('');
  const [showInputScreen, setShowInputScreen] = useState(false);
  const [openMultiplierModal, setOpenMultiplierModal] = useState(false);

  const { data: allTemplates = [] } = useGetAllTemplatesQuery();

  const [getMastersById, { data: masters = [] }] = useGetMastersByIdMutation();

  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const [getMediaById] = useLazyGetMediaByIdQuery();

  useEffect(() => {
    setLinkFieldOptions([]);
    if (inputScreen) {
      let masterIds = [];
      inputScreen?.data?.forEach((tab) => {
        tab?.data?.forEach((sheet) => {
          masterIds?.push(sheet?.masterId);
          sheet?.sheetData?.data?.forEach((item) => {
            if (
              +item['Horizontal Repetition'] === 1 &&
              +item['Vertical Repetition'] === 1 &&
              (item?.Type === 'NUMERIC' || item?.Type === 'NUMDROPDOWN')
            ) {
              setLinkFieldOptions((prev) => [
                ...prev,
                { name: item.Parameter, sheetName: sheet?.sheetData?.name },
              ]);
            }
          });
        });
      });

      if (masterIds?.length > 0) {
        getMastersById({
          data: {
            ids: [...new Set(masterIds)],
          },
        });
      }
    }
  }, [inputScreen, getMastersById]);

  const importRef = useRef(null);
  const handleLinkFieldFormValChange = (e) => {
    const { name: lName, value } = e.target;

    const linkEntries = Object.entries(linkFieldData || {});
    const temp = linkEntries?.find((en) => en.includes(lName));

    const name = temp?.[0];

    if (name === 'Batch Size' && projectStatus !== 'notStarted') return;

    if (!name) return;

    const tempGoalsData = generateGoalsData(
      goalsData,
      name,
      value,
      jobPlanningDetails?.isSingleBatch
    );

    setGoalsData(tempGoalsData);

    const tempGoalsTables = generateGoalsTable(
      tempGoalsData,
      productionFlow,
      defaultParam,
      multiProcessTablesData,
      goalsTables
    );

    if (isEdit) {
      setGoalsTables((prev) =>
        prev.map((i, idx) => ({ ...i, ...(tempGoalsTables?.[idx] || {}) }))
      );
    } else {
      setGoalsTables(tempGoalsTables);
    }
  };

  const exportXlsx = () => {
    if (inputScreen) {
      const wb = XLSX.utils?.book_new();

      inputScreen?.data?.forEach((tab) => {
        tab?.data?.forEach((sheet) => {
          let types = [];
          let params = [];

          sheet?.sheetData?.data?.forEach((item) => {
            if (
              +item?.['Horizontal Repetition'] > 1 &&
              item?.Type !== 'CHECKBOX'
            ) {
              const temp = item?.Type?.split('; ');
              item?.Title?.split('; ')?.forEach((title, tIdx) => {
                types.push(temp?.[tIdx]);
                params.push(`${item.Parameter}|${title}`);
              });
            } else {
              types.push(item.Type);
              params.push(item.Parameter);
            }
          });
          const ws_data = [types, params];

          const ws = XLSX.utils.aoa_to_sheet(ws_data);

          XLSX.utils.book_append_sheet(
            wb,
            ws,
            `${tab?.name}|${sheet?.sheetData?.name}`,
            true
          );
        });
      });

      if (wb?.SheetNames?.length > 0) {
        XLSX.writeFile(wb, `${Date.now()}.xlsx`);
      }
    }
  };

  const importXlsx = (e) => {
    e.preventDefault();
    const reader = new FileReader();
    reader.readAsBinaryString(e.target?.files?.[0]);
    reader.onload = async (evt) => {
      const bstr = evt.target.result;
      const wb = XLSX.read(bstr, { type: 'binary' });

      wb.SheetNames?.forEach((sheetName) => {
        const wsname = sheetName;
        const ws = wb.Sheets[wsname];

        const data = XLSX.utils.sheet_to_csv(ws, { header: 1 });

        const datas = convertToJson(data, 1);
        const wsSplit = wsname.split('|');

        const tab = inputScreen?.data?.find((tab) => tab?.name === wsSplit[0]);

        const sheet = tab?.data?.find(
          (sheet) => sheet?.sheetData?.name === wsSplit[1]
        );

        const sheetData = sheet?.sheetData;
        const inputData = sheet?.inputData;

        let tempObj = {};

        datas?.forEach((data, dIdx) => {
          for (const param in data) {
            const splitParam = param?.split('|');
            const paramInfo = sheetData?.data?.find(
              (item) => item.Parameter === splitParam[0]
            );
            if (splitParam?.length === 1) {
              if (dIdx === 0) {
                if (paramInfo?.Type === 'CHECKBOX') {
                  tempObj[param] = {
                    ...inputData?.[param],
                    value: data?.[param]?.split(';') || [],
                  };
                } else {
                  tempObj[param] = {
                    ...inputData?.[param],
                    value: data?.[param] || '',
                  };
                }
              }
            } else {
              tempObj[splitParam?.[0]] = {
                ...inputData?.[splitParam?.[0]],
                value: {
                  ...inputData?.[splitParam?.[0]]?.value,
                  ...(tempObj?.[splitParam?.[0]]?.value || {}),
                  [`${splitParam?.[1]}${dIdx}`]: data?.[param],
                },
              };
            }
          }
        });

        setFormData((prev) =>
          prev?.map((tab) => {
            if (tab?.name === wsSplit[0]) {
              return {
                ...tab,
                data: tab?.data?.map((sheet) => {
                  if (sheet?.sheetData?.name === wsSplit[1]) {
                    return { ...sheet, inputData: tempObj };
                  } else {
                    return sheet;
                  }
                }),
              };
            } else {
              return tab;
            }
          })
        );
      });
    };
  };

  const getMediaByID = useCallback(
    async (el) => {
      const response = await getMediaById({ id: el }).unwrap();
      return response;
    },
    [getMediaById]
  );

  useEffect(() => {
    (async () => {
      if (selectedPo && assemblyItem && !isTemplateStart) {
        const po = selectedPoData;

        if (po) {
          let mediaIds = [];

          if (po.type === 'Assembly' && bomAndAssemblyItem.assemblyItem) {
            mediaIds = bomAndAssemblyItem.assemblyItem.media || [];
          } else if (po.type === 'Inhouse') {
            // const { data } = await getPoById({ id: selectedPo });
            const inhouseData = po?.inhouse?.find(
              (el) => el?._id === assemblyItem
            );
            mediaIds = inhouseData?.media || [];
          } else {
            mediaIds =
              po?.items?.find((el) => el?._id === assemblyItem)?.media || [];
          }

          const mediaData = await Promise.all(mediaIds.map(getMediaByID));

          setImageURLs((prev) => ({
            ...prev,
            project: mediaData.map((item) => item?.media),
          }));
        }
      }
    })();

    // eslint-disable-next-line
  }, [
    assemblyItem,
    selectedPo,
    selectedPoData,
    bomAndAssemblyItem,
    // pos,
    // getPoById,
    getMediaByID,
    setImageURLs,
  ]);

  const handleSetDefault = () => {
    setGoalsTables((prev) =>
      prev?.map((table) => {
        const process = productionFlow?.processes?.find(
          (pro) => pro._id === table.flowId
        );

        const { isMultiProcess, bufferTime, setupTime } = process;

        if (isMultiProcess) {
          return {
            ...table,
            tableData: table?.tableData?.map((item) => ({
              ...item,
              subProcessData: item?.subProcessData?.map((sItem) => ({
                ...sItem,
                itemsPerHour: sItem?.status ? sItem?.itemsPerHour || 0 : 0,
                changeOverTime: sItem?.status ? sItem?.changeOverTime || 0 : 0,
                Speed: sItem?.status ? sItem?.itemsPerHour || 0 : 0,
                Time: sItem?.status ? sItem?.changeOverTime || 0 : 0,
                setupTime: sItem?.status
                  ? sItem?.setupTime || +setupTime || 0
                  : +setupTime || 0,
                bufferTime: sItem?.status
                  ? sItem?.bufferTime || +bufferTime || 0
                  : +bufferTime || 0,
              })),
            })),
          };
        } else {
          return {
            ...table,
            tableData: table?.tableData?.map((item) => ({
              ...item,
              itemsPerHour: item?.status ? item?.itemsPerHour || 0 : 0,
              changeOverTime: item?.status ? item?.changeOverTime || 0 : 0,
              Speed: item?.status ? item?.itemsPerHour || 0 : 0,
              Time: item?.status ? item?.changeOverTime || 0 : 0,
              setupTime: item?.status
                ? item?.setupTime || +setupTime || 0
                : +setupTime || 0,
              bufferTime: item?.status
                ? item?.bufferTime || +bufferTime || 0
                : +bufferTime || 0,
            })),
          };
        }
      })
    );
  };

  const selectedStyle = `px-4 py-2 text-sm font-bold bg-gray-100 outline-none w-auto`;
  const nonSelectedStyle =
    'px-4 py-2 text-sm font-bold outline-none bg-white w-auto';

  return (
    <>
      <RMAddModal
        isRMModalOpen={isRMModalOpen}
        setIsRMModalOpen={setIsRMModalOpen}
        setRmTableData={setRmTableData}
        rmTableData={rmTableData}
      />

      <div className="w-full">
        <input
          type="file"
          ref={importRef}
          className="fixed bottom-[200%]"
          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          onChange={importXlsx}
        />

        <form
          onSubmit={onSubmit}
          className="flex items-center text-gray-primary flex-wrap w-full font-semibold  text-sm justify-between mt-2"
        >
          {!createTemplateValue && (
            <section className="w-full border-2  bg-white rounded-lg">
              <div className="w-full px-5 py-3   rounded-lg bg-white  ">
                <section className="w-full ">
                  <div className=" grid grid-cols-2 mb-4">
                    <div className="flex w-64 mt-4">
                      <div className="flex items-center justify-center">
                        <label htmlFor="" className="ml-2 mr-3 text-nowrap ">
                          Start With Template:
                        </label>
                        <Switch
                          checkedChildren={<CheckOutlined />}
                          unCheckedChildren={<CloseOutlined />}
                          onChange={(checked) => {
                            localStorage.setItem(
                              'autoSave-isTemplateStart',
                              JSON.stringify(checked)
                            );
                            setIsTemplateStart(checked);
                          }}
                          checked={isTemplateStart}
                        />
                      </div>
                      {isTemplateStart && (
                        <div className="flex text-nowrap">
                          <label
                            htmlFor=""
                            className="ml-2 mr-3 flex justify-center items-center"
                          >
                            Select Template:
                          </label>
                          <Select
                            className={'!w-[240px] '}
                            options={[
                              ...allTemplates
                                ?.filter((el) => !el?.isForWorkOrder)
                                ?.map((el) => ({
                                  label: el?.name,
                                  value: el?._id,
                                })),
                            ]}
                            value={addJobDetails.template}
                            onChange={(e) => {
                              setAddJobDetails((prev) => ({
                                ...prev,
                                template: e.target.value,
                              }));
                            }}
                          />
                        </div>
                      )}
                    </div>

                    {isTemplateStart && (
                      <div className="flex gap-x-4 justify-end ">
                        {/* <Button
                          isLoading={isLoading}
                          type="submit"
                          size="sm"
                          color=""
                          className="bg-[#1BD57F] !px-4 !py-3"
                        >
                          Save & Proceed
                        </Button> */}

                        <HoverButton
                          isLoading={isLoading}
                          type="submit"
                          color=""
                          className="bg-[#1BD57F] text-white  border-[#27885b] px-5 !py-0 !text-sm font-normal  w-[170px] h-10 text-nowrap"
                        >
                          Save & Proceed
                        </HoverButton>
                      </div>
                    )}
                  </div>
                </section>

                {isTemplateStart && (
                  <div className="w-full grid grid-cols-2 h-[62vh] gap-2">
                    {/* 1 */}
                    <div className="">
                      <CreateJobDetails
                        goalsData={goalsData}
                        isTemplateStart={isTemplateStart}
                        pos={pos}
                        selectedPoData={selectedPoData}
                        isLoadingPos={isLoadingPos}
                        selectedPo={selectedPo}
                        setselectedPo={setselectedPo}
                        inputScreens={inputScreens}
                        selectInputs={selectInputs}
                        setSelectInputs={setSelectInputs}
                        productionFlows={productionFlows}
                        selectProductionFlows={selectProductionFlows}
                        setSelectProductionFlows={setSelectProductionFlows}
                        productionFlow={productionFlow}
                        setAssemblyItem={setAssemblyItem}
                        assemblyItem={assemblyItem}
                        setAssemblyItemType={setAssemblyItemType}
                        assemblyItemType={assemblyItemType}
                        workOrderId={workOrderId}
                        setWorkOrderId={setWorkOrderId}
                        setFormData={setFormData}
                        processGoals={processGoals}
                        setGoalsData={setGoalsData}
                        setGoalsTables={setGoalsTables}
                        setMultiProcessTablesData={setMultiProcessTablesData}
                        createTemplate={createTemplate}
                        templateName={templateName}
                        setTemplateName={setTemplateName}
                        SelectedBOM={SelectedBOM}
                        setSelectedBOM={setSelectedBOM}
                        // workOrderOptions={workOrderOptions}
                        setAssemblyItemToUpdate={setAssemblyItemToUpdate}
                        bomAndAssemblyItem={bomAndAssemblyItem}
                        setBomAndAssemblyItem={setBomAndAssemblyItem}
                        setModelIdData={setModelIdData}
                        setJobIdData={setJobIdData}
                        setAdditionalIdData={setAdditionalIdData}
                        inputScreen={inputScreen}
                      />
                    </div>
                    {/* 2 */}
                    <div className="  justify-start">
                      <DetailsPage
                        selectedtemplateId={selectedtemplateId}
                        selectProductionFlows={selectProductionFlows}
                        isTemplateStart={isTemplateStart}
                        productionFlow={productionFlow}
                        goalsData={goalsData}
                        setGoalsData={setGoalsData}
                        multiProcessTablesData={multiProcessTablesData}
                        setMultiProcessTablesData={setMultiProcessTablesData}
                        LinkFieldOptions={LinkFieldOptions}
                        linkFieldData={linkFieldData}
                        setLinkFieldData={setLinkFieldData}
                        goalsTables={goalsTables}
                        setGoalsTables={setGoalsTables}
                        workOrderId={workOrderId}
                        inputScreen={inputScreen}
                        projectStatus={projectStatus}
                        jobDetails={jobDetails}
                        isEdit={isEdit}
                        jobPlanningDetails={jobPlanningDetails}
                        setJobPlanningDetails={setJobPlanningDetails}
                        createTemplate={createTemplate}
                        setTemplateDetails={setTemplateDetails}
                        templateDetails={templateDetails}
                        selectedPo={selectedPo}
                        selectedPoData={selectedPoData}
                        SelectedBOM={SelectedBOM}
                        assemblyItem={assemblyItem}
                        setSelectedBOM={setSelectedBOM}
                        bomAndAssemblyItem={bomAndAssemblyItem}
                        openMultiplierModal={openMultiplierModal}
                        setOpenMultiplierModal={setOpenMultiplierModal}
                        setBatchIdData={setBatchIdData}
                        additionalIdData={additionalIdData}
                      />
                    </div>
                    {/* 3 */}
                    <div className=" ">
                      <p className="font-bold mb-2">QC Forms : </p>
                      <div className="h-[20vh] overflow-y-scroll">
                        <QcForms
                          processes={productionFlow?.processes || []}
                          allQCData={allQCData}
                          setAllQCData={setAllQCData}
                          allQCForms={allQCForms}
                          qr={qr}
                          setqr={setqr}
                          goalsTables={goalsTables}
                          goalsData={goalsData}
                        />
                      </div>
                    </div>
                    {/* 4 */}
                    <div>
                      <p className="font-bold mb-2">Assembly Forms : </p>
                      <div className="h-[20vh] overflow-y-scroll">
                        <AssemblyForms
                          allAssemblyForms={allAssemblyForms}
                          processes={productionFlow?.processes || []}
                          allAssemblyData={allAssemblyData}
                          setAllAssemblyData={setAllAssemblyData}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </section>
          )}
          {!isTemplateStart && (
            <section className="w-full border-2  bg-white rounded-lg ">
              <div
                className={`w-full px-5 py-3 flex ${createTemplateValue ? 'justify-between' : 'justify-end'} rounded-t-lg bg-white  border-b-2`}
              >
                {createTemplateValue && (
                  <div className="flex gap-x-2">
                    <Checkbox
                      checked={bomAndAssemblyItem?.isForWorkOrder || false}
                      onChange={(e) =>
                        setBomAndAssemblyItem((prev) => ({
                          ...prev,
                          isForWorkOrder: e.target.checked,
                        }))
                      }
                      label="Create Template"
                    />
                    <span className="text-sm font-semibold justify-center items-center flex ">
                      Used For Work Order
                    </span>
                  </div>
                )}
                <section className="flex gap-x-4 ">
                  {/* <Button
                    isLoading={isLoading}
                    type="submit"
                    size="sm"
                    color=""
                    className="bg-[#1BD57F] !px-4 !py-3"
                  >
                    Save & Proceed
                  </Button> */}

                  <HoverButton
                    isLoading={isLoading}
                    type="submit"
                    color=""
                    className="bg-[#1BD57F] text-white  border-[#27885b] w-[170px] text-center "
                  >
                    Save & Proceed
                  </HoverButton>
                </section>
              </div>

              <section className="h-[70vh] overflow-y-scroll overflow-x-hidden">
                {/* Job Details */}
                <Disclosure defaultOpen>
                  {({ open }) => (
                    <>
                      <DisclosureButton
                        open={open}
                        title={'Job Details'}
                        tooltipParas={[
                          'This table is utilized when a process consists of multiple subprocesses. It links with the Process Goals Batch table to enable detailed planning and management of subprocesses within the production process, ensuring comprehensive control and organization.',
                        ]}
                      >
                        {selectInputs && (
                          <>
                            <Button
                              onClick={() =>
                                setShowInputScreen((prev) => !prev)
                              }
                              disabled={!selectInputs}
                              size="sm"
                              className="!px-4"
                            >
                              {showInputScreen ? 'Hide' : 'Show'}&nbsp;Screen
                            </Button>
                            <Button
                              textColor=""
                              color=""
                              size="sm"
                              className="bg-white border text-gray-600 flex !px-4"
                              onClick={() => importRef?.current?.click()}
                              disabled={!selectInputs}
                            >
                              <Import className="mr-1" />
                              Import
                            </Button>
                            <Button
                              textColor=""
                              color=""
                              size="sm"
                              className="bg-white border text-gray-600 flex !px-4"
                              onClick={() => exportXlsx()}
                              disabled={!selectInputs}
                            >
                              <Export className="mr-1" />
                              Export
                            </Button>
                          </>
                        )}
                      </DisclosureButton>

                      <DisclosurePanel>
                        <div
                          className={`bg-white rounded-b px-8 py-3 grid responsive-grid-createjobs gap-4 border-b`}
                        >
                          {isEdit ? (
                            <EditJobDetails jobDetails={jobDetails} />
                          ) : (
                            <CreateJobDetails
                              isTemplateStart={isTemplateStart}
                              goalsData={goalsData}
                              pos={pos}
                              selectedPoData={selectedPoData}
                              isLoadingPos={isLoadingPos}
                              selectedPo={selectedPo}
                              setselectedPo={setselectedPo}
                              inputScreens={inputScreens}
                              selectInputs={selectInputs}
                              setSelectInputs={setSelectInputs}
                              productionFlows={productionFlows}
                              selectProductionFlows={selectProductionFlows}
                              setSelectProductionFlows={
                                setSelectProductionFlows
                              }
                              productionFlow={productionFlow}
                              setAssemblyItem={setAssemblyItem}
                              assemblyItem={assemblyItem}
                              setAssemblyItemType={setAssemblyItemType}
                              assemblyItemType={assemblyItemType}
                              workOrderId={workOrderId}
                              setWorkOrderId={setWorkOrderId}
                              setFormData={setFormData}
                              processGoals={processGoals}
                              setGoalsData={setGoalsData}
                              setGoalsTables={setGoalsTables}
                              setMultiProcessTablesData={
                                setMultiProcessTablesData
                              }
                              createTemplate={createTemplate}
                              templateName={templateName}
                              setTemplateName={setTemplateName}
                              SelectedBOM={SelectedBOM}
                              setSelectedBOM={setSelectedBOM}
                              // workOrderOptions={workOrderOptions}
                              setAssemblyItemToUpdate={setAssemblyItemToUpdate}
                              bomAndAssemblyItem={bomAndAssemblyItem}
                              setBomAndAssemblyItem={setBomAndAssemblyItem}
                              setModelIdData={setModelIdData}
                              setJobIdData={setJobIdData}
                              setAdditionalIdData={setAdditionalIdData}
                              inputScreen={inputScreen}
                            />
                          )}

                          {selectProductionFlows && (
                            <div className="w-full col-span-full flex items-center bg-white rounded-lg gap-2">
                              <p className="text-md font-bold text-gray-800 whitespace-nowrap">
                                Production Flow:
                              </p>
                              <section className="w-full flex items-center overflow-auto">
                                {productionFlow?.processes?.map((pro, pIdx) => (
                                  <Fragment key={pro._id}>
                                    <span className="px-3 bg-blue-50 text-blue-600 rounded-md font-medium">
                                      {pro.processName}
                                    </span>
                                    {pIdx !==
                                      productionFlow?.processes?.length - 1 && (
                                      <ChevronRight className="h-4 w-4" />
                                    )}
                                  </Fragment>
                                ))}
                              </section>
                            </div>
                          )}
                        </div>
                        {showInputScreen && (
                          <div className={`w-full`}>
                            {selectInputs ? (
                              <Tabs
                                formData={formData}
                                productionFlow={productionFlow}
                                setFormData={setFormData}
                                multiProcessTablesData={multiProcessTablesData}
                                goalsData={goalsData}
                                goalsTables={goalsTables}
                                setGoalsTables={setGoalsTables}
                                setMultiProcessTablesData={
                                  setMultiProcessTablesData
                                }
                                handleLinkFieldFormValChange={
                                  handleLinkFieldFormValChange
                                }
                                step={0}
                                masters={masters}
                                jobDetails={jobDetails}
                                isEdit={isEdit}
                              />
                            ) : (
                              <p className="w-full mt-5 text-center text-lg">
                                Please select an input screen
                              </p>
                            )}
                          </div>
                        )}
                      </DisclosurePanel>
                    </>
                  )}
                </Disclosure>

                {/* Order Details */}
                <Disclosure defaultOpen>
                  {({ open }) => (
                    <>
                      <DisclosureButton
                        open={open}
                        title={'Order Details'}
                        tooltipParas={[
                          'This page provides essential job parameters relevant for the production process, ensuring that users have access to the necessary details to streamline their production workflow.',
                          'This page empowers you to set specific process goals for each batch within your created job. These goals are instrumental in determining the overall performance and efficiency of your production process.',
                        ]}
                      >
                        {selectProductionFlows && (
                          <>
                            <Button
                              onClick={() => setOpenMultiplierModal(true)}
                              size="sm"
                              className="!px-4"
                            >
                              Manage Multipliers
                            </Button>
                            <div className="relative flex items-center">
                              <Button
                                onClick={handleSetDefault}
                                size="sm"
                                className="!px-6"
                              >
                                Set Default Values
                              </Button>
                              <InfoTooltip
                                className="absolute right-1"
                                id="setToDefaultValues"
                                position="right"
                              >
                                Simplify form filling with default values for
                                process goals. By selecting the start date of
                                batches, all other relevant details will be
                                automatically populated
                              </InfoTooltip>
                            </div>
                          </>
                        )}
                        {createTemplate && (
                          <Button
                            type="button"
                            onClick={() => setIsRMModalOpen((prev) => !prev)}
                          >
                            + Add
                          </Button>
                        )}
                      </DisclosureButton>
                      <DisclosurePanel>
                        {createTemplate ? (
                          <TemplateMultiplier
                            productionFlow={productionFlow}
                            templateDetails={templateDetails}
                            setTemplateDetails={setTemplateDetails}
                          />
                        ) : (
                          <DetailsPage
                            isTemplateStart={isTemplateStart}
                            productionFlow={productionFlow}
                            goalsData={goalsData}
                            setGoalsData={setGoalsData}
                            multiProcessTablesData={multiProcessTablesData}
                            setMultiProcessTablesData={
                              setMultiProcessTablesData
                            }
                            LinkFieldOptions={LinkFieldOptions}
                            linkFieldData={linkFieldData}
                            setLinkFieldData={setLinkFieldData}
                            goalsTables={goalsTables}
                            setGoalsTables={setGoalsTables}
                            workOrderId={workOrderId}
                            inputScreen={inputScreen}
                            projectStatus={projectStatus}
                            jobDetails={jobDetails}
                            isEdit={isEdit}
                            jobPlanningDetails={jobPlanningDetails}
                            setJobPlanningDetails={setJobPlanningDetails}
                            createTemplate={createTemplate}
                            setTemplateDetails={setTemplateDetails}
                            templateDetails={templateDetails}
                            selectedPo={selectedPo}
                            selectedPoData={selectedPoData}
                            SelectedBOM={SelectedBOM}
                            setSelectedBOM={setSelectedBOM}
                            bomAndAssemblyItem={bomAndAssemblyItem}
                            openMultiplierModal={openMultiplierModal}
                            setOpenMultiplierModal={setOpenMultiplierModal}
                            setBatchIdData={setBatchIdData}
                            additionalIdData={additionalIdData}
                          />
                        )}
                      </DisclosurePanel>
                    </>
                  )}
                </Disclosure>

                {/* Media */}
                <Disclosure>
                  {({ open }) => (
                    <>
                      <DisclosureButton
                        open={open}
                        title={'Media'}
                        tooltipParas={[
                          'Media added under a job will be displayed across all Process Monitors, offering comprehensive visibility and documentation. However, media added under a specific process will only be visible to the designated Process Monitor, ensuring focused and relevant information sharing.',
                        ]}
                      />
                      <DisclosurePanel>
                        <>
                          <Media
                            imageURLs={imageURLs}
                            setImageURLs={setImageURLs}
                            processes={productionFlow?.processes || []}
                            setRemovedMedia={setRemovedMedia}
                            isEdit={isEdit}
                          />
                        </>
                      </DisclosurePanel>
                    </>
                  )}
                </Disclosure>

                {/* RM / BOM */}
                <Disclosure>
                  {({ open }) => (
                    <>
                      <DisclosureButton open={open} title="RM/BOM" />
                      <DisclosurePanel>
                        <div className="flex flex-col">
                          {createTemplate ? (
                            <>
                              <RMTable rmTableData={rmTableData} />
                            </>
                          ) : (
                            <div>
                              <div className="flex gap-5 mt-2 ml-3">
                                <Button
                                  onClick={() => setActiveTab('rm')}
                                  textColor="black"
                                  color="white"
                                  className={`${
                                    activeTab === 'rm'
                                      ? selectedStyle
                                      : nonSelectedStyle
                                  }`}
                                >
                                  Raw Material
                                </Button>
                                <Button
                                  onClick={() => setActiveTab('bom')}
                                  textColor="black"
                                  color="white"
                                  className={`${
                                    activeTab === 'bom'
                                      ? selectedStyle
                                      : nonSelectedStyle
                                  }`}
                                >
                                  Bill of Material
                                </Button>
                                <Button
                                  onClick={() => setActiveTab('assemblyItem')}
                                  textColor="black"
                                  color="white"
                                  className={`${
                                    activeTab === 'assemblyItem'
                                      ? selectedStyle
                                      : nonSelectedStyle
                                  }`}
                                >
                                  RM
                                </Button>
                              </div>
                              <div>
                                {activeTab === 'rm' && (
                                  <Rm
                                    modalData={modalData}
                                    setModalData={setModalData}
                                  />
                                )}
                                {activeTab === 'bom' && (
                                  <>
                                    {selectedPoData?.items !== undefined ? (
                                      <div>
                                        <ItemBomRmDetails
                                          workOrder={selectedPoData}
                                        />
                                      </div>
                                    ) : (
                                      <AssignedAssemblyBom
                                        po={selectedPoData}
                                      />
                                    )}
                                  </>
                                )}
                                {activeTab === 'assemblyItem' && (
                                  <AssemblyItem
                                    bomAndAssemblyItem={bomAndAssemblyItem}
                                  />
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </DisclosurePanel>
                    </>
                  )}
                </Disclosure>

                {/* Qc Forms */}
                <Disclosure>
                  {({ open }) => (
                    <>
                      <DisclosureButton open={open} title={'QC Forms'} />
                      <DisclosurePanel>
                        <QcForms
                          processes={productionFlow?.processes || []}
                          allQCData={allQCData}
                          setAllQCData={setAllQCData}
                          allQCForms={allQCForms}
                          qr={qr}
                          setqr={setqr}
                          goalsTables={goalsTables}
                          goalsData={goalsData}
                        />
                      </DisclosurePanel>
                    </>
                  )}
                </Disclosure>

                {/* Process Selection */}
                <Disclosure>
                  {({ open }) => (
                    <>
                      <DisclosureButton
                        open={open}
                        title={'Process Selection'}
                        tooltipParas={[
                          'This feature enables users to select and determine which parameters will be visible to specific shop floor process monitors, offering customization and control over data display for enhanced monitoring and efficiency.',
                        ]}
                      />
                      <DisclosurePanel>
                        <div className={`w-full`}>
                          {selectInputs ? (
                            <Tabs
                              formData={formData}
                              productionFlow={productionFlow}
                              setFormData={setFormData}
                              multiProcessTablesData={multiProcessTablesData}
                              goalsData={goalsData}
                              goalsTables={goalsTables}
                              setGoalsTables={setGoalsTables}
                              setMultiProcessTablesData={
                                setMultiProcessTablesData
                              }
                              handleLinkFieldFormValChange={
                                handleLinkFieldFormValChange
                              }
                              step={4}
                              masters={masters}
                              jobDetails={jobDetails}
                              isEdit={isEdit}
                            />
                          ) : (
                            <p className="w-full mt-5 text-center text-lg">
                              Please select an input screen
                            </p>
                          )}
                        </div>
                      </DisclosurePanel>
                    </>
                  )}
                </Disclosure>

                {/* Assembly Forms */}
                <Disclosure>
                  {({ open }) => (
                    <>
                      <DisclosureButton open={open} title={'Assembly Forms'} />
                      <DisclosurePanel>
                        <AssemblyForms
                          allAssemblyForms={allAssemblyForms}
                          processes={productionFlow?.processes || []}
                          allAssemblyData={allAssemblyData}
                          setAllAssemblyData={setAllAssemblyData}
                        />
                      </DisclosurePanel>
                    </>
                  )}
                </Disclosure>

                {/* Qc Forms */}
                <Disclosure>
                  {({ open }) => (
                    <>
                      <DisclosureButton open={open} title={'Job QC Forms'} />
                      <DisclosurePanel>
                        <JobQcFrom
                          jobQCData={jobQCData}
                          setJobQCData={setJobQCData}
                          allQCForms={allQCForms}
                        />
                      </DisclosurePanel>
                    </>
                  )}
                </Disclosure>
              </section>
            </section>
          )}
        </form>
      </div>
    </>
  );
};

export default Job;
