import {
  ClockCircleOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EyeOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  MessageOutlined,
  PaperClipOutlined,
  PlusOutlined,
  ShareAltOutlined,
  ShoppingCartOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Button,
  Image,
  Input,
  Modal,
  Table,
  Tabs,
  Tag,
  Timeline,
  Typography,
  Upload,
  message,
} from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { FormatDate } from '../../helperFunction';
import {
  useAddTimelineRemarkMutation,
  useDeleteAttachmentMutation,
  useGetCustomerOrderByIdQuery,
  useUploadAttachmentMutation,
} from '../../slices/customerSalesOrderApiSlice';
import { useGetMediaByIdArrayMutation } from '../../slices/mediaSlice';
import Spinner from '../global/components/Spinner';
import PreviewImgPdfFullscreen from '../salesOrder/PreviewImgPdfFullscreen';
import AttachmentShareModal from './AttachmentShareModal';

const { Title, Text } = Typography;
const { Dragger } = Upload;

const OrderDetailsSidebar = ({ id }) => {
  const { data: order = {} } = useGetCustomerOrderByIdQuery(id);
  const user = JSON.parse(localStorage.getItem('user'))?.user;
  const userRole = user?.role;

  const [previewImage, setPreviewImage] = useState(null);
  const [uploadFiles, setUploadFiles] = useState([]);
  const [uploadAttachment] = useUploadAttachmentMutation();
  const [deleteAttachment] = useDeleteAttachmentMutation();
  const [addTimelineRemark] = useAddTimelineRemarkMutation();
  const [showPreview, setShowPreview] = useState(false);
  const [mediaToPreview, setMediaToPreview] = useState(null);
  const [allFormAttachments, setAllFormAttachments] = useState([]);
  const [openFormAttachmentModal, setOpenFormAttachmentModal] = useState(false);
  const [openShareModal, setOpenShareModal] = useState(false);
  const [selectedAttachments, setSelectedAttachments] = useState({});
  const [showRemarkModal, setShowRemarkModal] = useState(false);
  const [remarkText, setRemarkText] = useState('');

  const handleFormAttachmentsPreview = () => {
    setOpenFormAttachmentModal(true);
  };

  const handleAddRemark = async () => {
    if (!remarkText.trim()) {
      toast.error('Please enter a remark');
      return;
    }

    try {
      await addTimelineRemark({
        orderId: order._id,
        remark: remarkText.trim(),
      }).unwrap();
      toast.success('Remark added successfully');
      setRemarkText('');
      setShowRemarkModal(false);
    } catch (error) {
      toast.error('Failed to add remark');
    }
  };

  const productColumns = [
    {
      title: 'PRODUCT NAME',
      key: 'name',
      render: (_, record) => (
        <span>{record?.item?.name || record?.manualEntry || '-'}</span>
      ),
    },
    {
      title: 'QUANTITY',
      dataIndex: 'orderQuantity',
      key: 'quantity',
    },
    {
      title: 'UOM',
      key: 'uom',
      render: (_, record) => (
        <span>{(record?.item && record.item.uom) || record?.uom || '-'}</span>
      ),
    },
  ];

  const renderGeneralAndFormInfo = () => (
    <div className="space-y-6">
      <div>
        <div className="flex items-center gap-2 mb-4">
          <InfoCircleOutlined className="text-blue-500" />
          <Title level={5} className="!mb-0">
            General Information
          </Title>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg space-y-3">
          <div className="flex justify-between items-center">
            <Text className="text-gray-600">Order Date</Text>
            <Text>{FormatDate(order?.createdAt)}</Text>
          </div>
          <div className="flex justify-between items-center">
            <Text className="text-gray-600">Customer Name</Text>
            <Text>
              {order.customerId?.name || order?.customerId?.company_name || '-'}
            </Text>
          </div>
          <div className="flex justify-between items-center">
            <Text className="text-gray-600">Status</Text>
            <Tag color={order?.status === 'completed' ? 'green' : 'blue'}>
              {order?.status}
            </Tag>
          </div>
        </div>
      </div>

      {order?.formData && Object.keys(order?.formData).length > 0 && (
        <div>
          <div className="flex items-center gap-2 mb-4">
            <FileTextOutlined className="text-green-500" />
            <Title level={5} className="!mb-0">
              Form Data
            </Title>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg space-y-3">
            {Object.entries(order?.formData).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center">
                <Text className="text-gray-600">{key}</Text>
                {Array.isArray(value) ? (
                  <Button
                    type="link"
                    onClick={() => handleFormAttachmentsPreview()}
                  >
                    View
                  </Button>
                ) : (
                  <Text>{value}</Text>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <ShoppingCartOutlined className="text-purple-500" />
          <Title level={5} className="!mb-0">
            Products
          </Title>
        </div>
        <Table
          columns={productColumns}
          dataSource={order.products}
          rowKey="_id"
          pagination={false}
          size="small"
          className="border rounded-lg"
        />
      </div>
    </div>
  );

  const renderTimeline = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <ClockCircleOutlined className="text-orange-500" />
          <Title level={5} className="!mb-0">
            Timeline
          </Title>
        </div>
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={() => setShowRemarkModal(true)}
          className="bg-blue-500 hover:bg-blue-600"
        >
          Add Remark
        </Button>
      </div>
      <div className="bg-gray-50 p-4 rounded-lg">
        {order && order?.timeline && order?.timeline?.length > 0 ? (
          <Timeline>
            {order.timeline.map((event) => (
              <Timeline.Item
                key={event._id}
                dot={
                  event.activity === 'Remarks added' ? (
                    <MessageOutlined className="text-blue-500" />
                  ) : (
                    <ClockCircleOutlined className="text-orange-500" />
                  )
                }
              >
                <div className="space-y-1">
                  <Text strong>{event.activity}</Text>
                  <Text className="block text-gray-500 text-sm">
                    {FormatDate(event.timestamp)}
                  </Text>
                  {event.details && event.details.newStatus && (
                    <Text className="block">
                      New Status: {event.details.newStatus}
                    </Text>
                  )}
                  <div className="bg-white p-3 rounded border-l-4 border-blue-400 mt-2">
                    {event.details &&
                      (event.details.remark || event.details?.message) && (
                        <Text className="block">
                          {event.details.remark || event.details?.message}
                        </Text>
                      )}
                    {event.addedBy && (
                      <div className="flex items-center gap-1 mt-1">
                        <UserOutlined className="text-gray-400 text-xs" />
                        <Text className="text-xs text-gray-500">
                          Added by: {event.addedBy || 'Unknown'}
                        </Text>
                      </div>
                    )}
                  </div>
                  {event.details?.attachmentIds?.length > 0 &&
                    event.details?.attachmentIds?.map((media) => (
                      <Button
                        key={media._id}
                        type="default"
                        className="flex items-center gap-2 mt-2 border border-cyan-200 bg-blue-50 hover:bg-blue-100 rounded transition"
                        onClick={() => handlePreview(media)}
                        icon={<PaperClipOutlined className="text-cyan-500" />}
                        tabIndex={0}
                        role="button"
                        size="small"
                        title="Click to preview"
                      >
                        <span className="text-[10px] text-cyan-700 font-semibold underline">
                          {media.name}
                        </span>
                        <span className="ml-1 px-1 py-0.5 rounded bg-blue-100 text-blue-600 text-[8px] font-medium uppercase">
                          {media.type?.startsWith('image/')
                            ? 'Image'
                            : media.type === 'application/pdf'
                              ? 'PDF'
                              : 'File'}
                        </span>
                        <EyeOutlined className="text-blue-400 ml-2" />
                      </Button>
                    ))}
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        ) : (
          <Text className="text-gray-500">No timeline events available</Text>
        )}
      </div>
    </div>
  );

  const handlePreview = (file) => {
    setMediaToPreview(file);
    setShowPreview(true);
  };

  const handleUpload = async (file) => {
    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const data = {
          name: file.newName || file.name,
          type: file.type || file?.originFileObj?.type,
          data: e.target.result,
        };

        await uploadAttachment({
          orderId: order._id,
          formData: data,
        }).unwrap();

        setUploadFiles((prev) => prev.filter((f) => f.uid !== file.uid));
      };
      reader.readAsDataURL(file.originFileObj);
    } catch (error) {
      toast.error('Failed to upload file');
    }
  };

  const handleDelete = async (attachmentId) => {
    try {
      await deleteAttachment({
        orderId: order._id,
        attachmentId,
      }).unwrap();
      toast.success('File deleted successfully');
    } catch (error) {
      message.error('Failed to delete file');
    }
  };

  const customRequest = ({ file, onSuccess }) => {
    // Store the original file object along with other properties
    setUploadFiles((prev) => [
      ...prev,
      {
        ...file,
        newName: file.name,
        originFileObj: file,
      },
    ]);
    onSuccess();
  };

  const renderAttachments = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <PaperClipOutlined className="text-cyan-500" />
          <Title level={5} className="!mb-0">
            Attachments
          </Title>
        </div>
      </div>

      <Dragger
        customRequest={customRequest}
        multiple
        accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif"
        showUploadList={false}
      >
        <div className="p-6 text-center">
          <PaperClipOutlined className="text-3xl text-cyan-500 mb-3" />
          <p className="text-gray-600 font-medium">
            Click or drag files here to upload
          </p>
          <p className="text-gray-400 text-sm">
            Support for PDF, Word, Excel, and image files
          </p>
        </div>
      </Dragger>

      {/* Pending Uploads */}
      {uploadFiles.length > 0 && (
        <div className="space-y-3">
          <Text strong>Pending Uploads</Text>
          {uploadFiles?.map((file) => (
            <div
              key={file.uid}
              className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg"
            >
              <div className="flex-grow">
                <Input
                  value={file.newName}
                  onChange={(e) => {
                    setUploadFiles((prev) =>
                      prev.map((f) =>
                        f.uid === file.uid
                          ? { ...f, newName: e.target.value }
                          : f
                      )
                    );
                  }}
                />
                <Text className="text-xs text-gray-500">{file.type}</Text>
              </div>
              <Button type="primary" onClick={() => handleUpload(file)}>
                Upload
              </Button>
              <Button
                type="text"
                danger
                onClick={() =>
                  setUploadFiles((prev) =>
                    prev.filter((f) => f.uid !== file.uid)
                  )
                }
              >
                <DeleteOutlined />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Existing Attachments */}
      {order?.attachments?.length > 0 && (
        <div className="space-y-3">
          <Text strong>Uploaded Files</Text>
          {order?.attachments?.map((attachment, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center gap-3">
                <Text
                  strong
                  className="block truncate"
                  title={attachment?.name}
                >
                  {attachment?.name}
                </Text>
                <Text type="secondary" className="text-xs">
                  {attachment?.type}
                </Text>
              </div>
              <div className="flex">
                <Button
                  type="link"
                  onClick={() => {
                    setSelectedAttachments(attachment);
                    setOpenShareModal(true);
                  }}
                  icon={<ShareAltOutlined />}
                  className={
                    userRole === 'admin' || userRole === 'superuser'
                      ? ''
                      : 'hidden'
                  }
                />
                {(attachment?.type?.startsWith('image/') ||
                  attachment?.type?.startsWith('application/pdf') ||
                  attachment?.type === 'application/pdf') && (
                  <Button
                    type="link"
                    onClick={() => handlePreview(attachment)}
                    icon={<EyeOutlined />}
                  />
                )}
                <Button
                  type="link"
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = attachment?.data;
                    link.download = attachment?.name;
                    link.click();
                  }}
                  icon={<DownloadOutlined />}
                />
                <Button
                  type="text"
                  danger
                  onClick={() => handleDelete(attachment._id)}
                  className={
                    userRole === 'admin' || userRole === 'superuser'
                      ? ''
                      : 'hidden'
                  }
                  icon={<DeleteOutlined />}
                />
              </div>
            </div>
          ))}
        </div>
      )}
      {/* Image and PDF Preview */}
      {previewImage && (
        <div className="h-[90%] w-full" onClick={() => setPreviewImage(null)}>
          {previewImage.type.startsWith('image/') ? (
            <Image
              src={previewImage.data}
              alt={previewImage.name}
              style={{ maxWidth: '100%', maxHeight: '90vh' }}
              preview={false}
            />
          ) : (
            <iframe
              src={previewImage.data}
              title={previewImage.name}
              width="100%"
              height="100%"
              style={{ border: 'none', height: '70vh' }}
            />
          )}
        </div>
      )}
    </div>
  );

  const items = [
    {
      key: '1',
      label: 'General',
      children: renderGeneralAndFormInfo(),
    },
    {
      key: '3',
      label: 'Timeline',
      children: renderTimeline(),
    },
    {
      key: '4',
      label: 'Attachments',
      children: renderAttachments(),
    },
  ];
  const [getMediaByIdArray] = useGetMediaByIdArrayMutation();

  useEffect(() => {
    if (!order) return;

    const mediaIds = order?.formData
      ? Object.entries(order.formData)
          .filter(([_, value]) => Array.isArray(value))
          .flatMap(([_, value]) => value)
      : [];

    const fetchMedia = async () => {
      const response = await getMediaByIdArray({
        data: { ids: mediaIds },
      }).unwrap();
      setAllFormAttachments(response?.media || []);
    };

    if (mediaIds.length > 0) {
      fetchMedia();
    }
  }, [order, getMediaByIdArray]);

  if (!order) return <Spinner />;

  return (
    <>
      <div className="">
        <Title level={5} className="mb-6">
          Order Details
        </Title>
        <Tabs
          items={items}
          defaultActiveKey="1"
          className="h-full"
          type="card"
        />
        {showPreview && (
          <PreviewImgPdfFullscreen
            media={mediaToPreview}
            showPreview={showPreview}
            setShowPreview={setShowPreview}
          />
        )}
      </div>
      <Modal
        open={openFormAttachmentModal}
        onCancel={() => setOpenFormAttachmentModal(false)}
        width="80%"
        footer={null}
      >
        <div className="flex gap-4 flex-wrap">
          {allFormAttachments?.map((attachment, index) => (
            <div key={index} className="relative group flex justify-center">
              {attachment.type.startsWith('image/') ? (
                <img
                  src={attachment.data}
                  alt={`Full view ${index + 1}`}
                  className="w-[70%] h-full rounded-lg transform transition-transform duration-200 group-hover:scale-105 shadow-md object-contain"
                />
              ) : attachment.type === 'application/pdf' ? (
                <iframe
                  src={attachment.data}
                  title={`PDF ${index + 1}`}
                  className="w-[70%] h-[500px] rounded-lg transform transition-transform duration-200 group-hover:scale-105 shadow-md"
                />
              ) : (
                <div className="w-[70%] h-[200px] rounded-lg flex items-center justify-center bg-gray-200">
                  <Text>{attachment.name}</Text>
                </div>
              )}
            </div>
          ))}
        </div>
      </Modal>

      {/* Share Attachments Modal */}
      <AttachmentShareModal
        isOpen={openShareModal}
        onClose={() => setOpenShareModal(false)}
        customerPhoneNumber={order?.customerId?.phone_no?.[0] || ''}
        selectedAttachments={selectedAttachments}
        selectedOrderId={order?._id}
      />

      {/* Add Remark Modal */}
      <Modal
        title="Add Timeline Remark"
        open={showRemarkModal}
        onOk={handleAddRemark}
        onCancel={() => {
          setShowRemarkModal(false);
          setRemarkText('');
        }}
        okText="Add Remark"
        cancelText="Cancel"
        width={500}
      >
        <div className="space-y-4">
          <div>
            <Text className="block mb-2 font-medium">Remark</Text>
            <Input.TextArea
              value={remarkText}
              onChange={(e) => setRemarkText(e.target.value)}
              placeholder="Enter your remark here..."
              rows={4}
              maxLength={500}
              showCount
            />
          </div>
          <div className="text-sm text-gray-500">
            This remark will be added to the order timeline and will be visible
            to all users with access to this order.
          </div>
        </div>
      </Modal>
    </>
  );
};

export default OrderDetailsSidebar;
