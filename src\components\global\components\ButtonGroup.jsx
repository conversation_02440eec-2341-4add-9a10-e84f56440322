const ButtonGroup = ({ buttons, value, onChange, className }) => {
  return (
    <div className="overflow-x-auto no-scrollbar">
      <div
        className={`inline-flex rounded-lg border border-gray-200 bg-gray-50 p-1 shadow-sm transition-all duration-200 ${className}`}
      >
        {buttons.map((btn) => (
          <button
            key={btn}
            onClick={() => onChange(btn)}
            className={`relative px-4 py-1 sm:px-6 sm:py-1.5 text-sm font-medium capitalize text-gray-800 transition-all duration-200 outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50 text-nowrap
              ${
                value === btn
                  ? 'bg-white text-blue-600 rounded-md shadow-sm border border-gray-100 font-semibold'
                  : 'bg-transparent hover:bg-gray-100 text-gray-600'
              }`}
          >
            {btn.toLowerCase()}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ButtonGroup;
