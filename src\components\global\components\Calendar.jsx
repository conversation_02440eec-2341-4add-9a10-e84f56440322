import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import { useContext, useMemo } from 'react';
import { Store } from '../../../store/Store';
import { CALENDER_DAYS } from '../../../utils/Constant';

function Calendar({
  events = [],
  eventClick,
  eventContent,
  eventBorderColor = '',
  eventBackgroundColor = '',
  ...rest
}) {
  const { defaults: { defaultParam: { holidays } = {} } = {} } =
    useContext(Store);

  const daysOfWeek = useMemo(() => {
    if (holidays?.weeks)
      return CALENDER_DAYS.map((item, idx) =>
        holidays?.weeks?.includes(item) ? 'NA' : idx
      ).filter((i) => i !== 'NA');
    return [0, 1, 2, 3, 4, 5, 6];
  }, [holidays?.weeks]);

  return (
    <FullCalendar
      plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
      headerToolbar={{
        left: 'title',
        center: 'dayGridMonth,timeGridWeek,timeGridDay',
        right: 'today prev,next',
      }}
      initialView="dayGridMonth"
      editable={true}
      selectable={true}
      selectMirror={true}
      dayMaxEvents={true}
      eventClick={(e) => {
        if (eventClick && typeof eventClick === 'function')
          eventClick(events?.find((item) => item.id === e.event.id));
      }}
      eventContent={eventContent}
      eventBorderColor={eventBorderColor}
      eventBackgroundColor={eventBackgroundColor}
      events={events}
      businessHours={{
        daysOfWeek,
        startTime: '00:00',
        endTime: '23:59',
      }}
      {...rest}
    />
  );
}

export default Calendar;
