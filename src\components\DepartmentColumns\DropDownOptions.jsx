import { Button, Input, Modal } from 'antd';
import { Plus, Settings, Trash2 } from 'lucide-react';
import { toast } from 'react-toastify';

const DropDownOptions = ({
  //   tableConfig,
  setTableConfig,
  dropDownOptionModal,
  setDropDownOptionModal,
  columnIndex,
  dropDownOptions,
  setDropDownOptions,
}) => {
  const handleSetOptions = (idx) => {
    const allOptionsFilled = dropDownOptions?.every((option) => option !== '');
    if (!allOptionsFilled) {
      toast.error('Please fill all options');
      return;
    }
    setTableConfig((prev) => ({
      ...prev,
      columns: prev?.columns?.map((elem, i) => {
        if (i === idx) {
          return {
            ...elem,
            options: dropDownOptions,
          };
        }
        return elem;
      }),
    }));
    setDropDownOptionModal(false);
    setDropDownOptions([]);
  };

  const handleCancel = () => {
    setDropDownOptions([]);
    setDropDownOptionModal(false);
  };
  return (
    <div>
      <Modal
        title={
          <div className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            <span>Dropdown Options Configuration</span>
          </div>
        }
        open={dropDownOptionModal}
        onCancel={handleCancel}
        onOk={() => handleSetOptions(columnIndex)}
        okText="Save"
        width={800}
      >
        <div className="py-4">
          <div className="flex justify-end mb-4">
            <Button
              type="primary"
              className="flex items-center gap-2"
              onClick={() => setDropDownOptions((prev) => [...prev, ''])}
              icon={<Plus className="w-4 h-4" />}
            >
              Add Option
            </Button>
          </div>

          <div className="space-y-3 max-h-[400px] overflow-y-auto px-2">
            {dropDownOptions?.map((option, idx) => (
              <div
                key={idx}
                className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <span className="text-gray-500 text-sm min-w-[24px]">
                  {idx + 1}.
                </span>
                <Input
                  placeholder="Enter option value"
                  value={option}
                  className="flex-1"
                  onChange={(e) => {
                    const newOptions = dropDownOptions.map((opt, index) =>
                      index === idx ? e.target.value : opt
                    );
                    setDropDownOptions(newOptions);
                  }}
                />
                <Button
                  type="text"
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  icon={<Trash2 className="w-4 h-4" />}
                  onClick={() => {
                    const newOptions = dropDownOptions.filter(
                      (_, index) => index !== idx
                    );
                    setDropDownOptions(newOptions);
                  }}
                />
              </div>
            ))}

            {dropDownOptions.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No options added yet. Click "Add Option" to begin.
              </div>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default DropDownOptions;
