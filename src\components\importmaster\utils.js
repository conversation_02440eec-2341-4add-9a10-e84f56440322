// export const convertToJson = (csv) => {
//   let lines = csv.split('\n');

//   let result = [];

//   let headers = lines[0].split(',');

//   for (let i = 1; i < lines.length; i++) {
//     let obj = {};
//     let currentline = lines[i].split(',');

//     for (let j = 0; j < headers.length; j++) {
//       obj[headers[j]?.trim()] = currentline[j]?.trim();
//     }

//     if (Object.values(obj)?.find((i) => i)) result.push(obj);
//   }

//   return result;
// };

export const convertToJson = (csv) => {
  // Split by line breaks, filter out empty lines
  let lines = csv.split('\n').filter((line) => line.trim() !== '');

  if (lines.length <= 1) {
    return [];
  }

  let result = [];
  // Parse headers, trim each one
  let headers = lines[0].split(',').map((header) => header.trim());

  // Process each data row
  for (let i = 1; i < lines.length; i++) {
    let obj = {};
    // Handle potential quoted values with commas inside
    let currentline = parseCSVLine(lines[i]);

    // Make sure we have enough values for each header
    if (currentline.length >= headers.length) {
      // Map headers to values
      for (let j = 0; j < headers.length; j++) {
        if (headers[j]) {
          // Only process non-empty headers
          obj[headers[j]] = currentline[j] ? currentline[j].trim() : '';
        }
      }

      // Only add non-empty objects
      if (Object.values(obj).some((value) => value && value.trim() !== '')) {
        result.push(obj);
      }
    }
  }

  return result;
};

// Helper function to properly parse CSV lines with potential quoted values
function parseCSVLine(line) {
  const result = [];
  let currentValue = '';
  let inQuotes = false;

  for (let i = 0; i < line.length; i++) {
    const char = line[i];

    if (char === '"' && (i === 0 || line[i - 1] !== '\\')) {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      result.push(currentValue);
      currentValue = '';
    } else {
      currentValue += char;
    }
  }

  result.push(currentValue); // Add the last value
  return result;
}
