import { useContext } from 'react';
import { useParams } from 'react-router-dom';
import {
  deliveryDateOptions,
  generateDateString,
  getLocalDate,
  isObjectEmpty,
  transformAddressField,
} from '../../helperFunction';
import { Store } from '../../store/Store';

import {
  useGetAllVendorsForOptionsQuery,
  useLazyGetVendorByIdQuery,
} from '../../slices/vendorApiSlice';

import { useEffect, useState } from 'react';

import Input from '../global/components/Input';
import Select from '../global/components/Select';
// import Textarea from '../global/components/Textarea';
import usePrefixIds from '../../hooks/usePrefixIds';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice';
import { useGetAllPurchaseOrderQuery } from '../../slices/purchaseOrderApiSlice';
import MasterDetails from '../MasterDetails';
import { Label } from '../v2';

const GeneralInfo = ({
  paymentTermOptions,
  setShowAddNewModal,
  setAddNewVendor,
  isMobile,
  isTablet,
  changeHandler,
  formData,
  addressSelector,
  contactSelector,
  emailSelector,
  profileSelector,
  vendor,
  setVendor,
  setDefaults,
  companyDetails,
  companySelectedDetails,
  isCopy,
  indent,
  // defaultParam,
  setAdditionalFields,
  additionalFields,
  Searchparams,
  isEdit,
  setItems,
  setFormData,
}) => {
  const { defaults } = useContext(Store);
  const { id } = useParams();
  const [businessDetails, setBusinessDetails] = useState({});
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const { data: allVendorOptions } = useGetAllVendorsForOptionsQuery();
  const [DateValue, setDateValue] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const { data: allPOs } = useGetAllPurchaseOrderQuery();
  const [getVendorById] = useLazyGetVendorByIdQuery();
  const [vendorLoading, setVendorLoading] = useState(false);

  const { IdGenComp, idCompData, taskId } = usePrefixIds({
    idFor: 'poId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });

  useEffect(() => {
    const getCols = async () => {
      const path = '/purchase/po';
      getTemplates({ path });
    };
    getCols();
  }, [getTemplates]);

  useEffect(() => {
    const address = companyDetails?.address?.find(
      (i) => i._id === companySelectedDetails?.selectedDetails?.address
    );
    const contact = companyDetails?.contactNumber?.find(
      (i) => i._id === companySelectedDetails?.selectedDetails?.contact
    );
    const email = companyDetails?.emailAddress?.find(
      (i) => i._id === companySelectedDetails?.selectedDetails?.email
    );
    let businessDetail = {
      companyName: companyDetails?.name,
      address: address,
      contact: contact?.number,
      gstNumber: companyDetails?.gstNumber,
      email: email?.mail,
    };
    setBusinessDetails(businessDetail);
  }, [
    companyDetails,
    companySelectedDetails?.selectedDetails?.address,
    companySelectedDetails?.selectedDetails?.contact,
    companySelectedDetails?.selectedDetails?.email,
  ]);

  useEffect(
    () => {
      const setIdFormatFunc = () => {
        if (allPOs?.length === 0) {
          if (templatesData && selectedTemplate === null) {
            const defaultTemplate = templatesData?.find((template) =>
              template.name.startsWith('Default')
            );
            setAdditionalFields(defaultTemplate);
            setSelectedTemplate(defaultTemplate);
          }
        } else {
          const templateParamsId =
            Searchparams.get('templateId') === 'undefined'
              ? null
              : Searchparams.get('templateId');
          if (allPOs) {
            const lastEntry = allPOs[allPOs?.length - 1];
            const templateToUse = templatesData?.find((template) => {
              return (
                template?._id ===
                (templateParamsId
                  ? templateParamsId
                  : lastEntry?.additionalFields?._id)
              );
            });
            if (!templateToUse) {
              const defaultTemplate = templatesData?.find((template) =>
                template.name.startsWith('Default')
              );
              setAdditionalFields(defaultTemplate);
              setSelectedTemplate(defaultTemplate);

              return;
            }
            setSelectedTemplate(templateToUse);
            setAdditionalFields(templateToUse);
          }
        }
      };
      if (isEdit && !isCopy) {
        setAdditionalFields(additionalFields);
        return;
      } else if (isCopy) {
        setIdFormatFunc();
      } else {
        setIdFormatFunc();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      Searchparams,
      allPOs,
      // additionalFields,
      isEdit,
      templatesData,
      isCopy,
      selectedTemplate,
    ]
  );

  return (
    <>
      <div className="flex !md:w-full flex-col md:flex-row-reverse items-center justify-between mt-4">
        <div className="flex items-center !text-sm  min-w-[300px] ml-11  ">
          <div className="w-full flex items-center max-w-max  md:mt-[-22px] md:ml-12">
            <p className="font-semibold text-gray-800">PO Date :</p>
            <p className="text-gray-600 ml-1">{getLocalDate(Date.now())}</p>
          </div>
          <div className="w-[135px] flex items-center max-w-max gap-1  md:mt-[-22px]">
            <p className="font-semibold text-gray-800 ml-4">Task ID: </p>
            <p className="text-gray-600 ml-1">{taskId}</p>
          </div>
        </div>
        <div className="w-full  md:1/4 mt-5 !text-sm !rounded-lg">
          <span className="font-semibold text-gray-800">PO ID :</span>
          <span className="flex flex-wrap items-center mt-2 justify-start h-fit w-full md:w-1/4">
            {id === 'new' || isCopy ? (
              <IdGenComp {...idCompData} />
            ) : (
              <Input disabled value={formData?.poID} className="!w-full" />
            )}
          </span>
        </div>
      </div>

      {/* Template Selection */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mt-4 w-full">
        {' '}
        <div className="w-full md:w-2/5">
          <label className=" font-medium text-sm text-gray-500 ">
            Choose Template:
          </label>
          <div className="font-semibold text-[#515357]">
            <Select
              className="!text-sm !font-medium !text-gray-500"
              options={templatesData?.map((template) => ({
                value: template,
                name: template.name,
              }))}
              onChange={(e) => {
                const obj = e.target.value;
                setAdditionalFields({ ...obj });
                setSelectedTemplate(e.target.value);
                if (selectedTemplate?.idIndex === e.target.value.idIndex) {
                  return;
                }
              }}
              value={selectedTemplate}
            />
          </div>
        </div>
        <div className="w-full md:w-2/5 mt-3">
          <label className="block mb-1 text-sm text-gray-500 font-medium">
            Delivery Date
          </label>
          {DateValue === '+' ? (
            <Input
              type="date"
              className="w-full"
              id="deliveryDate"
              name="deliveryDate"
              placeholder="Delivery Date"
              value={
                formData?.deliveryDate
                  ? formData?.deliveryDate?.split('/').reverse().join('-')
                  : ''
              }
              onChange={(e) => changeHandler('deliveryDate', e.target.value)}
              min={isEdit ? '' : new Date().toISOString().split('T')[0]}
            />
          ) : (
            <Select
              options={deliveryDateOptions}
              className="w-full"
              placeholder={
                formData?.deliveryDate
                  ? generateDateString(new Date(formData?.deliveryDate))
                  : 'Select'
              }
              value={
                formData?.deliveryDate
                  ? generateDateString(new Date(formData?.deliveryDate))
                  : ''
              }
              onChange={(e) => {
                if (e.target.value === '+') {
                  changeHandler('deliveryDate', e.target.value);
                  setDateValue(e.target.value);
                } else {
                  const day = +e.target.value;
                  const deliveryDate = new Date();
                  deliveryDate.setDate(deliveryDate.getDate() + day);
                  setDateValue(deliveryDate);
                  changeHandler('deliveryDate', deliveryDate);
                }
              }}
            />
          )}
        </div>
      </div>
      <div className="flex flex-col md:flex-row md:items-start md:justify-between mt-4 w-full">
        {/* Vendor Selection */}
        <div className="w-full md:w-2/5 mt-1 md:mt-2">
          <label className="block mb-1 text-sm text-gray-500 font-medium">
            Select Vendor
          </label>
          {Searchparams?.get('with_indent') === 'true' ? (
            <Input value={vendor?.name || ''} disabled={true} />
          ) : (
            <Select
              value={vendor?._id}
              onChange={async (e) => {
                const selectedValue = e.target.value;
                if (e.target.value === '+') {
                  setAddNewVendor(true);
                } else {
                  setVendorLoading(true);
                  const { data: fetchedVendor } =
                    await getVendorById(selectedValue);
                  setVendor(fetchedVendor);
                  setVendorLoading(false);
                  setItems((prev) =>
                    prev.map((el) => {
                      if (el?.type === 'PartVariant' || el?.type === 'Part') {
                        const hasSelectedVendor = el.vendor_details?.find(
                          (currElem) =>
                            currElem?.vendor?._id === fetchedVendor?._id
                        );

                        if (!isObjectEmpty(hasSelectedVendor)) {
                          return {
                            ...el,
                            rate: hasSelectedVendor?.rate,
                            discount: hasSelectedVendor?.discount,
                          };
                        }

                        return {
                          ...el,
                          rate: 0,
                          discount: 0,
                        };
                      } else {
                        return el;
                      }
                    })
                  );
                }
              }}
              options={[
                { label: '+ Add New Vendor', value: '+' },
                ...(allVendorOptions?.map((vendor) => ({
                  label: vendor?.name,
                  value: vendor._id,
                })) || []),
              ]}
            />
          )}
          <p id="only-on-print">{vendor?.name || 'Name'}</p>
          <MasterDetails
            isLoading={vendorLoading}
            isMobile={isMobile}
            isTablet={isTablet}
            className="!mt-6 !text-gray-500 !text-sm !md:text-[5px]"
            details={vendor || {}}
            setDetails={setVendor}
            excludedFields={[
              'id',
              '_id',
              'logo',
              '__v',
              'profileId',
              'createdAt',
              'updatedAt',
              'idFormat',
              'isUsed',
              'isHidden',
              'lastUsed',
              'additionalFields',
              'name',
            ]}
          />
        </div>

        {/* company details */}
        <div className="w-full md:w-2/5 mt-4 md:mt-2">
          {profileSelector()}
          <MasterDetails
            isMobile={isMobile}
            isTablet={isTablet}
            className={`!mt-6 !text-gray-500 !text-sm `}
            details={transformAddressField(businessDetails) || {}}
            // details={{}}
            excludedFields={[
              'id',
              '_id',
              'logo',
              '__v',
              'profileId',
              'additionalFields',
              'createdAt',
              'updatedAt',
              'lastUsed',
              'number',
            ]}
            companySelectors={{
              address: addressSelector,
              contact: contactSelector,
              email: emailSelector,
            }}
          />
        </div>
      </div>

      <div className="grid mt-4 grid-cols-2  gap-4">
        {indent.length > 0 && (
          <div className="w-full">
            <label className="block mb-1 text-sm text-gray-500 font-medium">
              Indent Link
            </label>
            <Input
              disabled
              placeholder=" "
              value={indent?.map((item) =>
                item ? 'IND-' + item?.indent_no : ''
              )}
            />
          </div>
        )}
      </div>
      {/* <div className="col-span-full">{addressSelector()}</div>
          {contactSelector()}
          {emailSelector()} */}
      {/* <div className="w-full">
          <label className="block mb-1 text-sm text-gray-500 font-medium">
            Billing Address
          </label>
          <Textarea
            placeholder="address"
            id="ignore"
            onChange={(e) => changeHandler('billingAddress', e.target.value)}
            value={formData?.billingAddress}
          />
          <p id="only-on-print">{formData?.billingAddress || ''}</p>
          <div className="flex h-10 items-center gap-3 justify-end" id="ignore">
            <Input
              type="checkbox"
              id="ignore"
              className="-mt-1"
              onChange={() => {
                if (
                  dataDefault?.purchaseOrder?.billingAddress !==
                  formData?.billingAddress
                ) {
                  setDefaults((prev) => ({
                    ...prev,
                    purchaseOrder: {
                      ...prev.purchaseOrder,
                      billingAddress: formData?.billingAddress,
                    },
                  }));
                }
              }}
            />
            <Label className="text-sm">Set as default</Label>
          </div>
        </div>
        <div className="w-full">
          <label className="block mb-1 text-sm text-gray-500 font-medium">
            Delivery Address
          </label>
          <Textarea
            placeholder="address"
            onChange={(e) => changeHandler('deliveryAddress', e.target.value)}
            value={formData?.deliveryAddress}
            id="ignore"
          />

          <div className="flex items-center justify-end gap-2 ">
            {' '}
            <Input
              type="checkbox"
              onChange={(e) => {
                if (e.target.checked) {
                  changeHandler('deliveryAddress', formData?.billingAddress);
                  setVendor((prev) => ({
                    ...prev,
                    deliveryAddress: formData?.billingAddress,
                  }));
                } else {
                  changeHandler('deliveryAddress', '');
                  setVendor((prev) => ({
                    ...prev,
                    deliveryAddress: '',
                  }));
                }
              }}
            />
            <Label>Same as Billing Address</Label>
          </div>
          <p id="only-on-print">{formData?.deliveryAddress || ''}</p>
        </div> */}
      {/* <div className="w-full">
            <label className="block mb-1 text-sm text-gray-500 font-medium">
              Company Details
            </label>
            <Textarea value={ViewCompanyDetails} disabled={true} id="ignore" />
            <p id="only-on-print">{ViewCompanyDetails || ''}</p>
          </div> */}
      <div className="mt-7">
        <div className="w-full md:w-2/5 mt-4">
          <label className="block mb-1 text-sm text-gray-500 font-medium">
            Payment Term
          </label>

          <Select
            value={formData?.paymentTerm}
            options={[
              { label: '+ Add New PaymentTerm', value: '+' },
              ...(paymentTermOptions?.map((option) => {
                return {
                  label: option,
                  value: option,
                };
              }) || []),
            ]}
            onChange={(e) => {
              if (e.target.value === '+') {
                setShowAddNewModal(true);
              } else {
                changeHandler('paymentTerm', e.target.value);
                setVendor((prev) => {
                  return { ...prev, paymentTerm: e.target.value };
                });
              }
            }}
            id="ignore"
          />
          <p id="only-on-print">{formData?.paymentTerm || ''}</p>
          <div className="flex items-center gap-3 justify-end" id="ignore">
            <Input
              type="checkbox"
              id="ignore"
              onChange={() => {
                if (
                  defaults?.purchaseOrder?.paymentTerm !== formData?.paymentTerm
                ) {
                  setDefaults((prev) => ({
                    ...prev,
                    purchaseOrder: {
                      ...prev.purchaseOrder,
                      paymentTerm: formData?.paymentTerm,
                    },
                  }));
                }
              }}
            />
            <Label>Set as default</Label>
          </div>
        </div>
      </div>
    </>
  );
};

export default GeneralInfo;
