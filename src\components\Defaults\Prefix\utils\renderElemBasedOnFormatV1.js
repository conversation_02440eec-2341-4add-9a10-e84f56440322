import { MdEdit } from 'react-icons/md';
import NewInput from '../../../global/components/NewInput';
import Select from '../../../global/components/Select';

const renderElemBasedOnFormatV1 = (
  idType,
  format,
  setFormat,
  setFormatComponent,
  setShowEditOptions,
  setDropdown
) => {
  if (Object.keys(format?.[idType]).length == 0) {
    setFormatComponent({});
  } else {
    Object.keys(format[idType]).forEach((type) => {
      let elem = type.substring(0, type.indexOf('_'));
      switch (elem) {
        case 'String': {
          const StringInput = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 flex items-center relative pt-1 pr-1">
              <NewInput
                type="text"
                value={format[idType][type]}
                className="w-max"
                onChange={(e) => {
                  setFormat((prev) => {
                    return {
                      ...prev,
                      [idType]: {
                        ...prev[idType],
                        [type]: e.target.value,
                      },
                    };
                  });
                }}
              />
              <MdEdit
                className="absolute end-0 top-0 text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: StringInput,
            };
          });
          break;
        }
        case 'MM-YY': {
          const MonthYear = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 flex border items-center rounded-lg relative pt-1 pr-1">
              <p className="w-max ml-4">MM-YY</p>
              <MdEdit
                className="absolute end-0 top-0 text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: MonthYear,
            };
          });
          break;
        }
        case 'DD-MM-YY': {
          const DayMonthYear = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 flex border items-center rounded-lg relative pt-1 pr-1">
              <p className="w-max ml-4">DD-MM-YY</p>
              <MdEdit
                className="absolute end-0 top-0 text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: DayMonthYear,
            };
          });
          break;
        }
        case 'Increment': {
          const Increment = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 flex items-center relative pt-1 pr-1">
              <NewInput
                type="number"
                className="w-max"
                value={format[idType][type]}
                onChange={(e) => {
                  setFormat((prev) => {
                    return {
                      ...prev,
                      [idType]: {
                        ...prev[idType],
                        [type]: +e.target.value,
                      },
                    };
                  });
                }}
              />
              <MdEdit
                className="absolute end-0 top-0 text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: Increment,
            };
          });
          break;
        }
        case 'UserEntry': {
          const UserEntry = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 pt-1 pr-1 flex items-center relative">
              <NewInput
                type="text"
                placeholder="User Entry"
                className="w-max"
                disabled
              />
              <MdEdit
                className="absolute end-0 top-0 text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: UserEntry,
            };
          });
          break;
        }
        case 'WorkOrderId': {
          const WorkOrderId = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <div className="w-max">Work Order ID</div>
              <MdEdit
                className="absolute end-[-0.5rem] top-[-0.5rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
            // <></>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: WorkOrderId,
            };
          });
          break;
        }
        case 'JobId': {
          const JobId = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <div className="w-max">Job ID</div>
              <MdEdit
                className="absolute end-[-0.5rem] top-[-0.5rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
            // <></>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: JobId,
            };
          });
          break;
        }
        case 'ModelName': {
          const ModelName = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <div className="w-max">Model Name</div>
              <MdEdit
                className="absolute end-[-0.5rem] top-[-0.5rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: ModelName,
            };
          });
          break;
        }
        case 'Process': {
          const Process = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <div className="w-max">Process</div>
              <MdEdit
                className="absolute end-[-0.5rem] top-[-0.5rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: Process,
            };
          });
          break;
        }
        case 'BatchNo': {
          const BatchNo = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <div className="w-max">Batch No.</div>
              <MdEdit
                className="absolute end-[-0.5rem] top-[-0.5rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: BatchNo,
            };
          });
          break;
        }
        case 'InputScreen': {
          const InputScreen = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <div className="w-max">Input Screen</div>
              <MdEdit
                className="absolute end-[-0.5rem] top-[-0.5rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: InputScreen,
            };
          });
          break;
        }
        case 'Dropdown': {
          const Dropdown = (
            <div
              className="flex pt-1 pr-1 relative"
              onClick={() =>
                setDropdown(() => ({
                  open: true,
                  idType: idType,
                  type: type,
                  options: format?.[idType]?.[type] || [],
                }))
              }
            >
              <Select className="w-max" disabled placeholder="Dropdown">
                Dropdown
              </Select>
              <MdEdit
                className="absolute end-0 top-0 text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                  });
                }}
              />
            </div>
          );
          setFormatComponent((prev) => {
            return {
              ...prev,
              [type]: Dropdown,
            };
          });
          break;
        }
      }
    });
  }
};

export default renderElemBasedOnFormatV1;
