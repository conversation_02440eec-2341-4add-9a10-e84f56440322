import { ChevronDown, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import RSelect from 'react-select';

const SelectV2 = ({
  value,
  onChange,
  name = '',
  options = [],
  placeholder = 'Select',
  id,
  styles = {},
  className = '',
  disabled = false,
  error = false,
  onFocus,
  onBlur,
  isLoading = false,
  menuPlacement = 'auto',
  menuPosition = 'fixed',
  usePortal = false,
  portalTarget,
  size = 'default',
  variant = 'default',
  isClearable = false,
  isSearchable = true,
  ...rest
}) => {
  const [focused, setFocused] = useState(false);
  const [selectValue, setSelectValue] = useState(null);

  // Find the selected option based on value
  useEffect(() => {
    const val = options?.find((op) => op?.value === value);
    if (val) {
      setSelectValue({ label: val?.name || val?.label, value: val?.value });
    } else {
      setSelectValue(null);
    }
  }, [value, options]);

  // Size variants
  const sizeStyles = {
    small: {
      control: { minHeight: '28px', height: '28px' },
      valueContainer: { padding: '0 4px' },
      placeholder: { fontSize: '12px' },
      singleValue: { fontSize: '12px' },
    },
    default: {
      control: { minHeight: '34px', height: '34px' },
      valueContainer: { padding: '0 6px' },
      placeholder: { fontSize: '14px' },
      singleValue: { fontSize: '14px' },
    },
    large: {
      control: { minHeight: '46px', height: '46px' },
      valueContainer: { padding: '0 12px' },
      placeholder: { fontSize: '16px' },
      singleValue: { fontSize: '16px' },
    },
  };

  // Appearance variants
  const variantStyles = {
    default: {
      control: (base, state) => ({
        backgroundColor: state.isDisabled ? '#F3F4F6' : 'white',
        border: error
          ? '1px solid #EF4444'
          : state.isFocused
            ? '1px solid #3770ed'
            : '1px solid #D1D5DB',
        boxShadow: state.isFocused
          ? '0 0 0 2px rgba(37, 99, 235, 0.2)'
          : 'none',
        '&:hover': {
          border: error
            ? '1px solid #EF4444'
            : state.isFocused
              ? '1px solid #3770ed'
              : '1px solid #9CA3AF',
        },
      }),
    },
    outline: {
      control: (base, state) => ({
        backgroundColor: 'transparent',
        border: error
          ? '1px solid #EF4444'
          : state.isFocused
            ? '1px solid #3770ed'
            : '1px solid #D1D5DB',
        boxShadow: state.isFocused
          ? '0 0 0 2px rgba(59, 130, 246, 0.1)'
          : 'none',
        '&:hover': {
          border: error
            ? '1px solid #EF4444'
            : state.isFocused
              ? '1px solid #3770ed'
              : '1px solid #9CA3AF',
        },
      }),
    },
    ghost: {
      control: (base, state) => ({
        backgroundColor: state.isFocused
          ? 'rgba(243, 244, 246, 0.8)'
          : 'transparent',
        border: 'none',
        boxShadow: 'none',
        '&:hover': {
          backgroundColor: 'rgba(243, 244, 246, 0.8)',
        },
      }),
    },
  };

  const baseStyles = {
    control: (provided, state) => ({
      ...provided,
      borderRadius: '6px',
      transition: 'all 0.2s ease',
      cursor: state.isDisabled ? 'not-allowed' : 'pointer',
      padding: '0',
      minWidth: '7rem',
      width: '100%',
      ...sizeStyles[size].control,
      ...variantStyles[variant].control(provided, state),
    }),
    valueContainer: (provided) => ({
      ...provided,
      ...sizeStyles[size].valueContainer,
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#9CA3AF',
      ...sizeStyles[size].placeholder,
    }),
    singleValue: (provided) => ({
      ...provided,
      color: disabled ? '#6B7280' : '#111827',
      ...sizeStyles[size].singleValue,
    }),
    indicatorsContainer: (provided) => ({
      ...provided,
      height: '100%',
    }),
    dropdownIndicator: (provided, state) => ({
      ...provided,
      color: state.isDisabled ? '#9CA3AF' : '#6B7280',
      padding: size === 'small' ? '4px' : '6px',
      transition: 'transform 0.2s ease',
      transform: state.selectProps.menuIsOpen ? 'rotate(180deg)' : 'rotate(0)',
      '&:hover': {
        color: '#111827',
      },
    }),
    input: (provided) => ({
      ...provided,
      margin: '0',
      padding: '0',
      color: '#111827',
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 50,
      overflow: 'hidden',
      backgroundColor: 'white',
      borderRadius: '6px',
      boxShadow:
        '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      marginTop: '4px',
      border: '1px solid #E5E7EB',
    }),
    menuList: (provided) => ({
      ...provided,
      padding: '4px',
      maxHeight: '160px',
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#3b82f6'
        : state.isFocused
          ? '#F3F4F6'
          : 'white',
      color: state.isSelected ? 'white' : '#111827',
      padding: size === 'small' ? '6px 8px' : '8px 12px',
      fontSize: size === 'small' ? '12px' : '14px',
      cursor: 'pointer',
      '&:active': {
        backgroundColor: state.isSelected ? '#3b82f6' : '#E5E7EB',
      },
      '&:hover': {
        backgroundColor: state.isSelected ? '#3b82f6' : '#F3F4F6',
      },
      borderRadius: '4px',
      margin: '2px 0',
    }),
    noOptionsMessage: (provided) => ({
      ...provided,
      color: '#6B7280',
      fontSize: '14px',
    }),
    loadingMessage: (provided) => ({
      ...provided,
      color: '#6B7280',
      fontSize: '14px',
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 9999,
    }),
  };

  const customComponents = {
    IndicatorSeparator: () => null,
    LoadingIndicator: () => (
      <Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
    ),
    DropdownIndicator: (props) => (
      <ChevronDown
        className={`h-4 w-4 mr-2 ${props.isDisabled ? 'text-gray-400' : 'text-gray-600'}`}
      />
    ),
  };

  const handleChange = (option) => {
    if (onChange) {
      onChange({
        target: {
          label: option?.label,
          name,
          value: option?.value,
          type: 'select',
        },
      });
    }
  };

  return (
    <RSelect
      value={selectValue}
      onChange={handleChange}
      options={options?.map((op) => ({
        label: op?.name || op?.label,
        value: op?.value,
        isDisabled: op?.disabled,
      }))}
      placeholder={placeholder}
      isDisabled={disabled}
      id={id}
      name={name}
      menuPlacement={menuPlacement}
      menuPosition={menuPosition}
      isLoading={isLoading}
      isClearable={isClearable}
      isSearchable={isSearchable}
      menuPortalTarget={usePortal ? portalTarget || document.body : null}
      onFocus={() => {
        setFocused(true);
        if (onFocus) onFocus();
      }}
      onBlur={() => {
        setFocused(false);
        if (onBlur) onBlur();
      }}
      className={`text-start w-full ${focused ? 'z-10' : ''} ${className}`}
      styles={{
        ...baseStyles,
        ...styles,
      }}
      components={customComponents}
      {...rest}
    />
  );
};

export default SelectV2;
