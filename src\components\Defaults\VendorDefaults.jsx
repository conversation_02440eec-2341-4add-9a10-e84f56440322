import React, { useState } from 'react';
import Button from '../../components/global/components/Button';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { MAX_CHAR_ALLOWED } from '../../utils/Constant';
import { getstrLen } from '../../utils/Getstrlen';
import Input from '../global/components/Input';
// import { Store } from '../../store/Store';
import { camelCaseString } from '../../helperFunction';
// import { inventoryTypes } from '../components/v3/InventoryMasters/inventoryConstants';
import { TrashIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { inventoryTypes } from '../../components/v3/InventoryMasters/inventoryConstants';
import {
  useAddAdditionalColumnsMutation,
  useGetColumnsQuery,
} from '../../slices/columnsApiSlice';
import { customConfirm } from '../../utils/customConfirm';

export default function VendorDefaults({ defaults, setDefaults }) {
  const [name, setName] = useState('');
  const [updateDefaults, { isLoading: isUpdateDefaultsLoading }] =
    useUpdateDefaultsMutation();
  const { data: columnsData } = useGetColumnsQuery({
    path: '/settings/inventory/masters/vendor',
  });
  const [addColumns, { isLoading: isAddColumnsLoading }] =
    useAddAdditionalColumnsMutation();

  const handleInput = (e) => {
    setName(e.target.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (getstrLen(name) > MAX_CHAR_ALLOWED) {
      toast.error(`Column name cannot exceeds ${MAX_CHAR_ALLOWED} characters`, {
        position: 'top-right',
        theme: 'colored',
        toastId: 'name len error',
      });
      return;
    }
    if (name === '') {
      toast.error('Column name cannot be empty');
      return;
    }
    const exist = defaults?.vendorColumns?.find((item) => {
      return item.title.toLowerCase() === name.toLowerCase();
    });
    if (columnsData) {
      const columnExist = Object.keys(columnsData[0].columns).includes(
        name.toLowerCase()
      );
      if (columnExist) {
        toast.warning('Same column cannot be added', {
          position: toast.POSITION.TOP_RIGHT,
          toastId: 'warning',
        });
        setName('');
        return;
      }
    }
    if (exist) {
      toast.warning('Same column cannot be added', {
        position: toast.POSITION.TOP_RIGHT,
        toastId: 'warning',
      });
      setName('');
      return;
    }

    const data = [
      ...defaults?.vendorColumns,
      {
        field: camelCaseString(name),
        title: name,
        isUsed: false,
        // this field is required to differentiate between static and custom columns so that we can show the custom columns in bulk edit
        customColumn: true,
      },
    ];
    const key =
      inventoryTypes.VENDOR === 'vendor'
        ? 'vendorColumns'
        : inventoryTypes.VENDOR === 'store'
          ? 'storeColumns'
          : inventoryTypes.VENDOR === 'dropdowns'
            ? 'dropdownColumn'
            : '';

    await updateDefaults({ [key]: data }).unwrap();
    await addColumns({
      path: '/settings/inventory/masters/vendor',
      data: {
        ...columnsData[0],
        additionalFields: name?.toUpperCase(),
      },
    });
    setName('');

    toast.success('Column added successfully!');
  };

  // console.log("defaults",  defaults?.vendorColumns)
  const handleColDelete = async (index, isUsed) => {
    if (isUsed) {
      const userConfirmed = await customConfirm(
        'Are you sure? Data will be deleted!'
      );
      //    console.log("userConfirmed", userConfirmed)

      if (userConfirmed) {
        setDefaults((prev) => ({
          ...prev,
          vendorColumns: prev.vendorColumns.filter((_, idx) => idx !== index),
        }));
      }
      const data = Object.keys(columnsData[0].columns)
        .filter((item) => item !== defaults?.vendorColumns[index].field)
        .map((item) => item);

      if (userConfirmed) {
        await addColumns({
          path: '/settings/inventory/masters',
          data: {
            ...columnsData[0],
            columns: data,
          },
        });
      }
    } else {
      const userConfirmed = await customConfirm('Are you sure?');
      if (userConfirmed) {
        setDefaults((prev) => ({
          ...prev,
          vendorColumns: prev.vendorColumns.filter((_, idx) => idx !== index),
        }));
      }
      const data = Object.keys(columnsData[0].columns)
        .filter((item) => item !== defaults?.vendorColumns[index].field)
        .map((item) => item);

      if (userConfirmed) {
        await addColumns({
          path: '/settings/inventory/masters',
          data: {
            ...columnsData[0],
            columns: data,
          },
        });
      }
    }
  };

  return (
    <>
      <div className="w-full">
        <div className="flex gap-3">
          <Input
            type="text"
            value={name}
            placeholder="Please enter name"
            onChange={handleInput}
            disabled={isUpdateDefaultsLoading || isAddColumnsLoading}
          />
          <Button
            onClick={handleSubmit}
            isLoading={isUpdateDefaultsLoading || isAddColumnsLoading}
          >
            Add
          </Button>
        </div>
        <ol>
          {defaults?.vendorColumns?.map((val, index) => (
            <React.Fragment key={index}>
              <div className="w-full md:w-[270px] my-1  h-auto flex justify-between">
                <span className="ml-2 text-sm">
                  {index + 1}
                  {'.'}
                </span>
                <li className="text-sm ml-3 w-full">{val.title}</li>
                {![
                  'name',
                  'address',
                  'contact',
                  'gstin',
                  'emailId',
                  'billingAddress',
                  'deliveryAddress',
                  'paymentTerm',
                  'email',
                ].includes(val.field) && (
                  <TrashIcon
                    className="cursor-pointer text-red-500 hover:fill-red-900 w-6 h-6"
                    onClick={() => handleColDelete(index, val.isUsed)}
                  />
                )}
              </div>
              <hr className="w-[310px]" key={`hr-${index}`} />
            </React.Fragment>
          ))}
        </ol>
      </div>
    </>
  );
}
