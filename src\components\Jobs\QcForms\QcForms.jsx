import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import AddIcon from '../../../assets/images/add.png';
import Button from '../../global/components/Button';
// import Modal from '../../global/components/Modal';
import Select from '../../global/components/Select';
import Table from '../../global/components/Table';
import DynamicForm from './DynamicForm';
import CreateFormFullScreenModal from '../../global/components/CreateFormFullScreenModal';

const TrComponent = ({
  pIdx,
  pro,
  allQCForms,
  allQCData,
  setAllQCData,
  qr,
  setqr,
  // goalsData,
  goalsTables,
  disableButtons,
}) => {
  const [fName, setfName] = useState('');
  const [currForm, setCurrForm] = useState(null);
  const [saved, setSaved] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({});
  const [percent, setPercent] = useState(0);
  const [individualBatchSize, setIndividualBatchSize] = useState([]);
  const [showRecording, setShowRecording] = useState(false);

  useEffect(() => {
    const exists = allQCData?.find((item) => item?.flowId === pro?._id);
    if (exists) {
      setSaved(true);
      setFormData(exists?.refData || {});

      setCurrForm(
        allQCForms.find(
          (item) => item._id === (exists?.formId?._id || exists?.formId)
        )
      );
    }
  }, [allQCData, pro, allQCForms]);

  useEffect(() => {
    const temp = goalsTables.filter((item) => item?.flowId === pro?._id)[0];

    const tableData = temp?.tableData;

    setIndividualBatchSize([]);
    tableData?.map((item) => {
      setIndividualBatchSize((prev) => [
        ...prev,
        {
          batchNo: item?.batchNo,
          batchSize: item?.newBatchSize || item['Batch Size'],
        },
      ]);
    });
  }, [goalsTables]); // eslint-disable-line

  const changeHandler = (e, pro) => {
    // setAllQCData(allQCData.filter((item) => item?.processId !== pro.mqtt._id));
    setAllQCData(allQCData.filter((item) => item?.flowId !== pro?._id));
    const data = {
      flowId: pro._id,
      processId: pro.mqtt._id,
      formId: e.target.value,
    };

    if (currForm?._id !== e.target.value) {
      setFormData({});
    }
    setCurrForm(allQCForms.find((item) => item._id === e.target.value));
    setfName(e.target.value);
    setAllQCData((prev) => [...prev, data]);
  };

  const handleSubmit = () => {
    if (formData?.Sampling) {
      if (
        !formData.hasOwnProperty('interval') || // eslint-disable-line
        formData?.interval === '' ||
        !formData.hasOwnProperty('samples') || // eslint-disable-line
        formData?.samples === ''
      ) {
        toast.error('Check for value of interval or no of samples entered!');
        return;
      }
    }

    if (formData?.GenerateQR) {
      setqr(pro._id);
    } else {
      setqr('');
    }

    setAllQCData((prev) =>
      prev?.map((data) => {
        if (data?.flowId === pro?._id) {
          return { ...data, refData: formData };
        }
        return data;
      })
    );
    setSaved(true);
    setIsOpen(false);
  };

  const formExists = allQCData?.find((item) => item?.flowId === pro?._id);

  const isStarted = !!goalsTables?.find((item) => item.mqtt === pro?.mqtt?._id)
    ?.tableData?.[0]?.status;

  return (
    <>
      <Table.Row>
        <Table.Td>{pIdx + 1}</Table.Td>
        <Table.Td>{pro.processName}</Table.Td>
        <Table.Td>
          <div className="flex">
            <Button
              type="button"
              disabled={disableButtons && (formExists?._id || isStarted)}
              onClick={() => setIsOpen((prev) => !prev)}
              className="bg-blue-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mt-2"
            >
              <img
                src={AddIcon}
                alt="Add Icon"
                className="w-5 h-5 object-contain relative"
              />
              {saved ? 'Edit' : 'Add'}
            </Button>

            {saved && (
              <p className="font-normal text-green-400 w-fit px-4 py-1 mt-3  ml-4">
                Saved
              </p>
            )}
          </div>
        </Table.Td>

        {isOpen && (
          <CreateFormFullScreenModal
            title={'Assign Form'}
            description={'Assign form to this project'}
            onClose={setIsOpen}
            onSubmit={handleSubmit}
            canSubmit={false}
          >
            <>
              <Select
                // disabled={disable}
                value={fName}
                onChange={(e) => changeHandler(e, pro)}
                options={allQCForms?.map((option) => ({
                  name: option.formName,
                  value: option._id,
                }))}
                placeholder="Select Form"
              />
              {/* eslit-disable-next-line */}
              {currForm && (
                <DynamicForm
                  formFields={currForm?.formData}
                  onSubmit={handleSubmit}
                  processId={pro.mqtt._id}
                  setSaved={setSaved}
                  formData={formData}
                  setFormData={setFormData}
                  isQrDisabled={qr !== pro._id && qr}
                  percent={percent}
                  setPercent={setPercent}
                  individualBatchSize={individualBatchSize}
                  showRecording={showRecording}
                  setShowRecording={setShowRecording}
                />
              )}
            </>
          </CreateFormFullScreenModal>
        )}
      </Table.Row>
    </>
  );
};

const QcForms = ({
  allQCData,
  setAllQCData,
  // saved,
  // setSaved,
  processes,
  allQCForms = [],
  qr,
  setqr,
  goalsTables,
  goalsData,
  disableButtons = false,
}) => {
  const qcProcess = processes?.filter((itm) => itm.processCategory === 'QC');

  return (
    <div className="w-full h-full gap-x-5 gap-y-2">
      <Table className="w-full">
        <Table.Head>
          <Table.Row>
            <Table.Th>#</Table.Th>
            <Table.Th>Process</Table.Th>
            <Table.Th>Form</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {qcProcess?.map((pro, pIdx) => {
            return (
              <TrComponent
                key={pIdx}
                pIdx={pIdx}
                pro={pro}
                allQCForms={allQCForms}
                allQCData={allQCData}
                setAllQCData={setAllQCData}
                // saved={saved}
                // setSaved={setSaved}
                qr={qr}
                setqr={setqr}
                goalsData={goalsData}
                goalsTables={goalsTables}
                disableButtons={disableButtons}
              />
            );
          })}
        </Table.Body>
      </Table>
    </div>
  );
};

export default QcForms;
