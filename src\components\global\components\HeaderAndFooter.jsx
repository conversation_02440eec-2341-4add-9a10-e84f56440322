import { useEffect, useState } from 'react';
import { useLazyGetHeaderAndFooterQuery } from '../../../slices/headerApiSlice';
import Modal from './Modal';

const HeaderAndFooter = ({ onCloseModal, id }) => {
  const [headerImg, setHeaderImg] = useState('');
  const [footerImg, setFooterImg] = useState('');
  const [headerImgHeight, setHeaderImgHeight] = useState(50);
  const [headerImgWidth, setHeaderImgWidth] = useState(50);
  const [footerImgHeight, setFooterImgHeight] = useState(50);
  const [footerImgWidth, setFooterImgWidth] = useState(50);
  const [headerImgPosition, setHeaderImgPosition] = useState('start');
  const [footerImgPosition, setFooterImgPosition] = useState('start');
  const [headerText, setHeaderText] = useState('');
  const [footerText, setFooterText] = useState('');
  const [headerTextPosition, setHeaderTextPosition] = useState('start');
  const [headerTextFont, setHeaderTextFont] = useState('normal');
  const [headerTextSize, setHeaderTextSize] = useState('lg');
  const [footerTextFont, setFooterTextFont] = useState('normal');
  const [footerTextSize, setFooterTextSize] = useState('lg');
  const [footerTextPosition, setFooterTextPosition] = useState('start');
  const [getHeaderAndFooter] = useLazyGetHeaderAndFooterQuery();

  useEffect(() => {
    if (id) {
      (async () => {
        const response = await getHeaderAndFooter({ id }).unwrap();
        const data = response.headerAndFooterById;
        setHeaderImg(data.headerImg);
        setHeaderImgHeight(data.headerImgHeight);
        setHeaderImgWidth(data.headerImgWidth);
        setHeaderImgPosition(data.headerImgPosition);
        setHeaderText(data.headerText);
        setHeaderTextFont(data.headerTextFont);
        setHeaderTextSize(data.headerTextSize);
        setHeaderTextPosition(data.headerTextPosition);
        setFooterImg(data.footerImg);
        setFooterImgHeight(data.footerImgHeight);
        setFooterImgWidth(data.footerImgWidth);
        setFooterImgPosition(data.footerImgPosition);
        setFooterText(data.footerText);
        setFooterTextFont(data.footerTextFont);
        setFooterTextSize(data.footerTextSize);
        setFooterTextPosition(data.footerTextPosition);
      })();
    }
  }, [getHeaderAndFooter, id]);

  const renderPreview = () => {
    return (
      <>
        <section className="py-1 px-5 mt-1">
          <p className="mb-2">Header:</p>
          <div className="border-t border-l border-r py-2 px-3 flex items-center">
            {headerText && (
              <span
                className={`flex w-full justify-${headerTextPosition} text-${headerTextSize} font-${headerTextFont}`}
              >
                {headerText}
              </span>
            )}
          </div>
          {headerImg && (
            <div
              className={`flex justify-${headerImgPosition} w-full items-center`}
            >
              <img
                src={headerImg}
                style={{
                  height: `${headerImgHeight}px`,
                  width: `${headerImgWidth}px`,
                }}
                alt=""
              />
            </div>
          )}
        </section>
        <section className="py-1 px-5 mt-1">
          <p className="mb-2">Footer:</p>
          <div className="border-t border-l border-r py-2 px-3 flex items-center">
            {footerText && (
              <span
                className={`flex w-full justify-${footerTextPosition} text-${footerTextSize} font-${footerTextFont}`}
              >
                {footerText}
              </span>
            )}
          </div>
          {footerImg && (
            <div
              className={`flex justify-${footerImgPosition} w-full items-center`}
            >
              <img
                src={footerImg}
                style={{
                  height: `${footerImgHeight}px`,
                  width: `${footerImgWidth}px`,
                }}
                alt=""
              />
            </div>
          )}
        </section>
      </>
    );
  };

  return <Modal onCloseModal={onCloseModal}>{renderPreview}</Modal>;
};

export default HeaderAndFooter;
