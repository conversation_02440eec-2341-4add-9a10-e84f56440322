import {
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Space,
  Typography,
  Spin,
} from 'antd';
import { useState } from 'react';
import UploadButton from '../UploadButton';
import { toast } from 'react-toastify';
import {
  FileTextOutlined,
  DollarOutlined,
  CommentOutlined,
  PaperClipOutlined,
} from '@ant-design/icons';
import { useGetCustomColumnsQuery } from '../../slices/customCoulmn.ApiSlice';
import MediaCardsPreview from '../global/components/MediaCardsPreview';

const { Title, Text } = Typography;

const InvoiceModalV2 = ({ showModal, setShowModal, setInvoiceData }) => {
  const [form] = Form.useForm();
  const [invoiceDetails, setInvoiceDetails] = useState({
    invoiceId: '',
    invoiceComments: '',
    files: [],
    invoiceAmount: 0,
    additionalFields: [],
  });
  const { data: allCustomCols = [], isLoading } = useGetCustomColumnsQuery();

  const customColumns =
    allCustomCols?.filter((col) => col?.pageName === 'grn/invoice') || [];

  const handleInputChange = (name, value) => {
    setInvoiceDetails((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCustomColumnChange = (columnId, name, value) => {
    // Check if field already exists
    const fieldIndex = invoiceDetails.additionalFields.findIndex(
      (field) => field.name === name
    );

    if (fieldIndex >= 0) {
      // Update existing field
      const updatedFields = [...invoiceDetails.additionalFields];
      updatedFields[fieldIndex] = { name, value, columnId };
      setInvoiceDetails((prev) => ({
        ...prev,
        additionalFields: updatedFields,
      }));
    } else {
      // Add new field
      setInvoiceDetails((prev) => ({
        ...prev,
        additionalFields: [...prev.additionalFields, { name, value, columnId }],
      }));
    }
  };

  const handleMediaUpload = (e) => {
    for (let i in e) {
      if (i === 'length') return;

      const file = e[i];
      const fileName = file.name;
      const fileType = file.type;

      // Check for duplicate files
      const isDuplicate = invoiceDetails?.files?.some(
        (existingFile) => existingFile?.name === fileName
      );
      if (isDuplicate) {
        toast.error('File with the same name already added');
        continue;
      }

      const fr = new FileReader();
      fr.readAsDataURL(file);
      fr.addEventListener('load', () => {
        const url = fr.result;
        const fileData = {
          name: fileName,
          type: fileType,
          data: url,
          size: file.size,
        };

        setInvoiceDetails((prev) => ({
          ...prev,
          files: [...(prev.files || []), fileData],
        }));
      });
    }
  };

  const handleDeleteFile = (fileToDelete) => {
    setInvoiceDetails((prev) => ({
      ...prev,
      files: prev.files.filter((file) => file.name !== fileToDelete.name),
    }));
  };

  const handleSubmit = () => {
    form
      .validateFields()
      .then(() => {
        setInvoiceData((prev) => [...prev, invoiceDetails]);
        setShowModal(false);
        resetForm();
      })
      .catch(() => {
        toast.error('Please complete all required fields');
      });
  };

  const resetForm = () => {
    form.resetFields();
    setInvoiceDetails({
      invoiceId: '',
      invoiceComments: '',
      files: [],
      invoiceAmount: 0,
      additionalFields: [],
    });
  };

  const handleCancel = () => {
    setShowModal(false);
    resetForm();
  };

  return (
    <Modal
      title={
        <Space direction="vertical" size={0} style={{ width: '100%' }}>
          <Title level={4} style={{ margin: 0 }}>
            Add Invoice Entries
          </Title>
          <Text type="secondary">
            Add one or more invoice entries. Required fields are marked with an
            asterisk (*).
          </Text>
        </Space>
      }
      open={showModal}
      onCancel={handleCancel}
      okText="Save Entries"
      width={800}
      onOk={handleSubmit}
      centered
      styles={{
        body: {
          maxHeight: 'calc(100vh - 220px)',
          overflowY: 'auto',
          overflowX: 'hidden',
          padding: '8px',
        },
        header: {
          borderBottom: '1px solid #f0f0f0',
          padding: '8px',
          marginBottom: 0,
        },
        footer: {
          borderTop: '1px solid #f0f0f0',
          padding: '8px',
        },
      }}
    >
      {isLoading ? (
        <Spin />
      ) : (
        <Form
          form={form}
          layout="vertical"
          requiredMark={true}
          style={{ overflowX: 'hidden' }}
          size="middle"
        >
          <Row gutter={[16, 8]}>
            <Col span={12}>
              <Form.Item
                name="invoiceId"
                label="Invoice ID"
                rules={[{ required: true, message: 'Please enter invoice ID' }]}
                style={{ marginBottom: '8px' }}
              >
                <Input
                  prefix={<FileTextOutlined style={{ color: '#bfbfbf' }} />}
                  placeholder="Enter invoice ID"
                  value={invoiceDetails.invoiceId}
                  onChange={(e) =>
                    handleInputChange('invoiceId', e.target.value)
                  }
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="invoiceAmount"
                label="Amount"
                rules={[
                  { required: true, message: 'Please enter invoice amount' },
                ]}
                style={{ marginBottom: '8px' }}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  prefix={<DollarOutlined style={{ color: '#bfbfbf' }} />}
                  placeholder="Enter amount"
                  min={0}
                  step={0.01}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
                  value={invoiceDetails.invoiceAmount}
                  onChange={(value) =>
                    handleInputChange('invoiceAmount', value)
                  }
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                name="invoiceComments"
                label="Comments"
                style={{ marginBottom: '8px' }}
              >
                <Input.TextArea
                  prefix={<CommentOutlined style={{ color: '#bfbfbf' }} />}
                  placeholder="Enter comments about the invoice"
                  rows={2}
                  value={invoiceDetails.invoiceComments}
                  onChange={(e) =>
                    handleInputChange('invoiceComments', e.target.value)
                  }
                />
              </Form.Item>
            </Col>

            {customColumns.map((col) => (
              <Col span={12} key={col?._id}>
                <Form.Item
                  name={`custom_${col?.columnName}`}
                  label={col?.columnName}
                  rules={[
                    {
                      required: col?.isRequired,
                      message: `Please enter ${col?.columnName.toLowerCase()}`,
                    },
                  ]}
                  style={{ marginBottom: '8px' }}
                >
                  <Input
                    placeholder={`Enter ${col?.columnName.toLowerCase()}`}
                    onChange={(e) =>
                      handleCustomColumnChange(
                        col?._id,
                        col?.columnName,
                        e.target.value
                      )
                    }
                  />
                </Form.Item>
              </Col>
            ))}
          </Row>

          <Divider orientation="left" style={{ margin: '8px 0 16px' }}>
            <Space>
              <PaperClipOutlined />
              <span>Attachments</span>
            </Space>
          </Divider>

          <Form.Item name="attachments" style={{ marginBottom: '8px' }}>
            <div>
              <UploadButton
                accept="*"
                fileType="JPG/PNG/PDF"
                svg={<PaperClipOutlined />}
                multiple
                onChange={handleMediaUpload}
                buttonText="Upload Files"
                className="mb-4"
              />
              <MediaCardsPreview
                medias={invoiceDetails?.files}
                onDelete={handleDeleteFile}
              />
            </div>
          </Form.Item>
        </Form>
      )}
    </Modal>
  );
};

export default InvoiceModalV2;
