import { useRef } from 'react';

const Import = ({ onChange, svg, accept, multiple, disabled }) => {
  const inputRef = useRef();

  // triggers when file is selected with click
  const handleChange = (e) => {
    e.preventDefault();

    if (e.target.files && e.target.files[0] && !multiple) {
      onChange(e.target.files[0]);
    } else if (e.target.files && multiple) {
      onChange(e.target.files);
    }
    e.target.value = '';
  };

  // triggers the input when the button is clicked
  const onClick = () => {
    inputRef.current.click();
  };

  return (
    <form
      onSubmit={(e) => e.preventDefault()}
      onClick={onClick}
      className={`relative  rounded-xl border-2 border-black/30 border-dashed flex justify-center items-center ${disabled ? 'hover:cursor-not-allowed' : 'hover:cursor-pointer'}`}
    >
      <input
        ref={inputRef}
        type="file"
        className="hidden"
        accept={accept}
        onChange={handleChange}
        multiple={multiple}
        disabled={disabled}
      />
      <div>
        <div className="flex flex-row items-center"></div>
        <div className="flex flex-col items-center">
          <div> {svg && svg}</div>

          <div className="ml-4">
            <p className="text-black font-semibold py-1.5">
              <span className="text-blue-600 text-decoration-line: underline mr-3 ">
                Browse
              </span>
            </p>
          </div>
        </div>
      </div>
    </form>
  );
};

export default Import;
