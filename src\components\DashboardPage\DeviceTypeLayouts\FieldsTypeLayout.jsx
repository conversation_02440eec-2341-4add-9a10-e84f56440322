import { connect } from 'mqtt/dist/mqtt';
import { useContext, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { calculate } from '../../../calculateString';
import { Store } from '../../../store/Store';
import ColoredCircle from '../../ActiveStatus/ActiveStatus';

const ValueType = ({ value }) => {
  const valLength = value ? `${value}`.length : 1;
  return (
    <span
      className={`font-bold ${
        valLength <= 2
          ? 'text-[1.63rem]'
          : valLength <= 4
          ? 'text-[1.4rem]'
          : valLength <= 6
          ? 'text-[1rem]'
          : 'text-[0.8rem]'
      }`}
    >
      {+value || 0}
    </span>
  );
};

const ButtonType = ({ field, device }) => {
  const {
    defaults: { mqttUrl },
  } = useContext(Store);

  const publishHandler = () => {
    const client = connect(mqttUrl);

    client.on('connect', () => {
      console.log('button connected'); // eslint-disable-line
      client.publish(
        device.topics.find((item) => item[field.name])?.[field.name],
        '1',
        () => {
          console.log('published'); // eslint-disable-line
          client.end();
        }
      );
    });
  };

  return (
    <button
      type="button"
      onClick={publishHandler}
      className={`px-6 py-1 rounded-lg bg-white hover:bg-slate-100 font-medium ${
        field.name.includes('reset') || field.name.includes('Reset')
          ? 'text-red-primary'
          : 'text-blue-primary'
      }`}
    >
      {field.name}
    </button>
  );
};

const FormulaType = ({ field, values, device, notInSettingsPage }) => {
  const formula = field.formula;
  let variables = [];
  const tempVars = [...formula.matchAll(/[a-zA-Z]+[0-9]*/g)];
  tempVars.forEach((vari) => {
    if (!variables.includes(vari[0])) {
      variables.push(vari[0]);
    }
  });

  let str = formula;
  let check = [];

  variables.forEach((vari) => {
    const value = notInSettingsPage
      ? values?.[device.assignedDevice[field.deviceNo - 1].deviceId]?.[
          vari.toUpperCase()
        ]
      : 0;

    if (value) {
      str = str.replaceAll(vari, value);

      check = [...check, '1'];
    }
  });
  let val = 0;
  if (check.length === variables.length) {
    const temp = calculate(str);

    if (+temp < 0) {
      val = +temp * -1;
    } else {
      val = temp;
    }
  }

  const valLength = `${val}`.length;
  return (
    <span
      className={`font-bold ${
        valLength <= 2
          ? 'text-[1.63rem]'
          : valLength <= 4
          ? 'text-[1.4rem]'
          : valLength <= 6
          ? 'text-[1rem]'
          : 'text-[0.8rem]'
      }`}
    >
      {+val || 0}
    </span>
  );
};

const InputType = ({ field, device }) => {
  const [inputVal, setInputVal] = useState('0');

  const {
    defaults: { mqttUrl },
  } = useContext(Store);

  const publishHandler = () => {
    const client = connect(mqttUrl);

    client.on('connect', () => {
      console.log('input field connected'); // eslint-disable-line
      client.publish(
        device.topics.find((item) => item[field.name])?.[field.name],
        inputVal,
        () => {
          console.log('published'); // eslint-disable-line
          client.end();
        }
      );
    });
  };

  return (
    <div className="w-full flex justify-evenly">
      <input
        className="w-1/2 border p-1 text-[1.63rem] font-bold border-white rounded-lg bg-transparent outline-none"
        value={inputVal}
        onChange={(e) => setInputVal(e.target.value)}
      />
      <button
        className="w-1/3 outline-none border border-white rounded-lg"
        onClick={publishHandler}
      >
        Send
      </button>
    </div>
  );
};

const FieldsTypeLayout = ({ field, values, device }) => {
  const { pathname } = useLocation();
  const notInSettingsPage = !pathname.includes('/settings/');

  const value = notInSettingsPage
    ? values?.[device.assignedDevice[field.deviceNo - 1].deviceId]?.[
        field.type === 'status' ? 'STATUS' : field.name.toUpperCase()
      ]
    : 0;

  switch (field.type) {
    case 'status':
      return (
        <ColoredCircle
          status={
            value === '1' ? 'active' : value === '0' ? 'inactive' : 'pause'
          }
        />
      );
    case 'toggle':
      return (
        <ColoredCircle
          status={
            value === '1' ? 'active' : value === '0' ? 'inActive' : 'pause'
          }
        />
      );
    case 'value':
      return <ValueType value={value} />;
    case 'button':
      return <ButtonType field={field} device={device} />;
    case 'formula':
      return (
        <FormulaType
          field={field}
          values={values}
          device={device}
          notInSettingsPage={notInSettingsPage}
        />
      );
    case 'input':
      return <InputType field={field} device={device} />;
    default:
      return (
        <p className="text-center text-sm">{`No such type as ${field.type}`}</p>
      );
  }
};

export default FieldsTypeLayout;
