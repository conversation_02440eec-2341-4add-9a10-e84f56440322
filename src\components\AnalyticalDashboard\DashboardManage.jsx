import { Fragment } from 'react';
import CreateAnaBarGraph from './CreateAnaBarGraph';
import CreateAnaGraph from './CreateAnaGraph';
import CreateAnaHorizontal from './CreateAnaHorizontal';
import Table from './Table';
import Tile from './Tile';

const DashboardManage = ({ chartDatas }) => {
  const render = (type, params, mid, pid) => {
    switch (type) {
      case 'Horizontal Bar':
        return (
          <section className="w-[100%] bg-white mb-3 rounded-lg shadow-low px-2 py-1 ">
            <CreateAnaHorizontal params={params} pid={pid} />
          </section>
        );

      case 'Vertical Bar':
        return (
          <section className="w-[100%] bg-white mb-3 rounded-lg shadow-low px-2 py-1 ">
            <CreateAnaBarGraph params={params} pid={pid} />
          </section>
        );

      case 'Line':
        return (
          <section className="w-[100%] bg-white mb-3 rounded-lg shadow-low px-2 py-1 ">
            <CreateAnaGraph params={params} mid={mid} />
          </section>
        );

      case 'Tables':
        return (
          <section className="w-[100%] bg-white mb-3 rounded-lg shadow-low px-2 py-1 ">
            <Table params={params} pid={pid} />
          </section>
        );

      case 'Tiles':
        return (
          <section className="w-[100%] bg-white mb-3 cols-span-2 rounded-lg shadow-low px-2 py-1 ">
            <Tile params={params} mid={mid} pid={pid} />
          </section>
        );

      default:
        return null;
    }
  };
  return (
    <div className="grid grid-cols-2 gap-3 h-full">
      {chartDatas.map((item, idx) => {
        return (
          <Fragment key={idx}>
            {render(
              item.type || '',
              item.selectY || '',
              item.selectedMac || '',
              item.selectedProject
            )}
          </Fragment>
        );
      })}
    </div>
  );
};

export default DashboardManage;
