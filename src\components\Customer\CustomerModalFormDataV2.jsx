import {
  BankOutlined,
  DeleteOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  HomeOutlined,
  LockOutlined,
  MailOutlined,
  PhoneOutlined,
  PlusOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import { Button, Divider, Form, Input, Modal, Tabs, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { renderFieldsBasedOnType } from '../../helperFunction';
import usePrefixIds from '../../hooks/usePrefixIds';
import {
  useCreateCustomerMutation,
  useEditCustomerMutation,
  useGetLatestCustomerQuery,
} from '../../slices/customerDataSlice';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice';
import Select from '../global/components/Select';
import { Label } from '../v2';

const { Title } = Typography;
const { TabPane } = Tabs;

const CustomerModalFormDataV2 = ({
  open,
  onClose,
  initialData,
  onSuccess,
  title = 'Add Customer',
  customColumns = [],
}) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('1');
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [additionalFields, setAdditionalFields] = useState(null);
  const [createCustomer] = useCreateCustomerMutation();
  const { data: latestCustomer } = useGetLatestCustomerQuery();
  const [editCustomer] = useEditCustomerMutation();
  const [getTemplates, { data: templatesData, isError: isTemplateError }] =
    useLazyQueryTemplateByIdQuery();
  const { IdGenComp, idCompData } = usePrefixIds({ idFor: 'customerId' });
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [ColumnsValue, setColumnsValue] = useState([]);
  const [searchParams] = useSearchParams();

  useEffect(() => {
    if (open) {
      form.setFieldsValue(
        initialData || {
          name: '',
          company_name: '',
          unique_id: [''],
          phone_no: [''],
          address: [''],
          gstNumber: [''],
          billingAddress: [''],
          deliveryAddress: [''],
          paymentTerm: '',
          password: '',
          confirmPassword: '',
          customColumns: [],
        }
      );
    }
  }, [open, initialData, form]);

  useEffect(() => {
    const getCols = async () => {
      try {
        const path = '/settings/config/customer';
        await getTemplates({ path });
      } catch (error) {
        toast.error('Failed to load templates');
      }
    };
    getCols();
  }, [getTemplates]);

  useEffect(() => {
    const setIdFormatFunc = () => {
      if (!latestCustomer?.customer?.additionalFields?._id) {
        if (templatesData) {
          const defaultTemplate = templatesData?.find((template) =>
            template.name.startsWith('Default')
          );
          setAdditionalFields(defaultTemplate);
          setSelectedTemplate(defaultTemplate);
        }
      } else {
        const templateParamsId =
          searchParams.get('templateId') === 'undefined'
            ? null
            : searchParams.get('templateId');
        const templateToUse = templatesData?.find((template) => {
          return (
            template?._id ===
            (templateParamsId
              ? templateParamsId
              : latestCustomer?.customer?.additionalFields?._id)
          );
        });
        setSelectedTemplate(templateToUse);
        setAdditionalFields(templateToUse);
      }
    };
    if (initialData?._id) {
      const templateToUse = templatesData?.find((template) => {
        return template?._id === initialData?.additionalFields?._id;
      });
      setSelectedTemplate(templateToUse);
      setAdditionalFields(initialData?.additionalFields);
      setColumnsValue(initialData?.customColumns);
    } else {
      setIdFormatFunc();
    }
  }, [
    searchParams,
    latestCustomer,
    templatesData,
    initialData?.additionalFields,
    initialData?._id,
    initialData?.customColumns,
  ]);

  const handleSubmit = async (values) => {
    try {
      if (!values.company_name?.trim()) {
        toast.error('Company Name is required');
        return;
      }

      if (values.gstNumber?.[0]?.length > 15) {
        toast.error('GST Number should not exceed 15 characters');
        return;
      }

      if (values.password !== values.confirmPassword && values.password) {
        toast.error('Passwords do not match');
        return;
      }

      // Filter out empty arrays and remove confirmPassword
      const cleanedValues = {
        ...values,
        unique_id: values.unique_id.filter(Boolean),
        phone_no: values.phone_no.filter(Boolean),
        address: values.address.filter(Boolean),
        billingAddress: values.billingAddress.filter(Boolean),
        // deliveryAddress: values.deliveryAddress.filter(Boolean),
        additionalFields: additionalFields || {},
        customColumns: ColumnsValue,
      };
      delete cleanedValues.confirmPassword;
      if (initialData?._id) {
        await editCustomer({
          body: cleanedValues,
          id: initialData._id,
        }).unwrap();
        toast.success('Customer updated successfully');
      } else {
        await createCustomer(cleanedValues).unwrap();
        toast.success('Customer added successfully');
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      toast.error(error.message || 'Failed to save customer');
    }
  };

  const renderDynamicFields = (fieldName, label, icon = null) => (
    <Form.List name={fieldName}>
      {(fields, { add, remove }) => {
        // Ensure at least one field is present initially
        if (fields.length === 0) {
          add(); // Add an empty field if none exist
        }
        return (
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">{label}</span>
              <Button
                type="text"
                size="small"
                onClick={() => add()}
                icon={<PlusOutlined />}
                className="text-blue-500 hover:text-blue-600"
              >
                Add
              </Button>
            </div>
            {fields.map((field) => (
              <div key={field.key} className="flex gap-2 mb-2">
                <Form.Item
                  {...field}
                  className="flex-1 mb-0"
                  rules={
                    fieldName === 'phone_no'
                      ? [
                          { required: false },
                          {
                            pattern: /^\d{10}$/,
                            message:
                              'Enter a valid phone number (10 digits) without spaces or dashes',
                          },
                        ]
                      : undefined
                  }
                >
                  <Input
                    placeholder={`Enter ${label.toLowerCase()}`}
                    prefix={icon}
                    size="middle"
                    addonBefore={
                      fieldName === 'phone_no' && (
                        <span className="text-gray-400">+91</span>
                      )
                    }
                  />
                </Form.Item>
                {fields.length > 1 && (
                  <Button
                    size="small"
                    type="text"
                    icon={<DeleteOutlined />}
                    className="text-red-500 hover:text-red-600"
                    onClick={() => remove(field.name)}
                  />
                )}
              </div>
            ))}
          </div>
        );
      }}
    </Form.List>
  );

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setTemplateDropDownModal(true);
      setDropdownIdx(idx);
    } else {
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: prev.templateData?.map((el) => {
          if (el.fieldName === fieldName) {
            return {
              ...el,
              fieldValue,
            };
          } else {
            return el;
          }
        }),
      }));
    }
  };
  return (
    <div>
      <Modal
        open={open}
        onCancel={onClose}
        width={800}
        className="customer-modal"
        title={
          <Title level={5} className="!mb-0 !text-gray-800">
            {title}
          </Title>
        }
        footer={
          <div className="flex justify-end gap-2">
            <Button size="small" onClick={onClose}>
              Cancel
            </Button>
            <Button
              size="small"
              type="primary"
              onClick={() => form.submit()}
              className="bg-blue-500"
            >
              {initialData?._id ? 'Update' : 'Create'}
            </Button>
          </div>
        }
        centered
        styles={{
          body: {
            height: `calc(100vh - 150px)`,
            overflowY: 'auto',
          },
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          size="small"
          className="mt-4"
        >
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            size="small"
            className="custom-tabs"
            type="card"
          >
            <TabPane tab="Basic Info" key="1" forceRender>
              <div className="space-y-4">
                <div className="bg-blue-50/50 p-3 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-3">
                    <Label className="!text-sm !text-gray-700 !mb-0">
                      Customer ID:
                    </Label>
                    <IdGenComp {...idCompData} />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <Form.Item
                      name="company_name"
                      label="Company Name"
                      rules={[{ required: true, message: 'Required' }]}
                    >
                      <Input placeholder="Enter company name" size="middle" />
                    </Form.Item>

                    <Form.Item name="name" label="Contact Name">
                      <Input placeholder="Enter contact name" size="middle" />
                    </Form.Item>

                    <Form.Item
                      name="password"
                      label="Password"
                      rules={[{ min: 6, message: 'Min 6 characters' }]}
                    >
                      <Input.Password
                        placeholder="Enter password"
                        prefix={<LockOutlined />}
                        iconRender={(visible) =>
                          visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                        }
                        size="middle"
                      />
                    </Form.Item>

                    <Form.Item
                      name="confirmPassword"
                      label="Confirm Password"
                      rules={[
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            if (!value || getFieldValue('password') === value) {
                              return Promise.resolve();
                            }
                            return Promise.reject('Passwords do not match');
                          },
                        }),
                      ]}
                    >
                      <Input.Password
                        placeholder="Confirm password"
                        prefix={<LockOutlined />}
                        iconRender={(visible) =>
                          visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                        }
                        size="middle"
                      />
                    </Form.Item>
                  </div>
                </div>

                <div className="space-y-3">
                  {renderDynamicFields(
                    'unique_id',
                    'Email',
                    <MailOutlined className="text-gray-400" />
                  )}
                  {renderDynamicFields(
                    'phone_no',
                    'Phone',
                    <PhoneOutlined className="text-gray-400" />
                  )}
                  {renderDynamicFields(
                    'gstNumber',
                    'GST',
                    <SafetyCertificateOutlined className="text-gray-400" />
                  )}
                </div>
              </div>
            </TabPane>

            <TabPane tab="Address" key="2" forceRender>
              <div className="space-y-3">
                {renderDynamicFields(
                  'address',
                  'Ship To',
                  <HomeOutlined className="text-gray-400" />
                )}
                {renderDynamicFields(
                  'billingAddress',
                  'Bill To',
                  <BankOutlined className="text-gray-400" />
                )}
                {/* {renderDynamicFields(
                  'deliveryAddress',
                  'Delivery Address',
                  <ShopOutlined className="text-gray-400" />
                )} */}
              </div>
            </TabPane>

            <TabPane tab="Additional" key="3" forceRender>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4">
                  <Form.Item
                    label="Template"
                    validateStatus={isTemplateError ? 'error' : ''}
                    help={isTemplateError ? 'Failed to load templates' : ''}
                  >
                    <Select
                      className="w-full"
                      options={templatesData?.map((template) => ({
                        value: template,
                        name: template.name,
                      }))}
                      onChange={(e) => {
                        if (selectedTemplate === e.target.value) {
                          return;
                        }
                        setAdditionalFields(e.target.value);
                        setSelectedTemplate(e.target.value);
                        if (
                          additionalFields &&
                          additionalFields.idIndex === e.target.value.idIndex
                        ) {
                          return;
                        }
                      }}
                      value={selectedTemplate}
                    />
                  </Form.Item>

                  <Form.Item name="paymentTerm" label="Payment Term">
                    <Input
                      placeholder="Enter payment term"
                      size="middle"
                      type="text"
                    />
                  </Form.Item>
                </div>
              </div>
              <Divider className="my-4" />
              {customColumns.length > 0 && (
                <>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <span className="mb-5 text-sm font-semibold">
                      Custom Columns
                    </span>
                    <div className="grid grid-cols-2 gap-4">
                      {customColumns.map((column, idx) => (
                        <div key={idx}>
                          <label>{column.column_name}</label>
                          <Input
                            key={column.column_name}
                            value={
                              ColumnsValue.find(
                                (each) =>
                                  each.column_name === column.column_name
                              )?.column_value || ''
                            }
                            placeholder={`Enter ${column.column_name.toLowerCase()}`}
                            size="middle"
                            onChange={(e) => {
                              const updatedValue = e.target.value;
                              setColumnsValue((prev) => {
                                const index = prev.findIndex(
                                  (each) =>
                                    each.column_name === column.column_name
                                );

                                if (index !== -1) {
                                  const updated = [...prev];
                                  updated[index] = {
                                    ...updated[index],
                                    column_value: updatedValue,
                                  };
                                  return updated;
                                } else {
                                  return [
                                    ...prev,
                                    {
                                      column_name: column.column_name,
                                      column_value: updatedValue,
                                    },
                                  ];
                                }
                              });
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              <Divider className="my-4" />
              {additionalFields?.templateData?.length > 0 && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <span className="mb-5 font-semibold text-sm">
                    Addtional Fields
                  </span>
                  <div className="w-full">
                    <div className="w-full">
                      {/* ANCHOR */}
                      {renderFieldsBasedOnType(
                        additionalFields,
                        handleInputChange,
                        templateDropDownModal,
                        setTemplateDropDownModal,
                        setAdditionalFields,
                        newOptionStatus,
                        setNewOptionStatus,
                        dropdownIdx,
                        setDropdownIdx,
                        searchParams
                      )}
                    </div>
                  </div>
                </div>
              )}
            </TabPane>
          </Tabs>
        </Form>
      </Modal>
    </div>
  );
};

export default CustomerModalFormDataV2;
