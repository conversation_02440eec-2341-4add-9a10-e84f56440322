import { useState } from 'react';
import Button from '../global/components/Button';
import { InfoTooltip } from '../global/components/InfoTooltip';
import Input from '../global/components/Input';
import MultiSelect from '../global/components/MultiSelect';
import CardForm from './CardForm';
import ManualSheet from './ManualSheet';

const Sheet = ({ data, index, setList, masters, options }) => {
  const [isManual, setIsManual] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  return (
    <div>
      {isManual && (
        <ManualSheet
          setIsManual={setIsManual}
          setList={setList}
          index={index}
          info={data}
          isEdit={isEdit}
          setIsEdit={setIsEdit}
        />
      )}
      <div className="flex justify-end">
        <Button
          className={'h-7 !text-xs mr-2'}
          onClick={() => setIsManual(true)}
        >
          {data?.data?.length || data?.data[index]?.sheetData?.data?.length
            ? 'Edit'
            : 'Add Manually '}
        </Button>
      </div>
      <div className="flex w-full  flex-col md:flex-row justify-between items-center px-3">
        <div className="flex flex-col my-5 w-full md:w-[30rem]">
          <div className="flex gap-[5px] items-center">
            <label className="mb-1 font-semibold text-[#667085]">
              Sheet Name{' '}
              <span className="text-xl text-red-500 -mt-1 -ml-1 ">*</span>
            </label>
            <InfoTooltip className="mb-1" id={`sheetName`} position="right">
              This identify the Job template page
            </InfoTooltip>
          </div>

          <Input
            onChange={(e) => {
              setList((prev) =>
                prev?.map((item, iIdx) => {
                  if (iIdx === index) {
                    return { ...item, name: e.target.value };
                  } else {
                    return item;
                  }
                })
              );
            }}
            value={data?.name || ''}
            type="text"
          />
        </div>

        {!data?.data?.find((i) => i.sheetName === 'Manual Sheet') && (
          <div className="flex flex-col my-5 w-full md:w-[30rem]">
            <div className="flex gap-[5px] items-center">
              <label className="mb-1 font-semibold text-[#667085]">
                {' '}
                Select Sheets{' '}
                <span className="text-xl text-red-500 -mt-1 -ml-1 ">*</span>
              </label>
              <InfoTooltip className="mb-1" id={`sheetName`} position="right">
                Use this option to choose the imported Job Parameters sheet you
                wish to utilize for creating your template, streamlining the
                template creation process with ease and flexibility.
              </InfoTooltip>
            </div>

            <MultiSelect
              value={data?.data?.map((i) => ({
                label: i.sheetData.name,
                value: i.sheetData.id,
              }))}
              onChange={(e) => {
                const selectIds = e.target.value?.map((i) => i.value);
                const temp = masters?.filter((dt) => {
                  return selectIds?.includes(dt.sheetData.id);
                });
                setList((prev) =>
                  prev?.map((item, iIdx) => {
                    if (iIdx === index) {
                      return { ...item, data: temp };
                    } else {
                      return item;
                    }
                  })
                );
              }}
              disabled={data?.data?.find((i) => i.sheetName === 'Manual Sheet')}
              options={options}
            />
          </div>
        )}
      </div>
      <div className="grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-3 px-5">
        {data?.data?.map((master, idx) => {
          if (master?.sheetName === 'Manual Sheet') {
            return (
              <label key={idx} className="mb-1 font-semibold text-green-500">
                Sheet added manually
              </label>
            );
          }
          return <CardForm count={idx} key={idx} master={master} />;
        })}
      </div>
    </div>
  );
};

export default Sheet;
