import { useState, useEffect } from 'react';
import FieldsTypeLayout from './FieldsTypeLayout';

const GridLayout = ({ device, values }) => {
  const [buttonFields, setButtonFields] = useState([]);
  const [inputFields, setInputFields] = useState([]);

  useEffect(() => {
    if (device) {
      setButtonFields(
        device.deviceType.fields.filter((field) => field.type === 'button')
      );
      setInputFields(
        device.deviceType.fields.filter((field) => field.type === 'input')
      );
    }
  }, [device]);

  return (
    <div className={`grid grid-cols-${device.deviceType.columnCount} gap-px`}>
      {device &&
        [...Array(device.deviceType.layoutCount)].map((layout, lIdx) => {
          const layoutButtons = buttonFields.filter(
            (field) => field.layoutNo === lIdx + 1
          );

          const layoutInputs = inputFields.filter(
            (field) => field.layoutNo === lIdx + 1
          );

          return (
            <div key={lIdx} className={`text-center pb-1`}>
              <p className="w-full text-center text-[0.69rem]">
                Layout {lIdx + 1}
              </p>
              <div className="grid grid-cols-2">
                {device.deviceType.fields.map((field, fIdx) => {
                  if (
                    field.type === 'dropdown' ||
                    field.type === 'button' ||
                    field.name === 'Device Status'
                  ) {
                    return null;
                  }
                  if (field.layoutNo === lIdx + 1) {
                    return (
                      <div key={fIdx} className="flex flex-col items-center">
                        <FieldsTypeLayout
                          field={field}
                          values={values}
                          device={device}
                        />
                        <span className="text-[0.88rem] font-medium">
                          {field.name}
                        </span>
                      </div>
                    );
                  } else {
                    return null;
                  }
                })}
              </div>

              {/* input fields */}
              <div className={layoutInputs?.length > 0 ? 'pt-1 pb-2' : ''}>
                {layoutInputs?.map((iField, iIdx) => (
                  <div key={iIdx} className="flex flex-col items-center">
                    <span className="text-sm">{iField.name}</span>
                    <FieldsTypeLayout
                      field={iField}
                      values={values}
                      device={device}
                    />
                  </div>
                ))}
              </div>

              {/* button fields */}
              <div
                className={
                  buttonFields.length > 0
                    ? buttonFields.length > 1
                      ? 'grid grid-cols-2 gap-4 pt-1 pb-2'
                      : 'pb-2 pt-1 text-center'
                    : ''
                }
              >
                {layoutButtons &&
                  layoutButtons.map((bField, bIdx) => (
                    <FieldsTypeLayout
                      key={bIdx}
                      field={bField}
                      values={values}
                      device={device}
                    />
                  ))}
              </div>
            </div>
          );
        })}
    </div>
  );
};

export default GridLayout;
