import { useContext, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import { Disclosure } from '@headlessui/react';
// import MultiDepartmentForm from '../../components/DynamicDepartmentTables/MultiDepartmentForm';
import {
  generateDateString,
  getLocalDate,
  getPartVariantName,
  getProductVariantName,
} from '../../helperFunction';
import { useLazyGetBomByIdQuery } from '../../slices/assemblyBomApiSlice';
import { useLazyGetAssetsQuery } from '../../slices/assetApiSlice';
import { useGetColumnsForKanbanMutation } from '../../slices/columnsApiSlice';
// import { useLazyGetPoByIdQuery } from '../../slices/createPoApiSlice';
// import CustomDepartmentsSidebar from '../../SidebarComponents/CustomDepartmentsSidebar';
import FormManagementCreateModal from '../../SidebarComponents/DepartmentRowSidebarComponents/FormManagementCreateModal';
import FormManagementFillModal from '../../SidebarComponents/DepartmentRowSidebarComponents/FormManagementFillModal';
import DispatchRightSideBar from '../../SidebarComponents/DispatchRightSideBar';
import WorkOrderSideBar from '../../SidebarComponents/WorkOrderSideBar';
import { useLazyGetDepartmentColumnByIdQuery } from '../../slices/departmentColumnApiSlice';
import { useEditDepartmentRowMutation } from '../../slices/departmentRowApiSlice';
import {
  useGetMediaMetaQuery,
  useLazyGetMediaByIdQuery,
} from '../../slices/mediaSlice';
import { Store } from '../../store/Store';
import DynamicDepSideBar from '../DynamicDepartmentDashboards/DynamicDepSideBar';
import AssemblyForm from '../DynamicDepartmentTables/AssemblyForm';
import DepartmentForm from '../DynamicDepartmentTables/DepartmentForm';
import MediaElement from '../DynamicDepartmentTables/MediaElement';
import RFQsidebarData from '../RFQsidebarData';
import SalesInquirySidebar from '../SalesInquiryDashboard/SalesInquirySidebar';
import QuotationRightSidebar from '../SalesOrderManagement/Quotation/QuotationRightSidebar';
import Button from '../global/components/Button';
import CreateFormFullScreenModal from '../global/components/CreateFormFullScreenModal';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import PdfViewer from '../global/components/PdfViewer';
import Table from '../global/components/Table';
import LeadSideBar from '../leads/LeadSideBar';
import PerformaInvoiceSidebar from '../performa-invoice/performa-invoice-sidebar';
import PODashboardSidebar from '../po-dashboard/po-dashboard-sidebar';
import SalesOrderSideBar from '../salesOrder/salesOrderSideBar';
import WorkOrderSidebarV2 from '../v3/WorkOrder/WorkOrderSidebarV2';

const useKanbanSidebar = () => {
  const [Form, setForm] = useState(null);
  const [filledFormData, setFilledFormData] = useState([]);
  const [ShowFormModal, setShowFormModal] = useState(false);
  const [openFileModal, setOpenFileModal] = useState(false);
  const [sharedFiles, _setSharedFiles] = useState(null);

  const { state } = useContext(Store);
  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [editDepartmentRow] = useEditDepartmentRowMutation();
  const getMedia = async (id) => {
    const fileInfo = await getMediaById({ id });
    return fileInfo;
  };
  const [preview, setPreview] = useState({
    openModal: false,
    file: {},
  });

  const { data: media } = useGetMediaMetaQuery();
  const [getAssemblyBom] = useLazyGetBomByIdQuery();
  const [getAssets] = useLazyGetAssetsQuery();
  const [showBom, setShowBom] = useState(false);
  const [bomData, setBomData] = useState(null);
  const [assetData, setAssetData] = useState(false);
  const [showAsset, _setShowAsset] = useState(false);
  const [inPageCols, setInpageCols] = useState(null);
  // const [performaInvoice, setPerformaInvoice] = useState(null);
  const [grnCols, setGrnCols] = useState(null);
  // const [salesInquiryCol, setSalesInquiryCol] = useState(null);
  // const [quotationCols, setQuotationCols] = useState(null);
  // const [salesOrderCols, setSalesOrderCols] = useState(null);
  // const [poData, setPoData] = useState([]);
  // const [showPo, setShowPo] = useState(false);
  const [indentCol, setIndentCol] = useState(null);
  // const [purchaseOrderCol, setPurchaseOrderCol] = useState(null);
  // const [workOrderCol, setWorkOrderCol] = useState(null);
  // const [getPoById] = useLazyGetPoByIdQuery();
  const [showAssemblyForm, setShowAssemblyForm] = useState(false);
  const [comments, setComments] = useState([]);
  const [formManagementModal, setFormManagementModal] = useState(false);
  const [forms, _setForms] = useState([]);
  const [showFillForm, setShowFillForm] = useState(false);
  const [fillForms, _setFillForms] = useState([]);
  const [departmentColumn, _setDepartmentColumn] = useState('');
  const [openedKey, _setOpenedKey] = useState('');
  // const [visibleForms, setVisibleForms] = useState(forms.map(() => true));
  const [showMultipleBom, setShowMultipleBom] = useState(false);
  const [activeForm, setActiveForm] = useState({});
  useEffect(() => {
    (async () => {
      const response = await getAssets().unwrap();
      setAssetData(response);
    })();
  }, [getAssets]);

  const [getColumnsData] = useGetColumnsForKanbanMutation();
  const [getDepartmentColumnById] = useLazyGetDepartmentColumnByIdQuery();

  useEffect(() => {
    if (departmentColumn && openedKey) {
      (async () => {
        const response =
          await getDepartmentColumnById(departmentColumn).unwrap();
        setActiveForm(
          response?.columns?.find((item) => item?.name === openedKey)?.form
        );
      })();
    }
  }, [departmentColumn]); // eslint-disable-line

  useEffect(() => {
    const dataSetterMap = (item) => {
      switch (item?._id?.[0]) {
        case '/inventory/inpage':
          setInpageCols(item?.data?.[0]?.[0]?.columns);
          break;
        case '/inventory/inpage/grn':
          setGrnCols(item?.data?.[0]?.[0]?.columns);
          break;
        // case 'salesordermanagement/SalesInquiryDashboard':
        //   setSalesInquiryCol(item?.data?.[0]?.[0]?.columns);
        //   break;
        // case '/salesordermanagement/quotation':
        //   setQuotationCols(item?.data?.[0]?.[0]?.columns);
        //   break;
        case '/purchase/indent':
          setIndentCol(item?.data?.[0]?.[0]?.columns);
          break;
        // case '/purchase/po':
        //   setPurchaseOrderCol(item?.data?.[0]?.[0]?.columns);
        //   break;
        // case '/jobs/workorder':
        //   setWorkOrderCol(item?.data?.[0]?.[0]?.columns);
        //   break;
        // case '/accountmanagement/performainvoice':
        //   setPerformaInvoice(item?.data?.[0]?.[0]?.columns);
        //   break;

        default:
          return null;
      }
    };
    (async () => {
      const res = await getColumnsData({
        data: {
          paths: [
            '/inventory/inpage',
            '/inventory/inpage/grn',
            '/salesordermanagement/salesinquirydashboard',
            '/salesordermanagement/quotation',
            '/salesordermanagement/orders',
            '/purchase/indent',
            '/purchase/po',
            '/jobs/workorder',
            '/accountmanagement/performainvoice',
          ],
        },
      }).unwrap();

      res.forEach((item) => {
        dataSetterMap(item);
      });
    })();
  }, [getColumnsData]);

  // const toggleFormVisibility = (index) => {
  //   const updatedVisibility = [...visibleForms];
  //   updatedVisibility[index] = !updatedVisibility[index];
  //   setVisibleForms(updatedVisibility);
  // };

  const [displayedBom, setDisplayedBom] = useState(-1);

  const _handleDepartMentRowComments = async (data, item, d) => {
    const promises = data[item][d]?.value?.map(async (id) => {
      return await getAssemblyBom({ id }).unwrap();
    });

    const selectedBom = await Promise.all(promises);
    let allComments = selectedBom.map((bom) => {
      let productListAfterComments = bom.children.map((j) => {
        let foundComment = data[item][d]?.comments?.find(
          (comment) => comment._id === bom._id
        );

        let itemComment = foundComment?.comments?.find(
          (comment) =>
            comment.id ===
            (j.part?._id ||
              j.product?._id ||
              j.productVariant?._id ||
              j.partVariant?._id ||
              j.manualEntry)
        );

        return {
          ...j,
          comment: itemComment?.comment || '',
        };
      });

      return { ...bom, productList: productListAfterComments };
    });

    setBomData(allComments);
    setComments(allComments);
    setShowMultipleBom(true);
  };

  const handleSaveFormData = async (data, refetch, setRefetch) => {
    let finalData = data;
    if (
      state?.user?.approvalAccess?.includes('isEditableKanban') ||
      state?.user?.role === 'superuser' ||
      state?.user?.role === 'admin'
    ) {
      if (activeForm?.isKanbanEditable) {
        finalData = {
          ...finalData,
          data: {
            ...finalData?.data,
            [openedKey]: {
              ...finalData?.data?.[openedKey],
              value: {
                ...finalData?.data?.[openedKey]?.value,
                formData: filledFormData,
              },
            },
          },
        };

        const Editdata = {
          data: { ...finalData?.data, forms: finalData?.forms },
          attachments: finalData?.attachments,
        };

        const updated = await editDepartmentRow({
          id: finalData?._id,
          data: Editdata,
        }).unwrap();

        if (updated) {
          setRefetch(!refetch);
          toast.success('Form Updated!');
          setShowFormModal(false);
        }
      } else {
        toast.error('Form is not editable');
      }
    } else {
      toast.error('You are not allowed to edit this form Please contact admin');
    }
  };

  const renderTableBasedOnRefKey = (
    refKey,
    data,
    delayReason,
    setReadMore,
    _modalLeft = '24%',
    refetch,
    setRefetch,
    productObject
  ) => {
    return (
      <>
        {/* {ShowFormModal && (
          <Modal
            title="Form"
            onCloseModal={() => {
              setShowFormModal(false);
              setForm({});
              setFilledFormData({});
            }}
            onSubmit={() => {
              handleSaveFormData(data, refetch, setRefetch);
            }}
            // modalLeft={modalLeft}
            isSubmitRequired={filledFormData?.formData ? false : true}
          >
            {() => (
              <DepartmentForm
                setForm={setForm}
                Form={Form}
                initialData={filledFormData}
                filledFormData={
                  filledFormData?.formData
                    ? filledFormData?.formData
                    : filledFormData
                }
                setFilledFormData={setFilledFormData}
                setShowFormModal={setShowFormModal}
              />
            )}
          </Modal>
        )} */}

        {ShowFormModal && (
          <CreateFormFullScreenModal
            title="Form"
            onClose={() => {
              setShowFormModal(false);
              setForm({});
              setFilledFormData({});
            }}
            onSubmit={() => {
              handleSaveFormData(data, refetch, setRefetch);
            }}
            // modalLeft={modalLeft}
            isSubmitRequired={filledFormData?.formData ? false : true}
          >
            <DepartmentForm
              setForm={setForm}
              Form={Form}
              initialData={filledFormData}
              filledFormData={
                filledFormData?.formData
                  ? filledFormData?.formData
                  : filledFormData
              }
              setFilledFormData={setFilledFormData}
              setShowFormModal={setShowFormModal}
            />
          </CreateFormFullScreenModal>
        )}

        {openFileModal && (
          <Modal
            title={'Attachments'}
            onCloseModal={() => {
              setOpenFileModal(false);
            }}
            isSubmitRequired={false} // For opening files not required submit
            // modalLeft={modalLeft}
          >
            {() => (
              <>
                <div className="flex gap-3 items-center flex-wrap justify-center">
                  {sharedFiles?.map((file) => {
                    return (
                      <MediaElement
                        item={file}
                        getMedia={getMedia}
                        setPreview={setPreview}
                        key={file?._id}
                      />
                    );
                  })}
                </div>
              </>
            )}
          </Modal>
        )}

        {showAssemblyForm && (
          <Modal
            title={'Assembly Form'}
            onCloseModal={() => setShowAssemblyForm(false)}
            canSubmit={false}
            // modalLeft={modalLeft}
          >
            {() => {
              return (
                <AssemblyForm
                  addAssemblyFormData={data['sop']}
                  assemblyFormSteps={data?.sop?.steps}
                  stepCount={data?.sop?.steps?.length + 1 || 0}
                  disabled={true}
                />
              );
            }}
          </Modal>
        )}

        {preview?.openModal && (
          <div
            className="fixed top-0 left-0 flex justify-between items-center w-screen h-screen bg-black/10 z-[999999]"
            onClick={() => {
              if (media?.type !== 'application/pdf')
                setPreview((prev) => ({
                  ...prev,
                  openModal: false,
                }));
            }}
          >
            {preview?.file?.type !== 'text/csv' && (
              <>
                {preview?.file?.type === 'application/pdf' ? (
                  <PdfViewer
                    file={preview?.file?.data}
                    name={preview?.file?.name}
                    closeClick={(e) => {
                      e.preventDefault();
                      setPreview((prev) => ({
                        ...prev,
                        openModal: false,
                      }));
                    }}
                  />
                ) : preview?.file?.type === 'audio' ? (
                  <div className="flex flex-col items-center justify-center ml-[40%]">
                    <audio
                      src={preview?.file?.data}
                      controls
                      className="h-full aspect-video "
                    />
                  </div>
                ) : (
                  <>
                    {preview?.file?.data && (
                      <div className="relative w-full h-full">
                        <Button
                          className="!absolute !right-1 !top-1 !z-50 !bg-blue-500 !text-white"
                          onClick={() => {
                            setPreview(null);
                          }}
                        >
                          X
                        </Button>
                        <img
                          src={preview?.file?.data}
                          className="block object-contain w-full h-full"
                        />
                      </div>
                      // <Image
                      //   wrapperStyle={{ display: 'none' }}
                      //   preview={{
                      //     visible: preview?.openModal,
                      //     toolbarRender: () => null,
                      //   }}
                      //   src={preview?.file?.data}
                      // />
                    )}
                  </>
                )}
              </>
            )}
          </div>
        )}

        {showBom && (
          <Modal
            title={'Bom Comments'}
            onCloseModal={() => setShowBom(false)}
            canSubmit={false}
            // modalLeft={modalLeft}
          >
            {() => (
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>SR NO.</Table.Th>
                    <Table.Th>category</Table.Th>
                    <Table.Th>Item Name</Table.Th>
                    <Table.Th>Units</Table.Th>
                    <Table.Th>Subitem</Table.Th>
                    <Table.Th
                      className={`${refKey === 'CreatePo' || refKey === 'WorkOrder' ? '' : 'hidden'}`}
                    >
                      Deadline
                    </Table.Th>
                    <Table.Th
                      className={`${refKey === 'CreatePo' || refKey === 'WorkOrder' ? '' : 'hidden'}`}
                    >
                      RFD
                    </Table.Th>
                    <Table.Th
                      className={`${refKey === 'CreatePo' || refKey === 'WorkOrder' ? 'hidden' : ''}`}
                    >
                      Comment
                    </Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body className="w-full">
                  {bomData?.productList?.map((item, idx) => (
                    <>
                      <Table.Row key={idx}>
                        <Table.Td>{idx}</Table.Td>
                        <Table.Td>{item?.category}</Table.Td>
                        <Table.Td>
                          {item?.part?.name ||
                            item?.product?.name ||
                            item?.manualEntry ||
                            (item?.partVariant
                              ? getPartVariantName(item?.partVariant)
                              : item?.productVariant
                                ? getProductVariantName(item?.productVariant)
                                : '-')}
                        </Table.Td>
                        <Table.Td>{item?.units}</Table.Td>
                        <Table.Td>No</Table.Td>
                        {refKey === 'CreatePo' ||
                          (refKey === 'WorkOrder' && (
                            <Table.Td
                              className={`${refKey === 'CreatePo' || refKey === 'WorkOrder' ? '' : 'hidden'}`}
                            >
                              {data['bomDeadline'][item._id] || '-'}
                            </Table.Td>
                          ))}
                        {refKey === 'CreatePo' && (
                          <Table.Td
                            className={`${refKey === 'CreatePo' || refKey === 'WorkOrder' ? '' : 'hidden'}`}
                          >
                            {data['rfd'][item._id] || '-'}
                          </Table.Td>
                        )}
                        <Table.Td>
                          {comments.find(
                            (comment) => comment?.id === item?.part?._id
                          )?.comment || '-'}
                        </Table.Td>
                      </Table.Row>
                      {item?.subItems?.map((s, i) => (
                        <Table.Row key={i} className={`!bg-slate-200`}>
                          <Table.Td>{i + 1}</Table.Td>
                          <Table.Td>{s?.category}</Table.Td>
                          <Table.Td>
                            {s?.part?.name ||
                              s?.product?.name ||
                              s?.manualEntry ||
                              (s?.partVariant
                                ? getPartVariantName(s?.partVariant)
                                : s?.productVariant
                                  ? getProductVariantName(s?.productVariant)
                                  : '-')}
                          </Table.Td>
                          <Table.Td>{s?.units}</Table.Td>
                          <Table.Td>Yes</Table.Td>
                          {refKey === 'CreatePo' ||
                            (refKey === 'WorkOrder' && (
                              <Table.Td
                                className={`${refKey === 'CreatePo' || refKey === 'WorkOrder' ? '' : 'hidden'}`}
                              >
                                {data['bomDeadline'][item._id] || '-'}
                              </Table.Td>
                            ))}
                          {refKey === 'CreatePo' ||
                            (refKey === 'WorkOrder' && (
                              <Table.Td
                                className={`${refKey === 'CreatePo' || refKey === 'WorkOrder' ? '' : 'hidden'}`}
                              >
                                {data['rfd'][item._id] || '-'}
                              </Table.Td>
                            ))}
                          <Table.Td>
                            {comments.find(
                              (comment) => comment.id === item.part._id
                            )?.comment || '-'}
                          </Table.Td>
                        </Table.Row>
                      ))}
                    </>
                  ))}
                </Table.Body>
              </Table>
            )}
          </Modal>
        )}

        {showAsset && (
          <Modal
          // modalLeft={modalLeft}
          >
            {() => (
              <div>
                {assetData?.map((asset) => {
                  <>
                    <label>
                      Asset Name:
                      <p>{asset?.name}</p>
                    </label>
                    <label>
                      Asset ID:
                      <p>{asset?.assetId}</p>
                    </label>
                    <label>
                      Under Maintainence:
                      <p>{asset?.isUnderMaintenace}</p>
                    </label>
                  </>;
                })}
              </div>
            )}
          </Modal>
        )}

        {/* {showPo && (
          <Modal
            title={'Work Order'}
            onCloseModal={() => setShowPo(false)}
            canSubmit={false}
            modalLeft={modalLeft}
          >
            {() => (
              <Table>
                <Table.Body>
                  <Table.Row className={`flex flex-col`}>
                    {Object.entries(poData).map(([key, value]) => (
                      <>
                        {(typeof value !== 'object' ||
                          key === 'workOrderId' ||
                          key === 'name' ||
                          key === 'type') && (
                          <div className="flex w-full justify-between">
                            <Table.Td>{key}</Table.Td>
                            <Table.Td>{value}</Table.Td>
                          </div>
                        )}
                      </>
                    ))}
                  </Table.Row>
                </Table.Body>
              </Table>
            )}
          </Modal>
        )} */}
        {formManagementModal && (
          <FormManagementCreateModal
            forms={forms}
            setFormManagementModal={setFormManagementModal}
          />
        )}

        {showFillForm && (
          <FormManagementFillModal
            forms={fillForms}
            setFillModal={setShowFillForm}
          />
        )}
        {showMultipleBom && (
          <Modal
            title={'Bom Comments'}
            onCloseModal={() => setShowMultipleBom(false)}
            canSubmit={false}
          >
            {() => (
              <>
                <div className="flex gap-3 overflow-x-scroll">
                  {bomData?.map((bom, i) => (
                    <Button
                      key={i}
                      onClick={() => setDisplayedBom(i)}
                      className={`${displayedBom === i ? '' : '!bg-blue-300'}`}
                    >
                      {bom?.name}
                    </Button>
                  ))}
                </div>
                {displayedBom !== -1 && (
                  <Table key={bomData[displayedBom]?._id} className="w-full">
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>SR NO.</Table.Th>
                        <Table.Th>Category</Table.Th>
                        <Table.Th>Item Name</Table.Th>
                        <Table.Th>Units</Table.Th>
                        <Table.Th>Subitem</Table.Th>
                        <Table.Th>Comment</Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {bomData[displayedBom]?.productList?.map((item, idx) => (
                        <Table.Row key={idx}>
                          <Table.Td>{idx + 1}</Table.Td>
                          <Table.Td>{item?.category}</Table.Td>
                          <Table.Td>
                            {item?.part?.name ||
                              item?.product?.name ||
                              item?.manualEntry ||
                              (item?.partVariant
                                ? getPartVariantName(item?.partVariant)
                                : item?.productVariant
                                  ? getProductVariantName(item?.productVariant)
                                  : '-')}
                          </Table.Td>
                          <Table.Td>{item?.units}</Table.Td>
                          <Table.Td>No</Table.Td>
                          <Table.Td>
                            <Input
                              type="text"
                              placeholder="Enter comments"
                              disabled
                              value={item?.comment || '-'}
                            />
                          </Table.Td>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                )}
              </>
            )}
          </Modal>
        )}

        {refKey === 'DepartmentRow' && (
          <>
            <hr className="border-b-2 !w-full mt-3 !px-0" />
            <DynamicDepSideBar
              selectedRow={data}
              data={data}
              fromKanban={true}
            />
            {/* <CustomDepartmentsSidebar
              data={data}
              setOpenFileModal={setOpenFileModal}
              setFillForms={setFillForms}
              setShowFillForm={setShowFillForm}
              setForms={setForms}
              setSharedFiles={setSharedFiles}
              setFormManagementModal={setFormManagementModal}
              handleDepartMentRowComments={handleDepartMentRowComments}
              setFilledFormData={setFilledFormData}
              setShowFormModal={setShowFormModal}
              setForm={setForm}
              setOpenedKey={setOpenedKey}
              setDepartmentColumn={setDepartmentColumn}
            /> */}
          </>
        )}

        {refKey === 'grns' && (
          <>
            <hr className="border-b-2 !w-full mt-3 !px-0" />

            {Object.keys(data)?.map((item, idx) => {
              if (item === 'inpages') {
                return (
                  <section
                    className="mt-8"
                    style={{ fontSize: '15px' }}
                    key={idx}
                  >
                    <p className="text-lg text-gray-600">Product Details</p>
                    <div className="w-full mt-4 !border overflow-x-scroll !rounded-xl">
                      <table className="w-full min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            {Object.keys(inPageCols).map((head) => (
                              <th
                                key={head}
                                className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider"
                              >
                                {inPageCols[head].toUpperCase()}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {data[item].map((i, index) => (
                            <tr key={index}>
                              <td className="px-4 py-2 text-[10px]">
                                <div
                                  className={`!max-w-[30ch] !min-w-[24rem] break-words`}
                                >
                                  {i?.part ? (
                                    <p className="text-xs font-normal">
                                      Part: {i?.part?.name}
                                    </p>
                                  ) : i?.product ? (
                                    <p className="text-xs font-normal">
                                      Product: {i?.product?.name}
                                    </p>
                                  ) : (
                                    <p className="text-xs font-normal">
                                      SubAssembly: {i?.subAssembly?.name}
                                    </p>
                                  )}
                                </div>
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i.batchNo || '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i.lotNo || '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i.manufacturingDate || '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i.inTime || '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i?.workerId?.name || '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i?.store?.name || '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i?.vendor?.name || '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i.storeArea || '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i.part?.uom ||
                                  i?.product?.uom ||
                                  i?.subAssembly?.uom ||
                                  '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i.part?.valuation ||
                                  i?.product?.valuation ||
                                  i?.subAssembly?.valuation ||
                                  '-'}
                              </td>
                              <td className="text-center px-4 py-2 text-[10px]">
                                {i.quantity || '-'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </section>
                );
              } else {
                return (
                  <div
                    className={`mt-2 ${grnCols[item] === undefined ? 'hidden' : ''}`}
                    key={idx}
                  >
                    <div className="flex mt-3">
                      <p className="text-sm text-gray-500 min-w-[15rem]">
                        {![
                          'delivery challan',
                          'VEHICLE NUMBER',
                          'files',
                          'purchase order',
                          'CHECKED BY',
                        ]?.includes(grnCols[item]) && grnCols[item]}
                      </p>
                      <p className="text-gray-600 text-sm">
                        {typeof data[item] !== 'object' && data[item]}
                      </p>
                    </div>
                  </div>
                );
              }
            })}
          </>
        )}

        {refKey === 'SalesInquiryDashboard' && (
          <>
            <SalesInquirySidebar
              fromKanban={true}
              salesInquiryData={data}
              setReadMore={setReadMore}
            />
          </>
        )}

        {refKey === 'PerformaInvoice' && (
          <>
            <PerformaInvoiceSidebar
              fromKanban={true}
              currentClicked={0}
              allData={[data]}
            />
          </>
        )}

        {refKey === 'Quotation' && (
          <>
            <QuotationRightSidebar
              data={data}
              fromKanban={true}
              generateDateString={generateDateString}
              setReadMore={setReadMore}
            />
          </>
        )}

        {refKey === 'SalesOrder' && (
          <>
            <SalesOrderSideBar
              fromKanban={true}
              data={[data]}
              pos={0}
              setReadMore={setReadMore}
              productObject={productObject}
            />
          </>
        )}

        {refKey === 'indentrefs' && (
          <>
            {Object.keys(data)?.map((item, idx) => {
              if (item === 'indents') {
                return (
                  <div key={idx} className="w-full">
                    <Disclosure>
                      <Table.Row>
                        <Table.Td>{indentCol[item]?.toUpperCase()}</Table.Td>
                        <Disclosure.Button className={`text-blue-500 text-sm`}>
                          <Table.Td>Click</Table.Td>
                        </Disclosure.Button>
                      </Table.Row>
                      <Disclosure.Panel>
                        <div className="overflow-x-scroll">
                          <Table>
                            <Table.Head>
                              <Table.Row>
                                <Table.Th>Status</Table.Th>
                                <Table.Th>Po</Table.Th>
                                <Table.Th>Indent No</Table.Th>
                                <Table.Th>Indent Date</Table.Th>
                                <Table.Th>Request By</Table.Th>
                                {/* <Table.Th>Department</Table.Th> */}
                                <Table.Th>Product Name</Table.Th>
                                <Table.Th>UOM</Table.Th>
                                <Table.Th>Quantity</Table.Th>
                                <Table.Th>Delivery Address</Table.Th>
                                <Table.Th>Vendor Name</Table.Th>
                                <Table.Th>Type</Table.Th>
                                <Table.Th>Remark</Table.Th>
                              </Table.Row>
                            </Table.Head>
                            <Table.Body>
                              {data[item]?.map((i, index) => (
                                <Table.Row key={index}>
                                  {Object.keys(i)?.map(
                                    (nestedItem, nestedKey) =>
                                      nestedItem !== '_id' &&
                                      nestedItem !== 'value' &&
                                      nestedItem !== 'createdAt' &&
                                      nestedItem !== 'updatedAt' &&
                                      nestedItem !== '__v' &&
                                      nestedItem !== 'profileId' &&
                                      nestedItem !== 'productLink' &&
                                      nestedItem !== 'taskId' && (
                                        <Table.Td key={nestedKey}>
                                          {i[nestedItem]}
                                        </Table.Td>
                                      )
                                  )}
                                </Table.Row>
                              ))}
                            </Table.Body>
                          </Table>
                        </div>
                      </Disclosure.Panel>
                    </Disclosure>
                  </div>
                );
              }
              return (
                <>
                  <Table.Row
                    key={idx}
                    className={`${indentCol[item] === undefined ? 'hidden' : ''}`}
                  >
                    <Table.Td>{indentCol[item]?.toUpperCase()}</Table.Td>
                    {typeof data[item] !== 'object' && (
                      <Table.Td>{data[item]}</Table.Td>
                    )}
                  </Table.Row>
                </>
              );
            })}
          </>
        )}

        {refKey === 'PurchaseOrder' && (
          <>
            <PODashboardSidebar
              fromKanban={true}
              vendorData={[data?.vendor]}
              selectedPo={data}
            />
          </>
        )}

        {refKey === 'CreatePo' && (
          <>
            <WorkOrderSideBar fromKanban={true} selectedWorkOrder={data} />
          </>
        )}

        {refKey === 'WorkOrder' && (
          <>
            <WorkOrderSidebarV2
              fromKanban={true}
              selectedWorkOrder={data?._id}
            />
          </>
        )}

        {refKey === 'RequestForQuotation' && (
          <>
            <RFQsidebarData fromKanban={true} partSidebarData={data} />
          </>
        )}

        {refKey === 'OrderRefs' && (
          <>
            <hr className="border-b-2 !w-full mt-3 !px-0" />

            <section className="mt-8" style={{ fontSize: '15px' }}>
              <p className="text-lg text-gray-600">Product Details</p>
              <div className="w-full mt-4 border rounded-xl overflow-x-scroll">
                <table className="w-full min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        Chalaan No
                      </th>
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        Item
                      </th>
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        Batch No.
                      </th>
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        Lot No.
                      </th>
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        Remaining Quantity
                      </th>
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        Store Area
                      </th>
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        Store Worker
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {data?.orders?.map((order, index) => (
                      <tr key={index}>
                        <td className="text-center px-4 py-2 text-[10px]">
                          {order?.chalaanNo || '-'}
                        </td>
                        <td className="text-center px-4 py-2 text-[10px]">
                          {order?.inpageId?.part?.name ||
                            order?.inpageId?.subAssembly?.name ||
                            order?.inpageId?.product?.name ||
                            '-'}
                        </td>
                        <td className="text-center px-4 py-2 text-[10px]">
                          {order?.inpageId?.batchNo || '-'}
                        </td>
                        <td className="text-center px-4 py-2 text-[10px]">
                          {order?.inpageId?.lotNo || '-'}
                        </td>
                        <td className="text-center px-4 py-2 text-[10px]">
                          {order?.quantity || '-'}
                        </td>
                        <td className="text-center px-4 py-2 text-[10px]">
                          {order?.inpageId?.remainingQuantity || '-'}
                        </td>
                        <td className="text-center px-4 py-2 text-[10px]">
                          {order?.inpageId?.storeArea || '-'}
                        </td>
                        <td className="text-center px-4 py-2 text-[10px]">
                          {order?.inpageId?.workerId?.name || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </section>
          </>
        )}

        {refKey === 'dispatch' && (
          <>
            <DispatchRightSideBar
              dispatch={{ results: [data] }}
              ShowSidebar={true}
              SelectedDispatch={data}
              fromKanban={true}
              DispatchItems={data?.dispatch_items}
            />
          </>
        )}

        {refKey === 'Leads' && <LeadSideBar fromKanban={true} dataId={data} />}
        {delayReason?.length > 0 && (
          <div className="mt-4">
            <h3>Delay Reason</h3>
            <Table>
              <Table.Head>
                <Table.Th>Date</Table.Th>
                <Table.Th>Description</Table.Th>
              </Table.Head>
              {delayReason?.map((res, idx) => (
                <Table.Row key={idx}>
                  <Table.Td className="w-fit">
                    {getLocalDate(res?.delayDate)}
                  </Table.Td>
                  <Table.Td>{res?.reason}</Table.Td>
                </Table.Row>
              ))}
            </Table>
          </div>
        )}
      </>
    );
  };

  return renderTableBasedOnRefKey;
};

export default useKanbanSidebar;
