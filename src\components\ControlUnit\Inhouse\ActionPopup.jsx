import { useState, useContext } from 'react';
import { useGetAllLocationsQuery } from '../../../slices/locationApiSlice';
import Button from '../../global/components/Button';
import Input from '../../global/components/Input';
import Select from '../../global/components/Select';
import JobStockInModal from '../JobStockInModal';
import { Store } from '../../../store/Store';
import { toast } from 'react-toastify';

function ActionPopup({
  popupFor = '',
  errorMessages = [],
  submitFunction,
  userBatchSizeInput = false,
  locationName,
  setLocationName,
  setPauseMessage,
  pauseMessage,
  samplingBatchSize,
  setSamplingBatchSize,
  allEmployees,
  selectedEmployee,
  setSelectedEmployee,
  prevEmployee,
  setOperatorPopup,
  itemForJob,
  cuProject,
  inhOutput = 0,
}) {
  const [seletedError, setSeletedError] = useState('');
  const [showForceStop, setShowForceStop] = useState(false);
  const [itemTobeStockedOut, setItemTobeStockedOut] = useState([]);

  const {
    defaults: { defaultParam },
  } = useContext(Store);

  let stopMethod = defaultParam?.projectDefaults?.defaultJobStopType || 'none';

  const { data: allLocations = [] } = useGetAllLocationsQuery();

  const nextStepWithGRN = () => {
    if (popupFor === 'stop') {
      if (stopMethod === 'none') {
        submitFunction([seletedError]);
      } else if (stopMethod === 'manual') {
        if (
          !userBatchSizeInput &&
          inhOutput > +cuProject?.batchInfo?.['Batch Size']
        ) {
          toast.error('Output should be less than BatchSize');
          setOperatorPopup(false);
          return;
        }
        let data = {
          flowId: cuProject?.flowId,
          mqttId: cuProject?.mqtt?._id,
          batchInfo: {
            ['Batch Size']: cuProject?.batchInfo?.['Batch Size'],
            outputSize: !userBatchSizeInput
              ? inhOutput
              : samplingBatchSize || 0,
            batchName: cuProject?.batchInfo?.batchName,
            batchNo: cuProject?.batchInfo?.batchNo,
          },
        };
        setItemTobeStockedOut(() => [data]);
        setShowForceStop(true);
      } else {
        toast.error('automatic mode is under development');
        setOperatorPopup(false);
        return;
      }
    }
  };

  const jobStopFxnToCallAfterStockIn = () => {
    if (popupFor === 'stop') {
      submitFunction([seletedError]);
    }
  };

  return (
    <div className="h-full  flex flex-col">
      {showForceStop && (
        <JobStockInModal
          setShowForceStop={setShowForceStop}
          productFromCu={itemForJob}
          setOperatorPopup={setOperatorPopup}
          method="normalStop"
          itemStockOutData={itemTobeStockedOut}
          jobStopFxnToCallAfterStockIn={jobStopFxnToCallAfterStockIn}
        />
      )}
      <h3 className="w-full px-4 py-1.5 mb-4 h-1/5">
        <span className="capitalize">{popupFor}</span> project?
      </h3>
      <div className="px-4 mb-3">
        {prevEmployee?.name && (
          <div className="capitalize mb-3">{`Previous Employee : ${prevEmployee?.name}`}</div>
        )}

        <span className="capitalize">Select Operator</span>
        <Select
          value={selectedEmployee}
          onChange={(e) => setSelectedEmployee(e.target.value)}
          options={allEmployees.map((em) => ({
            label: em.name,
            value: em._id,
          }))}
        />
      </div>

      {popupFor === 'pause' ? (
        <div className="px-4 h-full">
          <div>
            <span className="capitalize">Select Error</span>
            <Select
              value={seletedError}
              onChange={(e) => setSeletedError(e.target.value)}
              options={errorMessages.map((em) => ({
                label: em.message,
                value: em._id,
              }))}
            />
          </div>

          <div className=" mt-3">
            <span className="capitalize">Add Remark</span>
            <Input
              type="text"
              placeholder="Please add you pause message here....."
              value={pauseMessage}
              onChange={(e) => setPauseMessage(e.target.value)}
            />
          </div>
        </div>
      ) : popupFor === 'stop' ? (
        <>
          <div className="px-4 h-4/5">
            <p className="sm:w-[60%] w-[65%] ">Select Location </p>
            <Select
              value={locationName}
              options={allLocations.map((el) => ({
                name: el?.locationName,
                value: el?._id,
              }))}
              placeholder="Select Loction"
              className="cursor-pointer"
              onChange={(e) => {
                setLocationName(e.target.value);
              }}
            />

            {userBatchSizeInput && (
              <div className="flex flex-col  h-[80%]">
                <p className="text-sm text-center mt-[5%]">
                  Please Enter Batch Size
                </p>
                <input
                  type="text"
                  autoFocus
                  className="outline-none bg-transparent text-center"
                  value={samplingBatchSize}
                  onChange={(e) => {
                    setSamplingBatchSize(e.target.value);
                  }}
                />
              </div>
            )}
          </div>
        </>
      ) : null}

      {popupFor === 'stop' ? (
        <>
          {cuProject?.productionFlow?.processes?.[
            cuProject?.productionFlow?.processes?.length - 1
          ]?._id?.toString() === (cuProject?.flowId).toString() ? (
            <div className="w-full flex justify-end px-4 h-1/5">
              <Button onClick={() => nextStepWithGRN()}>Next</Button>
            </div>
          ) : (
            <div className="w-full flex justify-end px-4 h-1/5">
              <Button
                // onClick={() => nextStepWithGRN()}
                onClick={() => submitFunction([seletedError])}
              >
                Submit
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="w-full flex justify-end px-4 h-1/5">
          <Button onClick={() => submitFunction([seletedError])}>Submit</Button>
        </div>
      )}
    </div>
  );
}

export default ActionPopup;
