import { XMarkIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import Marquee from 'react-fast-marquee';
import pdf from '../../assets/images/pdf.png';
import { ReactComponent as UploadIcon } from '../../assets/svgs/upload_cloud.svg';
import { downloadMedia } from '../../helperFunction';
import {
  useGetMediaByIdArrayMutation,
  useKanbanFormMediaHandlerMutation,
} from '../../slices/mediaSlice';
import Modal from '../global/components/Modal';
// import PdfViewer from '../global/components/PdfViewer';
import UploadButton from '../UploadButton';
const removeCommonElements = (originalArray, currentArray) => {
  let set1 = new Set(originalArray.map((item) => item.name));
  let set2 = new Set(currentArray.map((item) => item.name));

  let notChanged = originalArray.filter((item) => set2.has(item.name));

  let toBeDeleted = originalArray.filter((item) => !set2.has(item.name));
  let toBeAdded = currentArray.filter((item) => !set1.has(item.name));

  return [toBeDeleted, toBeAdded, notChanged];
};

const MediaModal = ({
  isAddMedia,
  setIsAddMedia,
  setFilledData,
  // filledFormData
}) => {
  const [originalMedia, setOriginalMedia] = useState([]);
  const [currentMedia, setCurrentMedia] = useState([]);
  const [media, setMedia] = useState('');
  const [ShowFullScreenModal, setShowFullScreenModal] = useState(false);
  const [getMediaByIdArray, { isLoading }] = useGetMediaByIdArrayMutation();
  const [kanbanFormMediaHandler] = useKanbanFormMediaHandlerMutation();

  useEffect(() => {
    if (isAddMedia.media) {
      (async () => {
        let data = {
          ids: isAddMedia?.media || [],
        };
        const res = await getMediaByIdArray({ data }).unwrap();
        if (res) {
          setOriginalMedia(res?.media || []);
          setCurrentMedia(res?.media || []);
        }
      })();
    }
  }, [isAddMedia.media]); // eslint-disable-line

  const changeHandler = (e) => {
    for (let i in e) {
      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      let name = e[i].name;
      let type = e[i].type;
      fr.addEventListener('load', () => {
        const url = fr.result;
        if (url) {
          const tempDate = new Date()
            .toLocaleString('en-IN', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
            })
            .replaceAll(',', '')
            .replaceAll(' ', '_');
          setCurrentMedia((prev) => [
            ...(prev || []),
            {
              name: `${name?.split('.')[0]}_${tempDate}.${name?.split('.')[1]}`,
              type: type,
              data: url,
            },
          ]);
        }
      });
    }
  };

  const handleMediaSubmit = async () => {
    const [toBeDeleted, toBeAdded, notChanged] = removeCommonElements(
      originalMedia,
      currentMedia
    );
    let deletedArrayId = toBeDeleted.map((item) => item._id) || [];
    let data = {
      toBeDeleted: deletedArrayId || [],
      toBeAdded: toBeAdded || [],
    };
    if (toBeAdded?.length > 0 || toBeDeleted?.length > 0) {
      const res = await kanbanFormMediaHandler({ data }).unwrap();
      if (res) {
        setFilledData((prev) => ({
          ...prev,
          [isAddMedia.fieldName]: {
            ...prev[isAddMedia.fieldName],
            media: [
              ...(res?.mediaAdded?.map((item) => item._id) || []),
              ...(notChanged?.map((item) => item._id) || []),
            ],
          },
        }));
        setIsAddMedia({ isAdd: false, fieldName: '', media: [] });
        setCurrentMedia([]);
        setOriginalMedia([]);
      }
    } else {
      setIsAddMedia({ isAdd: false, fieldName: '', media: [] });
      setCurrentMedia([]);
      setOriginalMedia([]);
    }
    setIsAddMedia({ isAdd: false, fieldName: '', media: [] });
  };

  return (
    <>
      {ShowFullScreenModal && (
        <div
          className="fixed top-0 left-0 flex justify-between items-center w-screen h-screen bg-black/10 z-[999999]"
          onClick={() => {
            if (media?.type !== 'application/pdf')
              setShowFullScreenModal(false);
          }}
        >
          <>
            {media?.type === 'application/pdf' ? null : (
              // <div className="flex items-center justify-center w-2/6 h-full ">
              //   <PdfViewer
              //     file={media?.data}
              //     name={media?.name}
              //     closeClick={(e) => {
              //       e.preventDefault();
              //       setShowFullScreenModal(false);
              //     }}
              //   />
              // </div>
              <div className="flex items-center justify-center">
                <img
                  className="h-[90%] aspect-video object-contain"
                  src={media?.data}
                  alt=""
                />
              </div>
            )}
          </>
        </div>
      )}

      <Modal
        title="Manage Media"
        description={
          'Efficiently manage  attached media  easily preview , edit , or delete files'
        }
        // modalLeft={modalLeft}
        onCloseModal={() => {
          setIsAddMedia({ isAdd: false, fieldName: '', media: [] });
          setCurrentMedia([]);
          setOriginalMedia([]);
        }}
        canSubmit={false}
        onSubmit={handleMediaSubmit}
      >
        {() => {
          return (
            <div className="flex flex-col justify-center w-full items-center">
              <div className="w-full flex justify-around">
                <h5 className="my-auto text-lg">Upload File</h5>
                <UploadButton
                  accept="image/*, application/pdf"
                  onChange={(e) => changeHandler(e)}
                  multiple
                  className={
                    'min-h-[120px] h-full bg-gray-primary w-full text-center'
                  }
                  svg={<UploadIcon />}
                />
              </div>
              <hr className="w-3/4 h-0.5 mx-auto my-4 bg-gray-100 border-0 rounded md:my-6 dark:bg-gray-500/65" />
              {currentMedia?.length > 0 ? (
                <>
                  {isLoading ? (
                    <>
                      <div
                        role="status"
                        className="flex items-center justify-center h-56 max-w-sm bg-gray-300 rounded-lg animate-pulse dark:bg-gray-700"
                      >
                        <svg
                          className="w-10 h-10 text-gray-200 dark:text-gray-600"
                          aria-hidden="true"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="currentColor"
                          viewBox="0 0 16 20"
                        >
                          <path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z" />
                          <path d="M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2ZM9 13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2Zm4 .382a1 1 0 0 1-1.447.894L10 13v-2l1.553-1.276a1 1 0 0 1 1.447.894v2.764Z" />
                        </svg>
                        <span className="sr-only">Loading...</span>
                      </div>
                    </>
                  ) : (
                    <div className="flex gap-4 flex-wrap">
                      {currentMedia?.length > 0 &&
                        currentMedia?.map((item, uIdx) => (
                          <section
                            key={uIdx}
                            className="p-2 border rounded-md w-[170px] flex flex-col justify-between"
                          >
                            <section
                              className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer "
                              onClick={() => {
                                if (item?.type === 'application/pdf') {
                                  downloadMedia(item);
                                } else {
                                  setMedia(item);
                                  setShowFullScreenModal(true);
                                }
                              }}
                            >
                              <img
                                className="w-[150px] aspect-video object-contain"
                                src={
                                  item?.type === 'application/pdf'
                                    ? pdf
                                    : item?.data
                                }
                                alt=""
                              />
                            </section>

                            <section className="flex justify-between items-center text-sm mt-2">
                              <Marquee className="w-">{item.name}</Marquee>
                              <button
                                type="button"
                                onClick={() =>
                                  setCurrentMedia(
                                    currentMedia.filter((_, i) => i !== uIdx)
                                  )
                                }
                                className="outline-none text-red-primary hover:text-white hover:bg-red-primary rounded px-2 py-1"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </section>
                          </section>
                        ))}
                    </div>
                  )}
                </>
              ) : (
                <span className="text-red-primary">No File Selected</span>
              )}
            </div>
          );
        }}
      </Modal>
    </>
  );
};

export default MediaModal;
