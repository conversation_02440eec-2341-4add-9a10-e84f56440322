import Table from '../global/components/Table';
import Tooltip from '../global/components/ToolTip';

const BomSidebarData = ({ bom }) => {
  return (
    <section>
      <div className="bom-id">
        <h3>
          #{bom?.data?.bomId} Bom Created on{' '}
          {new Date(bom?.data?.createdAt).toLocaleDateString('en-In')}
        </h3>
        <h3>Bom Id: {bom?.data?.bomId}</h3>
        <h3>Bom Name: {bom?.data?.name}</h3>
      </div>
      <div className="data mt-3 mb-2">
        <h4>Products</h4>
        <div className="table w-full">
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Th>Category</Table.Th>
                <Table.Th>Item</Table.Th>
                <Table.Th>Unit</Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {bom?.data?.productList?.map((item) => {
                return (
                  <Table.Row key={item?._id}>
                    <Table.Td>{item?.category}</Table.Td>
                    <Table.Td>
                      <Tooltip
                        text={
                          item?.part ? item?.part?.name : item?.product?.name
                        }
                      >
                        {(item?.part
                          ? item?.part?.name
                          : item?.product?.name
                        ).slice(0, 20) + '...'}
                      </Tooltip>
                      {item?.part ? item?.part?.name : item?.product?.name}
                    </Table.Td>
                    <Table.Td>{item?.units}</Table.Td>
                  </Table.Row>
                );
              })}
            </Table.Body>
          </Table>
        </div>
      </div>
    </section>
  );
};

export default BomSidebarData;
