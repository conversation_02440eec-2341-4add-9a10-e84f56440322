import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Select,
  Switch,
  Table,
  Tag,
} from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import {
  useCreateThirdPartyIntegrationMutation,
  useDeleteThirdPartyIntegrationMutation,
  useEditThirdPartyIntegrationMutation,
  useGetThirdPartyIntegrationsQuery,
} from '../../slices/ThirdPartyApiSlice';
import { customConfirm } from '../../utils/customConfirm';

const ThirdPartyIntegration = () => {
  const [services, setServices] = useState([]);
  const [realtimeFetch, setRealtimeFetch] = useState(false);
  const { data: thirdPartyIntegrations, isLoading } =
    useGetThirdPartyIntegrationsQuery();
  const [createThirdPartyIntegration] =
    useCreateThirdPartyIntegrationMutation();
  const [editThirdPartyIntegration] = useEditThirdPartyIntegrationMutation();
  const [deleteThirdPartyIntegration] =
    useDeleteThirdPartyIntegrationMutation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const [editingService, setEditingService] = useState(null);

  useEffect(() => {
    if (thirdPartyIntegrations) {
      setServices(thirdPartyIntegrations);
    }
  }, [thirdPartyIntegrations]);

  const handleEdit = (service) => {
    setEditingService(service);
    form.setFieldsValue(service);
    setRealtimeFetch(service.realtimeFetch);
    setIsModalOpen(true);
  };

  const handleDelete = async (service) => {
    const confirm = await customConfirm(
      'Are you sure you want to delete this service?',
      'delete'
    );
    if (!confirm) return;
    try {
      await deleteThirdPartyIntegration({ id: service._id });
      toast.success('Service deleted successfully');
    } catch (error) {
      toast.error('Failed to delete service:', error);
    }
  };

  const openModal = () => {
    setEditingService(null);
    form.resetFields();
    setIsModalOpen(true);
  };

  const handleSubmit = async (values) => {
    values.realtimeFetch = realtimeFetch;
    try {
      if (editingService) {
        await editThirdPartyIntegration({
          id: editingService._id,
          data: values,
        });
      } else {
        await createThirdPartyIntegration(values);
      }
      setIsModalOpen(false);
      setRealtimeFetch(false);
      form.resetFields();
    } catch (error) {
      toast.error('Failed to save service:', error);
    }
  };

  const columns = [
    { title: 'Service Name', dataIndex: 'name', key: 'name' },
    { title: 'Encrypted API Key', dataIndex: 'hashedApiKey', key: 'apiKey' },
    {
      title: 'Real-Time Refetch',
      key: 'realtimeFetch',
      render: (_, record) => (
        <Tag color={record.realtimeFetch ? 'green' : 'red'}>
          {record.realtimeFetch ? 'Yes' : 'No'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div className="flex gap-x-2">
          <Button type="primary" onClick={() => handleEdit(record)}>
            Edit
          </Button>
          <Button type="primary" danger onClick={() => handleDelete(record)}>
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="w-full flex flex-col gap-y-5 h-screen bg-white">
      <Card
        title="Third-Party Service Integrations"
        extra={
          <Button type="primary" onClick={openModal}>
            Add Service
          </Button>
        }
      >
        <Table
          dataSource={services}
          columns={columns}
          rowKey="_id"
          loading={isLoading}
          pagination={false}
          scroll={{ x: true }}
        />
      </Card>

      <Modal
        title={editingService ? 'Edit Service' : 'Add New Service'}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="Service Name"
            rules={[
              { required: true, message: 'Please enter the service name' },
            ]}
          >
            <Select
              placeholder="E.g., IndiaMART, WhatsApp"
              options={[
                { label: 'IndiaMART', value: 'indiamart' },
                { label: 'WhatsApp', value: 'whatsapp' },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="apiKey"
            label="API Key"
            rules={[{ required: true, message: 'Please enter the API key' }]}
          >
            <Input placeholder="Enter API Key or Token" />
          </Form.Item>
          <Form.Item>
            <div className="flex items-center gap-x-2">
              <span>Real-Time Refetch:</span>
              <Switch
                checked={realtimeFetch}
                onChange={(checked) => setRealtimeFetch(checked)}
              />
            </div>
          </Form.Item>
          <Button type="primary" htmlType="submit">
            Save
          </Button>
        </Form>
      </Modal>
    </div>
  );
};

export default ThirdPartyIntegration;
