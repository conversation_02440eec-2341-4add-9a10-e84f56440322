import { useNavigate } from 'react-router-dom';
import { getLocalDateTime, mobileWidth } from '../../helperFunction';
import {
  useGetAllTemplatesQuery,
  useDeleteJobTemplateMutation,
} from '../../slices/templateApiSlice';
import Table from '../global/components/Table';
import Tooltip from '../global/components/ToolTip';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';

export default function AllJobTemplate() {
  const handleNavigate = useNavigate();
  const isMobile = useMediaQuery({ query: mobileWidth });
  const { data = [] } = useGetAllTemplatesQuery();
  const [deleteTemplate] = useDeleteJobTemplateMutation();

  const handleDelete = async (el) => {
    await deleteTemplate({ id: el?._id }).then((res) => {
      if (!res?.error) {
        toast.success('Template deleted successfully');
      }
    });
  };

  const headings = ['#', 'DATE', 'NAME', 'FILES', 'ACTIONS'];

  const fileLength = (el) => {
    return Object.values(el.attachments || {})?.flatMap((el) => el)?.length;
  };

  const handleEdit = (el) => {
    handleNavigate(
      `/jobs/createjobs?create_template=true&mode=edit&id=${el?._id}`
    );
  };

  return (
    <Table>
      <Table.Head>
        <Table.Row>
          {(isMobile
            ? headings?.filter((el) => el !== '#' && el !== 'DATE')
            : headings
          )?.map((el, i) => (
            <Table.Th
              key={i}
              className={`${i === headings.length - 1 ? 'w-[4rem]' : ''}`}
            >
              {el}{' '}
            </Table.Th>
          ))}
        </Table.Row>
      </Table.Head>
      <Table.Body>
        {data.map((el, i) => (
          <Table.Row key={i}>
            {!isMobile && <Table.Td>{i + 1}</Table.Td>}
            {!isMobile && (
              <Table.Td>{getLocalDateTime(el?.createdAt)}</Table.Td>
            )}
            <Table.Td className={'md:!min-w-[24rem] md:!w-[24rem]'}>
              {el?.name?.length > 55 ? (
                <Tooltip text={el?.name}>
                  {el?.name?.slice(0, 55) + '...'}
                </Tooltip>
              ) : (
                el?.name
              )}
            </Table.Td>
            <Table.Td>
              <p className="text-blue-600 text-xs ml-1 cursor-pointer">
                Files <span className="text-[8px]">({fileLength(el)})</span>
              </p>
            </Table.Td>

            <Table.Options
              className={'!bg-white'}
              onEdit={() => handleEdit(el)}
              onDelete={() => handleDelete(el)}
            />
          </Table.Row>
        ))}
      </Table.Body>
    </Table>
  );
}
