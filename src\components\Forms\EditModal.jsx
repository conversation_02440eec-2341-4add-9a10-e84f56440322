import { Button, Checkbox, Input, Modal, Select, Typography } from 'antd';
import { Eye, Trash } from 'lucide-react';
import { useState } from 'react';
import { BiTrash } from 'react-icons/bi';
import { InfoTooltip } from '../global/components/InfoTooltip';
import { Label } from '../v2';
import ExistingFormData from './ExistingTableData';
const { Title } = Typography;
import { Alert } from 'antd';

const TrComponent = ({ setlabel, i, label }) => {
  return (
    <>
      <div className="flex flex-col  mt-2">
        <label className="mb-1 font-semibold text-[#667085]">Label Name </label>
        <Input
          onChange={(e) => {
            let temp = label;
            temp[i] = e.target.value;
            setlabel(temp);
          }}
          placeholder="Label"
        />
      </div>
    </>
  );
};
const EditModal = ({
  openEditModal,
  setOpenEditModal,
  FormName,
  setFormName,
  FormCategoryType,
  isKanbanEditable,
  setIsKanbanEditable,
  formInformation,
  handelFieldNameChange,
  handleFieldInfo,
  ctg,
  setFormInfomation,
  formInspection,
  handleInspectionFieldInfo,
  handeInspectionlFieldName,
  setFormInspection,
  departmentFormData,
  handeDepartmentFieldName,
  handleDepartmentFieldInfo,
  isMandatory,
  setIsMandatory,
  setDepartmentFormData,
  setFieldOptions,
  FieldName,
  setFieldName,
  FieldType,
  setFieldType,
  label,
  setlabel,
  fieldOptions,
  noOption,
  setnoOption,
  tooltipContent,
  handelEditform,
  handleAdd,
  setIsCopy,
  SearchParams,
  setSearchParams,
  setFormData,
}) => {
  const [existingFormData, setExistingFormData] = useState(null);
  return (
    <Modal
      title="Edit Form"
      open={openEditModal}
      onCancel={() => {
        setOpenEditModal(false);
        setOpenEditModal(false);
        setIsCopy(false);
        SearchParams.delete('form_id');
        setSearchParams(SearchParams);
        setExistingFormData(null);
        setFormData([]);
      }}
      width={800}
      centered
      styles={{
        body: {
          maxHeight: 'calc(100vh - 200px)',
          overflowY: 'auto',
        },
      }}
      footer={
        <div className="flex justify-between space-x-2">
          <Button
            type="primary"
            onClick={() => {
              handleAdd();
            }}
          >
            Add
          </Button>
          <Button type="primary" onClick={() => handelEditform()}>
            Save
          </Button>
        </div>
      }
    >
      <>
        <div>
          <div className="input-wrapper">
            <Label>Form Name</Label>
            <Input
              value={FormName}
              onChange={(e) => {
                setFormName(e.target.value);
              }}
            />
          </div>
          {FormCategoryType === 'DEPARTMENT' && (
            <div className="mt-2">
              <Checkbox
                id="sequenceFollow"
                name="sequenceFollow"
                value={isKanbanEditable}
                checked={isKanbanEditable}
                onChange={(e) => {
                  setIsKanbanEditable(e.target.checked);
                }}
              />
              <label className="mb-1 font-semibold text-[#667085] ml-2">
                Allow editing in Kanban
              </label>
            </div>
          )}

          <div className="heading mt-3">
            <Title level={5}>Form Fields</Title>
          </div>
          <div>
            {FormCategoryType === 'QC' && (
              <>
                {formInformation?.map((form, iddx) => (
                  <div
                    className="grid grid-cols-1 sm:grid-cols-[1fr,1fr,auto,auto] gap-4 border p-4 mb-4 rounded-lg"
                    key={iddx}
                  >
                    {/* Field Name */}
                    <div className="flex flex-col">
                      <Label>Field Name</Label>
                      <Input
                        value={form?.fieldName ?? ''}
                        onChange={(e) => {
                          handelFieldNameChange(iddx, e?.target?.value);
                        }}
                      />
                      {(form?.fieldName.includes('.') ||
                        form?.fieldName.includes('$')) && (
                        <Alert
                          className="mt-2"
                          message=" $ and . are not allowed"
                          type="error"
                          showIcon
                        />
                      )}
                    </div>

                    {/* Field Type */}
                    <div className="flex flex-col w-full sm:w-[300px]">
                      <Label>Field Type</Label>
                      <Select
                        showSearch
                        value={form?.fieldType}
                        options={ctg.map((option) => ({
                          label: option,
                          value: option,
                        }))}
                        onChange={(e) => handleFieldInfo(iddx, e)}
                        placeholder="Select Type"
                      />
                    </div>

                    {/* View Icon */}
                    <div
                      className={`mt-4 flex justify-end sm:mt-6 ${['Table', 'DropDown', 'MultiSelect', 'MultiCheckbox'].includes(form?.fieldType) ? '' : 'hidden'}`}
                    >
                      <span
                        className="cursor-pointer hover:text-green-600"
                        onClick={() => setExistingFormData(form)}
                      >
                        <Eye />
                      </span>
                    </div>

                    {/* Trash Icon */}
                    <div className="mt-4 flex justify-end sm:mt-6">
                      <span
                        className="cursor-pointer text-red-600 hover:text-red-800"
                        onClick={() => {
                          setFormInfomation((prev) =>
                            prev.filter(
                              (data) => data.fieldName !== form?.fieldName
                            )
                          );
                        }}
                      >
                        <Trash />
                      </span>
                    </div>
                  </div>
                ))}
                {existingFormData && (
                  <>
                    <ExistingFormData
                      existingFormData={existingFormData}
                      setExistingFormData={setExistingFormData}
                      FormCategoryType={FormCategoryType}
                      setFormInfomation={setFormInfomation}
                      setFormInspection={setFormInspection}
                      setDepartmentFormData={setDepartmentFormData}
                    />
                  </>
                )}
              </>
            )}
          </div>
          <div>
            {FormCategoryType === 'INSPECTION' && (
              <>
                {formInspection?.map((form, iddx) => (
                  <div
                    className="grid grid-cols-1 sm:grid-cols-[1fr,1fr,auto,auto] gap-4 border p-4 mb-4 rounded-lg"
                    key={iddx}
                  >
                    {/* Field Name */}
                    <div className="flex flex-col">
                      <Label>Field Name</Label>
                      <Input
                        value={form?.fieldName ?? ''}
                        onChange={(e) => {
                          handeInspectionlFieldName(iddx, e?.target?.value);
                        }}
                      />
                      {(form?.fieldName.includes('.') ||
                        form?.fieldName.includes('$')) && (
                        <Alert
                          className="mt-2"
                          message=" $ and . are not allowed"
                          type="error"
                          showIcon
                        />
                      )}
                    </div>
                    {/* Field Type */}
                    <div className="flex flex-col w-full sm:w-[300px]">
                      <Label>Field Type</Label>
                      <Select
                        showSearch
                        value={form?.fieldType}
                        options={[...ctg, 'Media', 'Min-Max']?.map(
                          (option) => ({
                            label: option,
                            value: option,
                          })
                        )}
                        onChange={(e) => handleInspectionFieldInfo(iddx, e)}
                        placeholder="Select Type"
                      />
                    </div>
                    {/* View Icon */}
                    <div
                      className={`mt-4 flex justify-end sm:mt-6 ${['Table', 'DropDown', 'MultiSelect', 'MultiCheckbox'].includes(form?.fieldType) ? '' : 'hidden'}`}
                    >
                      <span
                        className="cursor-pointer hover:text-green-600"
                        onClick={() => setExistingFormData(form)}
                      >
                        <Eye />
                      </span>
                    </div>
                    <div className="mt-4 flex justify-end sm:mt-6">
                      <span
                        className="cursor-pointer text-red-600 hover:text-red-800"
                        onClick={() => {
                          setFormInspection((prev) =>
                            prev.filter(
                              (data) => data.fieldName !== form?.fieldName
                            )
                          );
                        }}
                      >
                        <Trash />
                      </span>
                    </div>
                  </div>
                ))}
                {existingFormData && (
                  <>
                    <ExistingFormData
                      existingFormData={existingFormData}
                      setExistingFormData={setExistingFormData}
                      FormCategoryType={FormCategoryType}
                      setFormInfomation={setFormInfomation}
                      setFormInspection={setFormInspection}
                      setDepartmentFormData={setDepartmentFormData}
                    />
                  </>
                )}
              </>
            )}
          </div>
          <div>
            {FormCategoryType === 'DEPARTMENT' && (
              <>
                {departmentFormData?.map((form, iddx) => {
                  return (
                    <div
                      className="grid grid-cols-1 sm:grid-cols-[1fr,1fr,auto,auto] gap-4 border p-4 mb-4 rounded-lg"
                      key={iddx}
                    >
                      {/* Field Name */}
                      <div className="flex flex-col">
                        <Label>Field Name</Label>
                        <Input
                          value={form?.fieldName ?? ''}
                          onChange={(e) => {
                            handeDepartmentFieldName(iddx, e?.target?.value);
                          }}
                        />
                      </div>
                      {/* Field Type */}
                      <div className="flex flex-col w-full sm:w-[300px]">
                        <Label>Field Type</Label>
                        <Select
                          showSearch
                          value={form?.fieldType}
                          options={[...ctg, 'Section', 'Media'].map(
                            (option) => ({
                              label: option,
                              value: option,
                            })
                          )}
                          onChange={(e) => handleDepartmentFieldInfo(iddx, e)}
                          placeholder="Select Type"
                        />
                      </div>
                      {/* View Icon */}
                      <div
                        className={`mt-4 flex justify-end sm:mt-6 ${['Table', 'DropDown', 'MultiSelect', 'MultiCheckbox'].includes(form?.fieldType) ? '' : 'hidden'}`}
                      >
                        <span
                          className="cursor-pointer hover:text-green-600"
                          onClick={() => setExistingFormData(form)}
                        >
                          <Eye />
                        </span>
                      </div>
                      <div className="mt-4 flex justify-end sm:mt-6">
                        <span
                          className="bg-white !text-red-600"
                          onClick={() => {
                            setDepartmentFormData((prev) =>
                              prev.filter(
                                (data) => data.fieldName !== form?.fieldName
                              )
                            );
                          }}
                        >
                          <Trash />
                        </span>
                      </div>
                    </div>
                  );
                })}
                {existingFormData && (
                  <>
                    <ExistingFormData
                      existingFormData={existingFormData}
                      setExistingFormData={setExistingFormData}
                      FormCategoryType={FormCategoryType}
                      setFormInfomation={setFormInfomation}
                      setFormInspection={setFormInspection}
                      setDepartmentFormData={setDepartmentFormData}
                    />
                  </>
                )}
              </>
            )}
          </div>
        </div>
        <div>
          <div className="heading mt-3">
            <Title level={5}>Additional Fields</Title>
          </div>
          <div className="input-wrapper">
            <div className="input-wrapper flex gap-3">
              <div className="flex flex-col w-full">
                <Label>Field Name</Label>
                <Input
                  value={FieldName}
                  onChange={(e) => {
                    setFieldName(e.target.value);
                  }}
                />
                {(FieldName.includes('.') || FieldName.includes('$')) && (
                  <Alert
                    className="mt-2"
                    message=" $ and . are not allowed"
                    type="error"
                    showIcon
                  />
                )}
              </div>
              <div className="flex flex-col w-full">
                <label className="mb-1 text-sm text-[#667085] flex items-center gap-[5px]">
                  Field Type
                  <InfoTooltip
                    position="right"
                    id="adminRole"
                    width="400px"
                    isHtml={true}
                    content={tooltipContent()}
                  />
                </label>
                <Select
                  showSearch
                  value={FieldType}
                  onChange={(e) => setFieldType(e)}
                  options={[
                    ...ctg,
                    ...(FormCategoryType === 'DEPARTMENT'
                      ? ['Section', 'Media']
                      : []),
                    ...(FormCategoryType === 'INSPECTION'
                      ? ['Media', 'Min-Max']
                      : []),
                  ].map((option) => ({
                    label: option,
                    value: option,
                  }))}
                  placeholder="Select Type"
                />
              </div>
              <div className="flex flex-col justify-center">
                <Label className="mb-1 text-sm text-[#667085]">Mandatory</Label>
                <Checkbox
                  checked={isMandatory[FieldName] || false}
                  onChange={(e) => {
                    setIsMandatory((prev) => ({
                      ...prev,
                      [FieldName]: e.target.checked,
                    }));
                  }}
                />
              </div>
            </div>
          </div>
          {FieldType === 'MultiCheckbox' && (
            <div className="flex flex-col  mt-5">
              <label className="mb-1 font-semibold text-[#667085]">
                No of Options{' '}
              </label>
              <Input
                type="number"
                value={noOption}
                onChange={(e) => {
                  setnoOption(e.target.value);
                }}
                placeholder="Number of Options"
              />
            </div>
          )}

          {(FieldType === 'DropDown' || FieldType === 'MultiSelect') && (
            <>
              <div className="mt-4">
                <p className="text-sm font-medium">Dropdown Options</p>
                {fieldOptions?.map((_, idX) => (
                  <div className="flex items-center gap-3 mt-2" key={idX}>
                    <Input
                      value={fieldOptions[idX]}
                      onChange={(e) =>
                        setFieldOptions((prev) =>
                          prev.map((el, index) => {
                            if (index === idX) {
                              return e.target.value;
                            }
                            return el;
                          })
                        )
                      }
                    />

                    <BiTrash
                      size={20}
                      className="cursor-pointer"
                      onClick={() =>
                        setFieldOptions((prev) =>
                          prev.filter((_, index) => idX !== index)
                        )
                      }
                    />
                  </div>
                ))}
                <p
                  className="text-sm text-blue-500 mt-2 cursor-pointer"
                  onClick={() => setFieldOptions((prev) => [...prev, ''])}
                >
                  + Add More
                </p>
              </div>
            </>
          )}

          {noOption !== 0 && (
            <div className="flex flex-row flex-wrap justify-start mt-4 px-8 gap-x-3">
              {[...Array(+noOption)].map((e, i) => (
                <TrComponent setlabel={setlabel} i={i} label={label} key={i} />
              ))}
            </div>
          )}
        </div>
      </>
    </Modal>
  );
};

export default EditModal;
