import { useEffect } from 'react';
import { toast } from 'react-toastify';

import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';

import { useUpdateAssignUsersMutation } from '../../slices/orderApiSlice';
import { useGetAllEmployeesQuery } from '../../slices/userApiSlice';

const UserAssignModalV2 = ({
  userModal,
  setUserModal,
  card,
  selectedUsers,
  setSelectedUsers,
}) => {
  const { data: employees = [] } = useGetAllEmployeesQuery();
  const [updateAssignUsers, { isLoading }] = useUpdateAssignUsersMutation();

  const handleUpdateAssignUser = async () => {
    let adminId;
    let user = JSON.parse(localStorage.getItem('user'))?.user;
    if (user.role === 'admin' || user.role === 'superuser') {
      adminId = user?._id;
    }
    const res = await updateAssignUsers({
      selectedUsers: selectedUsers,
      id: card?._id,
      adminId: adminId,
    });
    if (res) {
      toast.success('Assigned User Successfully');
      setUserModal(false);
    }
  };

  useEffect(() => {
    if (employees && card) {
      let assigned = [];
      for (let i of card?.assignedUsers) {
        for (let j of employees) {
          if (i === j?._id) {
            assigned?.push({
              value: j?._id,
              label: j?.name,
            });
            break;
          }
        }
      }
      setSelectedUsers(assigned);
    }
  }, [employees, card, setSelectedUsers]);

  return (
    <>
      {userModal && (
        <Modal
          title="User Assignment"
          onSubmit={handleUpdateAssignUser}
          btnIsLoading={isLoading}
          onCloseModal={() => {
            setUserModal(false);
          }}
        >
          {() => {
            return (
              <div className="h-full">
                <h4 className="mb-2 text-slate-400">Assign To:</h4>
                <MultiSelect
                  closeMenuOnSelect={false}
                  className="!w-full"
                  placeholder="Assign Users"
                  options={employees?.map((employee) => ({
                    value: employee?._id,
                    label: employee?.name,
                  }))}
                  userOptions={true}
                  enableSelectAll={false}
                  value={selectedUsers}
                  onChange={(e) => {
                    setSelectedUsers(e.target.value);
                  }}
                />
              </div>
            );
          }}
        </Modal>
      )}
    </>
  );
};

export default UserAssignModalV2;
