import { useEffect, useState } from 'react';
import {
  convertToMinsAndSecs,
  getTimeDifference,
} from '../../../helperFunction';
import Table from '../../global/components/Table';

const DowntimeTable = ({
  data,
  details,
  idleTime,

  kpiParam,
  deviceDatas,
}) => {
  const [tableData, setTableData] = useState([]);

  const tableHeaders = [
    'Date Time',
    'Event',
    'Duration',
    'Planned DT',
    'Unplanned DT',
    'Worker Name',
  ];

  // useEffect(() => {
  //   if (data && details) {
  //     let allPauseErrs = [];
  //     let tempPauseData = [];
  //     let finalPauseData = [];
  //     let tempInactiveData = [];
  //     let finalInactiveData = [];
  //     let finalIdleData = [];

  //     details.forEach((detail) => {
  //       // for pause time details
  //       const maoData = detail?.mao;
  //       const errorMessages = detail.errorMessages;

  //       if (maoData) {
  //         const pauseErrs = errorMessages.filter(
  //           (em) =>
  //             em.machineAndOperatorId === maoData._id &&
  //             em.error.action === 'pause'
  //         );
  //         allPauseErrs = [...allPauseErrs, ...pauseErrs];
  //         maoData.pauseTime.forEach((t, tIdx) => {
  //           if (
  //             new Date(t) <= new Date(data?.stopDate) &&
  //             new Date(maoData.resumeTime[tIdx]) >= new Date(data?.startDate)
  //           ) {
  //             tempPauseData = [
  //               ...tempPauseData,
  //               {
  //                 pauseTime: t,
  //                 resumeTime: maoData.resumeTime[tIdx],
  //                 errorMessage: pauseErrs[tIdx],
  //                 diff: getTimeDifference(t, maoData.resumeTime[tIdx]),
  //               },
  //             ];
  //           }
  //         });
  //       }

  //       // for inactive time
  //       let newStartTime = null;
  //       let newStopTime = null;

  //       // check if start time and stop time is same as than of shift

  //       const isStartDateSame =
  //         new Date(maoData?.startTime) >= new Date(data?.startDate);

  //       const isStopDateSame =
  //         new Date(maoData?.stopTime) <= new Date(data?.stopDate);

  //       if (!isStartDateSame) {
  //         // if not same then find first resume time that is >= shift start time
  //         const temp = maoData?.resumeTime?.find(
  //           (time) => new Date(time) >= new Date(data?.startDate)
  //         );

  //         // if resume time exists then set it as new start time or set new start time to shift start
  //         if (temp) {
  //           newStartTime = temp;
  //         } else {
  //           newStartTime = data?.startDate;
  //         }
  //       } else {
  //         // if same then set start time to new start time
  //         newStartTime = maoData?.startTime;
  //       }

  //       if (!isStopDateSame) {
  //         // if not same then find last pause time that is <= shift stop time

  //         const temp = maoData?.pauseTime?.findLast(
  //           (time) => new Date(time) <= new Date(data?.stopDate)
  //         );

  //         // if pause time exists and machine status equals pause then set it as new stop time or set new start time to shift start
  //         if (temp && maoData?.status === 'pause') {
  //           newStopTime = temp;
  //         } else {
  //           /**
  //            * if not exists or machine status no equal to pause
  //            * then check if current date is greater than shift stop
  //            * if greater then use shift stop date else use current date
  //            */
  //           newStopTime =
  //             new Date() > new Date(data?.stopDate)
  //               ? data?.stopDate
  //               : new Date();
  //         }
  //       } else {
  //         newStopTime = maoData?.stopTime;
  //       }

  //       tempInactiveData = [
  //         ...tempInactiveData,
  //         { action: 'start', time: newStartTime },
  //         { action: 'stop', time: newStopTime },
  //       ];
  //     });

  //     // for pause time details
  //     const uniqueArray = [
  //       ...new Map(allPauseErrs.map((item) => [item.error._id, item])).values(),
  //     ];

  //     uniqueArray.forEach((arr) => {
  //       const filtered = tempPauseData.filter(
  //         (t) => t.errorMessage.error._id === arr.error._id
  //       );
  //       const totalTime = Math.round(
  //         filtered.reduce((acc, curVal) => acc + curVal.diff, 0)
  //       );

  //       finalPauseData.push({
  //         reason: arr.error.message,
  //         count: filtered.length,
  //         totalTime,
  //         average: Math.round(totalTime / filtered.length),
  //       });
  //     });

  //     // for inactive time
  //     let tempMins = [];
  //     const firstStart = tempInactiveData.shift();
  //     const firstVal = getTimeDifference(
  //       +data.startDate,
  //       +new Date(firstStart?.time)
  //     );
  //     const lastStop = tempInactiveData.pop();
  //     const lastVal = getTimeDifference(
  //       +new Date(lastStop?.time),
  //       +data.stopDate
  //     );

  //     const filteredStop = tempInactiveData.filter((d) => d.action === 'stop');
  //     const filteredStart = tempInactiveData.filter(
  //       (d) => d.action === 'start'
  //     );

  //     tempMins = [...tempMins, firstVal];

  //     filteredStop.forEach((item, idx) => {
  //       const val = getTimeDifference(
  //         +new Date(item.time),
  //         +new Date(filteredStart[idx].time)
  //       );
  //       tempMins = [...tempMins, val];
  //     });

  //     tempMins = [...tempMins, lastVal];

  //     const totalMins = Math.round(
  //       tempMins.reduce((acc, curVal) => acc + curVal, 0)
  //     );

  //     if (totalMins > 0) {
  //       finalInactiveData.push({
  //         reason: 'Inactive Time',
  //         count: tempMins.length,
  //         totalTime: totalMins,
  //         average: Math.round(totalMins / tempMins.length),
  //       });
  //     }

  //     const totalIdle = idleTime.reduce(
  //       (acc, curVal) => acc + +curVal?.data?.[kpiParam],
  //       0
  //     );

  //     if (totalIdle && kpiParam) {
  //       finalIdleData.push({
  //         reason: 'Idle Time',
  //         count: idleTime.length,
  //         totalTime: totalIdle,
  //         average: totalIdle / idleTime.length,
  //       });
  //     }

  //     setTableData([...finalPauseData, ...finalInactiveData, ...finalIdleData]);
  //   }
  // }, [data, details, idleTime, kpiParam]);

  // useEffect(() => {
  //   if (isFor) {
  //     const downTime = tableData.reduce(
  //       (acc, curVal) => acc + +curVal.totalTime,
  //       0
  //     );

  //     const upTime = data?.width - 1 - downTime;

  //     setPanelDetails((prev) => ({
  //       ...prev,
  //       [isFor]: {
  //         ...(prev?.[isFor] || {}),
  //         downTime,
  //         upTime: prev?.[isFor]?.batch ? upTime : 0,
  //       },
  //     }));
  //   }
  // }, [tableData, isFor, data, setPanelDetails]);

  // const targetDetail = details?.map((el) => {
  //   return {
  //     ...el,
  //     errorMessages: el.errorMessages.filter(
  //       (currElem) => currElem.machineAndOperatorId === el.mao._id
  //     ),
  //   };
  // });

  // let newData = [];

  // targetDetail?.forEach((el) => {
  //   let data = el.mao?.pauseTime.map((currElem, index) => {
  //     const targetError = el?.errorMessages[index];
  //     const duration = getTimeDifference(
  //       new Date(currElem),
  //       new Date(el?.mao?.resumeTime[index])
  //     );
  //     return { targetError, duration };
  //   });
  //   newData.push(data);
  // });

  useEffect(() => {
    let allPauseErrs = [];
    let tempPauseData = [];
    let tempInactiveData = [];
    let allStopErrs = [];

    details.forEach((el) => {
      const maoData = el?.mao;
      const errorMessages = el?.errorMessages;

      if (maoData) {
        const pauseErrs = errorMessages.filter(
          (em) =>
            em.machineAndOperatorId === maoData._id &&
            em.error.action === 'pause'
        );

        const stopErrs = errorMessages.filter(
          (em) =>
            em.machineAndOperatorId === maoData._id &&
            em.error.action === 'stop'
        );

        allPauseErrs = [...allPauseErrs, ...pauseErrs];
        allStopErrs = [...allStopErrs, ...stopErrs];

        maoData?.pauseTime.forEach((currElem, index) => {
          tempPauseData = [
            ...tempPauseData,
            {
              duration: getTimeDifference(currElem, maoData.resumeTime[index]),
              errorMessage: pauseErrs[index],
            },
          ];
        });
      }

      const targettedError = allStopErrs.filter(
        (stopErr) => stopErr.time === maoData?.stopTime
      );

      if (targettedError.length > 0) {
        targettedError.forEach((err) => {
          tempInactiveData = [
            ...tempInactiveData,
            {
              action: 'stop',
              time: maoData?.stopTime,
              errorMessage: err,
            },
          ];
        });
      }
    });

    setTableData([...tempPauseData, ...tempInactiveData]);
  }, [details, idleTime, kpiParam, data]);

  const formatDate = (inputDate) => {
    const date = new Date(inputDate);
    return date.toLocaleString('en-US', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    });
  };

  return (
    // <table className="w-full rounded-2xl overflow-hidden my-10 bg-white">
    //   <thead className="text-gray-primary bg-gray-table text-md">
    //     <tr>
    //       <th className="py-2 px-4">#</th>
    //       <th className="py-2 px-4">Reason</th>
    //       <th className="py-2 px-4">Count</th>
    //       <th className="py-2 px-4">Total Time</th>
    //       <th className="py-2 px-4">Average</th>
    //     </tr>
    //   </thead>
    //   <tbody>
    //     {tableData.map((tData, tIdx) => (
    //       <tr key={tIdx} className="text-center font-semibold text-sm border-b">
    //         <td className="py-2 px-4">{tIdx + 1}</td>
    //         <td className="py-2 px-4">{tData.reason}</td>
    //         <td className="py-2 px-4">{tData.count}</td>
    //         <td className="py-2 px-4">
    //           {convertToMinsAndSecs(tData.totalTime)}
    //         </td>
    //         <td className="py-2 px-4">{convertToMinsAndSecs(tData.average)}</td>
    //       </tr>
    //     ))}
    //   </tbody>
    // </table>

    <Table className="w-full mt-10">
      <Table.Head className="w-full">
        <Table.Row>
          {tableHeaders.map((el, index) => (
            <Table.Th key={index} className="py-2 px-4 text-sm uppercase">
              {el}
            </Table.Th>
          ))}
        </Table.Row>
      </Table.Head>
      <Table.Body className="w-full bg-white">
        {deviceDatas
          ?.filter(
            (item) => item?.type === 'kpi' && item?.data.hasOwnProperty('IDLE') //eslint-disable-line
          )
          ?.map((el, index) => {
            const rfid = el['RFID'];
            const hasTag = el?.hasOwnProperty('tag'); //eslint-disable-line
            const idleDuration = Number(el?.data['IDLE']);
            const tagDuration = Number(el?.tag?.duration);

            return (
              <Table.Row key={index} className="w-full">
                <Table.Td>{el?.data?.displayDate.toUpperCase()}</Table.Td>
                <Table.Td>{hasTag ? el?.tag?.name : 'Unplanned'}</Table.Td>
                <Table.Td>{idleDuration} m</Table.Td>
                <Table.Td>
                  {hasTag && idleDuration <= tagDuration ? idleDuration : '-'}
                </Table.Td>
                <Table.Td>
                  {!hasTag
                    ? idleDuration
                    : hasTag && idleDuration > tagDuration
                    ? idleDuration - tagDuration
                    : '-'}
                </Table.Td>
                <Table.Td>
                  {rfid ? `${rfid?.name} ${rfid?.employeeId}` : '-'}
                </Table.Td>
              </Table.Row>
            );
          })}
        {tableData?.map((el, index) => {
          return (
            <Table.Row key={index}>
              <Table.Td>{formatDate(el?.errorMessage?.time)}</Table.Td>
              <Table.Td>
                {el?.errorMessage?.error?.parameter
                  ? `${el?.errorMessage?.error?.parameter};`
                  : ''}{' '}
                {el?.errorMessage?.error?.message}
              </Table.Td>
              <Table.Td>{`${
                convertToMinsAndSecs(el?.duration)[0]
              } m`}</Table.Td>
              <Table.Td>-</Table.Td>
              <Table.Td>{`${
                convertToMinsAndSecs(el?.duration)[0]
              } m`}</Table.Td>
              <Table.Td>{`${el?.errorMessage?.operator?.name} ${el?.errorMessage?.operator?.employeeId}`}</Table.Td>
            </Table.Row>
          );
        })}
      </Table.Body>
    </Table>
  );
};

export default DowntimeTable;
