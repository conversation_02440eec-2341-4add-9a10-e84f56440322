// import { useEffect, useState } from 'react';

// const RightSidebar = ({
//   children,
//   className,
//   openSideBar,
//   setOpenSideBar,
//   scale = '1/3',
//   setIsTableOPen,
//   onClose = () => {},
// }) => {
//   const [transitionState, setTransitionState] = useState(false);

//   useEffect(() => {
//     if (openSideBar) {
//       setTransitionState(openSideBar);
//     }
//   }, [openSideBar]);

//   return (
//     <>
//       {openSideBar ? (
//         <div
//           className={`w-screen h-screen  fixed top-0 left-0 z-[9999] transition-[background] overflow-hidden pt-3 pr-[6px]  ${
//             transitionState ? 'bg-black/10' : 'bg-transparent'
//           }`}
//         >
//           <div className="w-full h-[97vh] relative">
//             <div
//               className={`absolute top-1/2 -translate-y-1/2 h-full rounded-md bg-white overflow-hidden w-${scale} px-5 py-3 transition-[right] ease-in-out overflow-y-scroll no-scrollbar ${
//                 transitionState ? 'right-0' : '-right-full'
//               } ${className}`}
//             >
//               <span
//                 className="absolute top-3 right-5 cursor-pointer overflow-hidden"
//                 onClick={() => {
//                   setTransitionState(false);
//                   onClose();

//                   setTimeout(() => {
//                     setOpenSideBar(false);
//                   }, 150);
//                   if (setIsTableOPen) {
//                     setIsTableOPen(false);
//                   }
//                 }}
//               >
//                 X
//               </span>
//               {children}
//             </div>
//             <div
//               className="w-full h-full"
//               onClick={() => {
//                 onClose();
//                 setTransitionState(false);
//                 setTimeout(() => {
//                   setOpenSideBar(false);
//                 }, 150);
//                 if (setIsTableOPen) {
//                   setIsTableOPen(false);
//                 }
//               }}
//             ></div>
//           </div>
//         </div>
//       ) : null}
//     </>
//   );
// };

// export default RightSidebar;

import { Drawer } from 'antd';
// import { useEffect, useState } from 'react';
import { IoCloseSharp } from 'react-icons/io5';
const RightSidebar = ({
  children,
  className,
  openSideBar,
  title = 'Details',
  setOpenSideBar,
  setIsTableOpen,
  onClose = () => {},
  loading,
  scale = 736,
}) => {
  const handleClose = () => {
    setOpenSideBar(false);
    onClose();
    if (setIsTableOpen) {
      setIsTableOpen(false);
    }
  };

  return (
    <>
      <Drawer
        title={title}
        placement="right"
        className={`${className} !w-full`}
        width={scale}
        closable={false}
        onClose={handleClose}
        open={openSideBar}
        maskClosable={true}
        loading={loading}
        extra={
          <span onClick={handleClose} className="cursor-pointer">
            <IoCloseSharp size={24} />
          </span>
        }
      >
        {children}
      </Drawer>
    </>
  );
};

export default RightSidebar;
