import Header from '../global/components/Header';

export default function SalesInquiryHeader({ isEdit }) {
  return (
    <div className="flex justify-between items-center w-full">
      <div className="!min-w-[20rem]">
        <Header
          title={!isEdit && 'Sales Inquiry'}
          description=""
          hasInfoPopup
          infoTitle="Welcome to the Sales Inquiry Dashboard Page"
          infoDesc="Simplify Sales Inquiry entries with sales inquiry creation,updation and deletion and accesss to sales inquiry history entries, Get a comprehensive overview of Sales Inquiry forms created by the customer"
          paras={[
            'Effortlessly track, manage, and respond to sales inquiries in real-time through an intuitive and user-friendly interface. Gain valuable insights into the status of inquiries.',
          ]}
          classNames=""
        />
      </div>
      {/* <div className="flex items-end justify-end gap-3">
        <div className="flex items-center">
          <Button
            onClick={() => setAddColumns(true)}
            type="button"
            icon={
              <img
                src={CreateIcon}
                alt="Create Icon"
                className="w-5 h-5 object-contain relative"
              />
            }
          >
            + Add Columns
          </Button>
        </div>
      </div> */}
    </div>
  );
}
