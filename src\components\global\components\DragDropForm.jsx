import { useCallback, useRef, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { BiTrash } from 'react-icons/bi';
import { RxDragHandleDots2 } from 'react-icons/rx';
import { toast } from 'react-toastify';
import TableGenerate from '../../Forms/TableGenerate';
import Input from './Input';
const TYPE = 'FORM_FIELD';

// Main Component
export default function DragDropForm({
  fromDerpartmentForm,
  isMandatory,
  setIsMandatory,
  handleSave,
  formData,
  setFormData,
  ctg,
  noOption,
  setnoOption,
  fieldOptions,
  setFieldOptions,
  tableOptions,
  setTableOptions,
  label,
  setlabel,
  fieldOptionsMultiSelect,
  setFieldOptionsMultiSelect,
  droppedFields,
  setDroppedFields,
}) {
  const [formFields] = useState(ctg); // Keep formFields as static

  // Function to handle adding new items to droppedFields and rearranging items within droppedFields
  const moveItem = useCallback(
    (dragIndex, hoverIndex, source, destination) => {
      if (source === 'ctg' && destination === 'dropped') {
        // Add a new item to `droppedFields`
        const draggedItem = formFields[dragIndex];
        setDroppedFields((prev) => [...(prev || []), draggedItem]);
      } else if (source === 'dropped' && destination === 'dropped') {
        // Rearrange items within `droppedFields`
        setDroppedFields((prev) => {
          const newDroppedFields = [...prev];
          const [movedItem] = newDroppedFields.splice(dragIndex, 1);
          newDroppedFields.splice(hoverIndex, 0, movedItem);
          return newDroppedFields;
        });
        setFormData((prev) => {
          const newDroppedFields = [...prev];
          const [movedItem] = newDroppedFields.splice(dragIndex, 1);
          newDroppedFields.splice(hoverIndex, 0, movedItem);
          return newDroppedFields;
        });
      }
    },
    [formFields] // eslint-disable-line
  );

  // Function to delete an item from droppedFields
  const deleteItem = useCallback((index) => {
    setDroppedFields((prev) => prev.filter((_, i) => i !== index));
    setFormData((prev) => prev.filter((_, i) => i !== index));
  }, []); // eslint-disable-line

  return (
    <div className="flex gap-6 px-2 py-2  w-full h-full bg-gray-100 rounded-md items-start">
      {/* Draggable Form Fields */}
      <ul className="flex flex-col w-[30%] h-[90%] overflow-y-scroll gap-y-2 p-4 bg-white shadow-md rounded-lg">
        {formFields.map((el, index) => (
          <DraggableFormFields
            key={el}
            index={index}
            itemType="ctg"
            moveItem={moveItem}
          >
            {el}
          </DraggableFormFields>
        ))}
      </ul>

      {/* Drop Area for Dragged Items */}
      <DropArea
        formData={formData}
        handleSave={handleSave}
        setFormData={setFormData}
        fields={droppedFields}
        deleteItem={deleteItem}
        moveItem={moveItem}
        noOption={noOption}
        setnoOption={setnoOption}
        fieldOptions={fieldOptions}
        setFieldOptions={setFieldOptions}
        tableOptions={tableOptions}
        setTableOptions={setTableOptions}
        label={label}
        isMandatory={isMandatory}
        setIsMandatory={setIsMandatory}
        setlabel={setlabel}
        fieldOptionsMultiSelect={fieldOptionsMultiSelect}
        setFieldOptionsMultiSelect={setFieldOptionsMultiSelect}
        fromDerpartmentForm={fromDerpartmentForm}
      />
    </div>
  );
}

// Draggable Form Fields Component
function DraggableFormFields({ children, index, itemType }) {
  const ref = useRef(null);

  const [{ isDragging }, dragRef] = useDrag({
    type: TYPE,
    item: { index, itemType },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const dragDropRef = dragRef(ref);

  return (
    <li
      className="text-sm p-4 bg-gray-200  rounded-md font-semibold flex items-center justify-between gap-x-2 hover:cursor-move"
      ref={dragDropRef}
      style={{ opacity: isDragging ? 0.2 : 1 }}
    >
      {children}
      <RxDragHandleDots2 size={20} className="text-gray-500" />
    </li>
  );
}

function DropArea({
  fromDerpartmentForm,
  formData,
  setFormData,
  fields,
  deleteItem,
  noOption,
  moveItem,
  setnoOption,
  fieldOptions,
  setFieldOptions,
  tableOptions,
  setTableOptions,
  label,
  setlabel,
  setFieldOptionsMultiSelect,
  fieldOptionsMultiSelect,
  // handleSave,
  isMandatory,
  setIsMandatory,
}) {
  const [{ isOver }, dropRef] = useDrop({
    accept: TYPE,
    drop: (item) => {
      if (item.itemType === 'ctg') {
        moveItem(item.index, null, 'ctg', 'dropped');
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  return (
    <div
      ref={dropRef}
      className={`w-[90%] min-h-[624px] h-[80%] overflow-y-scroll px-4 py-4 rounded-lg flex flex-col items-center justify-start transition-all duration-200 bg-white ${
        isOver ? 'border-2 border-blue-300' : 'bg-white'
      } shadow-[0px_10px_25px_5px_rgba(0,0,0,0.2)]`}
    >
      {fields?.length === 0 && (
        <div className="text-gray-500 mt-20 border-4 flex items-center justify-center border-blue-200 border-dotted w-[90%] rounded-xl h-[10rem] font-semibold">
          Drag Here
        </div>
      )}
      <ul className="w-full flex flex-col gap-y-2">
        {fields?.map((field, index) => (
          <DraggableDropAreaItem
            key={index}
            formData={formData}
            setFormData={setFormData}
            index={index}
            field={field}
            moveItem={moveItem}
            deleteItem={deleteItem}
            noOption={noOption}
            setnoOption={setnoOption}
            fieldOptions={fieldOptions}
            setFieldOptions={setFieldOptions}
            tableOptions={tableOptions}
            setTableOptions={setTableOptions}
            label={label}
            setlabel={setlabel}
            isMandatory={isMandatory}
            setIsMandatory={setIsMandatory}
            fieldOptionsMultiSelect={fieldOptionsMultiSelect}
            setFieldOptionsMultiSelect={setFieldOptionsMultiSelect}
            fromDerpartmentForm={fromDerpartmentForm}
          />
        ))}
      </ul>
    </div>
  );
}

// Draggable DropArea Item Component
function DraggableDropAreaItem({
  fromDerpartmentForm,
  isMandatory,
  setIsMandatory,
  formData,
  setFormData,
  field,
  index,
  moveItem,
  deleteItem,
  noOption,
  setnoOption,
  fieldOptions,
  setFieldOptions,
  tableOptions,
  setTableOptions,
  label,
  setlabel,
  fieldOptionsMultiSelect,
  setFieldOptionsMultiSelect,
}) {
  const ref = useRef(null);

  const [{ isDragging }, dragRef] = useDrag({
    type: TYPE,
    item: { index, itemType: 'dropped' },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, dropRef] = useDrop({
    accept: TYPE,
    hover: (item) => {
      if (item.itemType === 'dropped' && item.index !== index) {
        moveItem(item.index, index, 'dropped', 'dropped');
        item.index = index;
      }
    },
  });

  const dragDropRef = dragRef(dropRef(ref));

  const handleFieldNameChange = (newFieldName, FieldType, index) => {
    setFormData((curr) => {
      const updatedData = curr.map((field, i) => {
        if (i === index) {
          return {
            ...field,
            fieldName: newFieldName,
            fieldType: FieldType,
            isMandatory: isMandatory[newFieldName] || false,
          };
        }
        return field;
      });

      if (index >= curr.length) {
        updatedData.push({ fieldName: newFieldName, fieldType: FieldType });
      }

      return updatedData;
    });
  };

  return (
    <li
      ref={dragDropRef}
      style={{ opacity: isDragging ? 0.2 : 1 }}
      className={`text-sm py-2 px-4 
     
         ${
           isDragging && 'border-2 border-blue-300'
         }  bg-blue-50 rounded-md font-semibold flex items-center justify-between`}
    >
      <div className="flex flex-col gap-10 items-start">
        {/* Drag Handle Icon */}
        <span className="flex gap-3">
          <RxDragHandleDots2 size={20} className="text-gray-500" /> {field}
        </span>

        {/* Main Field Content */}
        <div className="mt-[-20px]">
          <div className="flex gap-2">
            <div className="flex flex-col">
              <label className="mb-1 font-semibold text-[#667085]">
                Field Name{' '}
                <span className="text-xl text-red-500 -mt-2 -ml-1">*</span>
              </label>
              <Input
                className="!bg-white"
                value={
                  formData?.find((el, idx) => idx === index)?.fieldName || '' // Default to empty if not found
                }
                onChange={(e) =>
                  handleFieldNameChange(e.target.value, field, index)
                } // Update directly on input change
                placeholder="Field Name"
              />
            </div>
            {fromDerpartmentForm && (
              <div className="flex gap-3 text-xs">
                <div className="flex flex-row space-x-2 ml-3 items-center">
                  <Input
                    type="checkbox"
                    id="isMandatory"
                    name="isMandatory"
                    checked={isMandatory[formData?.[index]?.fieldName] || false}
                    onChange={(e) => {
                      const fieldname = formData?.[index]?.fieldName;

                      if (!fieldname) {
                        toast.error('Fill Form Name First');
                        return;
                      }

                      // Update `isMandatory` state with the new checkbox value
                      setIsMandatory((prev) => ({
                        ...prev,
                        [fieldname]: e.target.checked,
                      }));

                      // Update `formData` to include the `isMandatory` status for this field
                      setFormData((prev) =>
                        prev.map((el, idx) => {
                          if (idx === index) {
                            return {
                              ...el,
                              isMandatory: e.target.checked, // use updated checkbox state
                            };
                          }
                          return el;
                        })
                      );
                    }}
                  />

                  <label className="mb-1 font-semibold text-[#667085]">
                    Mandatory Field
                  </label>
                </div>
              </div>
            )}
          </div>

          {/* Conditional Rendering Based on Field Type */}
          {field === 'MultiCheckbox' && (
            <div className="flex flex-col mt-5">
              <label className="mb-1 font-semibold text-[#667085]">
                No of Options
              </label>
              <Input
                className={'!bg-white'}
                type="number"
                value={noOption}
                onChange={(e) => setnoOption(e.target.value)}
                placeholder="Number of Options"
              />
              {/* Display Options for MultiCheckbox */}
              {noOption !== 0 && (
                <div className="flex flex-row flex-wrap justify-start mt-4 gap-x-3">
                  {[...Array(+noOption)]?.map((_, i) => (
                    <div className="flex flex-col mt-2" key={i}>
                      <label className="mb-1 font-semibold text-[#667085]">
                        Label Name
                      </label>
                      <Input
                        className="!bg-white"
                        value={label?.[i] || ''}
                        onChange={(e) => {
                          let newLabel = [...label];
                          newLabel[i] = e.target.value;
                          setlabel(newLabel); // Set only the updated array without recreating others

                          setFormData((prev) =>
                            prev.map((el, idx) => {
                              if (idx === index) {
                                return {
                                  ...el,
                                  labelArray: label,
                                };
                              }
                              return el;
                            })
                          );
                        }}
                        placeholder="Label"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {field === 'DropDown' && (
            <div className="mt-4">
              <p className="text-sm font-medium">Dropdown Options</p>
              {fieldOptions?.map((_, idX) => (
                <div className="flex items-center gap-3 mt-2" key={idX}>
                  <Input
                    className="!bg-white"
                    value={fieldOptions[idX]?.value || ''} // Fallback to an empty string if undefined
                    onChange={(e) => {
                      const newValue = e.target.value;

                      // Update fieldOptions with the new value
                      setFieldOptions((prev) =>
                        prev.map((el, index) =>
                          index === idX
                            ? { label: newValue, value: newValue }
                            : el
                        )
                      );

                      // Update formData with the new fieldOptions
                      setFormData((prev) =>
                        prev.map((el, idx) => {
                          if (idx === index) {
                            return {
                              ...el,
                              fieldOptions: fieldOptions,
                            };
                          }
                          return el;
                        })
                      );
                    }}
                  />

                  <BiTrash
                    size={20}
                    className="cursor-pointer"
                    onClick={() =>
                      setFieldOptions((prev) =>
                        prev.filter((_, index) => idX !== index)
                      )
                    }
                  />
                </div>
              ))}
              <p
                className="text-sm text-blue-500 mt-2 cursor-pointer"
                onClick={() => setFieldOptions((prev) => [...(prev || []), ''])}
              >
                + Add More
              </p>
            </div>
          )}

          {/* multiselect case  */}

          {field === 'MultiSelect' && (
            <div className="mt-4">
              <p className="text-sm font-medium">Dropdown Options</p>
              {fieldOptionsMultiSelect?.map((_, idX) => (
                <div className="flex items-center gap-3 mt-2" key={idX}>
                  <Input
                    className={'!bg-white'}
                    value={fieldOptionsMultiSelect[idX]?.value || ['']}
                    onChange={(e) => {
                      const newValue = e.target.value;

                      // Update fieldOptions with the new value
                      setFieldOptionsMultiSelect((prev) =>
                        prev.map((el, index) =>
                          index === idX
                            ? { label: newValue, value: newValue }
                            : el
                        )
                      );

                      // Update formData with the new fieldOptions
                      setFormData((prev) =>
                        prev.map((el, idx) => {
                          if (idx === index) {
                            return {
                              ...el,
                              fieldOptionsMultiSelect: fieldOptionsMultiSelect,
                            };
                          }
                          return el;
                        })
                      );
                    }}
                  />
                  <BiTrash
                    size={20}
                    className="cursor-pointer"
                    onClick={() =>
                      setFieldOptionsMultiSelect((prev) =>
                        prev.filter((_, index) => idX !== index)
                      )
                    }
                  />
                </div>
              ))}
              <p
                className="text-sm text-blue-500 mt-2 cursor-pointer"
                onClick={() =>
                  setFieldOptionsMultiSelect((prev) => [...prev, ''])
                }
              >
                + Add More
              </p>
            </div>
          )}

          {/* Table Generation Component for Table Field */}
          {field === 'Table' && (
            <TableGenerate
              tableOptions={tableOptions}
              setTableOptions={setTableOptions}
              setFormData={setFormData}
              fromDragFormComponent={true}
              indexOfFormInfo={index}
            />
          )}
        </div>
      </div>

      {/* Delete Button */}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth={1.5}
        stroke="currentColor"
        className="size-6 cursor-pointer"
        onClick={() => deleteItem(index)}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
        />
      </svg>
    </li>
  );
}

// Drop Area Component
