import PurchaseIndent from './PurchaseIndent';
import PurchaseOrder from './PurchaseOrder';
import Quotation from './Quotation';
import SalesInquiry from './SalesInquiry';
import SalesOrder from './SalesOrder';
import WorkOrder from './WorkOrder';

const strategyFunction = ({ page }) => {
  switch (page) {
    case 'salesInquiry':
      return <SalesInquiry />;
    case 'quotation':
      return <Quotation />;
    case 'salesOrder':
      return <SalesOrder />;
    case 'purchaseIndent':
      return <PurchaseIndent />;
    case 'purchaseOrder':
      return <PurchaseOrder />;
    case 'workOrder':
      return <WorkOrder />;
    default:
      <></>;
  }
};

export default strategyFunction;
