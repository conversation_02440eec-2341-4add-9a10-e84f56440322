'use client';

import { useState } from 'react';
import {
  Card,
  Collapse,
  Image,
  Table as AntTable,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import {
  DownloadOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { downloadMedia } from '../../helperFunction';

const { Title, Text } = Typography;
const { Panel } = Collapse;

const PreviewInspectionForm = ({ inspectionData, inspectionRefData }) => {
  const [expandedMedia, setExpandedMedia] = useState(null);

  // Filter table type fields
  const tableType =
    Object.keys(inspectionData || {})
      .filter(
        (key) =>
          inspectionData?.[key]?.fieldType === 'Table' ||
          inspectionData?.[key]?.type === 'Table'
      )
      .map((key) => ({
        ...inspectionData?.[key],
        fieldName: key,
      })) || [];

  // Filter media type fields
  const mediaType =
    Object.keys(inspectionData || {})
      .filter((key) => inspectionData?.[key]?.fieldType === 'Media')
      .map((key) => ({
        ...inspectionData?.[key],
        fieldName: key,
      })) || [];

  // Filter regular fields (not table or media)
  const regularFields = Object.keys(inspectionData || {})
    .filter((key) => {
      const ftype = inspectionData?.[key]?.fieldType;
      return ftype !== 'Table' && ftype !== 'Media';
    })
    .map((key) => ({
      key,
      label: key,
      type: inspectionData?.[key]?.fieldType,
      conditions: renderInputBasedOnType(inspectionRefData || {}, key),
      value: formatFieldValue(inspectionData?.[key]),
    }));

  // Format field value based on type
  function formatFieldValue(field) {
    if (!field) return '-';

    const ftype = field.fieldType;

    if (ftype === 'MultiSelect') {
      return field.filledValue?.map((el) => el.value).join(', ') || '-';
    } else if (ftype === 'Min-Max') {
      return `Min: ${field.filledMinValue || '-'}, Max: ${field.filledMaxValue || '-'}`;
    } else if (ftype === 'Check') {
      return field.filledValue ? 'Yes' : 'No';
    } else {
      return field.filledValue || '-';
    }
  }

  // Generate columns for the regular fields table
  const columns = [
    {
      title: 'Label',
      dataIndex: 'label',
      key: 'label',
      width: '20%',
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: '15%',
      render: (type) => <Tag color="blue">{type}</Tag>,
    },
    {
      title: (
        <span>
          Check Conditions{' '}
          <Tooltip title="Validation conditions for this field">
            <InfoCircleOutlined />
          </Tooltip>
        </span>
      ),
      dataIndex: 'conditions',
      key: 'conditions',
      width: '30%',
    },
    {
      title: 'Entered Value',
      dataIndex: 'value',
      key: 'value',
      width: '35%',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Regular Fields */}
      <Card
        title={<Title level={4}>Inspection Details</Title>}
        bordered={false}
        className="shadow-sm"
      >
        <AntTable
          dataSource={regularFields}
          columns={columns}
          pagination={false}
          rowClassName={(record, index) =>
            index % 2 === 0 ? 'bg-gray-50' : ''
          }
          size="middle"
        />
      </Card>

      {/* Table Fields */}
      {tableType?.length > 0 && (
        <Card
          title={<Title level={4}>Table Data</Title>}
          bordered={false}
          className="shadow-sm"
        >
          <Collapse defaultActiveKey={tableType.map((_, i) => i.toString())}>
            {tableType.map((form, fIdx) => (
              <Panel
                header={<Text strong>{form.fieldName}</Text>}
                key={fIdx.toString()}
              >
                <div className="overflow-x-auto">
                  <AntTable
                    size="small"
                    pagination={false}
                    bordered
                    dataSource={Array.from({
                      length: +form?.newRowNo + +form?.noOfRows,
                    }).map((_, index) => ({
                      key: index,
                      rowNum: form?.row?.[index] || index + 1,
                      ...form?.columns?.reduce((acc, col, colIndex) => {
                        const colData =
                          form?.rowData?.[index + 1]?.at(colIndex);
                        if (col?.columnType === 'Min-Max') {
                          acc[`${col.columnName}_min`] =
                            colData?.filledMinValue || '-';
                          acc[`${col.columnName}_max`] =
                            colData?.filledMaxValue || '-';
                        } else {
                          acc[col.columnName] = colData?.value || '-';
                        }
                        return acc;
                      }, {}),
                    }))}
                    columns={[
                      {
                        title: 'Sr. No.',
                        dataIndex: 'rowNum',
                        key: 'rowNum',
                        width: 80,
                        align: 'center',
                      },
                      ...form?.columns?.flatMap((col) => {
                        if (col?.columnType === 'Min-Max') {
                          return [
                            {
                              title: `${col.columnName} (Min)`,
                              dataIndex: `${col.columnName}_min`,
                              key: `${col.columnName}_min`,
                              align: 'center',
                            },
                            {
                              title: `${col.columnName} (Max)`,
                              dataIndex: `${col.columnName}_max`,
                              key: `${col.columnName}_max`,
                              align: 'center',
                            },
                          ];
                        } else {
                          return [
                            {
                              title: col.columnName,
                              dataIndex: col.columnName,
                              key: col.columnName,
                              align: 'center',
                            },
                          ];
                        }
                      }),
                    ]}
                  />
                </div>
              </Panel>
            ))}
          </Collapse>
        </Card>
      )}

      {/* Media Fields */}
      {mediaType?.length > 0 && (
        <Card
          title={<Title level={4}>Media Attachments</Title>}
          bordered={false}
          className="shadow-sm"
        >
          {mediaType.map((field, index) => (
            <div key={index} className="mb-6 last:mb-0">
              <Text strong className="text-base mb-3 block">
                {field.fieldName}
              </Text>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {field?.filledValue?.map((media, idx) => (
                  <Card
                    key={idx}
                    hoverable
                    className="overflow-hidden"
                    cover={
                      media?.type === 'application/pdf' ? (
                        <div className="h-32 flex items-center justify-center bg-gray-100">
                          <FileTextOutlined
                            style={{ fontSize: 48, color: '#f5222d' }}
                          />
                        </div>
                      ) : (
                        <div className="h-32 flex items-center justify-center bg-gray-50">
                          <Image
                            alt={media.name}
                            src={media.data || '/placeholder.svg'}
                            preview={false}
                            className="max-h-32 object-contain"
                            onClick={() => setExpandedMedia(media)}
                          />
                        </div>
                      )
                    }
                    actions={[
                      <Tooltip title="Download" key="download">
                        <DownloadOutlined
                          onClick={() => downloadMedia(media)}
                        />
                      </Tooltip>,
                    ]}
                  >
                    <Tooltip title={media.name}>
                      <Text ellipsis className="block text-center">
                        {media.name}
                      </Text>
                    </Tooltip>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </Card>
      )}

      {/* Image Preview Modal */}
      {expandedMedia && (
        <Image
          src={expandedMedia.data || '/placeholder.svg'}
          alt={expandedMedia.name}
          style={{ display: 'none' }}
          preview={{
            visible: true,
            src: expandedMedia.data,
            onVisibleChange: (visible) => {
              if (!visible) setExpandedMedia(null);
            },
          }}
        />
      )}
    </div>
  );
};

export default PreviewInspectionForm;

// Helper function to render input based on type
const renderInputBasedOnType = (refData, form) => {
  const infoObject = refData[form];
  if (!infoObject) return '-';

  if (infoObject?.fieldType === 'Date') {
    const options = [
      { value: 'gt', label: 'Greater Than' },
      { value: 'lt', label: 'Lesser Than' },
      { value: 'eq', label: 'Equal To' },
    ];

    const condition =
      options.find((option) => option.value === infoObject?.checkCondition)
        ?.label || '-';

    return (
      <div>
        <Tag color="purple">{condition}</Tag> {infoObject?.date || '-'}
      </div>
    );
  }

  if (infoObject?.fieldType === 'MultiSelect') {
    return (
      infoObject?.options?.map((el, i) => (
        <Tag key={i} color="green">
          {el.value}
        </Tag>
      )) || '-'
    );
  }

  if (infoObject?.fieldType === 'Range') {
    return (
      <div>
        <Tag color="blue">Min: {infoObject?.min || '-'}</Tag>
        <Tag color="orange">Max: {infoObject?.max || '-'}</Tag>
      </div>
    );
  }

  if (infoObject?.fieldType === 'DropDown') {
    return <Tag color="geekblue">{infoObject?.value || '-'}</Tag>;
  }

  if (infoObject?.fieldType === 'Range Threshold') {
    return (
      <div>
        <Tag color="cyan">Average: {infoObject?.average || '-'}</Tag>
        <Tag color="magenta">Margin: {infoObject?.margin || '-'}</Tag>
      </div>
    );
  }

  if (infoObject?.fieldType === 'String') {
    return infoObject?.string || '-';
  }

  if (infoObject?.fieldType === 'MultiCheckbox') {
    return (
      infoObject?.labelArray?.map((label, i) => (
        <Tag key={i} color="volcano">
          {label}
        </Tag>
      )) || '-'
    );
  }

  return '-';
};
