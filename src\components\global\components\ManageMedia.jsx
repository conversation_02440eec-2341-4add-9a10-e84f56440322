import { Button, Image, Modal } from 'antd';
import UploadButton from '../../UploadButton';
import { useEffect, useState } from 'react';
import Table from './Table';
import { Trash } from 'lucide-react';

export default function ManageMedia({ open, setOpen, medias, onSubmit }) {
  const [attachments, setAttachments] = useState([]);
  const [deletedMedias, setDeletedMedias] = useState([]);

  useEffect(() => {
    if (medias?.length > 0) {
      setAttachments(medias);
    }
  }, [medias]);

  const changeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name;
      let ftype = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          id: Date.now(),
          name: fname,
          type: ftype,
          data: url,
          description: '',
        };
        setAttachments((prev) => {
          const exists = prev.some((file) => file.name === data.name);
          if (!exists) {
            return [...prev, data];
          }
          return prev;
        });
      });
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleDelete = async (item) => {
    const newAtt = [];
    for (let i = 0; i < attachments.length; i++) {
      const el = attachments[i];
      if (el?.id === item?.id || el?._id === item?._id) {
        if (item?._id) setDeletedMedias((prev) => [...prev, item?._id]);
      } else {
        newAtt.push(el);
      }
    }
    setAttachments(newAtt);
  };

  return (
    <Modal
      title="Manage Media"
      open={open}
      width={'50%'}
      onClose={handleClose}
      onCancel={handleClose}
      footer={[
        <Button
          key={'1'}
          onClick={() => {
            if (onSubmit) onSubmit(attachments, deletedMedias);
          }}
        >
          Submit
        </Button>,
      ]}
    >
      <UploadButton onChange={changeHandler} width="w-full" multiple />

      <Table className={'mt-5'}>
        <Table.Head>
          <Table.Row>
            <Table.Th>Item</Table.Th>
            <Table.Th>Name</Table.Th>
            <Table.Th>Type</Table.Th>
            <Table.Th>Action</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {attachments?.map((att) => (
            <Table.Row key={att?._id || att?.id}>
              <Table.Td>
                <Image src={att?.data} alt={att?.name} width={'50px'} />
              </Table.Td>
              <Table.Td>{att?.name}</Table.Td>
              <Table.Td>{att?.type}</Table.Td>
              <Table.Td>
                <Button danger type="primary" onClick={() => handleDelete(att)}>
                  <Trash />
                </Button>
              </Table.Td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </Modal>
  );
}
