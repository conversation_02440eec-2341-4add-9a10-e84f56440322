import { useContext, useEffect, useState } from 'react';
// import { useLazyGetPartPagesQuery } from '../../slices/partApiSlice';
import { Radio, Table } from 'antd';
import { useGetAllPartsForOptionsQuery } from '../../slices/partApiSlice';
import { useGetAllProductsForOptionsQuery } from '../../slices/productApiSlice';
import { Store } from '../../store/Store';
import ItemModal from '../v3/WorkOrder/ItemModal';
import AssemblyDashboard from './Assembly/AssemblyDashboard';
import BomDetail from './BomDetail';
import InhouseDashboard from './Inhouse/InhouseDashboard';
import ProjectDetailsTab from './ProjectDetailsTab';
import QcDashboard from './QC/QcDashboard';

const BomDetailsV2 = ({
  items,
  assets,
  allParts,
  allProducts,
  type,
  orderQuantity,
}) => {
  const [rmToShow, setRmToShow] = useState({
    rawMaterials: [],
    assets: [],
    orderQuantity: 0,
    open: false,
  });
  const [mode, setMode] = useState('rawMaterials');

  const subtractReserved = (data) => {
    if (!data?.quantity) return 0;
    const diff = data?.quantity - (data?.reserved || 0);
    return (diff <= 0 ? 0 : diff) || 0;
  };

  const getInStock = (record) => {
    let all = [...allParts, ...allProducts];
    let item = all?.find(
      (elem) => elem?.value === (record?._id || record?.value)
    );
    return {
      inStockToReserved: `${subtractReserved(item) || 0}\xA0(${
        item?.quantity?.toFixed(2) || 0
      },\xA0${item?.reserved?.toFixed(2) || 0})`,
      inStock: item?.quantity?.toFixed(2) || 0,
    };
  };

  const closeRMModal = () => {
    setRmToShow({
      rawMaterials: {},
      orderQuantity: 0,
      open: false,
    });
  };

  const rmColumns = [
    {
      title: () => <span>{`Name [WO Order Quantity = ${orderQuantity}]`}</span>,
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => <span>{record?.name || record?.item?.name}</span>,
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      render: (_, record) => (
        <span>
          {record?.uom
            ? record?.uom?.toUpperCase()
            : record?.item?.uom?.toUpperCase()}
        </span>
      ),
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
      render: (_, record) => <span>{record?.units}</span>,
    },
    {
      title: 'In Stock',
      dataIndex: 'inStock',
      key: 'inStock',
      render: (_, record) => (
        <span>{getInStock(record)?.inStockToReserved}</span>
      ),
    },
    {
      title: 'Required Stock',
      dataIndex: 'requiredStock',
      key: 'requiredStock',
      render: (_, record) => (
        <span>
          {Math.max(
            0,
            parseInt(record?.units) * orderQuantity -
              getInStock(record)?.inStock
          )}
        </span>
      ),
    },
  ];

  const bomColumns = [
    {
      title: () => <span>{`Name [WO Order Quantity = ${orderQuantity}]`}</span>,
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => <span>{record?.name}</span>,
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (_, record) => <span>{record?.rowCategory?.toUpperCase()}</span>,
    },
    {
      title: 'Units',
      dataIndex: 'units',
      key: 'units',
      render: (_, record) => <span>{record?.units}</span>,
    },
    {
      title: 'In Stock',
      dataIndex: 'inStock',
      key: 'inStock',
      render: (_, record) => (
        <span>{getInStock(record)?.inStockToReserved}</span>
      ),
    },
    {
      title: 'Required Stock',
      dataIndex: 'requiredStock',
      key: 'requiredStock',
      render: (_, record) => (
        <span>
          {Math.max(
            0,
            parseInt(record?.units) *
              (orderQuantity - getInStock(record)?.inStock)
          )}
        </span>
      ),
    },
    {
      title: '',
      dataIndex: 'rm',
      key: 'rm',
      render: (_, record) => (
        <span
          className={`${record?.rawMaterials?.length > 0 ? 'text-blue-500 underline cursor-pointer hover:text-blue-300' : 'text-gray-400'}`}
          onClick={() => {
            if (record?.rawMaterials?.length > 0) {
              setRmToShow({
                rawMaterials: record?.rawMaterials || [],
                assets: record?.assetData || [],
                orderQuantity:
                  parseInt(record?.units) *
                  (orderQuantity - getInStock(record)?.inStock),
                open: true,
              });
            }
          }}
        >
          Show Raw Materials
        </span>
      ),
    },
  ];

  const assetColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => <span>{record?.name}</span>,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (_, record) => <span>{record?.description}</span>,
    },
  ];

  const handleModeChange = (e) => {
    setMode(e.target.value);
  };
  return (
    <div className="mt-2">
      <ItemModal
        items={rmToShow?.rawMaterials}
        assets={rmToShow?.assets}
        showModal={rmToShow?.open}
        orderQuantity={rmToShow?.orderQuantity}
        closeModal={closeRMModal}
        allParts={allParts}
        allProducts={allProducts}
        type="rm"
      />
      {type !== 'bom' && (
        <Radio.Group
          onChange={handleModeChange}
          value={mode}
          style={{
            marginBottom: 8,
          }}
        >
          <Radio.Button value="rawMaterials">Raw Materials</Radio.Button>
          <Radio.Button value="assets">Assets</Radio.Button>
        </Radio.Group>
      )}
      <div>
        <Table
          columns={
            type === 'bom'
              ? bomColumns
              : mode === 'rawMaterials'
                ? rmColumns
                : assetColumns
          }
          dataSource={
            type === 'bom'
              ? items?.item
              : mode === 'rawMaterials'
                ? items
                : assets
          }
          rowKey="_id"
          pagination={false}
          scroll={{ x: 'max-content' }}
          className="rounded-lg"
        />
      </div>
    </div>
  );
};

function CuDashboard({
  cuProject,
  selectedData,
  allMachines,
  allEmployees,
  getAllPo,
  selectedWo,
  itemForJob,
  setCusForStatus,
  selectedCi,
}) {
  const { data: allParts = [] } = useGetAllPartsForOptionsQuery();
  const { data: allProducts = [] } = useGetAllProductsForOptionsQuery();
  const [activeTab, setActiveTab] = useState({
    tab1: true,
    tab2: false,
    tab3: false,
  });
  const [partsData, setPartsData] = useState([]);
  const [item, setItem] = useState({
    value: '',
    type: '',
  });
  const [bomToShow, setBomToShow] = useState({
    bom: {},
  });

  const [rmToShow, setRMToShow] = useState({
    rawMaterials: [],
    assets: [],
    orderQuantity: 0,
  });

  useEffect(() => {
    let rm = selectedWo?.rawMaterials;
    if (rm?.length >= 1) {
      if (selectedData?.model?.assemblyPart) {
        let item = {
          value: selectedData?.model?.assemblyPart,
          type: 'part',
        };
        setItem(item);

        let correspondingItem = rm?.find((itm) => {
          return itm?.partId === selectedData?.model?.assemblyPart?._id;
        });
        setPartsData(correspondingItem?.rawMaterials);
      } else if (selectedData?.model?.assemblyManualEntry) {
        let item = {
          value: selectedData?.model?.assemblyManualEntry,
          type: 'manualEntry',
        };
        setItem(item);
        let correspondingItem = rm?.find((itm) => {
          return itm?.partId === selectedData?.model?.assemblyManualEntry;
        });

        setPartsData(correspondingItem?.rawMaterials);
      }
    }
  }, [selectedWo, selectedData.model]);

  useEffect(() => {
    if (itemForJob?.itemId?._id !== undefined && selectedWo) {
      if (itemForJob?.bom?.item?.length > 0) {
        setBomToShow({
          bom: itemForJob?.bom,
          orderQuantity: itemForJob?.units * selectedWo?.orderQuantity,
        });
      }
      if (itemForJob?.rawMaterials?.length > 0) {
        setRMToShow({
          rawMaterials: itemForJob?.rawMaterials || [],
          assets: itemForJob?.assetData || [],
          orderQuantity: itemForJob?.units,
        });
      }
    }
  }, [itemForJob, selectedWo]);

  const { refreshPrevTile } = useContext(Store);

  let processType = selectedData?.flow?.processCategory;

  {
    if (cuProject?.status === 'complete')
      return (
        <div className="w-full h-full flex justify-center items-center text-2xl sm:mt-[1%] mt-[50%] text-green-500">
          Process Completed!!!
        </div>
      );
  }

  return (
    <div className="w-full">
      {cuProject?._id ? (
        <div className="w-full  border-black/50 bg-white overflow-scroll p-3 rounded-md pb-">
          {/* Header --> */}
          <div className="w-full sm:h-[5%] md:h-fit rounded-l-lg rounded-r-lg border-[1px] border-blue-500 bg-blue-500 flex">
            {/* Tab 1 */}

            <section
              className={`relative md:text-lg sm:mt-0 rounded-l-lg border-r-[1px] border-blue-500 hover:cursor-pointer w-full text-center py-2 ${
                activeTab.tab1
                  ? 'bg-blue-500 text-white'
                  : ' bg-white text-blue-500'
              }`}
              onClick={() =>
                setActiveTab({ tab1: true, tab2: false, tab3: false })
              }
            >
              Real Time
            </section>

            {/* Tab 2 */}

            <section
              className={`relative md:text-lg hover:cursor-pointer w-full text-center py-2 ${
                activeTab.tab2
                  ? 'bg-blue-500 text-white'
                  : ' bg-white text-blue-500'
              }`}
              onClick={() =>
                setActiveTab({ tab1: false, tab2: true, tab3: false })
              }
            >
              Project Details
            </section>

            {/* Tab 3 */}

            <section
              className={`relative md:text-lg hover:cursor-pointer border-l-[1px] border-blue-500 rounded-r-lg w-full text-center py-2 ${
                activeTab.tab3
                  ? 'bg-blue-500 text-white'
                  : ' bg-white text-blue-500'
              }`}
              onClick={() =>
                setActiveTab({ tab1: false, tab2: false, tab3: true })
              }
            >
              BOM Details
            </section>
          </div>
          {/* Body --> */}

          <>
            {/* Tab-1 --> */}
            {activeTab?.tab1 && (
              <>
                {processType === 'QC' ? (
                  <>
                    <QcDashboard
                      activeTile={cuProject?._id}
                      woId={selectedData?.wo}
                      cuProject={cuProject}
                      cuprocess={cuProject?.productionFlow?.processes}
                      // prevProcessData={prevProcessData}
                      refreshPrevTile={refreshPrevTile}
                      currProcess={cuProject?.mqtt?.process}
                      allEmployees={allEmployees}
                      getAllPo={getAllPo}
                      itemForJob={itemForJob}
                      setCusForStatus={setCusForStatus}
                      selectedCi={selectedCi}
                      selectedData={selectedData}
                    />
                  </>
                ) : processType === 'Assembly' ? (
                  <AssemblyDashboard
                    activeTile={cuProject?._id}
                    woId={selectedData?.wo}
                    cuProject={cuProject}
                    cuprocess={cuProject?.productionFlow?.processes}
                    // prevProcessData={prevProcessData}
                    // refreshPrevTile={refreshPrevTile}
                    currProcess={cuProject?.mqtt?.process}
                    allEmployees={allEmployees}
                    getAllPo={getAllPo}
                    itemForJob={itemForJob}
                    // currProcess={cuData?.machineId?.mqtt?.process}
                  />
                ) : processType === 'Inhouse' ? (
                  <InhouseDashboard
                    cuProject={cuProject}
                    selectedData={selectedData}
                    allMachines={allMachines}
                    allEmployees={allEmployees}
                    getAllPo={getAllPo}
                    itemForJob={itemForJob}
                    setCusForStatus={setCusForStatus}
                    selectedCi={selectedCi}
                  />
                ) : null}
              </>
            )}

            {/* Tab-2 --> */}

            {activeTab.tab2 && (
              <ProjectDetailsTab
                cuProject={cuProject}
                itemForJob={itemForJob}
              />
            )}

            {/* Tab-3 --> */}

            {activeTab?.tab3 && (
              <>
                {partsData?.length ? (
                  <div className="w-full h-full overflow-y-scroll no-scrollbar mt-2">
                    <BomDetail partsData={partsData} item={item} />
                    {/* <BomTable bom={cuProject?.project?.bom} cuData={cuData} /> */}
                  </div>
                ) : itemForJob?.itemId?._id !== undefined ? (
                  <div>
                    {itemForJob?.bom?.item?.length > 0 ? (
                      <BomDetailsV2
                        items={bomToShow?.bom}
                        orderQuantity={bomToShow?.orderQuantity}
                        allParts={allParts}
                        allProducts={allProducts}
                        type="bom"
                      />
                    ) : (
                      <BomDetailsV2
                        items={rmToShow?.rawMaterials}
                        assets={rmToShow?.assets}
                        orderQuantity={rmToShow?.orderQuantity}
                        allParts={allParts}
                        allProducts={allProducts}
                        type="rm"
                      />
                    )}
                  </div>
                ) : (
                  <p className="mt-5 text-center font-bold text-xl">
                    No BOM Details
                  </p>
                )}
              </>
            )}
          </>
        </div>
      ) : (
        <div className="w-full h-full flex justify-center items-center sm:mt-[25%] mt-[50%]">
          Please select a project
        </div>
      )}
    </div>
  );
}

export default CuDashboard;
