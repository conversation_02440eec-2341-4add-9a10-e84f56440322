import { useState, useEffect } from 'react';
import {
  CircularProgressbarWithChildren,
  buildStyles,
} from 'react-circular-progressbar';
import { calculateValueForViews } from '../../../../helperFunction';

const WorkerEfficiency = ({
  mao,
  cuProject,
  deviceDatas,
  selectOptions,
  values,
}) => {
  const [itemsPerHour, setItemsPerHour] = useState(0);
  const [progress, setProgress] = useState(0);

  const parameter = selectOptions?.project?.processGoalView?.parameters?.find(
    (param) => param?.name === 'Batch Size'
  );

  useEffect(() => {
    if (selectOptions && cuProject) {
      let tempBatchData = selectOptions?.createInput?.goalsTable
        .find((table) => table.flowId === selectOptions.flowId)
        ?.tableData?.find(
          (data) => data.batchNo === cuProject?.batchInfo?.batchNo
        );

      if (cuProject?.isMultiProcess) {
        tempBatchData = selectOptions?.createInput?.goalsTable
          .find((table) => table.flowId === selectOptions.flowId)
          ?.tableData?.find(
            (data) => data.batchNo === cuProject?.batchInfo?.batchNo
          )
          ?.subProcessData?.find(
            (i, idx) => idx === cuProject?.subProcessIndex
          );
      }

      setItemsPerHour(tempBatchData?.itemsPerHour || 0);
    }
  }, [selectOptions, cuProject]);

  useEffect(() => {
    if (mao && cuProject && deviceDatas && values && parameter) {
      const isComplete = mao?.status === 'complete';
      const checkDate = new Date().toLocaleDateString();
      const filteredErrorMessages = cuProject?.errorMessages
        ?.filter((em) => em?.machineAndOperatorId === mao?._id)
        ?.map((em, idx) => ({ ...em, time: mao?.resumeTime[idx] }))
        ?.filter((em) => new Date(em?.time).toLocaleDateString() === checkDate);

      let workerStartTime = filteredErrorMessages[0]?.time;

      const isShiftChange = filteredErrorMessages?.find(
        (em) => em?.error?.message === 'Shift Change'
      );

      if (isShiftChange) {
        workerStartTime = isShiftChange?.time;
      }

      if (workerStartTime) {
        let startCount = 0;

        const batchSize = calculateValueForViews(parameter?.formula, values, [
          mao?.machine.machineId,
        ]);

        mao?.machine?.devices?.forEach((device) => {
          const firstCount = deviceDatas?.find(
            (item) =>
              item?.machine === mao?.machine._id &&
              item?.device === device &&
              +new Date(item?.data?.createdAt) > +new Date(workerStartTime)
          );

          if (firstCount) {
            startCount = startCount + +firstCount?.data?.COUNT;
          }
        });

        const count = batchSize - startCount;

        const currentDateTime = isComplete
          ? +new Date(mao?.stopTime)
          : +new Date();

        const toatlTimeWorked =
          (currentDateTime - +new Date(workerStartTime)) / 3600000;

        const progress = (count / toatlTimeWorked / itemsPerHour) * 100;

        setProgress(Math.round(progress > 100 ? 100 : progress));
      }
    }
  }, [mao, cuProject, deviceDatas, values, parameter, itemsPerHour]);

  return (
    <CircularProgressbarWithChildren
      value={progress}
      strokeWidth={8}
      styles={buildStyles({
        rotation: 0.25,
        strokeLinecap: 'butt',
        pathTransitionDuration: 0.5,
        pathColor: `#71A5DE`,
        trailColor: '#fff',
      })}
    >
      <p>{progress}%</p>
    </CircularProgressbarWithChildren>
  );
};
export default WorkerEfficiency;
