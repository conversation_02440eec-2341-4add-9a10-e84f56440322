import { useState } from 'react';
import { IoCloseSharp } from 'react-icons/io5';
import { toast } from 'react-toastify';
import useDebounceValue from '../../hooks/useDebounceValue';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice';
import {
  useAddManyProductsMutation,
  useGetProductByNameForMasterMutation,
} from '../../slices/productApiSlice';
import { useGetStorePagesQuery } from '../../slices/storeApiSlice';
import { getInStock } from '../v3/WorkOrder/CreateWorkOrder';
import Modal from './components/Modal';
import MultiSelect from './components/MultiSelect';
import Select from './components/Select';
import Table from './components/Table';
import ProductMasterSelector from './ProductMasterSelector';

export default function ProductMasterSelectorModal({ onSubmit, onCloseModal }) {
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  const debounceSearch = useDebounceValue(searchTerm);

  const { data: { results: stores } = {} } = useGetStorePagesQuery(
    {
      page: 1,
      limit: 50,
      debounceSearch,
    },
    { refetchOnMountOrArgChange: true }
  );
  const [getProductByNameForMaster] = useGetProductByNameForMasterMutation();
  const { data: { dropdowns = [] } = {} } = useGetDropdownsQuery();
  const [addManyProducts, { isLoading }] = useAddManyProductsMutation();
  const uoms = dropdowns?.find((i) => i?.name === 'uom')?.values || [];

  const handleAddProduct = async (val) => {
    const exists = selectedProducts?.find((el) => el?.name === val);

    if (exists) {
      toast.error(`Product ${val} already selected`);
      return;
    }

    const product = await getProductByNameForMaster({
      data: { name: val },
    }).unwrap();
    let data;

    if (product) {
      const obj = { fg: 0, sfg: 0 };
      for (let i = 0; i < product?.batches.length; i++) {
        const batch = product?.batches[i];
        if (batch?.itemType === 'finishedGoods') {
          obj.fg += batch?.quantity || 0;
        } else if (batch?.itemType === 'semiFinishedGoods') {
          obj.sfg += batch?.quantity || 0;
        }
      }
      data = {
        key: product?._id,
        _id: product?._id,
        name: product?.name,
        stores: product?.stores,
        uom: product?.uom,
        inStock: getInStock({ product }),
        ...obj,
      };
    } else {
      data = {
        key: Date.now(),
        _id: null,
        name: val,
        stores: [],
        uom: 'Nos',
        inStock: '0 (0, 0)',
        fg: 0,
        sfg: 0,
      };
    }
    if (data) {
      setSelectedProducts((prev) => [...prev, data]);
      toast.success('Product selected');
    }
  };

  const handleSubmit = async (e) => {
    e?.preventDefault();
    const storeExists = selectedProducts?.some(
      (el) => el?.stores?.length === 0
    );
    if (storeExists) {
      toast.error('Please select a store for each product');
      return;
    }
    const productsToCreate = [];
    const prodIds = [];

    for (let i = 0; i < selectedProducts.length; i++) {
      const prod = selectedProducts[i];
      if (!prod?._id) {
        productsToCreate.push({
          name: prod?.name,
          stores: prod?.stores,
          uom: prod?.uom,
          category: 'Inhouse Finished Goods',
        });
      } else {
        prodIds.push(prod?._id);
      }
    }

    if (productsToCreate?.length) {
      const { products } = await addManyProducts({
        data: productsToCreate,
      }).unwrap();
      prodIds.push(...(products?.insertedIds || []));
    }

    if (onSubmit) onSubmit(prodIds);
  };

  return (
    <Modal
      title={'Product Master Selector'}
      onSubmit={handleSubmit}
      btnIsLoading={isLoading}
      modalWidth="80%"
      onCloseModal={onCloseModal}
    >
      {() => (
        <>
          <ProductMasterSelector onSelect={handleAddProduct} />
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Th>#</Table.Th>
                <Table.Th>Name</Table.Th>
                <Table.Th>
                  Stores
                  <span className="text-xs text-red-500">*</span>
                </Table.Th>
                <Table.Th>Uom</Table.Th>
                <Table.Th>In Stock</Table.Th>
                <Table.Th>Finished Goods</Table.Th>
                <Table.Th>Semi Finished Goods</Table.Th>
                <Table.Th></Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {selectedProducts?.map((pro, pIdx) => (
                <Table.Row key={pro?.key}>
                  <Table.Td>{pIdx + 1}</Table.Td>
                  <Table.Td>{pro?.name}</Table.Td>
                  <Table.Td>
                    <MultiSelect
                      disabled={pro?._id}
                      value={pro?.stores}
                      onChange={(e) => {
                        setSelectedProducts((prev) =>
                          prev?.map((it) => {
                            if (pro?.key === it?.key)
                              return {
                                ...it,
                                stores: e?.target?.value?.map((i) => i?.value),
                              };
                            return it;
                          })
                        );
                        setSearchTerm('');
                      }}
                      options={stores?.map((s) => ({
                        label: s?.name,
                        value: s?._id,
                      }))}
                      onSearch={(e) => setSearchTerm(e)}
                    />
                  </Table.Td>
                  <Table.Td>
                    <Select
                      menuPosition="fixed"
                      menuPlacement="auto"
                      disabled={pro?._id}
                      value={pro?.uom || ''}
                      options={uoms?.map((uom) => ({
                        label: uom,
                        value: uom,
                      }))}
                      onChange={(e) =>
                        setSelectedProducts((prev) =>
                          prev?.map((it) => {
                            if (pro?.key === it?.key)
                              return {
                                ...it,
                                uom: e?.target?.value,
                              };
                            return it;
                          })
                        )
                      }
                    />
                  </Table.Td>
                  <Table.Td>{pro?.inStock}</Table.Td>
                  <Table.Td>{pro?.fg}</Table.Td>
                  <Table.Td>{pro?.sfg}</Table.Td>
                  <Table.Td>
                    <IoCloseSharp
                      onClick={() =>
                        setSelectedProducts((prev) =>
                          prev?.filter((it) => it?.key !== pro?.key)
                        )
                      }
                      className="text-red-500 text-lg rounded-md hover:text-white hover:bg-red-500 cursor-pointer"
                    />
                  </Table.Td>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </>
      )}
    </Modal>
  );
}
