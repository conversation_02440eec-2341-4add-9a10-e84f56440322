import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import TruncateString from '../../global/TruncateString';

const JobDetailsView = ({ data }) => {
  const [keys, setKeys] = useState([]);

  useEffect(() => {
    setKeys(
      data?.map((el) =>
        el?.data?.map((sheet) =>
          Object.keys(
            sheet.hasOwnProperty('inputData') ? sheet.inputData : {} // eslint-disable-line
          )
        )
      )
    );
  }, [data]);

  return (
    <div className=" py-3">
      {data &&
        data.map((tab, tabIdx) => (
          <div
            className="w-full text-black bg-black/10 p-2 rounded-new mb-4 last:mb-0"
            key={tab.name + tabIdx.toString()}
          >
            <h3 className="text-center capitalize mb-2 font-extrabold">
              {tab.name}
            </h3>
            {tab.data.map((sheet, sheetIdx) => (
              <div
                className="p-2 rounded-new bg-black/10 mb-3 last:mb-0"
                key={sheet.sheetData.name + sheetIdx.toString()}
              >
                <h4 className="mb-2 font-bold">{sheet.sheetData.name}</h4>
                <div className="grid grid-cols-4 gap-4 w-full text-base">
                  {keys[tabIdx] &&
                    keys[tabIdx][sheetIdx] &&
                    keys[tabIdx][sheetIdx].map((key, keyIdx) => {
                      const paramData = sheet.sheetData.data.find(
                        (item) => item.Parameter === key
                      );

                      const horizontalFields = [
                        ...Array(+paramData['Horizontal Repetition']),
                      ];
                      const verticalFields = [
                        ...Array(+paramData['Vertical Repetition']),
                      ];
                      const title = paramData.Title.split('; ');

                      return (
                        <div
                          className={`col-span-${paramData.Span} p-2 rounded-new bg-black/10 h-fit`}
                          key={key + keyIdx.toString()}
                        >
                          <p>{key}</p>
                          <div
                            className={`flex flex-row ${
                              horizontalFields.length > 5
                                ? 'overflow-x-scroll'
                                : 'ml-4'
                            }`}
                          >
                            {horizontalFields.map((h, hIdx) => {
                              return (
                                <div
                                  className={
                                    paramData.Type !== 'CHECKBOX'
                                      ? 'min-w-[120px]'
                                      : ''
                                  }
                                  key={hIdx}
                                >
                                  {horizontalFields.length > 1 && (
                                    <h6 className="text-center text-base">
                                      <TruncateString length={10}>
                                        {title[hIdx]}
                                      </TruncateString>
                                    </h6>
                                  )}
                                  {verticalFields.map((v, vIdx) => {
                                    if (
                                      sheet?.inputData?.[key]?.rows &&
                                      sheet?.inputData?.[key]?.rows < vIdx + 1
                                    ) {
                                      return null;
                                    }

                                    return (
                                      <div
                                        className={
                                          horizontalFields.length > 1
                                            ? 'text-center'
                                            : ''
                                        }
                                        key={vIdx}
                                      >
                                        <p>
                                          <TruncateString length={10}>
                                            {paramData.Type === 'CHECKBOX' ? (
                                              sheet.inputData[
                                                key
                                              ].value.includes(title[hIdx]) ? (
                                                <CheckIcon className="w-6 h-6 mx-auto mt-2" />
                                              ) : (
                                                <XMarkIcon className="w-6 h-6 mx-auto mt-2" />
                                              )
                                            ) : horizontalFields.length > 1 &&
                                              verticalFields.length > 1 ? (
                                              sheet.inputData[key].value[
                                                title[hIdx] + vIdx.toString()
                                              ] ? (
                                                sheet.inputData[key].value[
                                                  title[hIdx] + vIdx.toString()
                                                ]
                                              ) : (
                                                'N/A'
                                              )
                                            ) : horizontalFields.length > 1 ? (
                                              sheet.inputData[key].value[
                                                title[hIdx]
                                              ] ? (
                                                sheet.inputData[key].value[
                                                  title[hIdx]
                                                ]
                                              ) : (
                                                'N/A'
                                              )
                                            ) : verticalFields.length > 1 ? (
                                              sheet.inputData[key].value[
                                                vIdx
                                              ] ? (
                                                sheet.inputData[key].value[vIdx]
                                              ) : (
                                                'N/A'
                                              )
                                            ) : sheet.inputData[key].value ? (
                                              sheet.inputData[key].value
                                            ) : (
                                              'N/A'
                                            )}
                                          </TruncateString>
                                        </p>
                                      </div>
                                    );
                                  })}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            ))}
          </div>
        ))}
    </div>
  );
};

export default JobDetailsView;
