import Table from '../global/components/Table';
const BomDetail = ({ partsData, item }) => {
  return (
    <div>
      <section className="w-full p-10">
        {item?.type === 'part' ? (
          <section className="mt-2">
            <div className="flex items-center justify-between gap-3">
              <p className="font-semibold">Part ID:</p>
              <p> {item?.value?.id}</p>
            </div>
            <div className="flex items-center justify-between gap-3">
              <p className="font-semibold">Part Name:</p>
              <p> {item?.value?.name}</p>
            </div>
          </section>
        ) : (
          <section className="mt-2">
            <div className="flex items-center justify-between gap-3">
              <p className="font-semibold">Part Name:</p>
              <p> {item?.value}</p>
            </div>
            <div className="flex items-center justify-between gap-3">
              <p className="font-semibold">Type :</p>
              <p> {item?.type}</p>
            </div>
          </section>
        )}
        <section className="mt-6">
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Th>#</Table.Th>
                <Table.Th>RM</Table.Th>
                <Table.Th>Unit</Table.Th>
                <Table.Th>Uom</Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {partsData?.map((item, index) => {
                return (
                  <Table.Row key={index}>
                    <Table.Td>{index + 1}</Table.Td>
                    <Table.Td>{item?.part?.name}</Table.Td>
                    <Table.Td>{item?.units}</Table.Td>
                    <Table.Td>{item?.part?.uom}</Table.Td>
                  </Table.Row>
                );
              })}
            </Table.Body>
          </Table>
        </section>
      </section>
    </div>
  );
};

export default BomDetail;
