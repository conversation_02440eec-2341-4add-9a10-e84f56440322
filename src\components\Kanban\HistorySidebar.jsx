import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import React, { useContext, useEffect, useLayoutEffect, useState } from 'react';
import { CiEdit } from 'react-icons/ci';
import { useGetOrderByIdMutation } from '../../slices/kanbanApiSlice';
import { Store } from '../../store/Store';
import RightSidebar from '../global/components/RightSidebar';
import Spinner from '../global/components/Spinner';
import CreateLead from '../leads/CreateLead';
import MediaModal from '../v3/global/components/MediaModal';
import useKanbanSidebar from './useKanbanSidebar';

// SIDEBAR STATE MUST HAVE A "OPEN" FIELD AND A "STEPS" FIELD

const HistorySidebar = ({ sidebar, setSidebar }) => {
  const { state } = useContext(Store);
  const renderTableBasedOnRefKey = useKanbanSidebar();
  const [currentStepIndex, setCurrentStepIndex] = useState(
    sidebar?.columnNumber || 0
  );
  const [readMore, setReadMore] = useState(false);
  const [getOrder, { isLoading: isOrderLoading }] = useGetOrderByIdMutation();
  const [selectedOrder, setSelectedOrder] = useState({});
  const [currentStep, setCurrentStep] = useState({});
  const [refetch, setRefetch] = useState(false);

  const [openEditModal, setEditOpenModal] = useState(false);
  const [modalEditData, setModalEditData] = useState(null);

  // Handle Left Right Navigation Code
  const steps = sidebar?.steps || [];
  const totalSteps = steps.length;
  // const currentStep = steps[currentStepIndex];

  const handleNext = () => {
    setCurrentStepIndex((prevIndex) => Math.min(prevIndex + 1, totalSteps - 1));
  };
  const handlePrev = () => {
    setCurrentStepIndex((prevIndex) => Math.max(prevIndex - 1, 0));
  };
  const delayReason = sidebar?.delayReasons?.filter(
    (el) => el?.refKey === currentStep?.stepPage
  );
  useLayoutEffect(() => {
    if (readMore) {
      setSidebar((prevState) => ({
        ...prevState,
        open: false,
      }));
    }
  }, [readMore, setSidebar]);

  useEffect(() => {
    if (sidebar?.columnPage) {
      let index = sidebar?.steps?.length - 1;
      for (let i in sidebar?.steps) {
        if (sidebar?.steps?.[i]?.stepPage === sidebar?.columnPage) {
          index = i;
          break;
        }
      }
      setCurrentStepIndex(index);
    }
  }, [sidebar]);

  useEffect(() => {
    const getCurrentOrder = async () => {
      const order = await getOrder({ id: sidebar?.orderId });
      setSelectedOrder(order?.data?.[0]);
    };
    if (sidebar?.orderId?.length > 0) {
      getCurrentOrder();
    }
  }, [sidebar, getOrder, refetch]);

  useEffect(() => {
    if (selectedOrder?._id)
      setCurrentStep(selectedOrder?.steps?.[currentStepIndex]);
  }, [selectedOrder, setCurrentStep, currentStepIndex]);

  return (
    <>
      {readMore && (
        <MediaModal
          FormData={
            currentStep?.refKey === 'SalesInquiryDashboard'
              ? currentStep?.data?.files
              : currentStep?.refKey === 'Quotation'
                ? currentStep?.data?.attachments
                : currentStep?.refKey === 'SalesOrder'
                  ? currentStep?.data?.files
                  : ''
          }
          isView={true}
          setShowModal={setReadMore}
          ShowModal={readMore}
        />
      )}

      {openEditModal && (
        <CreateLead
          setIsOpenCreate={setEditOpenModal}
          isOpenCreate={openEditModal}
          leadData={{ ...modalEditData, stage: 'Leads' }}
          setLeadData={setModalEditData}
          isEdit={true}
        />
      )}

      <RightSidebar
        className={'w-full md:w-1/2'}
        scale={736}
        openSideBar={sidebar.open}
        setOpenSideBar={() => {
          setSidebar({
            open: false,
            steps: [],
          });
        }}
      >
        <div>
          {currentStepIndex > 0 && (
            <button onClick={handlePrev}>
              <ChevronLeftIcon className="w-6 h-6" />
            </button>
          )}
          {currentStepIndex < totalSteps - 1 && (
            <button onClick={handleNext}>
              <ChevronRightIcon className="w-6 h-6" />
            </button>
          )}
        </div>
        {isOrderLoading ? (
          <Spinner />
        ) : (
          <React.Fragment key={currentStepIndex}>
            <div className="flex justify-between w-full items-center">
              {/* <h3 className="-mt-2">
                {currentStep?.data?.taskId?.customTaskId
                  ? `${currentStep?.data?.taskId?.customTaskId} - ${currentStep?.stepPage}`
                  : `${currentStep?.data?.taskId?.taskId}- ${currentStep?.stepPage}`}
              </h3> */}
              <h3 className="-mt-2">
                {selectedOrder?.customTaskId
                  ? `${selectedOrder?.customTaskId} - ${currentStep?.stepPage}`
                  : `${selectedOrder?.taskId}- ${currentStep?.stepPage}`}
              </h3>
              {/* For now the Edit Functionality Only for the Leads */}
              {currentStep?.refKey === 'Leads' && (
                <CiEdit
                  className="cursor-pointer"
                  size={25}
                  onClick={() => {
                    setModalEditData(currentStep?.data);
                    setEditOpenModal(true);
                    setSidebar({
                      open: false,
                      steps: [],
                    });
                  }}
                />
              )}
            </div>

            <div className="mb-6 !w-full">
              {state?.user?.kanbanInfo?.includes(currentStep?.stepPage) ||
              state?.user?.role === 'superuser' ||
              state?.user?.role === 'admin' ||
              state?.user?.role === 'Admin' ? (
                <div>
                  {currentStep?.refKey &&
                    currentStep?.data &&
                    renderTableBasedOnRefKey(
                      currentStep.refKey,
                      currentStep.data,
                      delayReason,
                      setReadMore,
                      '7%',
                      refetch,
                      setRefetch,
                      selectedOrder?.productObject
                    )}
                </div>
              ) : (
                <div>
                  <div className="w-full h-[200px] flex items-center justify-center bg-slate-100 rounded-lg">
                    <p className="text-slate-500 text-md font-medium">
                      Access Denied
                    </p>
                  </div>
                </div>
              )}
            </div>
          </React.Fragment>
        )}
      </RightSidebar>
    </>
  );
};

export default HistorySidebar;
