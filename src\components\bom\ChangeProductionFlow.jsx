import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  useCreateJobTemplateMutation,
  useLazyGetTemplateByIdQuery,
} from '../../slices/templateApiSlice';
import { useLazyGetAllMqttsQuery } from '../../slices/mqttslice';
import CreateFlow from '../ProductionFlow/CreateFlow';
import { INITIAL_MODAL_FOR } from './bomConstants';

const initialFlowData = {
  name: '',
  processes: [],
};

const initialInputData = {
  mqtt: '',
  name: '',
  multiprocess: false,
  applicableDowntime: [],
  setupTime: '',
  bufferTime: '',
  cycleTime: '',
  countPerHour: '',
};

const ChangeProductionFlow = ({ modalFor, setModalfor, bom, setBoms }) => {
  const { rowId, open } = modalFor;
  const [inputData, setInputData] = useState(initialInputData);
  const [flowData, setFlowData] = useState(initialFlowData);

  const [selectedTemplateData, setSelectedTemplateData] = useState(null);
  const [getTemplateById] = useLazyGetTemplateByIdQuery();

  const [getAllMqtts, { data: mqtts = [] }] = useLazyGetAllMqttsQuery();
  const [postCreateTemplate] = useCreateJobTemplateMutation();

  useEffect(() => {
    getAllMqtts({}, false);
  }, [getAllMqtts]);

  useEffect(() => {
    if (bom?.template) {
      getTemplateById({ id: bom?.template }).then((res) => {
        let data = {
          name: res?.data?.flow?.name + '-Copy',
          processes: res?.data?.flow?.processes,
        };
        setFlowData(data);
        setSelectedTemplateData(res?.data);
      });
    }
  }, [bom?.template, getTemplateById]);

  const handleCreateTemplateForNewFlow = async (pfId) => {
    if (pfId) {
      let data = { ...selectedTemplateData };

      delete data._id;
      delete data.__v;
      delete data?.createdAt;
      delete data?.updatedAt;

      let newTemplateData = {
        ...data,
        name: selectedTemplateData?.name + '-Copy',
        flow: pfId,
        inputScreen: selectedTemplateData?.inputScreen?.id,
      };

      const res = await postCreateTemplate({ data: newTemplateData });

      if (res?.data) {
        toast.success('Template created successfully!');
        setBoms((prev) =>
          prev?.map((b) => {
            if (b.rowId === rowId) {
              return {
                ...b,
                isUpdated: true,
                template: res?.data?._id,
              };
            }
            return b;
          })
        );
        setModalfor(INITIAL_MODAL_FOR);
      }
    }
  };

  return (
    <>
      <CreateFlow
        isTemplate={true}
        handleCreateTemplateForNewFlow={handleCreateTemplateForNewFlow}
        mqtts={mqtts}
        modalOpen={open}
        setModalOpen={() => setModalfor(INITIAL_MODAL_FOR)}
        editOption={false}
        // setEditOption={setEditOption}
        // editHandler={editHandler}
        // editId={editId}
        // setEditId={setEditId}
        initialFlowData={initialFlowData}
        initialInputData={initialInputData}
        flowData={flowData}
        inputData={inputData}
        setFlowData={setFlowData}
        setInputData={setInputData}
        // productionFlows={productionFlows}
        // isLoadingEditPf={isLoadingEditPf}
      />
    </>
  );
};

export default ChangeProductionFlow;
