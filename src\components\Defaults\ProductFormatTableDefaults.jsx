import { InfoTooltip } from '../global/components/InfoTooltip';

const ProductFormatTableDefaults = ({ defaults, setDefaults }) => {
  return (
    <div className="py-2 border-b-2 border-gray-300/70">
      <h3 className="text-gray-subHeading mb-4">Show Product Format Table :</h3>
      <div className="flex gap-x-4 items-center">
        <input
          type="checkbox"
          checked={defaults?.projectDefaults?.showProductFormatTable || false}
          onChange={(e) => {
            setDefaults((prev) => ({
              ...prev,
              projectDefaults: {
                ...prev.projectDefaults,
                showProductFormatTable: e.target.checked,
              },
            }));
          }}
        />
        <div className="flex gap-x-2 items-center">
          <label className=" text-sm font-medium text-gray-700">
            Show Product Format Table
          </label>
          <InfoTooltip
            position="top"
            width="200px"
            id="downtimeType"
            isHtml={true}
            content="This Checkbox used to show the Product Format Table"
          />
        </div>
      </div>
    </div>
  );
};

export default ProductFormatTableDefaults;
