import {
  FileExcelOutlined,
  UploadOutlined,
  DownloadOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
} from '@ant-design/icons';
import {
  Button,
  Modal,
  Steps,
  Table,
  Upload,
  Alert,
  Progress,
  Typography,
  Space,
  Result,
  Card,
  Badge,
  Tag,
} from 'antd';
import * as XLSX from 'xlsx';
import { useState, useMemo } from 'react';
import { toast } from 'react-toastify';
import ImportInstructions from './ImportInstructions';

const { Text, Title } = Typography;

const generateGuidelinesCard = (fields) => {
  const separatorMap = {
    ',': [],
    '|': [],
  };

  const limits = [];

  fields.forEach((field) => {
    const { label, max, separator } = field;

    if (separator && separatorMap[separator]) {
      separatorMap[separator].push(label.split('(')[0].trim()); // extract 'Email' from 'Email (max 5)'
    }

    if (max) {
      limits.push(`Maximum ${max} ${label.toLowerCase()}`);
    }
  });

  return (
    <Card className="px-4 py-2">
      <Title level={5}>Data Format Guidelines</Title>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Text strong>Separators:</Text>
          <ul className="mt-2 space-y-1 text-sm">
            {Object.entries(separatorMap).map(([sep, labels]) =>
              labels.length > 0 ? (
                <li key={sep}>
                  •{' '}
                  <Badge color={sep === ',' ? 'blue' : 'green'}>
                    {sep === ',' ? 'Comma (,)' : 'Pipe (|)'}
                  </Badge>{' '}
                  for {labels.join(', ')}
                </li>
              ) : null
            )}
          </ul>
        </div>
        <div>
          <Text strong>Limits:</Text>
          <ul className="mt-2 space-y-1 text-sm">
            {limits.map((limit, i) => (
              <li key={i}>• {limit}</li>
            ))}
          </ul>
        </div>
      </div>
    </Card>
  );
};

const ExcelImportModal = ({
  open,
  setOpen,
  title = 'Import Data',
  templateFields = [],
  onImport,
  type = 'default',
  customColumns = [],
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [fileName, setFileName] = useState('');
  const [previewData, setPreviewData] = useState([]);
  const [validationErrors, setValidationErrors] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [importStats, setImportStats] = useState({
    total: 0,
    valid: 0,
    invalid: 0,
  });
  const [finalImportedLength, setFinalImportedLength] = useState(0);

  // Combine template fields and custom columns
  const allFields = useMemo(() => {
    const customFields = customColumns.map((col) => ({
      ...col,
      isCustom: true,
      type: col.type || 'text',
      required: col.required || false,
    }));

    return [...templateFields, ...customFields];
  }, [templateFields, customColumns]);

  const handleExcelImport = async (file) => {
    setImporting(true);
    setImportProgress(0);
    setFileName(file.name);
    setValidationErrors([]);

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        setImportProgress(25);
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        if (jsonData.length === 0) throw new Error('Excel file is empty.');

        setImportProgress(50);

        const formattedData = jsonData.map((row, index) => {
          const obj = { rowIndex: index + 1 };

          allFields.forEach((field) => {
            const raw = row[field.label] || '';
            if (field.separator) {
              obj[field.key] = String(raw)
                .split(field.separator)
                .map((x) => x.trim())
                .filter(Boolean);
            } else {
              obj[field.key] = String(raw).trim() || '';
            }
          });

          // TO Keep custom columns separate
          const rowCustomColumns = customColumns.map((col) => ({
            field: col.key,
            value: obj[col.key],
          }));

          obj.customColumns = rowCustomColumns;

          return obj;
        });

        setImportProgress(75);

        const allErrors = [];
        const validData = [];

        formattedData.forEach((row, index) => {
          const errs = validateRow(row, index);
          if (errs.length > 0) allErrors.push(...errs);
          else validData.push(row);
        });

        setValidationErrors(allErrors);
        setImportStats({
          total: formattedData.length,
          valid: validData.length,
          invalid: formattedData.length - validData.length,
        });
        setPreviewData(validData);
        setImportProgress(100);
        setCurrentStep(1);

        if (allErrors.length > 0)
          toast.warning(`${allErrors.length} validation errors found.`);
        else toast.success('All data validated successfully.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setImporting(false);
      }
    };

    reader.readAsArrayBuffer(file);
    return false;
  };

  const validateRow = (row, index) => {
    const errors = [];

    allFields.forEach((field) => {
      const value = row[field.key];
      if (field.required && (!value || value.length === 0)) {
        errors.push(`Row ${index + 2}: ${field.label} is required`);
      }

      if (field.max && Array.isArray(value) && value.length > field.max) {
        errors.push(
          `Row ${index + 2}: Max ${field.max} ${field.label} allowed`
        );
      }

      if (field.type === 'email' && Array.isArray(value)) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        value.forEach((email) => {
          if (email && !emailRegex.test(email)) {
            errors.push(`Row ${index + 2}: Invalid email "${email}"`);
          }
        });
      }

      if (field.type === 'phone' && Array.isArray(value)) {
        const phoneRegex = /^[0-9]{10,15}$/;
        value.forEach((phone) => {
          if (phone && !phoneRegex.test(phone.replace(/\s+/g, ''))) {
            errors.push(`Row ${index + 2}: Invalid phone number "${phone}"`);
          }
        });
      }
    });

    return errors;
  };

  const handleConfirmImport = async () => {
    if (previewData.length === 0)
      return toast.warning('No valid data to import');
    setIsProcessing(true);
    try {
      const cleanedData = previewData.map((row) => {
        const { customColumns = [], ...rest } = row;

        const cleanedRow = { ...rest, customColumns: [] };

        customColumns.forEach(({ field, value }) => {
          delete cleanedRow[field];
          cleanedRow.customColumns.push({ field, value });
        });

        return cleanedRow;
      });

      const res = await onImport(cleanedData);
      if (!res?.error) {
        toast.success(res?.data?.message || 'Import successful');
        setFinalImportedLength(res?.data?.imported || 0);
        setCurrentStep(2);
      }
    } catch (err) {
      toast.error(err.message || 'Import failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadTemplate = () => {
    const sample = [{}];

    // Include both template fields and custom columns in template
    allFields.forEach((field) => {
      if (field.type === 'email') {
        sample[0][field.label] = '<EMAIL>,<EMAIL>';
      } else if (field.type === 'phone') {
        sample[0][field.label] = '1234567890,1234567891';
      } else {
        sample[0][field.label] = field.isCustom
          ? 'Custom Value'
          : 'Sample Value';
      }
    });

    const ws = XLSX.utils.json_to_sheet(sample);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Template');
    XLSX.writeFile(wb, `${type?.toUpperCase()}_template.xlsx`);
    toast.success('Template downloaded');
  };

  const resetModal = () => {
    setCurrentStep(0);
    setPreviewData([]);
    setValidationErrors([]);
    setImportStats({ total: 0, valid: 0, invalid: 0 });
    setImportProgress(0);
    setFileName('');
  };

  // Create table columns for preview
  const tableColumns = useMemo(() => {
    return allFields.map((field) => ({
      title: (
        <Space className="whitespace-nowrap">
          {field.label}
          {field.isCustom && (
            <Tag color="purple" size="small">
              Custom
            </Tag>
          )}
        </Space>
      ),
      dataIndex: field.key,
      key: field.key,
      render: (val) => {
        if (!val) return '-';
        return Array.isArray(val) ? val.join(', ') : val;
      },
      width: field.isCustom ? 150 : undefined,
    }));
  }, [allFields]);

  return (
    <Modal
      open={open}
      onCancel={() => {
        setOpen(false);
        resetModal();
      }}
      footer={null}
      title={
        <Space>
          <FileExcelOutlined /> {title}
        </Space>
      }
      width={1200}
      centered
      styles={{
        body: {
          maxHeight: `calc(100vh - 150px)`,
          overflowY: 'auto',
        },
      }}
      maskClosable={false}
    >
      <Steps
        current={currentStep}
        items={[
          { title: 'Upload' },
          { title: 'Preview' },
          { title: 'Complete' },
        ]}
        className="my-6 px-4"
      />

      {currentStep === 0 && (
        <div className="space-y-6">
          {/* Instructions Card */}
          <ImportInstructions />

          {/* Column Information */}
          {customColumns.length > 0 && (
            <Card className="px-4 py-2">
              <Title level={5}>
                Available Columns
                <Badge count={allFields.length} style={{ marginLeft: 8 }} />
              </Title>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Text strong>Standard Fields:</Text>
                  <div className="mt-2 flex flex-wrap gap-1">
                    {templateFields.map((field) => (
                      <Tag key={field.key} color="blue">
                        {field.label}
                      </Tag>
                    ))}
                  </div>
                </div>
                <div>
                  <Text strong>Custom Fields:</Text>
                  <div className="mt-2 flex flex-wrap gap-1">
                    {customColumns.map((field) => (
                      <Tag key={field.key} color="purple">
                        {field.label}
                      </Tag>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Format Guidelines */}
          {generateGuidelinesCard(templateFields)}

          {/* Action Buttons */}
          <div className="flex gap-4 justify-self-end">
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadTemplate}
              loading={importing}
            >
              Download Template
            </Button>
            <Upload
              beforeUpload={handleExcelImport}
              accept=".xlsx,.xls"
              showUploadList={false}
            >
              <Button
                icon={<UploadOutlined />}
                type="primary"
                loading={importing}
              >
                {importing ? 'Processing...' : 'Upload Excel File'}
              </Button>
            </Upload>
          </div>
          {importing && (
            <div className="mt-4">
              <Progress percent={importProgress} status="active" />
              <Text className="text-sm text-gray-500 mt-2">
                Processing {fileName}...
              </Text>
            </div>
          )}
        </div>
      )}

      {currentStep === 1 && (
        <div className="space-y-4">
          {/* Import Statistics */}
          <Card>
            <Title level={5}>Import Summary</Title>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {importStats.valid}
                </div>
                <Text className="text-green-600">Valid Records</Text>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {importStats.invalid}
                </div>
                <Text className="text-red-600">Invalid Records</Text>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {importStats.total}
                </div>
                <Text className="text-blue-600">Total Records</Text>
              </div>
            </div>
          </Card>

          {validationErrors.length > 0 && (
            <Alert
              type="warning"
              message={`${validationErrors.length} Validation Errors Found`}
              description={
                <ul className="max-h-40 overflow-y-auto">
                  {validationErrors.slice(0, 10).map((err, i) => (
                    <li key={i}>{err}</li>
                  ))}
                  {validationErrors.length > 10 && (
                    <li>...and {validationErrors.length - 10} more</li>
                  )}
                </ul>
              }
            />
          )}

          <Table
            columns={tableColumns}
            dataSource={previewData}
            rowKey="rowIndex"
            size="small"
            scroll={{ x: '100%' }}
            pagination={{ pageSize: 10 }}
            bordered
          />

          <div className="flex justify-between">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => setCurrentStep(0)}
            >
              Back
            </Button>
            <Button
              icon={<ArrowRightOutlined />}
              type="primary"
              loading={isProcessing}
              onClick={handleConfirmImport}
            >
              Import {previewData.length} Records
            </Button>
          </div>
        </div>
      )}

      {currentStep === 2 && (
        <Result
          status="success"
          title="Import Successful!"
          subTitle={
            finalImportedLength > 0
              ? `Successfully imported ${finalImportedLength} records with ${customColumns.length} custom fields.`
              : 'No records were imported.'
          }
          extra={
            <Button
              type="primary"
              onClick={() => {
                setOpen(false);
                resetModal();
              }}
            >
              Close
            </Button>
          }
        />
      )}
    </Modal>
  );
};

export default ExcelImportModal;
