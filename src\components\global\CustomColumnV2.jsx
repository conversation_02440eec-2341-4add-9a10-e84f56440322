import {
  Button,
  Input,
  Modal,
  Select,
  Space,
  Table,
  Card,
  Typography,
  Tag,
} from 'antd';
import {
  DeleteOutlined,
  PlusCircleTwoTone,
  SaveOutlined,
  SettingOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { toast } from 'react-toastify';
import {
  useAddCustomColumnMutation,
  useDeleteCustomColumnMutation,
  useEditCustomColumnMutation,
  useGetCustomColumnsQuery,
} from '../../slices/customCoulmn.ApiSlice';
import { useState, useEffect } from 'react';

const { Title } = Typography;

// Dropdown options modal component
const DropDownOptionsModal = ({
  open,
  onCancel,
  onSave,
  existingOptions = [],
}) => {
  const [options, setOptions] = useState(existingOptions);

  useEffect(() => {
    setOptions(existingOptions);
  }, [existingOptions]);

  const handleAddOption = () => {
    setOptions([...options, '']);
  };

  const handleOptionChange = (value, index) => {
    const updatedOptions = [...options];
    updatedOptions[index] = value;
    setOptions(updatedOptions);
  };

  const handleDeleteOption = (index) => {
    setOptions(options.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    const filteredOptions = options?.map((opt) => opt.trim());
    const duplicateOptions = filteredOptions.filter(
      (opt, index) => filteredOptions.indexOf(opt) !== index
    );
    if (duplicateOptions.length > 0) {
      toast.error('Duplicate options are not allowed');
      return;
    }
    const emptyOptions = filteredOptions.filter((opt) => opt.trim() === '');
    if (emptyOptions.length > 0) {
      toast.error('Empty options are not allowed');
      return;
    }
    onSave(filteredOptions);
  };

  return (
    <Modal
      title="Dropdown Options"
      open={open}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          Save Options
        </Button>,
      ]}
    >
      <div className="space-y-4">
        <div className="text-gray-600 text-sm mb-2">
          Define the options that will appear in this dropdown field
        </div>

        {options.map((option, idx) => (
          <div key={idx} className="flex items-center gap-2 mb-2">
            <Input
              value={option}
              onChange={(e) => handleOptionChange(e.target.value, idx)}
              placeholder="Enter option value"
              className="flex-1"
              style={{ marginRight: '8px' }}
            />
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteOption(idx)}
            />
          </div>
        ))}

        <Button
          type="dashed"
          onClick={handleAddOption}
          block
          icon={<PlusOutlined />}
          style={{ marginTop: '8px' }}
        >
          Add Option
        </Button>
      </div>
    </Modal>
  );
};

const CustomColumnV2 = ({ pageName, openModal, setOpenModal }) => {
  const [addCustomColumns] = useAddCustomColumnMutation();
  const [deleteCustomCol] = useDeleteCustomColumnMutation();
  const [editCustomColumns] = useEditCustomColumnMutation();

  const { data: allCustomCols } = useGetCustomColumnsQuery();

  const [columnInputs, setColumnInputs] = useState([]);
  const [activeDropdownIndex, setActiveDropdownIndex] = useState(null);

  useEffect(() => {
    const filteredColumns = allCustomCols?.filter(
      (col) => col?.pageName === pageName
    );
    setColumnInputs(filteredColumns || []);
  }, [allCustomCols, pageName]);

  const handleSubmit = async () => {
    if (!columnInputs.length) {
      toast.error('Please add at least one column before submitting');
      return;
    }

    const allNotFilled = columnInputs?.some((col) => !col?.columnName?.trim());
    if (allNotFilled) {
      toast.error('Please fill all the fields');
      return;
    }

    // Check that not any two or more column have same name
    const columnNames = columnInputs?.map((col) => col?.columnName);
    const uniqueNames = new Set(columnNames);
    if (uniqueNames.size !== columnNames.length) {
      toast.error('Column names must be unique');
      return;
    }

    // Validate dropdown columns have options
    const dropdownsWithoutOptions = columnInputs.filter(
      (col) =>
        col.columnType === 'dropdown' &&
        (!col.dropDownOptions || col.dropDownOptions.length === 0)
    );

    if (dropdownsWithoutOptions.length > 0) {
      toast.error('Dropdown columns must have at least one option');
      return;
    }

    if (columnInputs.length !== 0) {
      await editCustomColumns(columnInputs);
    }

    const res = await addCustomColumns({
      columnInputs,
      pageName,
    });

    if (!res?.error) {
      toast.success('Column Added Successfully');
      setOpenModal(false);
    } else {
      toast.error(res.error?.data?.message || 'Failed to add columns');
    }
  };

  const changeHandler = (value, name, index) => {
    const tempInputs = [...columnInputs];

    if (name === 'columnName') {
      if (value.match(/[!@#$%^&*(),.?":{}|<>\-_=+]/)) {
        toast.error('Special characters are not allowed');
        return;
      }
    }

    let updatedColumn = { ...tempInputs[index], [name]: value };
    // Clear incompatible fields when column type changes
    if (name === 'columnType') {
      if (value !== 'dropdown') {
        delete updatedColumn.dropDownOptions;
      }
    }

    tempInputs[index] = updatedColumn;
    setColumnInputs(tempInputs);
  };

  const onAdd = () => {
    const emptyFields = columnInputs?.some((col) => col?.columnName === '');
    if (emptyFields) {
      toast.error('Please complete all previous fields first');
      return;
    }
    const tempInputs = [...columnInputs];
    tempInputs.push({ columnName: '', columnType: 'string' });
    setColumnInputs(tempInputs);
  };

  const removeColumn = async (id, index) => {
    if (!id) {
      const filteredInputs = columnInputs?.filter((_, ind) => ind !== index);
      setColumnInputs(filteredInputs);
      return;
    }

    try {
      const deletedCol = await deleteCustomCol({ id });
      if (deletedCol?.data) {
        toast.success('Column Deleted');
        // Update local state
        const filteredInputs = columnInputs?.filter((_, ind) => ind !== index);
        setColumnInputs(filteredInputs);
      }
    } catch (error) {
      toast.error('Failed to delete column');
    }
  };

  const saveDropdownOptions = (options, idx) => {
    const tempInputs = [...columnInputs];
    tempInputs[idx] = { ...tempInputs[idx], dropDownOptions: options };
    setColumnInputs(tempInputs);
    setActiveDropdownIndex(null);
  };

  // Table columns definition
  const tableColumns = [
    {
      title: 'Column Name',
      dataIndex: 'columnName',
      key: 'columnName',
      width: '30%',
      render: (text, record, index) => (
        <Input
          placeholder="Enter Column Name"
          value={text}
          onChange={(e) => {
            const value = e.target.value;
            if (value.match(/[!@#$%^&*(),.?":{}|<>\-_=+]/)) {
              toast.error('Special characters are not allowed');
              return;
            }
            changeHandler(e.target.value, 'columnName', index);
          }}
        />
      ),
    },
    {
      title: 'Type',
      dataIndex: 'columnType',
      key: 'columnType',
      width: '25%',
      render: (text, record, index) => (
        <Select
          style={{ width: '100%' }}
          options={[
            {
              label: 'Text',
              value: 'string',
            },
            {
              label: 'Number',
              value: 'number',
            },
            {
              label: 'Dropdown',
              value: 'dropdown',
            },
          ]}
          value={text}
          onChange={(e) => changeHandler(e, 'columnType', index)}
          placeholder="Select type"
        />
      ),
    },
    {
      title: 'Configuration',
      dataIndex: 'configuration',
      key: 'configuration',
      width: '35%',
      render: (_, record, index) => {
        if (record.columnType === 'dropdown') {
          return (
            <Button
              icon={<SettingOutlined />}
              onClick={() => setActiveDropdownIndex(index)}
            >
              Options{' '}
              {record?.dropDownOptions?.length > 0
                ? `(${record.dropDownOptions.length})`
                : ''}
            </Button>
          );
        } else {
          return <Tag color="default">No Configuration</Tag>;
        }
      },
    },
    {
      title: 'Action',
      key: 'action',
      width: '10%',
      render: (_, record, index) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeColumn(record?._id, index)}
        />
      ),
    },
  ];

  return (
    <div>
      <Modal
        title={<Title level={4}>Custom Column</Title>}
        open={openModal}
        onCancel={() => {
          setColumnInputs([]);
          setOpenModal(false);
        }}
        footer={null}
        width={800}
      >
        <Card style={{ marginBottom: '16px' }}>
          <div className="text-gray-600 text-sm mb-4">
            Configure custom columns that will appear in your data table. You
            can add text, number, or dropdown columns.
          </div>

          <Table
            columns={tableColumns}
            dataSource={columnInputs}
            pagination={false}
            rowKey={(record, index) => index}
            locale={{
              emptyText:
                'No custom columns defined. Click "Add Column" to create one.',
            }}
          />

          <div style={{ marginTop: '16px' }}>
            <Space style={{ width: '100%', justifyContent: 'space-between' }}>
              <Button icon={<PlusCircleTwoTone />} onClick={onAdd}>
                Add Column
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSubmit}
              >
                Submit
              </Button>
            </Space>
          </div>
        </Card>
      </Modal>

      {/* Dropdown options modal */}
      <DropDownOptionsModal
        open={activeDropdownIndex !== null}
        onCancel={() => setActiveDropdownIndex(null)}
        onSave={(options) =>
          activeDropdownIndex !== null &&
          saveDropdownOptions(options, activeDropdownIndex)
        }
        existingOptions={
          activeDropdownIndex !== null
            ? columnInputs[activeDropdownIndex]?.dropDownOptions || []
            : []
        }
      />
    </div>
  );
};

export default CustomColumnV2;
