import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import Textarea from '../global/components/Textarea';

import { generateDateTimeString } from '../../helperFunction';
import { useScheduleReminderMutation } from '../../slices/orderApiSlice';

const TaskReminderModal = ({ reminderModal, setReminderModal, card }) => {
  const [scheduleReminder, { isLoading }] = useScheduleReminderMutation();

  const [reminderData, setReminderData] = useState({});

  useEffect(() => {
    setReminderData({
      reminder: card?.reminderDate
        ? generateDateTimeString(card?.reminderDate)
        : '',
      title: card?.reminderTitle || '',
      description: card?.reminderDescription || '',
    });
  }, [card?.reminderDate, card?.reminderTitle, card?.reminderDescription]);

  const handleChange = (e) => {
    setReminderData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const setReminder = async () => {
    const res = await scheduleReminder({
      data: {
        ...reminderData,
        taskId: card?.taskId,
        departmentalOrderId: card?._id,
      },
    }).unwrap();
    if (res) {
      toast.success('Reminder Scheduled');
      setReminderModal(false);
    }
  };

  return (
    <>
      {reminderModal && (
        <Modal
          title="Schedule Reminder"
          onSubmit={setReminder}
          btnIsLoading={isLoading}
          onCloseModal={() => {
            setReminderModal(false);
          }}
        >
          {() => {
            return (
              <div className="w-full">
                <div className="flex items-center gap-2 w-full">
                  <div className="w-full">
                    <label className="text-[13px] text-slate-400 font-semibold">
                      Reminder Title:
                    </label>
                    <Input
                      name="title"
                      placeholder="Enter reminder title"
                      className="mt-[2px]"
                      value={reminderData?.title}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="w-full">
                    <label className="text-[13px] text-slate-400 font-semibold">
                      Reminder Time:
                    </label>
                    <Input
                      name="reminder"
                      placeholder="Enter reminder title"
                      className="mt-[2px]"
                      type="datetime-local"
                      value={reminderData?.reminder}
                      onChange={handleChange}
                    />
                  </div>
                </div>
                <div className="w-full mt-4">
                  <label className="text-[13px] text-slate-400 font-semibold">
                    Description:
                  </label>
                  <Textarea
                    name="description"
                    placeholder="Enter description"
                    className="mt-[2px]"
                    value={reminderData?.description}
                    onChange={handleChange}
                  />
                </div>
              </div>
            );
          }}
        </Modal>
      )}
    </>
  );
};

export default TaskReminderModal;
