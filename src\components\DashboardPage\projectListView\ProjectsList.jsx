import { useState } from 'react';
import Select from '../../global/components/Select';
import ProjectTile from './ProjectTile';

const ProjectsList = ({
  createInputs,
  mqtts,
  projects,
  setSelectOptions,
  values,
}) => {
  const [selectFilters, setSelectFilters] = useState('active');

  const filters = [
    { name: 'All', value: '' },
    { name: 'Active', value: 'active' },
    { name: 'Paused', value: 'pause' },
    { name: 'Completed', value: 'complete' },
  ];

  return (
    <div className="w-full">
      <div className="flex w-full items-center justify-between mb-3">
        <p className="font-bold text-[1.25rem]">Status</p>
        <Select
          value={selectFilters}
          onChange={(e) => setSelectFilters(e.target.value)}
          options={filters}
          type="round"
          className={`w-1/2`}
        />
      </div>
      {createInputs?.map((createInput, ciIdx) => {
        const cuProjects = createInput?.cuProjects?.filter(
          (cuProject) => cuProject.project === createInput._id
        );
        const machinesAndOperators = [];

        const machinesList = [];

        const mqttCheck = [];

        cuProjects.forEach((cuProject) => {
          if (!mqttCheck.includes(cuProject?.mqtt)) {
            mqttCheck.push(cuProject.mqtt);
          }
          cuProject.machineAndOperator.forEach((mao) => {
            // gets machine and operator status acoording to selected filter
            if (mao.status.includes(selectFilters)) {
              machinesAndOperators.push(mao);
            }
            // gets list of all machines used in project
            if (mao?.machine?.machineId) {
              if (machinesList.includes(mao.machine.machineId)) return;
              machinesList.push(mao.machine.machineId);
            }
          });
        });

        // if no machine with status of selectFilter return null
        if (machinesAndOperators.length < 1) {
          return null;
        }

        return (
          <ProjectTile
            key={ciIdx}
            createInput={createInput}
            cuProjects={cuProjects}
            mqtts={mqtts}
            mqttCheck={mqttCheck}
            selectFilters={selectFilters}
            projects={projects}
            setSelectOptions={setSelectOptions}
            values={values}
          />
        );
      })}
    </div>
  );
};
export default ProjectsList;
