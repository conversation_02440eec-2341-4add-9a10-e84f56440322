import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import ReactFlow, {
  Background,
  Controls,
  addEdge,
  applyEdgeChanges,
  applyNodeChanges,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { Store } from '../../store/Store';
import { customConfirm } from '../../utils/customConfirm';

import { useLazyGetDepartmentsQuery } from '../../slices/departmentApiSlice';
import { useEditUserMutation } from '../../slices/userApiSlice';
import SelectNode from './DepartmentFlow/SelectNode';

const DepartmentFlow = () => {
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [departments, setDepartments] = useState();
  const [getDepartments] = useLazyGetDepartmentsQuery();
  const [departmentFlow, setDepartmentFlow] = useState({});
  const [editUser] = useEditUserMutation();
  const { dispatch } = useContext(Store);
  const [updateDefaults] = useUpdateDefaultsMutation();
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const nodeTypes = useMemo(() => ({ select: SelectNode }), []);

  const pagesOptionInfo = departments?.flatMap((department) => {
    return department?.navs?.map((nav) => {
      return {
        departId: department._id,
        name: nav.name,
        childNavs: nav.childNavs,
      };
    });
  });

  const pagesOptions = pagesOptionInfo?.map((g) => ({
    label: g.name,
    options: g.childNavs?.map((c) => ({
      departIds: g.departId,
      label: c.cname,
      value: c.cslug,
      group: g.name,
    })),
    value: g.name,
  }));

  useEffect(() => {
    (async () => {
      try {
        let res = await getDepartments();
        setDepartments(res.data.results);
      } catch (e) {
        console.log(e); //eslint-disable-line
      }
    })();
  }, [getDepartments]);

  const departmentExists = (id) => {
    for (let elem of departments) {
      if (elem?._id === id) {
        return true;
      }
    }
    return false;
  };

  useEffect(() => {
    setDepartmentFlow(defaultParam?.departmentFlow?.eventStructure);
  }, [defaultParam]);

  function calculateNodeLevels(nodes) {
    const nodeMap = new Map(nodes.map((node) => [node.id, node]));
    const levels = new Map();

    function getLevel(nodeId) {
      if (levels.has(nodeId)) {
        return levels.get(nodeId);
      }

      const node = nodeMap.get(nodeId);
      if (!node) {
        return 1;
      }

      if (!node.data.previousNodeId) {
        levels.set(nodeId, 1);
        return 1;
      }

      const parentLevel = getLevel(node.data.previousNodeId);
      const currentLevel = parentLevel + 1;
      levels.set(nodeId, currentLevel);
      return currentLevel;
    }

    // Calculate levels for all nodes
    nodes.forEach((node) => getLevel(node.id));

    // Update node data with calculated levels
    return nodes.map((node) => ({
      ...node,
      data: {
        ...node.data,
        level: levels.get(node.id) || 1,
      },
    }));
  }

  useEffect(() => {
    if (departments) {
      let temp = [];
      for (let elem of defaultParam?.departmentFlow?.nodeStructure?.nodes) {
        if (departmentExists(elem?.data?.selectedDepartment?._id))
          temp = [
            ...temp,
            {
              ...elem,
              data: {
                ...elem.data,
                setNodes: setNodes,
                setDepartmentFlow: setDepartmentFlow,
                departmentFlow: departmentFlow,
                onConnect: onConnect,
              },
            },
          ];
      }
      if (temp?.length === 0 || temp?.length === undefined) {
        setNodes([
          {
            id: 'node-1',
            type: 'select',
            position: { x: 0, y: 0 },
            data: {
              departments: departments,
              setNodes: setNodes,
              id: 'node-1',
              // selectedDepartment: {},
              selectedDepartment: departments?.find(
                (dpt) => dpt._id === pagesOptions?.[0]?.options?.[0]?.departIds
              ),
              selectedPage: [
                {
                  label: pagesOptions?.[0]?.options?.[0]?.label,
                  value: pagesOptions?.[0]?.options?.[0]?.value,
                  group: pagesOptions?.[0]?.options?.[0]?.group,
                },
              ],

              setDepartmentFlow: setDepartmentFlow,
              departmentFlow: departmentFlow,
              onConnect: onConnect,
            },
          },
        ]);
      } else {
        // Calculate levels and update nodes
        const nodesWithLevels = calculateNodeLevels(temp);
        setNodes(nodesWithLevels);
      }
    }
  }, [departments, defaultParam?.departmentFlow?.nodeStructure?.nodes]); //eslint-disable-line

  useEffect(() => {
    setEdges(
      defaultParam?.departmentFlow?.nodeStructure?.edges?.length > 0
        ? defaultParam?.departmentFlow?.nodeStructure?.edges
        : []
    );
  }, [departments, defaultParam?.departmentFlow?.nodeStructure?.edges]);

  const onNodesChange = useCallback(
    (changes) => {
      setNodes((nds) => applyNodeChanges(changes, nds));
    },
    [setNodes]
  );

  const onEdgesChange = useCallback(
    (changes) => {
      setEdges((eds) => applyEdgeChanges(changes, eds));
    },
    [setEdges]
  );

  const onConnect = useCallback(
    (connection) => {
      let isAllowed = false;
      for (let elem of nodes) {
        if (elem?.id === connection?.source) {
          if (
            elem?.data?.selectedDepartment?._id?.length !== 0 &&
            elem?.data?.selectedDepartment?._id?.length !== undefined
          ) {
            isAllowed = true;
          }
        }
      }
      if (isAllowed || connection?.autoConnect) {
        setEdges((eds) => addEdge(connection, eds));
      }
    },
    [setEdges, nodes]
  );

  const labelsOfNodes = (nodes) => {
    let arr = [];
    nodes.forEach((node) => {
      const { data } = node;
      arr.push(data?.selectedPage?.[0]?.label);
    });
    return arr;
  };

  const onReset = async () => {
    if (
      !(await customConfirm(
        'Are you sure you want to reset your department flow?',
        'delete'
      ))
    ) {
      return;
    }
    let resetNodes = [
      {
        id: 'node-1',
        type: 'select',
        position: { x: 0, y: 0 },
        data: {
          departments: departments,
          setNodes: setNodes,
          id: 'node-1',
          // selectedDepartment: {},
          selectedDepartment: departments?.find(
            (dpt) => dpt._id === pagesOptions?.[0]?.options?.[0]?.departIds
          ),
          selectedPage: [
            {
              label: pagesOptions?.[0]?.options?.[0]?.label,
              value: pagesOptions?.[0]?.options?.[0]?.value,
              group: pagesOptions?.[0]?.options?.[0]?.group,
            },
          ],

          setDepartmentFlow: setDepartmentFlow,
          departmentFlow: departmentFlow,
          onConnect: onConnect,
        },
      },
    ];
    setNodes(resetNodes);
    setEdges([]);
    let data = {
      nodeStructure: {
        nodes: resetNodes,
        edges: [],
      },
      eventStructure: {},
    };

    let userTemp = JSON.parse(localStorage.getItem('user'));
    updateDefaults({
      ...defaultParam,
      departmentFlow: data,
    })
      .then((res) => {
        const labels = labelsOfNodes(
          res?.data?.defaultParam?.departmentFlow?.nodeStructure?.nodes
        );
        editUser({
          data: { columnView: labels },
          id: userTemp?.user?._id,
        }).then(() => {
          userTemp = {
            ...userTemp,
            user: {
              ...userTemp?.user,
              columnView: labels,
            },
          };
          dispatch({
            type: 'SET_USER',
            payload: {
              user: userTemp?.user,
            },
          });
          localStorage.setItem('user', JSON.stringify(userTemp));
          toast.success('Department Flow Reset');
        });
      })
      .catch((err) => {
        console.log(err); //eslint-disable-line
      });
  };

  const onSubmit = async () => {
    try {
      let flow = {};
      const existingNodeIds = new Set(nodes.map((node) => node.id));
      const validEdges = edges.filter(
        (edge) =>
          existingNodeIds.has(edge.source) && existingNodeIds.has(edge.target)
      );

      for (let el of validEdges) {
        let parent;
        let child;
        for (let e of nodes) {
          if (e?.id === el?.target) {
            parent = e?.data?.selectedDepartment?._id;
          }
          if (e?.id === el?.source) {
            child = e?.data?.selectedDepartment?._id;
          }
        }
        if (child !== undefined) {
          flow = {
            ...flow,
            [parent]: [...(flow?.[parent] || []), child],
          };
        }
      }

      setDepartmentFlow(flow);

      // for (let el of edges) {
      //   let parent;
      //   let child;
      //   for (let e of nodes) {
      //     if (e?.id === el?.target) {
      //       parent = e?.data?.selectedDepartment?._id;
      //     }
      //     if (e?.id === el?.source) {
      //       child = e?.data?.selectedDepartment?._id;
      //     }
      //   }
      //   if (child !== undefined) {
      //     flow = {
      //       ...flow,
      //       [parent]: [...(flow?.[parent] || []), child],
      //     };
      //   }
      //   setDepartmentFlow((prev) => {
      //     return {
      //       ...prev,
      //       [parent]: [...(prev?.[parent] || []), child],
      //     };
      //   });
      // }

      let data = {
        nodeStructure: {
          nodes: nodes,
          edges: validEdges,
        },
        eventStructure: flow,
      };

      let userTemp = JSON.parse(localStorage.getItem('user'));
      updateDefaults({
        ...defaultParam,
        departmentFlow: data,
      })
        .then((res) => {
          const labels = labelsOfNodes(
            res?.data?.defaultParam?.departmentFlow?.nodeStructure?.nodes
          );
          editUser({
            data: { columnView: labels },
            id: userTemp?.user?._id,
          }).then(() => {
            userTemp = {
              ...userTemp,
              user: {
                ...userTemp?.user,
                columnView: labels,
              },
            };
            dispatch({
              type: 'SET_USER',
              payload: {
                user: userTemp?.user,
              },
            });
            localStorage.setItem('user', JSON.stringify(userTemp));
            toast.success('Department Flow Updated');
          });
        })
        .catch((err) => {
          console.log(err); //eslint-disable-line
        });
    } catch (e) {
      toast.error(e, {
        theme: 'colored',
        position: 'top-right',
      });
    }
  };

  return (
    <div className="relative">
      <div className="absolute top-4 right-4 z-10 flex gap-2">
        <button
          className="py-2 px-6 bg-green-primary text-white rounded-md text-sm cursor-pointer hover:opacity-90"
          onClick={onSubmit}
        >
          Submit
        </button>
        <button
          className="py-2 px-6 bg-red-primary text-white rounded-md text-sm cursor-pointer hover:opacity-90"
          onClick={onReset}
        >
          Reset
        </button>
      </div>
      <div
        style={{
          width: '100vw',
          height: '100vh',
        }}
      >
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          deleteKeyCode={['Backspace', 'Delete']}
          fitView
        >
          <Background color="black" variant="dots" />
          <Controls position="top-left" />
        </ReactFlow>
      </div>
    </div>
  );
};

export default DepartmentFlow;
