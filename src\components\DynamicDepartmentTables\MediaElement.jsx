import pdfIcon from '../../assets/images/pdf.png';
import voice from '../../assets/images/voice.png';
import csvlogo from '../../assets/svgs/csvlogo.svg';
import jpgpng from '../../assets/svgs/jpg_png.svg';

import { useEffect, useState } from 'react';

const MediaElement = ({ item, getMedia, setPreview }) => {
  const [media, setMedia] = useState();

  const populateMedia = async () => {
    const mediaData = await getMedia(item);
    setMedia(mediaData?.data?.media);
    // downloadMedia(mediaData?.data?.media);
    setPreview({
      file: mediaData?.data?.media,
      openModal: true,
    });
  };

  useEffect(() => {
    if (item) {
      populateMedia();
    }
  }, [item]); //eslint-disable-line

  return (
    <section className="p-2 border rounded-md text-center w-fit max-w-[20%] basis-1/3 grow shrink">
      {media && (
        <img
          src={
            media?.type === 'application/pdf'
              ? pdfIcon
              : media?.type === 'audio'
                ? voice
                : media?.type === 'text/csv'
                  ? csvlogo
                  : jpgpng
          }
          className="hover:cursor-pointer min-w-[8%] m-auto aspect-video object-contain"
        />
      )}
      {media && <p className="truncate">{media.name}</p>}
    </section>
  );
};

export default MediaElement;
