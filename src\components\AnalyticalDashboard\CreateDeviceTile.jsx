import { useEffect } from 'react';
import { useLazyGetDeviceDataByDevicesQuery } from '../../slices/deviceDataApiSlice';
import Graph from './Graph';

const CreateDeviceTile = ({ cDiv, devices, period }) => {
  const [getDeviceDataByDevices, { data: divData = {} }] =
    useLazyGetDeviceDataByDevicesQuery();
  const { deviceDatas: allAeviceDatas = [] } = divData;

  useEffect(() => {
    if (devices) {
      const deviceIds = devices.map((div) => div._id).join(',');

      getDeviceDataByDevices(
        {
          query: {
            deviceIds,
            period,
          },
        },
        false
      ).unwrap();
    }
  }, [devices, period, getDeviceDataByDevices]);

  return (
    <div className="w-full last:mb-0">
      <section className="grid grid-cols-2 gap-3">
        {devices?.map((device) => {
          const deviceDatas = allAeviceDatas.filter(
            (data) => data.device === device._id
          );

          const fields = cDiv.fields.filter((field) => field.type === 'value');

          return (
            <section
              key={device._id}
              className="w-full bg-white mb-3 rounded-lg shadow-low px-2 py-1"
            >
              <h5 className="text-center">
                {`${cDiv.type} ${device.deviceId} `}
                <span className="text-[12px]">
                  ({device.assignedDevice.map((div) => div.deviceId).join(', ')}
                  )
                </span>
              </h5>
              <Graph
                deviceDatas={deviceDatas}
                device={device}
                period={period}
                fields={fields}
              />
            </section>
          );
        })}
      </section>
    </div>
  );
};

export default CreateDeviceTile;
