import { toast } from 'react-toastify';
import add from '../../../assets/images/voice.png';
import Input from '../../global/components/Input';
import MultiSelect from '../../global/components/MultiSelect';
import Select from '../../global/components/Select';
import Table from '../../global/components/Table';
import RecordingPopup from './RecordingPopup';
import { Store } from '../../../store/Store';
import { useContext, useEffect } from 'react';

const DynamicForm = ({
  formFields,
  formData,
  setFormData,
  isQrDisabled,
  percent,
  setPercent,
  individualBatchSize = [],
  showRecording,
  setShowRecording,
  isJobForm = false,
}) => {
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  useEffect(() => {
    if (defaultParam?.defaultSamplingMode) {
      let value = defaultParam?.defaultSamplingMode;
      if (value === 'Single') {
        setFormData({
          ...formData,
          Sampling: true,
          interval: [],
          samples: [],
          type: value,
        });
      } else if (value !== 'None') {
        setFormData({
          ...formData,
          Sampling: true,
          interval: [],
          samples: [],
          type: value,
        });
      } else {
        setFormData({
          ...formData,
          Sampling: false,
          interval: [],
          samples: [],
          type: 'None',
        });
      }
    }
  }, [defaultParam?.defaultSamplingMode]); // eslint-disable-line

  const handleChange = (e, idx) => {
    const { name, value, type, checked } = e.target;

    if (name === 'interval') {
      if (+value > +individualBatchSize[idx]?.batchSize) {
        toast.error('Interval cannot be greater than batch size', {
          toastId: 'interval',
        });
        return;
      }

      if (
        Math.floor(
          (+individualBatchSize[idx]?.batchSize / +value) *
            +formData?.samples[idx]
        ) > +individualBatchSize[idx]?.batchSize
      ) {
        toast.error('Sampling values cannot be greater than batch size', {
          toastId: 'calc',
        });
        return;
      }
    }
    if (name === 'samples') {
      if (+value > +individualBatchSize[idx]?.batchSize) {
        toast.error('Samples cannot be greater than batch size', {
          toastId: 'samples',
        });
        return;
      }

      if (
        Math.floor(
          (+individualBatchSize[idx]?.batchSize / +formData?.interval[idx]) *
            +value
        ) > +individualBatchSize[idx]?.batchSize
      ) {
        toast.error('Sampling values cannot be greater than batch size', {
          toastId: 'calc',
        });
        return;
      }
    }

    if (name === 'SamplingSelect') {
      if (value === 'Single') {
        setFormData({
          ...formData,
          Sampling: true,
          interval: [],
          samples: [],
          type: value,
        });
      } else if (value !== 'None') {
        setFormData({
          ...formData,
          Sampling: true,
          interval: [],
          samples: [],
          type: value,
        });
      } else {
        setFormData({
          ...formData,
          Sampling: false,
          interval: [],
          samples: [],
          type: 'None',
        });
      }
    } else if (name === 'percentage') {
      setPercent(+value);

      setFormData({
        ...formData,
        Sampling: true,
        interval: [],
        samples: [],
        percent: +value,
      });
    } else {
      if (name === 'interval') {
        let arr = [...formData?.interval];
        arr[idx] = value;

        setFormData({
          ...formData,
          [name]: arr,
        });
      } else if (name === 'samples') {
        let arr = [...formData?.samples];
        arr[idx] = value;

        setFormData({
          ...formData,
          [name]: arr,
        });
      } else {
        setFormData({
          ...formData,
          [name]: type === 'checkbox' ? checked : value,
        });
      }
    }
  };

  return (
    <>
      {showRecording && (
        <RecordingPopup
          setShowRecording={setShowRecording}
          formData={formData}
          setFormdata={setFormData}
        />
      )}
      <div className="w-full mt-8">
        <div className="flex justify-end mb-1 h-6">
          <img src={add} onClick={() => setShowRecording(true)}></img>
        </div>

        <Table className="w-full">
          <Table.Head>
            <Table.Row>
              <Table.Th>Field Name</Table.Th>
              <Table.Th>Actions</Table.Th>
              <Table.Th>Mandatory</Table.Th>
              <Table.Th>Media</Table.Th>
            </Table.Row>
          </Table.Head>

          <Table.Body>
            {formFields?.map((field, index) => {
              if (
                field.fieldType === 'MultiCheckbox' ||
                field.fieldType === 'Table'
              )
                return null;
              return (
                <Table.Row key={index}>
                  <Table.Td>{field.fieldName}</Table.Td>

                  <Table.Td>
                    {/* Check Input Field ============> */}
                    {field.fieldType === 'Check' && (
                      <input
                        type="checkbox"
                        name={field.fieldName}
                        id={field.fieldName}
                        checked={formData?.[field.fieldName] || false}
                        onChange={handleChange}
                        className=" leading-tight"
                      />
                    )}

                    {/* Check Input Field ============> */}
                    {field.fieldType === 'MultiCheckbox' && (
                      <input
                        type="checkbox"
                        name={field.fieldName}
                        id={field.fieldName}
                        checked={formData?.[field.fieldName] || false}
                        onChange={handleChange}
                        className=" leading-tight"
                      />
                    )}

                    {/* Range Input Field ============> */}
                    {field.fieldType === 'Range' && (
                      <div className="flex">
                        <input
                          type="number"
                          name={field.fieldName + '-Min'}
                          id={field.fieldName}
                          value={formData?.[field.fieldName + '-Min'] || ''}
                          onChange={handleChange}
                          placeholder="Min Range"
                          className="w-[45%] shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mr-6"
                        />

                        <input
                          type="number"
                          name={field.fieldName + '-Max'}
                          id={field.fieldName}
                          value={formData?.[field.fieldName + '-Max'] || ''}
                          onChange={handleChange}
                          placeholder="Max Range"
                          className="w-[45%] shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />
                      </div>
                    )}

                    {/* Threshold Input Field ============> */}
                    {field.fieldType === 'Range Threshold' && (
                      <div className="flex">
                        <input
                          type="number"
                          name={field.fieldName + '-Main'}
                          id={field.fieldName}
                          value={formData?.[field.fieldName + '-Main'] || ''}
                          onChange={handleChange}
                          placeholder="Main Value"
                          className="w-[45%] shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mr-6"
                        />

                        <input
                          type="number"
                          name={field.fieldName + '-Thres'}
                          id={field.fieldName}
                          value={formData?.[field.fieldName + '-Thres'] || ''}
                          onChange={handleChange}
                          placeholder="Threshold Value"
                          className="w-[45%] shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />
                      </div>
                    )}

                    {/* Date Input Field ============> */}
                    {field.fieldType === 'Date' && (
                      <div className="flex justify-between">
                        <input
                          type="date"
                          name={field?.fieldName}
                          id={field?.fieldName}
                          value={formData?.[field.fieldName] || ''}
                          onChange={handleChange}
                          className="w-[45%] shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        />

                        <div className="flex ">
                          <Select
                            menuPosition="fixed"
                            className=""
                            name={field.fieldName + '-Condition'}
                            value={
                              formData?.[field.fieldName + '-Condition'] || ''
                            }
                            onChange={handleChange}
                            options={['LT', 'GT', 'ET'].map((option) => ({
                              name: option,
                              value: option,
                            }))}
                            placeholder="Select Condition"
                          />
                        </div>
                      </div>
                    )}
                    {field.fieldType === 'String' && (
                      <input
                        type="text"
                        name={field?.fieldName}
                        id={field?.fieldName}
                        value={formData?.[field.fieldName] || ''}
                        onChange={handleChange}
                        placeholder="String"
                        className="w-[100%] shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      />
                    )}

                    {field.fieldType === 'DropDown' && (
                      <Select
                        menuPosition="fixed"
                        type="text"
                        name={field?.fieldName}
                        id={field?.fieldName}
                        options={field?.fieldOptions}
                        onChange={handleChange}
                        value={formData[field?.fieldName]}
                      />
                    )}

                    {field.fieldType === 'MultiSelect' && (
                      <MultiSelect
                        name={field?.fieldName}
                        id={field?.fieldName}
                        options={field?.fieldOptions}
                        onChange={handleChange}
                        value={formData[field?.fieldName]}
                      />
                    )}
                  </Table.Td>

                  <Table.Td className="text-center">
                    <input
                      type="checkbox"
                      name={field.fieldName + '-Man'}
                      id={field.fieldName}
                      checked={formData?.[field.fieldName + '-Man'] || false}
                      onChange={handleChange}
                      className="mr-2 mt-1 leading-tight"
                    />
                  </Table.Td>
                  <Table.Td className="text-center">
                    <input
                      type="checkbox"
                      name={field.fieldName + '-MediaMan'}
                      id={field.fieldName}
                      checked={
                        formData?.[field.fieldName + '-MediaMan'] || false
                      }
                      onChange={handleChange}
                      className="mr-2 leading-tight"
                    />
                  </Table.Td>
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
        {formData?.audioFile && (
          <div className="text-sm mt-2 ml-5">
            Recorded File : &nbsp; &nbsp;
            <span className="text-red-500">
              {formData?.audioFile?.name}.wav / {formData?.audioFile?.type}
            </span>
          </div>
        )}
      </div>

      {!isJobForm && (
        <>
          <Table className="w-full mt-2">
            <Table.Head>
              <Table.Row>
                <Table.Th>Sampling</Table.Th>
                <Table.Th>Generate QR</Table.Th>
                <Table.Th>Scan QR</Table.Th>
              </Table.Row>
            </Table.Head>

            <Table.Body>
              <Table.Row>
                <Table.Td className="text-center">
                  <Select
                    menuPosition="fixed"
                    className="mr-3"
                    name="SamplingSelect"
                    value={formData?.['type']}
                    onChange={handleChange}
                    options={['None', 'Single', 'Percentage', 'Sampling'].map(
                      (option) => ({
                        name: option,
                        value: option,
                      })
                    )}
                    placeholder="Select"
                  />
                </Table.Td>

                <Table.Td className="text-center">
                  <input
                    type="checkbox"
                    name="GenerateQR"
                    id="GenerateQR"
                    disabled={isQrDisabled || formData?.['ScanQR']}
                    checked={formData?.['GenerateQR'] || false}
                    onChange={handleChange}
                    className="mr- mt-1 leading-tight"
                  />
                </Table.Td>

                <Table.Td className="text-center">
                  <input
                    type="checkbox"
                    name="ScanQR"
                    id="ScanQR"
                    disabled={formData?.['GenerateQR']}
                    checked={formData?.['ScanQR'] || false}
                    onChange={handleChange}
                    className="mr-6 mt-1 leading-tight"
                  />
                </Table.Td>
              </Table.Row>
            </Table.Body>
          </Table>

          {formData?.['type'] === 'Sampling' && (
            <Table className="w-full mt-2">
              <Table.Head>
                <Table.Row>
                  <Table.Th>Batch Number</Table.Th>
                  <Table.Th>Batch Size</Table.Th>
                  <Table.Th>Interval</Table.Th>
                  <Table.Th>Samples</Table.Th>
                  <Table.Th>Total Samples</Table.Th>
                </Table.Row>
              </Table.Head>

              <Table.Body>
                {individualBatchSize.map((item, idx) => {
                  return (
                    <Table.Row key={idx}>
                      <Table.Td className="text-center">
                        {item.batchNo}
                      </Table.Td>
                      <Table.Td className="text-center">
                        {item.batchSize}
                      </Table.Td>
                      <Table.Td className="text-center">
                        <Input
                          type="number"
                          name="interval"
                          id="interval"
                          max={item?.batchSize}
                          value={formData?.['interval'][idx] || ''}
                          onChange={(e) => handleChange(e, idx)}
                          placeholder="Interval Value"
                        />
                      </Table.Td>

                      <Table.Td className="text-center">
                        <Input
                          type="number"
                          name="samples"
                          id="samples"
                          value={formData?.['samples'][idx] || ''}
                          onChange={(e) => handleChange(e, idx)}
                          placeholder="No of samples"
                        />
                      </Table.Td>

                      <Table.Td className="text-center">
                        <Input
                          readOnly
                          value={
                            Math.floor(
                              (item?.batchSize / +formData?.interval[idx]) *
                                +formData?.samples[idx]
                            ) || ''
                          }
                          placeholder="Total samples"
                        />
                      </Table.Td>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
          )}

          {formData?.['type'] === 'Percentage' && (
            <>
              <section className="flex  justify-evenly">
                <span className="text-md font-semibold mt-2 mr-[20%] ml-5">
                  Percentage :
                </span>
                <Input
                  type="number"
                  name="percentage"
                  id="percentage"
                  value={percent || formData?.percent}
                  onChange={handleChange}
                  placeholder="Percentage Value"
                />
              </section>

              <Table className="w-full mt-3">
                <Table.Head>
                  <Table.Row>
                    <Table.Th>Batch Number</Table.Th>
                    <Table.Th>Batch Size</Table.Th>
                    <Table.Th>Total Samples</Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {individualBatchSize.map((item, idx) => {
                    return (
                      <Table.Row key={idx}>
                        <Table.Td className="">{item.batchNo}</Table.Td>
                        <Table.Td className="">{item.batchSize}</Table.Td>
                        <Table.Td>
                          <Input
                            readOnly
                            value={
                              Math.floor(
                                (+item.batchSize * +formData?.percent) / 100
                              ) || 'NA'
                            }
                            placeholder="Total samples"
                          />
                        </Table.Td>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </>
          )}
        </>
      )}
    </>
  );
};

export default DynamicForm;
