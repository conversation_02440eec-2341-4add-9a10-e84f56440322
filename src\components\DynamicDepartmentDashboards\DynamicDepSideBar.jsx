import { EditOutlined, PrinterOutlined } from '@ant-design/icons';
import { Divider, Modal, Tooltip, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { FaLock } from 'react-icons/fa';
import { getLocalDateTime, handlePdf } from '../../helperFunction';
import CreateDynamicDep from './CreateDynamicDep';
import {
  renderFieldValueForSideBar,
  sanitizeColName,
} from './DynamicDepTableUtilityFunc';
import ViewDepFields from './ViewDepFields';
import { useLazyGetPdfQuery } from '../../slices/pdfApiSlice';
import Spinner from '../global/components/Spinner';
import { useLazyGetDepartmentColumnByChildNavQuery } from '../../slices/departmentColumnApiSlice';

const { Text, Title } = Typography;

const DynamicDepSideBar = ({
  selectedRow,
  columns,
  data,
  setOpenSideBar,
  fromKanban,
}) => {
  let userId = JSON.parse(localStorage.getItem('user'))?.user?._id;
  const [openEditModal, setOpenEditModal] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalData, setModalData] = useState({
    title: '',
    data: null,
    type: '',
  });
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const [getColumnsByChildNav, { data: depCols }] =
    useLazyGetDepartmentColumnByChildNavQuery();

  useEffect(() => {
    const fetchColumns = async () => {
      if (fromKanban && data) {
        await getColumnsByChildNav({ id: data?.departmentChildNav });
      }
    };
    fetchColumns();
  }, [fromKanban, data]); // eslint-disable-line

  const handleModalOpen = (e, value, title, type) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setIsModalOpen(true);
    setModalData({ title, data: value, type });
  };

  if (!selectedRow) return null;
  return (
    <div className="p-4">
      {!fromKanban && (
        <header className="flex gap-x-3 justify-self-end">
          {isFetchingPdf ? (
            <Spinner size={6} />
          ) : (
            <Tooltip title={'Print'}>
              <PrinterOutlined
                className="text-lg cursor-pointer"
                onClick={() => {
                  handlePdf(getPdf, selectedRow?._id, 'departmentRow');
                }}
              />
            </Tooltip>
          )}

          <Tooltip title={'Edit'}>
            <EditOutlined
              className="text-lg cursor-pointer"
              onClick={() => {
                setOpenEditModal(true);
                setOpenSideBar(false);
              }}
            />
          </Tooltip>
        </header>
      )}
      <div className="mb-6">
        <Title level={5}>Task Details</Title>
        <div className="border rounded-lg divide-y">
          <div className="p-3 flex items-center justify-between bg-gray-50">
            <Text>Task ID</Text>
            <Text>{selectedRow?.taskId?.taskId || '-'}</Text>
          </div>
          <div className="p-3 flex items-center justify-between">
            <Text>Created Date</Text>
            <Text>{getLocalDateTime(selectedRow?.createdAt)}</Text>
          </div>
        </div>
      </div>

      <Divider />
      {/* Field Details Section */}
      <div className="space-y-4">
        <Title level={5}>Field Details</Title>
        <div className="border rounded-lg divide-y">
          {(columns || depCols?.columns || [])?.map((column) => {
            let fieldData = selectedRow?.data?.[sanitizeColName(column?.name)];
            if (!fieldData) {
              fieldData = selectedRow?.data?.[column?.name];
            }
            let columnUsers = column?.users;
            let isVisible = true;
            if (column?.users?.length !== 0) {
              isVisible = columnUsers?.includes(userId);
            }
            return (
              <div
                key={column._id}
                className={`p-3 flex items-center justify-between `}
              >
                <Text className="flex-1">{column?.name}</Text>
                {isVisible ? (
                  <div className="flex flex-1 flex-wrap justify-end">
                    {renderFieldValueForSideBar(
                      fieldData,
                      sanitizeColName(column?.name),
                      handleModalOpen
                    )}
                  </div>
                ) : (
                  <FaLock size={20} color="gray" />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Edit Modal */}
      <CreateDynamicDep
        openModal={openEditModal}
        setOpenModal={setOpenEditModal}
        editData={selectedRow}
        columns={columns}
        data={data}
      />
      {/* Modal */}
      <Modal
        title={
          <Title level={4} style={{ textAlign: 'center' }}>
            {modalData?.title}
          </Title>
        }
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width={800}
        styles={{
          body: {
            maxHeight: `calc(100vh - 150px)`,
            overflowY: 'auto',
          },
        }}
      >
        <div className="py-4">
          <ViewDepFields data={modalData?.data} type={modalData?.type} />
        </div>
      </Modal>
    </div>
  );
};

export default DynamicDepSideBar;
