import { useState } from 'react';
import TruncateString from '../../global/TruncateString';
import Sheets from './Sheets';

const Tabs = ({
  formData,
  setFormData,
  productionFlow,
  multiProcessTablesData,
  setMultiProcessTablesData,
  handleLinkFieldFormValChange,
  step,
  goalsData,
  goalsTables,
  setGoalsTables,
  masters,
  jobDetails,
  isEdit,
}) => {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <div className="w-full">
      <div className="w-full flex overflow-x-scroll no-scrollbar border-b">
        {formData.map((tab, idx) => (
          <p
            key={idx}
            className={`relative text-black w-[15%] text-center hover:cursor-pointer font-bold p-3 border-b-2 ${
              activeTab === idx ? 'border-blue-primary' : 'border-white'
            }`}
            onClick={() => setActiveTab(idx)}
          >
            <TruncateString length={12}>{tab.name}</TruncateString>
          </p>
        ))}
      </div>
      <div className="w-full">
        <Sheets
          tab={formData?.[activeTab] || {}}
          tabIdx={activeTab}
          setFormData={setFormData}
          productionFlow={productionFlow}
          multiProcessTablesData={multiProcessTablesData}
          setMultiProcessTablesData={setMultiProcessTablesData}
          handleLinkFieldFormValChange={handleLinkFieldFormValChange}
          step={step}
          goalsData={goalsData}
          goalsTables={goalsTables}
          setGoalsTables={setGoalsTables}
          masters={masters}
          jobDetails={jobDetails}
          isEdit={isEdit}
        />
      </div>
    </div>
  );
};

export default Tabs;
