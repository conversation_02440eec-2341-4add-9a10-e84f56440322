import cross from '../../../assets/images/cross.png';
import userImg from '../../../assets/images/user 1.png';

function RightNav({ toggleMenu, isActive, dataList, navTitle }) {
  //   const [isActive, setIsActive] = useState(false);

  //   const toggleMenu = () => {
  //     setIsActive(!isActive);
  //   };
  return (
    <>
      {/* <button
        className={`fixed top-20 right-20 bg-purple-600 text-white p-4 transform transition-transform ${
          isActive ? 'translate-x-[-90px]' : ''
        }`}
        onClick={toggleMenu}
      >
        ☰
      </button> */}
      <nav
        className={`fixed right-0 top-0 bg-white h-screen p-8 z-[200] transform transition-transform w-72 ${
          isActive ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="flex flex-row justify-between">
          <div className="flex flex-row items-center">
            <img src={userImg} alt="" className="w-5 h-5" />
            <h4 className="ml-1 text-[#667085]">{navTitle}</h4>
          </div>
          <img
            src={cross}
            alt=""
            className="w-3 h-3 cursor-pointer"
            onClick={toggleMenu}
          />
        </div>
        <ul className="list-none p-0 mt-1">
          {dataList.map((e, idx) => (
            <li key={idx} className="py-[1px] ">
              <a href="#" className="text-[#8290BC] no-underline text-xs">
                {`${e.replaceAll('"', '')}`}
              </a>
            </li>
          ))}
          {/* <li className="py-4">
            <a href="#" className="text-black no-underline">
              About
            </a>
          </li>
          <li className="py-4">
            <a href="#" className="text-black no-underline">
              Contact
            </a>
          </li> */}
        </ul>
      </nav>
    </>
  );
}

export default RightNav;
