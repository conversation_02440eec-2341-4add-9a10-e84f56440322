import { useState } from 'react';

import { useUpdateReminderDateAndTimeMutation } from '../../slices/orderApiSlice';
import { handleUpdateRemainderDateAndTime } from './kanbanFunctions';

import { toast } from 'react-toastify';

import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Textarea from '../global/components/Textarea';

const ReminderModal = ({ setReminderModal, user, card }) => {
  const [updateReminderDateAndTime, { isLoading }] =
    useUpdateReminderDateAndTimeMutation();

  const [reminderData, setReminderData] = useState({
    date: new Date().toISOString()?.split('T')[0],
    time: '',
  });
  const [reminderRemarks, setReminderRemarks] = useState('');

  return (
    <div
      className="fixed inset-0 z-20 flex items-center justify-center z-100 bg-gray-900 bg-opacity-50"
      onClick={() => setReminderModal(false)}
    >
      <div
        className="relative w-[420px] bg-white border border-gray-200 shadow-md rounded-lg p-2"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col items-start w-full">
          <div className="w-full">
            <label className="mb-1 text-sm font-bold">Set Reminder</label>
            <div className="flex items-center gap-1 w-full">
              <Input
                type="date"
                onChange={(e) =>
                  setReminderData((prev) => ({
                    ...prev,
                    date: e.target.value,
                  }))
                }
                value={reminderData.date}
                wrapperClassName="w-full"
              />
              <Input
                type="time"
                onChange={(e) =>
                  setReminderData((prev) => ({
                    ...prev,
                    time: e.target.value,
                  }))
                }
                value={reminderData.time}
                wrapperClassName="w-full"
              />
            </div>
          </div>

          <div className="w-full">
            <label>Remarks</label>
            <Textarea
              placeholder="Enter Remarks"
              value={reminderRemarks}
              onChange={(e) => setReminderRemarks(e.target.value)}
            />
          </div>
        </div>
        <div className="flex items-center justify-end">
          <Button
            className="mt-4 !py-1"
            isLoading={isLoading}
            onClick={() => {
              handleUpdateRemainderDateAndTime({
                updateReminderDateAndTime,
                reminderRemarks,
                user,
                card,
                setReminderRemarks,
                toast,
                setReminderModal,
              });
            }}
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReminderModal;
