import { Fragment, useRef, useState } from 'react';
import dragImage from '../../../assets/images/hand1.png';
import deleteIcon from '../../../assets/images/icons8-delete.svg';
import Button from './Button'; // Import your Button component
import DragAndDrop from './DragAndDrop'; // Import your DragAndDrop component
import Input from './Input'; // Import your Input component

export default function DragAndDropForm({
  formFields,
  setFields,
  isLoadingDR,
  AllRelation,
  SearchParams,
  setSearchParams,
  DeleteField,
  setOpenEditModal,
  setFieldData,
  OpeneditModal,
  getFieldData,
  editicon,
  setRelationEditModal,
  setReletionSelectedForEdit,
  DeleteRelationField,
  ShowReltionEditModal,
}) {
  const capatalize = (text) => {
    return text.charAt(0).toUpperCase() + text.slice(1);
  };

  const DragItem = useRef(0);
  const DragOverItem = useRef(0);
  const [Show, setShow] = useState(false);
  const [selectedIndex] = useState();

  const handleSort = () => {
    setShow(false);
    const itemArray = [...formFields];
    const temp = itemArray[DragItem.current];
    itemArray[DragItem.current] = itemArray[DragOverItem.current];
    itemArray[DragOverItem.current] = temp;
    setFields(itemArray);
    DragItem.current = 0;
    DragOverItem.current = 0;
  };

  return (
    <>
      <div className="w-full">
        {formFields.map((item, idx) => (
          <div
            key={idx}
            className={`w-full mt-5 cursor-pointer`}
            draggable
            onDragStart={() => {
              DragItem.current = idx;
              setShow(false);
            }}
            onDragEnter={() => {
              DragOverItem.current = idx;
              setShow(false);
            }}
            onDragOver={(e) => e.preventDefault()}
            onDragEnd={handleSort}
          >
            <div className={`h-auto flex w-full`}>
              <div className="flex-col ">
                <label
                  htmlFor={item?.heading || item?.field_heading}
                  className="font-semibold flex items-center justify-between gap-x-[3px] "
                >
                  <span>
                    {item?.heading || item?.field_heading}
                    {item?.ismandatory && (
                      <span className="text-xl text-red-500 -mt-2 ">*</span>
                    )}
                  </span>
                  {SearchParams.get('edit') === 'edit' && (
                    <span className="edit-btn inline-block ml-[8px]">
                      <img
                        role="button"
                        onClick={() => {
                          setSearchParams(
                            (prev) => {
                              prev.set('edit_id', item._id);
                              return prev;
                            },
                            {
                              replace: true,
                            }
                          );
                          if (SearchParams?.get('edit_id') === 'undefined') {
                            setFieldData(item);
                            setOpenEditModal(!OpeneditModal);
                            setSearchParams(
                              (prev) => {
                                prev.set('field_name_for_edit', item?.heading);
                                return prev;
                              },
                              {
                                replace: true,
                              }
                            );
                            return;
                          }
                          setOpenEditModal(!OpeneditModal);
                          getFieldData();
                        }}
                        src={editicon}
                        className="block w-[18px] h-[18px] cursor-pointer"
                        alt="edit button"
                      />
                    </span>
                  )}
                </label>

                <div className="text-input flex">
                  {(item.type === 'date' || item?.field_type === 'date') && (
                    <Input
                      className="w-[30rem] mt-2"
                      type="date"
                      placeholder={`Enter ${
                        item.heading || item.field_heading
                      }`}
                    />
                  )}
                </div>
                <div className="text-input flex">
                  {(item.type === 'number' ||
                    item?.field_type === 'number') && (
                    <Input
                      className="w-[30rem] mt-2"
                      type="number"
                      placeholder={`Enter ${
                        item.heading || item.field_heading
                      } in Numbers Only`}
                    />
                  )}
                </div>
                <div className="text-input flex">
                  {(item.type === 'text' || item?.field_type === 'text') && (
                    <Input
                      className="w-[30rem] mt-2"
                      placeholder={`Enter ${
                        item.heading || item.field_heading
                      }`}
                    />
                  )}
                </div>
                <div className="text-input">
                  {(item.type === 'email' || item?.field_type === 'email') && (
                    <Input
                      className="w-[30rem] mt-2"
                      placeholder={`Enter ${
                        item.heading || item.field_heading
                      }`}
                    />
                  )}
                </div>
                <div className="flex flex-col gap-y-2">
                  {(item.type === 'dropdown' ||
                    item?.field_type === 'dropdown') && (
                    <select className="block w-[30rem] mt-2 min-h-[2.5rem] rounded-md border text-left text-black outline-none border-[#C8CEE1] px-2">
                      {item?.options?.map((option) => (
                        <option
                          key={option}
                          value={
                            item?.heading === 'Products' ||
                            item?.field_heading === 'Products'
                              ? option.split(',')[0]
                              : option
                          }
                          className="inline-block h-[2rem]"
                        >
                          {capatalize(
                            item?.heading === 'Products' ||
                              item?.field_heading === 'Products'
                              ? option.split(',')[1]
                              : option
                          )}
                        </option>
                      ))}
                    </select>
                  )}
                  {item?.options?.map((parent) => (
                    <Fragment key={parent}>
                      {AllRelation?.map((relation) => {
                        if (parent === relation?.parent_option) {
                          return (
                            <div
                              className="bg-slate-200 p-2 rounded-md relative"
                              key={relation?._id}
                            >
                              <label
                                htmlFor=""
                                className="font-semibold w-full flex items-end justify-between"
                              >
                                {relation?.heading}
                                {SearchParams?.get('edit') === 'edit' && (
                                  <span className="edit-btn">
                                    <img
                                      role="button"
                                      onClick={() => {
                                        setRelationEditModal(
                                          !ShowReltionEditModal
                                        );
                                        setReletionSelectedForEdit(relation);
                                      }}
                                      src={editicon}
                                      className="inline-block w-[18px] h-[18px] cursor-pointer"
                                      alt="edit button"
                                    />
                                  </span>
                                )}
                              </label>
                              <div className="relation-inputs flex w-full gap-3 items-center">
                                {relation?.type === 'text' && (
                                  <Input className="w-full" />
                                )}
                                {relation?.type === 'dropdown' && (
                                  <select
                                    name=""
                                    id=""
                                    className="block w-full mt-2 min-h-[2.5rem] rounded-md border text-left text-black outline-none border-[#C8CEE1] px-2"
                                  >
                                    {relation?.options?.map((option) => (
                                      <option key={option}>{option}</option>
                                    ))}
                                  </select>
                                )}
                                {relation?.type === 'media' && (
                                  <DragAndDrop className="w-[30rem] h-[2rem]" />
                                )}
                                <Button
                                  isLoading={isLoadingDR}
                                  className="bg-blue-500 rounded-full w-[36px] h-[36px] mb-1 p-1  hover:bg-blue-600 transition-all duration-200"
                                  onClick={() => {
                                    DeleteRelationField(relation?._id);
                                  }}
                                  icon={
                                    <img
                                      src={deleteIcon}
                                      className="block w-full h-full"
                                      alt={`delete ${relation?.heading} field`}
                                    />
                                  }
                                >
                                  ''
                                </Button>
                              </div>
                            </div>
                          );
                        }
                        return null;
                      })}
                    </Fragment>
                  ))}
                </div>
                {(item.type === 'media' || item?.field_type === 'media') && (
                  <DragAndDrop className="!w-[30rem] !h-[2rem]" />
                )}
              </div>
              <div className="relative flex">
                <span>
                  <img
                    title="Drag the field"
                    src={dragImage}
                    className="w-5 h-5 mt-11 ml-2  bg:white  select-none  hover:scale-(1) ease-in duration-300"
                  />
                </span>
                <span
                  className={`w-[10rem] text-white text-center ease-in mb-4 duration-300 bg-slate-800 rounded-md absolute bottom-10 left-3 ${
                    Show && selectedIndex == idx ? 'block' : 'hidden'
                  } `}
                >
                  {'Drag the field'}
                </span>
              </div>
              {!['Name', 'Email', 'Company Name', 'Phone Number'].includes(
                item?.heading || item?.field_heading
              ) && (
                <button
                  className="bg-blue-500 rounded-full w-8 h-8 p-1 mt-10 ml-2 hover:bg-blue-600 transition-all duration-200"
                  onClick={() => {
                    DeleteField(
                      item?._id,
                      item?.heading || item?.field_heading
                    );
                  }}
                >
                  <img
                    src={deleteIcon}
                    className="block w-full h-full"
                    alt=""
                  />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
