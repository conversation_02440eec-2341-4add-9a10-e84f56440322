import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { handleFormula } from '../../helperFunction.js';
import useCompanyDetailsSelector from '../../hooks/useCompanyDetailsSelector.js';
import usePrefixIds from '../../hooks/usePrefixIds.js';
import { useGetAdditionalChargesQuery } from '../../slices/additionalChargesApiSllice.js';
import { useLinkSalesOrderMutation } from '../../slices/createPoApiSlice.js';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice.js';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice.js';
import { useUpdateNcrMutation } from '../../slices/ncrApiSlice.js';
import {
  useCreateOrderMutation,
  useGetAllOrdersMutation,
  useUpdateOrderMutation,
} from '../../slices/orderApiSlice.js';
import { useGetQuotationsQuery } from '../../slices/quotationApiSlice.js';
import {
  useCreateSalesOrderMutation,
  useGetLatestSoQuery,
  useGetSalesOrderMutation,
  useUpdateSalesOrderMutation,
} from '../../slices/salesOrderSlices.js';
import { Store } from '../../store/Store.js';
import HsnSacModal from '../HsnSacModal.jsx';
import ProductFormatManager from '../ProductFormats/ProductFormatManager.jsx';
import { formatSalesOrderProducts } from '../ProductFormats/SalesOrderDataFormater.js';
import Attachments from './attachments.jsx';
import Charges from './charges.jsx';
import OrderDetails from './orderDetails.jsx';
import ProductTable from './productTable.jsx';
const calculateTaxAmount = (amount, percentage) =>
  (amount * (percentage || 0)) / 100;

const calculateTotalPrice = (cgst = 0, sgst = 0, igst = 0, amount) => {
  return new Promise((resolve) => {
    let localTotalPrice = 0;
    const cgstAmount = calculateTaxAmount(amount, cgst);
    const sgstAmount = calculateTaxAmount(amount, sgst);
    const igstAmount = calculateTaxAmount(amount, igst);
    localTotalPrice += amount + cgstAmount + sgstAmount + igstAmount;
    resolve({ price: localTotalPrice.toFixed(2), amount });
  });
};

const SoForm = (props) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'voucherId',
  });
  const [createDepOrder] = useCreateOrderMutation();
  const [getAllOrders] = useGetAllOrdersMutation();
  const [additionalCharges, setAdditionalCharges] = useState({});
  const [openAddUomModal, setOpenAddUomModal] = useState(false);
  const { data: allAdditionalCharges } = useGetAdditionalChargesQuery();
  const [pdf, setPdf] = useState(props?.files ?? []);
  const [items, setItems] = useState(props?.items ?? []);
  const [selectedTermAndCondition, setSelectedTermAndCondition] = useState([]);
  const [isDefaultTermsAndConditions, setIsDefaultTermsAndConditions] =
    useState(false);
  const { data: latestSo } = useGetLatestSoQuery();
  const [isDefaultDeliveryAddress, setIsDefaultDeliveryAddress] =
    useState(false);
  const [isDefaultBillingAddress, setIsDefaultBillingAddress] = useState(false);
  const [isDefaultBankDetails, setIsDefaultBankDetails] = useState(false);
  const [createOrder, { isLoading: isCreateSalesOrderLoading }] =
    useCreateSalesOrderMutation();
  const [updateOrder, { isLoading: isUpdateSalesOrderLoading }] =
    useUpdateSalesOrderMutation();
  const [linkSalesOrder] = useLinkSalesOrderMutation();
  const [showHsnSacModal, setShowHsnSacCodeModal] = useState(false);
  const [HsnOptions, setHsnOptions] = useState([]);
  const [showIgst, setShowIgst] = useState(false);
  const [itemsInput, setItemsInput] = useState({
    details: '',
    quantity: 0,
    cgst: 0,
    sgst: 0,
    igst: 0,
    rate: 0,
    amount: 0,
    UOM: '',
    discount: 0,
    totalAmount: 0,
    hsn: '',
  });
  const [subTotal, setSubTotal] = useState(0);
  const [updateDepOrder] = useUpdateOrderMutation();
  const [input, setInput] = useState({
    CustomerData: props?.CustomerData,
    customer: props?.customer || '',
    workOrders: props?.workOrders || [],
    salesOrderDate: props?.salesOrderDate?.split('T')[0],
    madeBy: props?.madeBy || '',
    quotationID: props?.quotationID || '',
    bankDetails: '',
    termsAndConditions: props?.termsAndConditions || '',
    salesOrderStatus: props.salesOrderStatus || 'pending',
    deliveryDate: props?.deliveryDate?.split('T')[0] || '',
    deliveryAddress: props?.edit
      ? [props?.deliveryAddress]
      : props?.defaultParam?.salesOrder?.deliveryAddress || '',
    billingAddress: props?.edit
      ? [props?.billingAddress]
      : props?.defaultParam?.salesOrder?.billingAddress || '',
    billingGST: props?.edit ? props?.billingGST : '',
    billingName: props?.edit ? props?.billingName : '',
    billingState: props?.edit ? props?.billingState : '',
    deliveryGST: props?.edit ? props?.deliveryGST : '',
    deliveryName: props?.edit ? props?.deliveryName : '',
    deliveryState: props?.edit ? props?.deliveryState : '',
    attachments: [],
    additionalFields: props?.edit ? props?.additionalFields : null,
    paymentTerm: props?.edit?.paymentTerm || '',
    deletedMedia: [],
    charges: {},
    businessDetails: {
      companyName: '',
      address: {},
      contact: '',
      gstNumber: '',
    },
    productColumnHideStatus: {},
    customColumnsHideStatus: {},
    bankDetailsHideStatus: false,
    termsAndConditionsHide: {},
    hideBillingAddress: false,
  });
  // product format
  const [tableFormatProducts, setTableFormatProducts] = useState([]);
  const [tableFormatsCharges, setTableFormatsCharges] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [chargesVisibility, setChargesVisibility] = useState({});
  const [displayFormat, setDisplayFormat] = useState(null);

  const [isSetAsDefault, setIsSetAsDefault] = useState(false);
  const [additionalFields, setAdditionalFields] = useState(null);
  const { defaults, state, dispatch } = useContext(Store);
  const [PaymentTermOptions, setPaymentTermOptions] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState(
    props.products ?? []
  );
  const { data: quotations } = useGetQuotationsQuery();
  const [allProductTemplates, setAllProductTemplates] = useState(
    props?.isEdit ? props?.allProductTemplates : []
  );
  const filtered = quotations?.filter((el) => {
    if (
      !defaults?.defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
        'salesOrder'
      )
    ) {
      return el?.quoteStatus.toLowerCase() === 'approved';
    }
    return el?.quoteStatus !== '';
  });
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const quotationData = filtered?.filter(
    (el) => !props.orders.some((order) => order.quotationID === el.quoteID)
  );
  const { data: dropdowns } = useGetDropdownsQuery();

  const [selectedQuotation, setSelectedQuotation] = useState(
    filtered?.find((el) => el?.quoteID === input?.quotationID)
  );
  const createdBy = state?.user?.name;
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [getSalesOrder, { data: allSalesOrder }] = useGetSalesOrderMutation();
  const [columnInputs, setColumnInputs] = useState([]);
  const [subtotalFormulaPrice, setSubtotalFormulaPrice] = useState(0);
  const pageSlug = '/salesordermanagement/orders';
  const [hidePoTable, setHidePoTable] = useState(() => {
    const savedValue = localStorage.getItem('hideProductDetailsObject');
    const parsedValue = savedValue ? JSON.parse(savedValue) : {};
    return parsedValue[pageSlug] || false;
  });
  const [additionalFieldsHideStatus, setAdditionalFieldsHideStatus] = useState(
    props?.additionalFieldsHideStatus || {}
  );

  const {
    addressSelector,
    bankDetailsSelector,
    contactSelector,
    emailSelector,
    profileSelector,
    companyDetails: data,
  } = useCompanyDetailsSelector(input, setInput);

  useEffect(() => {
    getTemplates({ path: '/salesordermanagement/orders' });
    getSalesOrder();
  }, [getTemplates, getSalesOrder]);

  const setBusinessDetails = useCallback(() => {
    const business = data;
    const address = business?.address?.find(
      (i) => i._id === input?.selectedDetails?.address
    );
    const contact = business?.contactNumber?.find(
      (i) => i._id === input?.selectedDetails?.contact
    );
    const email = business?.emailAddress?.find(
      (i) => i._id === input?.selectedDetails?.email
    );

    setInput((prev) => ({
      ...prev,
      businessDetails: {
        ...prev?.businessDetails,
        companyName: business?.name,
        address,
        contact: contact?.number,
        gstNumber: business?.gstNumber,
        email: email?.mail,
      },
    }));
  }, [
    input?.selectedDetails?.address,
    input?.selectedDetails?.contact,
    input?.selectedDetails?.email,
    data,
  ]);

  useEffect(() => {
    setBusinessDetails();
  }, [setBusinessDetails]);

  useEffect(() => {
    if (props?.isEdit || props?.isCopy) {
      setShowIgst(props?.showIgst || false);
      const allTemplates = props?.allProductTemplates;
      if (allTemplates?.length) {
        const productTemplates = allTemplates.filter((temp) =>
          props?.products?.some((product) => product?.value === temp.productId)
        );
        setAllProductTemplates(productTemplates);
      }
      const productsToSet = props?.products?.map((el) => {
        const matchedTemplate = allTemplates?.find(
          (temp) => temp?.productId === el?.value
        );
        return { ...(matchedTemplate || []), ...el };
      });
      setSelectedProducts(productsToSet);
      setInput((prev) => ({
        ...prev,
        charges: props?.charges,
        productTableFormat: props?.productTableFormat,
        productChargesFromFormat: props?.productChargesFromFormat,
        productDetailsFromFormat: props?.productDetailsFromFormat,
        productTableColumnHideStatus: props?.productTableColumnHideStatus,
        productTableChargesHideStatus: props?.productTableChargesHideStatus,
      }));
    }
  }, [props]);

  useEffect(() => {
    const setIdFormatFunc = () => {
      if (allSalesOrder?.length === 0) {
        if (templatesData) {
          const defaultTemplate = templatesData?.find((template) =>
            template.name.startsWith('Default')
          );
          setAdditionalFields(defaultTemplate);
          setSelectedTemplate(defaultTemplate);
        }
      } else {
        const templateParamsId =
          searchParams.get('templateId') === 'undefined'
            ? null
            : searchParams.get('templateId');
        if (allSalesOrder) {
          const lastEntry = allSalesOrder[allSalesOrder?.length - 1];
          const templateToUse = templatesData?.find((template) => {
            return (
              template?._id ===
              (templateParamsId
                ? templateParamsId
                : lastEntry?.additionalFields?._id)
            );
          });
          setSelectedTemplate(templateToUse);
          setAdditionalFields(templateToUse);
        }
      }
    };
    if (props?.edit && !props?.isCopy) {
      const templateToUse = templatesData?.find((template) => {
        return template?._id === props?.additionalFields?._id;
      });
      if (templateToUse) {
        setSelectedTemplate(templateToUse);
        setAdditionalFields(props?.additionalFields);
      }
      return;
    } else if (props?.isCopy) {
      setIdFormatFunc();
    } else {
      setIdFormatFunc();
    }
  }, [
    searchParams,
    allSalesOrder,
    defaults?.defaultParam?.prefixIds?.salesOrderId,
    props?.additionalFields,
    props?.edit,
    templatesData,
    props?.isCopy,
  ]);

  useEffect(() => {
    if (allAdditionalCharges) {
      const charges = allAdditionalCharges.filter(
        (charge) => charge.pageSlug === '/salesordermanagement/orders'
      );
      if (charges.length > 0) {
        setAdditionalCharges(charges[0]);
        const newCharges = charges[0]?.additionalCharges?.reduce(
          (acc, charge) => {
            acc[charge.name] = 0;
            return acc;
          },
          {}
        );
        if (newCharges) {
          setInput((prev) => ({
            ...prev,
            charges: { ...prev.charges, ...newCharges },
          }));
        }
      }
    }
  }, [allAdditionalCharges]);

  useEffect(() => {
    if (!dropdowns) return;
    const PaymentTerm = dropdowns?.dropdowns?.find(
      (dropdown) => dropdown?.name === 'Payment Term'
    );
    const hsn = dropdowns?.dropdowns?.find(
      (dropdown) => dropdown?.name === 'Hsn Code'
    );
    setPaymentTermOptions(PaymentTerm?.values);
    setHsnOptions(hsn?.values);
  }, [dropdowns]);
  useEffect(() => {
    if (props?.edit || props?.isCopy) {
      setAdditionalFieldsHideStatus(props?.additionalFieldsHideStatus || {});
    } else if (latestSo?.additionalFieldsHideStatus) {
      setAdditionalFieldsHideStatus(latestSo?.additionalFieldsHideStatus || {});
    }
  }, [
    props?.edit,
    props?.isCopy,
    latestSo?.additionalFieldsHideStatus,
    props?.additionalFieldsHideStatus,
  ]);

  useEffect(() => {
    (async () => {
      const orderId = searchParams.get('orderId');
      if (orderId) {
        const orders = await getAllOrders();
        const currentOrder = orders?.data?.find(
          (elem) => elem?._id === orderId
        );
        const quotation = currentOrder?.steps?.find(
          (elem) => elem?.stepPage === 'Quotation'
        );
        if (quotation?.data) {
          setSelectedTermAndCondition(quotation.data.termsAndConditions);
          setPdf(quotation.data.attachments);
          setInput((prev) => ({
            ...prev,
            quotationID: quotation.data.quoteID,
            termsAndConditions: quotation.data.termsAndConditions,
            attachments: quotation.data.attachments,
          }));
          setItems((prev) => {
            const updated = [...prev];
            quotation.data.productDetails?.forEach((product, i) => {
              updated[i] = {
                ...updated[i],
                hsn: product?.HsnSacCode,
                details: product?.productName,
                attachments: product?.attachments,
                UOM: product?.uom,
                quantity: product?.quantity,
                rate: product?.rate,
                discount: product?.discount,
                amount: product?.amount,
                sgst: product?.sgst,
                cgst: product?.cgst,
                igst: product?.igst,
                remarks: product?.remarks,
                customColumns: product?.customColumns,
              };
            });
            return updated;
          });
        }
      }
    })();
  }, [searchParams, getAllOrders]);

  useEffect(() => {
    const bankDetails = data?.bankDetails?.find(
      (i) => i._id === input?.selectedDetails?.bank
    );

    setInput((prev) => {
      if (!props.isEdit && latestSo) {
        const {
          productColumnHideStatus,
          customColumnsHideStatus,
          bankDetailsHideStatus,
          termsAndConditionsHide,
        } = latestSo;
        return {
          ...prev,
          bankDetails: {
            accountHolder: bankDetails?.accountHolder || '',
            accountNumber: bankDetails?.accountNumber,
            bankName: bankDetails?.bankName,
            ifsc: bankDetails?.ifsc,
            bankAddress: bankDetails?.bankAddress,
            upiId: bankDetails?.upiId || '',
          },
          customColumnsHideStatus: customColumnsHideStatus || {},
          productColumnHideStatus: productColumnHideStatus || {},
          termsAndConditionsHide: termsAndConditionsHide || {},
          bankDetailsHideStatus: bankDetailsHideStatus || false,
        };
      }
      return {
        ...prev,
        bankDetails: props.bankDetails || {},
        customColumnsHideStatus: props?.customColumnsHideStatus || {},
        productColumnHideStatus: props?.productColumnHideStatus || {},
        termsAndConditionsHide: props?.termsAndConditionsHide || {},
        bankDetailsHideStatus: props?.bankDetailsHideStatus || false,
      };
    });
  }, [
    data?.bankDetails,
    input?.selectedDetails?.bank,
    latestSo,
    props.isEdit,
    props.bankDetails,
    props?.customColumnsHideStatus,
    props?.productColumnHideStatus,
    props?.termsAndConditionsHide,
    props?.bankDetailsHideStatus,
  ]);

  useEffect(() => {
    if (!props.edit && selectedQuotation) {
      const allProductTemplates = selectedQuotation?.allProductTemplates;
      const products = selectedQuotation?.productDetails || [];

      if (allProductTemplates?.length) {
        const productTemplates = allProductTemplates.filter((temp) =>
          products.some((product) => product?._id === temp.productId)
        );
        setAllProductTemplates(productTemplates);
      }

      setItems(
        products.map((el) => {
          const matchedTemplate = allProductTemplates?.find(
            (temp) => temp?.productId === el?._id
          );
          return {
            ...(matchedTemplate || []),
            details: el?.productName,
            attachments: el?.attachments,
            UOM: el?.uom,
            cgst: parseInt(el?.cgst),
            sgst: parseInt(el?.sgst),
            igst: parseInt(el?.igst),
            quantity: el?.quantity,
            rate: el?.rate,
            amount: el?.amount,
            discount: el?.discount,
            totalAmount: el?.totalAmount,
            hsn: el?.HsnSacCode,
            value: el?._id,
            customColumns: el?.customColumns,
          };
        })
      );

      const termsAndConditions =
        selectedQuotation?.termsAndConditions?.map((tc) => ({
          label: tc.terms,
          value: { description: tc?.description, terms: tc?.terms, page: [] },
        })) || [];

      setSelectedTermAndCondition(termsAndConditions);

      const matchingCharges = {};
      const quotationCharges = selectedQuotation?.charges || {};
      additionalCharges?.additionalCharges?.forEach((charge) => {
        if (quotationCharges?.[charge?.name]?.type === charge?.type) {
          matchingCharges[charge.name] = quotationCharges[charge.name];
        }
      });

      setInput((prev) => ({
        ...prev,
        customer: selectedQuotation?.vendorDetails?.name,
        CustomerData: selectedQuotation?.vendorDetails,
        deliveryAddress: selectedQuotation?.vendorDetails?.address || [],
        billingAddress: selectedQuotation?.vendorDetails?.billingAddress?.length
          ? selectedQuotation.vendorDetails.billingAddress
          : selectedQuotation?.vendorDetails?.address || [],
        billingGST: selectedQuotation?.vendorDetails?.gstNumber || [],
        billingName: selectedQuotation?.vendorDetails?.companyName || '',
        billingState: '',
        charges: matchingCharges,
        termsAndConditions,
      }));

      const newlyAddedFiles = selectedQuotation?.attachments?.map((item) => ({
        name: item.name,
        type: item.type,
        data: item.data,
      }));
      setPdf(newlyAddedFiles || []);
    }
  }, [selectedQuotation, props.edit, additionalCharges?.additionalCharges]);

  useEffect(() => {
    setSelectedQuotation(
      quotationData?.find((el) => el?.quoteID === input?.quotationID)
    );
  }, [input.quotationID, quotationData]);

  useEffect(() => {
    (async () => {
      let productTotal = 0;

      for (const item of selectedProducts) {
        const amt =
          parseInt(item?.quantity) *
          parseInt(item?.rate) *
          (1 - (parseInt(item?.discount) || 0) / 100);
        const price = await calculateTotalPrice(
          item?.cgst,
          item?.sgst,
          item?.igst,
          amt
        );
        productTotal += +price?.price;
      }

      for (const item of items) {
        const price = await calculateTotalPrice(
          item?.cgst || 0,
          item?.sgst || 0,
          item?.igst || 0,
          item?.amount
        );
        productTotal += +price?.price;
      }

      setItemsInput({
        details: '',
        quantity: '',
        rate: '',
        UOM: '',
        cgst: 0,
        sgst: 0,
        igst: 0,
        amount: 0,
        discount: '',
        totalAmount: 0,
      });
      setSubTotal(productTotal);
    })();
  }, [items, selectedProducts]);

  useEffect(() => {
    const amount = +itemsInput?.quantity * +itemsInput?.rate;
    const total = amount - amount * (+itemsInput?.discount / 100);
    setItemsInput((prevState) => ({
      ...prevState,
      amount: total,
      totalAmount:
        total +
        total * (+itemsInput?.cgst / 100) +
        calculateTaxAmount(total, itemsInput?.sgst) +
        calculateTaxAmount(total, itemsInput.sgst),
    }));
  }, [
    itemsInput?.cgst,
    itemsInput?.discount,
    itemsInput?.quantity,
    itemsInput?.rate,
    itemsInput.sgst,
  ]);

  const inputChangeHandler = (e) => {
    setInput((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const inputChangeHandle = (e) => {
    setInput((prev) => ({
      ...prev,
      bankDetails: { ...prev.bankDetails, [e.target.name]: e.target.value },
    }));
  };

  const copyAddress = (e) => {
    setInput((prev) => ({
      ...prev,
      deliveryAddress: e.target.checked ? prev.billingAddress : '',
    }));
  };

  const itemsChangeHandler = (e, index) => {
    const { name, value } = e.target;

    if (['quantity', 'rate', 'discount'].includes(name) && value < 0) {
      return toast.error(`${name} cannot be negative`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Quantity, Rate and Discount cannot be negative',
      });
    }

    if (name === 'discount' && value > 100) {
      return toast.error('Discount cannot be greater than 100', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Discount cannot be greater than 100',
      });
    }

    setItems((prev) =>
      prev.map((item, idx) => {
        if (index !== idx) return item;

        const { name, value } = e.target;

        if (name === 'quantity') {
          const discountedPrice =
            (+value * (item?.rate || 0) * (item.discount || 0)) / 100;
          const amount = +value * (item?.rate || 0) - discountedPrice;
          let totalAmount = amount;
          if (!isNaN(item.gst)) totalAmount += (amount * +item?.gst) / 100;
          return { ...item, [name]: parseFloat(value), amount, totalAmount };
        }

        if (name === 'rate') {
          const discountedAmount =
            (parseFloat(item?.quantity) *
              parseFloat(value || 0) *
              +item?.discount) /
            100;
          const amount = +item.quantity * +value - discountedAmount;
          let totalAmount = amount;
          if (!isNaN(item.gst)) totalAmount += (amount * +item?.gst) / 100;
          return {
            ...item,
            [name]: parseFloat(value || 0),
            amount,
            totalAmount,
          };
        }

        if (name === 'discount') {
          const total = item?.quantity * item?.rate * (+value / 100);
          const amount = item?.quantity * item?.rate - total;
          return { ...item, discount: +value, amount, totalAmount: amount };
        }

        if (name === 'hsn') {
          if (value === '+') {
            setShowHsnSacCodeModal(true);
            return item;
          }
          return { ...item, hsn: value };
        }

        return { ...item, [name]: value };
      })
    );
  };

  const removeItem = (el) => {
    setItems(items.filter((item) => item?.details !== el?.details));
  };

  const linkSalesOrderToWorkorder = async (workorderid, salesOrder) => {
    await linkSalesOrder({
      id: workorderid,
      data: { salesOrder: salesOrder },
    });
  };

  const [updateNcr] = useUpdateNcrMutation();

  const itemsFormulaHandler = (_products) => {
    let transformedItems = [];

    // Combine the formData _products and items into a single array
    const allProducts = [...(_products || []), ...(items || [])];

    for (let item of allProducts) {
      let temp = { ...item };

      if (item?.customColumns) {
        for (let columnInput of columnInputs) {
          if (columnInput?.formula) {
            let val = {};
            let formula = columnInput?.formula?.substring(
              columnInput?.formula?.indexOf('{') + 1,
              columnInput?.formula?.indexOf('}')
            );
            formula = formula.trim();
            formula = formula + ' ';

            while (formula?.indexOf('$') !== -1) {
              let key = formula?.substring(
                formula?.indexOf('$') + 1,
                formula?.indexOf(' ')
              );
              let stringRegex = /^[A-Za-z]+$/;
              if (stringRegex.test(key)) {
                val = {
                  ...val,
                  [key]:
                    item?.[key] || parseFloat(item?.customColumns?.[key]) || 0,
                };
              }
              formula = formula?.substring(formula?.indexOf(' ') + 1);
            }
            const allFields = [
              {
                name: 'cgst',
                percentOf: 'amount',
              },
              {
                name: 'sgst',
                percentOf: 'amount',
              },
              {
                name: 'igst',
                percentOf: 'amount',
              },
              {
                name: 'discount',
                percentOf: 'amount',
              },
            ];
            let res = handleFormula(columnInput, val, allFields);

            // Add or update the customColumns field with the calculated value
            temp.customColumns = {
              ...temp.customColumns,
              [columnInput?.columnName]: res,
            };
          }
        }
      }

      transformedItems.push(temp); // Push the transformed product details into the array
    }
    return transformedItems;
  };
  const submitHandler = async (e, bool) => {
    try {
      setIsSetAsDefault(true);
      if (e) {
        e.preventDefault();
      }

      if (input?.shipping < 0) {
        toast.error(`Shipping cannot be negative`, {
          theme: 'colored',
          position: 'top-right',
          toastId: 'Discount and Shipping cannot be negative',
        });
        return;
      }
      let _products = [];
      for (let item of selectedProducts) {
        let obj = { ...item };
        if (item?.quantity && item?.rate) {
          let amt =
            parseInt(item?.quantity) *
            parseInt(item?.rate) *
            (1 - (parseInt(item?.discount) || 0) / 100);
          const { price } = calculateTotalPrice(
            item?.cgst,
            item?.sgst,
            item?.igst,
            item?.amount
          );
          obj = {
            ...obj,
            totalAmount: price,
            amount: amt,
          };
        }
        _products?.push(obj);
      }
      _products = itemsFormulaHandler(_products);

      // const _products = items.map((item) => {
      //   if (item?.quantity && item?.rate) {
      //     let amt = parseInt(item?.quantity) * parseInt(item?.rate) * (1 - (parseInt(item?.discount) || 0) / 100);
      //     const { price } = calculateTotalPrice(
      //       item?.cgst,
      //       item?.sgst,
      //       item?.igst,
      //       item?.amount
      //     );
      //     return {
      //       ...item,
      //       totalAmount: price,
      //       amount: amt
      //     };
      //   }
      // });
      if (!displayFormat) {
        if (items?.length === 0 && selectedProducts?.length === 0) {
          return toast.error('Please Select Atleast One Product');
        }
        for (let i = 0; i < items?.length; i++) {
          if (!items[i]?.details) {
            return toast.error('Please Enter Product Details');
          }
        }
        for (let i = 0; i < selectedProducts?.length; i++) {
          if (!selectedProducts[i]?.details) {
            return toast.error('Please Enter Product Details');
          }
        }
      }
      const updatedTerms = selectedTermAndCondition?.map((item) => item?.value);
      const hasUndefinedValues = updatedTerms.some(
        (value) => value === undefined
      );
      const billingGST = Array.isArray(input.billingGST)
        ? input.billingGST[0]
        : input.billingGST;
      let data = {
        ...input,
        billingGST,
        charges: {
          ...input?.charges,
          subTotal,
          total:
            subTotal +
              input?.charges?.shipping +
              input?.charges?.packaging +
              input?.charges?.installation +
              input?.charges?.shippingTax +
              input?.charges?.packagingTax +
              input?.charges?.installationTax || 0,
        },
        deliveryAddress:
          typeof input?.deliveryAddress === 'string'
            ? input.deliveryAddress
            : input?.deliveryAddress?.[0],
        billingAddress:
          typeof input?.billingAddress === 'string'
            ? input.billingAddress
            : input?.billingAddress?.[0],
        products: defaults?.defaultParam?.projectDefaults
          ?.showProductFormatTable
          ? formatSalesOrderProducts(tableFormatProducts)
          : _products,
        items: _products?.length > 0 ? [] : items,
        salesOrderDate: new Date().toISOString(),
        files: pdf,
        additionalFields: additionalFields,
        createdBy,
        termsAndConditions: hasUndefinedValues
          ? selectedTermAndCondition
          : updatedTerms,
        ncrId: searchParams.get('ncrId'),
        showIgst,
        subTotal: subtotalFormulaPrice,
        allProductTemplates,
        additionalFieldsHideStatus,
        productTableFormat: displayFormat?._id,
        productDetailsFromFormat: tableFormatProducts,
        productChargesFromFormat: tableFormatsCharges,
        productTableChargesHideStatus: chargesVisibility,
        productTableColumnHideStatus: columnVisibility,
        productTableFormatHideStatus:
          defaults?.defaultParam?.projectDefaults?.showProductFormatTable,
        salesOrderStatus:
          input?.salesOrderStatus === 'rejected'
            ? 'pending'
            : input?.salesOrderStatus,
      };

      if (defaults?.defaultParam?.projectDefaults?.salesVoucherAutoCreate) {
        data = {
          ...data,
          idDataVoucher: idCompData?.dataToReturn,
        };
      }

      const sendObj = {
        data,
        files: pdf,
      };
      let res = {};
      if (props.edit && !props.isCopy) {
        sendObj.id = props._id;
        res = await updateOrder(sendObj);
      } else {
        res = await createOrder(sendObj);
        // if (res?.error == null)
        //   await updateDefaults({ salesOrderId: props?.newId + 1 });
      }

      if (res.data) {
        toast.success(
          props.edit && !props?.isCopy
            ? `Sales Order Successfully Edited`
            : props?.isCopy
              ? `Sales Order Copied SuccessFully`
              : `Sales Order Created Successfully`,
          {
            theme: 'colored',
            position: 'top-right',
            toastId: 'Sales Order Created Successfully',
          }
        );

        for (let i = 0; i < input?.workOrders?.length; i++) {
          await linkSalesOrderToWorkorder(
            input?.workOrders[i]?.value,
            res?.data?._id
          );
        }

        const kanban = searchParams.get('kanban') === 'true';
        const orderId = searchParams.get('orderId');
        const allProducts = _products?.map((product) => product?.details);
        const kanbanFilterObj = {
          customer_name: input?.CustomerData?.name,
          company_name: input?.CustomerData?.company_name,
          product_details: allProducts,
        };

        const navigateParams = {
          department: searchParams.get('department'),
          id: res?.data?._id,
          refType: searchParams.get('refType'),
          page: searchParams.get('page'),
          taskId: searchParams.get('taskId'),
          orderId,
          index: searchParams.get('index'),
          idIndex: additionalFields?.idIndex,
          filterDetails: JSON.stringify(kanbanFilterObj),
        };
        if (!kanban) {
          let obj = {
            objRef: res?.data?._id,
            currentDepartment: 'sales',
            refKey: 'SalesOrder',
            currentPage: 'Sales Order',
            userId: state?.user?._id,
            filterDetails: JSON.stringify(kanbanFilterObj),
          };
          if (props.edit && !props?.isCopy) {
            if (res?.data?.taskId) {
              await updateDepOrder({
                data: {
                  ...obj,
                  id: res?.data?.taskId,
                },
              });
            }
          } else {
            await createDepOrder({
              data: obj,
            });
          }

          if (searchParams.get('ncrId')) {
            const ncrs = props.ncr;
            const ncrToUpdate = ncrs.find(
              (ncr) => ncr._id === searchParams.get('ncrId')
            );
            if (ncrToUpdate) {
              await updateNcr({
                data: {
                  ...ncrToUpdate,
                  checkedOrder: {
                    orderId: res?.data?._id,
                  },
                },
                id: ncrToUpdate._id,
                orderCheck: false,
              });
            }
          }
        }

        if (kanban) {
          let time = new Date();
          dispatch({
            type: 'ADD_CARD',
            payload: {
              data: {
                taskId: searchParams.get('taskId'),
                firstStepId: res?.data?.salesOrderID,
                stepPage: 'Sales Order',
                updatedAt: time?.toDateString(),
                filterDetails: kanbanFilterObj,
              },
              currentColumn: 'Sales Order',
            },
          });
        }

        const filteredParams = Object.fromEntries(
          Object.entries(navigateParams).filter(([_, value]) => value !== null)
        );

        const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;

        if (kanban) {
          navigate(navigateStr);
        } else if (searchParams.get('navigateTo')) {
          navigate(searchParams.get('navigateTo'));
        } else {
          navigate('/salesordermanagement/orders');
          props?.setIsAdd(false);
          props?.setIsEdit(false);
        }

        if (bool) {
          props.modalHandler(false);
        }
        return true;
      } else {
        if (props.edit) {
          toast.error(
            props.edit
              ? `Error in editing Sales Order`
              : `Error in creating Sales Order`,
            {
              theme: 'colored',
              position: 'top-right',
              toastId: 'Error in creating Sales Order',
            }
          );
        }
        return false;
      }
    } catch (error) {
      toast.error(
        error?.data?.message || error?.message || 'Something went wrong'
      );
    }
  };

  const shareAndSubmitHandler = async (e) => {
    e.preventDefault();
    if (!input.bankDetails) {
      toast.error(`Please Enter Bank Details`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'All fields are required',
      });
      return false;
    }
    if (!input.termsAndConditions) {
      toast.error(`Please Enter Terms and Conditions`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'All fields are required',
      });
      return false;
    }
  };
  return (
    <>
      {showHsnSacModal && (
        <HsnSacModal
          HsnOptions={HsnOptions}
          setShowModal={setShowHsnSacCodeModal}
          dropdowns={dropdowns}
        />
      )}
      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-7xl">
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                props?.setIsAdd(false);
                props?.setIsEdit(false);
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {props?.edit ? 'Edit' : 'Create'} Sales Order
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {props?.edit
                  ? 'Update sales order information'
                  : 'Create a new sales order'}
              </p>
            </div>
          </div>
        </div>
        <div>
          <div className="flex flex-col justify-between h-full">
            <>
              <OrderDetails
                isMobile={props.isMobile}
                isTablet={props.isTablet}
                input={input}
                setInput={setInput}
                inputChangeHandler={inputChangeHandler}
                isEdit={props.edit}
                isCopy={props.isCopy}
                setItemsInput={setItems}
                quotationData={props.edit ? filtered : quotationData}
                selectedQuotation={selectedQuotation}
                setSelectedQuotation={setSelectedQuotation}
                setIsDefaultDeliveryAddress={setIsDefaultDeliveryAddress}
                isDefaultDeliveryAddress={isDefaultDeliveryAddress}
                setIsDefaultBillingAddress={setIsDefaultBillingAddress}
                isDefaultBillingAddress={isDefaultBillingAddress}
                copyAddress={copyAddress}
                paymentTermOptions={PaymentTermOptions}
                additionalFields={additionalFields}
                setAdditionalFields={setAdditionalFields}
                setSelectedTemplate={setSelectedTemplate}
                selectedTemplate={selectedTemplate}
                templatesData={templatesData}
                orders={props?.orders}
                Searchparams={searchParams}
                defaults={defaults}
                addressSelector={addressSelector}
                contactSelector={contactSelector}
                emailSelector={emailSelector}
                profileSelector={profileSelector}
                salesOrderID={props?.salesOrderID}
                voucherID={props?.voucherID}
                additionalFieldsHideStatus={additionalFieldsHideStatus}
                setAdditionalFieldsHideStatus={setAdditionalFieldsHideStatus}
                IdGenCompVoucher={IdGenComp}
                idCompDataVoucher={idCompData}
              />
              {defaults?.defaultParam?.projectDefaults
                ?.showProductFormatTable ? (
                <ProductFormatManager
                  input={tableFormatProducts}
                  setInput={setTableFormatProducts}
                  charges={tableFormatsCharges}
                  setCharges={setTableFormatsCharges}
                  columnVisibility={columnVisibility}
                  setColumnVisibility={setColumnVisibility}
                  chargesVisibility={chargesVisibility}
                  setChargesVisibility={setChargesVisibility}
                  displayFormat={displayFormat}
                  setDisplayFormat={setDisplayFormat}
                  isEdit={props?.isEdit}
                  isCopy={props?.isCopy}
                  data={selectedQuotation ? selectedQuotation : input}
                  latestSo={latestSo}
                />
              ) : (
                <>
                  <ProductTable
                    isMobile={props.isMobile}
                    isTablet={props.isTablet}
                    items={items}
                    setPdf={setPdf}
                    removeItemHandler={removeItem}
                    itemsInput={itemsInput}
                    itemsChangeHandler={itemsChangeHandler}
                    setItems={setItems}
                    selectedProducts={selectedProducts}
                    setSelectedProducts={setSelectedProducts}
                    HsnOptions={HsnOptions}
                    showIgst={showIgst}
                    setColumnInputs={setColumnInputs}
                    setShowIgst={setShowIgst}
                    columnInputs={columnInputs}
                    input={input}
                    setInput={setInput}
                    setOpenAddUomModal={setOpenAddUomModal}
                    openAddUomModal={openAddUomModal}
                    hidePoTable={hidePoTable}
                    setHidePoTable={setHidePoTable}
                    pageSlug={pageSlug}
                    allProductTemplates={allProductTemplates}
                    setAllProductTemplates={setAllProductTemplates}
                    isEdit={props?.isEdit}
                  />
                  <Charges
                    isMobile={props.isMobile}
                    isTablet={props.isTablet}
                    formData={{ ...input, productDetails: items }}
                    setFormData={setInput}
                    totalPrice={subTotal}
                    additionalFields={additionalFields}
                    setAdditionalFields={setAdditionalFields}
                    additionalCharges={additionalCharges}
                    selectedProducts={selectedProducts}
                    pageSlug={`/salesordermanagement/orders`}
                    setSubtotalFormulaPrice={setSubtotalFormulaPrice}
                    hidePoTable={hidePoTable}
                    showIgst={showIgst}
                  />
                </>
              )}

              <Attachments
                isMobile={props.isMobile}
                isTablet={props.isTablet}
                pdf={pdf}
                setPdf={setPdf}
                input={input}
                setInput={setInput}
                inputChangeHandle={inputChangeHandle}
                inputChangeHandler={inputChangeHandler}
                edit={props.edit}
                shareAndSubmitHandler={shareAndSubmitHandler}
                setIsDefaultTermsAndConditions={setIsDefaultTermsAndConditions}
                isDefaultTermsAndConditions={isDefaultTermsAndConditions}
                setIsDefaultBankDetails={setIsDefaultBankDetails}
                isDefaultBankDetails={isDefaultBankDetails}
                selectedTermAndCondition={selectedTermAndCondition}
                setSelectedTermAndCondition={setSelectedTermAndCondition}
                bankDetailsSelector={bankDetailsSelector}
                isSetAsDefault={isSetAsDefault}
                page={['SO']}
              />
            </>
          </div>
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100  mt-4">
            <div className="flex items-center justify-end">
              <Button
                isLoading={
                  props?.isAdd || props?.isCopy
                    ? isCreateSalesOrderLoading
                    : isUpdateSalesOrderLoading
                }
                onClick={submitHandler}
                type="primary"
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SoForm;
