import { forwardRef } from 'react';
import useHeaderAndFooter from '../../hooks/useHeaderAndFooter';

/* eslint-disable */
const Report = forwardRef((props, ref) => {
  /* eslint-enable */
  const { header, footer } = useHeaderAndFooter({});

  const { reportData } = props;
  return (
    <>
      <div id="print-only">
        {header()}
        <div
          className="w-screen relative z-[-1] my-10 mx-2 grid grid-cols-3 items-center justify-between"
          style={{ marginTop: '10px' }}
        >
          <div className="flex items-center justify-center gap-1">
            <p>Work Order: </p>
            <p>{props.selectedPo}</p>
          </div>
          <div className="flex items-center justify-center gap-1">
            <p>Job: </p>
            <p>{props.selectedOptions}</p>
          </div>
          <div className="flex items-center justify-center gap-1">
            <p>Batch: </p>
            <p>{props.selectedBatch}</p>
          </div>
        </div>
        <table
          className="w-screen relative z-[-1]"
          align="center"
          id="margin-10"
        >
          <thead className="w-auto">
            <tr className="w-auto">
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Process Name</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Process Type</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Start Date</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>End Date</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Count/Pass</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Error/Scrap</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Speed/Rework</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Machine</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Uptime</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Downtime</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Status</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Workers</b>
              </th>
              <th className="font-medium min-w-[100px] border border-solid border-black">
                <b>Leading/Lagging</b>
              </th>
            </tr>
          </thead>
          <tbody className="w-auto">
            {reportData.map((process, pIdx) => {
              if (process.count !== undefined) {
                return (
                  <tr className="w-auto" key={pIdx}>
                    <td className="border border-solid border-black text-center">
                      {process.processName}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.processType}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.start}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.expected}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.processType !== 'QC'
                        ? process.count
                        : process.pass || 0}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.processType !== 'QC'
                        ? process.count
                        : process.scrap || 0}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.processType !== 'QC'
                        ? process.count
                        : process.rework || 0}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.machine}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.uptime}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.downtime}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.status}
                    </td>
                    <td className="border border-solid border-black text-center">
                      {process.worker}
                    </td>
                    <td className="border border-solid border-black text-center">
                      <b>
                        {process.leadingOrlagging.includes('undefined')
                          ? '-'
                          : process.leadingOrlagging}
                      </b>
                    </td>
                  </tr>
                );
              }
            })}
          </tbody>
        </table>
      </div>
      {footer()}
    </>
  );
});

export default Report;
