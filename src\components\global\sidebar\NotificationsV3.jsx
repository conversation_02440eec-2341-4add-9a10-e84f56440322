import { BellOutlined } from '@ant-design/icons';
import { Badge } from 'antd';
import {
  AlertCircle,
  AlertTriangle,
  Bell,
  Check,
  CheckCircle,
  Info,
  RefreshCw,
  Trash2,
  X,
} from 'lucide-react';
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import addNotification from 'react-push-notification';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useNotifications } from '../../../hooks/useNotifications';
import {
  useClearAllNewNotficationMutation,
  useDeleteNewNotificationMutation,
  useGetAllUnreadNotificationsLengthQuery,
  useGetNewNotificationQuery,
  useMarkAllAsReadNewNotficationMutation,
} from '../../../slices/newNotificationApiSlice';
import { Store } from '../../../store/Store';

export const formatMessage = (notification) => {
  let messageArray = [];
  let message = '';
  if (notification?.title !== 'Low Stock') {
    message = notification?.description || notification?.message;
    return { message, messageArray, isLowStock: false };
  }

  let notficationArr = notification?.description?.split(':');

  for (let i = 0; i < notficationArr.length; i += 3) {
    notficationArr[i] = notficationArr[i].split('-');
    messageArray.push({
      name: notficationArr[i][0],
      availableQuantity: notficationArr[i + 1],
      requiredQuantity: notficationArr[i + 2],
    });
  }

  message =
    messageArray[0]?.name +
    ' (' +
    messageArray[0]?.availableQuantity +
    '/' +
    messageArray[0]?.requiredQuantity +
    ')';
  if (messageArray.length > 1) {
    message += ` and ${messageArray.length - 1} more`;
  }

  return { message, messageArray, isLowStock: true };
};

const CustomNotificationToast = ({ title, desc }) => (
  <div className="space-y-1">
    <div className="font-medium text-white border-b border-white/20 pb-1">
      {title}
    </div>
    <div className="text-sm text-white/90">{desc}</div>
  </div>
);

const NotificationItem = ({ notification, onMarkAsRead, onDelete }) => {
  const formatTime = (timestamp) => {
    const diff = Date.now() - new Date(timestamp).getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    if (days > 0) return `${days}d`;
    if (hours > 0) return `${hours}h`;
    return `${minutes}m`;
  };

  const getIconAndColor = (type) => {
    const configs = {
      success: { icon: CheckCircle, color: 'text-emerald-500' },
      warning: { icon: AlertTriangle, color: 'text-amber-500' },
      error: { icon: AlertCircle, color: 'text-red-500' },
      info: { icon: Info, color: 'text-blue-500' },
    };
    return configs[type] || { icon: Bell, color: 'text-slate-500' };
  };

  const { icon: Icon, color } = getIconAndColor(notification.type);

  return (
    <div
      className={`group p-4 border-b border-slate-100 hover:bg-slate-50 transition-all duration-200 ${!notification.read ? 'bg-blue-50/40 border-l-4 border-l-blue-500' : ''}`}
    >
      <div className="flex gap-3">
        <div className={`mt-0.5 ${color}`}>
          <Icon size={18} />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <h4 className="font-medium text-slate-900 text-sm leading-snug">
              {notification.title}
            </h4>
            <div className="flex items-center gap-2">
              <span className="text-xs text-slate-500 whitespace-nowrap">
                {formatTime(notification.createdAt)}
              </span>
              {!notification.read && (
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
              )}
            </div>
          </div>
          <p className="text-sm text-slate-600 mt-1 line-clamp-2">
            {formatMessage(notification)?.message || ''}
            {/* {notification.description || notification.message} */}
          </p>
        </div>
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {!notification.read && (
            <button
              onClick={() => onMarkAsRead(notification._id)}
              className="p-1.5 text-slate-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
              title="Mark as read"
            >
              <Check size={14} />
            </button>
          )}
          <button
            onClick={() => onDelete(notification._id)}
            className="p-1.5 text-slate-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
            title="Delete"
          >
            <Trash2 size={14} />
          </button>
        </div>
      </div>
    </div>
  );
};

const TabButton = ({ label, count, active, onClick }) => (
  <button
    onClick={onClick}
    className={`relative flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-all ${
      active
        ? 'bg-slate-100 text-slate-900 shadow-sm'
        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
    }`}
  >
    <span>{label}</span>
    {count > 0 && (
      <span
        className={`px-2 py-0.5 text-xs font-medium rounded-full ${active ? 'bg-slate-200 text-slate-700' : 'bg-slate-100 text-slate-600'}`}
      >
        {count > 99 ? '99+' : count}
      </span>
    )}
  </button>
);

const NotificationV3 = ({ showSideBar }) => {
  const { socket, state } = useContext(Store);
  const { showNotification, permission } = useNotifications();
  const dropdownRef = useRef(null);
  const hasShownInitialNotifications = useRef(false);
  const notificationPermissionRequested = useRef(false);
  const [isOpen, setIsOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(5);
  const [selectedTab, setSelectedTab] = useState('unread');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const navigate = useNavigate();

  const [markAllAsReadNewNotification] =
    useMarkAllAsReadNewNotficationMutation();
  const [clearAllNewNotification] = useClearAllNewNotficationMutation();
  const [deleteSingleNotification] = useDeleteNewNotificationMutation();
  const { data: unreadNotificationsLength, refetch: refetchUnreadCount } =
    useGetAllUnreadNotificationsLengthQuery();
  const {
    data: newNotificationsData,
    refetch: refetchNewNotification,
    isLoading,
  } = useGetNewNotificationQuery({
    page,
    limit,
    type: selectedTab === 'unread' ? 'unRead' : 'read',
  });

  const realTimeUser = state?.user;
  const unreadCount = unreadNotificationsLength?.count || 0;
  const currentNotifications = useMemo(
    () => newNotificationsData?.results || [],
    [newNotificationsData?.results]
  );

  const totalCount = newNotificationsData?.totalResults || 0;
  const readCount = totalCount - unreadCount;
  const hasMore = currentNotifications.length < totalCount;

  const loadMore = useCallback(() => {
    if (!hasMore || isLoading) return;
    setLimit((prev) => prev + 10);
  }, [hasMore, isLoading]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const triggerNotificationInSystem = useCallback(
    (message, title, options = {}) => {
      showNotification(title, { body: message, ...options });
    },
    [showNotification]
  );

  const showUnreadNotificationsOnLoad = useCallback(async () => {
    if (
      hasShownInitialNotifications.current ||
      !realTimeUser ||
      permission !== 'granted'
    )
      return;
    try {
      if (currentNotifications.length > 0 && selectedTab === 'unread') {
        triggerNotificationInSystem(
          `You have ${currentNotifications.length} unread notification${currentNotifications.length > 1 ? 's' : ''}`,
          'Unread Notifications',
          { tag: 'unread-summary' }
        );
        currentNotifications.slice(0, 3).forEach((notif, index) => {
          setTimeout(
            () => {
              triggerNotificationInSystem(
                notif.description || notif.message,
                notif.title,
                { tag: `unread-${notif._id}` }
              );
            },
            (index + 1) * 1500
          );
        });
      }
    } catch (error) {
      toast.error('Error showing notifications:', error);
    }
    hasShownInitialNotifications.current = true;
  }, [
    currentNotifications,
    selectedTab,
    realTimeUser,
    permission,
    triggerNotificationInSystem,
  ]);

  useEffect(() => {
    const isPageRefresh =
      performance.navigation?.type === 1 ||
      performance.getEntriesByType('navigation')[0]?.type === 'reload';
    if (
      realTimeUser &&
      currentNotifications.length > 0 &&
      selectedTab === 'unread' &&
      !notificationPermissionRequested.current &&
      !isPageRefresh
    ) {
      notificationPermissionRequested.current = true;
      setTimeout(showUnreadNotificationsOnLoad, 2000);
    }
  }, [
    realTimeUser,
    currentNotifications,
    selectedTab,
    showUnreadNotificationsOnLoad,
  ]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && realTimeUser && permission === 'granted') {
        const now = Date.now();
        const lastShown = localStorage.getItem('lastNotificationShown');
        if (!lastShown || now - parseInt(lastShown) > 300000) {
          hasShownInitialNotifications.current = false;
          notificationPermissionRequested.current = false;
          localStorage.setItem('lastNotificationShown', now.toString());
          setTimeout(() => {
            if (refetchNewNotification) {
              refetchNewNotification().then(() => {
                showUnreadNotificationsOnLoad();
              });
            }
          }, 1000);
        }
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () =>
      document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [
    realTimeUser,
    permission,
    refetchNewNotification,
    showUnreadNotificationsOnLoad,
  ]);

  useEffect(() => {
    if (!socket || !realTimeUser) return;

    const handleNotification = (data) => {
      let showNotif = false;
      let viewTiles =
        realTimeUser?.kanbanFilter?.map((filter) => filter.label) ||
        realTimeUser?.kanbanFilter ||
        [];
      for (let i of data?.payload?.notification?.columns || []) {
        if (viewTiles.includes(i)) {
          showNotif = true;
          break;
        }
      }
      if (
        data?.payload?.notification?.profileId === realTimeUser?.profileId &&
        realTimeUser?._id !== data?.payload?.notification?.senderUser &&
        showNotif
      ) {
        let page =
          data?.payload?.notification?.data?.order?.currentPage?.join(',') ||
          '';
        addNotification({
          title: 'Tile Shift',
          message: `${data?.payload?.notification?.senderName} shifted order ${data?.payload?.notification?.data?.order?.taskId} to ${page}`,
          native: true,
          duration: 8000,
        });
      }
    };

    const handleOrderAssign = (data) => {
      if (
        Array.isArray(data?.payload?.forUser)
          ? data?.payload?.forUser?.includes(realTimeUser?._id)
          : data?.payload?.forUser === realTimeUser?._id
      ) {
        addNotification({
          title: 'Tile assigned to you',
          message: `Card ${data?.payload?.data?.taskId} assigned to you`,
          native: true,
          duration: 8000,
        });
      }
    };

    const handleRealtimeNotification = (data) => {
      const payload = JSON.parse(data);
      if (payload?.userIds?.includes(realTimeUser._id)) {
        Promise.all([refetchNewNotification?.(), refetchUnreadCount?.()]);
        if (realTimeUser._id !== payload?.userId) {
          toast?.[payload?.type || 'info'](
            <CustomNotificationToast
              title={payload?.title}
              desc={payload?.description}
            />,
            { toastId: payload?._id }
          );
          triggerNotificationInSystem(payload?.description, payload?.title, {
            tag: `realtime-${payload?._id}`,
          });
        }
      }
    };

    socket.on('notification', handleNotification);
    socket.on('departmentalOrderAssignUser', handleOrderAssign);
    socket.on(realTimeUser.profileId, handleRealtimeNotification);

    return () => {
      socket.off('notification');
      socket.off('departmentalOrderAssignUser');
      socket.off(realTimeUser.profileId);
    };
  }, [
    socket,
    realTimeUser,
    refetchNewNotification,
    refetchUnreadCount,
    triggerNotificationInSystem,
  ]);

  const handleMarkAllAsRead = useCallback(async () => {
    try {
      await markAllAsReadNewNotification();
      refetchUnreadCount();
      refetchNewNotification();
    } catch (error) {
      toast.error('Error marking all as read');
    }
  }, [
    markAllAsReadNewNotification,
    refetchUnreadCount,
    refetchNewNotification,
  ]);

  const handleClearAll = useCallback(async () => {
    try {
      await clearAllNewNotification();
      refetchUnreadCount();
      refetchNewNotification();
    } catch (error) {
      toast.error('Error clearing all notifications');
    }
  }, [clearAllNewNotification, refetchUnreadCount, refetchNewNotification]);

  const handleDeleteSingleNotification = useCallback(
    async (id) => {
      try {
        await deleteSingleNotification({ id });
        refetchUnreadCount();
        refetchNewNotification();
      } catch (error) {
        toast.error('Error clearing all notifications');
      }
    },
    [deleteSingleNotification, refetchUnreadCount, refetchNewNotification]
  );

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([refetchNewNotification(), refetchUnreadCount()]);
    } catch (error) {
      toast.error('Error refreshing notifications');
    } finally {
      setIsRefreshing(false);
    }
  }, [refetchNewNotification, refetchUnreadCount]);

  const handleTabChange = useCallback((tab) => {
    setSelectedTab(tab);
    setPage(1);
    setLimit(5);
  }, []);

  const tabs = [
    { key: 'unread', label: 'Unread', count: unreadCount },
    { key: 'read', label: 'Read', count: readCount },
  ];

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`relative flex items-center justify-center transition-all duration-200 hover:scale-105 ${showSideBar ? 'h-12 w-12' : 'h-8 w-8'}`}
      >
        <Badge count={unreadCount} showZero={false}>
          <BellOutlined className="text-white text-2xl" />
        </Badge>
      </button>
      {isOpen && (
        <div className="absolute right-0 mt-3 w-96 bg-white rounded-2xl shadow-xl border border-slate-200 z-50 overflow-hidden">
          <div className="p-5 border-b border-slate-100 bg-white">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-slate-900">
                Notifications
              </h3>
              <div className="flex items-center gap-2">
                <button
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors disabled:opacity-50"
                >
                  <RefreshCw
                    size={16}
                    className={isRefreshing ? 'animate-spin' : ''}
                  />
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
                >
                  <X size={16} />
                </button>
              </div>
            </div>
          </div>
          <div className="p-4 border-b border-slate-100 bg-slate-50/50">
            <div className="flex gap-2">
              {tabs.map((tab) => (
                <TabButton
                  key={tab.key}
                  label={tab.label}
                  count={tab.count}
                  active={selectedTab === tab.key}
                  onClick={() => handleTabChange(tab.key)}
                />
              ))}
            </div>
          </div>
          <div className="px-4 py-3 border-b border-slate-100 bg-slate-50/30">
            <div className="flex items-center justify-between">
              <button
                onClick={handleMarkAllAsRead}
                disabled={unreadCount === 0}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Mark all as read
              </button>
              <button
                onClick={handleClearAll}
                disabled={currentNotifications.length === 0}
                className="text-sm text-red-600 hover:text-red-700 font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Clear all
              </button>
            </div>
          </div>
          <div className="max-h-96 overflow-y-auto">
            {currentNotifications.length > 0 ? (
              <>
                {currentNotifications.map((notification, idx) => (
                  <NotificationItem
                    key={`${selectedTab}-${notification._id}-${idx}`}
                    notification={notification}
                    onMarkAsRead={handleMarkAllAsRead}
                    onDelete={handleDeleteSingleNotification}
                  />
                ))}
                {hasMore && (
                  <div className="p-4 text-center">
                    <button
                      onClick={loadMore}
                      disabled={isLoading}
                      className="text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors disabled:opacity-50"
                    >
                      {isLoading ? 'Loading...' : 'Load More'}
                    </button>
                  </div>
                )}
              </>
            ) : (
              <div className="p-8 text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-slate-100 flex items-center justify-center">
                  <Bell size={24} className="text-slate-400" />
                </div>
                <p className="text-slate-500 text-sm">
                  {selectedTab === 'unread'
                    ? 'No unread notifications'
                    : 'No read notifications'}
                </p>
              </div>
            )}
          </div>
          <div className="p-4 border-t border-slate-100 bg-slate-50/30">
            <button
              onClick={() => {
                setIsOpen(false);
                navigate('/notifications');
              }}
              className="w-full text-sm text-center text-blue-600 hover:text-blue-700 font-medium transition-colors py-2"
            >
              Go to notification center
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationV3;
