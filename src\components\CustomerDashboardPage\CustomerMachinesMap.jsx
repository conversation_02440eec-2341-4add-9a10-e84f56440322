import {
  AdvancedMarker,
  APIProvider,
  InfoWindow,
  Map,
  useAdvancedMarkerRef,
} from '@vis.gl/react-google-maps';
import { useContext } from 'react';
import { Store } from '../../store/Store';

function MachineMarker({ marker, data }) {
  const [markerRef, anchor] = useAdvancedMarkerRef();

  return (
    <>
      <AdvancedMarker position={marker} ref={markerRef} />
      <InfoWindow
        anchor={anchor}
        className="min-w-[150px]"
        headerContent={data?.machineName}
      ></InfoWindow>
    </>
  );
}

function CustomerMachinesMap({ rows }) {
  const { defaults: { googleMapApiKey = '' } = {} } = useContext(Store);

  return (
    <div className="w-full min-h-[75%]">
      <APIProvider apiKey={googleMapApiKey}>
        <div className="h-[500px]">
          <Map
            mapId={'test'}
            style={{ width: '100%', height: '100%' }}
            defaultCenter={{ lat: 22.977378795847805, lng: 79.26377078903572 }}
            defaultZoom={5}
            gestureHandling={'greedy'}
          >
            {rows?.map((row) => (
              <MachineMarker
                key={row?.device?._id}
                marker={row?.device?.location}
                data={row}
              />
            ))}
          </Map>
        </div>
      </APIProvider>
    </div>
  );
}

export default CustomerMachinesMap;
