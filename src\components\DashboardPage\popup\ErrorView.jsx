import { getLocalDateTime } from '../../../helperFunction';
import Button from '../../global/components/Button';

const ErrorView = ({ selectOptions, setShowPopup }) => {
  const { cuProjects, project } = selectOptions;

  return (
    <div className="fixed left-0 top-0 w-screen h-screen z-[150] bg-black/20 flex justify-center items-center">
      <div className="w-[90%] h-[90%] bg-white rounded-new overflow-y-scroll">
        <div className="w-full flex justify-between items-center px-5 py-2 border-b">
          <h2>Error View</h2>
          <Button
            color="red"
            className="w-1/5"
            onClick={() => setShowPopup('')}
          >
            Cancel
          </Button>
        </div>

        <div className="py-3">
          {cuProjects?.map((cuProject) => (
            <div key={cuProject._id} className="px-5 pb-2 border-b">
              <p className="mb-2">{`Batch ${cuProject?.batchInfo?.batchNo} :`}</p>

              {project?.machines?.map((machine) => {
                const machineAndOperator = cuProject?.machineAndOperator?.find(
                  (mao) => mao.machine._id === machine._id
                );

                if (!machineAndOperator) {
                  return null;
                }

                const filteredPauseErrors = cuProject?.errorMessages?.filter(
                  (em) =>
                    em.error.action === 'pause' &&
                    em.machineAndOperatorId === machineAndOperator._id
                );

                const pauseArr = [];
                filteredPauseErrors?.forEach((err, eIdx) => {
                  pauseArr.push({
                    type: err.error.action,
                    time: machineAndOperator.pauseTime[eIdx],
                    error: err.error.message,
                  });
                });

                // generates an array of single object with stop time, data and error
                const filteredStopErrors = cuProject?.errorMessages?.filter(
                  (em) =>
                    em.error.action === 'stop' &&
                    em.machineAndOperatorId === machineAndOperator._id
                );

                const stopArr = [
                  {
                    type: filteredStopErrors[0]?.error.action,
                    time: machineAndOperator.stopTime,
                    error: filteredStopErrors
                      .map(
                        (err, idx) =>
                          `${idx + 1}) ${err.error.parameter}: ${
                            err.error.message
                          }`
                      )
                      .join(', '),
                  },
                ];

                const allErrorAndTime = [...pauseArr, ...stopArr];

                return (
                  <div key={machine._id} className="bg-gray-primary rounded">
                    <h5 className="px-5 py-2">{machine.machineName}</h5>

                    <table className="w-full">
                      <thead className="bg-gray-primary border-b">
                        <tr>
                          <th className="py-2 px-4">#</th>
                          <th className="py-2 px-4">Action</th>
                          <th className="py-2 px-4">Time</th>
                          <th className="py-2 px-4  w-1/3">Message</th>
                        </tr>
                      </thead>
                      <tbody>
                        {allErrorAndTime?.map((err, eIdx) => {
                          if (!err.time) {
                            return null;
                          }
                          return (
                            <tr
                              key={eIdx}
                              className={`text-center ${
                                eIdx % 2 === 0 ? 'bg-gray-primary' : 'bg-white'
                              }`}
                            >
                              <td className="py-2 px-4">{eIdx + 1}</td>
                              <td className="py-2 px-4 capitalize">
                                {err.type}
                              </td>
                              <td className="py-2 px-4">
                                {getLocalDateTime(err.time)}
                              </td>
                              <td className="py-2 px-4">{err.error}</td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ErrorView;
