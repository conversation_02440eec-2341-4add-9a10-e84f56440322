import { useEffect, useState } from 'react';
import {
  useEditMandtoryFieldsMutation,
  useGetMandatoryFieldsQuery,
} from '../../slices/mandatoryFieldsApiSlice';
import Button from '../global/components/Button';
import { toast } from 'react-toastify';

const MODELS = [
  {
    label: 'Sales Order',
    value: 'SalesOrder', // mongoose model name
    fields: [
      {
        label: 'Delivery Date',
        value: 'deliveryDate', // field name in mongoose model
      },
    ],
  },
  {
    label: 'Sales Quotation',
    value: 'Quotation',
    fields: [
      {
        label: 'Expiry Date',
        value: 'date.expiryDate', // nested field name for mongoose model
      },
    ],
  },
];

function MandatoryFields() {
  const [inputData, setInputData] = useState({});

  const { data } = useGetMandatoryFieldsQuery();
  const [editMandtoryFields, { isLoading: isLoadingEdit }] =
    useEditMandtoryFieldsMutation();

  useEffect(() => {
    if (data) setInputData(data);
  }, [data]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const data = await editMandtoryFields({ data: inputData }).unwrap();
    if (data) {
      toast.success('Mandatoy fiels updated succesfully');
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className={`w-full ${isLoadingEdit ? 'animate-pulse' : ''}`}
    >
      <h2>Mandatory Fields</h2>
      {MODELS.map((model, mIdx) => (
        <div key={mIdx} className="border-b px-5 py-2">
          <p className="text-lg font-semibold">{model?.label}</p>
          <div className="grid responsive-grid-checkbox px-2 gap-1">
            {model?.fields?.map((field) => (
              <label
                key={field?.value}
                htmlFor={model?.value + field?.value}
                className="flex w-full gap-2 items-center cursor-pointer"
              >
                <input
                  type="checkbox"
                  name={field?.value}
                  id={model?.value + field?.value}
                  checked={
                    inputData?.[model?.value]?.find(
                      (i) => i?.value === field?.value
                    ) || false
                  }
                  onChange={(e) =>
                    setInputData((prev) => ({
                      ...(prev || {}),
                      [model?.value]: e?.target?.checked
                        ? [...(prev?.[model?.value] || []), field]
                        : prev?.[model?.value]?.filter(
                            (i) => i?.value !== field?.value
                          ),
                    }))
                  }
                />
                {field?.label}
              </label>
            ))}
          </div>
        </div>
      ))}
      <div className="mt-5 flex w-full justify-end">
        <Button type="submit">Update</Button>
      </div>
    </form>
  );
}

export default MandatoryFields;
