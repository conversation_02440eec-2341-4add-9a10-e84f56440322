import { useContext, useState } from 'react';
import {
  generateDateLabel,
  generateDateTimeStringProduction,
} from '../../helperFunction';
import { Store } from '../../store/Store';
import Table from '../global/components/Table';
import MachineRow from './MachineRow';
import PlannerMultiProcessRow from './PlannerMultiProcessRow';

const RowComp = ({
  process = {},
  currBatch = {},
  currGoalsTable = {},
  setGoalsTable,
  machines = [],
  machineSchedules = [],
  setMachineSchedules,
  customMachineTimes,
  allLocations,
  usersForAssigning,
}) => {
  const [expandMachineRow, setExpandMachineRow] = useState(true);

  const { defaults: { defaultParam = {} } = {} } = useContext(Store);
  const { isMultiProcess, processCategory } = process;

  const appDtIds =
    currGoalsTable?.applicableDowntime?.map((i) => i.value) || [];
  const applicableDowntime =
    defaultParam?.plannedDowntimes?.filter((i) => appDtIds?.includes(i._id)) ||
    [];

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setGoalsTable((prev) =>
      prev.map((gt) => {
        if (gt?.flowId === process._id) {
          return {
            ...gt,
            tableData: gt?.tableData?.map((tdt) => {
              if (tdt?.batchNo === currBatch?.batchNo) {
                return {
                  ...tdt,
                  [name]: type === 'number' ? +value : value,
                };
              }
              return tdt;
            }),
          };
        }
        return gt;
      })
    );
  };

  const hasIntervalDt = !!applicableDowntime?.find(
    (dt) => dt.type === 'interval'
  );
  const hasQuantityDt = !!applicableDowntime?.find(
    (dt) => dt.type === 'quantity'
  );

  const isInhouse = processCategory === 'Inhouse';

  if (isMultiProcess)
    return (
      <PlannerMultiProcessRow
        process={process}
        currBatch={currBatch}
        applicableDowntime={applicableDowntime}
        hasIntervalDt={hasIntervalDt}
        hasQuantityDt={hasQuantityDt}
        currGoalsTable={currGoalsTable}
        setGoalsTable={setGoalsTable}
        machines={machines}
        machineSchedules={machineSchedules}
        setMachineSchedules={setMachineSchedules}
        customMachineTimes={customMachineTimes}
        allLocations={allLocations}
        usersForAssigning={usersForAssigning}
      />
    );

  return (
    <>
      <Table.Row className="border-l-2 border-l-green-400">
        <Table.Td className={currBatch?.status ? 'pointer-events-none ' : ''}>
          <span className={isInhouse ? 'link-hover' : ''}>-</span>
        </Table.Td>
        <Table.Td
          className={currBatch?.status ? 'pointer-events-none ' : ''}
          onClick={
            isInhouse
              ? () => {
                  setExpandMachineRow((prev) => !prev);
                }
              : null
          }
        >
          <span className={isInhouse ? 'link-hover' : ''}>
            {process.processName}
          </span>
        </Table.Td>
        <Table.Td className={currBatch?.status ? 'pointer-events-none ' : ''}>
          {currBatch?.['Batch Size']}
        </Table.Td>
        <Table.Td className={currBatch?.status ? 'pointer-events-none ' : ''}>
          {currBatch?.newBatchSize || currBatch?.['Batch Size']}
        </Table.Td>
        <Table.Td className={currBatch?.status ? 'pointer-events-none ' : ''}>
          <span className="text-red-400">NA</span>
        </Table.Td>
        <Table.Td>-</Table.Td>
        <Table.Td>-</Table.Td>
        <Table.Td>-</Table.Td>

        <Table.Td className={currBatch?.status ? 'pointer-events-none ' : ''}>
          <input
            type="datetime-local"
            className="w-full outline outline-1 p-1 bg-transparent rounded-sm"
            name="startDate"
            readOnly={isInhouse}
            value={
              currBatch?.startDate
                ? generateDateTimeStringProduction(currBatch?.startDate)
                : ''
            }
            onChange={!isInhouse ? handleInputChange : null}
          />
        </Table.Td>
        <Table.Td className={currBatch?.status ? 'pointer-events-none ' : ''}>
          <input
            type="datetime-local"
            readOnly={isInhouse || !currBatch?.startDate}
            className="w-full outline outline-1 p-1 bg-transparent rounded-sm"
            name="stopDate"
            value={
              currBatch?.stopDate
                ? generateDateTimeStringProduction(currBatch?.stopDate)
                : ''
            }
            onChange={!isInhouse ? handleInputChange : null}
          />
        </Table.Td>
        <Table.Td className={currBatch?.status ? 'pointer-events-none ' : ''}>
          {'-'}
        </Table.Td>
        <Table.Td className={currBatch?.status ? 'pointer-events-none ' : ''}>
          {currBatch?.duration || ''}
        </Table.Td>
      </Table.Row>
      {isInhouse && expandMachineRow && (
        <MachineRow
          machines={machines}
          machineSchedules={machineSchedules}
          setMachineSchedules={setMachineSchedules}
          flowId={process._id}
          batchNo={currBatch?.batchNo}
          customMachineTimes={customMachineTimes}
          allLocations={allLocations}
          setGoalsTable={setGoalsTable}
          mqttId={process?.mqtt?._id}
          usersForAssigning={usersForAssigning}
        />
      )}
      {/* {isInhouse && expandMachineRow && (
        <tr>
          <td colSpan={'100%'} className="p-1"></td>
        </tr>
      )} */}
    </>
  );
};

const PlannerGoalsTable = ({
  batchData = [],
  productionFlow = {},
  goalsTable,
  setGoalsTable,
  machineSchedules = [],
  setMachineSchedules,
  allMachines = [],
  customMachineTimes,
  allLocations,
  usersForAssigning,
}) => {
  const [showAdvanceTable, setShowAdvanceTable] = useState(false);

  const { processes } = productionFlow;

  const firstStartDate = batchData?.[0]?.startDate || '';
  const lastStopDate = batchData?.[batchData?.length - 1]?.stopDate || '';

  return (
    <>
      <Table.Row className="!border-l-2 !border-l-red-500">
        <Table.Td onClick={() => setShowAdvanceTable((prev) => !prev)}>
          <span className="link-hover">{batchData?.[0]?.batchName}</span>
        </Table.Td>
        <Table.Td>
          {firstStartDate ? generateDateLabel(firstStartDate) : '-'}
        </Table.Td>
        <Table.Td>
          {lastStopDate ? generateDateLabel(lastStopDate) : '-'}
        </Table.Td>
      </Table.Row>
      {showAdvanceTable && (
        <>
          {processes?.map((process, pIdx) => {
            const gt = goalsTable?.find((gt) => gt.flowId === process._id);

            return (
              <RowComp
                key={process._id}
                process={process}
                currBatch={batchData?.[pIdx]}
                currGoalsTable={gt}
                machines={allMachines.filter((mac) =>
                  gt.selectedMachines.includes(mac._id)
                )}
                setGoalsTable={setGoalsTable}
                machineSchedules={machineSchedules}
                setMachineSchedules={setMachineSchedules}
                customMachineTimes={customMachineTimes}
                allLocations={allLocations}
                usersForAssigning={usersForAssigning}
              />
            );
          })}
        </>
      )}
    </>
  );
};

export default PlannerGoalsTable;
