import { XMarkIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import Marquee from 'react-fast-marquee';
import AttachmentIcon from '../../../assets/images/attachment.png';
import pdf from '../../../assets/images/pdf.png';
import { ReactComponent as UploadIcon } from '../../../assets/svgs/upload_cloud.svg';
import Modal from '../../global/components/Modal';
import PdfViewer from '../../global/components/PdfViewer';
import UploadButton from '../../UploadButton';

const Media = ({
  imageURLs,
  setImageURLs,
  processes = [],
  setRemovedMedia,
  isEdit,
  isSingleDrop = false,
  title = 'Project',
}) => {
  const [filteredProcesses, setFilteredProcesses] = useState([]);
  const [showModal, setShowModal] = useState(null);
  const [media, setMedia] = useState({});
  const [ShowFullScreenModal, setShowFullScreenModal] = useState(false);
  useEffect(() => {
    let unqPro = [];
    processes?.forEach((pro) => {
      const exists = unqPro?.find((i) => i?._id === pro?.mqtt?._id);
      const isOutSourceProcess = pro?.processCategory === 'Outsource';
      if (!exists && !isOutSourceProcess) {
        unqPro.push(pro?.mqtt);
      }
    });

    setFilteredProcesses(unqPro);
  }, [processes]);

  const changeHandler = (e, key) => {
    for (let i in e) {
      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      let name = e[i].name;
      let type = e[i].type;
      fr.addEventListener('load', () => {
        const url = fr.result;
        if (key) {
          setImageURLs((prev) => ({
            ...prev,
            [key]: [
              ...(prev?.[key] || []),
              {
                name: name,
                type: type,
                data: url,
              },
            ],
          }));
        } else {
          setImageURLs((prev) => ({
            ...prev,
            project: [
              ...(prev[key] || []),
              {
                name: name,
                type: type,
                data: url,
              },
            ],
          }));
        }
      });
    }
  };

  const removeHandler = (media, key) => {
    if (setRemovedMedia && isEdit) {
      setRemovedMedia((prev) => [...prev, media._id]);
    }
    setImageURLs((prev) => ({
      ...prev,
      [key]: prev[key].filter((item) => item.data !== media?.data),
    }));
  };

  return (
    <div className="flex justify-center w-full items-center">
      {ShowFullScreenModal && (
        <div
          className="fixed top-0 left-0 flex justify-between items-center w-screen h-screen bg-black/10 z-[999999]"
          onClick={() => {
            if (media?.type !== 'application/pdf')
              setShowFullScreenModal(false);
          }}
        >
          <>
            {media?.type === 'application/pdf' ? (
              <PdfViewer
                file={media?.data}
                name={media?.name}
                closeClick={(e) => {
                  e.preventDefault();
                  setShowFullScreenModal(false);
                }}
              />
            ) : (
              <div className="flex items-center justify-center">
                <img
                  className="h-[90%] aspect-video object-contain"
                  src={media?.data}
                  alt=""
                />
              </div>
            )}
          </>
        </div>
      )}

      <div className="w-full h-full">
        <div className="w-full flex px-10 mt-2 flex-wrap  gap-y-4">
          <div className={`mb-4 w-full ${!isSingleDrop ? 'basis-[31%]' : ''}`}>
            <div className="flex gap-2">
              <h3 className="mb-1">{title}</h3>
              <div
                onClick={() => setShowModal('Project')}
                className="flex bg-blue-primary !min-w-[80px] !w-[120px] text-white py-2 px-2 mb-3 rounded-lg cursor-pointer"
              >
                <span className="mr-1">
                  {imageURLs?.project?.length || 0} Attached
                </span>
                <img
                  src={AttachmentIcon}
                  alt="Attachment Icon"
                  className="w-5 h-5 object-contain relative"
                />
              </div>
            </div>
            <div className="w-full flex gap-x-5">
              {/* <section
                className={
                  imageURLs?.project?.length > 0 ? 'w-[35%]' : 'w-full'
                }
              > */}
              <UploadButton
                accept="image/*, application/pdf"
                onChange={(e) => changeHandler(e, 'project')}
                multiple
                className={
                  'min-h-[120px] h-full bg-gray-primary w-full text-center'
                }
                svg={<UploadIcon />}
              />
              {showModal == 'Project' && (
                <Modal
                  title={'Uploaded Files'}
                  description={'Uploaded Files'}
                  onCloseModal={() => setShowModal(null)}
                  canSubmit={false}
                  isSubmitRequired={false}
                >
                  {() => (
                    <div className="flex gap-4 flex-wrap">
                      {imageURLs?.project?.map((item, uIdx) => (
                        <section
                          key={uIdx}
                          className="p-2 border rounded-md w-[170px] flex flex-col justify-between"
                        >
                          <section
                            className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer "
                            onClick={() => {
                              setMedia(item);
                              setShowFullScreenModal(true);
                            }}
                          >
                            <img
                              className="w-[150px] aspect-video object-contain"
                              src={
                                item?.type === 'application/pdf'
                                  ? pdf
                                  : item?.data
                              }
                              alt=""
                            />
                          </section>

                          <section className="flex justify-between items-center text-sm mt-2">
                            <Marquee className="w-">{item.name}</Marquee>
                            <button
                              type="button"
                              onClick={() => removeHandler(item, 'project')}
                              className="outline-none text-red-primary hover:text-white hover:bg-red-primary rounded px-2 py-1"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </section>
                        </section>
                      ))}
                    </div>
                  )}
                </Modal>
              )}
              {/* </section> */}
            </div>
          </div>

          {filteredProcesses?.map((process) => (
            <div key={process._id} className="mb-4 w-full basis-[31%]">
              <div className="flex gap-2">
                <h3 className="mb-1">{process.process}</h3>
                <div
                  onClick={() => setShowModal(process._id)}
                  className="flex bg-blue-primary text-white py-2 px-2 mb-3 rounded-lg cursor-pointer"
                >
                  <span className="mr-1">
                    {imageURLs?.[process._id]?.length || 0} Attached
                  </span>
                  <img
                    src={AttachmentIcon}
                    alt="Attachment Icon"
                    className="w-5 h-5 object-contain relative"
                  />
                </div>
              </div>

              <div className="w-full flex gap-x-5">
                {/* <section
                  className={
                    imageURLs?.[process._id]?.length > 0 ? 'w-[35%]' : 'w-full'
                  }
                > */}
                <UploadButton
                  accept="image/*, application/pdf"
                  onChange={(e) => changeHandler(e, process._id)}
                  multiple
                  className={'min-h-[120px] h-full bg-gray-primary w-full'}
                  svg={<UploadIcon />}
                />
                {/* </section> */}
                {/* <section
                  className={
                    imageURLs?.[process._id]?.length > 0
                      ? 'w-[65%] flex gap-x-5 overflow-y-scroll'
                      : ''
                  }
                > */}
                {showModal == process?._id && (
                  <Modal
                    title={'Uploaded Files'}
                    description={'Uploaded Files'}
                    onCloseModal={() => setShowModal(null)}
                    canSubmit={false}
                    isSubmitRequired={false}
                  >
                    {() => {
                      return (
                        <div className="flex gap-4 flex-wrap">
                          {imageURLs?.[process._id]?.map((item, uIdx) => (
                            <section
                              key={uIdx}
                              className="p-2 border rounded-md w-[170px] flex flex-col justify-between"
                            >
                              <section
                                className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer "
                                onClick={() => {
                                  setMedia(item);
                                  setShowFullScreenModal(true);
                                }}
                              >
                                <img
                                  className="w-[150px] aspect-video object-contain"
                                  src={
                                    item?.type === 'application/pdf'
                                      ? pdf
                                      : item?.data
                                  }
                                  alt=""
                                />
                              </section>

                              <section className="flex justify-between items-center text-sm mt-2">
                                <Marquee className="w-">{item.name}</Marquee>
                                <button
                                  type="button"
                                  onClick={() =>
                                    removeHandler(item, process._id)
                                  }
                                  className="outline-none text-red-primary hover:text-white hover:bg-red-primary rounded px-2 py-1"
                                >
                                  <XMarkIcon className="h-4 w-4" />
                                </button>
                              </section>
                            </section>
                          ))}
                        </div>
                      );
                    }}
                  </Modal>
                )}
                {/* </section> */}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
export default Media;
