import { TrashIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { ReactComponent as JpgPng } from '../../../assets/svgs/pdfsvg.svg';
import DragAndDrop from '../../global/components/DragAndDrop';
import FullScreenModal from '../../global/components/FullScreenModal';
import Input from '../../global/components/Input';
import PdfViewer from '../../global/components/PdfViewer';
import Table from '../../global/components/Table';

const DynamicForm = ({
  selectedForm,
  isEdit,
  setSelectedForm,
  // removedPDFwhileUpdate,
  // setRemovedPDFwhileUpdate,
  newStep,
  setNewStep,
  stepCount,
  setIsChanged,
}) => {
  const [media, setMedia] = useState({});
  const [ShowFullScreenModal, setShowFullScreenModal] = useState(false);
  const handleStepDelete = (item) => {
    setSelectedForm((prev) => ({
      ...prev,
      steps: prev?.steps?.filter(
        (step) => step?.stepNumber !== item?.stepNumber
      ),
    }));
  };

  const assemblyFileChangeHandler = (e, type, newStep, index) => {
    for (let i in e) {
      let fileName = e[i].name;
      let fileType = e[i].type;

      const fileReader = new FileReader();
      if (i === 'length') return;
      fileReader.readAsDataURL(e[i]);
      fileReader.addEventListener('load', () => {
        const url = fileReader.result;
        let data = {
          name: fileName,
          type: fileType,
          data: url,
        };

        if (newStep) {
          setNewStep((prev) => ({
            ...prev,
            attachments: [...prev.attachments, data],
          }));
        } else {
          setSelectedForm((prev) => ({
            ...prev,
            steps: prev?.steps?.map((step, i) => {
              if (i === index) {
                return {
                  ...step,
                  attachments: [...step.attachments, data],
                };
              }
              return step;
            }),
          }));
        }
      });
    }
  };

  const handleRemovePdf = (file, newStep, index) => {
    if (newStep) {
      setNewStep((prev) => ({
        ...prev,
        attachments: prev?.attachments?.filter(
          (assembly) => assembly?.name !== file?.name
        ),
      }));
    } else {
      setSelectedForm((prev) => ({
        ...prev,
        steps: prev?.steps?.map((step, i) => {
          if (i === index) {
            return {
              ...step,
              attachments: step?.attachments?.filter(
                (assembly) => assembly?.name !== file?.name
              ),
            };
          }
          return step;
        }),
      }));
      // if (file?._id) {
      // setRemovedPDFwhileUpdate([...removedPDFwhileUpdate, file?._id]);
      // }
    }
  };

  return (
    <>
      <FullScreenModal
        ShowModal={ShowFullScreenModal}
        setShowModal={setShowFullScreenModal}
      >
        <>
          {media?.type === 'application/pdf' ? (
            <PdfViewer
              file={media?.data}
              name={media?.name}
              closeClick={(e) => {
                e.preventDefault();
                setShowFullScreenModal(false);
              }}
            />
          ) : (
            <div className="flex items-center justify-center">
              <img
                className=" block w-[55rem] h-[35rem]"
                src={media?.data}
                alt=""
              />
            </div>
          )}
        </>
      </FullScreenModal>

      <section className="mt-4">
        {isEdit && (
          <div className="flex flex-col my-5">
            <label className="mb-1 font-semibold text-[#667085]">
              Assembly Form Name
            </label>
            <Input
              type="text"
              id="assemblyFormName"
              name="assemblyFormName"
              placeholder="Assembly Form Name"
              value={selectedForm?.formName}
              onChange={(e) => {
                setIsChanged(true);
                setSelectedForm((prev) => ({
                  ...prev,
                  formName: e.target.value,
                }));
              }}
            />
          </div>
        )}
        {selectedForm?.steps?.length > 0 &&
          selectedForm?.steps?.map((item, index) => {
            return (
              <div key={index}>
                <div className="outline-[#4085ed80] rounded-lg border p-2 mb-5">
                  <div className="flex flex-row justify-between px-2">
                    <label className="font-bold text-[#667085] text-lg">
                      Step {item?.stepNumber}:
                    </label>
                    {isEdit && (
                      <div
                        className="hover:cursor-pointer"
                        onClick={() => {
                          handleStepDelete(item);
                          setIsChanged(true);
                        }}
                      >
                        <TrashIcon className="w-6 h-6 text-black my-auto" />
                      </div>
                    )}
                  </div>
                  <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2 mt-2">
                    <div className="flex flex-col">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Step Name
                      </label>
                      <Input
                        type="text"
                        id="stepName"
                        name="stepName"
                        placeholder="Step Name"
                        value={item?.stepName}
                        disabled={!isEdit}
                        onChange={(e) => {
                          setSelectedForm((prev) => ({
                            ...prev,
                            steps: prev?.steps?.map((prevItem, i) => {
                              if (i === index) {
                                return {
                                  ...prevItem,
                                  stepName: e.target.value,
                                };
                              }
                              return prevItem;
                            }),
                          }));
                          setIsChanged(true);
                        }}
                      />
                    </div>
                    <div className="flex flex-row justify-between">
                      <div></div>
                      <div className="flex flex-row space-x-2 mt-8 items-center ">
                        <label className="mb-1 font-semibold text-[#667085]">
                          Media Mandatory :{' '}
                        </label>
                        {!isEdit ? (
                          item?.isMediaMandatory ? (
                            'Yes'
                          ) : (
                            'No'
                          )
                        ) : (
                          <Input
                            type="checkbox"
                            id="isMediaMandatory"
                            name="isMediaMandatory"
                            value={item?.isMediaMandatory}
                            checked={item?.isMediaMandatory}
                            onChange={(e) => {
                              setSelectedForm((prev) => ({
                                ...prev,
                                steps: prev?.steps?.map((prevItem, i) => {
                                  if (i === index) {
                                    return {
                                      ...prevItem,
                                      isMediaMandatory: e?.target?.checked,
                                    };
                                  }
                                  return prevItem;
                                }),
                              }));
                              setIsChanged(true);
                            }}
                          />
                        )}
                      </div>
                    </div>
                    <div className="mt-2 w-full col-span-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Select SOP File
                      </label>
                      {isEdit && (
                        <DragAndDrop
                          accept="application/pdf"
                          fileType="JPG/PNG"
                          svg={<JpgPng className="h-10" />}
                          onChange={(e) => {
                            assemblyFileChangeHandler(
                              e,
                              'project',
                              false,
                              index
                            );
                            setIsChanged(true);
                          }}
                          multiple
                          className={`text-[#667085]`}
                        />
                      )}
                    </div>
                    {item?.attachments?.length > 0 && (
                      <div className="mt-2 col-span-2">
                        <Table className="mt-2">
                          <Table.Head>
                            <Table.Row>
                              <Table.Th>S.No.</Table.Th>
                              <Table.Th>File Name</Table.Th>
                              <Table.Th></Table.Th>
                              {isEdit && <Table.Th></Table.Th>}
                            </Table.Row>
                          </Table.Head>
                          <Table.Body>
                            {item?.attachments?.map((file, idx) => {
                              return (
                                <Table.Row key={idx}>
                                  <Table.Td>{idx + 1}</Table.Td>
                                  <Table.Td>{file?.name}</Table.Td>
                                  <Table.Td
                                    onClick={() => {
                                      setMedia(file);
                                      setShowFullScreenModal(true);
                                    }}
                                  >
                                    <div className="hover:cursor-pointer">
                                      <JpgPng />
                                    </div>
                                  </Table.Td>
                                  {isEdit && (
                                    <Table.Td
                                      className="hover:cursor-pointer"
                                      onClick={() => {
                                        handleRemovePdf(file, false, index);
                                        setIsChanged(true);
                                      }}
                                    >
                                      x
                                    </Table.Td>
                                  )}
                                </Table.Row>
                              );
                            })}
                          </Table.Body>
                        </Table>
                      </div>
                    )}
                    <div className="flex flex-col mt-2 col-span-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Message
                      </label>
                      <textarea
                        type="text"
                        disabled={!isEdit}
                        id="description"
                        name="description"
                        className="py-1 pl-4 pr-2 bg-transparent relative outline-[#4085ed80] rounded-lg flex justify-between items-center border text-black"
                        placeholder="Description"
                        value={item?.description}
                        onChange={(e) => {
                          setSelectedForm((prev) => ({
                            ...prev,
                            steps: prev?.steps?.map((prevItem, i) => {
                              if (i === index) {
                                return {
                                  ...prevItem,
                                  description: e.target.value,
                                };
                              }
                              return prevItem;
                            }),
                          }));
                          setIsChanged(true);
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
      </section>
      {isEdit && (
        <section>
          <label className="font-bold text-[#667085] text-lg">
            Step {stepCount}:
          </label>
          <div className="outline-[#4085ed80] rounded-lg border p-2">
            <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2 mt-2">
              <div className="flex flex-col">
                <label className="mb-1 font-semibold text-[#667085]">
                  Step Name
                </label>
                <Input
                  type="text"
                  id="stepName"
                  name="stepName"
                  placeholder="Step Name"
                  value={newStep?.stepName}
                  onChange={(e) =>
                    setNewStep((prev) => ({
                      ...prev,
                      stepName: e.target.value,
                    }))
                  }
                />
              </div>
              <div className="flex flex-row justify-between">
                <div></div>
                <div className="flex flex-row space-x-2 mt-8 items-center ">
                  <Input
                    type="checkbox"
                    id="isMediaMandatory"
                    name="isMediaMandatory"
                    checked={newStep?.isMediaMandatory}
                    onChange={(e) => {
                      setNewStep((prev) => ({
                        ...prev,
                        isMediaMandatory: e.target.checked,
                      }));
                    }}
                  />
                  <label className="mb-1 font-semibold text-[#667085]">
                    Media Mandatory?
                  </label>
                </div>
              </div>
              <div className="mt-2 w-full col-span-2">
                <label className="mb-1 font-semibold text-[#667085]">
                  Select SOP File
                </label>
                <DragAndDrop
                  accept="application/pdf"
                  fileType="JPG/PNG"
                  svg={<JpgPng className="h-10" />}
                  onChange={(e) => {
                    assemblyFileChangeHandler(e, 'project', true);
                  }}
                  multiple
                  className={`text-[#667085]`}
                />
              </div>
              {newStep?.attachments?.length > 0 && (
                <div className="mt-2 col-span-2">
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>S.No.</Table.Th>
                        <Table.Th>File Name</Table.Th>
                        <Table.Th></Table.Th>
                        <Table.Th></Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {newStep?.attachments?.map((file, index) => {
                        return (
                          <Table.Row key={index}>
                            <Table.Td>{index + 1}</Table.Td>
                            <Table.Td>{file?.name}</Table.Td>
                            <Table.Td
                              onClick={() => {
                                setMedia(file);
                                setShowFullScreenModal(true);
                              }}
                            >
                              <div className="hover:cursor-pointer">
                                <JpgPng />
                              </div>
                            </Table.Td>
                            <Table.Td
                              onClick={() => handleRemovePdf(file, true)}
                            >
                              <div className="hover:cursor-pointer">x</div>
                            </Table.Td>
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                  </Table>
                </div>
              )}
              <div className="flex flex-col mt-2 col-span-2">
                <label className="mb-1 font-semibold text-[#667085]">
                  Description
                </label>
                <textarea
                  type="text"
                  id="description"
                  name="description"
                  className="py-1 pl-4 pr-2 bg-transparent relative outline-[#4085ed80] rounded-lg flex justify-between items-center border text-black"
                  placeholder="Description"
                  value={newStep?.description}
                  onChange={(e) =>
                    setNewStep((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                />
              </div>
            </div>
          </div>
        </section>
      )}
      {/* <div className="w-full  overflow-scroll mt-8">
        <Table className="w-full">
          <Table.Head>
            <Table.Row>
              <Table.Th>Field Name</Table.Th>
              <Table.Th>Actions</Table.Th>
              <Table.Th>Mandatory</Table.Th>
              <Table.Th>Media</Table.Th>
            </Table.Row>
          </Table.Head>

          <Table.Body>



          </Table.Body>
        </Table>
      </div> */}
    </>
  );
};

export default DynamicForm;
