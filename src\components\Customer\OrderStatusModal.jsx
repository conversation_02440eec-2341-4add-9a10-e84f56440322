import {
  Button,
  Form,
  Input,
  List,
  Modal,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { DEFAULT_STATUS } from '../../pages/CustomerDetails';

const { Title, Text } = Typography;

const OrderStatusModal = ({
  showStatusModal,
  setShowStatusModal,
  editingStatus,
  setEditingStatus,
  form,
  createStatus,
  updateStatus,
  allStatus,
}) => {
  const [localEditingStatus, setLocalEditingStatus] = useState(null);

  useEffect(() => {
    setLocalEditingStatus(editingStatus);
  }, [editingStatus]);

  const onFinish = async (values) => {
    try {
      if (
        DEFAULT_STATUS.some(
          (status) =>
            status.value === values.name.toLowerCase() ||
            status.label.toLowerCase() === values.name.toLowerCase()
        )
      ) {
        toast.error('This status already exists as a default status');
        return;
      }

      const currentStatuses = Array.isArray(allStatus?.status)
        ? allStatus.status.filter(
            (status) =>
              !DEFAULT_STATUS.some(
                (defaultStatus) =>
                  defaultStatus.value === status.toLowerCase() ||
                  defaultStatus.label.toLowerCase() === status.toLowerCase()
              )
          )
        : [];

      const newStatusName = values.name;

      if (localEditingStatus !== null) {
        const updatedStatuses = [...currentStatuses];
        const actualIndex = localEditingStatus;

        if (actualIndex >= 0 && actualIndex < updatedStatuses.length) {
          updatedStatuses[actualIndex] = newStatusName;

          const result = await updateStatus({
            status: updatedStatuses,
          }).unwrap();

          if (!result.error) {
            toast.success('Status updated successfully');
            setLocalEditingStatus(null);
          }
        } else {
          toast.error('Invalid status index');
        }
      } else {
        const newStatuses = [...currentStatuses, newStatusName];
        const result = await createStatus({
          status: newStatuses,
        }).unwrap();

        if (!result.error) {
          toast.success('Status created successfully');
          form.resetFields();
        }
      }
    } catch (error) {
      toast.error(
        `Failed to ${localEditingStatus !== null ? 'update' : 'create'} status: ${error.message}`
      );
    }
  };

  const handleCancel = () => {
    setShowStatusModal(false);
    setLocalEditingStatus(null);
    setEditingStatus(null);
    form.resetFields();
  };

  const handleEdit = (item, index) => {
    // Only allow editing custom statuses
    if (index >= DEFAULT_STATUS.length) {
      form.setFieldsValue({ name: item });
      setLocalEditingStatus(index - DEFAULT_STATUS.length);
    }
  };

  const handleDelete = async (index) => {
    try {
      // Filter out default statuses from current statuses
      const currentStatuses = Array.isArray(allStatus?.status)
        ? allStatus.status.filter(
            (status) =>
              !DEFAULT_STATUS.some(
                (defaultStatus) =>
                  defaultStatus.value === status.toLowerCase() ||
                  defaultStatus.label.toLowerCase() === status.toLowerCase()
              )
          )
        : [];

      const actualIndex = index - DEFAULT_STATUS.length;
      const updatedStatuses = currentStatuses.filter(
        (_, i) => i !== actualIndex
      );

      const result = await updateStatus({
        status: updatedStatuses,
      }).unwrap();

      if (!result.error) {
        toast.success('Status deleted successfully');
      }
    } catch (error) {
      toast.error(`Failed to delete status: ${error.message}`);
    }
  };

  const isDefaultStatus = (item) => {
    return DEFAULT_STATUS.some(
      (status) =>
        status.value === item.toLowerCase() ||
        status.label.toLowerCase() === item.toLowerCase()
    );
  };

  // Combine default and custom statuses for display
  const displayStatuses = [
    ...DEFAULT_STATUS.map((status) => status.label),
    ...(Array.isArray(allStatus?.status)
      ? allStatus.status.filter(
          (status) =>
            !DEFAULT_STATUS.some(
              (defaultStatus) =>
                defaultStatus.value === status.toLowerCase() ||
                defaultStatus.label.toLowerCase() === status.toLowerCase()
            )
        )
      : []),
  ];

  return (
    <Modal
      title={
        <Title level={4} className="text-gray-800 mb-0">
          {localEditingStatus !== null ? 'Edit Status' : 'Manage Statuses'}
        </Title>
      }
      open={showStatusModal}
      onCancel={handleCancel}
      footer={null}
      width={600}
      styles={{
        body: {
          maxHeight: 'calc(100vh - 200px)',
          overflowY: 'auto',
        },
      }}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div className="bg-gray-50 p-3 rounded-md mb-2">
          <Text className="text-sm text-gray-600">
            Default statuses cannot be edited or deleted. You can add custom
            statuses below.
          </Text>
        </div>
        <List
          dataSource={displayStatuses}
          renderItem={(item, index) => {
            const isDefault = isDefaultStatus(item);
            return (
              <List.Item
                className={`${isDefault ? 'bg-gray-50' : 'hover:bg-blue-50'} transition-colors duration-200`}
                actions={
                  !isDefault
                    ? [
                        <Button
                          key={`edit-${index}`}
                          type="link"
                          className="text-blue-600 hover:text-blue-800"
                          onClick={() => handleEdit(item, index)}
                        >
                          Edit
                        </Button>,
                        <Button
                          key={`delete-${index}`}
                          type="link"
                          danger
                          className="hover:text-red-800"
                          onClick={() => handleDelete(index)}
                        >
                          Delete
                        </Button>,
                      ]
                    : [
                        <Tooltip
                          title="Default status cannot be modified"
                          key="default-tooltip"
                        >
                          <Text className="text-gray-400 text-sm italic px-4">
                            Default
                          </Text>
                        </Tooltip>,
                      ]
                }
              >
                <Text className={isDefault ? 'font-medium px-4' : 'px-4'}>
                  {item}
                </Text>
              </List.Item>
            );
          }}
          className="border rounded-lg overflow-hidden"
        />
        <Form
          form={form}
          onFinish={onFinish}
          layout="vertical"
          className="bg-white p-4 rounded-lg border"
        >
          <Form.Item
            name="name"
            label={
              <Text strong className="text-gray-700">
                {localEditingStatus !== null
                  ? 'Edit Status Name'
                  : 'Add New Status'}
              </Text>
            }
            rules={[
              { required: true, message: 'Please input the status name!' },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();

                  // Check if status exists in default statuses
                  const existsInDefault = DEFAULT_STATUS.some(
                    (status) =>
                      status.value === value.toLowerCase() ||
                      status.label.toLowerCase() === value.toLowerCase()
                  );
                  if (existsInDefault) {
                    return Promise.reject(
                      'This status already exists as a default status!'
                    );
                  }

                  // Check if status exists in custom statuses
                  if (
                    localEditingStatus === null &&
                    allStatus?.status?.includes(value.toLowerCase())
                  ) {
                    return Promise.reject('Status already exists!');
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input
              placeholder="Enter status name"
              className="hover:border-blue-400 focus:border-blue-500 transition-colors"
            />
          </Form.Item>
          <Form.Item className="flex justify-end gap-2 mb-0">
            <Button onClick={handleCancel} className="hover:bg-gray-100">
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              className="bg-blue-600 hover:bg-blue-700 ml-2"
            >
              {localEditingStatus !== null ? 'Update' : 'Add'}
            </Button>
          </Form.Item>
        </Form>
      </Space>
    </Modal>
  );
};

export default OrderStatusModal;
