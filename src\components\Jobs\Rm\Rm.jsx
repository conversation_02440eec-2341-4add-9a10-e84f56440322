import { useState } from 'react';
import { toast } from 'react-toastify';
import { ReactComponent as JpgPng } from '../../../assets/svgs/pdfsvg.svg';
import Button from '../../global/components/Button';
import Input from '../../global/components/Input';
import Table from '../../global/components/Table';
import AddIcon from '../../../assets/images/add.png';
import UploadButton from '../../UploadButton';

const Rm = ({ modalData, setModalData }) => {
  const [numberValue, setNumberValue] = useState('');
  const [textValue, setTextValue] = useState('');
  const [pdf, setpdf] = useState([]);

  const handleNumberChange = (event) => {
    setNumberValue(event.target.value);
  };

  const handleTextChange = (event) => {
    setTextValue(event.target.value);
  };

  const handleSubmit = () => {
    if (!numberValue || !textValue) {
      toast.error('Please fill all RM deatils', { position: 'top-right' });
      return;
    }
    const data = {
      rmNo: numberValue,
      rmDesc: textValue,
      files: pdf,
    };
    setModalData((prev) => [...prev, data]);
    toast.success('RM successfully added!', { position: 'top-right' });
    setNumberValue('');
    setTextValue('');
    setpdf([]);
  };

  const changeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name;
      let ftype = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;

        let data = {
          fname: fname,
          ftype: ftype,
          data: url,
        };
        setpdf((prev) => [...prev, data]);
      });
    }
  };

  return (
    <div className="bg-white p-4 overflow-scroll">
      <div className="flex justify-between">
        <p className="pt-4 pb-6">Add Raw Material Details</p>
        <div className="flex justify-center">
          <Button
            className="rounded-[8px] w-[138px] h-[32px]"
            type="button"
            onClick={handleSubmit}
          >
            <img
              src={AddIcon}
              alt="Add Icon"
              className="w-5 h-5 object-contain relative"
            />
            Add
          </Button>
        </div>
      </div>
      <div className="mb-4">
        <label
          htmlFor="numberInput"
          className="block text-sm font-medium text-gray-700"
        >
          RM Number<span className="text-xl text-red-500 -mt-2">*</span>
        </label>
        <Input
          id="numberInput"
          type="text"
          value={numberValue}
          onChange={handleNumberChange}
        />
      </div>
      <div className="mb-4">
        <label
          htmlFor="textInput"
          className="block text-sm font-medium text-gray-700"
        >
          Description<span className="text-xl text-red-500 -mt-2">*</span>
        </label>
        <textarea
          id="textInput"
          rows="3"
          className="border px-2 py-1 outline-none border-[#C8CEE1] block w-full sm:text-sm rounded-md"
          placeholder="Add description here"
          value={textValue}
          onChange={handleTextChange}
        ></textarea>
      </div>
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700">
          Select File
        </label>
        <UploadButton
          accept="application/pdf"
          fileType="JPG/PNG"
          svg={<JpgPng className="h-10" />}
          onChange={(e) => changeHandler(e, 'project')}
          multiple
        />
      </div>

      {pdf?.map((item, idx) => {
        return (
          <p key={idx} className="text-black/50 text-sm ml-10">
            {item.fname}
          </p>
        );
      })}

      <Table className={'mt-5'}>
        <Table.Head>
          <Table.Row>
            <Table.Th>#</Table.Th>
            <Table.Th>RM No.</Table.Th>
            <Table.Th>Description</Table.Th>
            <Table.Th>Pdf's</Table.Th>
            <Table.Th></Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {modalData?.map((data, dIdx) => (
            <Table.Row key={dIdx}>
              <Table.Td>{dIdx + 1}</Table.Td>
              <Table.Td>{data?.rmNo}</Table.Td>
              <Table.Td>{data?.rmDesc}</Table.Td>
              <Table.Td>{data?.files?.map((file) => file?.fname)}</Table.Td>
              <Table.Td>
                {
                  <span
                    onClick={() =>
                      setModalData((prev) =>
                        prev.filter((_i, idx) => idx !== dIdx)
                      )
                    }
                    className={` hover:text-white hover:bg-red-500 px-3 py-0.5 rounded cursor-pointer ${
                      data?.fromDb
                        ? 'pointer-events-none text-gray-400'
                        : 'text-red-600'
                    }`}
                  >
                    Remove
                  </span>
                }
              </Table.Td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
};

export default Rm;
