const ProgressBar = ({ progress, max, progressColor, strokeWidth = '6px' }) => {
  const percentage = (progress / max) * 100;

  const progressContainer = {
    height: strokeWidth,
    width: '100%',
    backgroundColor: '#dcdcdc',
    borderRadius: '40px',
    margin: '5px 0px',
    position: 'relative',
    overflow: 'hidden',
  };

  const progressIndicator = {
    height: strokeWidth,
    width: percentage > 100 ? '100%' : `${percentage}%`,
    borderRadius: '40px',
    backgroundColor: `${progressColor?.replace('bg-[', '')?.replace(']', '')}`,
    textAlign: 'center',
  };

  return (
    <div style={progressContainer}>
      <div style={progressIndicator}></div>
    </div>
  );
};

export default ProgressBar;
