import {
  useDeleteCustomColumnMutation,
  useEditCustomColumnMutation,
  useGetCustomColumnsQuery,
  useAddCustomColumnMutation,
} from '../../slices/customCoulmn.ApiSlice';
import { Button, Input, Modal, Switch, Typography } from 'antd';
import { toast } from 'react-toastify';
import { useEffect, useState } from 'react';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { customConfirm } from '../../utils/customConfirm';

const fixedAndDisableColumns = [
  {
    columnName: 'Invoice ID',
    isRequired: true,
    isDisabled: true,
  },
  {
    columnName: 'Amount',
    isRequired: true,
    isDisabled: true,
  },
  {
    columnName: 'Comments',
    isRequired: false,
    isDisabled: true,
  },
  {
    columnName: 'Files',
    isRequired: false,
    isDisabled: true,
  },
];

const { Text, Title } = Typography;

const InvoiceColumsModal = ({ showModal, setShowModal }) => {
  const { data: allCustomCols } = useGetCustomColumnsQuery();
  const [deleteCol] = useDeleteCustomColumnMutation();
  const [editCol] = useEditCustomColumnMutation();
  const [addCols] = useAddCustomColumnMutation();

  const [columns, setColumns] = useState([]);

  useEffect(() => {
    const customColumns =
      allCustomCols?.filter((col) => col?.pageName === 'grn/invoice') || [];
    if (customColumns.length > 0) {
      setColumns(customColumns);
    } else {
      setColumns([]);
    }
  }, [allCustomCols]);

  const handleOk = async () => {
    const columnNames = [...fixedAndDisableColumns, ...columns]?.map((col) =>
      col.columnName.trim()
    );
    const hasDuplicates = new Set(columnNames).size !== columnNames.length;

    if (hasDuplicates) {
      toast.error('Column names must be unique.');
      return;
    }
    const hasEmpty = columns?.some(
      (col) => col.columnName.trim() === '' || !col
    );
    if (hasEmpty) {
      toast.error('Please fill all the fields');
      return;
    }
    const withoutIds = columns?.filter((col) => col._id === undefined);
    const withIds = columns?.filter((col) => col._id !== undefined);
    let res;
    if (withIds?.length > 0) {
      res = await editCol(
        withIds.map((col) => ({ ...col, columnName: col.columnName.trim() }))
      );
    }

    let res2;
    if (withoutIds?.length > 0) {
      res2 = await addCols({
        columnInputs: withoutIds.map((col) => ({
          ...col,
          columnName: col.columnName.trim(),
        })),
        pageName: 'grn/invoice',
      });
    }
    if (res?.data || res2?.data) {
      toast.success('Columns Configured Successfully');
    }
    setShowModal(false);
  };

  const handleEdit = async (index, data) => {
    setColumns((prev) => {
      return prev.map((col, idx) => {
        if (idx === index) {
          return { ...col, ...data };
        }
        return col;
      });
    });
  };

  const handleDelete = async (id, index) => {
    if (id) {
      const confirm = await customConfirm(
        'Are you sure you want to delete this column? ',
        'error'
      );
      if (!confirm) return;
      const res = await deleteCol({ id });
      if (!res?.error) {
        toast.success('Column deleted successfully');
      }
    } else {
      setColumns((prev) => prev.filter((col, i) => i !== index));
    }
  };

  const handleAddEntry = () => {
    const hasEmpty = columns?.some((col) => col.columnName === '' || !col);
    if (hasEmpty) {
      toast.error('Please fill all the fields');
      return;
    }
    setColumns((prev) => [...prev, { columnName: '', isRequired: false }]);
  };

  const renderCols = (col, index) => {
    return (
      <div
        className={`flex items-center justify-between bg-white rounded-lg border border-gray-200 p-3 mb-3 ${
          col?.isDisabled ? 'bg-gray-50' : ''
        }`}
        key={index}
      >
        <div className="flex-1 mr-4">
          <Input
            value={col?.columnName}
            disabled={col?.isDisabled}
            placeholder="Column Name"
            className={col?.isDisabled ? 'text-gray-500' : ''}
            onChange={(e) => {
              handleEdit(index, { columnName: e.target.value });
            }}
          />
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center">
            <Text className="mr-2">Required</Text>
            <Switch
              checked={col?.isRequired}
              disabled={col?.isDisabled}
              onChange={(e) => {
                handleEdit(index, { isRequired: e });
              }}
            />
          </div>
          <Button
            type="text"
            className={`flex items-center justify-center h-8 w-8 ${col?.isDisabled ? 'invisible' : 'text-red-500 hover:text-red-700'}`}
            disabled={col?.isDisabled}
            onClick={async () => await handleDelete(col?._id, index)}
            icon={<DeleteOutlined />}
          />
        </div>
      </div>
    );
  };

  return (
    <Modal
      title={
        <div className="pb-3">
          <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>
            Manage Columns
          </Title>
          <Text type="secondary">
            Add, edit, or remove columns for your invoice entries. The first
            four columns are required for system functionality.
          </Text>
        </div>
      }
      open={showModal}
      onCancel={() => setShowModal(false)}
      okText="Save Columns"
      cancelText="Cancel"
      onOk={handleOk}
      centered
      width={640}
      styles={{
        body: {
          maxHeight: 'calc(100vh - 220px)',
          overflowY: 'auto',
          padding: '8px',
        },
        header: {
          borderBottom: '1px solid #f0f0f0',
          marginBottom: 0,
          padding: '8px',
        },
        footer: {
          borderTop: '1px solid #f0f0f0',
          padding: '8px',
        },
      }}
    >
      <div className="flex flex-col">
        {fixedAndDisableColumns?.map((col, index) => renderCols(col, index))}
        {columns?.map((col, index) => renderCols(col, index))}
        <Button
          type="dashed"
          className="w-full h-12 flex items-center justify-center mt-2"
          icon={<PlusOutlined />}
          onClick={handleAddEntry}
        >
          Add Column
        </Button>
      </div>
    </Modal>
  );
};

export default InvoiceColumsModal;
