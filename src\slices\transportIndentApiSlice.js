import { apiSlice } from './apiSlice';

const baseRoute = '/v1/transportIndent';

export const TransportIndentApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    createTransportIndent: builder.mutation({
      query: (data) => ({
        url: baseRoute,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['TransportIndent'],
    }),
    getPaginatedTransportIndents: builder.query({
      query: ({ page, limit, ...filters }) => ({
        url: `${baseRoute}/paginated?page=${page}&limit=${limit}&filters=${JSON.stringify({ filters })}`,
        method: 'GET',
      }),
      providesTags: ['TransportIndent'],
    }),
    updateTransportIndent: builder.mutation({
      query: ({ data, id }) => ({
        url: `${baseRoute}/${id}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['TransportIndent'],
    }),
  }),
});

export const {
  useCreateTransportIndentMutation,
  useGetPaginatedTransportIndentsQuery,
  useUpdateTransportIndentMutation,
} = TransportIndentApiSlice;
