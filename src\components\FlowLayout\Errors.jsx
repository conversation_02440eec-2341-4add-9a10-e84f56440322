import { useContext, useEffect, useState } from 'react';
import { ReactComponent as Warning } from '../../assets/svgs/warning.svg';
import {
  calculateValueForViews,
  convertToHrsAndMins,
  getLocalDate,
  getMinutesPassed,
  handlePercentDeicmalValue,
} from '../../helperFunction';
import { MqttContext } from '../../mqtt/DashboardMqttContext';
import { Store } from '../../store/Store';
import ProgressBar from '../global/components/ProgressBar';
import Directions from './Directions';

export default function Errors({
  isMobile,
  isTablet,
  data,
  cuProjects,
  nextCuProjects,
  processGoalViews,
  isReversed,
  isLastInRow,
  isLastItem,
}) {
  const [realtimeBatchSize, setRealtimeBatchSize] = useState(0);
  const [newValues, setNewValues] = useState({});
  const [haltData, setHaltData] = useState({ hrs: 0, mins: 0, status: false });

  const { values } = useContext(MqttContext);
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const cuProject = cuProjects.findLast((i) => i);

  const nextFlowCuPro = nextCuProjects?.findLast((i) => i);

  const isComplete = cuProject?.status === 'complete';

  const batchSizeParam = processGoalViews
    ?.find((item) => item.project.projectId === cuProject?.mqtt?._id)
    ?.parameters?.find((item) => item.name === 'Batch Size');

  const machineList = cuProject?.machineAndOperator?.map(
    (mao) => mao?.machine?.machineId
  );

  const batchData = cuProject?.isMultiProcess
    ? cuProject?.subProcessData
    : cuProject?.batchInfo;

  useEffect(() => {
    if (cuProject?.stopTime && nextFlowCuPro?.startTime) {
      const cuStop = new Date(cuProject?.stopTime);
      const nextCuStart = new Date(nextFlowCuPro?.startTime);

      if (nextCuStart > cuStop) {
        const totalMinutesPassed = getMinutesPassed(
          nextCuStart,
          cuStop,
          defaultParam,
          true
        );

        const [hrs, mins] = convertToHrsAndMins(totalMinutesPassed / 60);
        setHaltData({ hrs, mins, status: true });
      } else {
        setHaltData({ hrs: 0, mins: 0, status: false });
      }
    } else {
      setHaltData({ hrs: 0, mins: 0, status: false });
    }
  }, [cuProject, nextFlowCuPro, defaultParam]);

  useEffect(() => {
    if (batchSizeParam && machineList && newValues) {
      const val = calculateValueForViews(
        batchSizeParam?.formula,
        newValues,
        machineList
      );

      setRealtimeBatchSize(val || 0);
    }
  }, [batchSizeParam, machineList, newValues]);

  useEffect(() => {
    if (cuProject && values) {
      cuProject?.machineAndOperator?.forEach((mao) => {
        const isActive = mao?.status === 'active';

        const machine = mao.machine;

        machine?.devices?.forEach((device) => {
          device?.assignedDevice?.forEach((item) => {
            if (isActive) {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]: values?.[item?.deviceId] || {},
              }));
            } else {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]:
                  cuProject?.lastValues?.[machine?.machineId]?.[
                    item?.deviceId
                  ] || {},
              }));
            }
          });
        });
      });
    }
  }, [cuProject, values]);

  const progressPercent = (realtimeBatchSize / batchData?.['Batch Size']) * 100;

  let progress =
    handlePercentDeicmalValue(progressPercent > 100 ? 100 : progressPercent) ||
    0;

  return (
    <>
      <div
        className={`relative rounded-xl w-[250px] shadow-lg bg-white h-[330px] ${
          !cuProject ? 'flex flex-col gap-2 justify-center items-center' : ''
        }`}
      >
        {cuProject ? (
          <div className="w-full h-full flex flex-col text-[#36434D]">
            <div className="w-full flex justify-between items-center px-5 py-1 border-b">
              <section className="w-[80%]">
                <p className="mt-1 text-[12px] font-semibold">
                  {data?.processName}
                </p>
                <ProgressBar
                  progress={progress}
                  max={100}
                  progressColor={'#77DD77'}
                />
              </section>
              <section className="flex w-[15%] items-end">
                <p className="text-xl">{progress}</p>
                <span className="text-sm mb-0.5">%</span>
              </section>
            </div>
            <div className="overflow-y-scroll no-scrollbar h-full">
              {cuProject?.errorMessages?.map((em) => {
                const machineAndOperator = cuProject?.machineAndOperator?.find(
                  (mao) => mao._id === em.machineAndOperatorId
                );

                return (
                  <div
                    key={em._id}
                    className="w-full flex items-center py-1 px-3 border-b text-[10px]"
                  >
                    <span className="w-[15%]">
                      <Warning />
                    </span>

                    <section className="w-3/4 flex flex-col">
                      <p className="w-full flex justify-between font-semibold">
                        <span>{em?.error?.message}</span>
                        {em?.time ? (
                          <span>
                            {`${new Date(em?.time).getHours()}:${new Date(
                              em?.time
                            ).getMinutes()} `}
                            {getLocalDate(em?.time)}
                          </span>
                        ) : (
                          '-'
                        )}
                      </p>

                      <section className="w-full flex justify-between mt-1 px-2">
                        <section>
                          <p>Worker :</p>
                          <p>{`${em?.operator?.name}`}</p>
                        </section>
                        <section>
                          <p>Machine :</p>
                          <p>{`${machineAndOperator?.machine?.machineName}`}</p>
                        </section>
                      </section>
                    </section>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <>
            <p>No project started</p>
            <p>{data?.processName}</p>
          </>
        )}
        {!isLastItem && (
          <Directions
            isComplete={isComplete}
            scrap={batchData?.['Batch Size'] - realtimeBatchSize}
            isReversed={isReversed}
            isLastInRow={isLastInRow}
            haltData={haltData}
            isMobile={isMobile}
            isTablet={isTablet}
          />
        )}
      </div>
    </>
  );
}
