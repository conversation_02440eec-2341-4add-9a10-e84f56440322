{"name": "vite-project", "private": true, "version": "0.2.1", "type": "module", "scripts": {"start": "vite --host --port 3000 ", "build": "cross-env NODE_OPTIONS='--max-old-space-size=4096' vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "pretty": "prettier --write \"**/*.{js,jsx,json}\"", "prepare": "husky install"}, "dependencies": {"@aws-sdk/credential-providers": "^3.635.0", "@emotion/styled": "^11.11.0", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.11", "@headlessui/react": "^2.1.8", "@heroicons/react": "^2.1.1", "@react-hook/resize-observer": "^1.2.6", "@react-icons/all-files": "^4.1.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@reduxjs/toolkit": "^1.9.5", "@vis.gl/react-google-maps": "^1.1.0", "animated-gradient-background": "^1.0.3", "antd": "^5.21.6", "aws-iot-device-sdk-v2": "^1.20.0", "axios": "^0.27.2", "chart.js": "^4.3.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "deep-object-diff": "^1.1.9", "dompurify": "^3.1.3", "framer-motion": "^10.16.4", "he": "^1.2.0", "html-to-text": "^9.0.5", "html2canvas": "^1.4.1", "idb": "^8.0.3", "immutability-helper": "^3.1.1", "little-calculator": "^1.1.0", "lodash.throttle": "^4.1.1", "lucide-react": "^0.460.0", "mathjs": "^14.4.0", "moment": "^2.29.4", "mqtt": "^4.3.7", "numeral": "^2.0.6", "posthog-js": "^1.161.3", "query-string": "^8.2.0", "quill": "^2.0.2", "react": "^18.2.0", "react-chartjs-2": "^5.1.0", "react-charts": "^3.0.0-beta.57", "react-circular-progressbar": "^2.1.0", "react-csv": "^2.2.2", "react-csv-reader": "^4.0.0", "react-debounce-input": "^3.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.0", "react-hook-form": "^7.34.2", "react-icons": "^4.12.0", "react-intersection-observer": "^9.16.0", "react-js-cron": "^5.0.1", "react-media-recorder": "^1.6.6", "react-modal": "^3.16.1", "react-pdf": "^7.7.3", "react-push-notification": "^1.5.4", "react-qr-code": "^2.0.11", "react-qr-scanner": "^1.0.0-alpha.11", "react-quill": "^2.0.0", "react-redux": "^8.1.1", "react-responsive": "^10.0.0", "react-router-dom": "^6.3.0", "react-scripts": "^5.0.1", "react-select": "^5.8.2", "react-sortable-hoc": "^2.0.0", "react-spreadsheet": "^0.9.4", "react-to-print": "^2.14.13", "react-toastify": "^9.1.3", "react-tooltip": "^5.28.0", "react-top-loading-bar": "^2.3.1", "react-webcam": "^7.2.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "reactflow": "^11.10.1", "recharts": "^2.12.7", "short-unique-id": "^5.2.0", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "tailwind-merge": "^3.0.1", "vite-plugin-svgr": "^3.2.0", "web-vitals": "^3.0.1", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/node": "^20.10.4", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "cross-env": "^7.0.3", "esbuild": "^0.19.2", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "husky": "^8.0.3", "lint-staged": "^14.0.1", "postcss": "^8.4.27", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.3", "vite": "^4.5.3", "vite-plugin-pwa": "^0.16.5", "vite-plugin-static-copy": "^0.17.0", "workbox-window": "^7.0.0"}}