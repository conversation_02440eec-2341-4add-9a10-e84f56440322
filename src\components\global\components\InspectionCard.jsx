import { getLocalDateTime } from '../../../helperFunction';

export default function InspectionCard({
  workOrderId,
  outQuantity,
  createdAt,
  onClick,
  active,
}) {
  return (
    <div
      className={`${active ? 'bg-slate-100' : 'bg-white'}  rounded-xl w-full shadow-md shadow-black/10 p-4 text-sm cursor-pointer`}
      onClick={onClick}
    >
      <h4 className="text-lg">#{workOrderId?.workOrderId} - Outsource Job</h4>
      <p>Created At - {getLocalDateTime(createdAt)}</p>
      <p>Outsource Quantity - {outQuantity}</p>
    </div>
  );
}
