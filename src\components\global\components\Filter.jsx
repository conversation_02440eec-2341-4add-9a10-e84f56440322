import { Badge, Popover } from 'antd';
import moment from 'moment';
import { useState } from 'react';
import { FiFilter } from 'react-icons/fi';
import { Label } from '../../v2';
import Button from './Button';
import MultiSelect from './MultiSelect';
import Select from './Select';

const Filter = ({
  headingfilteroptions = [
    { label: 'Add Your Option', value: 'Add Your Options' },
  ],
  secondfilterdata = [],
  setSelectedHeading,
  setSelectedValue,
  className,
  isDisabledSecondFilter = false,
}) => {
  const [SelectedHeadingOption, setSelectedHeadingOption] = useState('');
  const [SelectedHeadingValue, setSelectedHeadingValue] = useState([]);
  const [SecondFilterOptions, setSecondFilterOptions] = useState([]);
  const getValueByStringPath = (obj, paths) => {
    // Ensure paths is an array
    const pathArray = Array.isArray(paths) ? paths : [paths];

    for (const path of pathArray) {
      const keys = path.split('.');
      let value = obj;
      let found = true;

      for (const key of keys) {
        if (value && Object.hasOwn(value, key)) {
          value = value[key];
        } else {
          found = false;
          break;
        }
      }

      if (found) {
        return value;
      }
    }

    return null;
  };
  const isObject = (checkvalue) => {
    try {
      const value = JSON.parse(checkvalue);
      return value instanceof Object;
    } catch (error) {
      return false;
    }
  };

  // function to handle merged fields - shows combined label but uses first field's value
  const handleMergedFields = (each, mergedConfig) => {
    if (!mergedConfig) return;
    const values = mergedConfig?.fields
      .map((field) => {
        const value = each[field];
        return value && value.toString().trim() ? value.toString().trim() : '';
      })
      .filter(Boolean);

    if (values.length > 0) {
      const combinedLabel = values.join(' '); // Display combined name

      return JSON.stringify({
        label: combinedLabel,
        value: each[mergedConfig?.fields[0]],
      });
    }
    return null;
  };

  const handelSetOptions = async (e) => {
    const selectedValue = e.target.value;
    let mergedConfig = null;

    if (isObject(selectedValue)) {
      const parsed = JSON.parse(selectedValue);
      if (parsed.type === 'merged' && parsed.fields) {
        mergedConfig = parsed;
      }
    }

    const options = await Promise.all(
      secondfilterdata?.map(async (each) => {
        // Handle merged fields
        if (mergedConfig) {
          return handleMergedFields(each, mergedConfig);
        }

        if (isObject(selectedValue)) {
          const parsed = JSON.parse(selectedValue);
          const value = await getValueByStringPath(each, parsed.value);
          const label = await getValueByStringPath(each, parsed.label);

          if (value && label) {
            if (
              !Array.isArray(parsed?.label) &&
              parsed?.label?.toLowerCase()?.includes('date')
            ) {
              const dateOnly = new Date(value).toLocaleDateString('en-IN');
              return JSON.stringify({
                label: dateOnly,
                value: dateOnly,
              });
            } else {
              return JSON.stringify({
                label: label.toString(),
                value,
              });
            }
          }
        } else {
          const fieldValue = each?.[selectedValue];
          if (selectedValue === 'assignedUsers' && Array.isArray(fieldValue)) {
            return fieldValue.map((item) => {
              if (item._id && item.name) {
                return JSON.stringify({
                  label: item.name,
                  value: item._id,
                });
              }
            });
          } else if (selectedValue === 'stores' && Array.isArray(fieldValue)) {
            return fieldValue.map((item) => {
              if (item._id && item.name) {
                return JSON.stringify({
                  label: item.name,
                  value: item._id,
                });
              }
            });
          } else if (selectedValue === 'tags' && Array.isArray(fieldValue)) {
            return fieldValue.map((item) => {
              if (item.name) {
                return JSON.stringify({
                  label: item.name,
                  value: item.name,
                });
              }
            });
          } else if (Array.isArray(fieldValue)) {
            return fieldValue.map((val) =>
              JSON.stringify({
                label: val.toString(),
                value: val,
              })
            );
          } else if (
            typeof fieldValue === 'string' &&
            fieldValue.includes(',')
          ) {
            return fieldValue.split(',').map((val) =>
              JSON.stringify({
                label: val.trim(),
                value: val.trim(),
              })
            );
          } else if (
            selectedValue.toLowerCase() === 'createdat' ||
            selectedValue?.toLowerCase()?.includes('time') ||
            selectedValue?.toLowerCase()?.includes('manufacturingdate') ||
            selectedValue?.toLowerCase()?.includes('date')
          ) {
            const dateOnly = new Date(fieldValue).toLocaleDateString('en-IN');
            return JSON.stringify({
              label: dateOnly,
              value: dateOnly,
            });
          } else if (
            (selectedValue === 'workOrderId' || selectedValue === 'bomId') &&
            each[selectedValue]
          ) {
            return JSON.stringify({
              label: each[selectedValue]?.name || each[selectedValue],
              value: each[selectedValue]?._id || each[selectedValue],
            });
          } else {
            return JSON.stringify({
              label: fieldValue?.toString(),
              value: fieldValue,
            });
          }
        }
      })
    );

    const flattenedOptions = options.flat();
    const unique_options = [...new Set(flattenedOptions)]
      .map((unique) => unique && JSON.parse(unique))
      .filter(
        (option) =>
          option &&
          Object.keys(option).length > 0 &&
          option.label !== '' &&
          option.value !== ''
      );
    setSecondFilterOptions(unique_options);
  };

  const handelParseDateOption = (e) => {
    if (
      e.target?.action === 'remove' &&
      e.target?.type === 'multiselect' &&
      e.target.value?.length === 0
    ) {
      setSelectedValue(JSON.stringify([]));
    }

    let isMergedField = false;
    if (isObject(SelectedHeadingOption)) {
      const parsed = JSON.parse(SelectedHeadingOption);
      isMergedField = parsed.type === 'merged';
    }
    if (
      SelectedHeadingOption === 'createdAt' ||
      SelectedHeadingOption?.toLowerCase()?.includes('time') ||
      SelectedHeadingOption?.toLowerCase()?.includes('manufacturingdate') ||
      SelectedHeadingOption?.toLowerCase()?.includes('date')
    ) {
      setSelectedHeadingValue(e.target.value);
      const selectedDates = e.target.value?.map((el) => {
        const date = moment(el.value, 'DD/MM/YYYY');
        return date.isValid() ? date.toISOString() : null;
      });
      setSelectedValue(JSON.stringify(selectedDates));
      return;
    } else if (isObject(SelectedHeadingValue) && !isMergedField) {
      setSelectedHeadingValue(e.target.value);
      const selectedDates = e.target.value?.map((el) => {
        const date = moment(el.value, 'DD/MM/YYYY');
        return date.isValid() ? date.toISOString() : null;
      });
      setSelectedValue(JSON.stringify(selectedDates));
      return;
    }

    setSelectedHeadingValue(e.target.value);
    const selectedData = e.target.value?.map((el) => el.value);
    setSelectedValue(JSON.stringify(selectedData));
  };

  return (
    <Popover
      trigger="click"
      placement="bottomRight"
      content={
        <div className="p-2 bg-white rounded-lg w-[16rem]">
          <div className="space-y-4">
            <div className="wrapper">
              <Label className="text-sm font-medium text-gray-700 mb-1">
                Select Column
              </Label>
              <Select
                className="rounded-md border-gray-300"
                options={headingfilteroptions}
                value={SelectedHeadingOption}
                onChange={(e) => {
                  handelSetOptions(e);
                  setSelectedHeadingOption(e.target.value);
                  let selectedValue = e.target.value;

                  if (isObject(selectedValue)) {
                    const parsed =
                      typeof selectedValue === 'string'
                        ? JSON.parse(selectedValue)
                        : selectedValue;

                    if (parsed.type === 'merged') {
                      // For backend, send only the first field
                      selectedValue = parsed.fields[0];
                    } else {
                      selectedValue =
                        parsed.path || parsed.value || parsed.label;
                    }
                  }

                  setSelectedHeading(selectedValue);
                  if (!isDisabledSecondFilter) {
                    setSelectedHeadingValue('');
                    setSelectedValue('');
                  }
                }}
              />
            </div>
            {!isDisabledSecondFilter && (
              <div className="wrapper">
                <Label className="text-sm font-medium text-gray-700 mb-1">
                  Select Entry
                </Label>
                <MultiSelect
                  value={SelectedHeadingValue}
                  options={SecondFilterOptions}
                  onChange={handelParseDateOption}
                />
              </div>
            )}
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors duration-200 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              onClick={() => {
                setSelectedHeadingOption('');
                setSelectedHeading('');
                setSelectedHeadingValue('');
                setSelectedValue();
              }}
            >
              Reset
            </Button>
          </div>
        </div>
      }
    >
      <Badge count={SelectedHeadingOption && SelectedHeadingValue && 1}>
        <button className={`cursor-pointer ${className}`}>
          <FiFilter className="text-gray-500 w-5 h-5" />
        </button>
      </Badge>
    </Popover>
  );
};

export default Filter;
