import { BiH<PERSON>, BiShow } from 'react-icons/bi';
import {
  getCorrespondingConversionFactor,
  getUOMDropDownOptions,
} from '../../helperFunction';
import Input from '../global/components/Input';
import Table from '../global/components/Table';
// Helper functions for handling various actions within the component
import SelectV2 from '../global/components/SelectV2';
import {
  handelpartChange,
  handleDelete,
  handleHideColumns,
  handleHideCustomColumns,
  handleInputChange,
} from './CreateRfqHelperFun';
import { Button } from 'antd';
import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
/*
 RfqProductDetails component renders a detailed view of the products for an RFQ (Request for Quotation).
 This component includes a table with several columns for displaying and editing product details like item name, UOM (Unit of Measure), quantity, and any custom columns added by the user.
 Users can show/hide specific columns and add new custom columns as needed.
 */
const RfqProductDetails = ({
  productHideColumnStatus, //for tracking the visibility status of standard columns.
  setProductHideColumnStatus, //Function to update visibility status of standard columns.
  allCustomCols, //Array of custom columns defined by the user.
  productHideCustomColumnStatus, //for tracking the visibility status of custom columns.
  setProductHideCustomColumnStatus, //Function to update visibility status of custom columns.
  setShowCustomColumnModal, //Function to show or hide the modal for adding custom columns.
  items, //Array of product items being displayed in the table.
  setItems, //Function to update the list of product items.
  uom, //Array of unit of measures corresponding to each product item.
  setUom, //Function to update the unit of measures for product items.
  quantity, //Array of quantities corresponding to each product item.
  setQuantity, //Function to update the quantities for product items.
  setSearchTerm, //Function to update the search term for filtering product items.
  data, //Array of data objects available for selection in the dropdowns.
  debounceValue, //Debounced value for handling input delays.
  dropdowns, //Object containing dropdown values and options.
  columnInputs, // Array of inputs corresponding to custom columns.
  getColumnValue, //Function to get the value of a specific custom column.
  isEdit, //Boolean indicating if the RFQ is in edit mode.
  itemTypes, //Array of item types available for selection.
  setItemTypes, //Function to update the list of item types.
}) => {
  return (
    <div className="flex w-full mb-3 mt-5">
      <div className="flex flex-col gap-3 w-full">
        {/* Header for the product details section */}
        <div className="w-full mr-4 mb-3">
          <div className="flex">
            <div className="flex mt-10 mb-5">
              <h3>Product Details</h3>
              <span className="text-red-500 text-xl">*</span>
            </div>
          </div>
          <div className="table-container mt-4 overflow-x-auto border rounded-md shadow-sm">
            {/* Table for displaying product details */}
            <Table className="w-full border-b mb-[160px]">
              <Table.Head>
                <Table.Row className={'border-b'}>
                  <Table.Th className="border-r">
                    <div>
                      Sr. no
                      <Table.Row></Table.Row>
                    </div>
                  </Table.Th>
                  <Table.Th className="border-r">
                    {/* Column for Item Details */}
                    <div className="flex gap-2">
                      <span>Item details</span>
                      {productHideColumnStatus?.name && (
                        <BiHide
                          className={`text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns(
                              'name',
                              setProductHideColumnStatus
                            );
                          }}
                        />
                      )}
                      {!productHideColumnStatus?.name && (
                        <BiShow
                          className={`text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns(
                              'name',
                              setProductHideColumnStatus
                            );
                          }}
                        />
                      )}
                    </div>
                  </Table.Th>
                  <Table.Th className="border-r">
                    <div className="flex gap-2">
                      {/* Column for UOM */}
                      <span>UOM</span>
                      {productHideColumnStatus?.uom && (
                        <BiHide
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns(
                              'uom',
                              setProductHideColumnStatus
                            );
                          }}
                        />
                      )}
                      {!productHideColumnStatus?.uom && (
                        <BiShow
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns(
                              'uom',
                              setProductHideColumnStatus
                            );
                          }}
                        />
                      )}
                    </div>
                  </Table.Th>
                  <Table.Th className="border-r">
                    <div className="flex gap-2">
                      {/* Column for Quantity */}
                      <span>Quantity</span>
                      {productHideColumnStatus?.quantity && (
                        <BiHide
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns(
                              'quantity',
                              setProductHideColumnStatus
                            );
                          }}
                        />
                      )}
                      {!productHideColumnStatus?.quantity && (
                        <BiShow
                          className={`float-right text-base cursor-pointer`}
                          onClick={() => {
                            handleHideColumns(
                              'quantity',
                              setProductHideColumnStatus
                            );
                          }}
                        />
                      )}
                    </div>
                  </Table.Th>
                  {/* Columns for Custom Fields */}
                  {allCustomCols?.map((col, idx) => (
                    <Table.Th
                      key={idx}
                      className={`!text-center mx-2 border-r`}
                    >
                      <div className="flex gap-2">
                        {' '}
                        <span>{col?.columnName}</span>
                        {productHideCustomColumnStatus?.[col?.columnName] && (
                          <BiHide
                            className={`float-right text-base cursor-pointer`}
                            onClick={() => {
                              handleHideCustomColumns(
                                col?.columnName,
                                setProductHideCustomColumnStatus
                              );
                            }}
                          />
                        )}
                        {!productHideCustomColumnStatus?.[col?.columnName] && (
                          <BiShow
                            className={`float-right text-base cursor-pointer`}
                            onClick={() => {
                              handleHideCustomColumns(
                                col?.columnName,
                                setProductHideCustomColumnStatus
                              );
                            }}
                          />
                        )}
                      </div>
                    </Table.Th>
                  ))}
                  {/* Button for adding new custom columns */}
                  <Table.Th className="border-r">
                    <Button
                      onClick={() => setShowCustomColumnModal(true)}
                      type="primary"
                      icon={<PlusCircleOutlined />}
                      size="small"
                    />
                  </Table.Th>
                </Table.Row>
              </Table.Head>
              {/* Table body with product items */}
              <Table.Body>
                {items?.map((part, index) => {
                  const conversion = getCorrespondingConversionFactor(
                    uom[index],
                    part?.item
                  );
                  return (
                    <Table.Row key={index}>
                      <Table.Td className={'!min-w-[3rem] !w-[3rem] border-r'}>
                        {index + 1}
                      </Table.Td>
                      {part?.manualEntry !== undefined ? (
                        <Table.Td className="border-r">
                          <Input
                            value={part?.manualEntry || ''}
                            onChange={(e) =>
                              handleInputChange(index, e.target.value, setItems)
                            }
                          />
                        </Table.Td>
                      ) : (
                        <Table.Td
                          className={'!min-w-[15rem] !w-[25rem] border-r'}
                        >
                          {/* Dropdown for selecting item */}
                          <SelectV2
                            placeholder={`Select items`}
                            size="small"
                            onSearch={(e) => setSearchTerm(e)}
                            doSearch={false}
                            onChange={(e) => {
                              if (e.target.value === '+') {
                                handelpartChange(
                                  { target: { value: '+' } },
                                  index,
                                  setItems,
                                  data
                                );
                              } else {
                                handelpartChange(
                                  { target: { value: e.target.value } },
                                  index,
                                  setItems,
                                  data
                                );
                              }
                            }}
                            value={
                              part?.value ||
                              part?.item?._id ||
                              items[index]?._id ||
                              part?.item?.value
                            }
                            closeMenuOnSelect
                            className="w-60 text-ellipsis"
                            options={[
                              { label: '+ Manual Entry', value: '+' },
                              ...(data?.map((e) => {
                                return {
                                  ...e,
                                  value: e?.value,
                                  label: e?.name,
                                  searchTerm: debounceValue,
                                };
                              }) || []),
                            ]}
                          />
                        </Table.Td>
                      )}

                      <Table.Td className={'!min-w-[5rem] !w-[10rem] border-r'}>
                        {/* Dropdown for selecting UOM */}
                        <SelectV2
                          size="small"
                          name="UOM"
                          id="UOM"
                          placeholder="Select UOM"
                          onChange={(e) =>
                            setUom((prev) => {
                              const newUom = [...prev];
                              newUom[index] = e.target.value;
                              return newUom;
                            })
                          }
                          value={uom[index] || part?.item?.uom}
                          closeMenuOnSelect={true}
                          className="!text-xsm !min-w-[2rem] !w-[9rem] !placeholder-text-sm"
                          options={getUOMDropDownOptions(part?.item, dropdowns)}
                        />
                        {conversion && (
                          <span
                            style={{ whiteSpace: 'nowrap' }}
                            className=" !text-gray-500"
                          >
                            {'( =' +
                              conversion?.conversionValue +
                              ' ' +
                              conversion?.conversionUnit +
                              ')'}
                          </span>
                        )}
                      </Table.Td>
                      <Table.Td className="border-r">
                        <div>
                          {/* Input for entering quantity */}
                          <Input
                            onChange={(e) =>
                              setQuantity((prev) => {
                                const newQuant = [...prev];
                                newQuant[index] = parseFloat(e.target.value);
                                return newQuant;
                              })
                            }
                            type="number"
                            value={quantity[index] || 0}
                            placeholder="Enter Quantity"
                            className="!min-w-[6rem] !w-[10rem]"
                          />
                        </div>
                      </Table.Td>
                      {/* Rendering custom column inputs */}
                      {columnInputs?.length >= 1 &&
                        columnInputs?.map((columnInput, idx) => {
                          if (columnInput) {
                            return (
                              <Table.Td key={idx} className="border-r">
                                <Input
                                  value={getColumnValue(
                                    part?.customColumns,
                                    columnInput.columnName
                                  )}
                                  className="w-24"
                                  type="text"
                                  onChange={(e) => {
                                    const newItems = [...items];
                                    const newCustomColumns = [
                                      ...(newItems[index]?.customColumns || []),
                                    ];
                                    const columnIndex =
                                      newCustomColumns?.findIndex((el) =>
                                        Object.prototype.hasOwnProperty.call(
                                          el,
                                          columnInput.columnName
                                        )
                                      );

                                    if (columnIndex >= 0) {
                                      newCustomColumns[columnIndex] = {
                                        ...newCustomColumns[columnIndex],
                                        [columnInput.columnName]:
                                          e.target.value,
                                      };
                                    } else {
                                      newCustomColumns?.push({
                                        [columnInput.columnName]:
                                          e.target.value,
                                      });
                                    }
                                    newItems[index] = {
                                      ...newItems[index],
                                      customColumns: newCustomColumns,
                                    };

                                    setItems(newItems);
                                  }}
                                />
                              </Table.Td>
                            );
                          }
                        })}
                      {/* Button to delete the current row */}
                      <Table.Td>
                        <Button
                          onClick={() =>
                            handleDelete(
                              part?.item?.value || part?._id,
                              setItems,
                              isEdit,
                              itemTypes,
                              setItemTypes
                            )
                          }
                          icon={<DeleteOutlined />}
                          size="small"
                          danger
                        />
                      </Table.Td>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RfqProductDetails;
