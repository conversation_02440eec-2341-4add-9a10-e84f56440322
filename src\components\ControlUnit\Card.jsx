import { useState } from 'react';
// import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import dateicon from '../../assets/images/date.png';
import jobicon from '../../assets/images/jobs.png';
import { useLazyGetCuProjectForIntervalQuery } from '../../slices/CuProjectNewApiSlice';
import CircularUploadButton from '../CircularUploadButton';
import Button from '../global/components/Button';
import Modal from '../global/components/Modal';
import StatusBar from './StatusBar';

function Card({
  title,
  children,
  onClick,
  id,
  jobs,
  createdAt,
  bom,
  processCategory,
  processName,
  isSelected,
  // setSelectedData,
  setShowModal,
  cuStatus,
  borderColors = false,
  elementid = '',
  // type = '',
  poId = '',
  setSelectedBom,
  getSelectedBoms,
  jobFetchQuery,
  media = null,
}) {
  // const navigate = useNavigate();
  const [showPrev, setShowPrev] = useState(false);
  const [prevCuProject, setPrevCuProject] = useState({});
  const [getCuProjectForInterval] = useLazyGetCuProjectForIntervalQuery();

  const Colors = {
    Inhouse: 'bg-yellow-500',
    QC: 'bg-green-500',
    Outsource: 'bg-orange-500',
    Assembly: 'bg-red-500',
  };

  return (
    <>
      {showPrev && (
        <Modal
          onCloseModal={() => setShowPrev(false)}
          title="Previous Process Information"
          isSubmitRequired={false}
          canSubmit={false}
        >
          {() => {
            return (
              <section>
                <p className="flex items-center justify-between font-semibold">
                  Stop Location :{' '}
                  <span className="font-normal">
                    {prevCuProject?.stopLocation || 'N/A'}
                  </span>{' '}
                </p>
                <p className="flex items-center justify-between font-semibold">
                  Batch Name :{' '}
                  <span className="font-normal">
                    {prevCuProject?.batchInfo?.batchName || 'N/A'}
                  </span>{' '}
                </p>
                <p className="flex items-center justify-between font-semibold">
                  Batch Size :{' '}
                  <span className="font-normal">
                    {prevCuProject?.batchInfo?.newBatchSize ||
                      prevCuProject?.batchInfo?.['Batch Size'] ||
                      'N/A'}
                  </span>{' '}
                </p>
                <p className="flex items-center justify-between font-semibold">
                  Operator :{' '}
                  <span className="font-normal">
                    {prevCuProject?.type === 'Inhouse' ? (
                      <>
                        {prevCuProject?.machineAndOperator
                          .slice(-1)[0]
                          ?.operator?.slice(-1)[0]?.user?.name || ''}
                      </>
                    ) : (
                      <></>
                    )}
                  </span>{' '}
                </p>
              </section>
            );
          }}
        </Modal>
      )}
      <div
        className={`px-4 py-2 mt-2 !w-full   bg-white  rounded-[8px] border overflow-hidden flex flex-col cursor-pointer h-fit shadow-sm ${
          isSelected ? 'border-[2px] border-[#b7b7ff]' : ''
        } !w-full flex-shrink-0`}
        onClick={onClick}
        id={elementid}
      >
        {id && (
          <div className="flex items-center justify-start gap-x-2 mt-1">
            <span className="text-[11px] pt-[0.15rem] w-[2rem] h-[2rem] flex items-center justify-center bg-blue-500 text-white rounded-full">
              {id?.slice(0, 1)}
            </span>
            <div className="leading-4 ">
              {title && <p className="text-[12px] font-semibold"> {title}</p>}
              {id && <p className="text-[12px] text-slate-500">{id}</p>}
            </div>
          </div>
        )}
        {processName && (
          <div className="flex items-center justify-start gap-x-2">
            <span
              className={`text-[11px] w-[2rem] h-[2rem] flex items-center justify-center text-white rounded-full ${Colors[processCategory]}`}
            >
              {processName?.slice(0, 1).toUpperCase()}
            </span>{' '}
            <div className="leading-3">
              <p className="text-[10px] font-semibold"> {processName}</p>
              {processCategory && (
                <p className="text-[10px]">{processCategory}</p>
              )}
            </div>
            <div className="w-full flex justify-end">
              <button
                onClick={(e) => {
                  e.stopPropagation();

                  if (jobFetchQuery !== 'NoPrevProcess') {
                    getCuProjectForInterval({
                      query: jobFetchQuery?.query,
                    }).then((res) => {
                      setPrevCuProject(res.data?.cuProject);
                      setShowPrev(true);
                    });
                  } else {
                    toast.error('No previous process found');
                  }
                }}
                type="button"
                className="text-[10px] font-medium text-blue-500 mt-3 flex items-center  gap-x-2 border w-fit px-1 rounded-2xl border-blue-600 mb-1"
              >
                {' '}
                Previous Info
              </button>
            </div>
          </div>
        )}

        {cuStatus ? (
          <>
            <p className="text-[10px] mt-2 font-medium text-slate-500  flex items-center justify-start gap-x-2">
              <img src={jobicon} className="block w-[12px] h-[12px]" alt="" />{' '}
              <span className="capitalize">{cuStatus?.status}</span>
            </p>
            <p className="text-[10px] text-slate-500 font-medium flex items-center justify-start gap-x-2">
              <img src={dateicon} className="block w-[12px] h-[12px]" alt="" />
              Start:{' '}
              {cuStatus?.startTime
                ? new Date(cuStatus?.startTime).toLocaleString('en-In')
                : '-'}
            </p>
            <p className="text-[10px] text-slate-500 font-medium flex items-center justify-start gap-x-2">
              <img src={dateicon} className="block w-[12px] h-[12px]" alt="" />
              Stop:{' '}
              {cuStatus?.stopTime
                ? new Date(cuStatus?.stopTime).toLocaleString('en-In')
                : '-'}
            </p>
          </>
        ) : (
          <div className="flex  justify-between ">
            <div className="mt-2 flex items-center w-full justify-between">
              {jobs && (
                <div className="flex items-center justify-start gap-x-2 mb-1 text-nowrap text-[12px] text-slate-500">
                  <img
                    src={jobicon}
                    className="block w-[18px] h-[18px]"
                    alt=""
                  />
                  <p className="pt-[0.1rem] font-medium">{jobs?.length} Jobs</p>
                </div>
              )}

              {createdAt && (
                <div className="flex items-center justify-start gap-x-2 mb-1 text-nowrap text-[12px] text-slate-500">
                  <img
                    src={dateicon}
                    className="block w-[18px] h-[18px]"
                    alt=""
                  />
                  <p className="pt-[0.1rem] font-medium">
                    {new Date(createdAt).toLocaleDateString('en-In')}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="mt-3">
          <StatusBar
            id={cuStatus ? cuStatus?._id : poId}
            type={cuStatus ? 'cu' : 'wo'}
          />
        </div>

        <div
          className={` rounded-full border-b-2 my-3 ${
            borderColors
              ? cuStatus?.status === 'active'
                ? 'border-green-400'
                : cuStatus?.status === 'complete'
                  ? 'border-blue-400'
                  : 'border-red-300'
              : 'border-slate-400'
          }`}
        />

        <div className="flex items-center justify-between gap-x-1">
          {bom?.length !== 0 && id && (
            <Button
              className="!text-[10px] !max-w-[6rem] !bg-transparent !text-slate-500 border-[2px] !px-[8px] !py-[2px] !border-[#c6c6c6ab] flex items-center justify-between"
              onClick={(e) => {
                e.stopPropagation();
                let ids = bom || [];
                getSelectedBoms({ data: { ids } })
                  .unwrap()
                  .then((bom) => {
                    setSelectedBom(bom);
                  });
                setShowModal(true);
              }}
            >
              <span className="w-[8px] h-[8px] rounded-full bg-blue-500"></span>
              BOM
            </Button>
          )}
          {jobs && (
            <div>
              <CircularUploadButton
                size={8}
                backgroundColor="bg-green-500"
                hoverColor="bg-green-600"
                title={title}
                isPreviewOnly={true}
                mediaArr={media}
                media={media?.[0] || null}
              />
            </div>
          )}
        </div>
        <section className="w-full text-[11px]">{children}</section>
      </div>
    </>
  );
}

export default Card;
