import { useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import { mobileWidth, tabletWidth } from '../../helperFunction';
import Modal from '../global/components/Modal';
import { getNextColumns, handleShiftColumn } from './kanbanFunctions';

const ShiftModal = ({
  openShiftModal,
  setOpenShiftModal,
  selectedColumns,
  setSelectedColumns,
  column,
  partiallyDone,
  dispatch,
  shiftKanbanColumn,
  defaultParam,
  card,
  shiftLoading,
  isSingleColumnMode,
}) => {
  const [selectAll, setSelectAll] = useState(false);
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });

  const handleSelectAll = (e) => {
    if (e.target.checked) {
      const allValues = getNextColumns({ defaultParam, column })?.map(
        (item) => item.label
      );
      setSelectedColumns(allValues);
    } else {
      setSelectedColumns([]);
    }
    setSelectAll(e.target.checked);
  };

  const handleCheckBoxChange = (event, item) => {
    setSelectedColumns((prev) => {
      if (event.target.checked) {
        return [...prev, item];
      } else {
        return prev.filter((data) => data !== item);
      }
    });
    setSelectAll(false);
  };

  return (
    <Modal
      isMobile={isMobile}
      isTablet={isTablet}
      title="Select Next Columns"
      description="Select one or more columns to shift the card to"
      onCloseModal={() =>
        setOpenShiftModal({
          open: false,
          isPartial: false,
        })
      }
      onSubmit={() => {
        if (selectedColumns?.length === 0) {
          toast.error('Please select next columns');
        } else {
          handleShiftColumn({
            isPartial: openShiftModal?.isPartial,
            nextColumns: selectedColumns,
            selectedColumns,
            column,
            partiallyDone,
            setOpenShiftModal,
            dispatch,
            shiftKanbanColumn,
            defaultParam,
            card,
          });
        }
      }}
      btnIsLoading={shiftLoading}
    >
      {() => {
        return (
          <>
            {!isSingleColumnMode && (
              <div className="flex gap-x-4">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={handleSelectAll}
                />
                <label>Select All</label>
              </div>
            )}
            {getNextColumns({ defaultParam, column })?.map((column, idx) => (
              <div className="flex gap-x-4" key={idx}>
                {isSingleColumnMode ? (
                  <input
                    type="radio"
                    name="singleColumnSelect"
                    checked={selectedColumns[0] === column.label}
                    onChange={() => setSelectedColumns([column?.label])}
                  />
                ) : (
                  <input
                    type="checkbox"
                    checked={selectedColumns.includes(column.label)}
                    onChange={(e) => handleCheckBoxChange(e, column.label)}
                  />
                )}

                <label>{column.label}</label>
              </div>
            ))}
          </>
        );
      }}
    </Modal>
  );
};

export default ShiftModal;
