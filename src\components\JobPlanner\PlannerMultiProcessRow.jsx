import { Fragment, useState } from 'react';
import { generateDateTimeStringProduction } from '../../helperFunction';
import { Row, Td } from '../global/components/Table';
import MachineRow from './MachineRow';

const PlannerMultiProcessRow = ({
  process = {},
  currBatch = {},
  setGoalsTable,
  machines,
  machineSchedules,
  setMachineSchedules,
  customMachineTimes,
  allLocations = { allLocations },
  usersForAssigning,
}) => {
  const [expand, setExpand] = useState(true);
  const [expandMachineRow, setExpandMachineRow] = useState(true);

  const isInhouse = process?.processCategory === 'Inhouse';

  return (
    <>
      <Row className="border-l-2 border-l-green-400">
        <td
          onClick={() => setExpandMachineRow((prev) => !prev)}
          className={`px-4 py-3 font-medium ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        >
          <span className="link-hover">{process.processName}</span>
        </td>
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        >
          {currBatch?.['Batch Size']}
        </td>
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        >
          {currBatch?.newBatchSize || currBatch?.['Batch Size']}
        </td>
        <td onClick={() => setExpand((prev) => !prev)}>
          <span className="link-hover">
            {' '}
            +{currBatch?.subProcessData?.length}
          </span>
        </td>
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        ></td>{' '}
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        ></td>{' '}
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        ></td>
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        >
          <input
            type="datetime-local"
            className="w-full outline-none bg-transparent"
            name="startDate"
            readOnly
            value={
              currBatch?.startDate
                ? generateDateTimeStringProduction(currBatch?.startDate)
                : ''
            }
          />
        </td>
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        >
          <input
            type="datetime-local"
            readOnly
            className="w-full outline-none bg-transparent"
            name="stopDate"
            value={
              currBatch?.stopDate
                ? generateDateTimeStringProduction(currBatch?.stopDate)
                : ''
            }
          />
        </td>
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        >
          {'-'}
        </td>
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        >
          {currBatch?.duration || '-'}
        </td>
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        ></td>{' '}
        <td
          className={`px-4 py-3 ${
            currBatch?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        ></td>{' '}
      </Row>
      {expand &&
        currBatch?.subProcessData?.map((subProcess, sIdx) => (
          <Fragment key={sIdx}>
            <Row className={'border-l-2 border-l-blue-400'}>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              ></Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              >
                {subProcess?.['Batch Size']}
              </Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              >
                {subProcess?.newBatchSize || subProcess?.['Batch Size']}
              </Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              >
                {subProcess?.process}
              </Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              ></Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              ></Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              ></Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              >
                <input
                  type="datetime-local"
                  readOnly
                  className="w-full outline-none bg-transparent"
                  name="startDate"
                  value={
                    subProcess?.startDate
                      ? generateDateTimeStringProduction(subProcess?.startDate)
                      : ''
                  }
                />
              </Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              >
                {' '}
                <input
                  type="datetime-local"
                  readOnly
                  className="w-full outline-none bg-transparent"
                  name="stopDate"
                  value={
                    subProcess?.stopDate
                      ? generateDateTimeStringProduction(subProcess?.stopDate)
                      : ''
                  }
                />
              </Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              >
                {subProcess?.duration || ''}
              </Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              ></Td>
              <Td
                className={
                  subProcess?.status
                    ? 'pointer-events-none !bg-gray-disabled'
                    : ''
                }
              ></Td>
            </Row>
            {isInhouse && expandMachineRow && (
              <MachineRow
                machines={machines}
                machineSchedules={machineSchedules}
                setMachineSchedules={setMachineSchedules}
                flowId={process._id}
                batchNo={currBatch?.batchNo}
                customMachineTimes={customMachineTimes}
                subProcess={subProcess}
                subProIdx={sIdx}
                allLocations={allLocations}
                setGoalsTable={setGoalsTable}
                usersForAssigning={usersForAssigning}
              />
            )}
          </Fragment>
        ))}
    </>
  );
};

export default PlannerMultiProcessRow;
