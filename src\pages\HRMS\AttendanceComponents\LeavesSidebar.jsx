import {
  ClockCircleOutlined,
  DollarOutlined,
  IdcardOutlined,
  PhoneOutlined,
  UserOutlined,
  WhatsAppOutlined,
} from '@ant-design/icons';

import {
  FaBusinessTime,
  FaCalendarCheck,
  FaCalendarDay,
  FaClock,
  FaMoon,
} from 'react-icons/fa';

import { Tag } from 'antd';
import RightSidebar from '../../../components/global/components/RightSidebar';

const GeneralDetailsRow = ({
  name,
  Icon,
  value,
  color,
  className,
  tag,
  tagColor,
}) => {
  return (
    <div
      className={`flex items-center justify-between border-b pb-2 ${className}`}
    >
      <div className="flex items-center gap-2">
        <Icon className={`${color} text-xl`} />
        <span className="font-medium text-gray-600">{name}</span>
      </div>
      {tag ? (
        <Tag color={tagColor}>{value}</Tag>
      ) : (
        <span className="text-gray-800 font-medium">{value}</span>
      )}
    </div>
  );
};

const LeavesSidebar = ({
  openSideBar,
  setOpenSideBar,
  record,
  getHoursWorkedYesterday,
  getHoursWorkedThisMonth,
  getDaysWorkedThisMonth,
  startDate,
  endDate,
}) => {
  function getNightShiftsWorked(attendance, startDate, endDate) {
    if (!attendance) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);
    let nightShiftCount = 0;

    for (const date in attendance) {
      const entryDate = new Date(date);
      const inRange = entryDate >= start && entryDate <= end;

      const entry = attendance[date];

      if (
        inRange &&
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        const login = new Date(entry.login);
        const logout = new Date(entry.logout);

        const loginHour = login.getHours();
        const workedHours = (logout - login) / (1000 * 60 * 60); // ms → hours

        // Case 1: Login at or after 6 PM → Night shift if ≥ 8 hours worked
        if (loginHour >= 18 && workedHours >= record?.workingHours) {
          nightShiftCount++;
        }
        // Case 2: Login before 6 PM → Night shift if ≥ 16 hours worked
        else if (loginHour < 18 && workedHours >= record?.workingHours * 2) {
          nightShiftCount++;
        }
      }
    }

    return nightShiftCount;
  }

  function getExtraHoursWorked(attendance, startDate, endDate) {
    if (!attendance) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);
    let extraHours = 0;

    for (const date in attendance) {
      const entryDate = new Date(date);
      const inRange = entryDate >= start && entryDate <= end;

      const entry = attendance[date];

      if (
        inRange &&
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        const login = new Date(entry.login);
        const logout = new Date(entry.logout);

        let workedHours = (logout - login) / (1000 * 60 * 60);
        let nightTime = 0;
        // --- Remove night shift hours ---
        const loginHour = login.getHours();
        if (loginHour >= 18 && workedHours >= record?.workingHours) {
          // If night shift starts after 6pm, count 8h as night shift
          nightTime = 1;
        } else if (loginHour < 18 && workedHours >= record?.workingHours * 2) {
          // If started before 6pm, count last 8h as night shift
          nightTime = 2;
        }
        if (nightTime === 0 || nightTime === 1) {
          extraHours = workedHours - record?.workingHours;
        } else {
          extraHours = workedHours - record?.workingHours * 2;
        }
      }
    }

    return extraHours;
  }

  // function getExtraHoursWorked(attendance, startDate, endDate) {
  //   const today = new Date();
  //   const currentMonth = today.getMonth();
  //   const currentYear = today.getFullYear();
  //   let extraHours = 0;

  //   for (const date in attendance) {
  //     const entryDate = new Date(date);

  //     const isSameMonth =
  //       entryDate.getMonth() === currentMonth &&
  //       entryDate.getFullYear() === currentYear;

  //     const entry = attendance[date];

  //     if (
  //       isSameMonth &&
  //       entry &&
  //       Object.keys(entry).length > 0 &&
  //       entry.isLeave !== true &&
  //       entry.login &&
  //       entry.logout
  //     ) {
  //       const login = new Date(entry.login);
  //       const logout = new Date(entry.logout);

  //       const workedMs = logout - login;
  //       const workedHours = workedMs / (1000 * 60 * 60);

  //       if (workedHours > record?.workingHours) {
  //         extraHours += workedHours - record?.workingHours;
  //       }
  //     }
  //   }

  //   return extraHours;
  // }

  return (
    <RightSidebar
      openSideBar={openSideBar}
      setOpenSideBar={setOpenSideBar}
      title="Attendance Details"
      scale={600}
    >
      <div>
        <h4 className="my-2">General Details</h4>
        <div className="border rounded p-4">
          <GeneralDetailsRow
            name={'Name'}
            Icon={IdcardOutlined}
            value={record?.name}
            color={'text-indigo-500'}
          />
          <GeneralDetailsRow
            name={'Email'}
            Icon={IdcardOutlined}
            value={record?.email}
            color={'text-purple-500'}
            className="mt-2"
          />
          <GeneralDetailsRow
            name={'Gender'}
            Icon={UserOutlined}
            value={record?.gender}
            color={'text-indigo-500'}
            className="mt-2"
          />
          <GeneralDetailsRow
            name={'Contact Number'}
            Icon={PhoneOutlined}
            value={record?.contactNumber}
            color={'text-yellow-500'}
            className="mt-2"
          />
          <GeneralDetailsRow
            name={'Whatsapp Number'}
            Icon={WhatsAppOutlined}
            value={record?.whatsappNumber}
            color={'text-green-500'}
            className="mt-2"
          />
          <GeneralDetailsRow
            name={'Working Hours'}
            Icon={ClockCircleOutlined}
            value={record?.workingHours}
            color={'text-orange-500'}
            className="mt-2"
          />
          <GeneralDetailsRow
            name={'Fixed Salary'}
            Icon={DollarOutlined}
            value={record?.fixedSalary}
            color={'text-lime-500'}
            className="mt-2"
          />
        </div>
        <div>
          <h4 className="my-2">Attendance Details</h4>
          <div className="border rounded p-4">
            <GeneralDetailsRow
              name={'Leaves Assigned'}
              Icon={FaCalendarDay}
              value={record?.leavesAllowed}
              color="text-blue-400"
              tag={true}
              tagColor="blue"
            />
            <GeneralDetailsRow
              name={'Leaves Taken'}
              Icon={FaCalendarCheck}
              value={record?.leavesTaken}
              color="text-red-400"
              tag={true}
              tagColor="red"
              className="mt-2"
            />
            {openSideBar && (
              <GeneralDetailsRow
                name={'Holidays'}
                Icon={FaCalendarCheck}
                value={
                  getDaysWorkedThisMonth(record?.attendance, startDate, endDate)
                    ?.holidaysCount
                }
                color="text-green-300"
                tag={true}
                tagColor="green"
                className="mt-2"
              />
            )}
            {openSideBar && (
              <GeneralDetailsRow
                name={'Hours Worked Yesterday'}
                Icon={FaClock}
                value={getHoursWorkedYesterday(record?.attendance)}
                color="text-orange-300"
                tag={true}
                tagColor="orange"
                className="mt-2"
              />
            )}
            {openSideBar && (
              <GeneralDetailsRow
                name={'Hours Worked This Month'}
                Icon={FaClock}
                value={getHoursWorkedThisMonth(
                  record?.attendance,
                  startDate,
                  endDate
                )}
                color="text-yellow-300"
                tag={true}
                tagColor="yellow"
                className="mt-2"
              />
            )}
            {openSideBar && (
              <GeneralDetailsRow
                name={'Days Worked This Month'}
                Icon={FaBusinessTime}
                value={
                  getDaysWorkedThisMonth(record?.attendance, startDate, endDate)
                    ?.daysWorked
                }
                color="text-green-300"
                tag={true}
                tagColor="green"
                className="mt-2"
              />
            )}
            {openSideBar && (
              <GeneralDetailsRow
                name={'Night Shifts Worked This Month'}
                Icon={FaMoon}
                value={getNightShiftsWorked(
                  record?.attendance,
                  startDate,
                  endDate
                )}
                color="text-purple-300"
                tag={true}
                tagColor="purple"
                className="mt-2"
              />
            )}
            {openSideBar && (
              <GeneralDetailsRow
                name={'Extra Hours Worked This Month'}
                Icon={FaMoon}
                value={getExtraHoursWorked(
                  record?.attendance,
                  startDate,
                  endDate
                )}
                color="text-purple-300"
                tag={true}
                tagColor="purple"
                className="mt-2"
              />
            )}
          </div>
        </div>
      </div>
    </RightSidebar>
  );
};

export default LeavesSidebar;
