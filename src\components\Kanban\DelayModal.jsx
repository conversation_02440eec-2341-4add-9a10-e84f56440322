import { useState } from 'react';
import { toast } from 'react-toastify';

import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import Textarea from '../global/components/Textarea';

import { getNextColumns, isLastColumn } from './kanbanFunctions';

const DelayModal = ({
  setIsReasonModalOpen,
  column,
  card,
  defaultParam,
  shiftKanbanColumn,
  dispatch,
  reasonForDelay,
  selectedColumns,
  setReasonForDelay,
  setSelectedColumns,
}) => {
  const [isCommonToAllChecked, setIsCommonToAllChecked] = useState(false);
  return (
    <Modal
      title="Delay Reason"
      className="z-20"
      description="Enter reason for delay"
      onCloseModal={() => setIsReasonModalOpen(false)}
      onSubmit={async () => {
        let canMarkAsDone = true;
        let currentColumn = column?.label;
        if (currentColumn === 'Quotation') {
          for (let step of card?.steps) {
            if (
              step?.stepPage === 'Quotation' &&
              (step?.data?.quoteStatus === 'pending' ||
                step?.data?.quoteStatus === 'rejected')
            ) {
              canMarkAsDone = false;
              break;
            }
          }
        }
        if (defaultParam?.projectDefaults?.disableApprovals) {
          canMarkAsDone = true;
        }

        if (canMarkAsDone) {
          let nextColumns = getNextColumns({ defaultParam, column });
          nextColumns = nextColumns?.map((elem) => elem?.label);
          if (isLastColumn({ defaultParam, column }) === true) {
            dispatch({
              type: 'SHIFT_CARD',
              payload: {
                data: card,
                nextColumns: ['Completed'],
                currentColumn: column?.label,
              },
            });
            await shiftKanbanColumn({
              id: card?._id,
              page: column?.label,
              isComplete: true,
              data: {
                delayReason: reasonForDelay,
              },
            });
            setIsReasonModalOpen(false);
          } else {
            dispatch({
              type: 'SHIFT_CARD',
              payload: {
                data: card,
                nextColumns: nextColumns ? nextColumns : selectedColumns,
                currentColumn: column?.label,
              },
            });
            await shiftKanbanColumn({
              id: card?._id,
              page: column?.label,
              isComplete: false,
              data: {
                delayReason: reasonForDelay,
                nextColumns: nextColumns ? nextColumns : selectedColumns,
              },
            });
            setIsReasonModalOpen(false);
          }
        }
      }}
    >
      {() => {
        return (
          <>
            <div className="w-full flex justify-between items-center mb-4">
              <label className="text-md  text-gray-700 font-medium block">
                Reason for Delay
              </label>
              <div className="flex gap-2  items-center">
                <label className="text-md  text-gray-700 font-medium ">
                  Common to All
                </label>
                <input
                  type="checkbox"
                  checked={isCommonToAllChecked}
                  onChange={(e) => {
                    const commonVal = reasonForDelay[0]?.reason;
                    if (e.target.checked && commonVal !== '') {
                      setIsCommonToAllChecked(e.target.checked);
                      setReasonForDelay((prev) =>
                        prev.map((item) => ({ ...item, reason: commonVal }))
                      );
                    } else {
                      toast.info('Add First Field');
                      setReasonForDelay((prev) =>
                        prev.map((item) => ({ ...item, reason: '' }))
                      );
                    }
                  }}
                />
              </div>
            </div>
            {reasonForDelay?.map((item, index) => (
              <div key={index} className="mb-2">
                <div className="flex gap-2">
                  <p className="text-gray-500 text-sm font-semibold">
                    Delay On
                  </p>{' '}
                  <input
                    type="date"
                    className="text-sm"
                    value={item.delayDate}
                    disabled={true}
                    onChange={(e) => {
                      const newDelayReasons = [...reasonForDelay];
                      newDelayReasons[index].delayDate = e.target.value;
                      setReasonForDelay(newDelayReasons);
                    }}
                  />
                </div>

                <Textarea
                  placeholder="Enter reason for delay"
                  className="text-sm"
                  value={item.reason}
                  onChange={(e) => {
                    const newDelayReasons = [...reasonForDelay];
                    newDelayReasons[index].reason = e.target.value;
                    setReasonForDelay(newDelayReasons);
                    setIsCommonToAllChecked(false);
                  }}
                />
              </div>
            ))}

            {!isLastColumn({ defaultParam, column }) &&
              getNextColumns({ defaultParam, column })?.length > 1 && (
                <div>
                  <label className="text-sm text-gray-700 font-medium block">
                    Select Next Columns
                  </label>
                  <MultiSelect
                    closeMenuOnSelect={false}
                    className="!w-full"
                    placeholder="Select Next Columns"
                    options={getNextColumns({ defaultParam, column })}
                    value={selectedColumns}
                    onChange={(e) => {
                      setSelectedColumns(e.target.value);
                    }}
                  />
                </div>
              )}
          </>
        );
      }}
    </Modal>
  );
};

export default DelayModal;
