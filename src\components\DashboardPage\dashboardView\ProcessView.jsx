import { useEffect, useState } from 'react';
import {
  CircularProgressbarWithChildren,
  buildStyles,
} from 'react-circular-progressbar';
import {
  calculateValueForViews,
  getLocalDate,
  handlePercentDeicmalValue,
} from '../../../helperFunction';
import useLeadingLagging from '../../../hooks/useLeadingLagging';

const FieldValue = ({ param, calculatedValue, setRealTimeBatchSize }) => {
  useEffect(() => {
    if (param?.name === 'Batch Size') {
      setRealTimeBatchSize(calculatedValue);
    }
  }, [param?.name, calculatedValue]); // eslint-disable-line

  return (
    <p className="text-[0.94rem] font-bold">{Math.round(calculatedValue)}</p>
  );
};

const CuProjectTab = ({ cuProject, selectOptions, values }) => {
  const [machineList, setMachineList] = useState([]);
  const [operatorList, setOperatorList] = useState([]);
  const [batchData, setBatchData] = useState({});
  const [realTimeBatchSize, setRealTimeBatchSize] = useState(0);
  const [newValues, setNewValues] = useState({});

  const leadLagData = useLeadingLagging(batchData, realTimeBatchSize);

  useEffect(() => {
    setMachineList([]);
    setOperatorList([]);
    if (selectOptions && cuProject) {
      cuProject?.machineAndOperator?.forEach((mao) => {
        setMachineList((prev) => [
          ...prev,
          {
            id: mao.machine.machineId,
            name: mao.machine.machineName,
          },
        ]);

        mao.operator.forEach((op) => {
          setOperatorList((prev) => {
            const exists = prev?.find((item) => item._id === op?.user?._id);

            if (exists) {
              return prev;
            } else {
              return [...prev, op.user];
            }
          });
        });
      });

      let tempBatchData = selectOptions?.createInput?.goalsTable
        .find((table) => table.flowId === selectOptions.flowId)
        ?.tableData?.find(
          (data) => data.batchNo === cuProject?.batchInfo?.batchNo
        );

      if (cuProject?.isMultiProcess) {
        tempBatchData = selectOptions?.createInput?.goalsTable
          .find((table) => table.flowId === selectOptions.flowId)
          ?.tableData?.find(
            (data) => data.batchNo === cuProject?.batchInfo?.batchNo
          )
          ?.subProcessData?.find(
            (i, idx) => idx === cuProject?.subProcessIndex
          );
      }

      setBatchData(tempBatchData);
    }
  }, [selectOptions, cuProject]);

  useEffect(() => {
    if (cuProject && values && selectOptions) {
      cuProject?.machineAndOperator?.forEach((mao) => {
        const isActive = mao?.status === 'active';

        const machine = selectOptions?.project?.machines?.find(
          (machine) => machine._id === mao?.machine._id
        );

        machine?.devices?.forEach((device) => {
          device?.assignedDevice?.forEach((item) => {
            if (isActive) {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]: values?.[item?.deviceId] || {},
              }));
            } else {
              setNewValues((prev) => ({
                ...prev,
                [item?.deviceId]:
                  cuProject?.lastValues?.[machine?.machineId]?.[
                    item?.deviceId
                  ] || {},
              }));
            }
          });
        });
      });
    }
  }, [cuProject, values, selectOptions]);

  const parameters = selectOptions?.project?.processGoalView?.parameters;

  return (
    <div className="w-full px-5 py-3 rounded-new bg-gray-primary mb-8 last:mb-0">
      <p className="text-[1.25rem] font-medium mb-4 flex justify-between">
        <span>
          {`Batch ${cuProject?.batchInfo?.batchNo} : `}
          <span
            className={`capitalize ${
              !leadLagData?.status || leadLagData?.status === 'lagging'
                ? 'text-red-primary'
                : 'text-green-primary'
            }`}
          >
            {leadLagData?.status || 'NA'}
          </span>
        </span>

        <span>{cuProject?.subProcessData?.process}</span>
      </p>
      <div className="w-full grid grid-cols-4 gap-3">
        {/* first two process goals */}
        {parameters?.slice(0, 2)?.map((param) => {
          const calculatedValue = calculateValueForViews(
            param.formula,
            newValues,
            machineList.map((item) => item?.id)
          );

          const displayValue =
            handlePercentDeicmalValue(
              (calculatedValue / batchData?.[param?.name]) * 100
            ) || 0;

          return (
            <div key={param._id} className="w-full flex flex-col items-center">
              <section className="w-1/2">
                <CircularProgressbarWithChildren
                  value={displayValue}
                  strokeWidth={8}
                  styles={buildStyles({
                    rotation: 0.25,
                    strokeLinecap: 'butt',
                    pathTransitionDuration: 0.5,
                    pathColor: `#71A5DE`,
                    trailColor: '#F3F2F2',
                  })}
                >
                  <FieldValue
                    param={param}
                    calculatedValue={calculatedValue}
                    setRealTimeBatchSize={setRealTimeBatchSize}
                  />
                </CircularProgressbarWithChildren>
              </section>

              <p className="mt-1 text-[0.94rem] font-medium">{param.name}</p>
            </div>
          );
        })}

        {/* leading lagging data */}
        <div className="col-start-3 col-span-2 w-full text-[0.88rem] font-medium">
          {/* active machines */}
          <p className="flex w-full">
            <span>Active Machines :</span>
            <span className="flex flex-col ml-1">
              {machineList.map((mac) => (
                <span className="text-blue-primary" key={mac.id}>
                  {mac.name}
                </span>
              ))}
            </span>
          </p>

          {/* dates */}
          <section className="flex gap-x-3">
            <section className="flex flex-col">
              <p className="italic">Start Date</p>
              <p>
                {leadLagData?.startDate
                  ? getLocalDate(leadLagData?.startDate)
                  : 'NA'}
              </p>
            </section>
            <section className="flex flex-col">
              <p className="italic">Expected Completion</p>
              <p>
                {leadLagData?.completionDateTime
                  ? getLocalDate(leadLagData?.completionDateTime)
                  : 'NA'}
              </p>
            </section>
          </section>

          {/* Workers */}
          <p className="flex w-full">
            <span>Workers :</span>
            <span className="flex flex-col ml-1">
              {operatorList.map((op) => (
                <span className="text-blue-primary" key={op._id}>
                  {op.name}
                </span>
              ))}
            </span>
          </p>
        </div>

        {/* other remaining process goals */}
        {parameters?.slice(2)?.map((param) => {
          const calculatedValue = calculateValueForViews(
            param.formula,
            newValues,
            machineList.map((item) => item?.id)
          );

          const displayValue =
            handlePercentDeicmalValue(
              (calculatedValue / batchData?.[param?.name]) * 100
            ) || 0;
          return (
            <div key={param._id} className="w-full flex flex-col items-center">
              <section className="w-1/2">
                <CircularProgressbarWithChildren
                  value={displayValue}
                  strokeWidth={8}
                  styles={buildStyles({
                    rotation: 0.25,
                    strokeLinecap: 'butt',
                    pathTransitionDuration: 0.5,
                    pathColor: `#71A5DE`,
                    trailColor: '#F3F2F2',
                  })}
                >
                  <FieldValue
                    param={param}
                    calculatedValue={calculatedValue}
                    setRealTimeBatchSize={setRealTimeBatchSize}
                  />
                </CircularProgressbarWithChildren>
              </section>

              <p className="mt-1 text-[0.94rem] font-medium">{param.name}</p>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const ProcessView = ({ selectOptions, values, searchOptions }) => {
  const [searchResult, setSearchResult] = useState([]);

  useEffect(() => {
    if (searchOptions && selectOptions?.cuProjects) {
      const { search, option } = searchOptions;
      const { cuProjects } = selectOptions;
      if (search) {
        // search by batch
        if (option === 'batch') {
          const temp = cuProjects.filter((cuProject) =>
            `batch ${cuProject.batchInfo.batchNo}`.includes(
              search?.toLowerCase()
            )
          );

          setSearchResult(temp);
        }

        // serach by machine
        if (option === 'machine') {
          const temp = cuProjects.filter((cuProject) => {
            let check = false;
            cuProject?.machineAndOperator?.forEach((mao) => {
              check = mao?.machine?.machineName
                ?.toLowerCase()
                ?.includes(search?.toLowerCase());
            });
            return check;
          });
          setSearchResult(temp);
        }

        // serach by worker
        if (option === 'worker') {
          const temp = cuProjects.filter((cuProject) => {
            let check = false;
            cuProject?.machineAndOperator?.forEach((mao) => {
              mao?.operator?.forEach((optr) => {
                check = optr?.user?.name
                  ?.toLowerCase()
                  ?.includes(search?.toLowerCase());
              });
            });
            return check;
          });
          setSearchResult(temp);
        }
      } else {
        setSearchResult(cuProjects);
      }
    }
  }, [selectOptions, searchOptions]);

  return (
    <div className="mt-5 w-full rounded-new py-3 px-5">
      <p className="font-bold mb-3">{selectOptions?.mqtt?.process}</p>
      {searchResult?.length > 0 ? (
        <>
          {searchResult?.map((cuProject) => (
            <CuProjectTab
              key={cuProject._id}
              cuProject={cuProject}
              selectOptions={selectOptions}
              values={values}
            />
          ))}
        </>
      ) : (
        <p className="text-center">No results found</p>
      )}
    </div>
  );
};
export default ProcessView;
