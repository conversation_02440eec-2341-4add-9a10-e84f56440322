import { createContext, useContext, useState } from 'react';

// 1. Create the context
const HrmsContext = createContext();

// 2. Create a provider component
export const HrmsProvider = ({ children }) => {
  const [payrollRunData, setPayrollRunData] = useState({});
  const [userDataDuringPayrollRun, setUserDataDuringPayrollRun] = useState({});

  function getHoursWorkedInRange(
    attendance,
    startDate,
    endDate,
    holidays,
    workingHoursPerDay = 8
  ) {
    if (!attendance) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);
    let totalHours = 0;
    let holidayHours = 0;

    // Helper: check if given date is a holiday
    const isHoliday = (date) => {
      return holidays?.some((holiday) => {
        const hStart = new Date(holiday.startDate.$date || holiday.startDate);
        const hEnd = new Date(holiday.endDate.$date || holiday.endDate);
        return date >= hStart && date <= hEnd;
      });
    };

    // Loop through each date in the range
    for (
      let day = new Date(start);
      day <= end;
      day.setDate(day.getDate() + 1)
    ) {
      const dateStr = day.toISOString().split('T')[0];
      const entry = attendance[dateStr];

      if (
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        const login = new Date(entry.login);
        const logout = new Date(entry.logout);
        const diffMs = logout - login;

        if (diffMs > 0) {
          totalHours += diffMs / (1000 * 60 * 60); // ms → hours
        }
      } else if (isHoliday(day)) {
        // Count full standard working hours for holidays
        holidayHours += workingHoursPerDay;
      }
    }

    return { totalHours, holidayHours };
  }

  // function getHoursWorkedInRange(attendance, startDate, endDate) {
  //   if (attendance !== undefined) {
  //     const start = new Date(startDate);
  //     const end = new Date(endDate);
  //     let totalHours = 0;

  //     for (const date in attendance) {
  //       const entryDate = new Date(date);
  //       const entry = attendance[date];
  //       if (
  //         entryDate >= start &&
  //         entryDate <= end &&
  //         entry &&
  //         Object.keys(entry).length > 0 &&
  //         entry.isLeave !== true &&
  //         entry.login &&
  //         entry.logout
  //       ) {
  //         const login = new Date(entry.login);
  //         const logout = new Date(entry.logout);
  //         const diffMs = logout - login;

  //         if (diffMs > 0) {
  //           totalHours += diffMs / (1000 * 60 * 60); // Convert ms to hours
  //         }
  //       }
  //     }
  //     return totalHours;
  //   } else {
  //     return 0;
  //   }
  // }

  // CONSIDERS DYNAMIC DAYS BETWEEN THE PAYROLLL START AND END DATE FOR PER HOUR CALCULATION

  // const calculatePayrollStepOneSalary = (userId, startDate, endDate) => {
  //   let user = userDataDuringPayrollRun?.[userId];
  //   const start = new Date(startDate);
  //   const end = new Date(endDate);

  //   // Normalize time to midnight to avoid time-related discrepancies
  //   start.setHours(0, 0, 0, 0);
  //   end.setHours(0, 0, 0, 0);

  //   const diffMs = end - start;

  //   // Convert milliseconds to days and add 1 (inclusive)
  //   const diffDays = diffMs / (1000 * 60 * 60 * 24) + 1;
  //   let pay = 0;
  //   if (user) {
  //     let perHourSalary = user?.fixedSalary / (diffDays * user?.workingHours);
  //     let hoursWorked = getHoursWorkedInRange(
  //       userDataDuringPayrollRun?.[userId]?.attendance,
  //       startDate,
  //       endDate
  //     );

  //     // Add 8 hours for each Friday in the date range
  //     let fridaysCount = 0;
  //     const current = new Date(start);
  //     while (current <= end) {
  //       if (current.getDay() === 5) {
  //         // 5 => Friday
  //         fridaysCount++;
  //       }
  //       current.setDate(current.getDate() + 1);
  //     }

  //     hoursWorked += fridaysCount * user?.workingHours;
  //     pay = perHourSalary * hoursWorked;
  //   }
  //   return pay;
  // };

  // MOSTLY STATIC PER HOUR SALARY CALCULATION, MONTH IS ALWAYS CONSIDERED A 30 DAY MONTH AND WORK HOURS IS DYNAMIC.

  const calculatePayrollStepOneSalary = (
    userId,
    startDate,
    endDate,
    holidays
  ) => {
    let user = userDataDuringPayrollRun?.[userId];
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Normalize time to midnight
    start.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);

    let pay = 0;
    if (user) {
      // Always base per-hour salary on 30 days in a month
      let perHourSalary = user?.fixedSalary / (30 * user?.workingHours);
      let hoursWorkedObj = getHoursWorkedInRange(
        user.attendance,
        startDate,
        endDate,
        holidays,
        user?.workingHours
      );
      let hoursWorked =
        hoursWorkedObj?.totalHours + hoursWorkedObj?.holidayHours;

      // Determine actual min/max date from attendance object
      const attendanceDates = Object.keys(user.attendance).sort(); // sorted YYYY-MM-DD
      if (attendanceDates.length > 0) {
        const attendanceStart = new Date(attendanceDates[0]);
        const attendanceEnd = new Date(
          attendanceDates[attendanceDates.length - 1]
        );
        // Count Fridays between earliest and latest attendance dates
        let fridaysCount = 0;
        const current = new Date(attendanceStart);
        while (current <= attendanceEnd) {
          if (current.getDay() === 5) {
            // Friday = 5
            fridaysCount++;
          }
          current.setDate(current.getDate() + 1);
        }

        hoursWorked += fridaysCount * user?.workingHours;
      }
      pay = perHourSalary * hoursWorked;
    }
    return pay;
  };

  const calculatePayrollStepTwoSalary = (
    userId,
    startDate,
    endDate,
    holidays
  ) => {
    let stepOnePay = calculatePayrollStepOneSalary(
      userId,
      startDate,
      endDate,
      holidays
    );

    let bonusData = getPayrollBonusData(userId, startDate, endDate);
    let bonusAmount = bonusData.reduce(
      (sum, item) => sum + parseInt(item.amount),
      0
    );

    let deductionData = getPayrollDeductionData(userId, startDate, endDate);
    let deductionAmount = deductionData.reduce(
      (sum, item) => sum + parseInt(item.amount),
      0
    );

    let advanceData = getPayrollAdvanceData(userId, startDate, endDate);
    let advanceAmount = advanceData.reduce(
      (sum, item) => sum + parseInt(item.amount),
      0
    );

    let pay = stepOnePay + bonusAmount - advanceAmount - deductionAmount;

    return pay;
  };

  const calculatePayrollStepThreeSalary = (userId, startDate, endDate) => {
    let stepTwoPay = calculatePayrollStepTwoSalary(userId, startDate, endDate);

    let adHocData = getPayrollAdHocData(userId, startDate, endDate);
    let adHocAmount = adHocData.reduce(
      (sum, item) => sum + parseInt(item.amount),
      0
    );

    let reimbursementsData = getPayrollReimbursementData(
      userId,
      startDate,
      endDate
    );
    let reimbursementsAmount = reimbursementsData.reduce(
      (sum, item) => sum + parseInt(item.amount),
      0
    );

    let pay = stepTwoPay + reimbursementsAmount + adHocAmount;

    return pay;
  };

  const calculatePayrollStepFourSalary = (userId, startDate, endDate) => {
    let stepThreePay = calculatePayrollStepThreeSalary(
      userId,
      startDate,
      endDate
    );
    let salaryComponents =
      userDataDuringPayrollRun?.[userId]?.salaryComponents || [];
    let salaryAmount = salaryComponents.reduce(
      (sum, item) => sum + parseInt(item.amount),
      0
    );

    let pay = stepThreePay + salaryAmount;

    return pay;
  };

  function filterDatesInRange(dataArray, startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    return dataArray.filter((item) => {
      if (item?.date === '') {
        return true;
      } else {
        const itemDate = new Date(item.date);
        return itemDate >= start && itemDate <= end;
      }
    });
  }

  const getPayrollBonusData = (userId, startDate, endDate) => {
    let res = filterDatesInRange(
      userDataDuringPayrollRun?.[userId]?.bonusData,
      startDate,
      endDate
    );
    return res;
  };

  const getPayrollDeductionData = (userId, startDate, endDate) => {
    let res = filterDatesInRange(
      userDataDuringPayrollRun?.[userId]?.deductionData,
      startDate,
      endDate
    );
    return res;
  };

  const getPayrollAdvanceData = (userId, startDate, endDate) => {
    let res = filterDatesInRange(
      userDataDuringPayrollRun?.[userId]?.advanceData,
      startDate,
      endDate
    );
    return res;
  };

  const getPayrollAdHocData = (userId, startDate, endDate) => {
    let res = filterDatesInRange(
      userDataDuringPayrollRun?.[userId]?.adHocData,
      startDate,
      endDate
    );
    return res;
  };

  const getPayrollReimbursementData = (userId, startDate, endDate) => {
    let res = filterDatesInRange(
      userDataDuringPayrollRun?.[userId]?.reimbursements,
      startDate,
      endDate
    );
    return res;
  };

  return (
    <HrmsContext.Provider
      value={{
        payrollRunData,
        userDataDuringPayrollRun,
        setPayrollRunData,
        setUserDataDuringPayrollRun,
        calculatePayrollStepOneSalary,
        calculatePayrollStepTwoSalary,
        calculatePayrollStepThreeSalary,
        calculatePayrollStepFourSalary,
        getHoursWorkedInRange,
        getPayrollBonusData,
        getPayrollDeductionData,
        getPayrollAdvanceData,
        getPayrollAdHocData,
        getPayrollReimbursementData,
      }}
    >
      {children}
    </HrmsContext.Provider>
  );
};

// 3. Create a custom hook to use the context
export const useHrmsContext = () => useContext(HrmsContext);
