import {
  CheckCircleFilled,
  FileExcelOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FileUnknownOutlined,
  FileWordOutlined,
} from '@ant-design/icons';
import { Button, Card, Input, Modal, Radio, Spin, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useShareAttachmentsMutation } from '../../slices/customerSalesOrderApiSlice';
const { Text, Title } = Typography;
const { TextArea } = Input;

const getFileIcon = (fileType) => {
  if (!fileType) return <FileUnknownOutlined />;

  if (fileType.startsWith('image/')) {
    return <FileImageOutlined />;
  } else if (fileType === 'application/pdf') {
    return <FilePdfOutlined />;
  } else if (fileType.includes('word') || fileType.includes('document')) {
    return <FileWordOutlined />;
  } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
    return <FileExcelOutlined />;
  } else {
    return <FileTextOutlined />;
  }
};

const AttachmentShareModal = ({
  isOpen,
  onClose,
  customerPhoneNumber = '',
  selectedAttachments = {},
  selectedOrderId,
}) => {
  const [phoneNumberType, setPhoneNumberType] = useState(
    customerPhoneNumber ? 'customer' : 'manual'
  );
  const [manualPhoneNumber, setManualPhoneNumber] = useState('');
  const [message, setMessage] = useState('');
  const [sendToWhatsApp, { isLoading }] = useShareAttachmentsMutation();

  useEffect(() => {
    if (isOpen) {
      setPhoneNumberType(customerPhoneNumber ? 'customer' : 'manual');
      setManualPhoneNumber('');
      setMessage('');
    }
  }, [isOpen, customerPhoneNumber]);

  const handleSend = async () => {
    const phoneNumber =
      phoneNumberType === 'customer' ? customerPhoneNumber : manualPhoneNumber;

    if (!phoneNumber) {
      toast.error('Please enter a valid phone number');
      return;
    }

    try {
      const data = {
        mediaId: selectedAttachments?._id,
        orderId: selectedOrderId,
        phoneNumber,
        message: message,
      };
      await sendToWhatsApp(data).unwrap();

      toast.success('Attachments shared successfully');
      onClose();
    } catch (error) {
      toast.error('Failed to share attachments');
    }
  };

  return (
    <Modal
      title={<Title level={4}>Share Attachments</Title>}
      open={isOpen}
      onCancel={onClose}
      width={700}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Cancel
        </Button>,
        <Button
          key="send"
          type="primary"
          onClick={handleSend}
          loading={isLoading}
          disabled={!selectedAttachments}
        >
          Send
        </Button>,
      ]}
    >
      <Spin spinning={isLoading}>
        <div className="space-y-6 pb-2">
          {selectedAttachments ? (
            <div className="grid grid-cols-2 gap-3 max-h-60 overflow-y-auto">
              <Card
                size="small"
                className="border border-gray-200 hover:shadow-md transition-shadow p-2"
              >
                <div className="flex items-start gap-3">
                  <div className="text-2xl text-blue-500 mt-1">
                    {getFileIcon(selectedAttachments?.type)}
                  </div>
                  <div className="flex-1 overflow-hidden">
                    <Text
                      strong
                      className="block truncate"
                      title={selectedAttachments?.name}
                    >
                      {selectedAttachments?.name}
                    </Text>
                    <Text type="secondary" className="text-xs">
                      {selectedAttachments?.type
                        ?.split('/')[1]
                        ?.toUpperCase() || selectedAttachments?.type}
                    </Text>

                    {selectedAttachments?.type?.startsWith('image/') &&
                      selectedAttachments?.data && (
                        <div className="mt-2 h-20 overflow-hidden rounded-md">
                          <img
                            src={selectedAttachments?.data}
                            alt={selectedAttachments?.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}
                  </div>
                  <div className="text-green-500">
                    <CheckCircleFilled />
                  </div>
                </div>
              </Card>
            </div>
          ) : (
            <div className="border rounded-lg p-6 text-center bg-gray-50">
              <Text className="text-gray-500 block">
                No attachments selected. Please select attachments from the
                Attachments tab.
              </Text>
            </div>
          )}
          <div className="space-y-3">
            <Text strong>Phone Number</Text>
            <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
              {customerPhoneNumber && (
                <div className="flex items-center gap-3 mb-3">
                  <Radio
                    checked={phoneNumberType === 'customer'}
                    onChange={() => setPhoneNumberType('customer')}
                  />
                  <Text>
                    Customer's Phone Number:{' '}
                    <span className="font-medium">{customerPhoneNumber}</span>
                  </Text>
                </div>
              )}
              <div className="flex items-center gap-3">
                <Radio
                  checked={phoneNumberType === 'manual'}
                  onChange={() => setPhoneNumberType('manual')}
                />
                <Input
                  placeholder="Enter phone number"
                  value={manualPhoneNumber}
                  onChange={(e) => setManualPhoneNumber(e.target.value)}
                  disabled={phoneNumberType !== 'manual'}
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          {/* Message */}
          <div className="space-y-3">
            <Text strong>Message</Text>
            <TextArea
              rows={4}
              placeholder="Enter a message to send with the attachments"
              value={message}
              maxLength={1024}
              showCount
              onChange={(e) => setMessage(e.target.value)}
              className="border-gray-300 resize-none"
            />
          </div>
        </div>
      </Spin>
    </Modal>
  );
};

export default AttachmentShareModal;
