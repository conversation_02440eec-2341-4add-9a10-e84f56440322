name: Upload the version build to dev server
on:
  push:
    branches:
      - 'dev-*'
jobs:
  build-and-upload:
    runs-on: ubuntu-22.04
    env:
      PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      HOSTNAME: ${{ secrets.SSH_HOST }}
      USER_NAME: ${{ secrets.USER_NAME }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ github.ref_name }}-optiwise-npm-cache-${{ hashFiles('**/package-lock.json') }}
      - name: Install dependencies
        run: npm ci
      - name: Build Code
        run: npm run build
      - name: Compress dist
        run: zip -r ${{ github.ref_name }}.zip dist/
      - name: Upload file
        run: |
          echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
          scp -o StrictHostKeyChecking=no -i private_key ./${{ github.ref_name }}.zip ${USER_NAME}@${HOSTNAME}:
      - name: Replace file on ec2
        run: |
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOSTNAME} '
              # Now we have got the access of EC2 and we will start the deploy .
              sudo mkdir -p code/versions/${{ github.ref_name }} &&
              cd code/versions/${{ github.ref_name }} &&
              sudo rm -rf dist/ &&
              cd /home/<USER>
              sudo unzip ${{ github.ref_name }}.zip &&
              sudo mv dist/ code/versions/${{ github.ref_name }} &&
              sudo rm ${{ github.ref_name }}.zip
              '
