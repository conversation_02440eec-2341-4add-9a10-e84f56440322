import { Disclosure } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { useContext } from 'react';
import {
  getPartVariantName,
  getProductVariantName,
} from '../../helperFunction';
import { Store } from '../../store/Store';
import Table from '../global/components/Table';

const AssignedAssemblyBom = ({ po, className }) => {
  const { defaults: { defaultParam: { bomColumns = [] } = {} } = {} } =
    useContext(Store);

  let bom = po?.bom;

  return (
    <div className="grid grid-cols-1 w-full gap-2 bg-white">
      <div className="gap-2 flex flex-col">
        {bom
          ?.filter((_, index) => index % 2 === 0)
          ?.map((current) => (
            <Disclosure key={current._id}>
              <div className="w-full text-sm">
                <Disclosure.Button className="group w-full md:w-1/4 flex items-center gap-2 border rounded-lg py-2 px-6 justify-between bg-white">
                  <div>BOM: {current?.name}</div>
                  <div className="flex justify-end items-center gap-2">
                    View Details
                    <ChevronDownIcon
                      className="w-5 group-data-[open]:rotate-180"
                      data-open
                    />
                  </div>
                </Disclosure.Button>
                <Disclosure.Panel className={`px-2 mt-8`}>
                  <div className={`px-7 py-4 ${className}`}>
                    <section className="w-full mb-5 flex flex-col">
                      <h3 className="text-black mb-1">BOM General Details</h3>
                      <section>
                        <div className="flex items-center gap-2">
                          <label>BOM ID :</label>
                          <p className="text-black">{current?.bomId}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <label>BOM Name :</label>
                          <p className="text-black">{current?.name}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <label>Order Quantity :</label>
                          <p className="text-black">
                            {parseInt(po?.orderQuantity?.[current._id])}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <label>SOP :</label>
                          <p className="text-black">
                            {po?.sop?.formName || '-'}
                          </p>
                        </div>
                      </section>
                    </section>

                    <section className="w-full mb-5">
                      <h3 className="text-black mb-1">Item Details</h3>
                      <section className="overflow-x-scroll">
                        <Table className="mt-1">
                          <Table.Head>
                            <Table.Row>
                              <Table.Th>Category</Table.Th>
                              <Table.Th>Item Name</Table.Th>
                              <Table.Th>Units</Table.Th>
                              <Table.Th>Stock Required</Table.Th>
                              <Table.Th>In Stock</Table.Th>
                              {bomColumns?.map((col) => (
                                <Table.Th key={col.title}>{col.title}</Table.Th>
                              ))}
                            </Table.Row>
                          </Table.Head>
                          <Table.Body>
                            {current?.children?.map((elem) => {
                              return (
                                <>
                                  <Table.Row key={elem?._id}>
                                    <Table.Td>{elem?.category}</Table.Td>
                                    <Table.Td>
                                      <div className="!max-w-[18ch] !break-words">
                                        {elem?.part?.name ||
                                          elem?.product?.name ||
                                          elem?.manualEntry ||
                                          (elem?.partVariant
                                            ? getPartVariantName(
                                                elem?.partVariant
                                              )
                                            : getProductVariantName(
                                                elem?.productVariant
                                              ))}
                                      </div>
                                    </Table.Td>
                                    <Table.Td>{elem?.units}</Table.Td>

                                    <Table.Td>
                                      {(() => {
                                        const units =
                                          parseInt(elem?.units) || 0;
                                        const orderQuantity =
                                          parseInt(
                                            po?.orderQuantity?.[current._id]
                                          ) || 0;

                                        const manualEntry = elem?.manualEntry
                                          ? 0
                                          : elem?.part
                                            ? parseInt(elem?.part?.quantity) ||
                                              0
                                            : elem?.partVariant
                                              ? parseInt(
                                                  elem?.partVariant?.quantity
                                                ) || 0
                                              : parseInt(
                                                  elem?.product?.quantity
                                                ) || 0;

                                        const result =
                                          units * orderQuantity - manualEntry;

                                        return result <= 0 ? 0 : result;
                                      })()}
                                    </Table.Td>

                                    <Table.Td>
                                      {elem?.manualEntry
                                        ? '-'
                                        : elem?.part?.quantity ||
                                          elem?.product?.quantity}
                                    </Table.Td>
                                    {bomColumns?.map((col) => (
                                      <Table.Td key={col.field}>
                                        {elem?.additionalFields?.[col.field]}
                                      </Table.Td>
                                    ))}
                                  </Table.Row>
                                  {elem?.children?.map((subItem) => {
                                    return (
                                      <>
                                        <Table.Row
                                          className="!bg-slate-100"
                                          key={subItem?._id}
                                        >
                                          <Table.Td>
                                            {subItem?.category}
                                          </Table.Td>
                                          <Table.Td>
                                            {' '}
                                            <div className="!max-w-[24rem] !break-words">
                                              {subItem?.part?.name ||
                                                subItem?.product?.name ||
                                                subItem?.manualEntry ||
                                                (subItem?.partVariant
                                                  ? getPartVariantName(
                                                      subItem?.partVariant
                                                    )
                                                  : getProductVariantName(
                                                      subItem?.productVariant
                                                    ))}
                                            </div>
                                          </Table.Td>
                                          <Table.Td>{subItem?.units}</Table.Td>
                                          <Table.Td>
                                            {isNaN(
                                              parseInt(subItem?.units) *
                                                parseInt(
                                                  po?.orderQuantity?.[
                                                    current._id
                                                  ]
                                                )
                                            )
                                              ? '-'
                                              : parseInt(subItem?.units) *
                                                parseInt(
                                                  po?.orderQuantity[current._id]
                                                )}
                                          </Table.Td>
                                          <Table.Td>
                                            {subItem?.part?.quantity ||
                                              subItem?.product?.quantity ||
                                              '-'}
                                          </Table.Td>
                                          {bomColumns?.map((col) => (
                                            <Table.Td key={col.field}>
                                              {
                                                subItem?.additionalFields?.[
                                                  col.field
                                                ]
                                              }
                                            </Table.Td>
                                          ))}
                                        </Table.Row>
                                        {subItem?.children?.map((currElem) => {
                                          return (
                                            <Table.Row
                                              className="!bg-slate-200"
                                              key={currElem?._id}
                                            >
                                              <Table.Td>
                                                {currElem?.category}
                                              </Table.Td>
                                              <Table.Td>
                                                <div className="!max-w-[24rem] !break-words">
                                                  {/* {subItem?.part?.name ||
                                            subItem?.product?.name ||
                                            elem?.manualEntry} */}

                                                  {currElem?.part?.name ||
                                                    currElem?.product?.name ||
                                                    currElem?.manualEntry ||
                                                    (currElem?.partVariant
                                                      ? getPartVariantName(
                                                          currElem?.partVariant
                                                        )
                                                      : getProductVariantName(
                                                          currElem?.productVariant
                                                        ))}
                                                </div>
                                              </Table.Td>
                                              <Table.Td>
                                                {currElem?.units}
                                              </Table.Td>
                                              <Table.Td>
                                                {isNaN(
                                                  parseInt(currElem?.units) *
                                                    parseInt(
                                                      po?.orderQuantity[
                                                        current._id
                                                      ]
                                                    )
                                                )
                                                  ? '-'
                                                  : parseInt(currElem?.units) *
                                                    parseInt(
                                                      po?.orderQuantity[
                                                        current._id
                                                      ]
                                                    )}
                                              </Table.Td>
                                              <Table.Td>
                                                {currElem?.part?.quantity ||
                                                  currElem?.product?.quantity ||
                                                  '-'}
                                              </Table.Td>
                                              {bomColumns?.map((col) => (
                                                <Table.Td key={col.field}>
                                                  {
                                                    currElem
                                                      ?.additionalFields?.[
                                                      col.field
                                                    ]
                                                  }
                                                </Table.Td>
                                              ))}
                                            </Table.Row>
                                          );
                                        })}
                                      </>
                                    );
                                  })}
                                </>
                              );
                            })}
                          </Table.Body>
                        </Table>
                      </section>
                    </section>
                  </div>
                </Disclosure.Panel>
              </div>
            </Disclosure>
          ))}
      </div>
      <div className="gap-2 flex flex-col">
        {bom
          ?.filter((_, index) => index % 2 !== 0)
          ?.map((current) => (
            <Disclosure key={current._id}>
              <div className="w-full">
                <Disclosure.Button className="group w-full flex items-center gap-2 border rounded-lg py-2 px-6 justify-between bg-white">
                  <div>BOM: {current?.name}</div>
                  <div className="flex justify-end items-center gap-2">
                    View Details
                    <ChevronDownIcon
                      className="w-5 group-data-[open]:rotate-180"
                      data-open
                    />
                  </div>
                </Disclosure.Button>
                <Disclosure.Panel className={`px-2`}>
                  <div className={`px-7 py-4 ${className}`}>
                    <section className="w-full mb-5 flex flex-col">
                      <h3 className="text-black mb-1">BOM General Details</h3>
                      <section>
                        <div className="flex items-center gap-2">
                          <label>BOM ID :</label>
                          <p className="text-black">{current?.bomId}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <label>BOM Name :</label>
                          <p className="text-black">{current?.name}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <label>Order Quantity :</label>
                          <p className="text-black">
                            {parseInt(po?.orderQuantity[current._id])}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <label>SOP :</label>
                          <p className="text-black">
                            {po?.sop?.formName || '-'}
                          </p>
                        </div>
                      </section>
                    </section>

                    <section className="w-full mb-5">
                      <h3 className="text-black mb-1">Item Details</h3>
                      <section className="overflow-x-scroll">
                        <Table className="mt-1">
                          <Table.Head>
                            <Table.Row>
                              <Table.Th>Category</Table.Th>
                              <Table.Th>Item Name</Table.Th>
                              <Table.Th>Units</Table.Th>
                              <Table.Th>Stock Required</Table.Th>
                              <Table.Th>In Stock</Table.Th>
                              {bomColumns?.map((col) => (
                                <Table.Th key={col.title}>{col.title}</Table.Th>
                              ))}
                            </Table.Row>
                          </Table.Head>
                          <Table.Body>
                            {current?.productList?.map((elem) => {
                              return (
                                <>
                                  <Table.Row key={elem?._id}>
                                    <Table.Td>{elem?.category}</Table.Td>
                                    <Table.Td>
                                      {elem?.part?.name ||
                                        elem?.product?.name ||
                                        elem?.manualEntry}
                                    </Table.Td>
                                    <Table.Td>{elem?.units}</Table.Td>
                                    <Table.Td>
                                      {parseInt(elem?.units) *
                                        parseInt(
                                          po?.orderQuantity[current._id]
                                        ) -
                                        (elem?.manualEntry
                                          ? 0
                                          : elem?.part
                                            ? parseInt(elem?.part?.quantity)
                                            : parseInt(
                                                elem?.product?.quantity
                                              )) <=
                                      0
                                        ? 0
                                        : parseInt(elem?.units) *
                                            parseInt(
                                              po?.orderQuantity[current._id]
                                            ) -
                                          (elem?.manualEntry
                                            ? 0
                                            : elem?.part
                                              ? parseInt(elem?.part?.quantity)
                                              : parseInt(
                                                  elem?.product?.quantity
                                                ))}
                                    </Table.Td>
                                    <Table.Td>
                                      {elem?.manualEntry
                                        ? '-'
                                        : elem?.part?.quantity ||
                                          elem?.product?.quantity}
                                    </Table.Td>
                                    {bomColumns?.map((col) => (
                                      <Table.Td key={col.field}>
                                        {elem?.additionalFields?.[col.field]}
                                      </Table.Td>
                                    ))}
                                  </Table.Row>
                                  {elem?.subItems?.map((subItem) => {
                                    return (
                                      <Table.Row
                                        className="!bg-slate-100"
                                        key={subItem?._id}
                                      >
                                        <Table.Td>{subItem?.category}</Table.Td>
                                        <Table.Td>
                                          {subItem?.part?.name ||
                                            subItem?.product?.name}
                                        </Table.Td>
                                        <Table.Td>{subItem?.units}</Table.Td>
                                        <Table.Td>
                                          {isNaN(
                                            parseInt(subItem?.units) *
                                              parseInt(
                                                po?.orderQuantity[current._id]
                                              )
                                          )
                                            ? '-'
                                            : parseInt(subItem?.units) *
                                              parseInt(
                                                po?.orderQuantity[current._id]
                                              )}
                                        </Table.Td>
                                        <Table.Td>
                                          {subItem?.part?.quantity ||
                                            subItem?.product?.quantity ||
                                            '-'}
                                        </Table.Td>
                                      </Table.Row>
                                    );
                                  })}
                                </>
                              );
                            })}
                          </Table.Body>
                        </Table>
                      </section>
                    </section>
                  </div>
                </Disclosure.Panel>
              </div>
            </Disclosure>
          ))}
      </div>
    </div>
  );
};

export default AssignedAssemblyBom;
