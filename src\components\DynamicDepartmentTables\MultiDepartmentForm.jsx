import { useEffect, useState } from 'react';
import { useLazyGetAllFormQuery } from '../../slices/createFormapiSlice';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import MultiSelect from '../global/components/MultiSelect';
// import Table from '../global/components/Table';

const MultiDepartmentForm = ({ fillForms, setFillForms, disabled = false }) => {
  const [queryform] = useLazyGetAllFormQuery();
  const [formList, setFormList] = useState(null);
  const [selectedFormIndex, setSelectedFormIndex] = useState(null);

  useEffect(() => {
    const getForm = async () => {
      const response = await queryform({ formType: 'Department' }).unwrap();
      setFormList(response);
    };
    getForm();
  }, [queryform]);

  const handleChange = (index, key, value, range) => {
    const updatedForms = fillForms?.map((form, formIndex) => {
      if (index !== formIndex) return form;
      return {
        ...form,
        formData: form?.formData?.map((item, itemIndex) => {
          if (key !== itemIndex) return item;
          if (
            item.fieldType === 'Range' ||
            item.fieldType === 'Range Threshold'
          ) {
            return {
              ...item,
              fieldValue: {
                ...item.fieldValue,
                [range]: +value,
              },
            };
          }
          return {
            ...item,
            fieldValue: value,
          };
        }),
      };
    });
    setFillForms(updatedForms);
  };

  return (
    <div>
      <h1 className="text-xl mb-3">Form Fields</h1>
      <p className={`${disabled ? 'hidden' : ''}  mt-5 text-md font-semibold`}>
        Choose a form to fill
      </p>
      <MultiSelect
        options={formList?.map((item) => ({
          value: item?._id,
          label: item?.formName,
        }))}
        onChange={(e) => {
          const findArr = fillForms?.filter((f) =>
            e.target.value?.some((s) => s?.value === f?._id)
          );
          const missingItems = e.target.value
            ?.filter((s) => !findArr.some((f) => f?._id === s?.value))
            ?.map((s) => formList.find((item) => item._id === s?.value));
          const updatedFillForms = [...findArr, ...missingItems];
          setFillForms(updatedFillForms);
        }}
        value={
          Array.isArray(fillForms) &&
          fillForms?.map((item) => ({
            value: item?._id,
            label: item?.formName,
          }))
        }
        closeOnSelect={true}
        className={`${disabled ? 'hidden' : ''} mb-3 mt-2`}
      />
      <div className="flex flex-wrap gap-1 mb-3">
        {Array.isArray(fillForms) &&
          fillForms?.map((form, index) => (
            <Button
              key={form?._id}
              className={`tab ${index === selectedFormIndex ? 'active' : ''} !h-7 text-sm`}
              onClick={() => setSelectedFormIndex(index)}
            >
              {form?.formName}
            </Button>
          ))}
      </div>
      {/* {selectedFormIndex !== null && (
        <Table className={`mb-3`}>
          <Table.Head>
            <Table.Row>
              <Table.Th>#</Table.Th>
              <Table.Th>Entry</Table.Th>
              <Table.Th>Condition</Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {fillForms?.[selectedFormIndex]?.formData?.map((item, idx) => {
              return (
                <Table.Row key={idx}>
                  <Table.Td className="font-semibold">
                    {item?.fieldName}
                  </Table.Td>
                  {item?.fieldType === 'Range' && (
                    <>
                      <Table.Td>
                        <div className="flex gap-5">
                          <Input
                            className=""
                            placeholder="Min Range"
                            type="number"
                            value={item?.fieldValue?.min || 0}
                            disabled={disabled}
                            onChange={(e) =>
                              handleChange(
                                selectedFormIndex,
                                idx,
                                e.target.value,
                                'min'
                              )
                            }
                          />
                          <Input
                            className=""
                            placeholder="Max Range"
                            type="number"
                            value={item?.fieldValue?.max || 0}
                            disabled={disabled}
                            onChange={(e) =>
                              handleChange(
                                selectedFormIndex,
                                idx,
                                e.target.value,
                                'max'
                              )
                            }
                          />
                        </div>
                      </Table.Td>
                      <Table.Td>
                        {
                          fillForms?.[selectedFormIndex]?.inspectionData?.[idx]
                            ?.min
                        }
                        -
                        {
                          fillForms?.[selectedFormIndex]?.inspectionData?.[idx]
                            ?.max
                        }
                      </Table.Td>
                    </>
                  )}
                  {item?.fieldType === 'Range Threshold' && (
                    <>
                      <Table.Td>
                        <div className="flex gap-5">
                          <Input
                            className=""
                            placeholder="Min Value"
                            value={item?.fieldValue?.min}
                            disabled={disabled}
                            onChange={(e) =>
                              handleChange(
                                selectedFormIndex,
                                idx,
                                e.target.value,
                                'min'
                              )
                            }
                          />
                          <Input
                            className=""
                            placeholder="Threshold Value"
                            value={item?.fieldValue?.max}
                            disabled={disabled}
                            onChange={(e) =>
                              handleChange(
                                selectedFormIndex,
                                idx,
                                e.target.value,
                                'max'
                              )
                            }
                          />
                        </div>
                      </Table.Td>
                      <Table.Td>
                        {fillForms[selectedFormIndex]?.inspectionData[idx]?.min}{' '}
                        -{' '}
                        {fillForms[selectedFormIndex]?.inspectionData[idx]?.max}
                      </Table.Td>
                    </>
                  )}
                  {item?.fieldType === 'String' && (
                    <Table.Td>
                      <Input
                        type="text"
                        className=""
                        placeholder="String"
                        value={item?.fieldValue}
                        disabled={disabled}
                        onChange={(e) =>
                          handleChange(selectedFormIndex, idx, e.target.value)
                        }
                      />
                    </Table.Td>
                  )}
                  {item?.fieldType === 'MultiCheckbox' && (
                    <Table.Td>
                      <Input />
                    </Table.Td>
                  )}
                  {item?.fieldType === 'Date' && (
                    <>
                      <Table.Td>
                        <div className="flex gap-5">
                          <Input
                            className=""
                            type="date"
                            value={item?.fieldValue}
                            disabled={disabled}
                            onChange={(e) =>
                              handleChange(
                                selectedFormIndex,
                                idx,
                                e.target.value
                              )
                            }
                          />
                        </div>
                      </Table.Td>
                      <Table.Td>
                        {fillForms[selectedFormIndex]?.inspectionData[idx]
                          ?.condition === 'gt'
                          ? 'After '
                          : 'Before '}
                        {
                          fillForms[selectedFormIndex]?.inspectionData[idx]
                            ?.value
                        }
                      </Table.Td>
                    </>
                  )}
                  {item?.fieldType === 'Check' && (
                    <Table.Td>
                      <input
                        className="text-[30px]"
                        type="checkbox"
                        value={item?.fieldValue}
                        disabled={disabled}
                        onChange={(e) =>
                          handleChange(selectedFormIndex, idx, e.target.checked)
                        }
                      />
                    </Table.Td>
                  )}
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
      )} */}

      {/* Google form like UI */}

      {selectedFormIndex !== null && (
        <div className="form-container mb-3 mt-10">
          {fillForms?.[selectedFormIndex]?.formData?.map((item, idx) => (
            <div key={idx} className="form-item mb-4  rounded-lg ">
              <label className="block text-md font-semibold mb-2">
                {item?.fieldName}
              </label>

              {item?.fieldType === 'Range' && (
                <div>
                  <div className="flex gap-5">
                    <Input
                      placeholder="Min Range"
                      type="number"
                      value={item?.fieldValue?.min || 0}
                      disabled={disabled}
                      onChange={(e) =>
                        handleChange(
                          selectedFormIndex,
                          idx,
                          e.target.value,
                          'min'
                        )
                      }
                    />
                    <Input
                      placeholder="Max Range"
                      type="number"
                      value={item?.fieldValue?.max || 0}
                      disabled={disabled}
                      onChange={(e) =>
                        handleChange(
                          selectedFormIndex,
                          idx,
                          e.target.value,
                          'max'
                        )
                      }
                    />
                  </div>
                  {/* <p className="text-sm text-gray-500 mt-1">
                    Allowed Range:{' '}
                    {fillForms[selectedFormIndex]?.inspectionData?.[idx]?.min} -{' '}
                    {fillForms[selectedFormIndex]?.inspectionData?.[idx]?.max}
                  </p> */}
                </div>
              )}

              {item?.fieldType === 'Range Threshold' && (
                <div>
                  <div className="flex gap-5">
                    <Input
                      placeholder="Min Value"
                      value={item?.fieldValue?.min}
                      disabled={disabled}
                      onChange={(e) =>
                        handleChange(
                          selectedFormIndex,
                          idx,
                          e.target.value,
                          'min'
                        )
                      }
                    />
                    <Input
                      placeholder="Threshold Value"
                      value={item?.fieldValue?.max}
                      disabled={disabled}
                      onChange={(e) =>
                        handleChange(
                          selectedFormIndex,
                          idx,
                          e.target.value,
                          'max'
                        )
                      }
                    />
                  </div>
                  {/* <p className="text-sm text-gray-500 mt-1">
                    Threshold Range:{' '}
                    {fillForms[selectedFormIndex]?.inspectionData?.[idx]?.min} -{' '}
                    {fillForms[selectedFormIndex]?.inspectionData?.[idx]?.max}
                  </p> */}
                </div>
              )}

              {item?.fieldType === 'String' && (
                <Input
                  type="text"
                  placeholder="Enter text"
                  value={item?.fieldValue}
                  disabled={disabled}
                  onChange={(e) =>
                    handleChange(selectedFormIndex, idx, e.target.value)
                  }
                />
              )}

              {item?.fieldType === 'MultiCheckbox' && <Input />}

              {item?.fieldType === 'Date' && (
                <div>
                  <Input
                    type="date"
                    value={item?.fieldValue}
                    disabled={disabled}
                    onChange={(e) =>
                      handleChange(selectedFormIndex, idx, e.target.value)
                    }
                  />
                  {/* <p className="text-sm text-gray-500 mt-1">
                    {fillForms[selectedFormIndex]?.inspectionData[idx]
                      ?.condition === 'gt'
                      ? 'After '
                      : 'Before '}
                    {fillForms[selectedFormIndex]?.inspectionData[idx]?.value}
                  </p> */}
                </div>
              )}

              {item?.fieldType === 'Check' && (
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    className="text-[30px]"
                    checked={item?.fieldValue}
                    disabled={disabled}
                    onChange={(e) =>
                      handleChange(selectedFormIndex, idx, e.target.checked)
                    }
                  />
                  <span>Check this option</span>
                </label>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MultiDepartmentForm;
