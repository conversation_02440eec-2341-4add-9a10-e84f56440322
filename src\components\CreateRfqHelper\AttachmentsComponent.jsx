import cross from '../../assets/images/cross.png';
import { ReactComponent as JpgPng } from '../../assets/svgs/pdfsvg.svg';
import Table from '../global/components/Table';
import UploadButton from '../UploadButton';
import { Label } from '../v2';
import EditButton from '../v3/global/components/EditButton';
/*
 AttachmentsComponent - A component to manage file attachments, 
 including uploading, displaying, editing, and removing files.
 */
const AttachmentsComponent = ({
  pdf, //List of uploaded PDF files
  setpdf, //Function to update the state of uploaded PDFs
  setEditMediaMetaData, //Function to trigger editing of media metadata
  setShowDescriptionSidebar, //Function to show the description sidebar for attachments
}) => {
  /*
  Handles the change event when files are uploaded.
  Converts files to base64 and updates the state.
   */
  const changeHandler = (e) => {
    for (let i in e) {
      let fname = e[i].name; // Get file name
      let ftype = e[i].type; // Get file type

      const fr = new FileReader(); // Create a new FileReader instance
      if (i === 'length') return; // Skip if 'length' property
      fr.readAsDataURL(e[i]); // Read the file as a data URL
      fr.addEventListener('load', () => {
        const url = fr.result; // Get the file data as a base64 URL
        let data = {
          name: fname,
          type: ftype,
          data: url,
        };
        setpdf((prev) => [...prev, data]); // Update the pdf state with the new file data
      });
    }
  };
  /*
   Removes a PDF from the list of uploaded files.
     */
  const removePdf = (name, pdf, setpdf) => {
    const filtered = pdf.filter(
      (itemm) => (itemm.fname ?? itemm.name) !== name
    ); // Filter out the file to be removed
    setpdf(filtered); // Update the pdf state
  };
  return (
    <>
      <div className="mt-2 col-span-2" id="ignore">
        {/* Section for uploading new attachments */}
        <Label className="mb-1">Attachments</Label>
        <div id="ignore">
          <UploadButton
            accept={'image/*, application/pdf'}
            fileType="JPG/PNG"
            svg={<JpgPng className="h-8" />}
            onChange={(e) => changeHandler(e, 'project')}
            multiple
            className={`text-[#667085]`}
          />
        </div>
      </div>
      {/* Display table if there are any uploaded PDFs */}
      {pdf?.length > 0 && (
        <Table className={`mt-5`} id="ignore">
          {/* table header  */}
          <Table.Head>
            <Table.Row>
              <Table.Th>Files</Table.Th>
              <Table.Th>Description</Table.Th>
              <Table.Th>
                {/* Edit button to trigger metadata editing */}
                <EditButton
                  onClick={() => {
                    setEditMediaMetaData(true);
                  }}
                />
              </Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {/* table body  */}
            {pdf?.map((item, idx) => (
              <Table.Row key={idx}>
                <Table.Td>{item.name}</Table.Td>
                {item?.description?.length > 20 ? (
                  <Table.Td>
                    <span className="break-words">
                      {item?.description?.slice(0, 20)}{' '}
                    </span>
                    <p
                      className="text-blue-500 cursor-pointer inline-block"
                      onClick={() => {
                        setShowDescriptionSidebar(true);
                      }}
                    >
                      <span className="ml-5">Read More...</span>
                    </p>
                  </Table.Td>
                ) : (
                  <Table.Td>{item.description}</Table.Td>
                )}
                {/* Remove button to delete the PDF */}
                <Table.Td
                  onClick={() => {
                    removePdf(item.name, pdf, setpdf);
                  }}
                  className="cursor-pointer"
                >
                  <img src={cross} alt="Cross Icon" className="w-2 h-2" />
                </Table.Td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      )}
    </>
  );
};

export default AttachmentsComponent;
