const SalesDefaults = ({ defaults, setDefaults }) => {
  return (
    <div>
      <h3 className="text-gray-subHeading mb-4">Sales Defaults :</h3>
      <div className="flex flex-col">
        <div className="flex gap-x-4 items-center">
          <input
            type="checkbox"
            checked={defaults?.projectDefaults?.hideAddItems || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  hideAddItems: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Hide Add Item
          </p>
        </div>
        <div className="flex gap-x-4 items-center">
          <input
            type="checkbox"
            checked={defaults?.projectDefaults?.hideProductTemplates || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  hideProductTemplates: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Hide Product Template
          </p>
        </div>
        <div className="flex gap-x-4 items-center">
          <input
            type="checkbox"
            checked={defaults?.projectDefaults?.hideAddFinishGoods || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  hideAddFinishGoods: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Hide Add Finish Good
          </p>
        </div>
        <div className="flex gap-x-4 items-center">
          <input
            type="checkbox"
            checked={defaults?.projectDefaults?.hideCreateProduct || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  hideCreateProduct: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Hide Create Product
          </p>
        </div>
        <div className="flex gap-x-4 items-center">
          <input
            type="checkbox"
            checked={defaults?.projectDefaults?.hideCreateItem || false}
            onChange={(e) => {
              setDefaults((prev) => ({
                ...prev,
                projectDefaults: {
                  ...prev.projectDefaults,
                  hideCreateItem: e.target.checked,
                },
              }));
            }}
          />
          <p className="ml-2 text-sm font-medium text-gray-700">
            Hide Create Item
          </p>
        </div>
      </div>
      <div className=" border-[1px]"></div>
    </div>
  );
};

export default SalesDefaults;
