import { Fragment, useContext, useEffect, useRef, useState } from 'react';
import { MdDeleteOutline } from 'react-icons/md';
import { toast } from 'react-toastify';
import PlusIcon from '../../../assets/images/add.png';
import { Store } from '../../../store/Store';
import Button from '../../global/components/Button';
import Modal from '../../global/components/Modal';
import NewInput from '../../global/components/NewInput';
import initializeWithOptions from './utils/initializeWithOptions';
import renderElemBasedOnFormat from './utils/renderElemBasedOnFormat';
import renderElemBasedOnFormatV1 from './utils/renderElemBasedOnFormatV1';

// do not show reset/plus button for id types mentioned in array bellow
const dontShowButtonsFor = ['itemsPerHour', 'changeOverTime'];

const IdFormat = ({ idType, format, setFormat }) => {
  // const [format, setFormat] = useState({});
  const { defaults: newDefaults } = useContext(Store);
  const [formatComponents, setFormatComponent] = useState({});
  const [dropdown, setDropdown] = useState({
    open: false,
    idType: '',
    type: '',
    options: [],
    idIndex: -1,
  });
  const [showOptions, setShowOptions] = useState({
    open: false,
    selectedIdIndex: -1,
  });
  const [showEditOptions, setShowEditOptions] = useState({
    open: false,
    idType: '',
    type: '',
    selectedIndex: -1,
  });
  const [options, setOptions] = useState([]);
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  // Function to check if the dropdown is close to the bottom
  const checkBottomPosition = () => {
    const dropdown = document.getElementById('dropdown');
    if (dropdown) {
      const dropdownRect = dropdown.getBoundingClientRect();
      const { innerHeight } = window;
      if (dropdownRect.bottom > innerHeight - 100) {
        dropdown.classList.add('-bottom-6');
      } else if (dropdownRect.top > innerHeight / 2) {
        dropdown.classList.add();
      }
    }
  };

  // Function to check if the edit dropdown is close to the bottom
  const checkEditBottomPosition = () => {
    const dropdown = document.getElementById('editDropdown');
    if (dropdown) {
      const dropdownRect = dropdown.getBoundingClientRect();
      const { innerHeight } = window;
      if (dropdownRect.bottom > innerHeight - 100) {
        dropdown.classList.add('-bottom-6');
      } else if (dropdownRect.top > innerHeight / 2) {
        dropdown.classList.add();
      }
    }
  };

  useEffect(() => {
    if (showOptions) {
      checkBottomPosition();
    }
  }, [showOptions]);

  useEffect(() => {
    if (showEditOptions) {
      checkEditBottomPosition();
    }
  }, [showEditOptions]);

  const ref = useRef();
  const editRef = useRef();

  useEffect(() => {
    initializeWithOptions(idType, setOptions);
  }, [idType]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (ref.current && !ref.current.contains(event.target)) {
        setShowOptions((prev) => ({
          ...prev,
          open: false,
        }));
      }
    };
    document.addEventListener('click', handleClickOutside, true);
    return () => {
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (editRef.current && !editRef.current.contains(event.target)) {
        setShowEditOptions((prev) => ({
          ...prev,
          open: false,
          selectedIndex: -1,
        }));
      }
    };
    document.addEventListener('click', handleClickOutside, true);
    return () => {
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, []);

  const addFormatEntry = (option, idIndex) => {
    setFormat((prev) => {
      return {
        ...prev,
        [idType]: [
          ...prev[idType].slice(0, idIndex),
          {
            ...prev[idType][idIndex],
            [`${option}_${Object.keys(prev[idType][idIndex]).length}`]:
              option === 'Increment' ? 1 : '',
          },
          ...prev[idType].slice(idIndex + 1),
        ],
      };
    });
  };

  const addFormatEntryV1 = (option) => {
    setFormat((prev) => {
      return {
        ...prev,
        [idType]: {
          ...prev[idType],
          [`${option}_${Object.keys(prev[idType]).length}`]:
            option === 'Increment' ? 1 : '',
        },
      };
    });
  };

  // ...prev[idType],
  // [`${option}_${Object.keys(prev[idType]).length}`]:
  // option === 'Increment' ? 1 : '',

  const editIDFormatV1 = (idType, type, option) => {
    let objKeys = Object.keys(format?.[idType]);
    let temp = {};
    for (let i in objKeys) {
      if (objKeys[i] === type) {
        temp = {
          ...temp,
          [`${option}_${i}`]:
            option === 'Increment'
              ? newDefaults?.defaultParam?.prefixIds?.[idType][`Increment_${i}`]
              : '',
        };
        // objKeys[i] = `${option}_${Object.keys(prev[idType]).length}`;
      } else {
        temp = {
          ...temp,
          [objKeys[i]]: format[idType][objKeys[i]],
        };
      }
    }
    setFormat((prev) => ({
      ...prev,
      [idType]: temp,
    }));
    setFormatComponent({});
    setShowEditOptions((prev) => ({
      ...prev,
      open: false,
    }));
  };

  const editIDFormat = (idType, type, option, idIndex) => {
    let chosenIdFormat = format[idType][idIndex];
    let objKeys = Object.keys(chosenIdFormat);
    let temp = {};
    for (let i in objKeys) {
      if (objKeys[i] === type) {
        temp = {
          ...temp,
          [`${option}_${i}`]:
            option === 'Increment'
              ? newDefaults?.defaultParam?.prefixIds?.[idType][idIndex][
                  `Increment_${i}`
                ]
              : '',
        };
        // objKeys[i] = `${option}_${Object.keys(prev[idType]).length}`;
      } else {
        temp = {
          ...temp,
          [objKeys[i]]: format[idType][idIndex][objKeys[i]],
        };
      }
    }

    setFormat((prev) => {
      return {
        ...prev,
        [idType]: [
          ...prev[idType].slice(0, idIndex),
          temp,
          ...prev[idType].slice(idIndex + 1),
        ],
      };
    });
    setFormatComponent({});
    setShowEditOptions((prev) => ({
      ...prev,
      open: false,
    }));
  };

  useEffect(() => {
    renderElemBasedOnFormatV1(
      idType,
      format,
      setFormat,
      setFormatComponent,
      setShowEditOptions,
      setDropdown
    );
  }, [format]); //eslint-disable-line

  const addDropdownOptions = () => {
    setDropdown((prev) => ({
      ...prev,
      options: [...(prev?.options || []), ''],
    }));
  };

  return (
    <>
      {dropdown?.open && (
        <Modal
          title={'Set Dropdown Options'}
          onCloseModal={() =>
            setDropdown(() => ({
              open: false,
              idType: '',
              type: '',
              options: [],
            }))
          }
          onAdd={{ label: 'Add', func: [addDropdownOptions], step: [0] }}
          onSubmit={() => {
            setDropdown(() => ({
              open: false,
              idType: '',
              type: '',
              options: [],
            }));
            if (Array.isArray(format?.[dropdown?.idType])) {
              setFormat((prev) => {
                return {
                  ...prev,
                  [dropdown?.idType]: [
                    ...(prev?.[dropdown?.idType]?.slice(0, dropdown?.idIndex) ||
                      []),
                    {
                      ...prev[dropdown?.idType]?.[dropdown?.idIndex],
                      [dropdown?.type]: dropdown?.options,
                    },
                    ...(prev?.[dropdown?.idType]?.slice(
                      dropdown?.idIndex + 1
                    ) || []),
                  ],
                };
              });
            } else {
              setFormat((prev) => {
                return {
                  ...prev,
                  [dropdown?.idType]: {
                    ...prev[dropdown?.idType],
                    [dropdown?.type]: dropdown?.options,
                  },
                };
              });
            }
          }}
        >
          {() => (
            <>
              {dropdown?.options?.map((elem, idx) => (
                <div className="flex items-center gap-2 w-full" key={idx}>
                  <NewInput
                    className="w-full mb-2"
                    placeholder="Enter Option"
                    value={dropdown?.options?.[idx]}
                    onChange={(e) => {
                      setDropdown((prev) => ({
                        ...prev,
                        options: [
                          ...(prev?.options?.slice(0, idx) || []),
                          e.target.value,
                          ...(prev?.options?.slice(idx + 1) || []),
                        ],
                      }));
                    }}
                  />
                  <MdDeleteOutline
                    className="text-2xl mb-2 cursor-pointer hover:text-red-400"
                    onClick={() => {
                      setDropdown((prev) => ({
                        ...prev,
                        options: [
                          ...(prev?.options?.slice(0, idx) || []),
                          ...(prev?.options?.slice(idx + 1) || []),
                        ],
                      }));
                    }}
                  />
                </div>
              ))}
            </>
          )}
        </Modal>
      )}
      <div className="grid grid-cols-5 gap-3 h-full relative mr-8">
        <div className="w-full relative">
          {format?.[idType]?.constructor === Array &&
            format?.[idType]?.map((elem, idx) => {
              let comp = renderElemBasedOnFormat(
                elem,
                idx,
                idType,
                setFormat,
                setShowEditOptions,
                setDropdown
              );
              return (
                <div className="flex mb-1 w-full relative" key={idx}>
                  {comp &&
                    Object.keys(comp).map((elem, eIdx) => {
                      return <Fragment key={eIdx}> {comp[elem]}</Fragment>;
                    })}
                  {dontShowButtonsFor?.includes(idType) ? null : (
                    <div className="w-full ml-3 flex">
                      <span
                        alt="Plus Icon"
                        className="text-white text-2xl font-light cursor-pointer flex items-center justify-center bg-blue-primary hover:bg-blue-hover w-7 h-7 px-1 spy-1 mt-1.5 rounded-full object-contain relative"
                        onClick={() =>
                          setShowOptions((prev) => {
                            return {
                              open: !prev?.open,
                              selectedIdIndex: idx,
                            };
                          })
                        }
                      >
                        +
                      </span>
                      {idx >= defaultParam?.prefixIds?.[idType]?.length && (
                        <Button
                          type="button"
                          className="ml-5 px-9 text-sm"
                          onClick={() => {
                            if (
                              format[idType].some((uformat) => uformat?.isUsed)
                            ) {
                              toast.warning(
                                'Format in use by template. Cannot reset'
                              );
                              return;
                            }
                            setFormat((prev) => {
                              return {
                                ...prev,
                                [idType]: [], // Because this button shows for those entries which are already arrays
                              };
                            });
                          }}
                        >
                          Reset
                        </Button>
                      )}
                      {showOptions?.open &&
                        showOptions?.selectedIdIndex === idx && (
                          <div
                            ref={ref}
                            id="dropdown"
                            className={`flex flex-wrap z-10 flex-col absolute min-w-[12rem] ml-12 mt-2 mb-10 shadow-2xl w-[30%] rounded-md`}
                          >
                            {options.map((option, index) => {
                              const optionToRemove = ['Dropdown'];
                              const yes = Object.keys(comp)?.find((e) =>
                                e.startsWith('Increment_')
                              );
                              if (yes && option === 'Increment') {
                                return null;
                              }
                              if (
                                idType === 'leadId' &&
                                optionToRemove.includes(option)
                              ) {
                                return null;
                              }
                              return (
                                <p
                                  key={index}
                                  className="py-2 px-5 bg-white cursor-pointer w-full hover:bg-sky-100"
                                  onClick={() => {
                                    addFormatEntry(option, idx);
                                    setShowOptions((prev) => ({
                                      ...prev,
                                      open: false,
                                    }));
                                  }}
                                >
                                  {option}
                                </p>
                              );
                            })}
                          </div>
                        )}
                      {showEditOptions?.open &&
                        idx === showEditOptions?.selectedIndex && (
                          <>
                            <div
                              ref={editRef}
                              id="editDropdown"
                              className={`flex flex-wrap z-10 flex-col absolute min-w-[12rem] ml-12 mt-2 mb-10 shadow-2xl w-[30%] rounded-md`}
                            >
                              {options.map((option, index) => {
                                const optionToRemove = ['Dropdown'];
                                const yes = Object.keys(comp)?.find((e) =>
                                  e.startsWith('Increment_')
                                );
                                if (yes && option === 'Increment') {
                                  return null;
                                }
                                if (
                                  idType === 'leadId' &&
                                  optionToRemove.includes(option)
                                ) {
                                  return null;
                                }
                                return (
                                  <p
                                    key={index}
                                    className="py-2 px-5 bg-white cursor-pointer w-full hover:bg-sky-100"
                                    onClick={() => {
                                      editIDFormat(
                                        showEditOptions?.idType,
                                        showEditOptions?.type,
                                        option,
                                        idx
                                      );
                                      setShowOptions((prev) => ({
                                        ...prev,
                                        open: false,
                                      }));
                                    }}
                                  >
                                    {option}
                                  </p>
                                );
                              })}
                            </div>
                          </>
                        )}
                    </div>
                  )}
                </div>
              );
            })}
          {format?.[idType]?.constructor !== Array && (
            <div className="flex mb-1 w-full relative">
              {Object.keys(formatComponents).map((elem, eIdx) => {
                return (
                  <Fragment key={eIdx}> {formatComponents[elem]}</Fragment>
                );
              })}
              {dontShowButtonsFor?.includes(idType) ? null : (
                <div className="w-full ml-3 flex">
                  <img
                    src={PlusIcon}
                    alt="Plus Icon"
                    className="text-white bg-blue-primary hover:bg-blue-hover w-7 h-7 px-1  py-1 mt-1.5 rounded-full object-contain relative cursor-pointer"
                    onClick={() =>
                      setShowOptions((prev) => ({ ...prev, open: !prev.open }))
                    }
                  />

                  <Button
                    type="button"
                    className="ml-5 px-9 text-sm"
                    onClick={() => {
                      setFormat((prev) => {
                        return {
                          ...prev,
                          [idType]: {},
                        };
                      });
                    }}
                  >
                    Reset
                  </Button>
                  {showOptions?.open && (
                    <div
                      ref={ref}
                      id="dropdown"
                      className={`flex flex-wrap z-10 flex-col absolute min-w-[12rem] ml-12 mt-2 mb-10 shadow-2xl w-[30%] rounded-md`}
                    >
                      {options.map((option, index) => {
                        const yes = Object.keys(formatComponents)?.find((e) =>
                          e.startsWith('Increment_')
                        );
                        if (yes && option === 'Increment') {
                          return null;
                        }
                        return (
                          <p
                            key={index}
                            className="py-2 px-5 bg-white cursor-pointer w-full hover:bg-sky-100"
                            onClick={() => {
                              addFormatEntryV1(option);
                              setShowOptions((prev) => ({
                                ...prev,
                                open: false,
                              }));
                            }}
                          >
                            {option}
                          </p>
                        );
                      })}
                    </div>
                  )}
                  {showEditOptions?.open && (
                    <>
                      <div
                        ref={editRef}
                        id="editDropdown"
                        className={`flex flex-wrap z-10 flex-col absolute min-w-[12rem] ml-12 mt-2 mb-10 shadow-2xl w-[30%] rounded-md`}
                      >
                        {options.map((option, index) => {
                          const yes = Object.keys(formatComponents)?.find((e) =>
                            e.startsWith('Increment_')
                          );
                          if (yes && option === 'Increment') {
                            return null;
                          }
                          return (
                            <p
                              key={index}
                              className="py-2 px-5 bg-white cursor-pointer w-full hover:bg-sky-100"
                              onClick={() => {
                                editIDFormatV1(
                                  showEditOptions?.idType,
                                  showEditOptions?.type,
                                  option
                                );
                                setShowOptions((prev) => ({
                                  ...prev,
                                  open: false,
                                }));
                              }}
                            >
                              {option}
                            </p>
                          );
                        })}
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default IdFormat;
