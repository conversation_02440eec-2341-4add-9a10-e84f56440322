import { useEffect, useState } from 'react';
import useFieldsAndTitle from '../../../hooks/useFieldsAndTitle';

const DropDown = ({ data, state, horIdx, verIdx, type, isDisabled }) => {
  const [dropdownData, setDropdownData] = useState([]);
  const [error, setError] = useState({ status: false, message: '' });
  const [horizontalFields, verticalFields, title] = useFieldsAndTitle(data);
  const [relationFilter, setRelationFilter] = useState([]);

  const {
    inputData,
    handleChange,
    renderState,
    dropdownSheet,
    relationsSheet,
  } = state;

  useEffect(() => {
    if (data?.Options?.length > 0) {
      setError({ status: false, message: '' });
      setDropdownData(data?.Options);
    } else if (dropdownSheet && dropdownSheet.data) {
      // if master contains sheet with name DROPDOWN

      setError({ status: false, message: '' });
      const entry = dropdownSheet.data.find(
        (entry) =>
          entry.Parameter === data.Title.split('; ')[horIdx] ||
          entry.Parameter === data.Parameter
      );

      if (entry) {
        // if sheet parameter name and parameter type is same

        setDropdownData([]);
        const values = Object.values(entry);
        const filteredValues = values.filter((val, idx) => idx > 0 && val);
        setDropdownData(filteredValues);
      } else {
        // if sheet parameter name and parameter type is not same

        setError({
          status: true,
          message: `No entry with name ${data.Parameter} found in the DROPDOWN sheet`,
        });
      }
    } else {
      // if master does not contains sheet with name DROPDOWN

      setError({
        status: true,
        message: `No sheet with name "${type}" found in the master`,
      });
    }
  }, [dropdownSheet, data, horIdx, type]);

  useEffect(() => {
    if (relationsSheet && inputData && data) {
      const temp = relationsSheet?.data?.find(
        (item) => item?.Parameter === data?.Parameter
      );
      if (temp) {
        // check to prevent value resetting
        let check = false;
        for (const x in temp) {
          if (x.startsWith('Relation') && temp?.[x]) {
            // split string to according to sepertors
            const relation = temp?.[x]?.split('?');
            const [variable, checkValue] = relation?.[0]?.split('=');
            // do not run again if 1st relation matches
            if (!check && relation?.[1]?.includes(';')) {
              // if value matches
              if (inputData?.[variable]?.value === checkValue) {
                check = true;
                setRelationFilter(relation?.[1]?.split(';'));
              } else {
                setRelationFilter([]);
              }
            }
          }
        }
      }
    }
  }, [relationsSheet, inputData, data]);

  const value =
    (horizontalFields.length > 1 && verticalFields.length > 1
      ? inputData?.[data.Parameter]?.value?.[title[horIdx] + verIdx.toString()]
      : horizontalFields.length > 1
        ? inputData?.[data.Parameter]?.value?.[title[horIdx]]
        : verticalFields.length > 1
          ? inputData?.[data.Parameter]?.value?.[verIdx]
          : inputData?.[data.Parameter]?.value) || '';

  return (
    <>
      {renderState && (
        <>
          {error.status ? (
            <p className="text-red-600 text-[10px]">{error.message}</p>
          ) : (
            <div
              className={`flex border rounded items-center text-gray-700 leading-nomal w-full ${
                verticalFields.length > 1 &&
                verticalFields.length - 1 !== verIdx
                  ? ' mb-2'
                  : ''
              }`}
            >
              <select
                key={data.Parameter + title[horIdx] + verIdx.toString()}
                // name={data.Parameter}
                onChange={handleChange}
                className={`bg-white appearance-none rounded py-0.5 px-3 text-sm text-gray-700 leading-nomal focus:outline-none focus:ring-2 ${
                  data.Units !== '' ? ' w-4/5' : ' w-full border'
                } `}
                // value={
                // 	dropdownData.includes(inputData[data.Parameter].value)
                // 		? inputData[data.Parameter].value
                // 		: 'none'
                // }
                name={
                  horizontalFields.length > 1 && verticalFields.length > 1
                    ? `${data.Parameter}-${
                        title[horIdx]
                      }${verIdx.toString()}-hv`
                    : horizontalFields.length > 1
                      ? `${data.Parameter}-${title[horIdx]}-h`
                      : verticalFields.length > 1
                        ? `${data.Parameter}-${verIdx.toString()}-v`
                        : data.Parameter
                }
                value={value || ''}
                disabled={isDisabled}
              >
                <option value="" hidden={!value}>
                  {value ? 'None' : 'Please select an option'}
                </option>
                {dropdownData.map((option, idx) => {
                  if (
                    relationFilter?.length > 0 &&
                    !relationFilter?.includes(`${idx + 1}`)
                  ) {
                    return null;
                  }
                  return (
                    <option
                      key={option + idx.toString()}
                      value={option}
                      className="text-md"
                    >
                      {option}
                    </option>
                  );
                })}
              </select>
              {data.Units && (
                <span className="text-md w-1/5 h-fit font-normal text-sm text-center">
                  {data.Units}
                </span>
              )}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default DropDown;
