import { useContext, useEffect, useState } from 'react';
import { Store } from '../../store/Store';
import Card from './Card';
import { renderCard } from './cardRendererV2';

const KanbanTile = ({ column, adminView = false, setHistorySidebar }) => {
  const [colCheck, setColCheck] = useState({
    minimized: false,
    disabled: false,
  });

  const { state: { user } = {} } = useContext(Store);

  useEffect(() => {
    // runs only for non admin users
    if (!adminView && user) {
      /**
       * the column object has field id which is same as _id of departemt. So here we check if the user object has access to department
       * by checking it in user.departments array and set the minimzed and disabled state acordingly.
       */
      if (!user.departments.includes(column?.id)) {
        setColCheck({ minimized: true, disabled: true });
      } else {
        setColCheck({ minimized: false, disabled: false });
      }
    }
  }, [column?.id, adminView, user]);

  return (
    <div
      className={`flex flex-col px-[10px] gap-y-5 rounded-md ${colCheck?.disabled ? 'bg-gray-100' : 'bg-transparent'}`}
    >
      {!colCheck?.minimized && (
        <>
          <div
            className={`flex flex-col  gap-y-4 h-full min ${colCheck?.disabled ? 'pointer-events-none' : ''}`}
          >
            {!adminView &&
              column?.cards?.map((card) => {
                return (
                  <>
                    <Card
                      key={card.id}
                      colId={column.id}
                      card={card}
                      column={column}
                      tile={true}
                      setHistorySidebar={setHistorySidebar}
                    />
                  </>
                );
              })}
            {adminView &&
              column?.map((card) =>
                card.cards.map((item, idx) => {
                  return (
                    <div
                      className="px-3 py-2 bg-white rounded-md border overflow-hidden flex flex-col gap-y-1 relative"
                      key={idx}
                    >
                      {renderCard(
                        item?.steps?.[item?.currentStep],
                        item?.steps?.[0],
                        item?.createdAt,
                        item?.updatedAt
                      )}
                    </div>
                  );
                })
              )}
          </div>
        </>
      )}
    </div>
  );
};

export default KanbanTile;
