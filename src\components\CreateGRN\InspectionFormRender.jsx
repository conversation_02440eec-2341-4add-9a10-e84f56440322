import Table from '../global/components/Table';
import { renderInputBasedOnTypeForInspectionSubmit } from '../../helperFunction';
import Input from '../global/components/Input';

const InspectionFormRender = ({
  children,
  currForm,
  refData,
  selectedForm,
  setSelectedForm,
  tableType,
}) => {
  return (
    <div>
      <>
        <div className="mt-7">
          <h2 className="text-center">Fill Form </h2>
          {currForm?.formData.map((form, index) => {
            let key = form?.fieldName;

            if (
              form?.fieldType === 'Table' ||
              form?.fieldType === 'MultiCheckbox'
              // form?.fieldType === 'Min-Max' ||
              // form?.fieldType === 'Media'
            ) {
              return null;
            }

            return (
              <div key={`${index}-${key}`} className="pb-1 mb-3 px-20">
                {/* Field Label */}
                <label className="block text-lg font-medium text-gray-700">
                  {key}
                </label>

                {/* Field Value Display */}
                <div className="mt-1 text-gray-500 text-xs">
                  {form?.fieldType === 'Date' && refData?.[key]?.date}
                  {form?.fieldType === 'String' && refData?.[key]?.string}
                  {form?.fieldType === 'Range' &&
                    `${refData?.[key]?.min} - ${refData?.[key]?.max}`}
                  {form?.fieldType === 'Range Threshold' &&
                    `${refData?.[key]?.average} - ${refData?.[key]?.margin}`}
                  {form?.fieldType === 'DropDown' && refData?.[key]?.value}
                  {form?.fieldType === 'MultiSelect' && (
                    <div className="flex flex-wrap gap-1">
                      {(refData?.[key]?.['value'] || [])?.map((el, eIdx) => (
                        <span
                          key={eIdx}
                          className="border rounded px-2 py-1 bg-gray-100"
                        >
                          {el?.['value']}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                <div className="mt-2">
                  {renderInputBasedOnTypeForInspectionSubmit(
                    form,
                    key,
                    refData,
                    selectedForm,
                    setSelectedForm
                  )}
                </div>
              </div>
            );
          })}

          <div className="px-20">
            {tableType?.length > 0 ? (
              <>
                {tableType?.map((field, idx) => {
                  return (
                    <div className="w-full col-span-2 mt-5" key={idx}>
                      <div className="overflow-x-scroll pb-4 col-span-2 h-48 mt-2">
                        <label className="block text-sm font-medium text-gray-700">
                          {field.fieldName}
                        </label>
                        <Table key={idx}>
                          <Table.Head>
                            <Table.Row>
                              <Table.Th className={`text-center`}>
                                Sr. No.
                              </Table.Th>
                              {field?.tableOptions?.column?.map(
                                (col, colIndex) => {
                                  if (col?.columnType === 'Min-Max') {
                                    return (
                                      <Table.Th
                                        key={colIndex}
                                        className={`text-center !py-2 `}
                                      >
                                        <div className="w-full flex items-center justify-center border-b-2 pb-1">
                                          <p className=" items-start justify-center">
                                            {col?.columnName}
                                          </p>
                                        </div>
                                        {col?.nestedColumns?.length > 0 && (
                                          <div className="flex gap-5">
                                            {col?.nestedColumns?.map(
                                              (cl, colIndex) => {
                                                return (
                                                  <Table.Th
                                                    key={colIndex}
                                                    className={`text-center w-full !py-2`}
                                                  >
                                                    {cl}
                                                  </Table.Th>
                                                );
                                              }
                                            )}
                                          </div>
                                        )}
                                      </Table.Th>
                                    );
                                  } else {
                                    return (
                                      <Table.Th
                                        key={colIndex}
                                        className={`text-center`}
                                      >
                                        {col?.columnName}
                                      </Table.Th>
                                    );
                                  }
                                }
                              )}
                            </Table.Row>
                            {/* <Table.Row>
                              <Table.Th className={`text-center`}>
                                Sr. No.
                              </Table.Th>
                              {field?.tableOptions?.column?.map(
                                (col, colIndex) => (
                                  <Table.Th
                                    key={colIndex}
                                    className={`text-center`}
                                  >
                                    {col?.columnName}
                                  </Table.Th>
                                )
                              )}
                            </Table.Row> */}
                          </Table.Head>
                          <Table.Body>
                            {Array.from({
                              length:
                                field?.tableOptions?.rows ||
                                0 +
                                  selectedForm?.[field?.fieldName]?.newRowNo ||
                                0,
                            }).map((_, bodyRowIndex) => (
                              <Table.Row key={bodyRowIndex}>
                                <Table.Td className={`text-center`}>
                                  {selectedForm?.[field?.fieldName]
                                    ?.newRowNo ? (
                                    <>
                                      <Input
                                        className={'min-w-24 w-full'}
                                        type="text"
                                        value={
                                          selectedForm?.[field?.fieldName]?.row[
                                            bodyRowIndex
                                          ] || ''
                                        }
                                        onChange={(e) => {
                                          setSelectedForm((prev) => {
                                            return {
                                              ...prev,
                                              [field?.fieldName]: {
                                                ...prev[field?.fieldName],
                                                row: prev?.[
                                                  field?.fieldName
                                                ]?.row?.map((itm, index) => {
                                                  if (index === bodyRowIndex) {
                                                    return e.target.value;
                                                  } else {
                                                    return itm;
                                                  }
                                                }),
                                              },
                                            };
                                          });
                                        }}
                                      ></Input>
                                    </>
                                  ) : (
                                    selectedForm?.[field?.fieldName]?.row?.[
                                      bodyRowIndex
                                    ] || bodyRowIndex + 1
                                  )}
                                </Table.Td>
                                {field?.tableOptions?.column?.map(
                                  (col, colIndex) => {
                                    let accesskey = (
                                      bodyRowIndex + 1
                                    ).toString();
                                    if (col?.columnType === 'string') {
                                      return (
                                        <Table.Td
                                          key={colIndex}
                                          className={`text-center`}
                                        >
                                          <Input
                                            type="string"
                                            onChange={(e) => {
                                              setSelectedForm((prev) => ({
                                                ...prev,
                                                [field?.fieldName]: {
                                                  ...prev?.[field?.fieldName],
                                                  ['rowData']: {
                                                    ...prev?.[
                                                      field?.fieldName
                                                    ]?.['rowData'],
                                                    [bodyRowIndex + 1]: prev?.[
                                                      field?.fieldName
                                                    ]?.['rowData']?.[
                                                      bodyRowIndex + 1
                                                    ]?.map((itm, index) =>
                                                      index !== colIndex
                                                        ? itm
                                                        : {
                                                            ...itm,
                                                            value:
                                                              e.target.value,
                                                            type: 'string',
                                                          }
                                                    ),
                                                  },
                                                },
                                              }));
                                            }}
                                            value={
                                              selectedForm?.[
                                                field?.fieldName
                                              ]?.['rowData']?.[accesskey]?.[
                                                colIndex
                                              ]?.value
                                            }
                                            className={`min-w-24 w-full`}
                                          />
                                        </Table.Td>
                                      );
                                    } else if (col?.columnType === 'number') {
                                      return (
                                        <Table.Td
                                          key={colIndex}
                                          className={`text-center`}
                                        >
                                          <Input
                                            type="number"
                                            onChange={(e) => {
                                              setSelectedForm((prev) => ({
                                                ...prev,
                                                [field?.fieldName]: {
                                                  ...prev[field?.fieldName],
                                                  ['rowData']: {
                                                    ...prev[field?.fieldName]?.[
                                                      'rowData'
                                                    ],
                                                    [bodyRowIndex + 1]: prev?.[
                                                      field?.fieldName
                                                    ]?.['rowData']?.[
                                                      bodyRowIndex + 1
                                                    ]?.map((itm, index) =>
                                                      index !== colIndex
                                                        ? itm
                                                        : {
                                                            ...itm,
                                                            value:
                                                              e.target.value,
                                                            type: 'number',
                                                          }
                                                    ),
                                                  },
                                                },
                                              }));
                                            }}
                                            value={
                                              selectedForm?.[
                                                field?.fieldName
                                              ]?.['rowData']?.[accesskey]?.[
                                                colIndex
                                              ]?.value
                                            }
                                            className={`min-w-24 w-full`}
                                          />
                                        </Table.Td>
                                      );
                                    } else if (col?.columnType === 'date') {
                                      return (
                                        <Table.Td
                                          key={colIndex}
                                          className={`text-center`}
                                        >
                                          <Input
                                            type="date"
                                            onChange={(e) => {
                                              setSelectedForm((prev) => ({
                                                ...prev,
                                                [field?.fieldName]: {
                                                  ...prev[field?.fieldName],
                                                  ['rowData']: {
                                                    ...prev[field?.fieldName]?.[
                                                      'rowData'
                                                    ],
                                                    [bodyRowIndex + 1]: prev?.[
                                                      field?.fieldName
                                                    ]?.['rowData']?.[
                                                      bodyRowIndex + 1
                                                    ]?.map((itm, index) =>
                                                      index !== colIndex
                                                        ? itm
                                                        : {
                                                            ...itm,
                                                            value:
                                                              e.target.value,
                                                            type: 'date',
                                                          }
                                                    ),
                                                  },
                                                },
                                              }));
                                            }}
                                            value={
                                              selectedForm?.[
                                                field?.fieldName
                                              ]?.['rowData']?.[accesskey]?.[
                                                colIndex
                                              ]?.value
                                            }
                                            className={`min-w-24 w-full`}
                                          />
                                        </Table.Td>
                                      );
                                    } else if (col?.columnType === 'checkbox') {
                                      return (
                                        <Table.Td
                                          key={colIndex}
                                          className={`text-center`}
                                        >
                                          <Input
                                            type="checkbox"
                                            onChange={(e) => {
                                              setSelectedForm((prev) => ({
                                                ...prev,
                                                [field?.fieldName]: {
                                                  ...prev[field?.fieldName],
                                                  ['rowData']: {
                                                    ...prev[field?.fieldName][
                                                      'rowData'
                                                    ],
                                                    [bodyRowIndex + 1]: prev?.[
                                                      field?.fieldName
                                                    ]?.['rowData']?.[
                                                      bodyRowIndex + 1
                                                    ]?.map((itm, index) =>
                                                      index !== colIndex
                                                        ? itm
                                                        : {
                                                            ...itm,
                                                            value:
                                                              e.target.checked,
                                                            type: 'checkbox',
                                                          }
                                                    ),
                                                  },
                                                },
                                              }));
                                            }}
                                            value={
                                              selectedForm?.[
                                                field?.fieldName
                                              ]?.['rowData']?.[accesskey]?.[
                                                colIndex
                                              ]?.value
                                            }
                                            className={`min-w-24 w-full`}
                                          />
                                        </Table.Td>
                                      );
                                    } else if (col?.columnType === 'Min-Max') {
                                      return (
                                        <Table.Td
                                          key={colIndex}
                                          className={`text-center`}
                                        >
                                          <div className="flex gap-5">
                                            <Input
                                              type="number"
                                              value={
                                                selectedForm?.[
                                                  field?.fieldName
                                                ]?.['rowData']?.[accesskey]?.[
                                                  colIndex
                                                ]?.filledMinValue
                                              }
                                              onChange={(e) => {
                                                setSelectedForm((prev) => ({
                                                  ...prev,
                                                  [field?.fieldName]: {
                                                    ...prev[field?.fieldName],
                                                    ['rowData']: {
                                                      ...prev[field?.fieldName][
                                                        'rowData'
                                                      ],
                                                      [bodyRowIndex + 1]:
                                                        prev?.[
                                                          field?.fieldName
                                                        ]?.['rowData']?.[
                                                          bodyRowIndex + 1
                                                        ]?.map((itm, index) =>
                                                          index !== colIndex
                                                            ? itm
                                                            : {
                                                                ...itm,
                                                                filledMinValue:
                                                                  e.target
                                                                    .value,
                                                                type: 'number',
                                                              }
                                                        ),
                                                    },
                                                  },
                                                }));
                                              }}
                                              className={`min-w-24 w-full`}
                                            />
                                            <Input
                                              type="number"
                                              value={
                                                selectedForm?.[
                                                  field?.fieldName
                                                ]?.['rowData']?.[accesskey]?.[
                                                  colIndex
                                                ]?.filledMaxValue
                                              }
                                              onChange={(e) => {
                                                setSelectedForm((prev) => ({
                                                  ...prev,
                                                  [field?.fieldName]: {
                                                    ...prev[field?.fieldName],
                                                    ['rowData']: {
                                                      ...prev[field?.fieldName][
                                                        'rowData'
                                                      ],
                                                      [bodyRowIndex + 1]:
                                                        prev?.[
                                                          field?.fieldName
                                                        ]?.['rowData']?.[
                                                          bodyRowIndex + 1
                                                        ]?.map((itm, index) =>
                                                          index !== colIndex
                                                            ? itm
                                                            : {
                                                                ...itm,
                                                                filledMaxValue:
                                                                  e.target
                                                                    .value,
                                                                type: 'number',
                                                              }
                                                        ),
                                                    },
                                                  },
                                                }));
                                              }}
                                              className={`min-w-24 w-full`}
                                            />
                                          </div>
                                        </Table.Td>
                                      );
                                    } else {
                                      return (
                                        <Table.Td key={colIndex}>
                                          <span>Unknown Type</span>
                                        </Table.Td>
                                      );
                                    }
                                  }
                                )}
                              </Table.Row>
                            ))}
                          </Table.Body>
                        </Table>
                      </div>
                      <p
                        className={`mt-2 ml-5 ${field?.tableOptions?.rows > 0 ? 'hidden' : ''} text-blue-700 hover:text-blue-500`}
                        onClick={() => {
                          let newRowNo =
                            +selectedForm?.[field?.fieldName]?.newRowNo || 0;
                          setSelectedForm((prev) => ({
                            ...prev,
                            [field?.fieldName]: {
                              ...prev[field?.fieldName],
                              row: [
                                ...prev?.[field?.fieldName]?.row,
                                newRowNo + 1,
                              ],
                              newRowNo: prev?.[field?.fieldName]?.newRowNo + 1,
                              ['rowData']: {
                                ...prev[field?.fieldName]['rowData'],
                                [newRowNo + 1]: prev?.[
                                  field?.fieldName
                                ]?.columns?.map((item) => ({
                                  value: '',
                                  type: item?.columnType,
                                })),
                              },
                            },
                          }));
                        }}
                      >
                        + Add Row
                      </p>
                    </div>
                  );
                })}
              </>
            ) : null}
          </div>

          {/* Submit Button */}
          {/* <div className="flex justify-end mt-6 mb-[250px] mr-20">
            <Button
              className="px-6 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700"
              onClick={handleSubmit}
            >
              Submit
            </Button>
          </div> */}
        </div>
      </>
      {children}
    </div>
  );
};

export default InspectionFormRender;
