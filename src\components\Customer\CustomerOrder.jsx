import { SearchOutlined } from '@ant-design/icons';
import { But<PERSON>, Table, Tabs } from 'antd';
import { Eye } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { FormatDate } from '../../helperFunction';
import useDebounceValue from '../../hooks/useDebounceValue';
import { DEFAULT_STATUS } from '../../pages/CustomerDetails';
import { useGetCustomerFormByIdQuery } from '../../slices/customerFormApiSlice';
import {
  useChangeStatusMutation,
  useGetAllCustomersOrderQuery,
} from '../../slices/customerSalesOrderApiSlice';
import { useGetQuotationStatusesQuery } from '../../slices/quotationApiSlice';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import CustomerForm from '../global/components/CustomerForm';
import Pagination from '../global/components/Pagination';
import RightSidebar from '../global/components/RightSidebar';
import SelectV2 from '../global/components/SelectV2';
import OrderDetailsSidebar from './OrderDetailsSidebar';

const CustomerOrder = () => {
  const [activeTab, setActiveTab] = useState('pending');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearch = useDebounceValue(searchTerm);

  const { data: statuses, sisLoading: isLoadingStatus } =
    useGetQuotationStatusesQuery();

  const { data: ordersData = {}, isLoading } = useGetAllCustomersOrderQuery({
    page,
    limit,
    searchTerm: debouncedSearch,
    tab: activeTab,
  });
  const [formManagementModal, setFormManagementModal] = useState(false);
  const [CustomerFormData, setCustomerFormData] = useState(null);
  const [customerFormId, setCustomerFormId] = useState(null);
  const { data: customerForm } = useGetCustomerFormByIdQuery();
  const [changeStaus] = useChangeStatusMutation();
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [openSidebar, setOpenSidebar] = useState(false);

  useEffect(() => {
    if (customerForm !== undefined) {
      setCustomerFormData(customerForm?.form);
      setCustomerFormId(customerForm?._id);
    }
  }, [customerForm]);

  const handleChangeStatus = async (selectedStatus, id) => {
    try {
      if (!selectedStatus || !id) {
        toast.error('Invalid status or order ID');
        return;
      }

      const allowedStatuses = [
        ...DEFAULT_STATUS,
        ...(statuses?.status?.map((s) => ({
          label: s,
          value: s.toLowerCase(),
        })) || []),
      ];
      if (!allowedStatuses.some((status) => status.value === selectedStatus)) {
        toast.error('Invalid status selected');
        return;
      }

      const response = await changeStaus({
        id,
        status: selectedStatus,
      }).unwrap();

      if (response) {
        toast.success('Status Updated Successfully');
      }
    } catch (error) {
      toast.error(error?.data?.message || 'Failed to update status');
    }
  };

  const columns = [
    {
      title: 'ORDER DATE',
      key: 'createdAt',
      render: (record) => FormatDate(record.createdAt),
    },
    {
      title: 'CUSTOMER',
      key: 'customer',
      render: (record) =>
        record.customerId?.name || record.customerId?.company_name || '',
    },
    {
      title: 'COMPANY NAME',
      key: 'customer',
      render: (record) => record.customerId?.company_name || '',
    },
    {
      title: 'PRODUCTS',
      key: 'products',
      render: (record) => record.products?.length || 0,
    },
    {
      title: 'STATUS',
      key: 'status',
      render: (record) => (
        <SelectV2
          usePortal={true}
          options={[
            ...DEFAULT_STATUS,
            ...(statuses?.status?.map((s) => ({
              label: s,
              value: s?.toLowerCase(),
            })) || []),
          ]}
          value={record.status}
          isLoading={isLoadingStatus}
          onChange={(e) => {
            if (e?.target?.value) {
              handleChangeStatus(e.target.value, record._id);
            }
          }}
          className="w-full max-w-fit"
        />
      ),
    },
    {
      title: 'ACTIONS',
      key: 'actions',
      fixed: 'right',
      render: (record) => (
        <div className="flex gap-2">
          <Button
            type="text"
            ghost
            onClick={() => {
              setOpenSidebar(true);
              setSelectedOrder(record?._id);
            }}
          >
            <Eye className="text-blue-500 hover:text-blue-600" />
          </Button>
        </div>
      ),
    },
  ];

  const items = [
    {
      key: 'pending',
      label: 'Pending Orders',
    },
    {
      key: 'completed',
      label: 'Completed Orders',
    },
  ];

  return (
    <div className="">
      <RightSidebar
        openSideBar={openSidebar}
        setOpenSideBar={setOpenSidebar}
        scale={600}
        title="Order Details"
      >
        <OrderDetailsSidebar id={selectedOrder} />
      </RightSidebar>

      <div className="flex flex-col">
        <div className="flex items-center justify-between border-b border-gray-200">
          <Tabs
            activeKey={activeTab}
            items={items}
            onChange={(key) => {
              setActiveTab(key);
              setPage(1);
            }}
            className="flex-1"
          />

          <div className="flex items-center gap-3">
            <div className="relative">
              <SearchOutlined className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="h-9 pl-9 pr-4 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none text-sm transition-all w-[250px]"
              />
            </div>
            <Button
              type="primary"
              onClick={() => setFormManagementModal(true)}
              className="h-9"
            >
              Manage Forms
            </Button>
          </div>
        </div>

        <div className="mt-2">
          <Table
            columns={columns}
            dataSource={ordersData?.results || []}
            loading={isLoading}
            rowKey="_id"
            scroll={{ x: 'max-content' }}
            pagination={false}
          />

          <Pagination
            limit={limit}
            page={page}
            totalPages={ordersData?.totalPages}
            totalResults={ordersData?.totalCount}
            setPage={setPage}
            setLimit={setLimit}
          />
        </div>
      </div>

      <CustomerForm
        isOpen={formManagementModal}
        setIsOpen={setFormManagementModal}
        customerFormData={CustomerFormData}
        setCustomerFormData={setCustomerFormData}
        customerFormId={customerFormId}
      />
    </div>
  );
};

export default CustomerOrder;
