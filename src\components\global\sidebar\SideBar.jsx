import {
  AdjustmentsIcon,
  CalendarIcon,
  ChartSquareBarIcon,
  CogIcon,
  CurrencyRupeeIcon,
  DocumentIcon,
  DocumentReportIcon,
  DocumentTextIcon,
  ExclamationIcon,
  HomeIcon,
  PresentationChartLineIcon,
  UserCircleIcon,
  ViewGridIcon,
} from '@heroicons/react/24/outline';
import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import noProfile from '../../../assets/images/no_profile.jpg';
import { ReactComponent as Aican } from '../../../assets/svgs/aican.svg';
import Notifications from './Notifications';

const Sidebar = ({ showSideBar, setshowSideBar, user }) => {
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const [showMenu, setShowMenu] = useState(false);

  const isSettingsPage = pathname.includes('/settings/');

  // const navigation = [
  //   {
  //     name: 'Analytical Dashboard',
  //     slug: '/analyticaldashboard',
  //     visibility: !isSettingsPage,
  //     icon: <ChartSquareBarIcon className="h-6 w-6" />,
  //     childNavs: ['downtime'],
  //   },
  //   {
  //     name: 'Dashboard',
  //     slug: '/dashboard',
  //     visibility: !isSettingsPage,
  //     icon: <ViewGridIcon className="h-6 w-6" />,
  //     childNavs: ['active machines'],
  //   },
  //   {
  //     name: 'Report Generation',
  //     slug: '/report',
  //     visibility: !isSettingsPage,
  //     icon: <DocumentReportIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Input Screen',
  //     slug: '/inputscreen',
  //     visibility: !isSettingsPage,
  //     icon: <DocumentIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Manage Projects',
  //     slug: '/manageprojects',
  //     visibility: !isSettingsPage,
  //     icon: <DocumentTextIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Manage Errors',
  //     slug: '/errorcodes',
  //     visibility: !isSettingsPage,
  //     icon: <ExclamationIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Flow Layout',
  //     slug: '/flowlayout',
  //     visibility: !isSettingsPage,
  //     icon: <PresentationChartLineIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Create Dashboard',
  //     slug: '/settings/createdashboard',
  //     visibility: isSettingsPage,
  //     icon: <ViewGridAddIcon className="h-6 w-6" />,
  //   },

  //   {
  //     name: 'Manage Process',
  //     slug: '/settings/manageprocess',
  //     visibility: isSettingsPage,
  //     icon: <PencilAltIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Import Masters',
  //     slug: '/settings/importmasters',
  //     visibility: isSettingsPage,
  //     icon: <DocumentAddIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'User Database',
  //     slug: '/settings/rfid',
  //     visibility: isSettingsPage,
  //     icon: <UserGroupIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Production Flow',
  //     slug: '/settings/productionflow',
  //     visibility: isSettingsPage,
  //     icon: <DocumentIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Create Input Screen',
  //     slug: '/settings/createinput',
  //     visibility: isSettingsPage,
  //     icon: <DocumentAddIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Manage Devices',
  //     slug: '/settings/managedevices',
  //     visibility: isSettingsPage,
  //     icon: <PlusIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Add User',
  //     slug: '/settings/adduser',
  //     visibility: isSettingsPage,
  //     icon: <UserAddIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Activity Logs',
  //     slug: '/settings/logs',
  //     visibility: isSettingsPage,
  //     icon: <DocumentTextIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: 'Manage Analytics',
  //     slug: '/settings/manageanalytics',
  //     visibility: isSettingsPage,
  //     icon: <DocumentAddIcon className="h-6 w-6" />,
  //   },
  //   {
  //     name: "Manage KPI's",
  //     slug: '/settings/managekpis',
  //     visibility: isSettingsPage,
  //     icon: <AdjustmentsIcon className="h-6 w-6" />,
  //   },
  // ]

  const nav = [
    {
      name: 'Setup Module',
      slug: '/settings/module',
      visibility: isSettingsPage,
      icon: <ChartSquareBarIcon className="h-6 w-6" />,
      // childNavs: ['Process', 'Device', 'Worker'],
      childNavs: [
        {
          cname: 'Process',
          cslug: '/settings/module/manageprocess',
        },
        {
          cname: 'Devices',
          cslug: '/settings/module/managedevices',
        },
        {
          cname: 'Worker',
          cslug: '/settings/module/rfid',
        },
      ],
    },

    {
      name: 'Setup Factory',
      slug: '/settings/factory',
      visibility: isSettingsPage,
      icon: <ChartSquareBarIcon className="h-6 w-6" />,
      // childNavs: ['Import Master', 'Project Layout', 'Production Flow'],

      childNavs: [
        {
          cname: 'Import Master',
          cslug: '/settings/factory/importmasters',
        },
        {
          cname: 'Project Layout',
          cslug: '/settings/factory/projectlayout',
        },
        {
          cname: 'Production Flow',
          cslug: '/settings/factory/productionflow',
        },
      ],
    },

    {
      name: 'Accounts',
      slug: '/settings/account',
      visibility: isSettingsPage,
      icon: <ChartSquareBarIcon className="h-6 w-6" />,
      // childNavs: ['Users', 'Activity Logs'],

      childNavs: [
        {
          cname: 'Users',
          cslug: '/settings/account/adduser',
        },
        {
          cname: 'Activity Logs',
          cslug: '/settings/account/logs',
        },
      ],
    },

    {
      name: 'Analytical Dashboard',
      slug: '/analyticaldashboard',
      visibility: !isSettingsPage,
      icon: <ChartSquareBarIcon className="h-6 w-6" />,
      // childNavs: ['downtime'],
      childNavs: [
        {
          cname: 'downtime',
          cslug: '/analyticaldashboard/downtime',
        },
      ],
    },
    {
      name: 'Dashboard',
      slug: '/dashboard',
      visibility: !isSettingsPage,
      icon: <ViewGridIcon className="h-6 w-6" />,
      // childNavs: ['active machines'],
      childNavs: [
        {
          cname: 'active machines',
          cslug: '/dashboard/activemachines',
        },
      ],
    },
    {
      name: 'Report Generation',
      slug: '/report',
      visibility: !isSettingsPage,
      icon: <DocumentReportIcon className="h-6 w-6" />,
    },
    {
      name: 'Input Screen',
      slug: '/jobs/createjobs',
      visibility: !isSettingsPage,
      icon: <DocumentIcon className="h-6 w-6" />,
    },
    {
      name: 'Manage Projects',
      slug: '/manageprojects',
      visibility: !isSettingsPage,
      icon: <DocumentTextIcon className="h-6 w-6" />,
    },
    {
      name: 'Manage Errors',
      slug: '/errorcodes',
      visibility: !isSettingsPage,
      icon: <ExclamationIcon className="h-6 w-6" />,
    },
    {
      name: 'Flow Layout',
      slug: '/flowlayout',
      visibility: !isSettingsPage,
      icon: <PresentationChartLineIcon className="h-6 w-6" />,
    },
  ];

  return (
    <nav
      className={`fixed z-[100] left-0 top-0 h-full transition-[width] ${
        showSideBar ? 'w-sidebar-expand' : 'w-sidebar-collapse'
      }`}
    >
      <div className={`relative bg-white h-full pt-12 pb-5`}>
        <div className="absolute top-[5%] left-1/2 -translate-x-1/2 w-full flex justify-center">
          <Aican onClick={() => setshowSideBar((prev) => !prev)} />
        </div>

        <ul
          className={`absolute top-[20%] left-1/2 -translate-x-1/2 mx-auto h-[70%] w-full overflow-y-scroll no-scrollbar flex flex-col items-center ${
            showSideBar ? 'h-[70%]' : 'h-[60%]'
          }`}
        >
          {nav.map((item, index) => {
            const isActive = pathname.includes(item.slug);

            return (
              item?.visibility && (
                <div className="w-4/5" key={index}>
                  <Link className="w-full" to={item?.slug}>
                    <li className="w-full" title={item.name}>
                      <span
                        className={`flex mt-3 py-2 rounded-md text-lg items-center cursor-pointer font-medium ${
                          showSideBar ? 'px-2' : 'justify-center px-1'
                        } ${
                          isActive
                            ? 'bg-blue-primary text-white shadow-low'
                            : 'hover:text-indigo-600 text-black font-semibold'
                        }`}
                      >
                        {item?.icon}
                        {showSideBar && (
                          <span className="ml-1">{item.name}</span>
                        )}
                      </span>
                    </li>
                  </Link>
                  {showSideBar && isActive && item?.childNavs?.length > 0 && (
                    <ul className="pl-20 text-lg mt-1 text-black list-disc">
                      {item?.childNavs?.map((child, cIdx) => {
                        // const newName =
                        // 	child[0].toUpperCase() + child.substring(1);
                        // const newSlug = `${item.slug}/${child.replace(' ', '')}`
                        const newSlug = child.cslug;

                        return (
                          <Link key={cIdx} to={newSlug}>
                            <li
                              className={`hover:underline mb-1 last:mb-0 capitalize ${
                                pathname.includes(newSlug)
                                  ? 'underline font-medium text-indigo-600'
                                  : ''
                              }`}
                              title={child.cname}
                            >
                              {child.cname}
                            </li>
                          </Link>
                        );
                      })}
                    </ul>
                  )}
                </div>
              )
            );
          })}
        </ul>

        {/* Account and home swithching  */}

        <div
          className={`absolute bottom-[2%] left-1/2 -translate-x-1/2 w-full flex justify-around ${
            showSideBar
              ? 'px-4 items-center h-[6%] min-h-[40px]'
              : 'px-1 flex-col-reverse h-[16%] min-h-[80px]'
          }`}
        >
          <div
            className={`relative aspect-square hover:cursor-pointer z-[101] ${
              showSideBar ? 'h-full' : 'h-10 w-10 mx-auto'
            }`}
            onMouseEnter={() => setShowMenu(true)}
            onMouseLeave={() => setShowMenu(false)}
          >
            <img
              className="aspect-square object-contain rounded-full"
              src={noProfile}
              alt={noProfile}
            />

            <section
              className={`absolute left-0 bottom-0 px-5 py-2 rounded-new bg-gray-primary shadow-low transition-[width,opacity] duration-500 ${
                showMenu
                  ? 'w-[180px] opacity-100'
                  : 'w-2 h-full rounded-full overflow-hidden opacity-0'
              }`}
            >
              {user?.role === 'superuser' ? (
                <p
                  className="flex items-center hover:text-blue-primary gap-x-2 mb-1"
                  onClick={() => navigate('/subscription')}
                >
                  <CurrencyRupeeIcon className="h-4 w-4" />
                  Subscription
                </p>
              ) : null}
              <p
                className="flex items-center hover:text-blue-primary gap-x-2 mb-1"
                onClick={() => navigate('/defaults')}
              >
                <AdjustmentsIcon className="h-4 w-4" />
                Defaults
              </p>
              <p
                className="flex items-center hover:text-blue-primary gap-x-2 mb-1"
                onClick={() => navigate('/calendar')}
              >
                <CalendarIcon className="h-4 w-4" />
                Calendar
              </p>
              <p
                className="flex items-center hover:text-blue-primary gap-x-2"
                onClick={() => navigate('/profile')}
              >
                <UserCircleIcon className="h-4 w-4" />
                Profile
              </p>
            </section>
          </div>

          {showSideBar ? (
            <div>
              <p
                className="text-md font-bold hover:cursor-pointer hover:underline"
                onClick={() => navigate('/profile')}
              >
                {user.name}
              </p>
              <p className="text-sm font-medium">{user.role}</p>
            </div>
          ) : null}

          <Notifications user={user} showSideBar={showSideBar} />

          {isSettingsPage ? (
            <HomeIcon
              onClick={() => navigate('/analytics/production')}
              className={`aspect-square hover:cursor-pointer ${
                showSideBar ? 'h-3/5' : 'h-1/5 max-h-[25px]'
              }`}
            />
          ) : (
            <CogIcon
              onClick={() => navigate('/settings/database/admin')}
              className={`aspect-square hover:cursor-pointer ${
                showSideBar ? 'h-3/5' : 'h-1/5 max-h-[25px]'
              }`}
            />
          )}
        </div>
      </div>
    </nav>
  );
};

export default Sidebar;
