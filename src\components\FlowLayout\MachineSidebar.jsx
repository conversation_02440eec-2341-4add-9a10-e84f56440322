import RightSidebar from '../global/components/RightSidebar';
import Table from '../global/components/Table';

const MachineSidebar = ({
  openSideBar,
  setOpenSideBar,
  allSelectedMachines,
  getLocalDateTime,
  setAllSelectedMachines,
}) => {
  return (
    <RightSidebar
      openSideBar={openSideBar && allSelectedMachines.length > 0}
      setOpenSideBar={(data) => {
        setOpenSideBar(data);
        setAllSelectedMachines([]);
      }}
      scale={736}
    >
      <h4>All Machines</h4>
      <div className="space-y-1 mt-4">
        <Table>
          <Table.Head>
            <Table.Row>
              <Table.Th>Machine ID</Table.Th>
              <Table.Th>START TIME</Table.Th>
              <Table.Th>WORKER NAME</Table.Th>
              <Table.Th>NO. OF PAUSES</Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {allSelectedMachines.map(
              ({ machineId, startTime, noOfPauses, workerName }, index) => {
                return (
                  <Table.Row key={index}>
                    <Table.Td>{machineId}</Table.Td>
                    <Table.Td>{getLocalDateTime(startTime)}</Table.Td>
                    <Table.Td>{workerName}</Table.Td>
                    <Table.Td>{noOfPauses}</Table.Td>
                  </Table.Row>
                );
              }
            )}
          </Table.Body>
        </Table>
      </div>
    </RightSidebar>
  );
};

export default MachineSidebar;
