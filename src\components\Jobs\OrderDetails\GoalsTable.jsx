import { toast } from 'react-toastify';
import { calculate } from '../../../calculateString';
import { DEFAULT_MULTIPLIER_VALUE } from '../../../utils/Constant';
import Table from '../../global/components/Table';
import usePrefixIds from '../../../hooks/usePrefixIds';
import { generateIdForJobBatch } from '../../Defaults/PrefixIds/prefixId.helperFunction';

const GoalsTable = ({
  process,
  goalsTable,
  setGoalsTables,
  isEdit,
  setBatchIdData,
  additionalIdData = {},
}) => {
  const tableData = goalsTable?.tableData;
  const goalsData = goalsTable?.goalsData;

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'batchId',
    setIdData: setBatchIdData,
    additionalIdData,
  });

  const inputHandlerForQuantity = (e, isMultiProcess = false) => {
    const { name: eName, value: eVal } = e.target;
    const splitName = eName.split('$');

    const currInputBatchNo = +splitName?.[1];

    if (+eVal > goalsData?.maxBatch) {
      toast.error(`Cannot exceed max batch size`, {
        position: 'top-right',
        toastId: `${process._id} batch ${currInputBatchNo} max`,
      });
    }

    const value =
      +eVal > goalsData?.maxBatch
        ? +goalsData?.maxBatch
        : +eVal < 1
          ? 1
          : +eVal;

    setGoalsTables((prev) =>
      prev?.map((table) => {
        let diff =
          table?.tableData?.find((i) => i.batchNo === currInputBatchNo)?.[
            'Batch Size'
          ] - value;

        if (diff === 0) {
          return table;
        } else if (diff > 0) {
          let arr = [];

          for (let i = table?.tableData?.length - 1; i >= 0; i--) {
            if (i + 1 === currInputBatchNo) {
              arr[i] = value;
            } else if (i + 1 < currInputBatchNo) {
              arr[i] = table?.tableData?.[i]?.['Batch Size'];
            } else {
              const canAdd =
                goalsData?.maxBatch - table?.tableData?.[i]?.['Batch Size'];
              if (diff > canAdd) {
                arr[i] = table?.tableData?.[i]?.['Batch Size'] + canAdd;
                diff -= canAdd;
              } else {
                arr[i] = table?.tableData?.[i]?.['Batch Size'] + diff;
                diff = 0;
              }
            }
          }

          const totalCheck = arr.reduce((acc, currVal) => (acc += currVal), 0);

          const isEqual = goalsData?.qty === totalCheck;

          if (!isEqual) {
            toast.error(
              `Cannot Change ${process?.processName} batch ${currInputBatchNo} to ${value} `,
              {
                position: 'top-right',
                toastId: `${process._id} batch ${currInputBatchNo} add`,
              }
            );
          }

          if (isMultiProcess) {
            return {
              ...table,
              tableData: isEqual
                ? table?.tableData?.map((i, idx) => ({
                    ...i,
                    ['Batch Size']: arr[idx],
                    newBatchSize: calculate(
                      `${arr[idx]}${
                        table?.multiplier || DEFAULT_MULTIPLIER_VALUE
                      }`
                    ),
                    subProcessData: table?.tableData?.[
                      idx
                    ]?.subProcessData?.map((si) => ({
                      ...si,
                      ['Batch Size']: arr[idx],
                      newBatchSize: calculate(
                        `${arr[idx]}${
                          table?.multiplier || DEFAULT_MULTIPLIER_VALUE
                        }`
                      ),
                    })),
                  }))
                : table?.tableData,
            };
          } else {
            return {
              ...table,
              tableData: isEqual
                ? table?.tableData?.map((i, idx) => ({
                    ...i,
                    ['Batch Size']: arr[idx],
                    newBatchSize: calculate(
                      `${arr[idx]}${
                        table?.multiplier || DEFAULT_MULTIPLIER_VALUE
                      }`
                    ),
                  }))
                : table?.tableData,
            };
          }
        } else {
          let arr = [];

          for (let i = table?.tableData?.length - 1; i >= 0; i--) {
            if (i + 1 === currInputBatchNo) {
              arr[i] = value;
            } else if (i + 1 < currInputBatchNo) {
              arr[i] = table?.tableData?.[i]?.['Batch Size'];
            } else {
              const canSubtract = table?.tableData?.[i]?.['Batch Size'] - 1;

              if (diff * -1 > canSubtract) {
                arr[i] = table?.tableData?.[i]?.['Batch Size'] - canSubtract;
                diff += canSubtract;
              } else {
                arr[i] = table?.tableData?.[i]?.['Batch Size'] + diff;
                diff = 0;
              }
            }
          }

          const totalCheck = arr.reduce((acc, currVal) => (acc += currVal), 0);

          const isEqual = goalsData?.qty === totalCheck;

          if (!isEqual) {
            toast.error(
              `Cannot Change ${process?.processName} batch ${currInputBatchNo} to ${value} `,
              {
                position: 'top-right',
                toastId: `${process._id} batch ${currInputBatchNo} sub`,
              }
            );
          }

          if (isMultiProcess) {
            return {
              ...table,
              tableData: isEqual
                ? table?.tableData?.map((i, idx) => ({
                    ...i,
                    ['Batch Size']: arr[idx],
                    newBatchSize: calculate(
                      `${arr[idx]}${
                        table?.multiplier || DEFAULT_MULTIPLIER_VALUE
                      }`
                    ),
                    subProcessData: table?.tableData?.[
                      idx
                    ]?.subProcessData?.map((si) => ({
                      ...si,
                      ['Batch Size']: arr[idx],
                      newBatchSize: calculate(
                        `${arr[idx]}${
                          table?.multiplier || DEFAULT_MULTIPLIER_VALUE
                        }`
                      ),
                    })),
                  }))
                : table?.tableData,
            };
          } else {
            return {
              ...table,
              tableData: isEqual
                ? table?.tableData?.map((i, idx) => ({
                    ...i,
                    ['Batch Size']: arr[idx],
                    newBatchSize: calculate(
                      `${arr[idx]}${
                        table?.multiplier || DEFAULT_MULTIPLIER_VALUE
                      }`
                    ),
                  }))
                : table?.tableData,
            };
          }
        }
      })
    );
  };

  return (
    <div className="w-full px-8 pb-5">
      {!isEdit && (
        <>
          <label className="text-gray-900 mb-2 font-semibold ">
            {' '}
            Batch ID Format
          </label>
          <IdGenComp {...idCompData} />
        </>
      )}
      <Table>
        <Table.Head>
          <Table.Row>
            <Table.Th>Batch&nbsp;No</Table.Th>
            <Table.Th>Batch&nbsp;Size</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {tableData?.map((data, dIdx) => {
            return (
              <Table.Row key={dIdx}>
                <Table.Td
                  className={`flex gap-x-1 ${
                    data?.status ? 'pointer-events-none !bg-gray-disabled' : ''
                  }`}
                >
                  <div className="flex gap-x-1">
                    {isEdit
                      ? data?.batchName
                      : generateIdForJobBatch(
                          idCompData?.dataToReturn,
                          {
                            ...(idCompData?.dataToReturn || {}),
                            ...(additionalIdData || {}),
                          },
                          dIdx + 1
                        )}
                  </div>
                </Table.Td>
                <Table.Td
                  className={
                    data?.status ? 'pointer-events-none !bg-gray-disabled' : ''
                  }
                >
                  <input
                    type="number"
                    value={data['Batch Size']}
                    disabled={data?.hiddenMac?.length > 0}
                    onChange={inputHandlerForQuantity}
                    onScroll={(e) => e.preventDefault()}
                    min={1}
                    max={goalsData?.maxBatch}
                    name={`Batch Size$${data.batchNo}`}
                    className={`w-full outline-none bg-transparent ${
                      +data['Batch Size'] > +goalsData?.maxBatch
                        ? 'bg-red-primary text-white'
                        : ''
                    }`}
                  />
                </Table.Td>
              </Table.Row>
            );
          })}
        </Table.Body>
      </Table>
    </div>
  );
};

export default GoalsTable;
