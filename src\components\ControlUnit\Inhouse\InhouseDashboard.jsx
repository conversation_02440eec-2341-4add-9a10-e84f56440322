import { useContext, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useAddNewMachineAndOperatorMutation } from '../../../slices/CuProjectNewApiSlice';
import { apiSlice } from '../../../slices/apiSlice';
import { useEditWorkOrderMutation } from '../../../slices/createPoApiSlice';
import { useGetAllErrorMessagesQuery } from '../../../slices/errorMessageApiSlice';
import { Store } from '../../../store/Store';
import Button from '../../global/components/Button';
import Select from '../../global/components/Select';
import AdditionalFieldRender from '../AdditionalFieldRender';
import MachineAndOperator from './MachineAndOperator';

function InhouseDashboard({
  cuProject,
  selectedData,
  allMachines = [],
  allEmployees,
  getAllPo,
  itemForJob,
  setCusForStatus,
  selectedCi,
}) {
  const dispatch = useDispatch();

  const [selectedMachine, setSelectedMachine] = useState('');

  const { state } = useContext(Store);

  const count = cuProject?.machineAndOperator?.reduce(
    (acc, curVal) => acc + +curVal?.manualStopData || 0,
    0
  );

  const { data: { errorMessages } = {} } = useGetAllErrorMessagesQuery();

  const [addNewMachineAndOperator] = useAddNewMachineAndOperatorMutation();
  const [editWorkOrder] = useEditWorkOrderMutation();

  const addMachine = async () => {
    const newTime = new Date();
    const data = {
      machineAndOperator: {
        machine: selectedMachine,
        operator: {
          action: 'start',
          user: state.user._id,
          time: newTime,
        },
        startTime: newTime,
      },
    };

    const res = await addNewMachineAndOperator({
      id: cuProject._id,
      data,
    }).unwrap();

    if (res) {
      await editWorkOrder({
        id: selectedData?.wo,
        data: { latestActivityTime: Date.now(), activity: true },
      }).unwrap();
    }

    dispatch(
      apiSlice.util.invalidateTags(['CuProjectBatch', 'MachineByProject'])
    );
  };

  return (
    <div className="w-full">
      <div className="grid grid-cols-3 gap-2 mt-6">
        <Select
          className="col-span-2"
          value={selectedMachine}
          placeholder="Select Machine"
          onChange={(e) => setSelectedMachine(e.target.value)}
          options={allMachines.map((mac) => ({
            label: mac.machineName,
            value: mac._id,
            disabled: mac.status === 'active',
          }))}
        />
        <Button
          onClick={addMachine}
          disabled={!selectedMachine}
          className={'w-full col-span-1'}
          size="sm"
        >
          Add Machine
        </Button>
      </div>
      <div className=" w-full flex justify-between mt-6 items-center">
        <p>
          Total Count : &nbsp;&nbsp;&nbsp;
          <span>{count}</span>
        </p>
        <p>
          Batch Size : &nbsp;&nbsp;&nbsp;
          <span>
            {cuProject?.batchInfo?.newBatchSize ||
              cuProject?.batchInfo?.['Batch Size']}
          </span>
        </p>
      </div>

      {cuProject?.mqtt?.additionalFields.length > 0 && (
        <div className="w-full mb-2 ">
          <AdditionalFieldRender
            fields={cuProject?.mqtt?.additionalFields || []}
            activeTile={cuProject?._id}
            saveDisabled={!cuProject?.additionalFields ? false : true}
          />
        </div>
      )}

      <div className="flex flex-col mt-1 gap-y-5">
        {cuProject?.machineAndOperator?.map((mao) => {
          if (mao?.status === 'complete') return null;

          return (
            <MachineAndOperator
              woId={selectedData?.wo}
              selectedData={selectedData}
              getAllPo={getAllPo}
              key={mao._id}
              data={mao}
              cuProject={cuProject}
              count={count}
              allEmployees={allEmployees}
              errorMessages={errorMessages?.filter(
                (em) => em.mqtt?._id === selectedData?.flow.mqtt
              )}
              itemForJob={itemForJob}
              setCusForStatus={setCusForStatus}
              selectedCi={selectedCi}
            />
          );
        })}
      </div>
      {/* <div className="grid grid-cols-1 md:grid-cols-3 my-4 gap-3">
        <div>
          <p className="text-[14px] md:text-[18px]">Select Machines</p>
          <p>
            <Select
              value={selectedMachine}
              onChange={(e) => setSelectedMachine(e.target.value)}
              options={allMachines.map((mac) => ({
                label: mac.machineName,
                value: mac._id,
                disabled: mac.status === 'active',
              }))}
            />
          </p>
        </div>
        <p>
          <Button
            onClick={addMachine}
            disabled={!selectedMachine}
            className={'w-full'}
          >
            Add
          </Button>
        </p>
      </div> */}
    </div>
  );
}

export default InhouseDashboard;
