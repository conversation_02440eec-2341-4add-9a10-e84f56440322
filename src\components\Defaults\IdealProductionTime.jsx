import { useEffect } from 'react';
import { useGetAllMachinesQuery } from '../../slices/machineApiSlice';
import Input from '../global/components/Input';

const IdealProductionTimes = ({ defaults, setDefaults }) => {
  const { data: machData = {} } = useGetAllMachinesQuery();
  const { machines = [] } = machData;

  useEffect(() => {
    if (setDefaults) {
      setDefaults((prev) => ({
        ...prev,
        idealProductionTimes: machines.map((item) => ({
          machine: item._id,
          time: '',
        })),
      }));
    }
  }, [setDefaults, machines]);

  const inputHandler = (e) => {
    setDefaults((prev) => {
      const check = prev.idealProductionTimes.find(
        (item) => item?.machine === e.target.name
      );

      if (!check) {
        return {
          ...prev,
          idealProductionTimes: [
            ...(prev.idealProductionTimes || []),
            { machine: e.target.name, time: e.target.value },
          ],
        };
      }

      return {
        ...prev,
        idealProductionTimes: prev.idealProductionTimes.map((item) => {
          if (item.machine === e.target.name) {
            return { machine: e.target.name, time: +e.target.value };
          }
          return item;
        }),
      };
    });
  };

  return (
    <div className="h-screen bg-white">
      <h3 className="text-gray-subHeading">Ideal Production Time:</h3>

      <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-x-10 gap-y-5 mt-3">
        {machines.map((machine) => {
          const value = defaults?.idealProductionTimes?.find(
            (item) => item.machine === machine._id
          );
          return (
            <div key={machine._id} className="flex flex-col gap-2">
              <label
                htmlFor={machine._id}
                className="flex  w-[100px] h-full items-center text-gray-label"
              >
                {machine.machineId}:
              </label>
              <Input
                type="number"
                id={machine._id}
                name={machine._id}
                value={value?.time || ''}
                onChange={inputHandler}
                className="w-full bg-white"
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default IdealProductionTimes;
