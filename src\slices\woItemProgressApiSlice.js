import { apiSlice } from './apiSlice';
const baseRoute = '/v1/woItemProgress';

export const woItemProgressApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    updateWoItemProgress: builder.mutation({
      query: ({ indexId, workOrder, data }) => ({
        url: baseRoute + `/update?indexId=${indexId}&workOrder=${workOrder}`,
        method: 'PUT',
        body: data,
      }),
    }),
  }),
});

export const { useUpdateWoItemProgressMutation } = woItemProgressApiSlice;
