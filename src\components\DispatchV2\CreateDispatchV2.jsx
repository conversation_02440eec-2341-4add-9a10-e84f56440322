import { ArrowLeftOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import { Button, Checkbox, message, Progress, Table } from 'antd';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  useGetAvailableQuantityQuery,
  useLazyGetDispatchV2ByIdQuery,
} from '../../slices/dispatchV2ApiSlice';
import { useGetSalesOrderMutation } from '../../slices/salesOrderSlices';
import MultiSelect from '../global/components/MultiSelect';

const CreateDispatchV2 = () => {
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const soId = params.get('soId');
  const id = params.get('id');
  const [formData, setFormData] = useState({ salesOrders: [] });
  const [itemsTableData, setItemsTableData] = useState([]);
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [getSo, { data: salesOrders }] = useGetSalesOrderMutation();
  const { data: totalDispatchedItems } = useGetAvailableQuantityQuery();
  const [getDispatch, { data: editData }] = useLazyGetDispatchV2ByIdQuery();
  const navigate = useNavigate();

  const handleOrderSelect = (e) => {
    const selectedOrders = e.target.value;
    setSelectedOrders(selectedOrders);
    const updatedSalesOrders = selectedOrders?.map(({ value: order }) => {
      const matchedEditData = editData?.salesOrders?.find(
        (so) => so.salesOrder === order._id
      );
      return {
        salesOrder: order._id,
        items: order?.products
          ?.map((product) => {
            let availableQuantity = getAvailableQuantity(
              order._id,
              product._id,
              product.quantity
            );
            let media = [];
            let disableItem = false;
            let dispatchQuantity = 0;
            const matchedItem = matchedEditData?.items?.find((item) => {
              return item.itemId === product._id;
            });
            if (id !== 'null' && id) {
              if (matchedItem?.availableQuantity) {
                availableQuantity = matchedItem?.availableQuantity;
              }
              if (matchedItem?.media) {
                media = matchedItem?.media;
              }
              dispatchQuantity = matchedItem?.dispatchQuantity || 0;
            }
            if (availableQuantity <= 0 || matchedItem) {
              disableItem = true;
            }
            if (availableQuantity > 0) {
              return {
                originalId: product.value,
                itemId: product._id,
                dispatchQuantity,
                availableQuantity,
                originalQuantity: product.quantity,
                details: product.details,
                media,
                disableItem,
              };
            }
            return null;
          })
          ?.filter(Boolean),
      };
    });
    if (id !== 'null' && id) {
      setFormData((prevData) => ({
        ...prevData,
        dispatchId: editData?.dispatchId,
        additionalFields: editData.additionalFields,
        salesOrders: updatedSalesOrders,
      }));
    } else {
      setFormData((prevData) => ({
        ...prevData,
        salesOrders: updatedSalesOrders,
      }));
    }

    const allProducts = selectedOrders?.flatMap(({ value: order }) =>
      order?.products?.map((product) => {
        let disableItem = false;
        const salesOrderId = order?.salesOrderID;
        const matchedEditData = editData?.salesOrders
          ?.find((so) => so.salesOrder === order._id)
          ?.items?.find((item) => {
            return item.itemId === product._id;
          });

        let availableQuantity = getAvailableQuantity(
          order._id,
          product._id,
          product.quantity
        );
        const quantity = matchedEditData?.availableQuantity;
        if (id !== 'null' && id && quantity) {
          availableQuantity = quantity;
        }
        if (availableQuantity <= 0 || matchedEditData) {
          disableItem = true;
        }
        return {
          ...product,
          availableQuantity: availableQuantity,
          originalQuantity: product.quantity,
          disableItem,
          salesOrderId,
        };
      })
    );

    const dataToSet = allProducts.map((item) => ({
      ...item,
      originalId: item?.value,
      itemId: item?._id,
    }));

    setItemsTableData(dataToSet);
  };

  const getSoOptionsForDropdown = () => {
    return salesOrders
      ?.filter(
        (option) =>
          option.salesOrderStatus === 'approved' ||
          option.salesOrderStatus === 'Approved'
      )
      .map((option) => ({
        label: `${option?.CustomerData?.name} (${option?.salesOrderID})`,
        value: option,
      }));
  };

  useEffect(() => {
    if (id && id !== 'null') {
      getDispatch(id);
      if (editData && salesOrders) {
        const options = getSoOptionsForDropdown();
        const selectedSOs = options.filter((option) =>
          editData?.salesOrders?.some(
            (so) => so?.salesOrder === option?.value?._id
          )
        );
        handleOrderSelect({
          target: {
            value: selectedSOs,
          },
        });
      }
    }
  }, [id, editData, getDispatch, salesOrders]); //eslint-disable-line

  useEffect(() => {
    const options = getSoOptionsForDropdown();
    if (soId && salesOrders) {
      const selectedSO = options?.find((option) => option?.value?._id === soId);
      if (selectedSO) {
        handleOrderSelect({
          target: {
            value: [selectedSO],
          },
        });
      }
    }
  }, [soId, salesOrders]); //eslint-disable-line

  useEffect(() => {
    getSo();
  }, [getSo]);

  const getAvailableQuantity = (salesOrderId, productId, quantity) => {
    const matchingSO = totalDispatchedItems?.find(
      (item) => item?.salesOrder === salesOrderId
    );
    const currentItemTotalDispatched = matchingSO?.items?.find(
      (item) => item?.itemId === productId
    )?.totalDispatchQuantity;
    const availableQuantity = quantity - (currentItemTotalDispatched || 0);
    return availableQuantity > 0 ? availableQuantity : 0;
  };

  const handleItemSelect = (checked, item) => {
    setFormData((prevData) => {
      const salesOrders = prevData.salesOrders.map((so) => {
        return {
          ...so,
          items: checked
            ? [
                ...so.items,
                {
                  originalId: item?.originalId,
                  itemId: item?.itemId,
                  dispatchQuantity: 0,
                  originalQuantity: item?.originalQuantity,
                  availableQuantity: item?.availableQuantity,
                },
              ]
            : so?.items?.filter((i) => i?.itemId !== item?.itemId),
        };
      });
      return { ...prevData, salesOrders };
    });
  };

  const handleNext = () => {
    if (formData.salesOrders.every((so) => so.items.length === 0)) {
      message.error('Please select items to dispatch.');
      return;
    }
    if (id) {
      navigate(`/dispatch/dashboard/dispatch-review/?id=${id}`, {
        state: formData,
      });
    } else if (soId) {
      navigate(`/dispatch/dashboard/dispatch-review/?soId=${soId}`, {
        state: formData,
      });
    } else {
      navigate(`/dispatch/dashboard/dispatch-review`, { state: formData });
    }
  };

  // Handle table filter change
  const handleTableChange = (_, filters) => {
    // Update formData after setting filters
    const newFilters = filters;
    setFormData((prevData) => ({
      ...prevData,
      salesOrders: prevData.salesOrders.map((so) => ({
        ...so,
        items: so.items.filter((item) => {
          const matchingTableItem = itemsTableData.find(
            (tableItem) => tableItem.itemId === item.itemId
          );
          if (!matchingTableItem) return false;

          return Object.entries(newFilters).every(([key, filterValue]) => {
            if (!filterValue || !filterValue.length) return true;

            switch (key) {
              case 'details':
                return filterValue.some(
                  (value) => matchingTableItem.details === value
                );
              case 'availableQuantity':
                return filterValue.some(
                  (value) => matchingTableItem.availableQuantity === value
                );
              case 'salesOrderId':
                return filterValue.some(
                  (value) => matchingTableItem.salesOrderId === value
                );
              default:
                return true;
            }
          });
        }),
      })),
    }));
  };

  const itemsTableColumns = [
    {
      title: 'Select',
      key: 'select',
      render: (_, record) => {
        const isItemSelected = formData?.salesOrders?.some((so) =>
          so?.items?.some((item) => item?.itemId === record?.itemId)
        );

        return (
          <Checkbox
            checked={isItemSelected}
            disabled={record?.disableItem}
            onChange={(e) => handleItemSelect(e.target.checked, record)}
          />
        );
      },
    },
    {
      title: 'Item Details',
      dataIndex: 'details',
      key: 'details',
      filterSearch: true,
      filters: [...new Set(itemsTableData.map((item) => item.details))]
        .filter(Boolean)
        .map((detail) => ({ text: detail, value: detail })),
      onFilter: (value, record) => {
        if (record.disableItem) return true; // Exclude disabled items
        return record.details?.toLowerCase() === value?.toLowerCase();
      },
    },
    {
      title: 'Overall Quantity',
      dataIndex: 'originalQuantity',
      key: 'originalQuantity',
    },
    {
      title: 'Available Quantity',
      dataIndex: 'availableQuantity',
      key: 'availableQuantity',
      filters: [
        ...new Set(itemsTableData.map((item) => item.availableQuantity)),
      ]
        .filter(Boolean)
        .sort((a, b) => a - b)
        .map((quantity) => ({ text: quantity.toString(), value: quantity })),
      onFilter: (value, record) => {
        if (record.disableItem) return true;
        if (value === undefined || value === null) return false;
        return record.availableQuantity === value;
      },
    },
    {
      title: 'Dispatched Progress',
      key: 'dispatchedProgress',
      render: (_, record) => {
        const dispatchedQuantity =
          record?.originalQuantity - record?.availableQuantity;
        const percentage =
          (dispatchedQuantity / record?.originalQuantity) * 100;

        return (
          <Progress
            type="circle"
            percent={Math.min(percentage, 100)}
            size={50}
            strokeWidth={10}
            format={(percent) => (
              <span className="text-[10px] font-semibold">
                {percent?.toFixed(2)}%
              </span>
            )}
          />
        );
      },
    },
    {
      title: 'Sales Order',
      dataIndex: 'salesOrderId',
      key: 'salesOrderId',
      filterSearch: true,
      filters: [...new Set(itemsTableData.map((item) => item.salesOrderId))]
        .filter(Boolean)
        .map((id) => ({ text: id, value: id })),
      onFilter: (value, record) => {
        if (record.disableItem) return true;
        if (!value || !record.salesOrderId) return false;
        return record.salesOrderId === value;
      },
    },
  ];

  return (
    <div className="p-12 bg-white min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold flex items-center">
            <ShoppingCartOutlined className="mr-2" /> Create New Dispatch
          </h1>
          <p className="text-gray-500 mt-1">
            Select orders and items to dispatch
          </p>
        </div>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/dispatch/dashboard')}
        >
          Back
        </Button>
      </div>
      <div className="mb-6">
        <div className="text-sm font-medium text-gray-700 mb-2">
          Select Sales Order
        </div>
        <MultiSelect
          placeholder="Search and select a sales order..."
          value={selectedOrders}
          onChange={handleOrderSelect}
          className="w-full"
          options={getSoOptionsForDropdown()}
          disabled={soId || id}
        />
      </div>

      <Table
        dataSource={itemsTableData}
        columns={itemsTableColumns}
        rowKey="_id"
        scroll={{ x: 800 }}
        pagination={false}
        className="shadow-sm"
        onChange={handleTableChange}
      />

      <div className="flex justify-end mt-6">
        <Button
          type="primary"
          onClick={handleNext}
          disabled={formData?.salesOrders?.every(
            (so) => so?.items?.length === 0
          )}
        >
          Next
        </Button>
      </div>
    </div>
  );
};

export default CreateDispatchV2;
