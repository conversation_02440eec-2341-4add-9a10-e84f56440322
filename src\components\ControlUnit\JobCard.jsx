import { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Store } from '../../store/Store';

import { toast } from 'react-toastify';
import { customConfirm } from '../../utils/customConfirm';
import Tooltip from '../global/components/ToolTip';
import JobStockInModal from './JobStockInModal';
import StatusBar from './StatusBar';

import { apiSlice } from '../../slices/apiSlice';
import { useLazyGetBomByIdQuery } from '../../slices/assemblyBomApiSlice';
import { useUpdateCiForForceStopMutation } from '../../slices/createInputApiSlice';
import WOInfoModal from './WOInfoModal';

const JobCard = ({
  itemName,
  ci,
  idx,
  selectedData,
  getWO,
  selectedWo,
  itemId,
  item,
  setSelectedData,
  setShowOutSrc,
  setIsOpenSidebar,
  setSideBarData,
  setSelectedCi,
  setSelectedCiIdx,
  isSelected,
}) => {
  const { defaultParam } = useContext(Store);
  const dispatch = useDispatch();

  const [isNotStarted, setIsNotStarted] = useState(false);
  const [activeBatches, setActiveBatches] = useState([]);
  const [itemTobeStockedOut, setItemTobeStockedOut] = useState([]);
  const [showForceStop, setShowForceStop] = useState(false);
  const [itemData, setItemData] = useState(null);
  const [showItem, setShowItem] = useState(false);

  const [updateCiForForceStop, { isLoading: isLoadingStop }] = //eslint-disable-line
    useUpdateCiForForceStopMutation();
  const [getBomById] = useLazyGetBomByIdQuery();

  let stopMethod = defaultParam?.projectDefaults?.defaultJobStopType || 'none';

  const reset = () => {
    setShowForceStop(false);
    setIsNotStarted(false);
    setItemTobeStockedOut([]);
    setActiveBatches([]);
    getWO();
  };

  const updateCiForForceStopFxn = async () => {
    let id = ci?._id;
    const res = await updateCiForForceStop({
      id: id,
      data: {
        isForceStopped: true,
      },
    });
    if (res) {
      reset();
      toast.success('Job is force stopped!');
      dispatch(apiSlice.util.invalidateTags(['ForceStop']));
    }
  };

  useEffect(() => {
    if (ci) {
      let notStartedBatches = [];
      let itemTobeStockedOutPre = [];
      let activeBatchesInfo = [];
      let isNotStartedArr = [];
      let noOfBatches = ci?.goalsTable?.[0]?.tableData?.length;
      let batchesWithProcessInfo = Array.from(
        { length: noOfBatches },
        (_, i) => i + 1
      )?.map((i) => {
        return {
          batchNo: i,
          process: ci?.goalsTable?.map((j) => {
            return {
              flowId: j?.flowId,
              mqttId: j?.mqtt?._id,
              batchInfo: j?.tableData?.find((k) => k?.batchNo === i),
              status:
                j?.tableData?.find((k) => k?.batchNo === i)?.status || 'NA',
              correspondingCuProject:
                ci?.cuProjects?.find(
                  (cuProject) =>
                    cuProject?.batchInfo?.batchNo === i &&
                    cuProject?.flowId === j?.flowId
                ) || 'NA',
            };
          }),
        };
      });

      batchesWithProcessInfo?.forEach((batch) => {
        let completedProcesses = batch?.process?.filter(
          (process) => process?.status === 'complete'
        );

        let act = batch?.process?.filter((pro) => pro?.status === 'start');

        let isNotStr = batch?.process?.filter(
          (process) => process?.status === 'NA'
        );

        if (isNotStr?.length === batch?.process?.length) {
          isNotStartedArr.push(batch?.batchNo);
        }

        // to prevent force stop when any batch is active if lenght >0
        if (act?.length > 0) activeBatchesInfo.push(batch);

        if (batch?.process?.length !== completedProcesses?.length) {
          let processesArray = batch?.process;

          let lastCompletedProcess = processesArray
            ?.slice()
            .reverse()
            .find((process) => process?.status === 'complete');

          if (lastCompletedProcess) {
            itemTobeStockedOutPre.push(lastCompletedProcess);
          } else {
            notStartedBatches.push(batch);
          }
        }
      });

      if (isNotStartedArr?.length === batchesWithProcessInfo?.length) {
        setIsNotStarted(true);
      }
      setActiveBatches(() => activeBatchesInfo);
      setItemTobeStockedOut(() => itemTobeStockedOutPre);
    }
  }, [ci, selectedData]);
  return (
    <>
      {showItem && (
        <WOInfoModal
          showItem={showItem}
          setShowItem={setShowItem}
          itemData={itemData}
          setItemData={setItemData}
          selectedWo={selectedWo}
        />
      )}
      {showForceStop && (
        <JobStockInModal
          setShowForceStop={setShowForceStop}
          productFromCu={item}
          method="forceStop"
          itemStockOutData={itemTobeStockedOut}
          reset={reset}
          updateCiForForceStop={updateCiForForceStopFxn}
        />
      )}
      <div
        className={`border-solid p-2 rounded-lg w-full min-w-[20rem] ${isSelected ? 'border-[2px] border-[#b7b7ff]' : 'border-[1px] border-slate-200'}`}
        onClick={() => {
          setSelectedCi(ci);
          setSelectedCiIdx(idx);
        }}
      >
        <div className="flex items-center justify-between">
          <h6>
            {itemName?.length > 30 ? (
              <Tooltip
                text={itemName}
                maxWidth={'!max-w-[500px]'}
                minWidth={'!min-w-[250px]'}
              >
                {itemName?.slice(0, 30) + '...'}
              </Tooltip>
            ) : (
              itemName || '-'
            )}
          </h6>
          {false && (
            <button
              disabled={ci?.isForceStopped}
              title="Force Stop will stop the current job and will not allow to restart."
              onMouseOver={(e) => {
                e.currentTarget.setAttribute(
                  'data-tooltip',
                  'Force Stop will stop the current job and will not allow to restart.'
                );
              }}
              onMouseOut={(e) => {
                e.currentTarget.removeAttribute('data-tooltip');
              }}
              onClick={async () => {
                if (ci?.isForceStopped) {
                  toast.error('Job is already force stopped');
                  return;
                }
                if (isNotStarted) {
                  toast.error('Job is not started yet');
                  return;
                }

                if (activeBatches.length > 0) {
                  toast?.error(
                    'Please stop all active ongoing job to force stop'
                  );
                  return;
                }
                const confirm = await customConfirm(
                  'Are you sure you want to force stop this job?',
                  'stop'
                );
                if (!confirm) return;
                if (stopMethod === 'manual') {
                  if (activeBatches.length > 0) {
                    toast?.error(
                      'Please stop all active ongoing job to force stop'
                    );
                    return;
                  }

                  if (itemTobeStockedOut?.length === 0) {
                    updateCiForForceStopFxn();
                  } else if (itemTobeStockedOut?.length > 0) {
                    setShowForceStop(true);
                  }
                } else if (stopMethod === 'none') {
                  updateCiForForceStopFxn();
                } else {
                  toast.error('Method under Development!');
                }
              }}
              type="button"
              className="text-[12px] px-[5px] border-[2px] border-[#ef3737] py-[2px] rounded-[8px] text-nowrap text-[#ee6666] "
            >
              Force Stop
            </button>
          )}
        </div>
        <StatusBar id={ci?._id} type={'job'} />
        <hr />
        <div className="flex justify-between items-center">
          <button
            type="button"
            onClick={() => {
              if (selectedWo?.type !== 'Assembly') {
                // let item = {};
                // item = selectedWo?.items?.find(
                //   (item) =>
                //     item?._id === itemId ||
                //     item?.manualEntry === ci?.assemblyManualEntry
                // );
                setShowItem(true);
                setItemData(item);
              } else {
                if (itemId) {
                  getBomById({ id: itemId }).then((res) => {
                    setShowItem(true);
                    setItemData(res.data);
                  });
                }
              }
            }}
            className="text-[12px] pt-2 rounded-[8px] text-[#549feffc]"
          >
            Info
          </button>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedData((prev) => ({
                ...prev,
                model: ci,
                // flow: flow,
                productionFlow: ci?.productionFlow,
              }));

              setShowOutSrc(true);
            }}
            disabled={ci?.isForceStopped}
            className={`text-[12px] pt-2 rounded-[8px] text-[#549feffc] ${
              ci?.isForceStopped && 'cursor-not-allowed text-slate-400'
            }`}
          >
            Outsource
          </button>
          <button
            type="button"
            className="text-[12px] pt-2 rounded-[8px] text-[#549feffc] "
            onClick={() => {
              setIsOpenSidebar(true);
              setSideBarData(ci);
            }}
          >
            Qc Data
          </button>
        </div>
      </div>
    </>
  );
};

export default JobCard;
