// import React from 'react'
import { useEffect, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { useLocation, useSearchParams } from 'react-router-dom';
import { mobileWidth, tabletWidth } from '../../../helperFunction.js';
import useDebounceValue from '../../../hooks/useDebounceValue.js';
import {
  useAddQuotationMutation,
  useGetQuotationPagesQuery,
  useUpdateQuotationsMutation,
} from '../../../slices/quotationApiSlice.js';
import { PAGINATION_LIMIT } from '../../../utils/Constant.js';
import Header from '../../global/components/Header.jsx';
import Pagination from '../../global/components/Pagination.jsx';
import QuotationInfoTiles from './QuotationInfoTiles.jsx';
import QuotationModal from './QuotationModal';
import QuotationTable from './QuotationTable';
// import QuotationPDF from './QuotationPDF.jsx';
// import { generateDateString } from '../../../helperFunction.js';

const Quotation = () => {
  const location = useLocation();
  const data = location.state;
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [isAdd, setIsAdd] = useState(
    searchParams.get('kanban') === 'true' ? true : false
  );
  const [isQuotationEdit, setIsQuotationEdit] = useState(false);
  const [editData, setEditData] = useState();
  const [copyData, setCopyData] = useState({});
  const [isCopy, setIsCopy] = useState(false);
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const [expired, setExpired] = useState(false);
  const [field, setField] = useState('createdAt');
  const [type, setType] = useState('desc');
  const [allQuotation, setAllQuotation] = useState([]);
  const debounceSearch = useDebounceValue(searchTerm || '');
  const [opportunityCustomer, setOpportunityCustomer] = useState(null);
  const [showFilters, setShowFilters] = useState(true);
  const [filters, setFilters] = useState([]);

  useEffect(() => {
    if (data) {
      setIsAdd(true);
      setOpportunityCustomer(data.customer);
    }
  }, [data]);

  const [add, { isLoading: isAddQuotationLoading }] = useAddQuotationMutation();
  const { data: quoteData = {}, isLoading: isQuotationLoading } =
    useGetQuotationPagesQuery(
      {
        page,
        limit,
        expired,
        debounceSearch,
        filters,
        type,
        field,
      },
      { skip: !page || !limit, refetchOnMountOrArgChange: true }
    );

  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const [updateQuotations, { isLoading: isUpdateQuotationLoading }] =
    useUpdateQuotationsMutation();

  const { results: allData = [], totalPages = 0, totalResults = 1 } = quoteData;

  useEffect(() => {
    setAllQuotation(allData);
    /* eslint-disable-next-line */
  }, [quoteData]);
  useEffect(() => {
    if (allData?.length === 0) return;
    if (searchParams.get('navigateTo') && searchParams.get('editid')) {
      const quotationToEdit = allData?.find((quote) => {
        return quote?._id === searchParams.get('editid');
      });
      setEditData(quotationToEdit);
      setIsQuotationEdit(true);
    }
  }, [searchParams, allData]);

  return (
    <div>
      <div className="w-full rounded-new">
        <div className="w-full flex flex-row justify-between">
          {!isAdd && !isQuotationEdit && !isCopy && (
            <div className="flex flex-col w-full">
              <div className="flex items-center gap-[5px] w-full">
                <div className="w-full flex flex-row justify-between items-center">
                  <Header
                    title="Quotation"
                    description=""
                    infoTitle="Welcome to the Quotation Page"
                    infoDesc="Your hub for generating new Quotations with ease."
                    paras={[
                      'The Quotation page simplifies the process of creating and managing quotes. Quickly generate, review, and track quotations with ease, enhancing your quoting efficiency. Easily communicate pricing details and streamline your quotation workflow.',
                    ]}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
        {!isAdd && !isQuotationEdit && !isCopy && (
          <QuotationInfoTiles isMobile={isMobile} />
        )}
        {isAdd || isQuotationEdit || isCopy ? (
          <div
            className={`${!isMobile && !isTablet && 'mx-48 !bg-white border-x-2 !px-12'} !bg-white px-4`}
          >
            <QuotationModal
              isMobile={isMobile}
              isTablet={isTablet}
              setIsAdd={setIsAdd}
              isAdd={isAdd}
              opportunityCustomer={opportunityCustomer}
              method={
                isAdd || isCopy
                  ? add
                  : isQuotationEdit
                    ? updateQuotations
                    : null
              }
              isQuotationLoading={
                isAdd || isCopy
                  ? isAddQuotationLoading
                  : isQuotationEdit
                    ? isUpdateQuotationLoading
                    : false
              }
              isEdit={isQuotationEdit}
              isCopy={isCopy}
              setIsCopy={setIsCopy}
              data={isQuotationEdit ? editData : copyData}
              setIsQuotationEdit={setIsQuotationEdit}
              quotation={allData}
              leadId={data?.leadId}
            />
          </div>
        ) : (
          <>
            <QuotationTable
              data={allData}
              setExpired={setExpired}
              expired={expired}
              setField={setField}
              type={type}
              setType={setType}
              isQuotationEdit={isQuotationEdit}
              setIsQuotationEdit={setIsQuotationEdit}
              editData={editData}
              copyData={copyData}
              setCopyData={setCopyData}
              setEditData={setEditData}
              setIsCopy={setIsCopy}
              updateQuotations={updateQuotations}
              field={field}
              setSearchTerm={setSearchTerm}
              isAdd={isAdd}
              isCopy={isCopy}
              allQuotation={allQuotation}
              setIsAdd={setIsAdd}
              isMobile={isMobile}
              filters={filters}
              setFilters={setFilters}
              showFilters={showFilters}
              setShowFilters={setShowFilters}
              isQuotationLoading={isQuotationLoading}
            />
            <Pagination
              limit={limit}
              page={page}
              totalPages={totalPages}
              totalResults={totalResults}
              setPage={setPage}
              setLimit={setLimit}
              className={`w-full`}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default Quotation;
