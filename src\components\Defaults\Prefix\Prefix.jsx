import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import PlusIcon from '../../../assets/images/add.png';
import DeleteIcon from '../../../assets/images/delete_black.png';
import { customConfirm } from '../../../utils/customConfirm';
import IdFormat from './IdFormat';

const Prefix = ({ defaults, setDefaults }) => {
  const [format, setFormat] = useState({
    workOrderId: [],
    jobId: {},
    modelName: {},
    inputScreen: {},
    batchId: {},
    vendorId: {},
    storeId: {},
    partsId: {},
    productId: {},
    saId: {},
    chalanId: [],
    inventoryBatchNo: {},
    lotNo: {},
    qr: {},
    itemsPerHour: {},
    changeOverTime: {},
    assemblyBomId: {},
    poId: [],
    taskId: {},
    salesOrderId: [],
    salesInquiryId: [],
    quotationId: [],
    customerId: [],
    outsourceId: {},
    ncrId: [],
    leadId: [],
    grnId: [],
    renewalId: [],
    dailyTaskId: [],
    dispatchId: [],
    quotationVersion: [],
  });
  const [activeTab, setActiveTab] = useState({
    general: true,
    inventory: false,
  });

  const selectedStyle = `px-6 py-3 text-xs md:text-sm font-medium text-blue-primary outline-none w-auto bg-blue-100 overflow-hidden`;
  const nonSelectedStyle =
    ' px-6 py-3 text-xs md:text-sm font-medium text-gray-900 bg-gray-100 outline-none w-auto overflow-hidden';

  const initializeWithDefaultFormat = () => {
    const updateProperty = (property, defaultValue) => {
      if (
        Object?.keys(defaults?.prefixIds?.[property] || {}).length === 0 ||
        defaults?.prefixIds?.[property] === undefined
      ) {
        temp = {
          ...temp,
          [property]: defaultValue,
        };
      } else {
        temp = {
          ...temp,
          [property]: defaults?.prefixIds?.[property],
        };
      }
    };

    let temp = format;

    updateProperty('workOrderId', [{ Increment_0: 1 }]);
    updateProperty('assemblyBomId', { Increment_0: 1 });
    updateProperty('jobId', { WorkOrderId_0: '', Increment_1: 1 });
    updateProperty('modelName', { WorkOrderId_0: '', UserEntry_1: '' });
    updateProperty('inputScreen', { Increment_0: 1 });
    updateProperty('batchId', { Increment_1: 1 });
    updateProperty('storeId', { Increment_0: 1 });
    updateProperty('vendorId', { Increment_0: 1 });
    updateProperty('partsId', { Increment_0: 1 });
    updateProperty('productId', { Increment_0: 1 });
    updateProperty('saId', { Increment_0: 1 });
    updateProperty('chalanId', [{ Increment_0: 1 }]);
    updateProperty('inventoryBatchNo', { Increment_0: 1 });
    updateProperty('lotNo', { Increment_0: '' });
    updateProperty('qr', {});
    updateProperty('itemsPerHour', { String_0: 'iph' });
    updateProperty('changeOverTime', { String_0: 'cot' });
    updateProperty('poId', [{ Increment_0: 1 }]);
    updateProperty('taskId', { Increment_0: 1 });
    updateProperty('salesOrderId', [{ Increment_0: 1 }]);
    updateProperty('salesInquiryId', [{ Increment_0: 1 }]);
    updateProperty('quotationId', [{ Increment_0: 1 }]);
    updateProperty('customerId', [{ Increment_0: 1 }]);
    updateProperty('outsourceId', { Increment_0: 1 });
    updateProperty('ncrId', [{ Increment_0: 1 }]);
    updateProperty('leadId', [{ Increment_0: 1 }]);
    updateProperty('grnId', [{ Increment_0: 1 }]);
    updateProperty('renewalId', [{ Increment_0: 1 }]);
    updateProperty('dailyTaskId', [{ Increment_0: 1 }]);
    updateProperty('dispatchId', [{ Increment_0: 1 }]);
    updateProperty('quotationVersion', [{ Increment_0: 1 }]);

    setFormat(temp);
  };

  useEffect(() => {
    // setFormat((prev) => {
    //   return {
    //     ...prev,
    //     ...defaults?.prefixIds,
    //   };
    // });
    initializeWithDefaultFormat();
  }, []); //eslint-disable-line

  useEffect(() => {
    setDefaults((prev) => {
      return {
        ...prev,
        prefixIds: format,
      };
    });
  }, [format]); //eslint-disable-line

  const addIdTemplate = (idType) => {
    // setFormat((prev) => {
    //   return {
    //     ...prev,
    //     [idType]: [...(prev[idType] || []), {}],
    //   };
    // });
    setFormat((prev) => {
      return {
        ...prev,
        [idType]: Array.isArray(prev[idType])
          ? [...(prev[idType] || []), {}]
          : [prev[idType], {}],
      };
    });
  };

  const validatePrefixId = () => {
    let canReset = true;
    for (let prefixId of Object.keys(defaults?.prefixIds)) {
      if (
        Array.isArray(defaults?.prefixIds[prefixId]) &&
        defaults?.prefixIds[prefixId].some((id) => id.isUsed)
      ) {
        canReset = false;
      } else if (
        !Array.isArray(defaults?.prefixIds[prefixId]) &&
        defaults?.prefixIds[prefixId].isUsed
      ) {
        canReset = false;
      }
    }
    return canReset;
  };

  return (
    <div className="border rounded-lg overflow-x-auto border-[#d0d5dd] bg-white mt-2">
      <div className="w-full border-b-2 overflow-hidden flex justify-between items-center">
        <div className="inline-flex shadow-sm overflow-hidden  border border-t-0 border-l-0 border-[#d0d5dd] gap-x-px bg-[#d0d5dd] w-fit">
          <button
            onClick={() => setActiveTab({ inventory: false, general: true })}
            className={activeTab?.general ? selectedStyle : nonSelectedStyle}
          >
            General
          </button>
          <button
            onClick={() => setActiveTab({ general: false, inventory: true })}
            className={activeTab?.inventory ? selectedStyle : nonSelectedStyle}
          >
            Inventory
          </button>
        </div>
        <div
          onClick={async () => {
            const canReset = validatePrefixId();
            if (!canReset) {
              toast.error(
                'Cannot reset Prefix ID that has been used in a template'
              );
              return;
            }
            const confirmation = await customConfirm(
              `Are you sure you want to reset the prefix data of ${
                activeTab?.general ? 'general' : 'inventory'
              } tab?`,
              'delete'
            );
            if (confirmation) {
              activeTab?.general
                ? setFormat({
                    ...format,
                    workOrderId: [],
                    jobId: {},
                    modelName: {},
                    inputScreen: {},
                    batchId: {},
                    qr: {},
                    taskId: {},
                    salesInquiryId: [],
                    salesOrderId: [],
                    quotationId: [],
                    customerId: [],
                    outsourceId: {},
                    ncrId: [],
                  })
                : setFormat({
                    ...format,
                    vendorId: {},
                    storeId: {},
                    partsId: {},
                    productId: {},
                    saId: {},
                    chalanId: [],
                    inventoryBatchNo: {},
                    lotNo: {},
                  });
            }
          }}
          className="flex items-center cursor-pointer text-xs md:text-sm mr-2 md:mr-8"
        >
          <img src={DeleteIcon} alt="Delete Icon" className="w-8 h-8 mr-2" />
          <div className="">Reset All</div>
        </div>
      </div>
      <div className="w-full relative m-8">
        {activeTab?.general && (
          <>
            {/* <h3 className="text-gray-subHeading">General:</h3> */}
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Work Order ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative cursor-pointer"
                  onClick={() => addIdTemplate('workOrderId')}
                />
              </div>
              <IdFormat
                idType="workOrderId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>

            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Assembly BOM ID: </h5>
                {/* <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('assemblyBomId')}
                /> */}
              </div>
              <IdFormat
                idType="assemblyBomId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>

            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Job ID: </h5>
                {/* <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('jobId')}
                /> */}
              </div>

              <IdFormat
                idType="jobId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Model Name: </h5>
                {/* <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('modelName')}
                /> */}
              </div>
              <IdFormat
                idType="modelName"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Input Screen: </h5>
                {/* <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('inputScreen')}
                /> */}
              </div>
              <IdFormat
                idType="inputScreen"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Batch ID: </h5>
                {/* <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('batchId')}
                /> */}
              </div>
              <IdFormat
                idType="batchId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">
                  Purchase Order ID:{' '}
                </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('poId')}
                />
              </div>
              <IdFormat
                idType="poId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>

            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">QR: </h5>
                {/* <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('qr')}
                /> */}
              </div>
              <IdFormat
                idType="qr"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Items Per Hour: </h5>
                {/* <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('itemsPerHour')}
                /> */}
              </div>
              <IdFormat
                idType="itemsPerHour"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Change Over Time: </h5>
                {/* <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('changeOverTime')}
                /> */}
              </div>
              <IdFormat
                idType="changeOverTime"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Task Id: </h5>
                {/* <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('taskId')}
                /> */}
              </div>
              <IdFormat
                idType="taskId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Sales Order ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('salesOrderId')}
                />
              </div>
              <IdFormat
                idType="salesOrderId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Sales Inquiry ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('salesInquiryId')}
                />
              </div>
              <IdFormat
                idType="salesInquiryId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Quotation ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('quotationId')}
                />
              </div>
              <IdFormat
                idType="quotationId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Customer ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('customerId')}
                />
              </div>
              <IdFormat
                idType="customerId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Outsource Job ID: </h5>
              </div>
              <IdFormat
                idType="outsourceId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">NCR ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('ncrId')}
                />
              </div>
              <IdFormat
                idType="ncrId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Lead ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('leadId')}
                />
              </div>
              <IdFormat
                idType="leadId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">GRN ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('grnId')}
                />
              </div>
              <IdFormat
                idType="grnId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Renewal ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('renewalId')}
                />
              </div>
              <IdFormat
                idType="renewalId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Daily Tasks ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('dailyTaskId')}
                />
              </div>
              <IdFormat
                idType="dailyTaskId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Dispatch Id: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('dispatchId')}
                />
              </div>
              <IdFormat
                idType="dispatchId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">
                  Quotation Version :
                </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('quotationVersion')}
                />
              </div>
              <IdFormat
                idType="quotationVersion"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
          </>
        )}

        {activeTab?.inventory && (
          <>
            {/* <h3 className="text-gray-subHeading">Inventory:</h3> */}
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <h5 className="w-[8rem] text-gray-label">Store ID: </h5>
              <IdFormat
                idType="storeId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <h5 className="w-[8rem] text-gray-label">Vendor ID: </h5>
              <IdFormat
                idType="vendorId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <h5 className="w-[8rem] text-gray-label">Parts ID: </h5>
              <IdFormat
                idType="partsId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <h5 className="w-[8rem] text-gray-label">Product ID: </h5>
              <IdFormat
                idType="productId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <h5 className="w-[8rem] text-gray-label">SubAssembly ID: </h5>
              <IdFormat
                idType="saId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="flex flex-col flex-wrap  gap-3 w-full relative mt-5">
              <div className="flex items-center">
                <h5 className="w-[8rem] text-gray-label">Challan ID: </h5>
                <img
                  src={PlusIcon}
                  alt="Plus Icon"
                  className="text-white bg-blue-primary hover:bg-blue-hover w-5 h-5 rounded-full object-contain relative"
                  onClick={() => addIdTemplate('chalanId')}
                />
              </div>
              <IdFormat
                idType="chalanId"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            {/* <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <h5 className="w-[8rem] text-gray-label">Challan ID: </h5>
              <IdFormat
                idType="chalanId"
                format={format}
                setFormat={setFormat}
              />
            </div> */}
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <h5 className="w-[8rem] text-gray-label">Inventory Batch No: </h5>
              <IdFormat
                idType="inventoryBatchNo"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
            <div className="grid-cols-5 gap-3 w-full relative mt-5">
              <h5 className="w-[8rem] text-gray-label">Lot No: </h5>
              <IdFormat
                idType="lotNo"
                format={format}
                setFormat={setFormat}
                defaults={defaults}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Prefix;
