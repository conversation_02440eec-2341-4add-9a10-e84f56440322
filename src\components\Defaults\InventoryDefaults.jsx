import { useState } from 'react';
import ButtonGroup from '../global/components/ButtonGroup';
import AssetDefaults from './AssetDefaults';
import BomDefaults from './BomDefaults';
import CategoryDefaults from './CategoryDefaults';
import MachineJobDefaults from './MachineJobDefaults';
import PartsDefaults from './PartsDefaults';
import StoreDefaults from './StoreDefaults';
import VendorDefaults from './VendorDefaults';
import WorkOrderCategoryManagement from './WorkOrderCategoryManagement';

const buttons = [
  'Vendors',
  'Stores',
  'Parts',
  'Assets',
  'Category',
  'BOM',
  'Inhouse',
  'Work Order Category',
];

const InventoryDefaults = ({ defaults, setDefaults }) => {
  const [selectVal, setSelectVal] = useState('Vendors');
  return (
    <div className="w-full flex flex-col gap-y-5 h-screen bg-white">
      <span className="">
        <ButtonGroup
          className={'w-full text-nowrap overflow-x-auto'}
          buttons={buttons}
          value={selectVal}
          onChange={(val) => setSelectVal(val)}
        />
      </span>

      {selectVal === 'Vendors' ? (
        <VendorDefaults defaults={defaults} setDefaults={setDefaults} />
      ) : selectVal === 'Stores' ? (
        <StoreDefaults defaults={defaults} setDefaults={setDefaults} />
      ) : selectVal === 'Parts' ? (
        <PartsDefaults defaults={defaults} setDefaults={setDefaults} />
      ) : selectVal === 'Assets' ? (
        <AssetDefaults defaults={defaults} setDefaults={setDefaults} />
      ) : selectVal === 'Category' ? (
        <CategoryDefaults defaults={defaults} setDefaults={setDefaults} />
      ) : selectVal === 'BOM' ? (
        <BomDefaults defaults={defaults} setDefaults={setDefaults} />
      ) : selectVal === 'Inhouse' ? (
        <MachineJobDefaults defaults={defaults} setDefaults={setDefaults} />
      ) : selectVal === 'Work Order Category' ? (
        <WorkOrderCategoryManagement
          defaults={defaults}
          setDefaults={setDefaults}
        />
      ) : null}
    </div>
  );
};

export default InventoryDefaults;
