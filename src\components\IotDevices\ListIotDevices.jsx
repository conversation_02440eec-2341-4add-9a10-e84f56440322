import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { calculateIndexForPagination } from '../../helperFunction';
import {
  useDeleteIotDeviceMutation,
  useQueryIotDevicesQuery,
} from '../../slices/iotDeviceApiSlice';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import { customConfirm } from '../../utils/customConfirm';
import Pagination from '../global/components/Pagination';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';

function ListIotDevices({ setOpenModal, setEditData }) {
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams({
    page: 1,
    limit: PAGINATION_LIMIT,
  });

  const page = +searchParams.get('page');
  const limit = +searchParams.get('limit');

  const {
    data: queryData = {},
    isLoading: isLoadingGet,
    isFetching: isFetchingGet,
  } = useQueryIotDevicesQuery(
    { page, limit },
    { skip: !page || !limit, refetchOnMountOrArgChange: true }
  );
  const { results: iotDevices, totalPages, totalResults } = queryData;

  const [deleteIotDevice] = useDeleteIotDeviceMutation();

  const handleDelete = async (id) => {
    const confirm = await customConfirm(
      'Are you sure you want to delete IoT Device?',
      'delete'
    );
    if (!confirm) return;

    const res = await deleteIotDevice({ id }).unwrap();

    if (res) {
      toast.success('IoT device deleted successfully', { toastId: 'delete' });
    }
  };

  return (
    <div className="w-full">
      {isLoadingGet ? (
        <Spinner />
      ) : (
        <>
          <div className="w-full overflow-x-scroll">
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Th>#</Table.Th>
                  <Table.Th>Name</Table.Th>
                  <Table.Th>DeviceId</Table.Th>
                  <Table.Th>Fields</Table.Th>
                  <Table.Th>Profile</Table.Th>
                  <Table.Th></Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                {iotDevices.map((item, iIdx) => (
                  <Table.Row key={item._id} isFetching={isFetchingGet}>
                    <Table.Td>
                      {calculateIndexForPagination(page, limit, iIdx)}
                    </Table.Td>
                    <Table.Td>{item.name}</Table.Td>
                    <Table.Td>{item.deviceId}</Table.Td>
                    <Table.Td>
                      <span className="link-hover">{item.fields?.length}</span>
                    </Table.Td>
                    <Table.Td>{item.profileId?.name}</Table.Td>
                    <Table.Options
                      onEdit={() => {
                        setOpenModal(true);
                        setEditData(item);
                      }}
                      onCalibrate={() => {
                        navigate(`${item?._id}`);
                      }}
                      onDelete={() => {
                        handleDelete(item._id);
                      }}
                    />
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
          <Pagination
            totalPages={totalPages}
            totalResults={totalResults}
            page={page}
            limit={limit}
            setPage={(val) =>
              setSearchParams(
                (prev) => {
                  prev.set('page', val);
                  return prev;
                },
                { replace: true }
              )
            }
            setLimit={(val) =>
              setSearchParams(
                (prev) => {
                  prev.set('limit', val);
                  return prev;
                },
                { replace: true }
              )
            }
          />
        </>
      )}
    </div>
  );
}

export default ListIotDevices;
