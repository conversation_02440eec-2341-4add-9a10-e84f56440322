import { useContext, useState } from 'react';
import { useDispatch } from 'react-redux';

// import { useNavigate } from 'react-router-dom';
// import { mqttActions } from '../../helperFunction';
import { useCreateCuProjectMutation } from '../../slices/CuProjectAPiSlice';
import { apiSlice } from '../../slices/apiSlice';
import {
  useCreateCuNotificationMutation,
  useDeleteCuNotificationMutation,
} from '../../slices/cuNotificationApiSlice';
import { useEditGoalsTableMutation } from '../../slices/goalsTableapiSlice';
import { useCreateSkeletonMutation } from '../../slices/outsourceJobApiSlice';
// import { useLazyGetMachineByIdQuery } from '../../slices/machineApiSlice';
import {
  // useEditPoMutation,
  useEditWorkOrderMutation,
} from '../../slices/createPoApiSlice';
import { useCreateNotificationMutation } from '../../slices/notificationApiSlice';
import { useCreateProcessGoalDataMutation } from '../../slices/processGoalDataApiSlice';
import { Store } from '../../store/Store';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Select from '../global/components/Select';
import StartOutsource from './StartOutsource';

function StartCu({
  selectedData,
  setCuProject,
  allMachines = [],
  mqttData,
  allEmployees,
  setRefreshMachine,
  handleAfterStart,
  itemUsed = {},
  setCusForStatus,
  selectedCi,
}) {
  const [selectedMachine, setSelectedMachine] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [dateTime, setDateTime] = useState('');
  const {
    defaults,
    // state,
    mqttClient,
    refreshParams: { refreshUrl, refreshPayload },
  } = useContext(Store);
  // const navigate = useNavigate();
  const dispatch = useDispatch();

  const defaultNoti = defaults?.defaultParam?.notificationDefaults;
  const [createProcessGoalData] = useCreateProcessGoalDataMutation();
  const [createNotification] = useCreateNotificationMutation();
  const [createCuNotification] = useCreateCuNotificationMutation();
  const [deleteCuNotification] = useDeleteCuNotificationMutation();
  const [editGoalsTable] = useEditGoalsTableMutation();
  // const [editPo] = useEditPoMutation();
  const [editWorkOrder] = useEditWorkOrderMutation();
  const [createSkeleton] = useCreateSkeletonMutation();
  // const [getMachineById] = useLazyGetMachineByIdQuery({});

  const [createCuProject] = useCreateCuProjectMutation();

  const tempProcesses = selectedData?.productionFlow?.processes;
  const tempProcess = selectedData?.flow;
  const tempProcessIndex = tempProcesses?.indexOf(tempProcess);
  // const prevProcess = tempProcesses?.[tempProcessIndex - 1];
  const isMultiProcess = tempProcess?.isMultiProcess;

  let nextProcess = null;
  if (tempProcesses) {
    if (tempProcessIndex === tempProcesses?.length - 1) {
      nextProcess = null;
    } else if (
      tempProcesses[tempProcessIndex + 1]?.processCategory === 'Outsource'
    ) {
      nextProcess = null;
    } else {
      nextProcess = tempProcesses[tempProcessIndex + 1]?.mqtt;
    }
  }

  let processCategory = selectedData?.flow?.processCategory;

  const submitHandler = async (outData = {}) => {
    const newTime = new Date();

    const proType = selectedData?.flow?.processCategory;

    // const isQc = proType === 'QC';
    // const isAssembly = proType === 'Assembly';

    let data = {};
    if (proType === 'Inhouse') {
      let temp = selectedData?.model?.outSourceJobs
        ?.find((el) => el?.processId === selectedData?.flow?._id)
        ?.batchData?.find(
          (el) => el?.batchNo === (selectedData?.batchInfo?.batchNo).toString()
        )?.outQuantity;
      temp = temp ? temp : 0;

      let newBatchSize = +selectedData?.batchInfo?.['Batch Size'] - +temp;

      let batchInfo = {
        ...selectedData?.batchInfo,
        ['Batch Size']: newBatchSize,
        newBatchSize: newBatchSize,
      };

      data = {
        project: selectedData?.model._id,
        mqtt: mqttData._id,
        startTime: dateTime || newTime,
        cuLoginMachineId: mqttData.machineid?.id,
        batchInfo: batchInfo || selectedData?.batchInfo,
        productionFlow: selectedData?.productionFlow?._id,
        flowId: selectedData?.flow._id,
        isMultiProcess: false,
        machineAndOperator: {
          machine: selectedMachine,
          operator: {
            action: 'start',
            user: selectedEmployee || null,
            time: dateTime || newTime,
          },
          startTime: newTime,
        },
      };
    } else if (proType === 'Outsource') {
      data = [
        {
          data: {
            project: selectedData?.model._id,
            isOutwarded: true,
            mqtt: mqttData._id,
            startTime: dateTime || newTime,
            batchInfo: selectedData?.batchInfo,
            productionFlow: selectedData?.productionFlow?._id,
            flowId: selectedData?.flow._id,
            isMultiProcess: false,
            type: 'Outsource',
          },
          currentCuId: null,
        },
      ];
    } else {
      // for qc and assembly
      data = {
        project: selectedData?.model._id,
        mqtt: mqttData._id,
        startTime: dateTime || newTime,
        cuLoginMachineId: mqttData.machineid?.id,
        batchInfo: selectedData?.batchInfo,
        productionFlow: selectedData?.productionFlow?._id,
        flowId: selectedData?.flow._id,
        isMultiProcess: false,
        type: proType,
        [proType === 'QC' ? 'qcData' : 'assemblyData']: {
          operator: selectedEmployee || null,
        },
      };
    }

    const res = await createCuProject({
      data,
      type: proType === 'Inhouse' ? '' : proType.toLowerCase(),
    }).unwrap();
    // .then((response) => {
    //   setCuProject(response);
    //   if (proType === 'Outsource') {
    //     navigate('/inward');
    //   }
    // });

    if (res) {
      // if (proType === 'Outsource') {
      //   setCuProject(res?.cuProject[0]);
      // } else {
      //   setCuProject(res?.cuProject);
      // }

      // create a Notification if stopped
      let currentDate = new Date();
      const tempId = [...Array(24)]
        .map(() => Math.floor(Math.random() * 10))
        .join('');
      setCusForStatus((prev) => {
        return [
          ...prev,
          {
            status: 'active',
            batchInfo: selectedData?.batchInfo,
            flowId: selectedData?.flow?._id,
            project: selectedCi?._id,
            startTime: currentDate,
            _id: tempId,
          },
        ];
      });

      if (proType === 'Outsource' && outData) {
        let data = {
          ...outData,
          cuProject: res.cuProject[0]._id,
        };

        await createSkeleton({ data }).unwrap();
      }
      // if (selectedData?.v2 === true) {
      await editWorkOrder({
        id: selectedData?.wo,
        data: { latestActivityTime: Date.now(), activity: true },
      }).unwrap();
      // } else {
      //   await editPo({
      //     id: selectedData?.wo,
      //     data: { latestActivityTime: Date.now(), activity: true },
      //   }).unwrap();
      // }

      const resp = await createProcessGoalData({
        data: {
          cuProject:
            proType === 'Outsource' ? res.cuProject[0]._id : res.cuProject._id,
          requiredData: selectedData?.batchInfo,
        },
      }).unwrap();

      if (defaultNoti?.Start) {
        const response = await createNotification({
          data: {
            createdAt: new Date(),
            cuProject:
              proType === 'Outsource'
                ? res.cuProject[0]._id
                : res.cuProject._id,
            type: 'start',
          },
        }).unwrap();

        if (response) {
          if (mqttClient?.publish)
            mqttClient?.publish(refreshUrl, `ACTION_CHANGE`);
        }
      }

      if (nextProcess) {
        const finaldata = {
          action: 'start',
          mqttId: nextProcess,
          batchNo: selectedData.batchInfo.batchNo,
          createInputId: selectedData?.model?._id,
          index: tempProcessIndex,
          cuProject:
            proType === 'Outsource' ? res.cuProject[0]._id : res.cuProject._id,
        };

        await createCuNotification({ data: finaldata }).unwrap();
      }

      if (tempProcessIndex > 0) {
        const delCuNotificationQuery = {
          batchNo: selectedData.batchInfo.batchNo,
          index: tempProcessIndex - 1,
          createInputId: selectedData?.model?._id,
        };

        await deleteCuNotification({ data: delCuNotificationQuery }).unwrap();
      }

      if (tempProcessIndex === 0) {
        const delCuNotificationQuery = {
          index: tempProcessIndex,
          createInputId: selectedData?.model?._id,
          action: 'create',
        };

        await deleteCuNotification({ data: delCuNotificationQuery }).unwrap();
      }

      if (mqttClient?.publish)
        mqttClient?.publish(
          '/IP/REFRESH/IOT/CONTROLUNIT/APP',
          `REFRESH_PREV_TILE`
        );

      // get tabble data according to process and project
      // const tabData = createInputs
      //   ?.find((input) => input._id === selectedData?.model?._id)
      //   ?.goalsTable.find((table) => table.flowId === selectedData?.flow?._id);
      const tabData = selectedData?.model?.goalsTable.find(
        (table) => table.flowId === selectedData?.flow?._id
      );

      // only run if tabdata exists and is a multiprocess
      if (tabData && isMultiProcess) {
        const tableData = tabData.tableData.map((table) => {
          // modify table subprocess batch data
          if (table.batchNo === selectedData?.batchInfo?.batchNo) {
            return {
              ...table,
              status: 'start',
              subProcessData: table?.subProcessData?.map((item, iIdx) => {
                if (iIdx === selectedData?.subProcessIndex) {
                  return { ...item, status: 'start' };
                }
                return item;
              }),
            };
          }
          return table;
        });

        if (tableData.length > 0)
          await editGoalsTable({
            id: tabData._id,
            data: {
              tableData,
            },
          }).unwrap();
      } else if (tabData && !isMultiProcess) {
        const tableData = tabData.tableData.map((table) => {
          // modify table subprocess batch data
          if (table.batchNo === selectedData?.batchInfo?.batchNo) {
            return {
              ...table,
              status: 'start',
            };
          }
          return table;
        });

        if (tableData.length > 0)
          await editGoalsTable({
            id: tabData._id,
            data: {
              tableData,
            },
          }).unwrap();
      }

      if (resp) {
        //Don't delete commented line below - for automatic machine
        // if (!isQc && !isAssembly)
        //   mqttActions(getMachineById, mqttClient, 'start', selectData?.machine);
        if (mqttClient?.publish)
          mqttClient?.publish(refreshUrl, refreshPayload);
      }

      if (proType === 'Outsource') {
        setCuProject(() => res?.cuProject[0]);
      } else {
        setCuProject(() => res?.cuProject);
      }
    }
    setRefreshMachine((prev) => !prev);

    // For Inspection Dashboard Page
    if (typeof handleAfterStart !== 'undefined') {
      handleAfterStart();
    }

    dispatch(apiSlice.util.invalidateTags(['CuProject', 'CuProjectBatch']));
  };

  // useEffect(() => {
  //   const currentDate = new Date();
  //   const formattedDateTime = currentDate.toLocaleString().slice(0, 16); // Format to 'YYYY-MM-DDTHH:MM'
  //   setDateTime(formattedDateTime);
  // }, []);

  return (
    <div className="w-full">
      {processCategory !== 'Outsource' ? (
        <div className="w-full px-4">
          <h3 className=" mb-2 text-[18px] leading-none font-medium">
            Start Cu Project
          </h3>
          <hr className="mb-2" />
          <div className="grid grid-cols-2 gap-3">
            <p className="font-semibold">Model</p>
            <p>{selectedData?.model?.modelName}</p>
            <p className="font-semibold">Process</p>
            <p>{selectedData?.flow?.processName}</p>
            <p className="font-semibold">Batch</p>
            <p>{selectedData?.batchInfo?.batchName}</p>
            <p className="font-semibold">Type</p>
            <p>{selectedData?.flow?.processCategory}</p>

            <>
              <p className="font-semibold">Start time</p>
              <p>
                <Input
                  type="datetime-local"
                  placeholder="Start time"
                  name="StartTime"
                  value={dateTime}
                  onChange={(e) => setDateTime(e.target.value)}
                />
              </p>
            </>
            <>
              <p className="font-semibold">Operator</p>
              <p>
                <Select
                  placeholder="Select Operator"
                  value={selectedEmployee}
                  onChange={(e) => setSelectedEmployee(e.target.value)}
                  options={allEmployees?.map((mac) => ({
                    label: mac?.name,
                    value: mac?._id,
                    // disabled: mac.status === 'active',
                  }))}
                />
              </p>
            </>

            {selectedData?.flow?.processCategory === 'Inhouse' ? (
              <>
                <p>Machines</p>
                <p>
                  <Select
                    placeholder="Select Machines"
                    value={selectedMachine}
                    onChange={(e) => setSelectedMachine(e.target.value)}
                    options={allMachines?.map((mac) => ({
                      label: mac.machineName,
                      value: mac._id,
                      disabled: mac.status === 'active',
                    }))}
                  />
                </p>
              </>
            ) : null}
          </div>

          <Button
            onClick={submitHandler}
            disabled={
              selectedData?.flow?.processCategory === 'Inhouse' &&
              !selectedMachine
            }
            className={'md:w-1/3 w-full mx-auto mt-10'}
          >
            Start
          </Button>
        </div>
      ) : (
        <div>
          <div className="grid grid-cols-2 gap-3">
            <p className="font-semibold">Model</p>
            <p>{selectedData?.model?.modelName}</p>
            <p className="font-semibold">Process</p>
            <p>{selectedData?.flow?.processName}</p>
            <p className="font-semibold">Batch</p>
            <p>{selectedData?.batchInfo?.batchName}</p>
            <p className="font-semibold">Type</p>
            <p>{selectedData?.flow?.processCategory}</p>

            {/* <>
              <p>Start time</p>
              <p>
                <Input
                  type="datetime-local"
                  placeholder="Start time"
                  name="StartTime"
                  value={dateTime}
                  onChange={(e) => setDateTime(e.target.value)}
                />
              </p>
            </> */}
          </div>
          <StartOutsource
            itemUsed={itemUsed}
            outQunatity={selectedData?.batchInfo['Batch Size']}
            wo={selectedData?.wo}
            // dateTime={dateTime}
            submitHandler={submitHandler}
          />
        </div>
      )}
    </div>
  );
}

export default StartCu;
