import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../../../components/global/components/Button';
import { FormatDate } from '../../../helperFunction';
import RightSidebar from '../../global/components/RightSidebar';
import Table from '../../global/components/Table';
import {
  filterJobsBasedOnDateTile,
  filterUnscheduledJobsBasedOnDate,
} from '../utils/JobPlanner.functions';

const DateTile = ({
  date,
  isTodayDate,
  jobs,
  unscheduledJobs,
  handleDateTileClick,
  requests,
}) => {
  const navigate = useNavigate();
  const [todayJobs, setTodayJobs] = useState([]);
  const [todayUnscheduledJobs, setTodayUnscheduledJobs] = useState([]);
  const [Requests, setRequests] = useState([]);
  const [ShowSidebar, setShowSidebar] = useState(false);

  useEffect(() => {
    if ((jobs, unscheduledJobs)) {
      let res = filterJobsBasedOnDateTile(
        date?.getDate(),
        date?.getMonth(),
        jobs
      );
      let res2 = filterUnscheduledJobsBasedOnDate(
        date?.getDate(),
        date?.getMonth(),
        unscheduledJobs,
        jobs
      );
      setTodayJobs([...new Set(res)]);
      setTodayUnscheduledJobs([...new Set(res2)]);
    }
  }, [jobs, unscheduledJobs]); //eslint-disable-line

  useEffect(() => {
    const tmp = requests?.filter((request) => {
      const maintenanceDate = new Date(
        request?.maintenanceDate
      ).toLocaleDateString('en-In');
      const tiledate = new Date(date).toLocaleDateString('en-In');
      return maintenanceDate === tiledate;
    });
    setRequests(tmp);
  }, [requests]); // eslint-disable-line

  return (
    <>
      <RightSidebar
        openSideBar={ShowSidebar}
        setOpenSideBar={setShowSidebar}
        scale={736}
      >
        <section>
          <div className="heading">
            <h3>Maintenance Requests</h3>
          </div>
          <div className="details">
            <Table>
              <Table.Head>
                <Table.Th>Equipment</Table.Th>
                <Table.Th>Category</Table.Th>
                <Table.Th>Maintenance Date</Table.Th>
              </Table.Head>
              <Table.Body>
                {Requests?.map((request) => {
                  return (
                    <Table.Row
                      key={request?._id}
                      isClickable={true}
                      onClick={() => {
                        navigate('/maintenance/dashboard');
                      }}
                    >
                      <Table.Td>{request?.equipment?.name}</Table.Td>
                      <Table.Td>{request?.maintenanceCategory}</Table.Td>
                      <Table.Td>
                        {FormatDate(request?.maintenanceDate)}
                      </Table.Td>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
          </div>
        </section>
      </RightSidebar>
      <div
        className={`h-40 text-center hover:cursor-pointer font-medium hover:bg-slate-50 ${
          isTodayDate ? 'border-2 border-black' : 'border-[1px] border-gray-100'
        }`}
        onClick={() => {
          handleDateTileClick(todayJobs, todayUnscheduledJobs, date);
        }}
      >
        <div className={`w-full text-left flex items-center`}>
          <div className="w-full">
            <span className="py-1 px-2 w-full">{date.getDate()}</span>
          </div>
          {todayUnscheduledJobs?.length > 0 && (
            <span className="w-[11px] mx-1 h-[10px] rounded-full bg-red-600"></span>
          )}
        </div>
        {Requests?.length !== 0 && (
          <div className="maintenancerequest border mt-10 !text-left p-2 !text-[14px] relative">
            {Requests?.length === 1 && (
              <div>
                <p>
                  {Requests[0]?.equipment?.name ||
                    Requests[0]?.asset?.name ||
                    Requests[0]?.machine?.machineName}
                </p>
                <p className="text-[12px]">
                  {Requests[0]?.maintenanceCategory}
                </p>
              </div>
            )}
            {Requests?.length > 1 && (
              <div>
                <p>
                  {Requests[0]?.equipment?.name ||
                    Requests[0]?.asset?.name ||
                    Requests[0]?.machine?.machineName}
                </p>
                <p className="text-[12px]">
                  {Requests[0]?.maintenanceCategory}
                </p>
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowSidebar(true);
                  }}
                  className="absolute -top-[10px] right-0 w-[1.5rem] h-[1.5rem] bg-blue-500 text-white rounded-full text-[12px] flex items-center justify-center"
                >
                  +{Requests?.length - 1}
                </Button>
              </div>
            )}
          </div>
        )}
        <div className="px-1 py-1 h-full">
          {todayJobs?.map((elem, idx) => {
            if (idx <= 3) {
              return (
                <div
                  key={elem?.createInput?.id}
                  className="truncate mb-1 bg-cyan-100 text-sm p-1 border-l-8 border-l-cyan-200"
                >
                  {elem?.createInput?.id}
                </div>
              );
            }
          })}
          {todayJobs?.length > 4 && (
            <div className="bg-slate-200 text-slate-600">
              {' '}
              +{todayJobs.length - 4}{' '}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default DateTile;
