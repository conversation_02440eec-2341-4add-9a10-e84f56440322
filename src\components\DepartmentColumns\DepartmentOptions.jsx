import { Checkbox, Divider, Modal } from 'antd';
import { useContext, useEffect, useState } from 'react';
import { Store } from '../../store/Store';

const DepartmentOptions = ({
  setTableConfig,
  openModal,
  setOpenModal,
  columnIndex,
  departmentCheckedCols,
  setDepartmentCheckedCols,
}) => {
  const { defaults } = useContext(Store);
  const allNodes = defaults?.defaultParam?.departmentFlow?.nodeStructure?.nodes;

  const kanbanColumns = allNodes?.map((node) => {
    const page = node?.data?.selectedPage?.[0];
    const department = node?.data?.selectedDepartment?.name;
    return {
      label: page?.label,
      value: page?.label,
      cslug: page?.value,
      department,
    };
  });

  const [allSelected, setAllSelected] = useState(false);

  // Sync "Select All" checkbox with individual selections
  useEffect(() => {
    setAllSelected(
      kanbanColumns?.length > 0 &&
        departmentCheckedCols.length === kanbanColumns.length
    );
  }, [departmentCheckedCols, kanbanColumns]);

  const handleCheckboxChange = (column) => {
    setDepartmentCheckedCols((prev) =>
      prev.some((item) => item.value === column.value)
        ? prev.filter((item) => item.value !== column.value)
        : [...prev, column]
    );
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setDepartmentCheckedCols(kanbanColumns);
    } else {
      setDepartmentCheckedCols([]);
    }
  };

  const handleOk = () => {
    setTableConfig((prev) => ({
      ...prev,
      columns: prev?.columns?.map((elem, i) => {
        if (i === columnIndex) {
          return {
            ...elem,
            options: departmentCheckedCols,
          };
        }
        return elem;
      }),
    }));
    setOpenModal(false);
  };

  const handleCancel = () => {
    setDepartmentCheckedCols([]);
    setOpenModal(false);
  };

  return (
    <Modal
      title="Kanban Columns"
      open={openModal}
      onCancel={handleCancel}
      onOk={handleOk}
      footer={[
        <Checkbox
          key="select-all"
          checked={allSelected}
          onChange={(e) => handleSelectAll(e.target.checked)}
        >
          Select All
        </Checkbox>,
        <Divider key="divider" type="vertical" />,
        <button key="ok" className="ant-btn ant-btn-primary" onClick={handleOk}>
          Save
        </button>,
      ]}
    >
      <p className="text-gray-600">
        This modal shows columns available in the Kanban flow.
      </p>
      <Divider />
      {kanbanColumns?.length > 0 ? (
        kanbanColumns.map((column, index) => (
          <div key={index} className="flex items-center gap-2">
            <Checkbox
              checked={departmentCheckedCols.some(
                (item) => item.value === column.value
              )}
              onChange={() => handleCheckboxChange(column)}
            >
              {column.label}
            </Checkbox>
          </div>
        ))
      ) : (
        <p>No department options available.</p>
      )}
    </Modal>
  );
};

export default DepartmentOptions;
