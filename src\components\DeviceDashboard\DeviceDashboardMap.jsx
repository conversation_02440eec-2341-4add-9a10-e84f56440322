import {
  AdvancedMarker,
  APIProvider,
  InfoWindow,
  Map,
  Pin,
  useAdvancedMarkerRef,
} from '@vis.gl/react-google-maps';
import { useContext, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { getIotDeviceData } from '../../helperFunction';
import { useGetAllDeviceDashboardMachinesQuery } from '../../slices/deviceDashboardMachineApiSlice';
import { Store } from '../../store/Store';

function CustomMarker({ location, header, children, pin }) {
  const [markerRef, anchor] = useAdvancedMarkerRef();
  const [showInfo, setShowInfo] = useState(false);

  return (
    <>
      <AdvancedMarker
        position={location}
        ref={markerRef}
        onClick={() => setShowInfo((prev) => !prev)}
        title={header}
      >
        <Pin
          background={pin?.background || '#EA4335'}
          glyphColor={pin?.glyphColor || '#B42C1A'}
          borderColor={pin?.glyphColor || '#B42C1A'}
        />
      </AdvancedMarker>
      {showInfo && (
        <InfoWindow
          minWidth={150}
          anchor={anchor}
          headerContent={header}
          onClose={() => setShowInfo(false)}
        >
          {children}
        </InfoWindow>
      )}
    </>
  );
}

function DeviceDashboardMap({ mapMode, allCustomers, referenceDate }) {
  const { defaults: { googleMapApiKey = '' } = {} } = useContext(Store);

  const { iotDeviceData = {} } = useOutletContext();

  const { data: allMachines } = useGetAllDeviceDashboardMachinesQuery(
    {},
    { skip: mapMode !== 'machine', refetchOnMountOrArgChange: true }
  );

  return (
    <div className="w-full h-[72vh]">
      <APIProvider apiKey={googleMapApiKey}>
        <Map
          mapId={'deviceDashboard'}
          style={{ width: '100%', height: '100%' }}
          defaultCenter={{ lat: 22.977378795847805, lng: 79.26377078903572 }}
          defaultZoom={4}
          gestureHandling={'greedy'}
        >
          {mapMode === 'customer' ? (
            <>
              {allCustomers?.map((cust) => {
                if (!cust?.location?.lat || !cust?.location?.lng) return null;

                return (
                  <CustomMarker
                    key={cust?._id}
                    location={cust?.location}
                    header={cust?.name}
                  >
                    <p className="flex justify-between">
                      <span>No. of machines:</span>&nbsp;
                      {cust?.deviceDashboardMachineIds?.length}
                    </p>
                  </CustomMarker>
                );
              })}
            </>
          ) : (
            <>
              {allMachines?.map((mac) => {
                const customer = allCustomers?.find(
                  (cust) => cust?._id === mac?.deviceDashboardCustomerId
                );

                const { isActive } = getIotDeviceData(
                  iotDeviceData,
                  mac?.devices?.[0],
                  new Date(),
                  referenceDate
                );

                return (
                  <CustomMarker
                    key={mac?._id}
                    header={mac?.name}
                    location={mac?.devices?.[0]?.location}
                    pin={{
                      background: isActive ? '#4AB25F' : '',
                      glyphColor: isActive ? '#337F3B' : '',
                    }}
                  >
                    <p className="flex justify-between">
                      <span>Customer name:</span>&nbsp;{customer?.name}
                    </p>
                  </CustomMarker>
                );
              })}
            </>
          )}
        </Map>
      </APIProvider>
    </div>
  );
}

export default DeviceDashboardMap;
