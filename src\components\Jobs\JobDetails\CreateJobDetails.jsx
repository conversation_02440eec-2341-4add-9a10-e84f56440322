import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Store } from '../../../store/Store';
import { InfoTooltip } from '../../global/components/InfoTooltip';
import Input from '../../global/components/Input';
import Select from '../../global/components/Select';
import Spinner from '../../global/components/Spinner';
// import { subtractReserved } from '../OrderDetails/Details';
import usePrefixIds from '../../../hooks/usePrefixIds';
import GetStockStatus from './GetStockStatus';

const saveSelectDataToLocalStorage = (key, value) => {
  const selectData = JSON.parse(localStorage.getItem('createJobSelects'));
  let obj = {
    selectInputs: '',
    selectProductionFlows: '',
    selectedPo: '',
  };
  if (selectData) {
    obj = { ...(selectData || {}), [key]: value };
  }
  localStorage.setItem('createJobSelects', JSON.stringify(obj));
};

const CreateJobDetails = ({
  isTemplateStart = false,
  pos,
  selectedPoData,
  isLoadingPos,
  selectedPo,
  setselectedPo,
  inputScreens,
  selectInputs,
  setSelectInputs,
  productionFlows,
  selectProductionFlows,
  setSelectProductionFlows,
  productionFlow,
  workOrderId,
  setWorkOrderId,
  setFormData,
  processGoals,
  goalsData,
  setGoalsData,
  setGoalsTables,
  setMultiProcessTablesData,
  setAssemblyItem,
  assemblyItem,
  bomAndAssemblyItem,
  setBomAndAssemblyItem,
  setAssemblyItemType,
  createTemplate,
  templateName,
  setTemplateName,
  machines,
  setAssemblyItemToUpdate,
  setModelIdData,
  setJobIdData,
  setAdditionalIdData,
  inputScreen,
}) => {
  const navigate = useNavigate();
  const { defaults } = useContext(Store);
  const [inhouseItems, setInhouseItems] = useState([]);
  const [isManual, setIsManual] = useState(false);

  useEffect(() => {
    if (selectedPoData && setWorkOrderId) {
      setWorkOrderId(selectedPoData?.workOrderId);
    }
  }, [setWorkOrderId, selectedPoData]);

  const jobPrefix = usePrefixIds({
    idFor: 'jobId',
    setIdData: setJobIdData,
    additionalIdData: { WorkOrderId: workOrderId },
  });
  const modelPrefix = usePrefixIds({
    idFor: 'modelName',
    setIdData: setModelIdData,
    additionalIdData: { WorkOrderId: workOrderId },
  });

  useEffect(() => {
    if (
      selectedPoData?.workOrderId &&
      jobPrefix?.currentPrefixId &&
      modelPrefix?.currentPrefixId &&
      inputScreen?.name &&
      setAdditionalIdData
    ) {
      setAdditionalIdData({
        InputScreen: inputScreen?.name,
        WorkOrderId: selectedPoData?.workOrderId,
        JobId: jobPrefix?.currentPrefixId,
        ModelName: modelPrefix?.currentPrefixId,
      });
    }
  }, [
    setAdditionalIdData,
    selectedPoData?.workOrderId,
    jobPrefix?.currentPrefixId,
    modelPrefix?.currentPrefixId,
    inputScreen?.name,
  ]);

  const onInputScreenChange = (inputScreenId) => {
    if (inputScreenId) {
      Object?.keys(localStorage)?.forEach((key) => {
        if (key?.startsWith('autoSave-')) {
          localStorage?.removeItem(key);
        }
      });
      setMultiProcessTablesData({
        multiProcessData: {},
        inputScreenTableData: {},
      });

      const inputScreen = inputScreens?.find((is) => is.id === inputScreenId);
      setFormData(
        inputScreen?.data?.map((tab) => {
          return {
            ...tab,
            data: tab?.data?.map((sheet) => {
              let inputData = {};

              // set initial data for each sheet
              sheet.sheetData.data.forEach((item) => {
                if (
                  (+item['Horizontal Repetition'] > 1 ||
                    +item['Vertical Repetition'] > 1) &&
                  item.Type !== 'CHECKBOX'
                ) {
                  const horizontalFields = [
                    ...Array(+item['Horizontal Repetition']),
                  ];
                  const verticalFields = [
                    ...Array(+item['Vertical Repetition']),
                  ];
                  const title = item.Title.split('; ');
                  let obj = {};
                  horizontalFields.forEach((h, hIdx) => {
                    if (verticalFields.length > 1) {
                      verticalFields.forEach((v, vIdx) => {
                        obj[title[hIdx] + vIdx.toString()] = '';
                      });
                    } else {
                      obj[title[hIdx]] = '';
                    }
                  });
                  inputData = {
                    ...inputData,
                    [item.Parameter]: {
                      ...(inputData?.[item.Parameter] || {}),
                      value: obj,
                      selectOptions: [],
                      visibility: {},
                      rows: null,
                    },
                  };
                } else if (item.Type === 'CHECKBOX') {
                  inputData = {
                    ...inputData,
                    [item.Parameter]: {
                      ...(inputData?.[item.Parameter] || {}),
                      value: [],
                      selectOptions: [],
                    },
                  };
                } else {
                  inputData = {
                    ...inputData,
                    [item.Parameter]: {
                      ...(inputData?.[item.Parameter] || {}),
                      value: '',
                      selectOptions: [],
                    },
                  };
                }
              });

              return { ...sheet, inputData };
            }),
          };
        })
      );
    }
    if (selectProductionFlows) {
      const productionFlow = productionFlows?.find(
        (pf) => pf._id === selectProductionFlows
      );

      let tempGoalsData = {};
      productionFlow?.processes?.forEach((pro) => {
        const goal = processGoals?.find(
          (goal) => goal?.mqtt?._id === pro?.mqtt?._id
        );

        goal?.parameters?.forEach((param) => {
          tempGoalsData = {
            ...tempGoalsData,
            [pro._id]: {
              ...(tempGoalsData[pro._id] || {}),
              [param.name]: param?.name === 'Number of Ups' ? 1 : 0,
            },
          };
        });
      });
      setGoalsData(tempGoalsData);
    }
  };

  const onProductionFlowChange = (productionFLowId) => {
    if (productionFLowId) {
      const productionFlow = productionFlows?.find(
        (pf) => pf._id === productionFLowId
      );

      let tempGoalsData = {};
      let tempGoalsTables = [];
      productionFlow?.processes?.forEach((pro) => {
        const goal = processGoals?.find(
          (goal) => goal?.mqtt?._id === pro?.mqtt?._id
        );

        const obj = {
          flowId: pro._id,
          applicableDowntime: pro?.applicableDowntime,
          mqtt: pro?.mqtt?._id,
        };

        if (pro.processCategory === 'Inhouse') {
          const filteredMachines =
            machines?.filter((mac) => mac.mqtt === pro?.mqtt?._id) || [];

          obj.compatibleMachines = filteredMachines?.map((mac) => ({
            label: mac.machineName,
            value: mac._id,
            itemsPerHour: 0,
            changeOverTime: 0,
            capacity: mac?.maxCapacity,
            cost: mac?.costPerHour,
          }));
        }

        tempGoalsTables.push(obj);

        goal?.parameters?.forEach((param) => {
          tempGoalsData = {
            ...tempGoalsData,
            [pro._id]: {
              ...(tempGoalsData[pro._id] || {}),
              [param.name]: param?.name === 'Number of Ups' ? 1 : 0,
            },
          };
        });
      });

      setGoalsTables(tempGoalsTables);
      setGoalsData(tempGoalsData);
    }
  };

  useEffect(() => {
    if (selectedPoData && selectedPo) {
      setInhouseItems(getInhouseItemsInBoms() || []);
    }
  }, [selectedPoData, selectedPo]); //eslint-disable-line

  const getInhouseItmFromChildrenRecursion = (children) => {
    let inhouseItems = [];
    children?.forEach((child) => {
      if (
        child?.category === 'inhouse' ||
        child?.category === 'finishedGoods'
        // child?.category === 'subAssembly'
      ) {
        inhouseItems.push(child);
      }

      if (child?.category === 'subAssembly') {
        if (
          child?.product?.category === 'Inhouse Finished Goods' ||
          child?.part?.category === 'Inhouse Finished Goods' ||
          child?.productVariant?.product?.category ===
            'Inhouse Finished Goods' ||
          child?.partVariant?.part?.category === 'Inhouse Finished Goods'
        ) {
          inhouseItems.push(child);
        }
      }

      if (child?.children?.length) {
        inhouseItems = [
          ...inhouseItems,
          ...getInhouseItmFromChildrenRecursion(child?.children),
        ];
      }
    });
    return inhouseItems;
  };

  const getInhouseItemsInBoms = () => {
    let po = selectedPoData;
    let inhouseItems = [];
    if (selectedPoData?.items !== undefined) {
      inhouseItems = po?.items?.filter((i) => i?.category === 'inhouse');
      inhouseItems = inhouseItems?.map((elem) => ({
        part: elem?.itemId,
        units: elem?.units,
        _id: elem?._id,
        category: 'Inhouse',
      }));
    } else {
      let boms = selectedPoData?.bom;
      if (po?.type === 'Assembly') {
        if (boms) {
          for (let i of boms) {
            inhouseItems = [
              ...inhouseItems,
              ...getInhouseItmFromChildrenRecursion(i?.children),
            ];
          }
        }
      } else if (po?.type === 'Inhouse') {
        for (let i of po?.inhouse) {
          if (i?.category === 'Inhouse' || i?.categoryId === 'manualEntry') {
            inhouseItems.push(i);
          }
        }
      }
    }

    return inhouseItems;
  };

  let workOrder = [];
  if (pos.length > 0) {
    workOrder = [{ value: '+', name: '+ Add Work Order' }, ...pos];
  }

  return (
    <>
      {isTemplateStart ? (
        <div className="">
          <div className="flex flex-col w-[88%] ">
            <div className="flex items-center gap-[5px]">
              <label className="block text-md mt-2 mb-1 text-black font-bold">
                Select Work Order
              </label>
              <InfoTooltip
                className="mt-2 mb-1"
                id="selectPoCreateJobs"
                position="right"
              >
                Use this feature to assign a specific job to an existing work
                order via the dropdown menu, ensuring organized job management
                and alignment with work order priorities.
              </InfoTooltip>
            </div>
            <Select
              value={selectedPo}
              isLoading={isLoadingPos}
              loadingMessage={() => <Spinner size={6} />}
              onChange={(e) => {
                if (e.target.value === '+') {
                  navigate('/jobs/workorder?redirect=jobs/createjobs');
                }
                setselectedPo(e.target.value);
                saveSelectDataToLocalStorage('selectedPo', e.target.value);
              }}
              options={
                workOrder
                  ? workOrder?.map((flow) => ({
                      label: flow?.workOrderId
                        ? `${flow?.name} (${flow?.workOrderId})`
                        : flow?.name,
                      value: flow?._id || flow?.value,
                    }))
                  : []
              }
            />
            {!isManual && assemblyItem && (
              <GetStockStatus
                rawMaterials={
                  selectedPoData?.items?.find((i) => i?._id === assemblyItem)
                    ?.rawMaterials || []
                }
                className="flex justify-start items-center mt-8 w-full"
                goalsData={goalsData}
              />
            )}
          </div>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-3 w-full col-span-full gap-5">
            {createTemplate && (
              <div className="flex flex-col w-full">
                <label className="block text-md mt-2 mb-1 text-black font-bold">
                  Job template name
                </label>
                <Input
                  type="text"
                  inputClassname="border text-left text-black outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full"
                  placeholder=""
                  value={templateName}
                  onChange={(e) => {
                    setTemplateName(e.target.value);
                    setAssemblyItemToUpdate(
                      inhouseItems.find((i) => i?._id === assemblyItem)?._id ||
                        ''
                    );
                  }}
                />
              </div>
            )}

            {!bomAndAssemblyItem?.isForWorkOrder && (
              <div className="flex flex-col w-full">
                <div className="flex items-center gap-[5px]">
                  <label className="block text-md mt-2 mb-1 text-black font-bold">
                    Select Work Order
                  </label>
                  <InfoTooltip
                    className="mt-2 mb-1"
                    id="selectPoCreateJobs"
                    position="right"
                  >
                    Use this feature to assign a specific job to an existing
                    work order via the dropdown menu, ensuring organized job
                    management and alignment with work order priorities.
                  </InfoTooltip>
                </div>
                <Select
                  value={selectedPo}
                  isLoading={isLoadingPos}
                  loadingMessage={() => <Spinner size={6} />}
                  onChange={(e) => {
                    if (e.target.value === '+') {
                      navigate('/jobs/workorder?redirect=jobs/createjobs');
                    }
                    setselectedPo(e.target.value);
                    saveSelectDataToLocalStorage('selectedPo', e.target.value);
                  }}
                  options={
                    workOrder?.map((flow) => ({
                      label: flow?.workOrderId
                        ? `${flow?.name} (${flow?.workOrderId})`
                        : flow?.name,
                      value: flow?._id || flow?.value,
                    })) || []
                  }
                />
              </div>
            )}

            <div className="flex flex-col w-full">
              <div className="flex items-center gap-[5px]">
                <label
                  htmlFor="selectinput"
                  className="block text-md mt-2 mb-1 text-black font-bold"
                >
                  {defaults?.defaultParam?.projectDefaults?.inputScreenLabel}
                </label>
                <InfoTooltip
                  className="mt-2 mb-1"
                  id="selectIsCreateJobs"
                  position="right"
                >
                  Select job template
                </InfoTooltip>
              </div>
              {inputScreens && (
                <Select
                  placeholder="Please select"
                  value={selectInputs}
                  onChange={(e) => {
                    if (e.target.value === '+') {
                      navigate(
                        '/settings/create/jobtemplate?redirect=jobs/createjobs'
                      );
                    }
                    setSelectInputs(e.target.value);
                    onInputScreenChange(e.target.value);
                    saveSelectDataToLocalStorage(
                      'selectInputs',
                      e.target.value
                    );
                  }}
                  options={[
                    { value: '+', name: `+ Add Input Screen` },
                    ...(inputScreens.map((screen) => ({
                      name: `${screen?.name || ''} 
                        (${productionFlow?.name || 'Select PF'})`,
                      value: screen?.id || '',
                    })) || []),
                  ]}
                />
              )}
            </div>
            <div className="flex flex-col w-full">
              <label className="block text-md mt-2 mb-1 mr-2 text-black font-bold ">
                Production Flow
              </label>
              <Select
                placeholder="Production Flow"
                value={selectProductionFlows}
                onChange={(e) => {
                  if (e.target.value === '+') {
                    navigate(
                      '/settings/create/productionflow?modalOpen=true&redirect=jobs/createjobs'
                    );
                  } else {
                    setSelectProductionFlows(e.target.value);
                    saveSelectDataToLocalStorage(
                      'selectProductionFlows',
                      e.target.value
                    );
                    setTimeout(() => {
                      onProductionFlowChange(e.target.value);
                    }, 1000);
                  }
                }}
                options={[
                  { name: '+ Add Production Flow', value: '+' },
                  ...(productionFlows?.map((pf) => ({
                    name: pf.name,
                    value: pf._id,
                  })) || []),
                ]}
              />
            </div>
          </div>
          {selectedPo && (
            <div className="grid grid-cols-3 w-full col-span-full gap-5 items-center justify-center">
              {!createTemplate && (
                <>
                  <div className="min-w-[33%] h-full">
                    <label className="text-md text-black font-bold">
                      {defaults?.defaultParam?.projectDefaults?.modelLabel}
                    </label>
                    <div className="mt-3">
                      <modelPrefix.IdGenComp {...modelPrefix?.idCompData} />
                    </div>
                  </div>
                  <div className="min-w-[33%] h-full">
                    <label className="text-md text-black font-bold">
                      {
                        defaults?.defaultParam?.projectDefaults
                          ?.projectIdentifier
                      }
                    </label>
                    <div className="mt-3">
                      <jobPrefix.IdGenComp {...jobPrefix?.idCompData} />
                    </div>
                  </div>
                </>
              )}

              <div className="flex w-full justify-between items-center">
                {!bomAndAssemblyItem?.isForWorkOrder && (
                  <div className="w-full h-full">
                    <div className="flex items-center gap-[5px]">
                      <label className="block text-md mt-2 mb-1 text-black font-bold">
                        Select Item
                      </label>
                      <InfoTooltip
                        className="mt-2 mb-1"
                        id="selectPoCreateJobs"
                        position="right"
                      >
                        Use this feature to assign a specific job to an existing
                        work order via the dropdown menu, ensuring organized job
                        management and alignment with work order priorities.
                      </InfoTooltip>
                    </div>
                    {!isManual ? (
                      <Select
                        isLoading={isLoadingPos}
                        value={assemblyItem}
                        onChange={(e) => {
                          let selectItem = selectedPoData?.items?.find(
                            (i) => i?._id === e.target.value
                          );

                          setAssemblyItemToUpdate(selectItem?._id);

                          setBomAndAssemblyItem((prev) => ({
                            ...prev,
                            assemblyItem: selectItem,
                            correspondingBom: selectItem?.bom?.bomId,
                            correspondingPo: selectedPo,
                          }));
                          if (e.target.value === 'manualEntry') {
                            setIsManual(true);
                          } else {
                            setIsManual(false);
                          }
                          setAssemblyItem(e.target.value);
                          setAssemblyItemType(selectItem?.itemType);
                        }}
                        options={[
                          {
                            name: '+ Manual Entry',
                            value: 'manualEntry',
                          },
                          ...(getInhouseItemsInBoms() || [])?.map((item) => ({
                            ...item,
                            name:
                              item?.manualEntry?.name ||
                              item?.product?.name ||
                              (item?.part?.name
                                ? `${item?.part?.name}(${item?.units}) `
                                : false),
                            value: item?._id,

                            // value: item?.value,

                            // TODO need to add in new workorder for already created job
                            className: `${
                              selectedPoData?.bomItemUsed?.includes(item._id)
                                ? '!text-red-500'
                                : ''
                            }`,

                            // DO NOT DELETE
                            // disabled: selectedPoData?.bomItemUsed?.includes(
                            //   item._id
                            // ),
                          })),
                        ]}
                      />
                    ) : (
                      <>
                        <Input
                          inputClassname={''}
                          value={bomAndAssemblyItem?.manualEntry || ''}
                          className={'min-w-[120px]'}
                          onChange={(e) => {
                            setBomAndAssemblyItem((prev) => ({
                              ...prev,
                              manualEntry: e.target.value,
                            }));
                            setAssemblyItemType('Manual Entry');
                          }}
                        />
                      </>
                    )}
                  </div>
                )}

                {!isManual && assemblyItem && (
                  <GetStockStatus
                    rawMaterials={
                      selectedPoData?.items?.find(
                        (i) => i?._id === assemblyItem
                      )?.rawMaterials || []
                    }
                    className="flex justify-start items-center mt-8 w-full ml-3"
                    goalsData={goalsData}
                  />
                )}
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default CreateJobDetails;
