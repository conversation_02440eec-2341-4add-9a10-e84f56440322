import { Card } from 'antd';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

const BentoGrid = ({ children, className = '' }) => {
  const [screenSize, setScreenSize] = useState('desktop');

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width <= 480) {
        setScreenSize('mobile');
      } else if (width <= 640) {
        setScreenSize('mobile-lg');
      } else if (width <= 768) {
        setScreenSize('tablet');
      } else if (width <= 1024) {
        setScreenSize('tablet-lg');
      } else if (width <= 1400) {
        setScreenSize('desktop-sm');
      } else {
        setScreenSize('desktop');
      }
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  const getGridClasses = () => {
    const baseClasses = 'grid w-full';

    switch (screenSize) {
      case 'mobile':
        return `${baseClasses} grid-cols-1 gap-3 auto-rows-[minmax(200px,auto)]`;
      case 'mobile-lg':
        return `${baseClasses} grid-cols-1 gap-3 auto-rows-[minmax(220px,auto)]`;
      case 'tablet':
        return `${baseClasses} grid-cols-2 gap-4 auto-rows-[minmax(240px,auto)]`;
      case 'tablet-lg':
        return `${baseClasses} grid-cols-2 gap-4 auto-rows-[minmax(260px,auto)]`;
      case 'desktop-sm':
        return `${baseClasses} grid-cols-3 gap-5 auto-rows-[minmax(280px,auto)]`;
      default:
        return `${baseClasses} grid-cols-4 gap-6 auto-rows-[minmax(300px,auto)]`;
    }
  };

  return (
    <div
      className={`${getGridClasses()} ${className}`}
      data-screen-size={screenSize}
    >
      {children}
    </div>
  );
};

const BentoGridItem = ({
  children,
  className = '',
  span = 1,
  rowSpan = 1,
  index = 0,
  title,
  subtitle,
}) => {
  const [screenSize, setScreenSize] = useState('desktop');

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width <= 480) {
        setScreenSize('mobile');
      } else if (width <= 640) {
        setScreenSize('mobile-lg');
      } else if (width <= 768) {
        setScreenSize('tablet');
      } else if (width <= 1024) {
        setScreenSize('tablet-lg');
      } else if (width <= 1400) {
        setScreenSize('desktop-sm');
      } else {
        setScreenSize('desktop');
      }
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        delay: index * 0.1,
      },
    },
  };

  const getResponsiveSpan = () => {
    switch (screenSize) {
      case 'mobile':
      case 'mobile-lg':
        return 'col-span-1'; // Always single column on mobile
      case 'tablet':
        return span >= 3
          ? 'col-span-2'
          : span >= 2
            ? 'col-span-2'
            : 'col-span-1';
      case 'tablet-lg':
        return span >= 3
          ? 'col-span-2'
          : span >= 2
            ? 'col-span-2'
            : 'col-span-1';
      case 'desktop-sm':
        return span >= 4
          ? 'col-span-3'
          : span >= 3
            ? 'col-span-3'
            : span >= 2
              ? 'col-span-2'
              : 'col-span-1';
      default:
        return `col-span-${span}`;
    }
  };

  const getResponsiveRowSpan = () => {
    if (screenSize === 'mobile' || screenSize === 'mobile-lg') {
      return 'row-span-1'; // Always single row on mobile
    }
    return rowSpan > 1 ? `row-span-${rowSpan}` : 'row-span-1';
  };

  const getResponsivePadding = () => {
    switch (screenSize) {
      case 'mobile':
        return '12px';
      case 'mobile-lg':
      case 'tablet':
        return '16px';
      default:
        return '20px';
    }
  };

  return (
    <motion.div
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      whileHover={{
        scale:
          screenSize === 'mobile' || screenSize === 'mobile-lg' ? 1.01 : 1.02,
        transition: { duration: 0.2 },
      }}
      className={`${getResponsiveSpan()} ${getResponsiveRowSpan()} ${className}`}
    >
      <Card
        className="h-full border-0 shadow-md hover:shadow-lg transition-all duration-300"
        styles={{
          body: {
            padding: getResponsivePadding(),
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
          },
        }}
      >
        {(title || subtitle) && (
          <div className="mb-4">
            {title && (
              <h3
                className={`font-semibold text-gray-900 mb-1 ${
                  screenSize === 'mobile' ? 'text-base' : 'text-lg'
                }`}
              >
                {title}
              </h3>
            )}
            {subtitle && (
              <p
                className={`text-gray-600 ${
                  screenSize === 'mobile' ? 'text-xs' : 'text-sm'
                }`}
              >
                {subtitle}
              </p>
            )}
          </div>
        )}
        <div className="flex-1 flex flex-col">{children}</div>
      </Card>
    </motion.div>
  );
};

BentoGrid.Item = BentoGridItem;

export default BentoGrid;
