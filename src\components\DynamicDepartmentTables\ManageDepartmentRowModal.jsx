import { useEffect, useState } from 'react';
import { TiDelete } from 'react-icons/ti';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useGetBomsMutation } from '../../slices/assemblyBomApiSlice';
import { useLazyGetAssetsQuery } from '../../slices/assetApiSlice';
import { useGetDepartmentsQuery } from '../../slices/departmentApiSlice';
import {
  useAddDepartmentRowMutation,
  useEditDepartmentRowMutation,
} from '../../slices/departmentRowApiSlice';
import Button from '../global/components/Button';
import DragAndDrop from '../global/components/DragAndDrop';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';

// import { useNavigate } from 'react-router-dom'
import Table from '../global/components/Table';

const initialInputData = { data: {}, attachments: [] };

const renderFields = (
  col,
  inputData,
  setInputData,
  boms,
  allAssets,
  navigate,
  setStep,
  selectedBomCom,
  setSelectedBomCom,
  setFormFilling,
  setSelectedForm
) => {
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setInputData((prev) => ({
      ...prev,
      data: {
        ...(prev?.data || {}),
        [name]:
          type === 'checkbox' ? checked : type === 'number' ? +value : value,
      },
    }));
  };

  const fileExists = (name) => {
    for (let i of inputData?.attachments) {
      if (i?.name === name) return true;
    }
    return false;
  };

  const handleMediaAdd = (e) => {
    for (let i in e) {
      const fr = new FileReader();
      if (i === 'length') return;
      const name = e[i].name;
      if (!fileExists(name)) {
        fr.readAsDataURL(e[i]);
        const type = e[i].type;
        fr.addEventListener('load', () => {
          const url = fr.result;
          setInputData((prev) => ({
            ...prev,
            attachments: [
              ...(prev?.attachments || []),
              { name, type, data: url },
            ],
          }));
        });
      } else {
        toast.error('File of the same name has already been uploaded!', {
          toastId: 'fileDuplication',
        });
      }
    }
  };

  switch (col?.type) {
    case 'string':
      return (
        <div className="flex flex-col w-full " key={col?._id}>
          <label className="mb-1 font-semibold text-[#667085]">
            {col?.name}
          </label>
          <Input
            name={col?.name}
            onChange={handleInputChange}
            value={
              localStorage.getItem(col?.name)
                ? JSON.parse(localStorage.getItem(col?.name))
                : inputData?.data?.[col?.name] || ''
            }
          />
        </div>
      );
    case 'number':
      return (
        <div className="flex flex-col w-full " key={col?._id}>
          <label className="mb-1 font-semibold text-[#667085]">
            {col?.name}
          </label>
          <Input
            type="number"
            name={col?.name}
            onChange={handleInputChange}
            value={
              localStorage.getItem(col?.name)
                ? JSON.parse(localStorage.getItem(col?.name))
                : inputData?.data?.[col?.name] || ''
            }
          />
        </div>
      );
    case 'checkbox':
      return (
        <div className="flex flex-col w-full " key={col?._id}>
          <label className="mb-1 font-semibold text-[#667085]">
            {col?.name}
          </label>
          <input
            type="checkbox"
            name={col?.name}
            onChange={handleInputChange}
            checked={
              localStorage.getItem(col?.name)
                ? JSON.parse(localStorage.getItem(col?.name))
                : inputData?.data?.[col?.name] || false
            }
          />
        </div>
      );
    case 'media':
      return (
        <div className="col-span-full flex flex-col" key={col?._id}>
          <div className="flex justify-between items-center mb-1">
            <label className="font-semibold text-[#667085] w-fit">
              {col?.name}
            </label>
            <p className="px-4 py-1 bg-green-400 text-white min-w-[100px] rounded">
              +{inputData?.attachments?.length} Files
            </p>
          </div>

          <DragAndDrop
            accept="image/*, application/pdf"
            multiple
            onChange={handleMediaAdd}
          />
        </div>
      );
    case 'date':
      return (
        <div className="flex flex-col w-full " key={col?._id}>
          <label className="mb-1 font-semibold text-[#667085]">
            {col?.name}
          </label>
          <Input
            type="date"
            name={col?.name}
            onChange={handleInputChange}
            value={
              localStorage.getItem(col?.name)
                ? JSON.parse(localStorage.getItem(col?.name))
                : inputData?.data?.[col?.name] || ''
            }
          />
        </div>
      );
    case 'email':
      return (
        <div className="flex flex-col w-full " key={col?._id}>
          <label className="mb-1 font-semibold text-[#667085]">
            {col?.name}?
          </label>
          <Input
            type="email"
            name={col?.name}
            onChange={handleInputChange}
            value={
              localStorage.getItem(col?.name)
                ? JSON.parse(localStorage.getItem(col?.name))
                : inputData?.data?.[col?.name] || ''
            }
          />
        </div>
      );
    case 'bom':
      return (
        <div className="flex flex-col w-full " key={col?._id}>
          <label className="mb-1 font-semibold text-[#667085]">
            {col?.name}
          </label>
          <Select
            name={col?.name}
            value={
              localStorage.getItem(col?.name)
                ? JSON.parse(localStorage.getItem(col?.name))
                : inputData?.data?.[col?.name] || ''
            }
            onChange={(e) => {
              if (e.target.value === '+') {
                Object.keys(inputData?.data || {}).forEach((key) => {
                  localStorage.setItem(
                    key,
                    JSON.stringify(inputData?.data?.[key])
                  );
                });
                localStorage.setItem(
                  'inputData',
                  JSON.stringify(inputData?.data)
                );
                navigate(
                  `/jobs/workorder?redirect=${window.location.pathname.slice(
                    1
                  )}&modalOpen=true&selectedTab=BOM`
                );
              }
              handleInputChange(e);
            }}
            options={[
              { value: '+', name: `+ Add BOM` },
              ...(boms?.map((item) => ({
                name: item?.name,
                value: item?._id,
              })) || []),
            ]}
          />
        </div>
      );
    case 'bomComment':
      return (
        <div className="flex flex-col w-full " key={col?._id}>
          <label className="mb-1 font-semibold text-[#667085]">
            {col?.name}
          </label>
          <Select
            name={col?.name}
            value={selectedBomCom?._id}
            onChange={(e) => {
              if (e.target.value === '+') {
                Object.keys(inputData?.data || {}).forEach((key) => {
                  localStorage.setItem(
                    key,
                    JSON.stringify(inputData?.data?.[key])
                  );
                });
                localStorage.setItem(
                  'inputData',
                  JSON.stringify(inputData?.data)
                );
                navigate(
                  `/jobs/workorder?redirect=${window.location.pathname.slice(
                    1
                  )}&modalOpen=true&selectedTab=BOM`
                );
              }
              setSelectedBomCom(
                boms?.find((item) => item?._id === e.target.value)
              );
            }}
            options={[
              { value: '+', name: `+ Add BOM` },
              ...(boms?.map((item) => ({
                name: item?.name,
                value: item?._id,
              })) || []),
            ]}
          />
        </div>
      );
    case 'assets':
      return (
        <div className="flex flex-col w-full " key={col?._id}>
          <label className="mb-1 font-semibold text-[#667085]">
            {col?.name}
          </label>
          <MultiSelect
            isMulti
            name={col?.name}
            closeMenuOnSelect={false}
            value={
              localStorage.getItem(col?.name)
                ? JSON.parse(localStorage.getItem(col?.name))
                : inputData?.data?.[col?.name] || []
            }
            onChange={handleInputChange}
            options={allAssets?.map((option) => ({
              label: option?.name,
              value: option?._id,
            }))}
            isDisabled={allAssets && allAssets?.length < 1}
          />
        </div>
      );
    case 'forms':
      return (
        <div className="flex flex-col w-full " key={col?._id}>
          <label className="mb-1 font-semibold text-[#667085]">
            {col?.name}
          </label>
          <div className="flex gap-4">
            <div>{col?.form?.formName}</div>
            <Button
              onClick={() => {
                setFormFilling(true);
                setSelectedForm(col);
              }}
            >
              Fill Form
            </Button>
          </div>
        </div>
      );
    default:
      return <p key={col?._id}>Invalid field type</p>;
  }
};

const ManageDepartmentRowModal = ({
  setOpenModal,
  colData = {},
  editData,
  setEditData,
  currentDepartmentId,
}) => {
  const [getAssets] = useLazyGetAssetsQuery();
  const [getBoms] = useGetBomsMutation();

  const [boms, setBoms] = useState([]);
  const [allAssets, setAllAssets] = useState([]);
  // const navigate = useNavigate();
  const [selectedBomCom, setSelectedBomCom] = useState();
  const [formFilling, setFormFilling] = useState(false);
  const [selectedForm, setSelectedForm] = useState(false);
  const [filledFormData, setFilledFormData] = useState({});

  useEffect(() => {
    const getAllBoms = async () => {
      const res = await getBoms();
      setBoms(res?.data);
    };
    getAllBoms();
  }, [getBoms]);

  useEffect(() => {
    const getAllAssets = async () => {
      const res = await getAssets();
      setAllAssets(res?.data);
    };
    getAllAssets();
  }, [getAssets]);

  const [inputData, setInputData] = useState(initialInputData);
  const { data } = useGetDepartmentsQuery();
  let [searchParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    if (editData?._id) {
      setInputData(editData);
    }
  }, [editData]);

  useEffect(() => {
    if (localStorage.getItem('inputData')) {
      const dat = JSON.parse(localStorage.getItem('inputData'));
      setInputData((prev) => ({
        ...prev,
        data: { ...dat, bom: JSON.parse(localStorage.getItem('bom')) },
      }));
    }
  }, []);

  const {
    columns = [],
    _id: depColId,
    department,
    departmentNav,
    departmentChildNav,
  } = colData;

  const [addDepartmentRow, { isLoading: isloadingAdd }] =
    useAddDepartmentRowMutation();

  const [editDepartmentRow, { isLoading: isloadingEdit }] =
    useEditDepartmentRowMutation();

  const setDepartments = (e, mediaName) => {
    let temp = inputData?.attachments;
    for (let i in temp) {
      if (temp[i]?.name === mediaName) {
        temp[i] = {
          ...temp[i],
          departments: e.target.value,
        };
      }
    }
    setInputData((prev) => ({
      ...prev,
      attachments: temp,
    }));
  };

  const handleMediaRemove = (idx, id) => {
    if (inputData?._id) {
      setInputData((prev) => ({
        ...prev,
        attachments: prev?.attachments?.filter((_, i) => i !== idx),
        deletedAttachments: [...(prev?.deletedAttachments || []), id],
      }));
    } else {
      setInputData((prev) => ({
        ...prev,
        attachments: prev?.attachments?.filter((_, i) => i !== idx),
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const allVals = Object.values(inputData?.data || {});

    const isEmpty = allVals?.some((i) => i);

    if (allVals?.length === 0 || !isEmpty) {
      toast.error('Please fill form before submitting', {
        toastId: 'emptyform',
      });
      return;
    }

    if (editData?._id) {
      const data = {
        data: inputData?.data,
        deletedAttachments: inputData?.deletedAttachments || [],
        attachments: inputData?.attachments?.filter((i) => !i._id),
      };

      const res = await editDepartmentRow({ id: editData?._id, data }).unwrap();

      if (res) {
        toast.success('Edited row successfully', { toastId: 'edit' });
        setInputData(initialInputData);
        setOpenModal(false);
        setEditData({});
      }
    } else {
      const data = {
        ...inputData,
        department: department?._id,
        departmentNav: departmentNav?._id,
        departmentChildNav: departmentChildNav?._id,
        departmentColumn: depColId,
      };

      const res = await addDepartmentRow({ data }).unwrap();

      if (res) {
        toast.success('Add row successfully', { toastId: 'sucess' });
        setInputData(initialInputData);
        setOpenModal(false);
      }

      if (
        searchParams.get('kanban') === 'true' &&
        !searchParams.get('orderId')
      ) {
        navigate(
          `/primary/kanban?department=${searchParams.get('department')}&id=${
            res?._id
          }&refType=${searchParams.get('refType')}&page=${searchParams.get(
            'page'
          )}&index=${searchParams.get('index')}`
        );
      } else {
        navigate(
          `/primary/kanban?department=${searchParams.get('department')}&id=${
            res?._id
          }&refType=${searchParams.get('refType')}&page=${searchParams.get(
            'page'
          )}&orderId=${searchParams.get('orderId')}&index=${searchParams.get('index')}`
        );
      }
    }
    Object.keys(inputData?.data || {}).forEach((key) => {
      localStorage.removeItem(key, JSON.stringify(inputData?.data?.[key]));
    });
    localStorage.removeItem('inputData', JSON.stringify(inputData?.data));
  };

  return (
    <Modal
      title={`${editData?._id ? 'Edit' : 'Add'} Row`}
      onCloseModal={() => {
        setOpenModal(false);
        setEditData({});
      }}
      pages={inputData?.attachments?.length > 0 ? ['General', 'Files'] : []}
      onSubmit={handleSubmit}
      btnIsLoading={isloadingAdd || isloadingEdit}
    >
      {({ step, setStep }) => (
        <>
          {step === 0 && !formFilling && (
            <div className="grid grid-rows-1 gap-x-7 gap-y-3 grid-cols-1 md:grid-cols-2 mb-3">
              {columns?.map((col) => {
                return renderFields(
                  col,
                  inputData,
                  setInputData,
                  boms,
                  allAssets,
                  navigate,
                  setStep,
                  selectedBomCom,
                  setSelectedBomCom,
                  setFormFilling,
                  setSelectedForm,
                  data?.results || [],
                  currentDepartmentId
                );
              })}
            </div>
          )}
          {step === 0 && formFilling && (
            <div>
              <h1 className="text-xl mt-10 mb-2">Form Fields</h1>
              <Table>
                <Table.Head>
                  <Table.Row>
                    <Table.Th>field Name</Table.Th>
                    <Table.Th>Action</Table.Th>
                    <Table.Th></Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {selectedForm?.form?.formData?.map((item, idx) => {
                    return (
                      <Table.Row key={idx}>
                        <Table.Td className="font-semibold">
                          {item?.fieldName}
                        </Table.Td>
                        {item?.fieldType === 'Range' && (
                          <Table.Td>
                            <div className="flex gap-5">
                              <Input
                                className=""
                                placeholder="Min Range"
                                type="number"
                                value={filledFormData?.[item?.fieldName]?.min}
                                onChange={(e) =>
                                  setFilledFormData((prev) => ({
                                    ...prev,
                                    [item?.fieldName]: {
                                      ...prev?.[item?.fieldName],
                                      min: e.target.value,
                                    },
                                  }))
                                }
                              />
                              <Input
                                className=""
                                placeholder="Max Range"
                                type="number"
                                value={filledFormData?.[item?.fieldName]?.max}
                                onChange={(e) =>
                                  setFilledFormData((prev) => ({
                                    ...prev,
                                    [item?.fieldName]: {
                                      ...prev?.[item?.fieldName],
                                      max: e.target.value,
                                    },
                                  }))
                                }
                              />
                            </div>
                          </Table.Td>
                        )}
                        {item?.fieldType === 'Range Threshold' && (
                          <Table.Td>
                            <div className="flex gap-5">
                              <Input
                                className=""
                                placeholder="Min Value"
                                value={filledFormData?.[item?.fieldName]?.min}
                                onChange={(e) =>
                                  setFilledFormData((prev) => ({
                                    ...prev,
                                    [item?.fieldName]: {
                                      ...prev?.[item?.fieldName],
                                      min: e.target.value,
                                    },
                                  }))
                                }
                              />
                              <Input
                                className=""
                                placeholder="Threshold Value"
                                value={
                                  filledFormData?.[item?.fieldName]?.threshold
                                }
                                onChange={(e) =>
                                  setFilledFormData((prev) => ({
                                    ...prev,
                                    [item?.fieldName]: {
                                      ...prev?.[item?.fieldName],
                                      threshold: e.target.value,
                                    },
                                  }))
                                }
                              />
                            </div>
                          </Table.Td>
                        )}
                        {item?.fieldType === 'String' && (
                          <Table.Td>
                            <Input
                              type="text"
                              className=""
                              placeholder="String"
                              value={filledFormData?.[item?.fieldName]}
                              onChange={(e) =>
                                setFilledFormData((prev) => ({
                                  ...prev,
                                  [item?.fieldName]: e.target.value,
                                }))
                              }
                            />
                          </Table.Td>
                        )}
                        {item?.fieldType === 'MultiCheckbox' && (
                          <Table.Td>
                            <Input />
                          </Table.Td>
                        )}
                        {item?.fieldType === 'Date' && (
                          <Table.Td>
                            <div className="flex gap-5">
                              <Input
                                className=""
                                type="date"
                                value={filledFormData?.[item?.fieldName]}
                                onChange={(e) =>
                                  setFilledFormData((prev) => ({
                                    ...prev,
                                    [item?.fieldName]: e.target.value,
                                  }))
                                }
                              />
                            </div>
                          </Table.Td>
                        )}
                        {item?.fieldType === 'Check' && (
                          <Table.Td>
                            <input
                              className="text-[30px]"
                              type="checkbox"
                              checked={filledFormData?.[item?.fieldName]}
                              onChange={(e) =>
                                setFilledFormData((prev) => ({
                                  ...prev,
                                  [item?.fieldName]: e.target.checked,
                                }))
                              }
                            />
                          </Table.Td>
                        )}
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
              <Button
                className="mt-5"
                onClick={() => {
                  setInputData((prev) => ({
                    ...prev,
                    data: {
                      ...(prev?.data || {}),
                      [selectedForm?.name]: {
                        formName: selectedForm?.form?.formName,
                        formData: filledFormData,
                      },
                    },
                  }));
                  setFormFilling(false);
                }}
              >
                SUBMIT
              </Button>
            </div>
          )}
          {step === 1 && (
            <>
              {inputData?.attachments?.length > 0 && (
                <div>
                  <Table>
                    <Table.Head>
                      <Table.Th>Name</Table.Th>
                      <Table.Th>Select Departments</Table.Th>
                      <Table.Th></Table.Th>
                    </Table.Head>
                    <Table.Body>
                      {inputData?.attachments?.map((item, idx) => {
                        return (
                          <Table.Row key={item?._id}>
                            <Table.Td>{item?.name}</Table.Td>
                            <Table.Td>
                              <MultiSelect
                                options={
                                  data?.results
                                    ? data?.results
                                        ?.filter(
                                          (department) =>
                                            department?.isDefault === false &&
                                            department?._id !==
                                              currentDepartmentId
                                        )
                                        ?.map((department) => ({
                                          label: department?.name,
                                          value: department?._id,
                                        }))
                                    : []
                                }
                                onChange={(e) => setDepartments(e, item?.name)}
                                value={item?.departments}
                                placeholder="Select Departments"
                              />
                            </Table.Td>
                            <Table.Td>
                              <TiDelete
                                className="w-[65%] h-[65%]"
                                onClick={() => {
                                  if (inputData?.attachments?.length === 1) {
                                    setStep(0);
                                  }
                                  handleMediaRemove(idx, item?._id);
                                }}
                              />
                            </Table.Td>
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                  </Table>
                </div>
              )}
              {formFilling && selectedForm?.form && (
                <div>
                  <h1 className="text-xl mt-10 mb-2">Form Fields</h1>
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>field Name</Table.Th>
                        <Table.Th>Action</Table.Th>
                        <Table.Th></Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {selectedForm?.form?.formData?.map((item, idx) => {
                        return (
                          <Table.Row key={idx}>
                            <Table.Td className="font-semibold">
                              {item?.fieldName}
                            </Table.Td>
                            {item?.fieldType === 'Range' && (
                              <Table.Td>
                                <div className="flex gap-5">
                                  <Input
                                    className=""
                                    placeholder="Min Range"
                                    type="number"
                                    value={
                                      filledFormData?.[item?.fieldName]?.min
                                    }
                                    onChange={(e) =>
                                      setFilledFormData((prev) => ({
                                        ...prev,
                                        [item?.fieldName]: {
                                          ...prev?.[item?.fieldName],
                                          min: e.target.value,
                                        },
                                      }))
                                    }
                                  />
                                  <Input
                                    className=""
                                    placeholder="Max Range"
                                    type="number"
                                    value={
                                      filledFormData?.[item?.fieldName]?.max
                                    }
                                    onChange={(e) =>
                                      setFilledFormData((prev) => ({
                                        ...prev,
                                        [item?.fieldName]: {
                                          ...prev?.[item?.fieldName],
                                          max: e.target.value,
                                        },
                                      }))
                                    }
                                  />
                                </div>
                              </Table.Td>
                            )}
                            {item?.fieldType === 'Range Threshold' && (
                              <Table.Td>
                                <div className="flex gap-5">
                                  <Input
                                    className=""
                                    placeholder="Min Value"
                                    value={
                                      filledFormData?.[item?.fieldName]?.min
                                    }
                                    onChange={(e) =>
                                      setFilledFormData((prev) => ({
                                        ...prev,
                                        [item?.fieldName]: {
                                          ...prev?.[item?.fieldName],
                                          min: e.target.value,
                                        },
                                      }))
                                    }
                                  />
                                  <Input
                                    className=""
                                    placeholder="Threshold Value"
                                    value={
                                      filledFormData?.[item?.fieldName]
                                        ?.threshold
                                    }
                                    onChange={(e) =>
                                      setFilledFormData((prev) => ({
                                        ...prev,
                                        [item?.fieldName]: {
                                          ...prev?.[item?.fieldName],
                                          threshold: e.target.value,
                                        },
                                      }))
                                    }
                                  />
                                </div>
                              </Table.Td>
                            )}
                            {item?.fieldType === 'String' && (
                              <Table.Td>
                                <Input
                                  type="text"
                                  className=""
                                  placeholder="String"
                                  value={filledFormData?.[item?.fieldName]}
                                  onChange={(e) =>
                                    setFilledFormData((prev) => ({
                                      ...prev,
                                      [item?.fieldName]: e.target.value,
                                    }))
                                  }
                                />
                              </Table.Td>
                            )}
                            {item?.fieldType === 'MultiCheckbox' && (
                              <Table.Td>
                                <Input />
                              </Table.Td>
                            )}
                            {item?.fieldType === 'Date' && (
                              <Table.Td>
                                <div className="flex gap-5">
                                  <Input
                                    className=""
                                    type="date"
                                    value={filledFormData?.[item?.fieldName]}
                                    onChange={(e) =>
                                      setFilledFormData((prev) => ({
                                        ...prev,
                                        [item?.fieldName]: e.target.value,
                                      }))
                                    }
                                  />
                                </div>
                              </Table.Td>
                            )}
                            {item?.fieldType === 'Check' && (
                              <Table.Td>
                                <input
                                  className="text-[30px]"
                                  type="checkbox"
                                  checked={filledFormData?.[item?.fieldName]}
                                  onChange={(e) =>
                                    setFilledFormData((prev) => ({
                                      ...prev,
                                      [item?.fieldName]: e.target.checked,
                                    }))
                                  }
                                />
                              </Table.Td>
                            )}
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                  </Table>
                  <Button
                    className="mt-5"
                    onClick={() => {
                      setInputData((prev) => ({
                        ...prev,
                        data: {
                          ...(prev?.data || {}),
                          [selectedForm?.name]: {
                            formName: selectedForm?.form?.formName,
                            formData: filledFormData,
                          },
                        },
                      }));
                      setFormFilling(false);
                      setStep((prev) => prev - 1);
                    }}
                  >
                    SUBMIT
                  </Button>
                </div>
              )}
            </>
          )}
        </>
      )}
    </Modal>
  );
};

export default ManageDepartmentRowModal;
