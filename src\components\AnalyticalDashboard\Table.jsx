import { useEffect, useState } from 'react';
import { getTimeDifference } from '../../helperFunction';
import { useLazyGetAllMachineDataQuery } from '../../slices/createAnalyticsApiSlice';
import { useLazyGetCreateInputByIDQuery } from '../../slices/createInputApiSlice';
import { useGetAllMachinesQuery } from '../../slices/machineApiSlice';

const Table = ({ params, pid }) => {
  const [macDataCount, setMacDataCount] = useState([]);
  const [error, setError] = useState([]);

  const [getCreateInputByID] = useLazyGetCreateInputByIDQuery();

  const [getAllMachineData] = useLazyGetAllMachineDataQuery();

  const { data: machData = {} } = useGetAllMachinesQuery();
  const { machines: allMacId = [] } = machData;

  //Project ID
  let id = pid;

  useEffect(() => {
    const fetchData = async () => {
      try {
        const allerror = await getCreateInputByID({ id: id }, false).unwrap();
        setError(allerror.cuProjects);
      } catch (error) {
        console.error('Error fetching data:', error); // eslint-disable-line
      }
    };
    if (id) fetchData();
  }, [id, getCreateInputByID]);

  const emsgs = [];
  const operators = [];
  const machine = [];
  const workerData = [];
  const machineUptimeData = [];
  const machineDowntimeData = [];

  error.forEach((itm) => {
    itm.machineAndOperator.forEach((item) => {
      machine.push(item.machine.machineId);
      item.operator.forEach((i) => {
        operators.push(i.user.RFId);
      });
    });
    itm.errorMessages.forEach((item) => {
      emsgs.push(item.error.message);
    });
  });

  let uniqueOperator = operators.filter(
    (item, i, ar) => ar.indexOf(item) === i
  );
  let uniqueMachine = machine.filter((item, i, ar) => ar.indexOf(item) === i);

  //Here unique machine and operator is stored in above which then used to filter the data

  uniqueOperator.map((item) => {
    let workerArray = [];
    error.map((itm) => {
      itm.machineAndOperator.map((itr) => {
        itr.operator.map((i) => {
          if (i.user.RFId === item) {
            const res = {
              action: i.action,
              time: i.time,
            };
            workerArray.push(res);
          }
        });
      });
    });
    //================================================================================================================================
    //Now we calculate the working hour for different workers in uniqueOperator--->>>

    let netActiveTime = 0;
    let isPaused = false;
    let lastEventTime = null;

    workerArray.forEach(function (event) {
      let eventType = event.action;

      if (eventType === 'start' || eventType === 'resume') {
        let eventTime = new Date(event.time).getTime(); // Convert to milliseconds
        if (lastEventTime !== null && isPaused) {
          netActiveTime += eventTime - lastEventTime;
        }
        isPaused = false;
        lastEventTime = eventTime;
      } else if (eventType === 'pause') {
        isPaused = true;
        lastEventTime = new Date(event.time).getTime(); // Convert to milliseconds
      } else if (eventType === 'stop') {
        let eventTime = new Date(event.time).getTime(); // Convert to milliseconds
        if (lastEventTime !== null && !isPaused) {
          netActiveTime += eventTime - lastEventTime;
        }
        lastEventTime = null;
      }
    });

    let netActiveHours = netActiveTime / (1000 * 60); // Convert milliseconds to hours
    const ans = {
      workerID: item,
      uptime: netActiveHours || 0,
    };

    workerData.push(ans);
  });

  //Now we calculate the uptime  for different machine in uniqueMachine--->>>

  uniqueMachine.forEach((machine) => {
    let tempData = [];
    error.forEach((cuPro) =>
      cuPro.machineAndOperator.forEach((mao) => {
        if (mao.machine.machineId === machine) {
          tempData.push({
            mao,
            errorMessages: cuPro?.errorMessages?.filter(
              (em) => em.machineAndOperatorId === mao._id
            ),
          });
        }
      })
    );
    let upTimeArray = [];
    let plannedDTArray = [];
    let unPlannedDTArray = [];

    tempData.forEach((data) => {
      const startTime = data.mao.startTime;
      const stopTime = data.mao.stopTime;
      const pauseTime = data.mao.pauseTime;
      const resumeTime = data.mao.resumeTime;
      const firstPauseTime = pauseTime[0];
      const lastStopTime = resumeTime[resumeTime?.length - 1];
      const pauseErrs = data.errorMessages.filter(
        (em) =>
          em.error.action === 'pause' &&
          em.machineAndOperatorId === data.mao._id
      );

      if (pauseTime?.length > 0) {
        // downtime
        // if machines is paused
        pauseTime.forEach((time, tIdx) => {
          const val = getTimeDifference(time, resumeTime[tIdx]);
          const timer = pauseErrs?.[tIdx]?.error?.timer;
          if (timer) {
            // if paused with timer check if time excced or not
            if (val > +timer) {
              // if val exceeds timer push val to unPlannedDT array
              plannedDTArray.push(0);
              unPlannedDTArray.push(val);
            } else {
              // push val to plannedDT array
              plannedDTArray.push(val);
              unPlannedDTArray.push(0);
            }
          } else {
            // push val to unplannedDT array
            unPlannedDTArray.push(val);
            plannedDTArray.push(0);
          }
        });

        // upTime
        const firstVal = getTimeDifference(startTime, firstPauseTime);

        let middleVals = [];
        pauseTime?.forEach((time, tIdx) => {
          if (tIdx === 0) return;
          middleVals.push(getTimeDifference(resumeTime[tIdx - 1], time));
        });

        const lastVal = getTimeDifference(lastStopTime, stopTime);

        upTimeArray.push(...[firstVal, ...middleVals, lastVal]);
      } else {
        // set both DT array to 0
        plannedDTArray.push(0);
        unPlannedDTArray.push(0);
        // set upTime array to diff between start and stop time
        const val = getTimeDifference(startTime, stopTime);
        upTimeArray.push(val);
      }
    });

    // calcuate values for temp generated data
    const upTime = Math.round(
      upTimeArray.reduce((acc, curVal) => acc + curVal, 0)
    );

    const plannedDT = Math.round(
      plannedDTArray.reduce((acc, curVal) => acc + curVal, 0)
    );

    const unPlannedDT = Math.round(
      unPlannedDTArray.reduce((acc, curVal) => acc + curVal, 0)
    );

    const ans1 = {
      macID: machine,
      uptime: upTime || 0,
    };

    machineUptimeData.push(ans1);

    const ans2 = {
      macID: machine,
      downtime: plannedDT + unPlannedDT || 0,
    };

    machineDowntimeData.push(ans2);
  });

  // ===================================================================================================================================

  let frequency = emsgs.reduce(function (freq, str) {
    freq[str] = (freq[str] || 0) + 1;
    return freq;
  }, {});

  let uniqueStrings = Object.keys(frequency).map(function (key) {
    let obj = {};
    obj[key] = frequency[key];
    return obj;
  });

  useEffect(() => {
    let ids = [];
    allMacId.forEach((itm) => {
      ids.push(itm._id);
    });
    const data = ids.join(',');

    const fetchData = async () => {
      try {
        const res = await getAllMachineData({ data }).unwrap();
        setMacDataCount(res.allMacData);
      } catch (error) {
        console.error(error); // eslint-disable-line
      }
    };
    fetchData();
  }, [allMacId, getAllMachineData]);

  const filteredCount = macDataCount
    .filter((item) => item !== null)
    .sort((a, b) => b.data.COUNT - a.data.COUNT);

  const renderTable = (params) => {
    switch (params) {
      case 'Error Count':
        return (
          <div className="flex justify-center items-center ">
            <div className="w-full p-6 rounded-md ">
              <h2 className="text-2xl font-bold mb-4">Top Error Count</h2>
              <table className="min-w-full border ">
                <thead>
                  <tr>
                    <th className="py-2 px-2 border-b">Error Name</th>
                    <th className="py-2 px-4 border-b">Frequency</th>
                  </tr>
                </thead>
                <tbody>
                  {uniqueStrings?.map((item) => (
                    <tr key={item.id}>
                      <td className="py-2 px-[20%] border-b">
                        {Object.keys(item)[0]}
                      </td>

                      <td className="py-2 px-[20%] border-b">
                        {Object.values(item)[0]}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'Machine Count':
        return (
          <div className="flex justify-center items-center ">
            <div className="w-full p-6 rounded-md ">
              <h2 className="text-2xl font-bold mb-4">Top Machine Count</h2>
              <table className="min-w-full border">
                <thead>
                  <tr>
                    <th className="py-2 px-4 border-b">Machine Name</th>
                    <th className="py-2 px-4 border-b">Mac ID</th>
                    <th className="py-2 px-4 border-b">Count</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCount.map((item) => (
                    <tr key={item.machine.machineId}>
                      <td className="py-2 px-10 border-b">
                        {item.machine.machineName}
                      </td>
                      <td className="py-2 px-10 border-b">
                        {item.machine.machineId}
                      </td>
                      <td className="py-2 px-10 border-b">{item.data.COUNT}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'Worker Efficiency':
        return (
          <div className="flex justify-center items-center ">
            <div className="w-full p-6 rounded-md ">
              <h2 className="text-2xl font-bold mb-4">Worker Uptime</h2>
              <table className="min-w-full border">
                <thead>
                  <tr>
                    <th className="py-2 px-4 border-b">Worker RFId</th>

                    <th className="py-2 px-4 border-b">Uptime in mins</th>
                  </tr>
                </thead>
                <tbody>
                  {workerData.map((item) => (
                    <tr key={item.workerID}>
                      <td className="py-2 px-20 border-b">{item.workerID}</td>
                      <td className="py-2 px-20 border-b">{item.uptime}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'Machine Uptime':
        return (
          <div className="flex justify-center items-center ">
            <div className="w-full p-6 rounded-md ">
              <h2 className="text-2xl font-bold mb-4">Machine Uptime</h2>
              <table className="min-w-full border">
                <thead>
                  <tr>
                    <th className="py-2 px-4 border-b">Machine macID</th>

                    <th className="py-2 px-4 border-b">Uptime in mins</th>
                  </tr>
                </thead>
                <tbody>
                  {machineUptimeData.map((item) => (
                    <tr key={item.macID}>
                      <td className="py-2 px-20 border-b">{item.macID}</td>
                      <td className="py-2 px-20 border-b">{item.uptime}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'Machine Downtime':
        return (
          <div className="flex justify-center items-center ">
            <div className="max-w-md p-6 rounded-md ">
              <h2 className="text-2xl font-bold mb-4">Machine Downtime</h2>
              <table className="min-w-full border">
                <thead>
                  <tr>
                    <th className="py-2 px-4 border-b">Machine macID</th>

                    <th className="py-2 px-4 border-b">Downtime in mins</th>
                  </tr>
                </thead>
                <tbody>
                  {machineDowntimeData.map((item) => (
                    <tr key={item.macID}>
                      <td className="py-2 px-4 border-b">{item.macID}</td>
                      <td className="py-2 px-4 border-b">{item.downtime}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      default:
        return <h2>Wrong input type</h2>;
    }
  };

  return <div>{renderTable(params)}</div>;
};

export default Table;
