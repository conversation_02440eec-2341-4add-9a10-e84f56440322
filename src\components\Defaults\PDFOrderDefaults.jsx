import { useCallback, useContext, useState } from 'react';
import { toast } from 'react-toastify';
import { useUpdateDefaultsMutation } from '../../slices/defaultsApiSlice';
import { Store } from '../../store/Store';
import Button from '../global/components/Button';
import ButtonGroup from '../global/components/ButtonGroup';
import DraggableListItem from './DraggableListItem';

const ALL_PDF_ORDER_DEFAULTS = [
  'Sales Order',
  'Sales Quotation',
  'Sales Inquiry',
  'Request For Quotation',
  'Purchase Order',
  'Proforma Invoice',
  'Stock Out',
  'Dispatch',
  'Leads',
  'GRN',
  'Indent',
  'Journal',
];

const PDF_ORDER_DEFAULTS_MAP = {
  'Sales Order': [
    'COMPANY DETAILS',
    'SALES ORDER DETAILS',
    'ADDRESS DETAILS',
    'MORE DETAILS',
    'PRODUCT DETAILS',
    'COMPANY SIGNATURE',
    'TERMS AND CONDITIONS',
    'PRODUCT TEMPLATE FIELDS',
    'SIGNATURES',
  ],
  'Sales Quotation': [
    'COMPANY DETAILS',
    'QUOTATION DETAILS',
    'CUSTOMER DETAILS',
    'MORE DETAILS',
    'PRODUCT DETAILS',
    'ADDITIONAL COMMENTS',
    'TERMS AND CONDITIONS',
    'PRODUCT TEMPLATE FIELDS',
    'SIGNATURES',
  ],
  'Sales Inquiry': [
    'COMPANY DETAILS',
    'INQUIRY DETAILS',
    'PRODUCT DETAILS',
    'TEMPLATE FIELDS',
    'REMARKS',
  ],
  'Request For Quotation': [
    'COMPANY DETAILS',
    'QUOTATION DETAILS',
    'ADDRESS DETAILS',
    'PRODUCT DETAILS',
    'TERMS AND CONDITIONS',
  ],
  'Proforma Invoice': [
    'COMPANY DETAILS',
    'PROFORMA INVOICE DETAILS',
    'ADDRESS DETAILS',
    'PRODUCT DETAILS',
    'TERMS AND CONDITIONS',
  ],
  'Purchase Order': [
    'COMPANY DETAILS',
    'PURCHASE ORDER DETAILS',
    'VENDOR DETAILS',
    'PRODUCT DETAILS',
    'TERMS AND CONDITIONS',
  ],
  'Stock Out': [
    'COMPANY DETAILS',
    'ORDER DETAILS',
    'PRODUCT DETAILS',
    'TEMPLATE FIELDS',
  ],
  Dispatch: [
    'COMPANY DETAILS',
    'DISPATCH DETAILS',
    'PRODUCT DETAILS',
    'MORE DETAILS',
  ],
  Leads: ['COMPANY DETAILS', 'LEAD DETAILS', 'PRODUCT DETAILS'],
  GRN: [
    'COMPANY DETAILS',
    'GRN DETAILS',
    'VENDOR DETAILS',
    'INPAGE DETAILS',
    'INVOICE DETAILS',
    'TEMPLATE FIELDS',
  ],
  Indent: ['COMPANY DETAILS', 'INDENT DETAILS', 'PRODUCT DETAILS'],
  Journal: ['COMPANY DETAILS', 'JOURNAL DETAILS', 'ACCOUNT DETAILS'],
  Voucher: [
    'COMPANY DETAILS',
    'VOUCHER DETAILS',
    'PARTY DETAILS',
    'ITEMS TABLE',
    'ACCOUNTS TABLE',
    'PAYMENT DETAILS',
    'TERMS AND CONDITIONS',
  ],
  TrialBalance: ['COMPANY DETAILS', 'TRIAL BALANCE DETAILS'],
};

export default function PDFOrderDefaults() {
  const { defaults } = useContext(Store);

  const getMergedDefaults = (key) => {
    const dbValues = defaults?.defaultParam?.pdfOrderDefaults?.[key] || [];
    const templateValues = PDF_ORDER_DEFAULTS_MAP[key] || [];

    // Find newly added fields (not in DB)
    const newFields = templateValues.filter(
      (field) => !dbValues.includes(field)
    );

    // Keep DB order and append new fields
    return [...dbValues, ...newFields];
  };

  const [selectedTab, setSelectedTab] = useState(ALL_PDF_ORDER_DEFAULTS[0]);
  const [orderList, setOrderList] = useState(
    getMergedDefaults(ALL_PDF_ORDER_DEFAULTS[0])
  );
  const [updateDefaults, { isLoading: isUpdateDefaultsLoading }] =
    useUpdateDefaultsMutation();

  const moveItem = useCallback((dragIndex, hoverIndex) => {
    setOrderList((prev) => {
      const newList = [...prev];
      const [removed] = newList.splice(dragIndex, 1);
      newList.splice(hoverIndex, 0, removed);
      return newList;
    });
  }, []);

  const handleTabChange = (val) => {
    if (val === selectedTab) return;
    setSelectedTab(val);
    setOrderList(getMergedDefaults(val));
  };

  const handleSubmit = async () => {
    const updatedPdfOrderDefaults = {
      ...defaults?.defaultParam?.pdfOrderDefaults,
      [selectedTab]: orderList,
    };

    await updateDefaults({
      ...defaults,
      pdfOrderDefaults: updatedPdfOrderDefaults,
    });

    setOrderList(updatedPdfOrderDefaults[selectedTab]);

    toast.success('Order list updated successfully', {
      position: 'top-right',
    });
  };

  return (
    <div className="w-full h-screen">
      <div className="w-full flex flex-col gap-y-5">
        <ButtonGroup
          buttons={ALL_PDF_ORDER_DEFAULTS}
          value={selectedTab}
          onChange={handleTabChange}
          className="w-full text-nowrap  overflow-x-auto"
        />

        <div className="mt-6 ">
          <ul className="flex flex-col gap-y-2">
            {orderList.map((el, index) => (
              <DraggableListItem key={el} index={index} moveItem={moveItem}>
                {el}
              </DraggableListItem>
            ))}
          </ul>
        </div>

        <div className="flex justify-end">
          <Button
            onClick={handleSubmit}
            disabled={isUpdateDefaultsLoading}
            isLoading={isUpdateDefaultsLoading}
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
}
