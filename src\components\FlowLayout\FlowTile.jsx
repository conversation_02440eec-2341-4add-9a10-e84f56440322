import Default from './Default';
import Errors from './Errors';
import Machine from './Machine';
import Worker from './Worker';

const FlowTile = ({
  isMobile,
  isTablet,
  data,
  index,
  cuProjects,
  nextCuProjects,
  selectedFilter,
  proLenght,
  processGoalViews,
  deviceDatas,
  isReversed,
  isLastInRow,
  isLastItem,
  reportData,
  setReportData,
  selectedBatch,
  setAllSelectedMachines,
  setOpenSideBar,
  qcSampleData,
  setShowErrorSidebar,
  setAddtionalFields,
}) => {
  const switchTile = (props) => {
    switch (selectedFilter) {
      case 'Default':
        return <Default {...props} />;
      case 'Errors':
        return <Errors {...props} />;
      case 'Machine':
        return <Machine {...props} />;
      case 'Worker':
        return <Worker {...props} />;
      default:
        return <p>Invalid Filter</p>;
    }
  };

  return (
    <>
      {switchTile({
        isMobile,
        isTablet,
        data,
        index,
        cuProjects,
        nextCuProjects,
        proLenght,
        processGoalViews,
        deviceDatas,
        isReversed,
        isLastInRow,
        isLastItem,
        reportData,
        setReportData,
        selectedBatch,
        setAllSelectedMachines,
        setOpenSideBar,
        qcSampleData,
        setShowErrorSidebar,
        setAddtionalFields,
      })}
    </>
  );
};

export default FlowTile;
