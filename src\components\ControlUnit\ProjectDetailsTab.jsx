import { useEffect, useState } from 'react';
import pdf from '../../assets/images/pdf.png';
import { useLazyGetAllMachineIdsQuery } from '../../slices/machineIdApiSlice';
import { useLazyGetMediaByIdQuery } from '../../slices/mediaSlice';
import PdfViewer from '../global/components/PdfViewer';
import ProjectDetails from './ProjectDetails';

const ProjectDetailsTab = ({ cuProject = {}, itemForJob }) => {
  const [showImage, setShowImage] = useState({ data: '', type: '' });
  const [cuData, setCuData] = useState({});
  const [mediaData, setMediaData] = useState([]);
  const [getAllMachineIds, { data: allMachineIds }] =
    useLazyGetAllMachineIdsQuery();
  const [getMediaById] = useLazyGetMediaByIdQuery();

  useEffect(() => {
    getAllMachineIds();
  }, [getAllMachineIds]);

  useEffect(() => {
    if (itemForJob?.media) {
      Promise.all(
        itemForJob?.media?.map(async (itm) => {
          const temp = await getMediaById({ id: itm }).unwrap();
          return temp.media;
        })
      ).then((res) => setMediaData(res));
    }
  }, [itemForJob]); //eslint-disable-line

  useEffect(() => {
    if (allMachineIds) {
      setCuData(() =>
        allMachineIds?.machineId?.find(
          (e) => e?.mqtt?._id === cuProject?.mqtt?._id
        )
      );
    }
  }, [allMachineIds]); // eslint-disable-line

  return (
    <>
      {showImage?.data ? (
        <>
          {showImage?.type === 'application/pdf' ? (
            <div className="z-[100] fixed top-0 left-0 h-screen w-screen flex justify-center items-center bg-black/20">
              <div className="relative w-[95%] h-[95%]">
                <PdfViewer
                  file={showImage?.data}
                  name={showImage?.name}
                  closeClick={() => setShowImage({ data: '', type: '' })}
                />
              </div>
            </div>
          ) : (
            <div
              className="z-[100] fixed top-0 left-0 h-screen w-screen flex justify-center items-center bg-black/20"
              onClick={() => setShowImage({ data: '', type: '' })}
            >
              <img
                src={showImage?.data}
                alt=""
                className="h-[90%] aspect-video object-contain"
              />
            </div>
          )}
        </>
      ) : null}
      <div className="text-white h-full mt-5 p-2 overflow-y-scroll no-scrollbar">
        {cuProject?.project?.imageURLs && (
          <div className="w-full flex px-4 gap-x-4 overflow-y-scroll no-scrollbar">
            {[
              ...(cuProject?.project?.imageURLs?.project || []),
              ...(mediaData || []),
            ]?.map((url, uIdx) => {
              return (
                <section
                  key={uIdx}
                  className="p-2 border rounded-md w-fit flex flex-col justify-between"
                >
                  <img
                    src={url?.type === 'application/pdf' ? pdf : url?.data}
                    alt=""
                    className="hover:cursor-pointer min-w-[180px] max-w-[250px] aspect-video object-contain"
                    onClick={() =>
                      setShowImage({
                        data: url.data,
                        type: url.type,
                        name: url.name,
                      })
                    }
                  />

                  <section className="flex justify-between items-center text-sm mt-2">
                    <span className="px-2 text-black">{url.name}</span>
                  </section>
                </section>
              );
            })}
            {cuProject?.project?.imageURLs?.[cuProject?.mqtt?._id]?.map(
              (url, uIdx) => (
                <section
                  key={uIdx}
                  className="p-2 border rounded-md w-fit flex flex-col justify-between"
                >
                  <img
                    src={url?.type === 'application/pdf' ? pdf : url?.data}
                    alt=""
                    className="hover:cursor-pointer min-w-[180px] max-w-[250px] aspect-video object-contain"
                    onClick={() =>
                      setShowImage({
                        data: url.data,
                        type: url.type,
                        name: url.name,
                      })
                    }
                  />

                  <section className="flex justify-between items-center text-sm mt-2">
                    <span className="px-2 text-black">{url.name}</span>
                  </section>
                </section>
              )
            )}
          </div>
        )}

        <ProjectDetails alldata={cuProject?.project?.alldata} cuData={cuData} />
      </div>
    </>
  );
};

export default ProjectDetailsTab;
