import { Button, Form, Layout, Modal } from 'antd';
import { PlusCircle } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';
import {
  useCreateTemplateMutation,
  useDeleteTemplateMutation,
  useGetTemplatesQuery,
  useUpdateTemplateMutation,
} from '../../slices/whatsappConfigurationApiSlice';
import Header from '../WhatsAppConfig/Header';
import TemplateForm from '../WhatsAppConfig/TemplateForm';
import TemplateList from '../WhatsAppConfig/TemplateList';
const { Content } = Layout;

const defaultTemplate = [
  {
    isDefault: false,
    name: 'Renewal',
    language: 'en',
    category: 'RENEWAL',
    body: {
      text: "Welcome to our service! We're glad to have you with us, {company_name}}. Feel free to reach out if you need any assistance.",
      variables: [{ name: '{{company_name}}' }],
    },
    constant: true,
  },
  //po default template
  {
    isDefault: false,
    name: 'PO',
    language: 'en',
    category: 'PO',
    body: {
      text: "Hi {{company_name}}, \n\nWelcome to our service! We're glad to have you with us. Feel free to reach out if you need any assistance.",
      variables: [{ name: '{{company_name}}' }],
    },
    constant: true,
  },
  //quotation default template
  {
    isDefault: false,
    name: 'QUOTATION',
    language: 'en',
    category: 'QUOTATION',
    body: {
      text: "Hi {{company_name}}, \n\nWelcome to our service! We're glad to have you with us. Feel free to reach out if you need any assistance.",
      variables: [{ name: '{{company_name}}' }],
    },
    constant: true,
  },
];

const WhatsAppConfiguration = () => {
  const { data: templatesData } = useGetTemplatesQuery();
  const [isConnected, setIsConnected] = useState(false);
  const [templateModalOpen, setTemplateModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [previewTemplate, setPreviewTemplate] = useState({});
  const [isPreviewTemplate, setIsPreviewTemplate] = useState(false);
  const [form] = Form.useForm();
  const [createTemplate] = useCreateTemplateMutation();
  const [updatedTemplate] = useUpdateTemplateMutation();
  const [deleteTemplate] = useDeleteTemplateMutation();

  const handleCreateTemplate = async (values) => {
    const newTemplate = {
      name: values.name,
      language: values.language,
      category: values.category,
      body: {
        text: values.content,
        variables:
          values.variables?.map((name) => ({
            name: name.name,
          })) || [],
      },
    };
    await createTemplate(newTemplate).unwrap();
    setTemplateModalOpen(false);
    setEditingTemplate(null);
    form.resetFields();
    toast.success('Template Created Successfully');
  };

  const handleEditTemplate = (template) => {
    setEditingTemplate(template);
    setTemplateModalOpen(true);
  };

  const handleUpdateTemplate = async (values) => {
    if (!editingTemplate) return;

    const data = {
      name: values.name,
      language: values.language,
      category: values.category,
      body: {
        text: values.content,
        variables:
          values.variables?.map((name) => ({
            name: name.name,
          })) || [],
      },
    };
    await updatedTemplate({ data, id: editingTemplate._id }).unwrap();
    setTemplateModalOpen(false);
    setEditingTemplate(null);
    form.resetFields();
    toast.success('Template Updated Successfully');
  };

  const handleDeleteTemplate = async (templateId) => {
    await deleteTemplate(templateId).unwrap();
    toast.success('Template Deleted Successfully');
  };
  const formatWhatsAppText = (text) => {
    if (!text) return '';
    return text
      .replace(/\*(.*?)\*/g, '<strong>$1</strong>') // Bold
      .replace(/_(.*?)_/g, '<em>$1</em>') // Italic
      .replace(/~(.*?)~/g, '<del>$1</del>') // Strikethrough
      .replace(/\n/g, '<br />'); // New lines
  };

  return (
    <div className="w-full">
      <div className="min-h-screen bg-gray-100">
        <Header
          isConnected={isConnected}
          setIsConnected={setIsConnected}
          onDisconnect={() => setIsConnected(false)}
        />

        <Content className="p-6">
          <div className="">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-800">
                Message Templates
              </h2>
              <Button
                type="primary"
                icon={<PlusCircle className="h-4 w-4" />}
                onClick={() => setTemplateModalOpen(true)}
                style={{ backgroundColor: '#25D366' }}
              >
                Create Template
              </Button>
            </div>

            <TemplateList
              templates={[...(defaultTemplate || []), ...(templatesData || [])]}
              onEdit={handleEditTemplate}
              onDelete={handleDeleteTemplate}
              onPreview={(template) => {
                setPreviewTemplate(template);
                setIsPreviewTemplate(true);
              }}
            />
          </div>
        </Content>
        <Modal
          title={previewTemplate?.name}
          open={isPreviewTemplate}
          onCancel={() => {
            setIsPreviewTemplate(false);
          }}
          centered
          footer={null}
        >
          <div className="p-4">
            <div className="mb-4 pb-3 border-b">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-500">Language:</span>
                <span className="font-medium uppercase">
                  {previewTemplate?.language}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Category:</span>
                <span className="font-medium capitalize">
                  {previewTemplate?.category}
                </span>
              </div>
            </div>

            <div className="mb-4">
              <h3 className="text-sm font-semibold text-gray-600 mb-2">
                Content:
              </h3>
              <div className="mb-4">
                <h3 className="mb-2">
                  Preview (as it will appear in WhatsApp)
                </h3>
                <div
                  className="p-4 border rounded bg-gray-50 whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{
                    __html: formatWhatsAppText(previewTemplate?.body?.text),
                  }}
                />
              </div>
            </div>

            <div className="mb-4">
              <h3 className="text-sm font-semibold text-gray-600 mb-2">
                Variables:
              </h3>
              <div className="flex flex-wrap gap-2">
                {previewTemplate?.body?.variables?.map((variable, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-sm"
                  >
                    {variable?.name}
                  </span>
                ))}
              </div>
            </div>

            <div className="text-right text-sm text-gray-500">
              Last Modified:{' '}
              {new Date(previewTemplate.updatedAt).toLocaleDateString()}
            </div>
          </div>
        </Modal>
        <Modal
          title={editingTemplate ? 'Edit Template' : 'Create New Template'}
          open={templateModalOpen}
          onCancel={() => {
            setTemplateModalOpen(false);
            setEditingTemplate(null);
            form.resetFields();
          }}
          footer={null}
          centered
          width={1000}
          styles={{
            body: { maxHeight: 'calc(100vh - 200px)', overflow: 'auto' },
          }}
        >
          <TemplateForm
            initialValues={editingTemplate || undefined}
            onSubmit={
              editingTemplate ? handleUpdateTemplate : handleCreateTemplate
            }
            form={form}
            onCancel={() => {
              setTemplateModalOpen(false);
              setEditingTemplate(null);
              form.resetFields();
            }}
          />
        </Modal>
      </div>
    </div>
  );
};

export default WhatsAppConfiguration;
