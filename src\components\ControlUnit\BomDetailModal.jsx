import Modal from '../global/components/Modal';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import {
  getPartVariantName,
  getProductVariantName,
} from '../../helperFunction';

const BomDetailModal = ({
  ShowModal,
  setShowModal,
  selectedWo,
  selectedBom,
  setSelectedBom,
  isLoadingBom,
}) => {
  return (
    <div>
      {ShowModal && (
        <Modal
          title="BOM Details"
          onCloseModal={() => {
            setShowModal(false);
            setSelectedBom([]);
          }}
          isSubmitRequired={false}
          canSubmit={false}
        >
          {() => {
            return (
              <>
                {isLoadingBom ? (
                  <Spinner />
                ) : (
                  <section>
                    <div>
                      <div className="heading mb-3">
                        <h3>Work Order Details</h3>
                      </div>
                      <p className="flex items-center justify-between font-semibold">
                        Work Order ID:{' '}
                        <span className="font-normal">
                          {selectedWo?.workOrderId}
                        </span>{' '}
                      </p>
                      <p className="flex items-center justify-between font-semibold">
                        Work Order Name:{' '}
                        <span className="font-normal">{selectedWo?.name}</span>
                      </p>
                      <p className="flex items-center justify-between font-semibold">
                        Work Order Type:{' '}
                        <span className="font-normal">{selectedWo?.type}</span>
                      </p>
                      <p className="flex items-center justify-between font-semibold">
                        Assigned Sales Order:
                      </p>
                    </div>

                    <div className="heading my-3">
                      <h3>BOM Details</h3>
                    </div>
                    {selectedBom?.map((e) => {
                      return (
                        <div className=" mt-5" key={e?._id}>
                          <hr className="mb-2" />
                          <div>
                            <p className="flex items-center justify-between font-semibold">
                              BOM ID:{' '}
                              <span className="font-normal">{e?.bomId}</span>{' '}
                            </p>
                            <p className="flex items-center justify-between font-semibold">
                              BOM Name:{' '}
                              <span className="font-normal">{e?.name}</span>
                            </p>
                            <p className="flex items-center justify-between font-semibold">
                              Order Quantity:{' '}
                              <span className="font-normal">
                                {selectedWo?.orderQuantity?.[e?._id]}
                              </span>
                            </p>
                          </div>

                          <div>
                            <div className="heading my-3">
                              <h3>Items Information</h3>
                            </div>
                            <Table>
                              <Table.Head>
                                <Table.Row>
                                  <Table.Th>Category</Table.Th>
                                  <Table.Th>Item</Table.Th>
                                  <Table.Th>Units</Table.Th>
                                  <Table.Th>InStock</Table.Th>
                                  <Table.Th>Stock Required</Table.Th>
                                  <Table.Th></Table.Th>
                                </Table.Row>
                              </Table.Head>
                              <Table.Body>
                                {e?.children?.map((item) => {
                                  return (
                                    <>
                                      <Table.Row key={item?._id}>
                                        <Table.Td>{item?.category}</Table.Td>
                                        <Table.Td>
                                          {item?.manualEntry ||
                                            item?.part?.name ||
                                            item?.product?.name ||
                                            (item?.partVariant
                                              ? getPartVariantName(
                                                  item?.partVariant
                                                )
                                              : getProductVariantName(
                                                  item?.productVariant
                                                ))}
                                        </Table.Td>
                                        <Table.Td>{item?.units}</Table.Td>
                                        <Table.Td>
                                          {item?.part?.quantity !== undefined
                                            ? item?.part?.quantity
                                            : item?.product?.quantity}
                                        </Table.Td>
                                        <Table.Td>
                                          {item?.units *
                                            selectedWo?.orderQuantity[e?._id]}
                                        </Table.Td>
                                        <Table.Td>
                                          {selectedWo?.rfd?.[item?._id]}
                                        </Table.Td>
                                      </Table.Row>
                                      {item?.children?.map((subItem) => {
                                        return (
                                          <>
                                            <Table.Row
                                              className="!bg-slate-100"
                                              key={subItem?._id}
                                            >
                                              <Table.Td>
                                                {subItem?.category}
                                              </Table.Td>
                                              <Table.Td>
                                                {subItem?.manualEntry ||
                                                  subItem?.part?.name ||
                                                  subItem?.product?.name ||
                                                  (subItem?.partVariant
                                                    ? getPartVariantName(
                                                        subItem?.partVariant
                                                      )
                                                    : getProductVariantName(
                                                        subItem?.productVariant
                                                      ))}
                                              </Table.Td>
                                              <Table.Td>
                                                {subItem?.units}
                                              </Table.Td>
                                              <Table.Td>
                                                {subItem?.part?.quantity ||
                                                  subItem?.product?.quantity}
                                              </Table.Td>
                                              <Table.Td>
                                                {parseInt(subItem?.units) *
                                                  parseInt(
                                                    selectedWo?.orderQuantity[
                                                      e?._id
                                                    ]
                                                  )}
                                              </Table.Td>
                                            </Table.Row>
                                            {subItem?.children?.map(
                                              (currElem) => {
                                                return (
                                                  <Table.Row
                                                    className="!bg-slate-100"
                                                    key={currElem?._id}
                                                  >
                                                    <Table.Td>
                                                      {currElem?.category}
                                                    </Table.Td>
                                                    <Table.Td>
                                                      {currElem?.manualEntry ||
                                                        currElem?.part?.name ||
                                                        currElem?.product
                                                          ?.name ||
                                                        (currElem?.partVariant
                                                          ? getPartVariantName(
                                                              currElem?.partVariant
                                                            )
                                                          : getProductVariantName(
                                                              currElem?.productVariant
                                                            ))}
                                                    </Table.Td>
                                                    <Table.Td>
                                                      {currElem?.units}
                                                    </Table.Td>
                                                    <Table.Td>
                                                      {currElem?.part
                                                        ?.quantity ||
                                                        currElem?.product
                                                          ?.quantity}
                                                    </Table.Td>
                                                    <Table.Td>
                                                      {parseInt(
                                                        currElem?.units
                                                      ) *
                                                        parseInt(
                                                          selectedWo
                                                            ?.orderQuantity[
                                                            e?._id
                                                          ]
                                                        )}
                                                    </Table.Td>
                                                  </Table.Row>
                                                );
                                              }
                                            )}
                                          </>
                                        );
                                      })}
                                    </>
                                  );
                                })}
                                <Table.Row></Table.Row>
                              </Table.Body>
                            </Table>
                          </div>
                        </div>
                      );
                    })}
                  </section>
                )}
              </>
            );
          }}
        </Modal>
      )}
    </div>
  );
};

export default BomDetailModal;
