import { Button, Checkbox, message, Modal } from 'antd';
import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGetDepartmentsQuery } from '../../slices/departmentApiSlice';
import {
  useGetTrialUserPageAccessQuery,
  useLazyGetTrialUserPageAccessQuery,
  useSavetrialUserPageAccessMutation,
} from '../../slices/trialuserApiSlice';
import { Store } from '../../store/Store';

const InitialModulesSetup = () => {
  const { isTrialUser } = useContext(Store);
  const { data: { results: allDepartments = [] } = {} } =
    useGetDepartmentsQuery();
  const [saveTrailUserPageAccess] = useSavetrialUserPageAccessMutation();
  const [getTrialUserPageAccess, { data }] =
    useLazyGetTrialUserPageAccessQuery();
  const { data: trialUserPageAccessData } = useGetTrialUserPageAccessQuery();
  const [selectedModules, setSelectedModules] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isKanbanAccess, setIsKanbanAccess] = useState(false);
  const navigate = useNavigate();
  const [selectedMultiple, setSelectedMultiple] = useState(false);
  useEffect(() => {
    if (
      isTrialUser &&
      trialUserPageAccessData?.trialUserPageAccess?.length === 0
    ) {
      setIsModalVisible(true);
    }
  }, [isTrialUser, trialUserPageAccessData?.trialUserPageAccess?.length]);

  const handleModuleSelection = (moduleId) => {
    const totalSelected = selectedModules.length + (isKanbanAccess ? 1 : 0);
    if (
      isTrialUser &&
      totalSelected === 1 &&
      !selectedModules.includes(moduleId)
    ) {
      message.error(
        'As a trial user, you can only select one module or Kanban Access. Please upgrade to premium for multiple selections.'
      );
      setSelectedMultiple(true);
      return;
    }

    setSelectedModules((prev) =>
      prev.includes(moduleId)
        ? prev.filter((id) => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  const handleKanbanAccess = () => {
    const totalSelected = selectedModules.length + (isKanbanAccess ? 1 : 0);
    if (isTrialUser && totalSelected === 1 && !isKanbanAccess) {
      message.error(
        'As a trial user, you can only select one module or Kanban Access. Please upgrade to premium for multiple selections.'
      );
      setSelectedMultiple(true);
      return;
    }
    setIsKanbanAccess((prev) => !prev);
  };

  const handleSave = async () => {
    const totalSelected = selectedModules.length + (isKanbanAccess ? 1 : 0);
    if (totalSelected === 0) {
      message.error('Please select at least one module or Kanban Access');
      return;
    }
    const departments = allDepartments.filter((dep) =>
      selectedModules.includes(dep._id)
    );

    await saveTrailUserPageAccess({
      modules: departments,
      isKanbanAccess: isKanbanAccess,
    });
    await getTrialUserPageAccess();
    if (data?.isKanbanAccess) {
      navigate('/');
    } else {
      navigate(data?.trialUserPageAccess?.[0]);
    }

    // Additional logic for saving can be added here
    // setIsModalVisible(false);
  };

  const handleClose = () => {
    const totalSelected = selectedModules.length + (isKanbanAccess ? 1 : 0);
    if (totalSelected === 0) {
      message.error(
        'Please select at least one module or Kanban Access before closing'
      );
      return;
    }
    setIsModalVisible(false);
  };
  const handleUpgrade = () => {
    navigate('/plans');
  };
  return (
    <div
      className={
        isModalVisible
          ? 'fixed inset-0 bg-white z-50 flex justify-center items-center'
          : ''
      }
    >
      <Modal
        title="Module setup"
        open={isModalVisible}
        centered
        onCancel={handleClose}
        footer={[
          selectedMultiple && (
            <Button key="upgrade" type="default" onClick={handleUpgrade}>
              Upgrade Plan
            </Button>
          ),
          <Button key="submit" type="primary" onClick={() => handleSave()}>
            Save
          </Button>,
        ]}
        maskClosable={false}
      >
        <div className="flex flex-col justify-center items-center gap-5 w-full h-full">
          <p>
            Please select the modules you want to use as initial setup. As a
            trial user, you can only select one module.
          </p>
          <div className="grid grid-cols-2 gap-4">
            {allDepartments.map((department) => (
              <Checkbox
                key={department.id}
                onChange={() => handleModuleSelection(department._id)}
                checked={selectedModules.includes(department._id)}
                className="capitalize"
              >
                {department.name}
              </Checkbox>
            ))}
            <Checkbox checked={isKanbanAccess} onChange={handleKanbanAccess}>
              Kanban
            </Checkbox>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default InitialModulesSetup;
