import { useState } from 'react';
import { toast } from 'react-toastify';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import Table from '../global/components/Table';
import Select from '../global/components/Select';
import { BiTrash } from 'react-icons/bi';
import { EditOutlined, CloseOutlined } from '@ant-design/icons';
// import { FcInfo } from 'react-icons/fc';
// import Details from '../Jobs/OrderDetails/Details';

const initialDetails = {
  Parameter: '',
  Type: '',
  'Horizontal Repetition': '1',
  'Vertical Repetition': '1',
  Title: '',
  Units: '',
  Formula: '',
  Rows: '1',
  Columns: '1',
  Span: '1',
  'Is Table': '',
  Options: [],
};

const ManualSheet = ({ setIsManual, setList, index, info }) => {
  const [fieldDetail, setFieldDetail] = useState(initialDetails);
  const [data, setData] = useState(
    info?.data[index]?.sheetData?.data?.length
      ? info?.data[index]?.sheetData?.data || []
      : []
  );
  // const [rows, setRows] = useState(1);
  // const [columns, setColumns] = useState(1);
  const [editIdx, setEditIdx] = useState(-1);

  // const [isEdit, setIsEdit] = useState(
  //   info?.data?.length || info?.data[index]?.sheetData?.data?.length
  //     ? true
  //     : false
  // );

  const handleAdd = () => {
    if (fieldDetail?.['Parameter'] === '' || fieldDetail?.['Type'] === '') {
      toast.error('Parameter Name and Type is required');
      return;
    }
    if (editIdx < 0) {
      setData((prev) => [
        ...prev,
        {
          ...fieldDetail,
        },
      ]);
      setEditIdx(-1);
    } else {
      setData((prev) => {
        const updatedData = [...prev];
        updatedData[editIdx] = { ...fieldDetail };
        return updatedData;
      });
      setEditIdx(-1);
    }
    // if (columns < 4) {
    //   setColumns(columns + 1);
    // } else {
    //   setRows(rows + 1);
    //   setColumns(1);
    // }
    setFieldDetail(initialDetails);
  };

  const handleSubmit = () => {
    if (!data || data.length === 0) {
      toast.error('No fields added');
      return;
    }

    let rw = 1;
    let cl = 1;

    let nData = [];

    data.forEach((item) => {
      if (cl < 4) {
        nData.push({ ...item, Rows: rw.toString(), Columns: cl.toString() });
        cl += 1;
      } else {
        rw += 1;
        cl = 1;
        nData.push({ ...item, Rows: rw.toString(), Columns: cl.toString() });
        cl += 1;
      }
    });

    setList((prev) =>
      prev?.map((item, iIdx) => {
        if (iIdx === index) {
          return {
            ...item,
            data: [
              {
                sheetData: { data: nData },
                sheetName: 'Manual Sheet',
                isManual: true,
              },
            ],
          };
        } else {
          return item;
        }
      })
    );
    cl = 1;
    rw = 1;

    setIsManual(false);
  };

  return (
    <div>
      <Modal
        onCloseModal={() => {
          setIsManual(false);
        }}
        title="Add fields for sheet"
        onAdd={{ label: 'Add', func: [handleAdd], step: [0] }}
        onSubmit={handleSubmit}
      >
        {() => {
          return (
            <>
              <div className="flex gap-2">
                <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2 w-full">
                  <div className="flex flex-col mt-5">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Field Name{' '}
                    </label>
                    <Input
                      name="Parameter"
                      value={fieldDetail?.['Parameter'] || ''}
                      onChange={(e) => {
                        setFieldDetail((prev) => ({
                          ...prev,
                          [e.target.name]: e.target.value,
                        }));
                      }}
                      placeholder="Field Name"
                    />
                  </div>

                  <div className="flex flex-col mt-5">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Field Type{' '}
                    </label>
                    <Select
                      name="Type"
                      value={fieldDetail?.['Type'] || ''}
                      onChange={(e) => {
                        setFieldDetail((prev) => ({
                          ...prev,
                          [e.target.name]: e.target.value,
                        }));
                      }}
                      options={[
                        { value: 'NUMERIC', label: 'Number' },
                        { value: 'ALPHANUMERIC', label: 'Text' },
                        {
                          value: 'DROPDOWN',
                          label: 'Dropdown',
                        },
                      ]}
                    />
                  </div>

                  {/* ENDS HERE DPR */}
                </div>
                {editIdx >= 0 && (
                  <div className="flex flex-col  items-center justify-center mt-10  ">
                    <CloseOutlined
                      className="text-red-500"
                      onClick={() => {
                        setEditIdx(-1);
                        setFieldDetail(initialDetails);
                      }}
                    />
                  </div>
                )}
              </div>
              {fieldDetail?.['Type'] === 'DROPDOWN' && (
                <div className="mt-4 px-2 ">
                  <label className="mb-1 font-semibold text-[#667085]">
                    Dropdown Options
                  </label>

                  <div className="grid-cols-3 grid gap-5 justify-center">
                    {fieldDetail?.['Options']?.map((_, idX) => (
                      <div className=" flex items-center gap-3 mt-2 " key={idX}>
                        <Input
                          className="!bg-white"
                          value={fieldDetail?.['Options'][idX] || ''}
                          onChange={(e) => {
                            const newValue = e.target.value;

                            setFieldDetail((prev) => ({
                              ...prev,
                              Options: prev?.Options?.map((el, index) =>
                                index === idX ? newValue : el
                              ),
                            }));
                          }}
                        />

                        <BiTrash
                          size={20}
                          className="cursor-pointer text-red-500"
                          onClick={() =>
                            setFieldDetail((prev) => ({
                              ...prev,
                              Options: prev?.Options?.filter(
                                (_, index) => idX !== index
                              ),
                            }))
                          }
                        />
                      </div>
                    ))}
                  </div>
                  <p
                    className="text-sm text-blue-500 mt-2 cursor-pointer"
                    onClick={() =>
                      setFieldDetail((prev) => ({
                        ...prev,
                        Options: [...(prev?.Options || []), ''],
                      }))
                    }
                  >
                    + Add More
                  </p>
                </div>
              )}
              <Table className="w-full mt-6">
                <Table.Head>
                  <Table.Row>
                    <Table.Th>#</Table.Th>
                    <Table.Th>Parameter Name</Table.Th>
                    <Table.Th>Parameter Value</Table.Th>
                    <Table.Th className={'text-center'}>Actions</Table.Th>
                    <Table.Th></Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {data?.map((itm, idx) => {
                    return (
                      <Table.Row key={idx}>
                        <Table.Td>{idx + 1}</Table.Td>
                        <Table.Td>{itm?.['Parameter']}</Table.Td>
                        <Table.Td>{itm?.['Type']}</Table.Td>
                        <Table.Td>
                          <div className="flex items-center  w-full justify-between">
                            <EditOutlined
                              size={20}
                              className="cursor-pointer"
                              onClick={() => {
                                setEditIdx(idx);
                                setFieldDetail(() => itm);
                              }}
                            />
                            <BiTrash
                              size={15}
                              className="cursor-pointer text-red-500"
                              onClick={() => {
                                setData((prev) =>
                                  prev?.filter((_, id) => id !== idx)
                                );
                              }}
                            />
                          </div>
                        </Table.Td>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </>
          );
        }}
      </Modal>
    </div>
  );
};

export default ManualSheet;
