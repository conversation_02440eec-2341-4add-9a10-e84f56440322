import {
  BellOutlined,
  DeleteOutlined,
  EditOutlined,
  MailOutlined,
  PauseOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  SoundOutlined,
  WhatsAppOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Space,
  Switch,
  Tag,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import {
  useCreateReminderMutation,
  useDeleteReminderMutation,
  useGetRemindersQuery,
  usePauseReminderMutation,
  useUpdateReminderMutation,
} from '../../slices/reminderApiSlice';
import {
  useCreateEmailDataMutation,
  useEditEmailDataMutation,
  useGetEmailDataQuery,
} from '../../slices/sendReportApiSlice';
import { customConfirm } from '../../utils/customConfirm';
import ReminderCreationModal from './ReminderManager/ReminderCreationModal';

const { Text } = Typography;

const NotificationDefaults = ({ defaults, setDefaults }) => {
  const [openModal, setOpenModal] = useState(false);
  const [emailData, setEmailData] = useState({});
  const [formData, setFormData] = useState({ email: '', password: '' });
  const [editingReminder, setEditingReminder] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [form] = Form.useForm();

  const { data: getEmailData } = useGetEmailDataQuery();
  const [createEmailData, { isLoading: isLoadingCreate }] =
    useCreateEmailDataMutation();
  const [editEmailData, { isLoading: isLoadingEdit }] =
    useEditEmailDataMutation();

  const {
    data: reminders = [],
    isLoading: loadingReminders,
    refetch: refetchReminders,
  } = useGetRemindersQuery();
  const [createReminder, { isLoading: creatingReminder }] =
    useCreateReminderMutation();
  const [updateReminder, { isLoading: updatingReminder }] =
    useUpdateReminderMutation();
  const [deleteReminder] = useDeleteReminderMutation();

  const [pauseReminder] = usePauseReminderMutation();

  const handleToggle = (key) => {
    setDefaults((prev) => ({
      ...prev,
      notificationDefaults: {
        ...prev?.notificationDefaults,
        [key]: !prev?.notificationDefaults?.[key],
      },
    }));
  };

  const handleSubmit = async () => {
    if (!formData.email || !formData.password) {
      return toast.error('Email and Password are required');
    }

    try {
      const apiFunc = emailData?.email ? editEmailData : createEmailData;
      const payload = {
        email: formData.email,
        pass: formData.password,
        ...(emailData?.email && { password: formData.password }),
      };

      const response = await apiFunc({ data: payload }).unwrap();

      if (response?.message) {
        setEmailData({ email: response.email, pass: '' });
        setFormData({ email: '', password: '' });
        toast.success(
          emailData?.email
            ? 'Email updated successfully'
            : 'Email configured successfully'
        );
        setOpenModal(false);
      }
    } catch (error) {
      toast.error('Configuration failed. Please try again.');
    }
  };

  const handleSubmitReminder = async (values) => {
    try {
      if (editingReminder) {
        await updateReminder({
          id: editingReminder._id,
          data: values,
        }).unwrap();
        toast.success('Reminder updated successfully');
        setEditingReminder(null);
        setShowForm(false);
      } else {
        await createReminder(values).unwrap();
        toast.success('Reminder created successfully');
      }
      form.resetFields();
      refetchReminders();
      setOpenModal(false);
      setShowForm(false);
    } catch (error) {
      toast.error(error?.data?.message || 'Operation failed');
    }
  };

  const handleDeleteReminder = async (id) => {
    const confirm = await customConfirm(
      'Are you sure you want to delete this reminder?',
      'delete'
    );
    if (!confirm) return;
    try {
      await deleteReminder({ id }).unwrap();
      toast.success('Reminder deleted successfully');
      refetchReminders();
    } catch (error) {
      toast.error('Failed to delete reminder');
    }
  };

  const handleEditReminder = (reminder) => {
    setEditingReminder(reminder);
    setShowForm(true);
  };

  const handlePauseReminder = async (reminderId, status) => {
    try {
      await pauseReminder({ id: reminderId }).unwrap();
      toast.success(
        `Reminder ${status === 'inactive' ? 'active' : 'inactive'} successfully`
      );
    } catch (error) {
      toast.error('Failed to send test reminder');
    }
  };

  const handleCancelEdit = () => {
    setEditingReminder(null);
    form.resetFields();
    setShowForm(false);
  };

  useEffect(() => {
    if (getEmailData?.email) {
      setEmailData(getEmailData.email);
      setFormData((prev) => ({ ...prev, email: getEmailData.email.email }));
    }
  }, [getEmailData]);

  const isSubmitting = creatingReminder || updatingReminder;

  const getChannelIcon = (type) => {
    const icons = {
      email: <MailOutlined />,
      whatsapp: <WhatsAppOutlined />,
      push: <SoundOutlined />,
    };
    return icons[type] || <BellOutlined />;
  };

  return (
    <div className="space-y-6">
      {/* Quick Settings */}
      <Card title="Notification Settings" size="small">
        <div className="flex gap-6 p-3">
          {['Start', 'Stop', 'Pause'].map((label) => (
            <div key={label} className="flex items-center gap-2">
              <Text>{label}</Text>
              <Switch
                checked={defaults?.notificationDefaults?.[label]}
                onChange={() => handleToggle(label)}
              />
            </div>
          ))}
        </div>
      </Card>

      {/* Email Config */}
      <Card
        title={
          <>
            <MailOutlined className="mr-2" />
            Email Configuration
          </>
        }
        size="small"
        extra={
          <Button
            type="primary"
            icon={emailData?.email ? <EditOutlined /> : <PlusOutlined />}
            onClick={() => setOpenModal(true)}
            size="small"
          >
            {emailData?.email ? 'Update' : 'Configure'}
          </Button>
        }
      >
        {emailData?.email ? (
          <div className="flex items-center gap-3 p-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <MailOutlined className="text-green-600" />
            </div>
            <div>
              <Text strong>{emailData.email}</Text>
              <div className="text-xs text-gray-500">
                Ready to send notifications
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-gray-500 p-3">
            <MailOutlined className="text-2xl mb-2" />
            <div>No email configured</div>
          </div>
        )}
      </Card>

      {/* Reminders */}
      <Card
        title={
          <>
            <BellOutlined className="mr-2" />
            Reminders ({reminders.length})
          </>
        }
        size="small"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowForm(true)}
            size="small"
          >
            Create
          </Button>
        }
      >
        {loadingReminders ? (
          <div className="text-center py-8">Loading...</div>
        ) : reminders?.length > 0 ? (
          <div className="space-y-3 p-3">
            {reminders?.map((reminder) => (
              <div
                key={reminder._id}
                className="border rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Text strong>
                        {reminder.page} - {reminder.triggerEvent}
                      </Text>
                      <Tag
                        color={reminder.status === 'active' ? 'green' : 'red'}
                      >
                        {reminder.status}
                      </Tag>
                    </div>
                    <div className="flex gap-2 mb-2">
                      {reminder.statuses?.map((status) => (
                        <Tag key={status} color="blue">
                          {status}
                        </Tag>
                      ))}
                    </div>
                    <div className="flex gap-2 mb-2">
                      <Tag icon={getChannelIcon(reminder.channel)} size="small">
                        {reminder.channel}
                      </Tag>
                    </div>
                    {/* Contact Information */}
                    {reminder.contactInformation && (
                      <div className="mt-2">
                        <Text strong>Contact Information:</Text>
                        <div className="ml-2">
                          {Object.entries(reminder.contactInformation).map(
                            ([key, values]) => (
                              <div key={key} className="text-xs text-gray-600">
                                <span className="font-medium">{key}:</span>{' '}
                                {Array.isArray(values) ? values.join(', ') : ''}
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  <Space>
                    <Button
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => handleEditReminder(reminder)}
                    />
                    <Button
                      size="small"
                      icon={
                        reminder.status === 'active' ? (
                          <PauseOutlined />
                        ) : (
                          <PlayCircleOutlined />
                        )
                      }
                      onClick={() =>
                        handlePauseReminder(reminder._id, reminder.status)
                      }
                    />
                    <Button
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteReminder(reminder._id)}
                    />
                  </Space>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <BellOutlined className="text-3xl mb-2" />
            <div>No reminders yet</div>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setShowForm(true)}
              className="mt-2"
            >
              Create First Reminder
            </Button>
          </div>
        )}
      </Card>
      <ReminderCreationModal
        form={form}
        showForm={showForm}
        editingReminder={editingReminder}
        handleCancelEdit={handleCancelEdit}
        handleSubmitReminder={handleSubmitReminder}
        isSubmitting={isSubmitting}
      />

      {/* Email Config Modal */}
      <Modal
        title="Email Configuration"
        open={openModal}
        onCancel={() => setOpenModal(false)}
        onOk={handleSubmit}
        confirmLoading={isLoadingCreate || isLoadingEdit}
      >
        <div className="space-y-4">
          <div>
            <Text strong>Email Address</Text>
            <Input
              placeholder="Enter email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              prefix={<MailOutlined />}
            />
          </div>
          <div>
            <Text strong>Password</Text>
            <Input.Password
              placeholder="Enter password"
              value={formData.password}
              onChange={(e) =>
                setFormData({ ...formData, password: e.target.value })
              }
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default NotificationDefaults;
