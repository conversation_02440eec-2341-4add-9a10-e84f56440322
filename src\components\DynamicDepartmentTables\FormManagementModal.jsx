import { useEffect, useState } from 'react';

import { Button, Modal } from 'antd';
import { default as ButtonOptiwise } from '../global/components/Button';
import Input from '../global/components/Input';
import Select from '../global/components/Select';
import Table from '../global/components/Table';

import { useLazyGetFormByIdQuery } from '../../slices/createFormapiSlice';

import { FaPlusCircle } from 'react-icons/fa';
import { MdKeyboardArrowUp } from 'react-icons/md';
import { TiDelete } from 'react-icons/ti';
import { toast } from 'react-toastify';

const FormManagementModal = ({
  formType,
  open,
  setOpen,
  changeHandler,
  column,
  value,
  deletedForms,
  setDeletedForms,
}) => {
  const [initialState, setInitialState] = useState(value); //eslint-disable-line
  const [getFormById, { isLoading: isCreatingForm }] = //eslint-disable-line
    useLazyGetFormByIdQuery();

  const [forms, setForms] = useState([
    {
      formFor: '',
      formName: '',
      formData: [],
      isUsed: false,
      requiresInspection: false,
      inspectionData: [],
      panelOpen: false,
      key: Math.floor(Math.random() * 1000),
    },
  ]);
  const populateForms = async (value) => {
    let formArray = [];
    for (let i of value) {
      if (i?.length === 24) {
        const form = await getFormById({ id: i });
        formArray?.push(form?.data);
      }
    }
    setForms(formArray);
  };

  useEffect(() => {
    if (initialState) {
      populateForms(initialState);
    }
  }, [initialState]); //eslint-disable-line

  const createFormHandler = (name, value, index) => {
    setForms((prev) => [
      ...prev?.slice(0, index),
      {
        ...prev?.[index],
        [name]: value,
      },
      ...prev?.slice(index + 1),
    ]);
  };

  const addForm = () => {
    setForms((prev) => [
      ...prev,
      {
        formFor: '',
        formName: '',
        formData: [],
        isUsed: false,
        requiresInspection: false,
        inspectionData: [],
        panelOpen: false,
        key: Math.floor(Math.random() * 1000),
      },
    ]);
  };

  const closeModal = () => {
    setOpen(false);
  };

  const handleSubmit = async () => {
    const hasEmptyFormName = !!forms.find((form) => form.formName === '');
    const hasEmptyField = !!forms.find((form) => form.formData.length === 0);
    if (hasEmptyFormName) {
      toast.error('One or more form name is empty');
      return;
    }
    if (hasEmptyField) {
      toast.error('One or more forms has no fields');
      return;
    }

    let data = forms?.map((elem) => {
      if (elem?._id) {
        return {
          formFor: elem?.formFor,
          formData: elem?.formData,
          formName: elem?.formName,
          isUsed: elem?.isUsed,
          requiresInspection: elem?.requiresInspection,
          inspectionData: elem?.inspectionData,
          _id: elem?._id,
        };
      } else {
        return {
          formFor: elem?.formFor,
          formData: elem?.formData,
          formName: elem?.formName,
          isUsed: elem?.isUsed,
          requiresInspection: elem?.requiresInspection,
          inspectionData: elem?.inspectionData,
        };
      }
    });
    // const res = await createManyForms({ data });
    changeHandler(data);
    closeModal();
  };

  const togglePanel = (key, isKey) => {
    if (isKey) {
      setForms((prev) =>
        prev?.map((elem) =>
          elem?.key === key ? { ...elem, panelOpen: !elem?.panelOpen } : elem
        )
      );
    } else {
      setForms((prev) =>
        prev?.map((elem) =>
          elem?._id === key ? { ...elem, panelOpen: !elem?.panelOpen } : elem
        )
      );
    }
  };

  const deleteForm = (key, isKey) => {
    let deleted = deletedForms?.[column?.name]?.value
      ? deletedForms?.[column?.name]?.value
      : [];
    let filtered = forms;
    if (isKey) {
      filtered = filtered?.filter((elem) => elem?.key !== key);
    } else {
      filtered = filtered?.filter((elem) => elem?._id !== key);
      for (let i of forms) {
        if (i?._id === key) {
          deleted?.push(i?._id);
        }
      }
    }
    setForms(filtered);
    setDeletedForms((prev) => ({
      ...prev,
      [column?.name]: {
        type: 'form management-create',
        value: deleted,
      },
    }));
  };

  return (
    <Modal
      title={formType === 'create' ? 'Create Forms' : 'Fill Forms'}
      width={700}
      centered
      open={open}
      onOk={handleSubmit}
      onCancel={closeModal}
      footer={[
        <Button //eslint-disable-line
          type="primary"
          onClick={addForm}
          className="absolute left-[20px]"
        >
          Add
        </Button>,
        <Button key="back" onClick={closeModal}>
          Return
        </Button>,
        <Button
          key="submit"
          type="primary"
          className="bg-[#14BA6D]"
          onClick={handleSubmit}
        >
          Submit
        </Button>,
      ]}
    >
      <div className="mb-4 h-[25rem] overflow-y-scroll">
        <div className="w-full">
          {forms?.map((elem, idx) => {
            return (
              <div className="w-full mt-4" key={idx}>
                <div
                  className="group flex items-center gap-2 py-2 px-4 bg-slate-100 border-[1px] border-solid border-slate-300 w-full rounded text-left data-[open]:rounded-b-none"
                  onClick={() =>
                    togglePanel(
                      elem?.key ? elem?.key : elem?._id,
                      elem?.key ? true : false
                    )
                  }
                >
                  <MdKeyboardArrowUp
                    className={`${elem?.panelOpen ? 'rotate-180' : ''} transition duration-200 text-2xl`}
                  />
                  <p className="font-semibold">
                    {elem?.formName?.length !== 0
                      ? elem?.formName
                      : `Form ${idx + 1}`}
                  </p>
                  <TiDelete
                    className="ml-auto text-red-500 text-xl"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteForm(
                        elem?.key ? elem?.key : elem?._id,
                        elem?.key ? true : false
                      );
                    }}
                  />
                </div>
                {elem?.panelOpen && (
                  <div
                    className={`origin-top transition duration-200 ease-out p-2 ${elem?.panelOpen ? 'border-l-[1px] border-r-[1px] border-b-[1px] border-solid border-slate-300 rounded-b' : '-translate-y-6 opacity-0'}`}
                  >
                    <CreateForm
                      changeHandler={createFormHandler}
                      index={idx}
                      forms={forms}
                      setForms={setForms}
                      keyVal={elem?.key}
                    />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </Modal>
  );
};

const CreateForm = ({ changeHandler, index, forms, setForms, keyVal }) => {
  const [fieldName, setFieldName] = useState('');
  const [fieldType, setFieldType] = useState('');
  const [fieldOptions, setFieldOptions] = useState([]);

  const ctg = [
    'Date',
    'Range',
    'Range Threshold',
    'Check',
    'MultiCheckbox',
    'String',
  ];

  const handleAdd = () => {
    setForms((prev) =>
      prev?.map((elem) =>
        elem?.key === keyVal
          ? {
              ...elem,
              formData: [
                ...elem?.formData,
                { fieldName, fieldType, fieldOptions },
              ],
            }
          : elem
      )
    );
    setFieldName('');
    setFieldType('');
  };

  const handleRemove = (idx) => {
    setForms((prev) =>
      prev?.map((elem) =>
        elem?.key === keyVal
          ? {
              ...elem,
              formData: elem?.formData?.filter((elem, index) => index !== idx),
            }
          : elem
      )
    );
  };

  const addOption = () => {
    setFieldOptions((prev) => [
      ...prev,
      {
        label: '',
        value: '',
      },
    ]);
  };

  return (
    <div>
      <div className="flex items-center gap-2 w-full mb-3">
        <div className="w-full">
          <h6 className="ml-[2px] mb-[5px]">Form Name:</h6>
          <Input
            value={forms?.[index]?.formName}
            placeholder="Enter Form Name"
            className="w-full"
            type="text"
            name="formName"
            onChange={(e) => {
              changeHandler(e.target.name, e.target.value, index);
            }}
          />
        </div>
        <div className="w-full">
          <h6 className="ml-[2px] mb-[5px]">Form Type</h6>
          <Select
            options={[
              {
                label: 'QC',
                value: 'QC',
              },
              {
                label: 'Inspection',
                value: 'Inspection',
              },
              {
                label: 'Department',
                value: 'Department',
              },
            ]}
            className="w-full"
            value={forms?.[index]?.formFor}
            name="formFor"
            onChange={(e) => {
              changeHandler(e.target.name, e.target.value, index);
            }}
          />
        </div>
      </div>
      <div className="flex items-center gap-2 justify-between mb-[5px]">
        <h6 className="ml-[2px] mb-[5px]">Form Fields:</h6>
        <ButtonOptiwise className="!h-[1.75rem]" onClick={handleAdd}>
          Add
        </ButtonOptiwise>
      </div>
      <div className="flex items-center gap-2 mb-3">
        <Input
          className="w-full"
          placeholder="Enter field name"
          value={fieldName}
          onChange={(e) => setFieldName(e.target.value)}
        />
        <Select
          options={ctg.map((option) => ({
            name: option,
            value: option,
          }))}
          className="w-full"
          placeholder="Select field type"
          value={fieldType}
          onChange={(e) => {
            setFieldType(e.target.value);
            if (e.target.value === 'MultiCheckbox') {
              setFieldOptions([
                {
                  label: '',
                  value: '',
                },
              ]);
            }
          }}
        />
      </div>
      {fieldType === 'MultiCheckbox' && (
        <div>
          <div className="flex items-center gap-2">
            <h6 className="ml-[2px] mb-[5px]">Multi Checkbox options:</h6>
            <FaPlusCircle
              className="text-lg mb-[5px] text-blue-500 cursor-pointer"
              onClick={addOption}
            />
          </div>
          {fieldOptions?.map((elem, idx) => {
            return (
              <div className="flex items-center gap-2" key={idx}>
                <Input
                  value={fieldOptions?.[idx]?.label}
                  placeholder="Enter Field Label"
                  onChange={(e) => {
                    setFieldOptions((prev) => [
                      ...prev?.slice(0, idx),
                      {
                        ...prev?.[idx],
                        label: e.target.value,
                      },
                      ...prev?.slice(idx + 1),
                    ]);
                  }}
                />
                <Input
                  value={fieldOptions?.[idx]?.value}
                  placeholder="Enter Field Value"
                  onChange={(e) => {
                    setFieldOptions((prev) => [
                      ...prev?.slice(0, idx),
                      {
                        ...prev?.[idx],
                        value: e.target.value,
                      },
                      ...prev?.slice(idx + 1),
                    ]);
                  }}
                />
              </div>
            );
          })}
        </div>
      )}
      <Table className="mt-2">
        <Table.Head>
          <Table.Th>Label</Table.Th>
          <Table.Th>Type</Table.Th>
          <Table.Th></Table.Th>
        </Table.Head>
        <Table.Body>
          {forms?.[index]?.formData?.map((elem, idx) => {
            return (
              <Table.Row key={idx}>
                <Table.Td>{elem?.fieldName}</Table.Td>
                <Table.Td>{elem?.fieldType}</Table.Td>
                <Table.Td
                  className="!text-red-500"
                  onClick={() => handleRemove(idx)}
                >
                  Remove
                </Table.Td>
              </Table.Row>
            );
          })}
        </Table.Body>
      </Table>
      {forms?.[index]?.formData?.length === 0 && (
        <div className="h-[6rem] w-full flex items-center justify-center flex-col">
          {/* <RxLinkNone1 className="text-lg text-slate-200" /> */}
          <p className="text-base text-slate-400">No Data</p>
        </div>
      )}
    </div>
  );
};

export default FormManagementModal;
