import { getLocalDateTime } from '../../helperFunction';
import Table from '../global/components/Table';

const LogsTable = ({ logs, isMobile }) => {
  return (
    <div className="bg-white rounded-lg overflow-scroll">
      <Table className="w-full">
        <Table.Head>
          <Table.Row>
            {/* <Table.Th>#</Table.Th> */}
            <Table.Th>Date and Time</Table.Th>
            <Table.Th>Name</Table.Th>
            {/* <Table.Th>#</Table.Th> */}
            {!isMobile && <Table.Th>Role</Table.Th>}
            {/* <Table.Th>#</Table.Th> */}
            {!isMobile && <Table.Th>Action</Table.Th>}
            <Table.Th>Activity</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {logs.map((log) => (
            <Table.Row key={log._id}>
              {/* <Table.Td>{logs.indexOf(log) + pageVisited}</Table.Td> */}
              {/* <Table.Th>#</Table.Th> */}
              <Table.Td>{getLocalDateTime(log.dateAndTime)}</Table.Td>

              <Table.Td
                className={'md:!min-w-[24rem]'}
                title={
                  log?.operator?.employeeId ||
                  log?.operatorData?.employeeId ||
                  log?.user?.email ||
                  log?.userData?.email
                }
              >
                {log?.operator?.name ||
                  log?.operatorData?.name ||
                  log?.user?.name ||
                  log?.userData?.name ||
                  '-'}
              </Table.Td>
              {/* <Table.Th>#</Table.Th> */}
              {!isMobile && (
                <Table.Td>
                  {log?.operatorData?.role ||
                    log?.user?.role ||
                    log?.userData?.role ||
                    'user'}
                </Table.Td>
              )}
              {/* <Table.Th>#</Table.Th> */}
              {!isMobile && <Table.Td>{log.action || '-'}</Table.Td>}
              <Table.Td>{log.activity || '-'}</Table.Td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
};

export default LogsTable;
