import { toast } from 'react-toastify';
import { usePutCreateInputOutsourceDataMutation } from '../../slices/createInputApiSlice';
import { useCreateSkeletonMutation } from '../../slices/outsourceJobApiSlice';
import Button from '../global/components/Button';

const StartOutsource = ({
  itemUsed,
  outQunatity,
  wo,
  // dateTime,
  submitHandler,
  isPartialOutsource = false,
  updateCiInfo = {},
  ci,
  handleClose,
}) => {
  const [createSkeleton] = useCreateSkeletonMutation();

  const [putCreateInputOutsourceData] =
    usePutCreateInputOutsourceDataMutation();

  const outsourceHandlerV2 = async () => {
    let data = {
      workOrderId: wo,
      bomId: itemUsed?.topBomId || itemUsed?.bom?.bomId?._id,
      fromCu: true,
      cuProject: '',
      productListId: itemUsed._id,
      itemId:
        itemUsed?.product?._id || itemUsed?.part?._id || itemUsed?.itemId?._id,
      rawMaterials: [],
      itemType: itemUsed?.itemType
        ? itemUsed?.itemType
        : itemUsed?.part
          ? 'Part'
          : 'Product',
      requiredStock: outQunatity,
      itemName:
        itemUsed?.part?.name ||
        itemUsed?.manualEntry ||
        itemUsed?.partVariant?.name ||
        itemUsed?.product?.name ||
        itemUsed?.productVariant?.name ||
        itemUsed?.itemId?.name,
      selectedVendor: '',
    };

    try {
      // const res = await createOutsourceJob({ data }).unwrap();

      if (isPartialOutsource) {
        await createSkeleton({ data }).unwrap();
        const resp = await putCreateInputOutsourceData({
          id: ci,
          data: updateCiInfo,
        });
        if (resp) {
          toast.success('OutSource updated successfully');
          handleClose();
        }
      } else {
        submitHandler(data);
        toast.success('OutSource updated successfully');
      }
    } catch (error) {
      toast.error(error);
    }
  };

  return (
    <div className="mt-2 text-xs md:text-[15px]">
      <div className="grid grid-cols-2 gap-3"></div>
      <Button
        onClick={outsourceHandlerV2}
        // disabled={
        //   selectedData?.flow?.processCategory === 'Inhouse' && !selectedMachine
        // }
        className={'!h-10 !text-sm w-1/2 mx-auto mt-10 md:mt-5'}
      >
        Outsource
      </Button>
    </div>
  );
};

export default StartOutsource;
