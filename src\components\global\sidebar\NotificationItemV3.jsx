import {
  DeleteOutlined,
  EllipsisOutlined,
  ReadOutlined,
} from '@ant-design/icons';
import { Avatar, Dropdown, Tag, Typography } from 'antd';
import {
  useDeleteNewNotificationMutation,
  useToggleMarkAsReadMutation,
} from '../../../slices/newNotificationApiSlice';

const { Text, Paragraph } = Typography;

// Define a function to get a consistent color based on the first letter
const getAvatarColorByLetter = (letter) => {
  // Normalize letter to uppercase for consistency
  const uppercaseLetter = letter.toUpperCase();

  // Define colors for each letter of the alphabet
  const colorMap = {
    A: '#f56a00', // Orange
    B: '#7265e6', // Purple
    C: '#ffbf00', // Amber
    D: '#00a2ae', // Cyan
    E: '#f56a00', // Orange
    F: '#7265e6', // Purple
    G: '#ffbf00', // <PERSON>
    H: '#00a2ae', // Cyan
    I: '#f56a00', // Orange
    J: '#7265e6', // Purple
    K: '#ffbf00', // Amber
    L: '#00a2ae', // Cyan
    M: '#f56a00', // Orange
    N: '#7265e6', // Purple
    O: '#ffbf00', // Amber
    P: '#00a2ae', // Cyan
    Q: '#f56a00', // Orange
    R: '#7265e6', // Purple
    S: '#ffbf00', // Amber
    T: '#00a2ae', // Cyan
    U: '#f56a00', // Orange
    V: '#7265e6', // Purple
    W: '#ffbf00', // Amber
    X: '#00a2ae', // Cyan
    Y: '#f56a00', // Orange
    Z: '#7265e6', // Purple
  };

  // Return the color for the letter or a default color if not found
  return colorMap[uppercaseLetter] || '#1890ff'; // Default blue
};

const NotificationItemV3 = ({ notif, user }) => {
  const [deleteNewNotification] = useDeleteNewNotificationMutation();
  const [toggleMarkAsRead] = useToggleMarkAsReadMutation();

  function getDaysBetweenDates(creationDate) {
    const date1 = new Date(creationDate);
    const date2 = new Date();

    // Get the time difference in milliseconds
    const differenceInTime = date2.getTime() - date1.getTime();

    // Convert milliseconds to days
    const millisecondsInDay = 1000 * 60 * 60 * 24;
    const daysDifference = Math.floor(differenceInTime / millisecondsInDay);

    return daysDifference;
  }

  let msg = notif?.description;
  let createdDate = new Date(notif?.createdAt);
  let daysDifference = getDaysBetweenDates(notif?.createdAt);

  let dayArray = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];

  let dateToShow;
  if (daysDifference < 7) {
    let day = dayArray[createdDate?.getDay()];
    let time = createdDate?.toLocaleTimeString('en-US');

    dateToShow = `${day}, ${time}`;
  } else {
    dateToShow = createdDate?.toDateString();
  }

  const isRead = notif?.read?.includes(user?._id);

  // Get first letter for the avatar
  const firstLetter = notif?.sender?.name?.charAt(0) || 'S';
  // Get the color based on the first letter
  const avatarColor = getAvatarColorByLetter(firstLetter);

  const menuItems = [
    {
      key: 'delete',
      icon: <DeleteOutlined className="text-red-500" />,
      label: 'Clear',
      onClick: () => deleteNewNotification({ id: notif?._id }),
    },
    {
      key: 'toggleRead',
      icon: <ReadOutlined className="text-green-500" />,
      label: `Mark as ${isRead ? 'Unread' : 'Read'}`,
      onClick: () => toggleMarkAsRead({ id: notif?._id }),
    },
  ];

  return (
    <div className="flex items-start p-4 border-b border-gray-100 hover:bg-gray-50 relative">
      <Avatar
        className="mr-3 flex-shrink-0"
        style={{ backgroundColor: avatarColor, color: '#ffffff' }}
        size="large"
      >
        {firstLetter.toUpperCase()}
      </Avatar>

      <div className="flex-grow min-w-0">
        <Paragraph className="mb-1 text-sm text-gray-800">{msg}</Paragraph>
        {notif?.tags?.length > 0 && (
          <div className="mb-1">
            {notif.tags.map((tag, index) => (
              <Tag key={index} size="small" color="blue" className="capitalize">
                {tag}
              </Tag>
            ))}
          </div>
        )}
        <Text className="text-xs text-gray-500">{dateToShow}</Text>
      </div>

      <Dropdown
        menu={{ items: menuItems }}
        placement="bottomRight"
        trigger={['click']}
        className="ml-2 self-start flex-shrink-0"
      >
        <button className="p-1 text-gray-400 hover:text-gray-600 focus:outline-none rounded-full hover:bg-gray-100">
          <EllipsisOutlined className="text-lg" />
        </button>
      </Dropdown>
    </div>
  );
};

export default NotificationItemV3;
