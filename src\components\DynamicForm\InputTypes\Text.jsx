import { ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { DebounceInput } from 'react-debounce-input';
import useFieldsAndTitle from '../../../hooks/useFieldsAndTitle';

const Text = ({ data, state, horIdx, verIdx, type, isDisabled }) => {
  const [horizontalFields, verticalFields, title] = useFieldsAndTitle(data);
  const [error, setError] = useState(false);
  const { inputData, handleChange, renderState } = state;

  const handleLocalChange = (e) => {
    const regex = new RegExp(/^[A-Za-z ]+$/);
    if (e.target.value.length > 0) {
      if (regex.test(e.target.value)) {
        setError(false);
      } else {
        setError(true);
      }
    } else {
      setError(false);
    }
  };

  return (
    <>
      {renderState && (
        <div
          className={`flex border rounded items-center text-gray-700 leading-nomal w-full ${
            error && 'border-red-500'
          } ${
            verticalFields.length > 1 && verticalFields.length - 1 !== verIdx
              ? ' mb-2'
              : ''
          }`}
        >
          <DebounceInput
            debounceTimeout={500}
            key={data.Parameter + title[horIdx] + verIdx.toString()}
            type="text"
            className={`appearance-none rounded py-0.5 px-3 text-sm text-gray-700 leading-nomal focus:outline-none focus:ring-2 ${
              data.Units !== '' ? ' w-4/5' : ' w-full border'
            } `}
            placeholder={type}
            pattern="^[A-Za-z ]+$"
            title="No number or special character allowed"
            name={
              horizontalFields.length > 1 && verticalFields.length > 1
                ? `${data.Parameter}-${title[horIdx]}${verIdx.toString()}-hv`
                : horizontalFields.length > 1
                  ? `${data.Parameter}-${title[horIdx]}-h`
                  : verticalFields.length > 1
                    ? `${data.Parameter}-${verIdx.toString()}-v`
                    : data.Parameter
            }
            value={
              (horizontalFields.length > 1 && verticalFields.length > 1
                ? inputData?.[data.Parameter]?.value?.[
                    title[horIdx] + verIdx.toString()
                  ]
                : horizontalFields?.length > 1
                  ? inputData?.[data.Parameter]?.value?.[title[horIdx]]
                  : verticalFields?.length > 1
                    ? inputData?.[data.Parameter]?.value?.[verIdx]
                    : inputData?.[data.Parameter]?.value) || ''
            }
            onChange={(e) => {
              handleLocalChange(e);
              handleChange(e);
            }}
            disabled={isDisabled}
          />
          {error ? (
            <span
              title="No number or special character allowed"
              className="mx-auto"
            >
              <ExclamationCircleIcon className="h-6 w-6 text-red-600 mx-2" />
            </span>
          ) : (
            data.Units && (
              <span className="text-md w-1/5 h-fit font-normal text-sm text-center">
                {data.Units}
              </span>
            )
          )}
        </div>
      )}
    </>
  );
};

export default Text;
