import Input from 'antd/es/input/Input';
import { useCallback, useEffect, useState } from 'react';
import { useReactMediaRecorder } from 'react-media-recorder';
import { useMediaQuery } from 'react-responsive';
import { mobileWidth, tabletWidth } from '../../../helperFunction';
import Modal from '../../global/components/Modal';

const RecordingPopup = ({
  setFormdata,
  setShowRecording,
  isUpload = true,
  isQC = true,
}) => {
  const [isActive, setIsActive] = useState(false);
  const [recordedBlob, setRecordedBlob] = useState(null);
  const [fileName, setFileName] = useState('');
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });

  const {
    status,
    startRecording,
    stopRecording,
    pauseRecording,
    mediaBlobUrl,
  } = useReactMediaRecorder({
    video: false,
    audio: true,
    echoCancellation: true,
    onData: (data) => {
      setRecordedBlob(data);
    },
  });
  const checkPermissions = async () => {
    const status = await navigator.permissions.query({ name: 'microphone' });
    if (status.state !== 'granted') {
      alert('Please allow microphone access');
    }
  };

  useEffect(() => {
    checkPermissions();
  }, []);

  const blobToString = useCallback((blobUrl) => {
    fetch(blobUrl, { mode: 'cors' })
      .then((response) => {
        if (!response.ok) {
          throw new Error('Failed to fetch blob. Check CORS policies.');
        }
        return response.blob();
      })
      .then((blob) => {
        const fr = new FileReader();
        fr.readAsDataURL(blob);
        fr.onload = () => {
          const url = fr.result;
          setRecordedBlob({
            name: 'audioFile',
            type: 'audio',
            data: url,
          });
        };
      })
      .catch((error) => {
        console.error('Error fetching or reading the Blob:', error); // eslint-disable-line
      });
  }, []);
  useEffect(() => {
    if (mediaBlobUrl) {
      blobToString(mediaBlobUrl);
    }
  }, [blobToString, mediaBlobUrl]);
  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();
      isQC
        ? setFormdata((prev) => ({ ...prev, audioFile: { ...recordedBlob } }))
        : setFormdata((prev) => [
            ...(prev || []),
            { ...recordedBlob, name: fileName },
          ]);
      setShowRecording(false);
    },
    [recordedBlob, fileName, isQC, setFormdata, setShowRecording]
  );

  const handleUpload = (e) => {
    const file = e.target.files[0];
    console.log(file); // eslint-disable-line
  };

  return (
    <Modal
      isMobile={isMobile}
      isTablet={isTablet}
      title="Attach audio file"
      description="Upload or record media file"
      onCloseModal={() => setShowRecording(false)}
      onSubmit={handleSubmit}
      canSubmit={false}
    >
      {() => (
        <div>
          {isUpload && (
            <div className="flex justify-around">
              <h2 className="text-xl mt-2">Upload Audio :</h2>
              <input
                type="file"
                accept=".wav,.mp3,audio/*"
                onChange={handleUpload}
                className="mb-4 p-2 border border-gray-400"
              />
            </div>
          )}

          {!isQC && (
            <Input
              className="mb-2"
              placeholder="Enter file name...."
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
            />
          )}

          <div className="border-2 rounded-md border-black w-700 h-350">
            <div className="w-full my-2 flex">
              <audio src={mediaBlobUrl} controls className="w-full px-2" />
              <div className="h-70 w-[30%] flex items-center">
                <h4 className="ml-10 text-md">
                  {status === 'idle'
                    ? ''
                    : status === 'recording'
                      ? 'Recording...'
                      : status}
                </h4>
              </div>
            </div>
            <div className="col-md-6 col-md-offset-3 bg-black/80 text-white py-2">
              <div className="flex justify-around">
                <button
                  type="button"
                  className="px-8 py-2 border-none ml-15 text-white bg-green-600 hover:bg-green-700 rounded font-bold transition duration-300 transform"
                  onClick={() => {
                    if (!isActive) {
                      startRecording();
                    } else {
                      pauseRecording();
                    }
                    setIsActive(!isActive);
                  }}
                >
                  {isActive ? 'Pause' : 'Start'}
                </button>
                <button
                  type="button"
                  className="px-8 py-2 border-none bg-red-600 ml-15 text-white hover:bg-red-700 rounded font-bold transition duration-300 transform"
                  onClick={() => {
                    stopRecording();
                    setIsActive(false);
                  }}
                >
                  Stop
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default RecordingPopup;
