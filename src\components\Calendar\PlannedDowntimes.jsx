import { useState } from 'react';
import { toast } from 'react-toastify';
import { ReactComponent as Doc } from '../../assets/svgs/documentprevious.svg';
import { useUpdateShiftsAndBreaksMutation } from '../../slices/defaultsApiSlice';
import { MAX_CHAR_ALLOWED } from '../../utils/Constant';
import { getstrLen } from '../../utils/Getstrlen';
import { customConfirm } from '../../utils/customConfirm';
import AddButton from '../AddButton';
import { InfoTooltip } from '../global/components/InfoTooltip';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
// import NewInput from '../global/components/NewInput';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
const initialPlannedDowntime = {
  name: '',
  type: '',
  quantity: '',
  interval: '',
  time: '',
  duration: '',
  priority: '',
  threshold: '',
};

const plannedDownTimeTypes = ['time', 'quantity', 'interval'];

const priorityLevels = ['before', 'skip', 'after'];

const PlannedDowntime = ({ defaults, setDefaults }) => {
  const [pdData, setPdData] = useState(initialPlannedDowntime);
  const [defaultEditedData, setDefaultEditedData] = useState([]);

  const [updateShiftsAndBreaks, updateSBData] =
    useUpdateShiftsAndBreaksMutation();
  const { isLoading } = updateSBData;
  const addHandler = async () => {
    if (getstrLen(pdData?.name) > MAX_CHAR_ALLOWED) {
      toast.error(`Name cannot exceeds ${MAX_CHAR_ALLOWED} characters`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Name len error',
      });
      return;
    }
    if (!pdData?.name) {
      toast.error('Please add Name', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Please add Name',
      });
      return;
    }
    if (!pdData?.type) {
      toast.error(`Please select type`, {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Please add type',
      });
      return;
    }

    if (
      (pdData?.type === 'time' && !pdData?.time) ||
      (pdData?.type === 'quantity' && !pdData?.quantity) ||
      (pdData?.type === 'interval' && !pdData?.interval)
    ) {
      toast.error(`Please add ${pdData?.type}`, {
        theme: 'colored',
        position: 'top-right',
        toastId: `Please add ${pdData?.type}`,
      });
      return;
    }

    if (!pdData?.duration) {
      toast.error('Please add duration', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Please add duration',
      });
      return;
    }
    if (!pdData?.threshold) {
      toast.error('Please add threshold', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Please add threshold',
      });
      return;
    }
    if (pdData?.type !== 'time' && !pdData?.priority) {
      toast.error('Please select priority', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'Please add priority',
      });
      return;
    }
    setDefaults((prev) => ({
      ...prev,
      plannedDowntimes: [...(prev?.plannedDowntimes || []), pdData],
    }));
    setPdData(initialPlannedDowntime);
    setModalOpen(!modalOpen);
    const res = await updateShiftsAndBreaks({
      plannedDowntimes: [...(defaults?.plannedDowntimes || []), pdData],
    }).unwrap();
    if (res) {
      setPdData(initialPlannedDowntime);
      toast.success('Planned Downtime added successfully', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'added',
      });
    }
  };

  const editHandler = async () => {
    const eData = {
      id: defaultEditedData[0]?._id,
      name: defaultEditedData[0]?.name,
      type: defaultEditedData[0]?.type,
      quantity: editdataDefault,
      interval: interValData,
      time: defaultEditedData[0]?.time,
      duration: durationData,
      priority: defaultEditedData[0]?.priority,
      threshold: defaultEditedData[0]?.threshold,
      isUsed: true,
    };
    const res = await updateShiftsAndBreaks({
      plannedDowntimes: defaults?.plannedDowntimes?.map((item) => {
        return item?._id === eData?.id ? eData : item;
      }),
    }).unwrap();
    if (res) {
      setEditModalOpen(!editModalOpen);
      toast.success('Planned Downtime edited successfully', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'added',
      });
    }
  };

  const removeHandler = async (id) => {
    if (
      !(await customConfirm(
        'Are you sure you want to delete this entry?',
        'delete'
      ))
    ) {
      return;
    }

    const res = await updateShiftsAndBreaks({
      plannedDowntimes: [
        ...(defaults?.plannedDowntimes?.filter((item) => item._id !== id) ||
          []),
      ],
    }).unwrap();
    if (res) {
      setPdData(initialPlannedDowntime);
      toast.success('Entry deleted successfully', {
        theme: 'colored',
        position: 'top-right',
        toastId: 'added',
      });
    }
  };

  const inputHandler = (e) => {
    const { name: eName, value: eValue, type } = e.target;

    if (eName === 'type') {
      setPdData((prev) => ({
        ...prev,
        [eName]: type === 'number' ? +eValue : eValue,
        quantity: '',
        interval: '',
        time: '',
        priority: '',
      }));
    } else {
      setPdData((prev) => ({
        ...prev,
        [eName]: type === 'number' ? +eValue : eValue,
      }));
    }
  };

  const handleEditQualityDefault = (e) => {
    setDefaultToEditedData(e.target.value);
  };

  const handleInterval = (e) => {
    setIntervalData(e.target.value);
  };

  const handleEditDurationDefault = (e) => {
    setDurationData(e.target.value);
  };

  // const rowInputHandler = (e) => {
  //   setDefaults((prev) => ({
  //     ...prev,
  //     plannedDowntimes: prev?.plannedDowntimes?.map((data, idx) => {
  //       if (idx === tIdx) {
  //         return { ...data, [e.target.name]: e.target.value };
  //       }
  //       return data;
  //     }),
  //   }));
  // };

  const [editdataDefault, setDefaultToEditedData] = useState();
  const [durationData, setDurationData] = useState();
  const [interValData, setIntervalData] = useState();

  const handleEdit = (id) => {
    const editedData = defaults?.plannedDowntimes?.filter(
      (item) => item._id === id
    );
    setDefaultToEditedData(editedData[0]?.quantity);
    setDurationData(editedData[0]?.duration);
    setIntervalData(editedData[0]?.interval);
    setDefaultEditedData(editedData);
    setEditModalOpen((prev) => !prev);
  };

  const [modalOpen, setModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);

  return (
    <div className="w-full py-0">
      <div className="flex flex-row justify-end">
        <AddButton modalOpen={modalOpen} setModalOpen={setModalOpen} />
      </div>

      <>
        <Table className="mt-5">
          <Table.Head>
            <Table.Row>
              <Table.Th>Name</Table.Th>
              <Table.Th>Type</Table.Th>
              <Table.Th>Quantity</Table.Th>
              <Table.Th>Interval</Table.Th>
              <Table.Th>Time</Table.Th>
              <Table.Th>Duration (mins)</Table.Th>
              <Table.Th>Threshold</Table.Th>
              <Table.Th>Priority</Table.Th>
              <Table.Th></Table.Th>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            {defaults?.plannedDowntimes?.map((item, tIdx) => (
              <Table.Row key={tIdx}>
                <Table.Td>{item.name}</Table.Td>
                <Table.Td>{item.type || '-'}</Table.Td>
                <Table.Td>{item.quantity || '-'}</Table.Td>
                <Table.Td>{item.interval || '-'}</Table.Td>
                <Table.Td>{item.time || '-'}</Table.Td>
                <Table.Td>{item.duration || '-'}</Table.Td>
                <Table.Td>{item.threshold || '-'}</Table.Td>
                <Table.Td>{item.priority || '-'}</Table.Td>
                <Table.Options
                  onEdit={() => handleEdit(item._id)}
                  onDelete={() => removeHandler(item._id)}
                />
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </>

      {modalOpen && (
        <Modal
          title={'Planned Downtime'}
          svg={<Doc className="h-8 w-8" />}
          onCloseModal={setModalOpen}
          onSubmit={addHandler}
          btnIsLoading={isLoading}
        >
          {() => {
            return (
              <>
                <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
                  <div className="flex flex-col my-5 w-full">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Name{' '}
                      <span className="text-xl text-red-500 -mt-1 -ml-1">
                        *
                      </span>
                    </label>
                    <Input
                      type="text"
                      placeholder="Break name"
                      name="name"
                      value={pdData?.name}
                      onChange={inputHandler}
                    />
                  </div>
                  <div className="flex flex-col my-5 w-full">
                    <div className="flex flow-row items-center gap-x-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Type{' '}
                        <span className="text-xl text-red-500 -mt-1 -ml-1">
                          *
                        </span>
                      </label>
                      <InfoTooltip
                        position="right"
                        width="400px"
                        id="downtimeType"
                        isHtml={true}
                        content=" Use this dropdown to select the type of planned downtime:<br />
              <b>Time:</b> Choose for fixed-schedule breaks.<br />
              <b>Count:</b> Select for breaks after a specific production count.<br />
              <b>Interval:</b> Opt for breakdowns after every specific time interval.<br />
              This selection allows precise planning and optimization of your production schedules."
                      ></InfoTooltip>
                    </div>
                    <Select
                      placeholder="Type"
                      name="type"
                      value={pdData?.type}
                      onChange={inputHandler}
                      options={plannedDownTimeTypes?.map((type) => ({
                        name: type,
                        value: type,
                      }))}
                    />
                  </div>
                  <div className="flex flex-col my-5 w-full">
                    <label className="mb-1 font-semibold text-[#667085]">
                      {pdData?.type === 'time'
                        ? 'Time'
                        : pdData?.type === 'quantity'
                          ? 'Quantity'
                          : pdData?.type === 'interval'
                            ? 'Interval'
                            : 'Select Type'}{' '}
                      <span className="text-xl text-red-500 -mt-1 -ml-1">
                        *
                      </span>
                    </label>
                    {pdData?.type === 'time' ? (
                      <Input
                        type="time"
                        placeholder="Break time"
                        name="time"
                        value={pdData?.time}
                        onChange={inputHandler}
                        className={' py-0'}
                      />
                    ) : pdData?.type === 'quantity' ? (
                      <Input
                        type="number"
                        placeholder="Quantity"
                        name="quantity"
                        min={0}
                        value={pdData?.quantity}
                        onChange={inputHandler}
                      />
                    ) : pdData?.type === 'interval' ? (
                      <Input
                        type="number"
                        placeholder="Interval"
                        name="interval"
                        min={0}
                        value={pdData?.interval}
                        onChange={inputHandler}
                      />
                    ) : (
                      <span
                        className={`border text-left text-gray-400 outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full`}
                      >
                        Please select Type
                      </span>
                    )}
                  </div>
                  <div className="flex flex-col my-5 w-full">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Duration{' '}
                      <span className="text-xl text-red-500 -mt-1 -ml-1">
                        *
                      </span>
                    </label>
                    <Input
                      type="number"
                      placeholder="Duration in mins"
                      name="duration"
                      min={0}
                      value={pdData?.duration}
                      onChange={inputHandler}
                    />
                  </div>
                  <div className="flex flex-col my-5 w-full">
                    <div className="flex flow-row items-center gap-x-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Threshold{' '}
                        <span className="text-xl text-red-500 -mt-1 -ml-1">
                          *
                        </span>
                      </label>
                      <InfoTooltip
                        position="right"
                        id="threshold"
                        className="mb-1"
                      >
                        This setting defines the allowed threshold for planned
                        breaks before they are categorized as unplanned downtime
                      </InfoTooltip>
                    </div>
                    <Input
                      type="number"
                      placeholder="Threshold"
                      name="threshold"
                      min={0}
                      value={pdData?.threshold}
                      onChange={inputHandler}
                    />
                  </div>
                  {pdData?.type !== 'time' ? (
                    <div className="flex flex-col my-5 w-full">
                      <div className="flex flow-row items-center gap-x-2">
                        <label className="mb-1 font-semibold text-[#667085]">
                          Priority{' '}
                          <span className="text-xl text-red-500 -mt-1 -ml-1">
                            *
                          </span>
                        </label>
                        <InfoTooltip
                          className="mb-1"
                          isHtml={true}
                          position="right"
                          id="priority"
                          width="400px"
                          content="
            Use this dropdown to determine how to handle planned breaks in case of clashes with scheduled breaks:<br />
            <b>Skip:</b> Select this option to skip the break that clashes with the scheduled break. <br />
            <b>Before:</b> Choose this if the break should take priority over the scheduled break. <br />
            <b>After:</b> Opt for this if the break should start after the scheduled break. <br />
          "
                        ></InfoTooltip>
                      </div>
                      <Select
                        placeholder="Priority"
                        name="priority"
                        value={pdData?.priority}
                        onChange={inputHandler}
                        options={priorityLevels?.map((level) => ({
                          name: level,
                          value: level,
                        }))}
                        disabled={pdData?.type === 'time'}
                      />
                    </div>
                  ) : null}
                </div>
              </>
            );
          }}
        </Modal>
      )}
      {editModalOpen && (
        <Modal
          title={'Edit Planned Downtime '}
          svg={<Doc className="h-8 w-8" />}
          onCloseModal={setEditModalOpen}
          onSubmit={editHandler}
          btnIsLoading={isLoading}
        >
          {() => {
            return (
              <>
                <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
                  <div className="flex flex-col my-5 w-full">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Name{' '}
                    </label>
                    <div>{defaultEditedData[0]?.name}</div>
                  </div>
                  <div className="flex flex-col my-5 w-full">
                    <div className="flex flow-row items-center gap-x-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Type
                      </label>
                      <InfoTooltip
                        position="right"
                        width="400px"
                        id="downtimeType"
                        isHtml={true}
                        content=" Use this dropdown to select the type of planned downtime:<br />
              <b>Time:</b> Choose for fixed-schedule breaks.<br />
              <b>Count:</b> Select for breaks after a specific production count.<br />
              <b>Interval:</b> Opt for breakdowns after every specific time interval.<br />
              This selection allows precise planning and optimization of your production schedules."
                      ></InfoTooltip>
                    </div>
                    <div>{defaultEditedData[0]?.type}</div>
                  </div>
                  <div className="flex flex-col my-5 w-full">
                    <label className="mb-1 font-semibold text-[#667085]">
                      {defaultEditedData[0]?.type === 'time'
                        ? 'Time'
                        : defaultEditedData[0]?.type === 'quantity'
                          ? 'Quantity'
                          : defaultEditedData[0]?.type === 'interval'
                            ? 'Interval'
                            : 'Select Type'}
                    </label>
                    {defaultEditedData[0]?.type === 'time' ? (
                      <Input
                        type="time"
                        placeholder="Break time"
                        name="time"
                        value={defaultEditedData[0]?.time}
                        className={' py-0'}
                      />
                    ) : defaultEditedData[0]?.type === 'quantity' ? (
                      <Input
                        type="number"
                        placeholder="Quantity"
                        name="quantity"
                        min={0}
                        value={editdataDefault}
                        onChange={handleEditQualityDefault}
                      />
                    ) : defaultEditedData[0]?.type === 'interval' ? (
                      <Input
                        type="number"
                        placeholder="Interval"
                        name="interval"
                        min={0}
                        value={interValData}
                        onChange={handleInterval}
                      />
                    ) : (
                      <span
                        className={`border text-left text-gray-400 outline-none border-[#C8CEE1] bg-transparent rounded-lg py-2 px-3 w-full`}
                      >
                        Please select Type
                      </span>
                    )}
                  </div>
                  <div className="flex flex-col my-5 w-full">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Duration
                    </label>
                    <Input
                      type="number"
                      placeholder="Duration in mins"
                      name="duration"
                      min={0}
                      value={durationData}
                      onChange={handleEditDurationDefault}
                    />
                  </div>
                  <div className="flex flex-col my-5 w-full">
                    <div className="flex flow-row items-center gap-x-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Threshold
                      </label>
                      <InfoTooltip
                        position="right"
                        id="threshold"
                        className="mb-1"
                      >
                        This setting defines the allowed threshold for planned
                        breaks before they are categorized as unplanned downtime
                      </InfoTooltip>
                    </div>
                    <div>{defaultEditedData[0]?.threshold}</div>
                  </div>
                  {defaultEditedData?.type !== 'time' ? (
                    <div className="flex flex-col my-5 w-full">
                      <div className="flex flow-row items-center gap-x-2">
                        <label className="mb-1 font-semibold text-[#667085]">
                          Priority
                        </label>
                        <InfoTooltip
                          className="mb-1"
                          isHtml={true}
                          position="right"
                          id="priority"
                          width="400px"
                          content="
            Use this dropdown to determine how to handle planned breaks in case of clashes with scheduled breaks:<br />
            <b>Skip:</b> Select this option to skip the break that clashes with the scheduled break. <br />
            <b>Before:</b> Choose this if the break should take priority over the scheduled break. <br />
            <b>After:</b> Opt for this if the break should start after the scheduled break. <br />
          "
                        ></InfoTooltip>
                      </div>
                      <div>{defaultEditedData[0]?.priority}</div>
                    </div>
                  ) : null}
                </div>
              </>
            );
          }}
        </Modal>
      )}
    </div>
  );
};

export default PlannedDowntime;
