import { ArrowLeftOutlined } from '@ant-design/icons';
import { useContext, useEffect, useMemo, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import SelectV2 from '../../../components/global/components/SelectV2';
import Textarea from '../../../components/global/components/Textarea';
import MasterDetails from '../../../components/MasterDetails';
import ProductFormatManager from '../../../components/ProductFormats/ProductFormatManager';
import StaticProductTable from '../../../components/ProductFormats/StaticProductTable';
import {
  mobileWidth,
  renderFieldsBasedOnType,
  tabletWidth,
} from '../../../helperFunction';
import { Store } from '../../../store/Store';
import CustomTypesModal from '../global/CustomTypesModal';
import SalesVoucherStockOutModal from './SalesVoucherStockOutModal';

import usePrefixIds from '../../../hooks/usePrefixIds';

import { Button, DatePicker, Form, Input } from 'antd';
import dayjs from 'dayjs';
import { useGetCustomTypesQuery } from '../../../slices/AccountManagement/customTypesApiSlice';
import {
  useCreateVoucherMutation,
  useEditVoucherMutation,
  useGetVoucherByIdQuery,
} from '../../../slices/AccountManagement/voucherApiSlice';
import { useGetAllcustomerQuery } from '../../../slices/customerDataSlice';
import { useGetDropdownsQuery } from '../../../slices/dropdownApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice';
import { useGetAllVendorsForOptionsQuery } from '../../../slices/vendorApiSlice';

const SalesVoucherForm = ({ props }) => {
  const { setOpenModal, editData, setEditData } = props;
  const [editVoucher] = useEditVoucherMutation();
  const { data: ledgerTypes, isLoading: isLedgerTypesLoading } =
    useGetCustomTypesQuery({ type: 'ledgerType' });
  const { data: vendors, isLoading: isVendorLoading } =
    useGetAllVendorsForOptionsQuery();
  const { data: customers } = useGetAllcustomerQuery();
  const { data: voucher } = useGetVoucherByIdQuery(
    { id: editData?._id },
    { skip: editData?._id === undefined }
  );
  const isEditing = editData?._id !== undefined;

  const [formData, setFormData] = useState({});
  const [openCustomTypeModal, setOpenCustomTypeModal] = useState(false);
  const [vendorCustomerOptions, setVendorCustomerOptions] = useState([]);
  const [customerVendorLoading, setCustomerVendorLoading] = useState(false);
  const [createVoucher] = useCreateVoucherMutation();
  const [vendorCustomer, setVendorCustomer] = useState(false);
  const { data: dropdownsData } = useGetDropdownsQuery();
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const { defaults: { defaultParam } = {} } = useContext(Store);
  const [additionalFields, setAdditionalFields] = useState(null);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [openStockOut, setOpenStockOut] = useState(false);
  const [preparedPayload, setPreparedPayload] = useState(null);
  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'salesVoucherId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });
  // Product Format Manager state variables
  const [items, setItems] = useState(
    defaultParam?.projectDefaults?.showProductFormatTable || isEditing
      ? []
      : [
          {
            key: Date.now() + Math.random(),
            itemId: '',
            productName: '',
            uom: '',
            hsn: '',
            quantity: '',
            rate: '',
            discount: '',
            amount: 0,
            cgst: '',
            sgst: '',
            cgstAmount: 0,
            sgstAmount: 0,
            igst: '',
            igstAmount: 0,
            totalAmount: 0,
            color: '#FFFFFF',
          },
        ]
  );
  const [charges, setCharges] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [chargesVisibility, setChargesVisibility] = useState({});
  const [displayFormat, setDisplayFormat] = useState(null);

  useEffect(() => {
    const path = '/accountmanagement/voucher/salesVoucher';
    getTemplates({ path });
  }, [getTemplates]);

  useEffect(() => {
    if (voucher?._id && voucher?.salesVoucherData?.additionalFields) {
      setAdditionalFields(voucher.salesVoucherData.additionalFields);
      setSelectedTemplate(voucher.salesVoucherData.additionalFields);
    } else if (templatesData && Array.isArray(templatesData)) {
      const defaultTemplate =
        templatesData.find((t) => t?.name?.startsWith('Default')) ||
        templatesData?.[0];
      if (defaultTemplate) {
        setAdditionalFields(defaultTemplate);
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, [
    voucher?._id,
    voucher?.salesVoucherData?.additionalFields,
    templatesData,
  ]);

  const changeHandler = (e) => {
    const { name, value } = e.target;
    if (name === 'ledgerType' && value === 'addType') {
      setOpenCustomTypeModal(true);
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  useEffect(() => {
    let options = [];
    if (vendors?.length > 0) {
      let temp = vendors.map((vendor) => ({
        name: vendor.name,
        value: vendor._id,
      }));
      options = [...options, ...temp];
    }
    if (customers?.customers?.length > 0) {
      let temp = customers.customers.map((customer) => ({
        name: customer.name,
        value: customer._id,
      }));
      options = [...options, ...temp];
    }
    setVendorCustomerOptions(options);
  }, [vendors, customers]);

  const uomOptions = useMemo(() => {
    return (
      dropdownsData?.dropdowns
        ?.find((e) => e.name === 'uom')
        ?.values?.map((option) => ({
          label: option,
          value: option,
        })) || []
    );
  }, [dropdownsData]);

  // Prefill when editing
  useEffect(() => {
    if (voucher?._id) {
      const dateStr = voucher?.date
        ? new Date(voucher.date)?.toISOString()?.split('T')?.[0]
        : '';

      const base = {
        date: dateStr,
        voucherId: voucher?.salesVoucherId,
        voucherType: voucher?.voucherType,
        ledgerType: voucher?.salesVoucherData?.ledgerType?._id,
        remark: voucher?.remarks,
        salesOrder: voucher?.salesOrder?._id,
      };

      if (voucher?.salesVoucherData?.vendor?._id) {
        setFormData({ ...base, vendor: voucher.salesVoucherData.vendor._id });
        setVendorCustomer(voucher.salesVoucherData.vendor);
      } else if (voucher?.salesVoucherData?.customer?._id) {
        setFormData({
          ...base,
          customer: voucher.salesVoucherData.customer._id,
        });
        setVendorCustomer(voucher.salesVoucherData.customer);
      } else {
        setFormData(base);
      }
    }
  }, [voucher]);

  const productFormatData = useMemo(() => {
    const rv = voucher?.salesVoucherData || {};
    const pf = rv?.productTableFormat;
    const pfId = typeof pf === 'object' ? pf?._id : pf;
    return {
      productDetailsFromFormat: rv?.items || [],
      productChargesFromFormat: rv?.charges || {},
      productTableColumnHideStatus: rv?.columnVisibility || {},
      productTableChargesHideStatus: rv?.chargesVisibility || {},
      productTableFormat: pfId || null,
    };
  }, [voucher]);

  const proceedCreateVoucher = async (payload) => {
    let res;
    if (editData?._id) {
      res = await editVoucher({
        data: { updateData: payload, id: editData?._id },
      });
    } else {
      res = await createVoucher({
        data: {
          ...payload,
          voucherType: 'salesVoucher',
        },
      });
    }
    if (!res?.error) {
      setOpenModal(false);
      setFormData({});
      toast.success(
        `Sales Voucher ${editData?._id ? 'Updated' : 'Created'} successfully`
      );
    } else {
      toast.error(
        'Faced an error while creating voucher, please reload and try again.'
      );
    }
  };

  const handleSubmit = async () => {
    let stockOutOperation = null;
    if (!isEditing) {
      if (
        defaultParam?.projectDefaults?.createStockOutRequestFromSalesVoucher
      ) {
        stockOutOperation = 'stockOutRequest';
      } else if (
        defaultParam?.projectDefaults?.directStockOutFromSalesVoucher
      ) {
        stockOutOperation = 'directStockOut';
      }
    }

    const payload = {
      ...formData,
      additionalFields: additionalFields || formData?.additionalFields || null,
      items,
      charges,
      columnVisibility,
      chargesVisibility,
      productTableFormat: displayFormat,
      stockOutOperation,
    };

    const shouldOpenStockOut = !isEditing && stockOutOperation;

    if (shouldOpenStockOut) {
      setPreparedPayload(payload);
      setOpenStockOut(true);
      return;
    }

    await proceedCreateVoucher(payload);
  };

  const handleStockOutSubmit = async (rows) => {
    try {
      const payload = preparedPayload || {};
      const stockOutItems = (rows || [])
        .filter((r) => r?.store && (r?.quantity || 0) > 0)
        .map((r) => ({
          productName: r?.name,
          storeId: r?.store,
          quantity: Number(r?.quantity || 0),
        }));

      // Include stock out items in the main payload
      const finalPayload = {
        ...payload,
        stockOutItems: stockOutItems.length > 0 ? stockOutItems : undefined,
      };

      // Create voucher with stock out data in a single API call
      await proceedCreateVoucher(finalPayload);
    } catch (err) {
      toast.error(
        'Faced an error while creating voucher with stock out, please reload and try again.'
      );
    }

    setOpenStockOut(false);
    setPreparedPayload(null);
  };

  const handleStockOutSkip = async () => {
    if (preparedPayload) {
      const {
        stockOutOperation: _stockOutOperation,
        ...payloadWithoutStockOut
      } = preparedPayload;
      await proceedCreateVoucher(payloadWithoutStockOut);
    }
    setOpenStockOut(false);
    setPreparedPayload(null);
  };

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        const updated = {
          ...prev,
          templateData: updatedTemplateData,
        };
        // mirror to formData
        setFormData((p) => ({
          ...p,
          additionalFields: {
            ...(p.additionalFields || {}),
            templateData: updatedTemplateData,
            name: prev?.name,
          },
        }));
        return updated;
      });
      return;
    }

    if (fieldValue === '+') {
      setDropdownIdx(idx);
      setTemplateDropDownModal(true);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: updatedAdditionalFields,
      }));
      setFormData((prev) => ({
        ...prev,
        additionalFields: {
          ...prev.additionalFields,
          templateData: updatedAdditionalFields,
          name: additionalFields?.name,
        },
      }));
    }
  };

  return (
    <>
      {openCustomTypeModal && (
        <CustomTypesModal
          type="ledgerType"
          openModal={openCustomTypeModal}
          setOpenModal={setOpenCustomTypeModal}
        />
      )}
      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setOpenModal(false);
                setEditData({});
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEditing ? 'Edit' : 'Create'} Sales Voucher
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEditing
                  ? 'Update sales voucher information'
                  : 'Create a new sales voucher'}
              </p>
            </div>
          </div>
        </div>

        <Form layout="vertical" onFinish={handleSubmit}>
          <div className="p-4 space-y-4">
            {/* Basic Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Voucher ID
                  </label>
                  {isEditing ? (
                    <p className="text-sm text-gray-900">
                      <Input
                        disabled
                        className="text-sm bg-gray-50"
                        value={formData?.voucherId}
                      />
                    </p>
                  ) : (
                    <IdGenComp {...idCompData} />
                  )}
                </div>
                <div className="space-y-1">
                  <label className="block mb-1 text-sm text-gray-500 font-medium">
                    Choose Template
                  </label>
                  <SelectV2
                    options={templatesData?.map((template) => ({
                      value: template?._id,
                      name: template?.name,
                    }))}
                    value={selectedTemplate?._id}
                    onChange={(e) => {
                      const template = templatesData.find(
                        (t) => t._id === e.target.value
                      );
                      if (selectedTemplate?._id === e.target.value) {
                        return;
                      }
                      setAdditionalFields(template);
                      setSelectedTemplate(template);
                    }}
                  />
                </div>
                <div className="space-y-1 flex flex-col gap-1">
                  <label className="text-sm font-medium text-gray-600">
                    Date
                  </label>
                  <DatePicker
                    defaultValue={formData?.date ? dayjs(formData?.date) : null}
                    format={'DD-MM-YYYY'}
                    onChange={(date) =>
                      changeHandler({
                        target: {
                          name: 'date',
                          value: date?.format('YYYY-MM-DD'),
                        },
                      })
                    }
                    className="text-sm placeholder:text-gray-400"
                    name="date"
                    value={formData?.date ? dayjs(formData?.date) : null}
                    size="middle"
                  />
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Ledger Type
                  </label>
                  <SelectV2
                    name="ledgerType"
                    placeholder="Select ledger type"
                    value={formData.ledgerType}
                    isLoading={isLedgerTypesLoading}
                    options={[
                      { name: '+ Add Type', value: 'addType' },
                      ...(ledgerTypes?.map((item) => ({
                        name: item.name,
                        value: item._id,
                      })) || []),
                    ]}
                    onChange={(e) => changeHandler(e)}
                    className="text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Customer/Vendor & Sales Order */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Customer/Vendor Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Select Customer/Vendor
                  </label>
                  <SelectV2
                    name="vendor"
                    placeholder="Select customer/vendor"
                    value={formData.vendor || formData?.customer}
                    isLoading={isVendorLoading}
                    options={vendorCustomerOptions || []}
                    onChange={(e) => {
                      let foundVendor = vendors?.find(
                        (elem) => elem?._id === e.target.value
                      );
                      let foundCustomer = customers?.customers?.find(
                        (elem) => elem?._id === e.target.value
                      );
                      if (foundVendor?._id) {
                        setFormData((prev) => ({
                          ...prev,
                          vendor: foundVendor?._id,
                        }));
                        setCustomerVendorLoading(true);
                        setVendorCustomer(foundVendor);
                        setCustomerVendorLoading(false);
                      }
                      if (foundCustomer?._id) {
                        setFormData((prev) => ({
                          ...prev,
                          customer: foundCustomer?._id,
                        }));
                        setCustomerVendorLoading(true);
                        setVendorCustomer(foundCustomer);
                        setCustomerVendorLoading(false);
                      }
                    }}
                    className="text-sm"
                  />
                </div>
              </div>
            </div>
            {/* Customer/Vendor Details */}
            {vendorCustomer && (
              <div className="bg-white border border-gray-200 rounded-lg p-3">
                <h3 className="text-sm font-medium text-gray-700 mb-3">
                  Customer/Vendor Details
                </h3>
                <MasterDetails
                  isLoading={customerVendorLoading}
                  isMobile={isMobile}
                  isTablet={isTablet}
                  className="!text-gray-500 !text-sm"
                  details={vendorCustomer || {}}
                  setDetails={setVendorCustomer}
                  hideUIKeys={['deliveryAddress', 'departments', 'pageAccess']}
                  excludedFields={[
                    'id',
                    '_id',
                    'logo',
                    '__v',
                    'profileId',
                    'additionalFields',
                    'createdAt',
                    'updatedAt',
                    'data',
                    'form_id',
                    'user_id',
                    'customColumns',
                    'idData',
                    'pageAccess',
                    'departments',
                  ]}
                />
              </div>
            )}
            {/* Item Details */}
            {(() => {
              const useFormatTable = isEditing
                ? !!voucher?.salesVoucherData?.productTableFormat
                : defaultParam?.projectDefaults?.showProductFormatTable;

              return (
                <div className="bg-white border border-gray-200 rounded-lg p-3">
                  <h3 className="text-sm mt-2 font-medium text-gray-700">
                    {useFormatTable ? 'Product Format Table' : 'Product Table'}
                  </h3>
                  {useFormatTable ? (
                    <ProductFormatManager
                      input={items}
                      setInput={setItems}
                      charges={charges}
                      setCharges={setCharges}
                      columnVisibility={columnVisibility}
                      setColumnVisibility={setColumnVisibility}
                      chargesVisibility={chargesVisibility}
                      setChargesVisibility={setChargesVisibility}
                      displayFormat={displayFormat}
                      setDisplayFormat={setDisplayFormat}
                      isEdit={isEditing}
                      isCopy={false}
                      data={productFormatData}
                      enableManualEntry={false}
                    />
                  ) : (
                    <StaticProductTable
                      input={items}
                      setInput={setItems}
                      charges={charges}
                      setCharges={setCharges}
                      uomOptions={uomOptions}
                      enableManualEntry={false}
                    />
                  )}
                </div>
              );
            })()}

            {/* Template Details */}
            {additionalFields?.templateData?.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-3">
                <h3 className="text-sm font-medium text-gray-700 mb-3">
                  Template Details
                </h3>
                <section className="w-full">
                  {renderFieldsBasedOnType(
                    additionalFields,
                    handleInputChange,
                    templateDropDownModal,
                    setTemplateDropDownModal,
                    setAdditionalFields,
                    newOptionStatus,
                    setNewOptionStatus,
                    dropdownIdx,
                    setDropdownIdx
                  )}
                </section>
              </div>
            )}

            {/* Remarks */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Remarks
              </h3>
              <Textarea
                name="remark"
                value={formData.remark}
                onChange={(e) => changeHandler(e)}
                rows={3}
                placeholder="Enter additional remarks or comments"
                className="text-sm resize-none"
              />
            </div>
          </div>

          {/* Footer Actions */}
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
            <div className="flex items-center justify-end">
              <Button
                htmlType="submit"
                type="primary"
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                {isEditing ? 'Update' : 'Save'} Sales Voucher
              </Button>
            </div>
          </div>
        </Form>
      </div>
      {openStockOut && (
        <SalesVoucherStockOutModal
          open={openStockOut}
          onCancel={() => {
            setOpenStockOut(false);
            setPreparedPayload(null);
          }}
          onSkip={handleStockOutSkip}
          onSubmit={handleStockOutSubmit}
          items={items}
        />
      )}
    </>
  );
};

export default SalesVoucherForm;
