import { useState, useEffect } from 'react';
import { useLazyGetMediaByIdQuery } from '../../slices/mediaSlice';
import DynamicForm from '../ControlUnit/QC/DynamicForm';
import { toast } from 'react-toastify';
import { customConfirm } from '../../utils/customConfirm';
import { FormOutlined, CheckSquareOutlined } from '@ant-design/icons';
import { useFillJobInspectionMutation } from '../../slices/createInputApiSlice';
import { Tabs, Empty, Button } from 'antd';

import ShortUniqueId from 'short-unique-id';
import { checkAllQCFormFieldsAreFilled } from '../../helperFunction';

const createObjects = (rows, column) => {
  const result = {};
  let colsData = [];
  column.forEach((col) => {
    colsData.push({ type: col?.columnType || '', value: '' });
  });
  for (let i = 0; i < rows; i++) {
    result[i + 1] = colsData;
  }
  return result;
};

const JobForm = ({ ciData, afterSubmit, activeKey }) => {
  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [formData, setFormData] = useState(
    ciData?.jobInspectionformData?.find((item) => item?.uid === activeKey) || {}
  );
  const [formDetail, setFormDetail] = useState({});
  const [ref, setref] = useState({});
  const [blobUrl, setBlobUrl] = useState(null); // eslint-disable-line
  const [generatedQrData, setGeneratedQrData] = useState({});
  const [fillJobInspection] = useFillJobInspectionMutation();
  const uid = new ShortUniqueId();

  useEffect(() => {
    if (ciData) {
      let currForm =
        ciData?.jobCompletionQcForm?.find((item) => item?.uid === activeKey) ||
        {};
      setFormDetail(currForm);
      let qrData = {
        Signature: 'AICAN',
      };
      setGeneratedQrData(qrData);
    }
  }, [ciData, activeKey]);

  //-----------------------------------------------------------------------------------------------
  // Setting up default value for each field type
  useEffect(() => {
    if (formDetail) {
      let data = {};
      formDetail?.formId?.formData?.forEach((item) => {
        if (item.fieldType === 'Date') {
          data = {
            ...data,
            [item.fieldName]: {
              value: '',
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'Range') {
          data = {
            ...data,
            [item.fieldName]: {
              value: '',
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'Range Threshold') {
          data = {
            ...data,
            [item.fieldName]: {
              value: '',
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'Check') {
          data = {
            ...data,
            [item.fieldName]: {
              value: false,
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'String') {
          data = {
            ...data,
            [item.fieldName]: {
              value: '',
              type: item.fieldType,
            },
          };
        } else if (item.fieldType === 'Table') {
          data = {
            ...data,
            [item.fieldName]: {
              type: item.fieldType,
              columns: item?.tableOptions?.column || [],
              noOfRows: +item?.tableOptions?.rows || 0,
              newRowNo: 0,
              value: '',
              rows: +item?.tableOptions?.rows || 0,
              row:
                item?.tableOptions?.row ||
                Array.from({ length: item?.tableOptions?.rows }, () => ''),
              rowData: createObjects(
                +item?.tableOptions?.rows || 0,
                item?.tableOptions?.column || []
              ),
            },
          };
        }
      });

      let found = ciData?.jobInspectionformData?.find(
        (item) => item?.uid === activeKey
      );

      if (found) {
        setFormData(found);
      } else {
        setFormData(data);
      }
    }
  }, [formDetail, ciData]); // eslint-disable-line

  //-----------------------------------------------------------------------------------------------
  // Generating Reference Data(ref data) for validation during form submission
  useEffect(() => {
    if (formDetail) {
      const refData = formDetail?.refData;
      const formfield = formDetail?.formId?.formData;

      let data = {};
      formfield?.forEach((item) => {
        if (item.fieldType === 'Date') {
          let fName = item.fieldName;
          let cond = fName + '-Condition';
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';

          data = {
            ...data,
            [fName]: {
              value: refData?.[fName],
              type: 'Date',
              condition: refData?.[cond],
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        } else if (item.fieldType === 'Range') {
          let fName = item.fieldName;
          let min = fName + '-Min';
          let max = fName + '-Max';
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';

          data = {
            ...data,
            [fName]: {
              min: Number(refData?.[min]),
              max: Number(refData?.[max]),
              type: 'Range',
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        } else if (item.fieldType === 'Range Threshold') {
          let fName = item.fieldName;
          let main = fName + '-Main';
          let thres = fName + '-Thres';
          let min = Number(refData?.[main]) - Number(refData?.[thres]);
          let max = Number(refData?.[main]) + Number(refData?.[thres]);
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';

          data = {
            ...data,
            [fName]: {
              min: min,
              max: max,
              type: 'Range Threshold',
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        } else if (item.fieldType === 'String') {
          let fName = item.fieldName;
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';
          data = {
            ...data,
            [fName]: {
              type: 'String',
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        } else if (item.fieldType === 'Check') {
          let fName = item.fieldName;
          let textMan = fName + '-Man';
          let mediaMan = fName + '-MediaMan';
          data = {
            ...data,
            [fName]: {
              type: 'Check',
              textMan: refData?.[textMan] || false,
              mediaMan: refData?.[mediaMan] || false,
            },
          };
        }
      });
      setref(data);

      if (refData?.audioFile) {
        (async () => {
          const res = await getMediaById({ id: refData?.audioFile }).unwrap();
          setBlobUrl(res?.media?.data);
        })();
      }
    }
  }, [formDetail]); // eslint-disable-line

  const submitHandler = async (type) => {
    let flag = true;
    let cnt = 0;
    let msg = '';
    let textManMsg = '';
    let textManCnt = 0;
    let mediaManMsg = '';
    let mediaManCnt = 0;

    for (let key in formData) {
      if (ref[key]?.textMan) {
        if (formData[key] === '' || formData[key]?.value === '') {
          textManMsg = textManMsg + ' ' + key;
          textManCnt++;
        }
      }

      if (ref[key]?.mediaMan) {
        if (formData[key] === '') {
          mediaManMsg = mediaManMsg + ' ' + key;
          mediaManCnt++;
        } else if (
          !formData[key].hasOwnProperty('media') || // eslint-disable-line
          formData[key]?.media?.length === 0
        ) {
          mediaManMsg = mediaManMsg + ' ' + key;
          mediaManCnt++;
        }
      }

      if (ref[key]?.type === 'Range') {
        if (
          Number(formData[key]?.value) >= ref[key]?.min &&
          Number(formData[key]?.value) <= ref[key]?.max
        ) {
          flag = true;
        } else {
          flag = false;
          msg = msg + ' ' + key;
          cnt++;
        }
      } else if (ref[key]?.type === 'Range Threshold') {
        if (
          Number(formData[key]?.value) >= ref[key]?.min &&
          Number(formData[key]?.value) <= ref[key]?.max
        ) {
          flag = true;
        } else {
          flag = false;
          msg = msg + ' ' + key;
          cnt++;
        }
      } else if (ref[key]?.type === 'Date') {
        if (ref[key]?.condition === 'GT') {
          if (formData[key]?.value > ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ' + key;
            cnt++;
          }
        } else if (ref[key]?.condition === 'LT') {
          if (formData[key]?.value < ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ' + key;
            cnt++;
          }
        } else if (ref[key]?.condition === 'ET') {
          if (formData[key]?.value === ref[key]?.value) {
            flag = true;
          } else {
            flag = false;
            msg = msg + ' ' + key;
            cnt++;
          }
        }
      }
    }

    if (textManCnt) {
      toast.error(`${textManMsg} are required!`);

      return;
    }

    if (mediaManCnt) {
      toast.error(`Media for ${mediaManMsg} are required!`);
      return;
    }

    if (cnt) {
      if (
        await customConfirm(
          `Validation Check for ${msg}, Do you want to proceed?`
        )
      ) {
        flag = true;
      } else {
        flag = false;
      }
    }
    if (flag) {
      try {
        let data = {};

        Object.keys(formData).forEach((key) => {
          if (key === 'uid') {
            return;
          }

          let accessKey = uid.rnd(15);
          data = {
            ...data,
            [accessKey]: {
              ...formData[key],
              ['key']: key.toString(),
            },
          };
        });
        data[`uid`] = activeKey;
        data[`type`] = type;

        //  old logic
        // let data = { ...formData, uid: activeKey, type: type };

        const res = await fillJobInspection({
          id: ciData?._id,
          data: data,
        });

        if (!res.error) {
          setFormData({});
          setFormDetail({});
          setref({});
          setBlobUrl(null);
          setGeneratedQrData({});
          afterSubmit();
          toast.success('Form submitted successfully');
        }
      } catch (error) {
        console.log(error); //eslint-disable-line
      }
    }
  };

  return (
    <div className="h-[74vh] relative px-2  ">
      <div className="overflow-y-scroll h-full pb-14  ">
        {formDetail && (
          <DynamicForm
            formDetail={formDetail}
            formData={formData}
            setFormData={setFormData}
            generatedQrData={generatedQrData}
          />
        )}
      </div>
      <div className="absolute bottom-0 right-0 pr-4 pb-4">
        <div className="flex gap-4">
          <Button
            type="button"
            name="save"
            onClick={() => submitHandler('save')}
            className="bg-aican-blue-palette-600 hover:bg-aican-blue-palette-500 text-white  py-1 px-5 rounded "
          >
            Save
          </Button>
          <Button
            type="button"
            name="submit"
            disabled={!checkAllQCFormFieldsAreFilled(formData)}
            onClick={() => {
              submitHandler('submit');
            }}
            className={`bg-success hover:bg-green-600 text-white  py-1 px-3 rounded disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            Submit
          </Button>
        </div>
      </div>
    </div>
  );
};

const SubmitMessage = () => {
  return (
    <div className=" h-[50vh] flex items-center justify-center py-2  ">
      <Empty
        description="Form already submitted"
        className="text-lg    "
        image={Empty.PRESENTED_IMAGE_DEFAULT}
      />
    </div>
  );
};

const JobInspectionForm = ({ ciData, afterSubmit }) => {
  const [activeKey, setActiveKey] = useState('');

  return (
    <div>
      {/* <JobForm ciData={ciData} afterSubmit={afterSubmit} /> */}
      <div className="flex items-center justify-center py-2  ">
        <h2 className="text-lg  ml-2 rounded-md border px-3  border-gray-500/20 bg-aican-blue-palette-50 text-aican-blue-palette-600">
          <FormOutlined /> Job Inspection Form
        </h2>
      </div>

      <Tabs
        type="card"
        moreIcon={<CheckSquareOutlined />}
        activeKey={activeKey}
        animated={true}
        className="px-2"
        centered={true}
        onChange={setActiveKey}
        items={Array.from({ length: ciData?.jobCompletionQcForm?.length }).map(
          (_, idx) => ({
            label: `${ciData?.jobCompletionQcForm[idx]?.formId?.formName || `Form ${idx + 1}`}`,
            key: ciData?.jobCompletionQcForm[idx]?.uid,
            icon: ciData?.jobCompletionQcForm[idx]?.isSubmitted ? (
              <CheckSquareOutlined style={{ color: '#34C759' }} />
            ) : (
              <FormOutlined />
            ),
            children: ciData?.jobCompletionQcForm[idx]?.isSubmitted ? (
              <SubmitMessage />
            ) : (
              <JobForm
                ciData={ciData}
                afterSubmit={afterSubmit}
                activeKey={activeKey}
              />
            ),
          })
        )}
      />
    </div>
  );
};

export default JobInspectionForm;
