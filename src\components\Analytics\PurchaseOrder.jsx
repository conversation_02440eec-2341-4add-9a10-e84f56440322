import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  Pie,
  Pie<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import Card from '../../components/global/components/Card';
import Table from '../../components/global/components/Table';
import POTilesInfo from '../POTilesInfo';

const PurchaseOrder = () => {
  const data = [
    {
      name: 'Page A',
      uv: 4000,
      pv: 2400,
    },
    {
      name: 'Page B',
      uv: 3000,
      pv: 1398,
    },
    {
      name: 'Page C',
      uv: 2000,
      pv: 9800,
    },
    {
      name: 'Page D',
      uv: 2780,
      pv: 3908,
    },
    {
      name: 'Page E',
      uv: 1890,
      pv: 4800,
    },
    {
      name: 'Page F',
      uv: 2390,
      pv: 3800,
    },
    {
      name: 'Page G',
      uv: 3490,
      pv: 4300,
    },
  ];

  return (
    <section>
      <div className="mt-4">
        <POTilesInfo />
      </div>
      <div className="grid grid-cols-2 h-full w-full items-center gap-4 mt-4">
        <div className="grid grid-cols-2 gap-2 w-full h-full flex-1">
          <Card className="flex flex-1 w-full h-full items-center gap-2">
            <div className="w-full h--full">
              <p className="text-slate-400">Total Revenue</p>
              <h1>$4,345</h1>
              <p className="mt-4">
                <span className="text-green-400">12%</span> increase from last
                month
              </p>
            </div>
            <div className="w-full h-full">
              <ResponsiveContainer height="100%" width="100%">
                <LineChart
                  data={data}
                  margin={{ top: 30, right: 20, left: 0, bottom: 0 }}
                >
                  <Line type="monotone" dataKey="uv" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
          <Card className="flex flex-1 h-full items-center gap-2">
            <div className="w-full">
              <p className="text-slate-400">Total Revenue</p>
              <h1>$4,345</h1>
              <p className="mt-4">
                <span className="text-green-400">12%</span> increase from last
                month
              </p>
            </div>
            <div className="w-full h-full">
              <ResponsiveContainer height="100%" width="100%">
                <LineChart
                  data={data}
                  margin={{ top: 30, right: 20, left: 0, bottom: 0 }}
                >
                  <Line type="monotone" dataKey="uv" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
          <Card className="flex flex-1 h-full items-center gap-2">
            <div className="w-full">
              <p className="text-slate-400">Total Revenue</p>
              <h1>$4,345</h1>
              <p className="mt-4">
                <span className="text-green-400">12%</span> increase from last
                month
              </p>
            </div>
            <div className="w-full h-full">
              <ResponsiveContainer height="100%" width="100%">
                <LineChart
                  data={data}
                  margin={{ top: 30, right: 20, left: 0, bottom: 0 }}
                >
                  <Line type="monotone" dataKey="uv" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
          <Card className="flex flex-1 h-full items-center gap-2">
            <div className="w-full">
              <p className="text-slate-400">Total Revenue</p>
              <h1>$4,345</h1>
              <p className="mt-4">
                <span className="text-green-400">12%</span> increase from last
                month
              </p>
            </div>
            <div className="w-full h-full">
              <ResponsiveContainer height="100%" width="100%">
                <LineChart
                  data={data}
                  margin={{ top: 30, right: 20, left: 0, bottom: 0 }}
                >
                  <Line type="monotone" dataKey="uv" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </div>
        <Card className="flex-1 h-full w-full">
          <ResponsiveContainer height="100%" width="100%">
            <BarChart data={data} className="flex-1 w-full h-full">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="pv" fill="#8884d8" />
              <Bar dataKey="uv" fill="#82ca9d" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>
      <div className="mt-6">
        <Card>
          <h3 className="mb-2">Recent Invoices:</h3>
          <Table>
            <Table.Head>
              <Table.Th>Heading</Table.Th>
              <Table.Th>Heading</Table.Th>
              <Table.Th>Heading</Table.Th>
              <Table.Th>Heading</Table.Th>
              <Table.Th>Heading</Table.Th>
              <Table.Th>Heading</Table.Th>
              <Table.Th>Heading</Table.Th>
            </Table.Head>
            <Table.Body>
              <Table.Row>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
              </Table.Row>
              <Table.Row>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
                <Table.Td>Item</Table.Td>
              </Table.Row>
            </Table.Body>
          </Table>
        </Card>
      </div>
      <div className="mt-6 flex items-center gap-4">
        <Card className="flex-1 w-full">
          <ResponsiveContainer height={270} width="100%">
            <AreaChart
              data={data}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis dataKey="name" />
              <YAxis />
              <CartesianGrid strokeDasharray="3 3" />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="uv"
                stroke="#8884d8"
                fillOpacity={1}
                fill="url(#colorUv)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>
        <Card className="flex items-center gap-2 flex-1 w-full">
          <div className="w-full">
            <div>
              <p className="font-bold">Lorum Ipsum</p>
              <p className="font-[12px] text-slate-400">
                120% Lorum Ipsum Lorum Ipsum
              </p>
            </div>
            <div className="mt-2">
              <p className="font-bold">Lorum Ipsum</p>
              <p className="font-[12px] text-slate-400">
                120% Lorum Ipsum Lorum Ipsum
              </p>
            </div>
            <div className="mt-2">
              <p className="font-bold">Lorum Ipsum</p>
              <p className="font-[12px] text-slate-400">
                120% Lorum Ipsum Lorum Ipsum
              </p>
            </div>
          </div>
          <ResponsiveContainer width="100%" height={270}>
            <PieChart>
              <Tooltip />
              <Pie data={data} dataKey="uv" nameKey="name" fill="#8884d8" />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>
    </section>
  );
};

export default PurchaseOrder;
