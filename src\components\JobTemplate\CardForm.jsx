import { reqMasterData } from '../../helperFunction';

function CardForm({ master, count }) {
  const keys = Object.keys(master?.sheetData?.data?.[0]);

  const isValid = keys?.every((key) => reqMasterData.includes(key));
  return (
    <div
      className={`px-16 pt-4 pb-4 text-white w-full rounded-xl ${
        isValid ? 'bg-blue-primary' : 'bg-red-primary'
      }`}
    >
      <h3>{`Master ${count + 1}`}</h3>
      <p className="text-sm">{master.sheetData.name}</p>
    </div>
  );
}

export default CardForm;
