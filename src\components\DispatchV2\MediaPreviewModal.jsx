import {
  DeleteOutlined,
  DownloadOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { Button, Modal, Tooltip } from 'antd';
import { useState } from 'react';
import { downloadMedia } from '../../helperFunction';
import PreviewImgPdfFullscreen from '../salesOrder/PreviewImgPdfFullscreen';

const MediaPreviewModal = ({
  openMediaModal,
  setOpenMediaModal,
  mediaData,
  setMediaData,
  handleDeleteMedia,
  fieldName,
}) => {
  const [mediaToPreview, setMediaToPreview] = useState(null);
  const [previewMedia, setPreviewMedia] = useState(false);
  return (
    <div>
      <Modal
        title="Media"
        open={openMediaModal}
        onCancel={() => {
          setOpenMediaModal(false);
          setMediaData([]);
        }}
        footer={null}
        width={800}
      >
        <div>
          {previewMedia && (
            <PreviewImgPdfFullscreen
              media={mediaToPreview}
              showPreview={previewMedia}
              setShowPreview={setPreviewMedia}
            />
          )}
          {mediaData?.media?.map((item, idx) => (
            <div
              key={`${item.name}-${idx}`}
              className="flex items-center justify-between py-2 border-b last:border-b-0"
            >
              <Tooltip title={item.name}>
                <span className="text-sm text-gray-500 truncate max-w-[200px]">
                  {item.name.length > 30
                    ? `${item.name.slice(0, 30)}...`
                    : item.name}
                </span>
              </Tooltip>
              <div className="flex space-x-2">
                {(item.type?.includes('image') ||
                  item.type?.includes('pdf')) && (
                  <Button
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => {
                      setMediaToPreview(item);
                      setPreviewMedia(true);
                    }}
                    className="text-blue-500 hover:text-blue-700"
                  >
                    Preview
                  </Button>
                )}
                <Button
                  type="text"
                  icon={<DownloadOutlined />}
                  onClick={() => downloadMedia(item)}
                  className="text-green-500 hover:text-green-700"
                >
                  Download
                </Button>
                {handleDeleteMedia && (
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={() =>
                      handleDeleteMedia(mediaData?.itemId, idx, fieldName)
                    }
                    className="text-red-500 hover:text-red-700"
                  >
                    Delete
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </Modal>
    </div>
  );
};

export default MediaPreviewModal;
