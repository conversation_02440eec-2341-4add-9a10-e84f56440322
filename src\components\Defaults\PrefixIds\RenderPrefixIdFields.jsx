import { Dropdown } from 'antd';
import { IoMdCreate } from 'react-icons/io';
import DropdownField from './DropdownField';

function RenderPrefixIdFields({
  type,
  tVal,
  idType,
  idIndex,
  setFormat,
  options,
  setEditData,
  hasMultipleIds,
}) {
  const [splitType, splitNo] = type?.split('_');

  const onChange = (e) => {
    setFormat((prev) => {
      const data = prev?.[idType]?.map((i, idx) => {
        if (idIndex === idx) {
          return {
            ...i,
            [type]:
              splitType === 'Increment' ? +e?.target?.value : e?.target?.value,
          };
        }
        return i;
      });
      setEditData((prev) => ({ ...(prev || {}), [idType]: data }));
      return {
        ...prev,
        [idType]: data,
      };
    });
  };

  const handleEditType = (opt) => {
    let val = '';

    if (opt === 'Increment') val = 1;
    else if (opt === 'Dropdown') val = [];

    setFormat((prev) => {
      const data = prev?.[idType]?.map((item, idx) => {
        if (idIndex === idx) {
          const newObj = {};
          const keys = Object?.keys(item);
          for (let i = 0; i < keys.length; i++) {
            const elem = keys[i];
            if (elem === type) {
              newObj[`${opt}_${splitNo}`] = val;
            } else {
              newObj[elem] = item?.[elem];
            }
          }
          return newObj;
        }
        return item;
      });
      setEditData((prev) => ({ ...(prev || {}), [idType]: data }));
      return {
        ...prev,
        [idType]: data,
      };
    });
  };

  const switchFields = () => {
    switch (splitType) {
      case 'Increment':
      case 'String':
        return (
          <input
            type={splitType === 'Increment' ? 'number' : 'text'}
            name={splitType}
            value={tVal || ''}
            onChange={onChange}
            className="h-[45px] w-40 px-2 border border-[#C8CEE1] rounded-lg flex items-center"
            placeholder={splitType}
          />
        );
      case 'Dropdown':
        return (
          <DropdownField
            idType={idType}
            idIndex={idIndex}
            type={type}
            tVal={tVal}
            setFormat={setFormat}
            setEditData={setEditData}
          />
        );
      default:
        return (
          <span className="h-[45px] w-fit px-2 border border-[#C8CEE1] bg-gray-100 rounded-lg flex items-center whitespace-nowrap">
            {splitType}
          </span>
        );
    }
  };

  return (
    <div className="relative">
      {switchFields()}
      {hasMultipleIds && (
        <Dropdown
          className="absolute top-0 right-0 translate-x-1/3 -translate-y-1/3"
          trigger={['click']}
          menu={{
            items: options?.map((opt) => ({
              label: <span onClick={() => handleEditType(opt)}>{opt}</span>,
              key: opt,
            })),
          }}
        >
          <span className="flex h-[20px] aspect-square bg-blue-primary rounded-full items-center justify-center text-xs text-white">
            <IoMdCreate />
          </span>
        </Dropdown>
      )}
    </div>
  );
}

export default RenderPrefixIdFields;
