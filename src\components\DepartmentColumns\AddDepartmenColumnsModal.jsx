import { Button as ButtonAntd, Modal } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { HiPencil } from 'react-icons/hi2';
import { IoIosRemoveCircle } from 'react-icons/io';
import { MdDelete } from 'react-icons/md';
import { toast } from 'react-toastify';
import { unCamelCaseString } from '../../helperFunction';
import { useLazyGetAllFormQuery } from '../../slices/createFormapiSlice';
import {
  useAddDepColumnMutation,
  useEditDepColumnMutation,
  useRemoveDepColumnMutation,
} from '../../slices/departmentColumnApiSlice';
import { useGetAllUsersQuery } from '../../slices/userApiSlice';
import { DEPARTMENT_COLUMN_TYPE } from '../../utils/Constant';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
// import Modal from '../global/components/Modal';
import dragImage from '../../assets/images/hand1.png';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import DepartmentTableConfig from './DepartmentTableConfig';
import { InfoTooltip } from '../global/components/InfoTooltip';

const AddDepartmenColumnsModal = ({ data, setModalFor, modalFor }) => {
  const [inputData, setInputData] = useState({
    name: '',
    type: '',
    isMandatory: false,
  });
  const [tableConfig, setTableConfig] = useState({
    rows: 0,
    columns: [],
  });
  const { data: users } = useGetAllUsersQuery();
  const [editColumn] = useEditDepColumnMutation();
  const [columns, setColumns] = useState([]);
  const [values, setValues] = useState(['']);
  const [isDisabled, setIsDisabled] = useState(false);
  const [FILTERED_DEPARTMENT_COLUMN_TYPE, setFILTERED_DEPARTMENT_COLUMN_TYPE] =
    useState(DEPARTMENT_COLUMN_TYPE);
  const [addColumn] = useAddDepColumnMutation();
  const [removeColumn] = useRemoveDepColumnMutation();
  const [forms, setForms] = useState([]);
  const [selectedForm, setSelectedForm] = useState(null);
  const [isEdit, setIsEdit] = useState(false);
  const DragItem = useRef(0);
  const DragOverItem = useRef(0);
  // const [formType, setFormType] = useState('');

  const [getAllForm] = useLazyGetAllFormQuery();

  useEffect(() => {
    if (data) {
      setColumns(data?.columns);
    }
  }, [data]);

  const handleSort = () => {
    const itemArray = [...columns];
    const temp = itemArray[DragItem.current];
    itemArray[DragItem.current] = itemArray[DragOverItem.current];
    itemArray[DragOverItem.current] = temp;
    setColumns(itemArray);
    DragItem.current = 0;
    DragOverItem.current = 0;
  };

  useEffect(() => {
    let res = DEPARTMENT_COLUMN_TYPE?.filter((col) => col !== 'email'); //Filtering the Email For Temporary In Future it will added Again
    data?.columns?.map((column) => {
      if (column?.type === 'email') {
        res = res?.filter((dep) => dep !== 'email');
      }
      if (column?.type === 'bom') {
        res = res?.filter((dep) => dep !== 'bom' && dep !== 'bomComment');
      }
      if (column?.type === 'bomComment') {
        res = res?.filter((dep) => dep !== 'bom' && dep !== 'bomComment');
      }
    });
    setFILTERED_DEPARTMENT_COLUMN_TYPE(
      res?.filter((dep) => dep !== 'assembly form') // Temporary Filter the Assembly Form Fields
    );
  }, [data]);
  useEffect(
    function () {
      const getForms = async () => {
        try {
          const data = await getAllForm(
            { formType: 'Department' },
            false
          ).unwrap();
          if (data) {
            setForms(data?.filter((el) => el?.formFor === 'Department'));
          }
        } catch (error) {
          toast.error(error?.message);
        }
      };
      getForms();
    },
    [getAllForm]
  );

  const handleSubmit = async (e) => {
    e.preventDefault();
    const res = await editColumn({ id: data?._id, data: { columns } });
    if (res) {
      setModalFor('');
      toast.success('Successfully Added');
    } else {
      toast.error('Error Updating Department Columns');
    }
  };
  const handleAdd = async () => {
    const alreadyTable = columns?.find((column) => column?.type === 'table');
    if (alreadyTable && inputData?.type === 'table') {
      toast.error('Table Column Already Added');
      return;
    }
    const dataToSend = { ...inputData };
    if (
      inputData?.type === 'multiCheckbox' ||
      inputData?.type === 'dropdown' ||
      inputData?.type === 'select'
    ) {
      const newValues = values.filter((i) => i);
      const newSet = [...new Set(newValues)];

      if (newValues?.length !== newSet?.length) {
        toast.error('Options cannot share same value', {
          toastId: 'samevalue',
        });
        return;
      }

      dataToSend.values = newValues;
    }
    if (inputData?.type === 'forms') {
      dataToSend.form = selectedForm?._id;
    }
    if (inputData?.type === 'form management') {
      dataToSend.formType = selectedForm;
      // setFormType(selectedForm);
    }
    if (inputData?.type === 'table') {
      if (tableConfig?.columns?.length === 0) {
        toast.error('Column Length Should more than Zero');
        return;
      }
      dataToSend.tableConfig = tableConfig;
    }
    const res = await addColumn({ id: data._id, data: dataToSend }).unwrap();
    if (res) {
      setInputData({ name: '', type: '' });
      setValues(['']);
      toast.success('Successfully Added column', { toastId: 'add' });
    }
    setIsDisabled(false);
  };

  const handleremove = async (item) => {
    const confirm = await customConfirm(
      `Are you sure ypu want to delete ${item.name}?`,
      ''
    );
    if (!confirm) return;

    const res = await removeColumn({ id: data._id, data: { colId: item._id } });
    if (res) {
      toast.success('Successfully deleted column', { toastId: 'delete' });
    }
  };

  const handleColumnChange = (value, name, id) => {
    setColumns((prev) =>
      prev?.map((elem) => {
        if (elem?._id === id) {
          return {
            ...elem,
            [name]: value,
          };
        } else {
          return elem;
        }
      })
    );
  };

  const closeModal = () => {
    setModalFor('');
  };

  const handleEdit = async () => {
    const confirm = await customConfirm(
      `Are you sure you want to make changes to the existing department? All previous data will be lost.`,
      ''
    );
    if (!confirm) return;
    let temp = columns?.map((elem) => {
      if (elem?._id === inputData?._id) {
        if (
          inputData?.type === 'multiCheckbox' ||
          inputData?.type === 'dropdown'
        ) {
          return {
            ...inputData,
            values: values,
          };
        } else if (inputData?.type === 'table') {
          return {
            ...inputData,
            tableConfig: tableConfig,
          };
        }
        return inputData;
      }
      return elem;
    });
    setColumns(temp);
    setInputData({});
  };
  return (
    <>
      <Modal
        title="Add Columns"
        width={1000}
        centered
        open={modalFor?.length !== 0 ? true : false}
        onOk={handleSubmit}
        onCancel={closeModal}
        footer={
          isEdit
            ? [
                <ButtonAntd //eslint-disable-line
                  type="primary"
                  onClick={isEdit ? handleEdit : handleAdd}
                  className="absolute left-[20px]"
                >
                  {'Update'}
                </ButtonAntd>,
                <ButtonAntd //eslint-disable-line
                  onClick={() => {
                    setInputData({
                      name: '',
                      type: '',
                    });
                    setIsEdit(false);
                  }}
                  className="absolute left-[6rem]"
                >
                  Cancel Edit
                </ButtonAntd>,
                <ButtonAntd key="back" onClick={closeModal}>
                  Return
                </ButtonAntd>,
                <ButtonAntd
                  key="submit"
                  type="primary"
                  className="bg-[#14BA6D]"
                  onClick={handleSubmit}
                >
                  Submit
                </ButtonAntd>,
              ]
            : [
                <ButtonAntd //eslint-disable-line
                  type="primary"
                  onClick={isEdit ? handleEdit : handleAdd}
                  className="absolute left-[20px]"
                >
                  {isEdit ? 'Update' : 'Add'}
                </ButtonAntd>,
                <ButtonAntd key="back" onClick={closeModal}>
                  Return
                </ButtonAntd>,
                <ButtonAntd
                  key="submit"
                  type="primary"
                  className="bg-[#14BA6D]"
                  onClick={handleSubmit}
                >
                  Submit
                </ButtonAntd>,
              ]
        }
      >
        <div>
          <div className="grid grid-rows-1 gap-x-7 gap-y-3 grid-cols-1 md:grid-cols-3 mb-3">
            <div className="flex flex-col w-full ">
              <h4 className=" font-semibold text-black-300 mt-2 mb-2">Name</h4>
              <Input
                name="name"
                value={inputData?.name}
                disabled={isDisabled}
                onChange={(e) =>
                  setInputData((prev) => ({ ...prev, name: e.target.value }))
                }
              />
            </div>
            <div className="flex flex-col w-full ">
              <h4 className=" font-semibold text-black-300 mt-2 mb-2">Type</h4>
              <Select
                name="type"
                value={inputData?.type}
                disabled={isEdit ? true : false}
                onChange={(e) => {
                  setIsDisabled(false);
                  setInputData((prev) => ({
                    ...prev,
                    name:
                      prev.type === 'bom' ||
                      prev.type === 'bomComment' ||
                      prev.type === 'email'
                        ? ''
                        : prev.name,
                  }));
                  if (
                    e.target.value === 'email' ||
                    e.target.value === 'bom' ||
                    e.target.value === 'bomComment'
                  ) {
                    setInputData((prev) => ({
                      ...prev,
                      type: e.target.value,
                      name: e.target.value,
                    }));
                    // setIsDisabled(true);
                  } else {
                    setInputData((prev) => ({
                      ...prev,
                      type: e.target.value,
                    }));
                  }
                  if (
                    e.target.value === 'multiCheckbox' ||
                    e.target.value === 'dropdown'
                  ) {
                    setValues(['']);
                  }
                }}
                options={FILTERED_DEPARTMENT_COLUMN_TYPE.map((i) => ({
                  label: unCamelCaseString(i),
                  value: i,
                }))}
              />
            </div>
            <div className="flex flex-col">
              <h4
                htmlFor="mandatory"
                className=" font-semibold text-black-300 mt-2 mb-2"
              >
                Mandatory
              </h4>
              <div className="flex items-center h-10 px-3">
                <input
                  id="mandatory"
                  type="checkbox"
                  checked={inputData?.isMandatory || false}
                  onChange={(e) => {
                    setInputData((prev) => ({
                      ...prev,
                      isMandatory: e.target.checked,
                    }));
                  }}
                  className="h-5 w-5 rounded border-gray-300 text-blue-600 
              
              cursor-pointer"
                />
              </div>
            </div>
          </div>
          {(inputData?.type === 'multiCheckbox' ||
            inputData?.type === 'select' ||
            inputData?.type === 'dropdown') && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className=" font-semibold text-black-300 mt-2 mb-2">
                  Column Options
                </h4>
                <Button
                  onClick={() => setValues((prev) => [...prev, ''])}
                  className="!px-[10px] !py-[5px] text-sm"
                >
                  Add
                </Button>
              </div>
              <div className="h-[20vh] w-full overflow-y-scroll grid grid-cols-2 gap-2 pb-3">
                {values?.map((val, vIdx) => (
                  <div key={vIdx} className="flex flex-col w-[94%] relative">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Option {vIdx + 1}
                    </label>
                    <Input
                      id={`Options-${vIdx}`}
                      className=""
                      value={values?.[vIdx]}
                      onChange={(e) => {
                        const inp = document.getElementById(`Options-${vIdx}`);
                        if (values?.includes(e.target.value)) {
                          inp.classList.add('!border-red-500', '!bg-red-50');
                        } else {
                          inp.classList.remove('!border-red-500', '!bg-red-50');
                        }
                        setValues((prev) =>
                          prev.map((item, iIdx) => {
                            if (iIdx === vIdx) return e.target.value;
                            return item;
                          })
                        );
                      }}
                    />
                    <IoIosRemoveCircle
                      className="absolute right-[-8px] top-[1.2rem] text-lg text-red-500 cursor-pointer"
                      onClick={() =>
                        setValues((prev) => prev?.filter((_, i) => i !== vIdx))
                      }
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {inputData?.type === 'forms' ? (
            <>
              <div className="flex flex-col w-full ">
                <label className="mb-1 font-semibold text-[#667085]">
                  Select Form
                </label>
                <Select
                  name="forms"
                  value={selectedForm}
                  onChange={(e) => {
                    setSelectedForm(e.target.value);
                  }}
                  options={forms?.map((i) => ({
                    label: unCamelCaseString(i?.formName),
                    value: i,
                  }))}
                />
              </div>
            </>
          ) : null}
          {inputData?.type === 'form management' ? (
            <>
              <div className="flex flex-col w-full ">
                <label className="mb-1 font-semibold text-[#667085]">
                  Select Form Type
                </label>
                <Select
                  name="forms"
                  value={selectedForm}
                  onChange={(e) => {
                    setSelectedForm(e.target.value);
                  }}
                  // disabled={formType !== ''}
                  options={[
                    { value: 'create', label: 'Creation' },
                    { value: 'fill', label: 'Filling' },
                  ]}
                />
              </div>
            </>
          ) : null}
          {inputData?.type === 'table' && (
            <DepartmentTableConfig
              tableConfig={tableConfig}
              setTableConfig={setTableConfig}
            />
          )}
        </div>
        <h4 className=" font-semibold text-black-300 mt-2 mb-2">
          Added Fields
        </h4>
        <div className="w-full h-[50vh] mt-4 overflow-y-scroll">
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Th>#</Table.Th>
                <Table.Th>Name</Table.Th>
                <Table.Th>Type</Table.Th>
                <Table.Th>Options</Table.Th>
                <Table.Th>
                  <div className="flex gap-x-2 items-center">
                    Users
                    <InfoTooltip
                      position="top"
                      width="200px"
                      id="users"
                      isHtml={true}
                      content={
                        'This field allows us to give access to the column for certain users. If no user is selected, it means all users have access.'
                      }
                    />
                  </div>
                </Table.Th>
                <Table.Th>Action</Table.Th>
                <Table.Th>Mandatory</Table.Th>
                <Table.Th>Drag</Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {columns?.map((item, idx) => (
                <Table.Row
                  draggable
                  onDragStart={() => {
                    DragItem.current = idx;
                  }}
                  onDragEnter={() => {
                    DragOverItem.current = idx;
                  }}
                  onDragOver={(e) => e.preventDefault()}
                  onDragEnd={handleSort}
                  key={item?._id}
                >
                  <Table.Td>{idx + 1}</Table.Td>
                  <Table.Td>{item?.name}</Table.Td>
                  <Table.Td>{item?.type}</Table.Td>
                  <Table.Td>
                    {item.type === 'multiCheckbox' ||
                    item.type === 'dropdown' ||
                    item.type === 'select'
                      ? item?.values?.join(', ')
                      : 'NA'}
                  </Table.Td>
                  <Table.Td>
                    <MultiSelect
                      options={users?.map((user) => ({
                        label: user?.name,
                        value: user?._id,
                      }))}
                      value={item?.users}
                      onChange={(e) => {
                        let ids = e.target.value?.map((elem) => elem?.value);
                        handleColumnChange(ids, 'users', item?._id);
                      }}
                    />
                  </Table.Td>
                  <Table.Td className="flex items-center gap-x-3">
                    <span
                      className="text-blue-500 hover:underline cursor-pointer"
                      onClick={() => {
                        setInputData(item);
                        if (
                          item?.type === 'multiCheckbox' ||
                          item?.type === 'dropdown'
                        ) {
                          setValues(item?.values);
                        }
                        setIsEdit(true);
                      }}
                    >
                      <HiPencil className="text-xl" />
                    </span>
                    <span
                      className="text-red-500 hover:underline cursor-pointer"
                      onClick={() => handleremove(item)}
                    >
                      <MdDelete className="text-xl" />
                    </span>
                  </Table.Td>
                  <Table.Td>
                    <input
                      type="checkbox"
                      name="isMandatory"
                      id="isMandatory"
                      disabled={true}
                      checked={item?.isMandatory}
                    />
                  </Table.Td>
                  <Table.Td>
                    <img
                      title="Drag the field"
                      src={dragImage}
                      className="bg:white cursor-pointer w-5 h-5 select-none  hover:scale-(1) ease-in duration-300"
                    />
                  </Table.Td>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
      </Modal>
    </>
  );
};

export default AddDepartmenColumnsModal;
