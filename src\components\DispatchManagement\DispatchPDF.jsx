import {
  getPartVariantName,
  getProductVariantName,
} from '../../helperFunction';
import useHeaderAndFooter from '../../hooks/useHeaderAndFooter';
import EmbedPdf from '../global/components/EmbedPdf';
import Table from '../global/components/Table';
const DispatchPDF = ({ dispatch }) => {
  const { header } = useHeaderAndFooter({}, dispatch?.selectedDetails || {});
  // console.log(dispatch)
  return (
    <>
      <div id="print-only" className="!p-2">
        {dispatch ? (
          <>
            <div
              className="relative w-full mx-2 break-after-page !mb-2"
              id="border"
            >
              <div className="grid grid-cols-2 gap-3" id="border">
                <div className="col-span-2 !px-5 !py-2"> {header()}</div>
              </div>
              <h1 className="!text-xl !font-semibold w-full !text-center">
                DISPATCH DETAILS
              </h1>
              <div id="border"></div>
              <main className="main text-[14px]">
                <div className="flex justify-between !pl-2 !py-2 ">
                  <div className=" border-black flex-col min-w-[400px]">
                    <p className="font-semibold">
                      <span className="font-normal text-gray-400">
                        Customer Name:
                      </span>{' '}
                      {dispatch?.customer_name}{' '}
                    </p>
                    <div>
                      {' '}
                      <p className="text-gray-400 !font-semibold">
                        Dispatch Details
                      </p>
                      <div className="company-details max-w-[35rem] text-[15px]">
                        <p>
                          <span>Work Order Id: </span>
                          {dispatch?.workorder_name
                            ?.map((name) => name.workOrderId)
                            .join(', ')}
                        </p>
                        <p>
                          <span>Sales Order: </span>
                          N/A
                        </p>
                        <p>
                          <span>Transporter Name: </span>
                          {dispatch?.transporter_details?.name}
                        </p>
                        <p>
                          <span>Dispatch Date: </span>
                          {new Date(dispatch?.createdAt).toLocaleDateString(
                            'en-In'
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="min-w-[50px] -mt-[3px]">
                    <p className="text-gray-400 !font-semibold">
                      Pickup Address
                    </p>
                    <div>
                      <p>{dispatch?.pickup_address}</p>
                    </div>
                  </div>
                </div>
                <div id="border"></div>
                <div className="delivery-details !p-2">
                  <p className="text-gray-400 !font-semibold">
                    Delivery Address
                  </p>
                  <div className="company-details text-[15px]">
                    <p>{dispatch?.delivery_address}</p>
                  </div>
                </div>
                <div id="border"></div>
                {dispatch?.additionalFields?.templateData?.length > 0 && (
                  <div className="additional-fields mb-4 !px-2">
                    <h3 className="font-semibold">Template Fields</h3>
                    <Table id="border">
                      <Table.Head id="border">
                        <Table.Row id="border" className="text-center">
                          <Table.Th id="border" className="text-center">
                            SR No
                          </Table.Th>
                          <Table.Th id="border" className="text-center">
                            Field Name
                          </Table.Th>
                          <Table.Th id="border" className="text-center">
                            Field Value
                          </Table.Th>
                        </Table.Row>
                      </Table.Head>
                      <Table.Body id="border">
                        {dispatch?.additionalFields?.templateData?.map(
                          (templateData, idx) => {
                            return (
                              <Table.Row key={templateData?._id} id="border">
                                <Table.Td id="border" className="text-center">
                                  {idx + 1}
                                </Table.Td>
                                <Table.Td id="border" className="text-center">
                                  {templateData?.fieldName}
                                </Table.Td>
                                <Table.Td id="border" className="text-center">
                                  {(templateData?.fieldType === 'Check' &&
                                    templateData?.fieldValue &&
                                    'true') ||
                                    templateData?.fieldValue}
                                </Table.Td>
                              </Table.Row>
                            );
                          }
                        )}
                      </Table.Body>
                    </Table>
                  </div>
                )}

                <div className="!px-2">
                  <h3 className="mt-3 mb-1 font-semibold">Product Details</h3>
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>Item Name</Table.Th>
                        <Table.Th>Unit</Table.Th>
                        <Table.Th>Order Quantity</Table.Th>
                        <Table.Th>Dispatched Quantity</Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {dispatch?.dispatch_items?.map((item) => {
                        return (
                          <Table.Row key={item?._id} id="border">
                            <Table.Td>
                              {item?.part?.name ||
                                item?.product?.name ||
                                item?.manualEntry ||
                                item?.name ||
                                (item?.bomId ? item.bomId.name : '') ||
                                (item?.productVariant &&
                                  getProductVariantName(
                                    item?.productVariant
                                  )) ||
                                (item?.partVariant
                                  ? getPartVariantName(item.partVariant)
                                  : '-')}
                            </Table.Td>
                            <Table.Td>{item?.units || '-'}</Table.Td>
                            <Table.Td>{item?.order_qty}</Table.Td>
                            <Table.Td>{item?.dispatch_qty}</Table.Td>
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                  </Table>
                </div>

                <div className="!p-2">
                  <h3 className="mt-3 mb-1 font-semibold">Loading Details</h3>
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>Loading Type</Table.Th>
                        <Table.Th>Unit</Table.Th>
                        <Table.Th>Order Quantity</Table.Th>
                        <Table.Th>Total Amount</Table.Th>
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      <Table.Row>
                        <Table.Td>
                          {dispatch?.loadingItem?.loadingType}
                        </Table.Td>
                        <Table.Td>{dispatch?.loadingItem?.unit}</Table.Td>
                        <Table.Td>{dispatch?.loadingItem?.qty}</Table.Td>
                        <Table.Td>
                          {dispatch?.loadingItem?.totalAmount}
                        </Table.Td>
                      </Table.Row>
                    </Table.Body>
                  </Table>
                </div>
                {dispatch?.remark && (
                  <div className="!p-2" id="border">
                    <p className="!font-semibold">Remark</p>
                    <p>{dispatch?.remark}</p>
                  </div>
                )}
              </main>
            </div>
            <div className="w-full !mb-4"></div>
            {dispatch?.attachments?.length > 0 && (
              <p className="!pl-2 !font-semibold underline !text-xs">
                Attached Media:
              </p>
            )}
            <div className="flex flex-wrap justify-center gap-2">
              {dispatch?.attachments?.map((file, index) => {
                if (file.type === 'application/pdf') {
                  return <EmbedPdf key={index} file={file} />;
                } else if (file?.type?.startsWith('image/')) {
                  return (
                    <div
                      key={index}
                      className="flex flex-col items-center"
                      style={{
                        width: '45%',
                        height: '40vh',
                        pageBreakInside: 'avoid',
                        pageBreakAfter:
                          (index + 1) % 4 === 0 ? 'always' : 'auto',
                      }}
                    >
                      <div className="border rounded-lg overflow-hidden w-full h-full">
                        <img
                          src={file?.data}
                          alt={file?.name}
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <p className="mt-2 text-sm text-gray-600">{file?.name}</p>
                    </div>
                  );
                } else {
                  return null;
                }
              })}
            </div>
          </>
        ) : null}
      </div>
    </>
  );
};

export default DispatchPDF;
