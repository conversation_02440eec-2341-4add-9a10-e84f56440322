import {
  MinusCircleIcon,
  PlusCircleIcon,
  TruckIcon,
} from '@heroicons/react/24/outline';
import BackIcon from './../../../assets/images/back.png';
import NextIcon from './../../../assets/images/next.png';
import SubmitIcon from './../../../assets/images/submit.png';
import BreadCrumbs from './BreadCrumbs';
import Button from './Button';

const FullscreenModal = ({
  onCloseModal,
  title,
  description,
  pages,
  step,
  current,
  canAddValue,
  canRemoveValue,
  canSubmit,
  canDispatch,
  onSubmit,
  nextClickHandler,
  formData,
  setFormData,
  setValueCount,
  btnIsLoading,
  isBackButton,
  isSubmitRequired,
  onBackClick,
  onDispatch,
  children,
  // className,
  isChangeInAssembly,
}) => {
  return (
    <div
      className={`w-screen h-screen bg-white  absolute top-0 left-0 z-[9999] flex flex-col`}
    >
      {/* Header Section */}
      <div className="flex justify-between items-center p-4 border-b border-gray-200">
        <section className="flex flex-col items-start pt-6">
          <Button
            textColor="text-gray-600"
            className="bg-white border-2 text-gray-600 !text-sm !h-10  !cursor-pointer"
            onClick={() => onCloseModal(false)}
          >
            Back
          </Button>

          <section className="mt-8 px-1">
            <h3 className="font-semibold text-lg">{title}</h3>
            <p className="font-semi-bold text-xs text-[#919487]">
              {description}
            </p>
          </section>
          {pages?.length > 0 && (
            <div className="mt-2">
              <BreadCrumbs pages={pages} step={step} />
            </div>
          )}
        </section>
      </div>

      {/* Scrollable Children Section */}
      <div className="flex-1 overflow-y-auto mt-8  px-4">
        {children(current)}
      </div>

      {/* Fixed bottom buttons */}
      <div className="flex items-center justify-between p-4 bg-white border-t border-gray-200">
        {canAddValue && (
          <Button
            onClick={() => {
              const updatedValues = formData?.values
                ? [...formData.values, '']
                : [''];
              setFormData((prev) => ({ ...prev, values: updatedValues }));
              setValueCount((prev) => prev + 1);
            }}
            type="button"
          >
            <PlusCircleIcon height={20} width={20} />
          </Button>
        )}

        {canRemoveValue && (
          <Button
            onClick={() => {
              if (formData?.values?.length > 0) {
                const updatedValues = formData.values.slice(0, -1);
                setFormData((prev) => ({ ...prev, values: updatedValues }));
              }
              setValueCount((prev) => prev - 1);
            }}
            type="button"
            className="ml-10"
          >
            <MinusCircleIcon height={20} width={20} />
          </Button>
        )}

        <div className="flex items-center gap-x-8 justify-end">
          {isBackButton || step > 0 ? (
            <Button
              type="button"
              className="rounded-[8px]  !h-6 !text-xs"
              onClick={() => {
                if (onBackClick) {
                  onBackClick({ ...current });
                } else {
                  // Handle back step logic here
                }
              }}
              icon={<img src={BackIcon} alt="Back Icon" className="w-5 h-5" />}
            >
              Back
            </Button>
          ) : null}

          {canDispatch && (
            <Button onClick={onDispatch} className={'!h-7 !text-xs'}>
              <TruckIcon className="w-[1rem] h-[1rem]" />
              Dispatch
            </Button>
          )}

          {isSubmitRequired && (
            <Button
              isLoading={btnIsLoading}
              type={canSubmit ? 'submit' : 'button'} // Check if canSubmit is set correctly
              onClick={(e) => {
                if (canSubmit && onSubmit) {
                  // Ensure onSubmit is called correctly
                  onSubmit(e, current);
                }
              }}
              className={`rounded-[8px]  !h-7 !text-xs ${isChangeInAssembly ? 'bg-[#14BA6D] hover:bg-[#21d17f]' : 'bg-green-600 hover:brightness-105'}`}
              icon={
                <img src={SubmitIcon} alt="Submit Icon" className="w-5 h-5" />
              }
            >
              {isChangeInAssembly ? 'Make Copy and Proceed' : 'Submit'}
            </Button>
          )}

          {pages?.length !== 0 && (
            <Button
              type="button"
              className={`rounded-[8px]  !h-7 !text-xs ${step + 1 !== pages?.length ? '' : 'hidden'}`}
              onClick={nextClickHandler}
              icon={<img src={NextIcon} alt="Next Icon" className="w-5 h-5" />}
            >
              Next
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default FullscreenModal;
