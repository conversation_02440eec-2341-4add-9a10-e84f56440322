import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import AddIcon from '../../../assets/images/add.png';
import { useAddAssemblyFormMutation } from '../../../slices/assemblyFormApiSlice';
import { customConfirm } from '../../../utils/customConfirm';
import Button from '../../global/components/Button';
import Modal from '../../global/components/Modal';
import Select from '../../global/components/Select';
import Table from '../../global/components/Table';
import DynamicForm from './DynamicForm';

const defaultNewStepData = {
  stepNumber: '',
  stepName: '',
  description: '',
  attachments: [],
  isMediaMandatory: false,
};

const TrComponent = ({
  pIdx,
  pro,
  allAssemblyForms,
  allAssemblyData,
  setAllAssemblyData,
}) => {
  const [fName, setfName] = useState('');
  const [currForm, setCurrForm] = useState(null);
  const [disable, setDisable] = useState(false);
  const [saved, setSaved] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  // const [removedPDFwhileUpdate, setRemovedPDFwhileUpdate] = useState([]);
  const [newStep, setNewStep] = useState(defaultNewStepData);
  const [stepCount, setStepCount] = useState(1);
  const [showEditButton, setShowEditButton] = useState(false);
  const [isChanged, setIsChanged] = useState(false);
  const [formData, setFormData] = useState();

  const [addAssemlyForm, { isLoading: isLoadingCreateAssembly }] =
    useAddAssemblyFormMutation();

  useEffect(() => {
    setFormData({
      category: currForm?.category,
      formName: currForm?.formName,
      isSequential: currForm?.isSequential,
      steps: currForm?.steps,
    });
  }, [currForm]);
  const handleNewStep = () => {
    if (newStep.stepName !== '') {
      setFormData((prev) => ({
        ...prev,
        steps: [
          ...prev.steps,
          {
            ...newStep,
            stepNumber: stepCount,
          },
        ],
      }));
      toast.success('Step Added Successfully');
      setNewStep(defaultNewStepData);
      setStepCount((prev) => prev + 1);
      setIsChanged(true);
    } else {
      toast.error('Cannot add empty field');
    }
  };

  useEffect(() => {
    setStepCount(formData?.steps?.length + 1);
  }, [formData]);

  useEffect(() => {
    if (currForm && !isEdit) {
      setShowEditButton(true);
    }
  }, [currForm, isEdit]);

  useEffect(() => {
    const exists = allAssemblyData?.find(
      (item) => (item?.processId?._id || item?.processId) === pro?.mqtt?._id
    );
    if (exists) {
      setSaved(true);

      setCurrForm(
        allAssemblyForms.find(
          (item) => item._id === (exists?.formId?._id || exists?.formId)
        )
      );
    }
  }, [allAssemblyData, pro, allAssemblyForms]);

  const changeHandler = (e) => {
    setCurrForm(allAssemblyForms.find((item) => item._id === e.target.value));
    setfName(e.target.value);
  };

  const handleSubmit = async () => {
    if (isChanged) {
      if (formData.formName !== '') {
        const res = await addAssemlyForm(
          currForm.formName === formData.formName
            ? {
                ...formData,
                formName: formData?.formName + ' + copy1',
              }
            : formData
        ).unwrap();
        if (res?.status !== 400) {
          toast.success('Form Copy Added Successfully');
          setAllAssemblyData(
            allAssemblyData.filter((item) => item?.processId !== pro.mqtt._id)
          );

          const data = {
            processId: pro?.mqtt._id,
            formId: res?._id,
          };
          setAllAssemblyData((prev) => [...prev, data]);
          setCurrForm(res);
          setfName(res?._id);
          setIsEdit(false);
          setIsChanged(false);
          setShowEditButton(false);
          setIsOpen(false);
        } else {
          toast.error('Error in adding form');
        }
      } else {
        toast.error('Cannot save without form name or empty data');
      }
    } else {
      setAllAssemblyData(
        allAssemblyData.filter((item) => item?.processId !== pro.mqtt._id)
      );
      const data = {
        processId: pro.mqtt._id,
        formId: fName,
      };
      setAllAssemblyData((prev) => [...prev, data]);
      setIsOpen(false);
    }
    setSaved(true);
  };

  useEffect(() => {
    const alreadyAdded = allAssemblyData?.find(
      (item) => item?.processId?._id === pro?.mqtt?._id
    );

    if (alreadyAdded) {
      setDisable(true);
      setfName(alreadyAdded?.formId?._id);
    }
  }, [pro, allAssemblyData, allAssemblyForms]);

  return (
    <>
      <Table.Row>
        <Table.Td>{pIdx + 1}</Table.Td>
        <Table.Td>{pro.processName}</Table.Td>
        <Table.Td>
          <div className="flex">
            <Button
              type="button"
              onClick={() => setIsOpen((prev) => !prev)}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mt-2"
            >
              <img
                src={AddIcon}
                alt="Add Icon"
                className="w-5 h-5 object-contain relative"
              />
              {saved ? 'Edit' : 'Add'}
            </Button>

            {saved && (
              <p className="font-normal text-green-400 w-fit px-4 py-1 mt-3  ml-4">
                Saved
              </p>
            )}
          </div>
        </Table.Td>
        {isOpen && (
          <td>
            <Modal
              title={'Assign Form'}
              description={'Assign form to this project'}
              onCloseModal={async () => {
                if (isChanged) {
                  const confirmation = await customConfirm(
                    'Are you sure you want to go back? No data has been saved',
                    'delete'
                  );
                  if (confirmation) {
                    setNewStep(defaultNewStepData);
                    setFormData({
                      category: currForm?.category,
                      formName: currForm?.formName,
                      isSequential: currForm?.isSequential,
                      steps: currForm?.steps,
                    });
                    setIsEdit(false);
                    setIsChanged(false);
                    setIsOpen(false);
                  }
                } else {
                  setIsOpen(false);
                }
              }}
              isLoading={isLoadingCreateAssembly}
              isChangeInAssembly={isChanged}
              onSubmit={handleSubmit}
              canSubmit={false}
              onAdd={
                isEdit
                  ? {
                      label: 'Add',
                      func: [handleNewStep],
                      step: [0],
                    }
                  : null
              }
            >
              {() => {
                return (
                  <>
                    <Select
                      disabled={disable}
                      value={fName}
                      onChange={(e) => changeHandler(e, pro)}
                      options={allAssemblyForms?.map((option) => ({
                        name: option.formName,
                        value: option._id,
                      }))}
                      placeholder="Select Form"
                    />
                    {showEditButton && (
                      <div className="flex mt-2 w-full flex-col">
                        <Button
                          className="self-end"
                          onClick={() => {
                            setIsEdit(true);
                            setShowEditButton(false);
                          }}
                        >
                          Edit
                        </Button>
                      </div>
                    )}
                    {/* eslit-disable-next-line */}
                    {currForm && (
                      <DynamicForm
                        selectedForm={formData}
                        setSelectedForm={setFormData}
                        onSubmit={handleSubmit}
                        processId={pro.mqtt._id}
                        setIsChanged={setIsChanged}
                        isEdit={isEdit}
                        newStep={newStep}
                        setNewStep={setNewStep}
                        stepCount={stepCount}
                      />
                    )}
                  </>
                );
              }}
            </Modal>
          </td>
        )}
      </Table.Row>
    </>
  );
};

const AssemblyForms = ({
  processes,
  allAssemblyData,
  setAllAssemblyData,
  allAssemblyForms,
}) => {
  const assemblyProcess = processes?.filter(
    (itm) => itm.processCategory === 'Assembly'
  );

  return (
    <div className="w-full h-full gap-x-5 gap-y-2">
      <Table className="w-full">
        <Table.Head>
          <Table.Row>
            <Table.Th>#</Table.Th>
            <Table.Th>Process</Table.Th>
            <Table.Th>Form</Table.Th>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {assemblyProcess?.map((pro, pIdx) => {
            return (
              <TrComponent
                key={pIdx}
                pIdx={pIdx}
                pro={pro}
                allAssemblyForms={allAssemblyForms}
                allAssemblyData={allAssemblyData}
                setAllAssemblyData={setAllAssemblyData}
              />
            );
          })}
        </Table.Body>
      </Table>
    </div>
  );
};

export default AssemblyForms;
