import { useContext } from 'react';
import Input from '../global/components/Input';
import SelectV2 from '../global/components/SelectV2';
import { Row, Td } from '../global/components/Table';
import { Store } from '../../store/Store';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import { BOM_CATEGORIES, INITIAL_BOM_DATA } from './bomConstants';
import {
  ListTree,
  Paperclip,
  RefreshCw,
  ShoppingCart,
  Trash,
  XIcon,
} from 'lucide-react';

export default function BomRow({
  row,
  boms,
  setBoms,
  variantIndex,
  allParts,
  allProducts,
  uoms,
  allTemplates,
  setModalFor,
  setDeletedBoms,
  setDeletedMedias,
}) {
  const { defaults } = useContext(Store);

  const bomsToMap = boms.filter(
    (el) =>
      el.variantIndex === variantIndex + 1 && el?.rowId?.startsWith(row?.rowId)
  );

  const handleAddSubitem = () => {
    setBoms((prev) => [
      ...prev,
      {
        ...INITIAL_BOM_DATA,
        variantIndex: variantIndex + 1,
        rowId: `${row?.rowId}.${bomsToMap?.length + 1}`,
        tempId: Date.now().toString(),
      },
    ]);
  };

  const handleRemove = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to remove row?',
      'delete'
    );
    if (!confirm) return;

    const deletedId = row?.rowId;
    if (!deletedId) return;

    const deletedPrefix = deletedId + '.';
    const deletedIdParts = deletedId.split('.');

    let newBoms = [];

    for (const bom of boms) {
      const bomClone = { ...bom };

      if (
        bomClone.rowId === deletedId ||
        bomClone.rowId.startsWith(deletedPrefix)
      ) {
        if (bomClone._id) {
          setDeletedMedias((prev) => [
            ...(prev || []),
            ...(bomClone.media || []).map((m) => m?._id || m),
          ]);
          setDeletedBoms((prev) => [...prev, bomClone._id]);
        }
        continue;
      }

      newBoms.push(bomClone);
    }

    const renamedBoms = newBoms.map((bom) => {
      const currentIdParts = bom.rowId.split('.');

      if (
        currentIdParts.length >= deletedIdParts.length &&
        currentIdParts.slice(0, deletedIdParts.length - 1).join('.') ===
          deletedIdParts.slice(0, -1).join('.')
      ) {
        const currentIndex = +currentIdParts[deletedIdParts.length - 1];
        const deletedIndex = +deletedIdParts[deletedIdParts.length - 1];

        if (currentIndex > deletedIndex) {
          currentIdParts[deletedIdParts.length - 1] = String(currentIndex - 1);
          return {
            ...bom,
            rowId: bom.rowId.replace(
              bom.rowId.split('.').slice(0, deletedIdParts.length).join('.'),
              currentIdParts.slice(0, deletedIdParts.length).join('.')
            ),
          };
        }
      }

      return bom;
    });

    setBoms(renamedBoms);
  };

  const handleInputChange = (e) => {
    let { name, value, type } = e.target;
    let objToSpread = {};

    if (['part', 'product'].includes(name)) {
      if (value === '+') {
        name = 'manualEntry';
        value = '';
      } else {
        const item = (name === 'part' ? allParts : allProducts).find(
          (it) => (it?._id || it?.value) === value
        );
        objToSpread.uomDetails = {
          selectedUom: item?.uom,
          originalUom: item?.uom,
          conversionFactor: item?.additionalUoms || [],
        };
      }
    }

    if (name === 'category') {
      objToSpread = { ...INITIAL_BOM_DATA };
    }

    setBoms((prev) =>
      (name === 'category'
        ? prev?.filter((b) => {
            if (b.rowId?.startsWith(`${row?.rowId}.`)) {
              return false;
            }

            return true;
          })
        : prev
      )?.map((b) => {
        if (b.rowId === row?.rowId) {
          if (name === 'uom') {
            return {
              ...b,
              isUpdated: true,
              ...objToSpread,
              uomDetails: {
                selectedUom: value,
                originalUom: value,
                conversionFactor: [],
              },
            };
          } else {
            return {
              ...b,
              isUpdated: true,
              ...objToSpread,
              [name]: type === 'number' ? +value : value,
            };
          }
        }
        return b;
      })
    );
  };

  const handleAdditonalColumnsInput = (e) => {
    let { name, value } = e.target;

    setBoms((prev) =>
      prev?.map((b) => {
        if (b.rowId === row?.rowId) {
          return {
            ...b,
            isUpdated: true,
            addtionalFields: { ...(b?.addtionalFields || {}), [name]: value },
          };
        }
        return b;
      })
    );
  };

  const getOptions = () => {
    let options = [];
    const inhouseFG = allProducts?.filter((el) =>
      el?.category?.startsWith('Inhouse')
    );

    const outsourceFG = allProducts?.filter((el) =>
      el?.category?.startsWith('Outsource')
    );
    switch (row?.category) {
      case 'subAssembly':
        options.push({ name: '+ Manual Entry', value: '+' });
        options.push(
          ...inhouseFG?.map((el) => ({
            name: el?.name,
            value: el?.value,
          }))
        );
        options.push(
          ...outsourceFG?.map((el) => ({
            name: el?.name,
            value: el?.value,
          }))
        );
        break;

      case 'inhouse':
        // options.push({ name: '+ Add Part', value: 'add part' });
        options.push({ name: '+ Manual Entry', value: '+' });
        options.push(
          ...inhouseFG?.map((el) => ({
            name: el?.name,
            value: el?.value,
          }))
        );
        break;

      case 'outsource':
        // options.push({ name: '+ Add Part', value: 'add part' });
        options.push({ name: '+ Manual Entry', value: '+' });
        options.push(
          ...outsourceFG?.map((el) => ({
            name: el?.name,
            value: el?.value,
          }))
        );
        break;

      case 'inventoryItem':
        // options.push({ name: '+ Add Part', value: 'add part' });
        options.push(
          ...allParts?.map((el) => ({
            name: el?.name,
            value: el?.value,
          }))
        );
        break;
      default:
        break;
    }
    return options;
  };

  return (
    <>
      <Row>
        <Td className={'relative'}>
          {variantIndex > 1 ? (
            <>
              {[...Array(variantIndex - 1)].map((_, idx) => (
                <div
                  key={idx}
                  style={{ left: 20 + 8 * idx }}
                  className="absolute h-full w-px bg-gray-300 top-0"
                />
              ))}
            </>
          ) : null}
          <p style={{ marginLeft: `${8 * (variantIndex - 1)}px` }}>
            {row?.rowId}
          </p>
        </Td>
        <Td>
          <SelectV2
            name="category"
            value={row?.category}
            onChange={handleInputChange}
            className="min-w-[160px]"
            options={
              // NOTE: remove the below filter condition to enable infinite nesting
              variantIndex > 2
                ? BOM_CATEGORIES.filter((i) => i.value !== 'subAssembly')
                : BOM_CATEGORIES
            }
          />
        </Td>
        <Td>
          {row?.manualEntry !== null ? (
            <div className="flex items-center gap-2">
              <Input
                name="manualEntry"
                value={row?.manualEntry}
                onChange={handleInputChange}
                inputClassname="!h-[34px]"
              />
              <XIcon
                onClick={() =>
                  handleInputChange({
                    target: { name: 'manualEntry', value: null },
                  })
                }
                className="rounded-full text-red-500 cursor-pointer hover:bg-red-500 hover:text-white aspect-square h-fit w-8"
              />
            </div>
          ) : (
            <SelectV2
              name={row?.category === 'inventoryItem' ? 'part' : 'product'}
              value={row?.part || row?.product}
              options={getOptions()}
              className="min-w-[200px]"
              onChange={handleInputChange}
            />
          )}
        </Td>
        <Td>
          {row?.manualEntry !== null ? (
            <SelectV2
              value={row?.uomDetails?.selectedUom}
              onChange={handleInputChange}
              options={uoms?.map((u) => ({ label: u, value: u }))}
            />
          ) : (
            <p>{row?.uomDetails?.selectedUom || '-'}</p>
          )}
        </Td>
        <Td>
          <Input
            type="number"
            name="units"
            min={1}
            value={row?.units}
            onChange={handleInputChange}
            inputClassname="!h-[34px] min-w-[60px]"
          />
        </Td>
        <Td className={'flex gap-2 items-center'}>
          <SelectV2
            name="template"
            value={row?.template}
            onChange={handleInputChange}
            className="min-w-[140px]"
            options={allTemplates.map((el) => ({
              label: el?.name,
              value: el?._id,
            }))}
            disabled={!['subAssembly', 'inhouse'].includes(row?.category)}
          />
          <Button
            className={'!w-full'}
            color="orange"
            onClick={() =>
              setModalFor({ open: true, for: 'pf', rowId: row?.rowId })
            }
            title="Change Flow"
            disabled={!row?.template}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </Td>
        {defaults?.defaultParam?.bomColumns?.map((el) => (
          <Td key={el?.title}>
            <Input
              inputClassname="!h-[34px] min-w-[120px]"
              name={el?.title}
              value={row?.addtionalFields?.[el?.title]}
              onChange={handleAdditonalColumnsInput}
            />
          </Td>
        ))}
        <Td>
          <Button
            onClick={() =>
              setModalFor({ open: true, for: 'media', rowId: row?.rowId })
            }
            className={'relative !overflow-visible'}
            title="Add Media"
          >
            <Paperclip className="h-4 w-4" />
            {row?.media?.length > 0 && (
              <p className="absolute bg-red-500 text-white -top-1 -right-1 w-4 aspect-square rounded-full flex justify-center items-center">
                {row?.media?.length}
              </p>
            )}
          </Button>
        </Td>
        <Td>
          <Button
            onClick={() =>
              setModalFor({ open: true, for: 'rm', rowId: row?.rowId })
            }
            className={'relative !overflow-visible'}
            title="Add RM"
          >
            <ShoppingCart className="h-4 w-4" />
            {row?.rawMaterials?.length > 0 && (
              <p className="absolute bg-red-500 text-white -top-1 -right-1 w-4 aspect-square rounded-full flex justify-center items-center">
                {row?.rawMaterials?.length}
              </p>
            )}
          </Button>
        </Td>
        <Td className={'flex gap-2 items-center'}>
          {row?.category === 'subAssembly' && (
            <Button onClick={handleAddSubitem} title="Add Subitems">
              <ListTree className="h-4 w-4" />
            </Button>
          )}

          <Button color="red" onClick={handleRemove} title="Remove Item">
            <Trash className="h-4 w-4" />
          </Button>
        </Td>
      </Row>
      {bomsToMap?.length > 0 ? (
        <>
          {bomsToMap.map((item) => (
            <BomRow
              key={item?._id || item?.tempId}
              row={item}
              boms={boms}
              setBoms={setBoms}
              variantIndex={variantIndex + 1}
              allProducts={allProducts}
              allParts={allParts}
              uoms={uoms}
              allTemplates={allTemplates}
              setModalFor={setModalFor}
              setDeletedBoms={setDeletedBoms}
              setDeletedMedias={setDeletedMedias}
            />
          ))}
        </>
      ) : null}
    </>
  );
}
