import Modal from '../global/components/Modal.jsx';
import { ReactComponent as Briefcase } from '../../assets/svgs/briefcase.svg';
import Button from '../global/components/Button.jsx';
import NewSelect from '../global/components/NewSelect.jsx';
import Input from '../global/components/Input.jsx';
import Select from '../global/components/Select.jsx';

function AddBatchModal({ onClose, onSubmit }) {
  return (
    <Modal
      title={'Add New Batch'}
      svg={<Briefcase className="h-8 w-8" />}
      onCloseModal={onClose}
      onSubmit={onSubmit}
    >
      {() => (
        <div className="flex flex-col gap-1">
          <h1>Add Details</h1>
          <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
            <div className="flex flex-col mt-5">
              {/* GENERATION */}
              <label className="mb-1 font-semibold text-[#667085]">
                Add Batch
              </label>
              {/* {!isAnyButtonClicked && allBatches.length > 0 && ( */}
              <div className="flex space-x-4">
                <Button
                  type="button"
                  //   onClick={handleExistingBatch}
                >
                  Add Existing Batch
                </Button>
                <Button
                  type="button"
                  //    onClick={handleNewBatch}
                >
                  Create New Batch
                </Button>
              </div>
              {/* )} */}

              {/* {isAnyButtonClicked &&
                !isNewCreateBatchClicked &&
                isExistingClicked &&
                allBatches.length > 0 && ( */}
              <div className="flex items-center pr-3">
                <NewSelect
                  placeholder="Select Batch"
                  //   options={allBatches?.map((el) => ({
                  //     name: el,
                  //     value: el,
                  //   }))}
                  //   onChange={(e) => {
                  //     setSelectetBatch(e.target.value);
                  //     setSelectedLot(allLots[e.target.value]);

                  //     setFormData((prev) => ({
                  //       ...prev,

                  //       batchNo: e.target.value,
                  //     }));
                  //   }}
                  //   value={selectedBatch}
                />
              </div>
              {/* )} */}

              {/* {((isAnyButtonClicked &&
                isNewCreateBatchClicked &&
                !isExistingClicked) ||
                allBatches.length < 1) && (
                <div className="flex items-center pr-3">
                  {Object.keys(bacthNoFormatComponents || {}).map((elem) => {
                    return bacthNoFormatComponents[elem];
                  })}
                </div>
              )} */}
            </div>
            {/* {((isAnyButtonClicked &&
              (isNewCreateBatchClicked || isExistingClicked)) ||
              allBatches.length < 1) && (
              <div className="flex flex-col mt-5">
                <label className="mb-1 font-semibold text-[#667085]">
                  Lot Number
                </label>

                <div className="flex items-center pr-3">
                  {Object.keys(lotNoFormatComponents).map((elem) => {
                    return lotNoFormatComponents[elem];
                  })}
                </div>
              </div>
            )} */}
            <div className="flex flex-col mt-5">
              <label className="mb-1 font-semibold text-[#667085]">
                Manufacturing Date
              </label>
              <Input
                type="datetime-local"
                // value={formData?.manufacturingDate}
                // className={inputStyling}
                // onChange={(e) =>
                //   setFormData((prev) => ({
                //     ...prev,
                //     manufacturingDate:
                //       e.target.value.split('T')[0] >
                //       new Date().toISOString().split('T')[0]
                //         ? new Date().toISOString()
                //         : e.target.value,
                //   }))
                // }
              />
            </div>
            <div className="flex flex-col mt-5">
              <label className="mb-1 font-semibold text-[#667085]">Type</label>
              <Select
                // onChange={(e) => {
                //   setInType(e.target.value);
                //   setFormData((prev) => ({
                //     ...prev,
                //     vendor: null,
                //     store: null,
                //     inName: null,
                //     inType: e.target.value,
                //   }));
                // }}
                // value={inType}
                options={[
                  { value: 'store', label: 'In-house' },
                  { value: 'vendor', label: 'Out-Source' },
                ]}
              />
            </div>
            <div className="flex flex-col mt-5">
              <label className="mb-1 font-semibold text-[#667085]">
                {/* {inType} */}
                Label
              </label>
              <Select
                // filterOption={filterOptions}
                // onInputChange={(e) => setSearchTerm(e)}
                // options={[
                //   { value: '+', label: `+ Add ${inType}` },
                //   ...(inType === 'vendor'
                //     ? formData?.inventory?.type === 'Part'
                //       ? formData?.selectedItem?.vendors?.map((vendor) => ({
                //           value: vendor?._id,
                //           label: vendor?.name,
                //         })) || []
                //       : vendorsData?.vendors?.items?.map((e) => ({
                //           label: e.name,
                //           value: e._id,
                //         })) || []
                //     : formData?.inventory?.type !== 'Part'
                //       ? formData?.selectedItem?.stores?.map((store) => ({
                //           value: store?._id,
                //           label: store?.name,
                //         })) || []
                //       : storesData?.stores?.items?.map((e) => ({
                //           label: e.name,
                //           value: e._id,
                //         })) || []),
                // ]}
                // value={formData[inType]}
                // onChange={(e) => {
                //   if (e?.target?.value === '+') {
                //     const route = `/settings/inventory/${
                //       inType === 'vendor'
                //         ? 'masters?selectedTab=vendors'
                //         : 'masters?selectedTab=stores'
                //     }`;
                //     navigate(route);
                //   }
                //   setFormData((prev) => ({
                //     ...prev,
                //     [inType]: e.target.value,
                //     inName: e.label,
                //   }));
                // }}
                styles={{
                  valueContainer: () => ({
                    display: 'flex',
                    alignItems: 'center',
                  }),
                  menu: () => ({
                    width: '100%',
                    zIndex: 50,
                    backgroundColor: 'white',
                    position: 'absolute',
                    overflowY: 'scroll',
                  }),
                }}
              />
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
}

export default AddBatchModal;
