import {
  useGetAllPaginatedInPagesQuery,
  useDeleteManyPendingInPagesMutation,
  useDeletePendingInPageMutation,
} from '../../slices/pendingInpageApiSlice';
import { useState } from 'react';
import useDebounceValue from '../../hooks/useDebounceValue';
import Pagination from '../global/components/Pagination';
import { Table, Tag, Button, Input, Spin, Space } from 'antd';
import { Plus, Search, Trash2 } from 'lucide-react';
import { getLocalDate } from '../../helperFunction';
import Filter from '../global/components/Filter';
import { toast } from 'react-toastify';
import { customConfirm } from '../../utils/customConfirm';
import { useContext } from 'react';
import { Store } from '../../store/Store';
import { useAddInPageMutation } from '../../slices/inPageApiSlice';
import PendingInpageSideBar from './PendingInpageSideBar';
import { formatFormDataToPreventLossOfDataInSerialization } from '../../utils/serializationHandling';

const PendingStockIn = () => {
  const [filterHeadingValue, setFilterHeadingValue] = useState('');
  const [createInpage] = useAddInPageMutation();
  const { state } = useContext(Store);
  const [openSideBar, setOpenSideBar] = useState(false);
  const [sideBarData, setSideBarData] = useState({});
  const [filterValue, setFilterValue] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const debounceSearch = useDebounceValue(searchTerm || '');
  const [limit, setLimit] = useState(25);
  const [page, setPage] = useState(1);
  const { data: pendingInpages, isLoading } = useGetAllPaginatedInPagesQuery({
    page,
    limit,
    filter_name: filterHeadingValue,
    filter_value: filterValue,
    searchTerm: debounceSearch,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [deleteManyInPage, { isLoading: isDeleting }] =
    useDeleteManyPendingInPagesMutation();
  const [deletePendingInpage] = useDeletePendingInPageMutation();

  const getStatusTagColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'gold';
      case 'approved':
        return 'green';
      case 'rejected':
        return 'red';
      default:
        return 'default';
    }
  };

  const filterOptions = [
    {
      label: 'Status',
      value: 'status',
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  const handleDeleteSelected = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to delete selected rows?',
      'error'
    );
    if (!confirm) return;
    if (!state?.user?.deletePendingStockIn) {
      toast.error('You do not have permission to delete pending stock in');
      return;
    }
    const res = await deleteManyInPage({ ids: selectedRowKeys });
    if (res.data) {
      toast.success(res.data.msg);
      setSelectedRowKeys([]);
    }
  };

  const createGRN = async (data) => {
    const confirm = await customConfirm(
      'Are you sure you want to create GRN?',
      'success'
    );
    if (!confirm) return;
    const { _createdAt, _updatedAt, ...dataCopy } = data;
    const formattedData = formatFormDataToPreventLossOfDataInSerialization(
      dataCopy,
      'inspectionData'
    );

    const finalFormatted = formatFormDataToPreventLossOfDataInSerialization(
      formattedData,
      'inspectionFormData'
    );
    const res = await createInpage({
      ...finalFormatted,
      grnid: data?.grnid?._id,
      approvedOrRejectBy: data?.approvedOrRejectBy?._id,
      store: data?.store?._id,
      pendingStockIn: true,
    });
    if (!res.error) {
      const res2 = await deletePendingInpage(data?._id);
      if (!res2.error) {
        toast.success('GRN created successfully');
      }
    }
  };

  const columns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      width: 70,
      render: (_, __, index) => <span>{(page - 1) * limit + index + 1}</span>,
    },
    {
      title: 'Inventory Name',
      dataIndex: ['inventory', 'name'],
      key: 'name',
      // sorter: (a, b) => a.inventory.name.localeCompare(b.inventory.name),
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'date',
      render: (date) => getLocalDate(date),
      // sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusTagColor(status)}>{status || 'Unknown'}</Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            disabled={record.status !== 'approved'}
            icon={<Plus size={16} />}
            onClick={async (e) => {
              e.stopPropagation();
              await createGRN(record);
            }}
            className="flex items-center"
          >
            Create GRN
          </Button>
        </Space>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <PendingInpageSideBar
        openSideBar={openSideBar}
        setOpenSideBar={setOpenSideBar}
        data={sideBarData}
      />

      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
          <div className="w-full sm:w-1/2">
            <Input
              placeholder="Search inventory..."
              prefix={<Search size={16} className="text-gray-400" />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="w-full sm:w-auto flex flex-wrap gap-x-2 items-center">
            <Filter
              headingfilteroptions={filterOptions}
              secondfilterdata={pendingInpages?.results}
              setSelectedHeading={setFilterHeadingValue}
              setSelectedValue={setFilterValue}
            />
            <Button
              danger
              type="primary"
              icon={<Trash2 size={16} />}
              disabled={selectedRowKeys.length === 0}
              loading={isDeleting}
              className="flex items-center"
              onClick={handleDeleteSelected}
            >
              Delete ({selectedRowKeys.length})
            </Button>
          </div>
        </div>
      </div>

      <Table
        rowSelection={rowSelection}
        dataSource={pendingInpages?.results || []}
        columns={columns}
        rowKey="_id"
        pagination={false}
        size="middle"
        className="bg-white rounded-md shadow-sm"
        rowClassName="hover:bg-gray-50 transition-colors"
        loading={isLoading}
        locale={{
          emptyText: (
            <div className="py-8 flex flex-col items-center">
              <p className="text-gray-500">No pending stock in found</p>
            </div>
          ),
        }}
        onRow={(record) => ({
          onClick: () => {
            setSideBarData(record);
            setOpenSideBar(true);
          },
        })}
      />

      <div className="mt-6">
        <Pagination
          limit={limit}
          page={page}
          totalPages={pendingInpages?.totalPages}
          totalResults={pendingInpages?.totalResults}
          setPage={setPage}
          setLimit={setLimit}
          className="w-full"
        />
      </div>
    </div>
  );
};

export default PendingStockIn;
