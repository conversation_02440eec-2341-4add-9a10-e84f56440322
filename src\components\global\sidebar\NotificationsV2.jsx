import { Popover, Tab, Transition } from '@headlessui/react';
import { Badge } from 'antd';
import { Fragment, useContext, useEffect, useRef, useState } from 'react';
import { FiBell } from 'react-icons/fi';
import addNotification from 'react-push-notification';
import { toast } from 'react-toastify';
import {
  useGetNewNotificationQuery,
  useClearAllNewNotficationMutation,
  useMarkAllAsReadNewNotficationMutation,
} from '../../../slices/newNotificationApiSlice';
import {
  // useDeleteAllNotificationsMutation,
  useGetAllReadNotificationQuery,
  useGetAllUnReadNotificationQuery,
  // useMarkAllAsReadMutation,
} from '../../../slices/notificationApiSlice';
import { Store } from '../../../store/Store';
import NotificationItem from './NotificationItem';
import NotificationItemV2 from './NotificationItemV2';

function CustomNotificationToast({ title, desc }) {
  return (
    <div>
      <p className="border-b border-white">{title}</p>
      <p className="text-xs p-2">{desc}</p>
    </div>
  );
}

const NotificationsV2 = ({ showSideBar }) => {
  const { socket, state } = useContext(Store);
  const isMounted = useRef(false);

  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(5); //eslint-disable-line
  const [readPage, setReadPage] = useState(1);
  const [readLimit, setReadLimit] = useState(5); //eslint-disable-line

  // const [markAllAsRead] = useMarkAllAsReadMutation();
  // const [deleteAllNotifications] = useDeleteAllNotificationsMutation();
  const { data: notificationsData } = useGetAllUnReadNotificationQuery(
    {
      page,
      limit,
    },
    { skip: !page || !limit, refetchOnMountOrArgChange: true }
  );

  const [markAllAsReadNewNotification] =
    useMarkAllAsReadNewNotficationMutation();
  const [clearAllNewNotification] = useClearAllNewNotficationMutation();

  const { data: notificationsReadData } = useGetAllReadNotificationQuery(
    {
      page: readPage,
      limit: readLimit,
    },
    { skip: !readPage || !readLimit, refetchOnMountOrArgChange: true }
  );

  const [selectedTab, setSelectedTab] = useState(0);

  const { data: newNotificationsData, refetch: refetchNewNotification } =
    useGetNewNotificationQuery(
      {
        page,
        limit,
        type: selectedTab === 0 ? 'unRead' : 'read',
      },
      { skip: !page || !limit, refetchOnMountOrArgChange: true }
    );

  let unreadNotificationsLength;
  if (selectedTab === 0) {
    unreadNotificationsLength = newNotificationsData?.results?.length;
  }

  const [notifications, setNotifications] = useState([]);
  const [readNotifications, setReadNotifications] = useState([]);
  const [realTimeUser, setRealTimeUser] = useState(
    () => JSON.parse(localStorage.getItem('user'))?.user
  );

  useEffect(() => {
    const handleStorageChange = () => {
      setRealTimeUser(JSON.parse(localStorage.getItem('user'))?.user);
    };

    window.addEventListener('storage', handleStorageChange); // Listen to storage changes

    return () => {
      window.removeEventListener('storage', handleStorageChange); // Clean up
    };
  }, []);

  useEffect(() => {
    if (!socket) return;
    if (!isMounted.current && realTimeUser) {
      isMounted.current = true;
      socket.on('notification', (data) => {
        let showNotif = false;
        let viewTiles = [];
        if (realTimeUser?.kanbanFilter?.[0]?.label) {
          for (let i of realTimeUser?.kanbanFilter) viewTiles?.push(i?.label);
        } else {
          viewTiles = realTimeUser?.kanbanFilter;
        }

        for (let i of data?.payload?.notification?.columns) {
          let hasAccess = false;
          for (let j of viewTiles) {
            if (j == i) {
              showNotif = true;
              hasAccess = true;
              break;
            }
            if (hasAccess) {
              break;
            }
          }
        }
        if (
          data?.payload?.notification?.profileId === realTimeUser?.profileId &&
          realTimeUser?._id !== data?.payload?.notification?.senderUser &&
          showNotif
        ) {
          setNotifications((prev) => [
            { ...data?.payload?.notification },
            ...prev,
          ]);
          let page;
          if (
            data?.payload?.notification?.data?.order?.currentPage?.length > 1
          ) {
            for (let pageLabel of data?.payload?.notification?.data?.order
              ?.currentPage) {
              page = page + `${pageLabel}, `;
            }
          } else {
            page = `${data?.payload?.notification?.data?.order?.currentPage?.[0]}`;
          }
          addNotification({
            title: 'Tile Shift',
            // subtitle: 'This is a subtitle',
            message: `${data?.payload?.notification?.senderName} shifted order ${data?.payload?.notification?.data?.order?.taskId} to ${page}`,
            // theme: 'darkblue',
            native: true, // when using native, your OS will handle theming.
            duration: 8000,
          });
        }
      });
      socket.on('departmentalOrderAssignUser', (data) => {
        if (
          Array.isArray(data?.payload?.forUser)
            ? data?.payload?.forUser?.includes(realTimeUser?._id)
            : data?.payload?.forUser === realTimeUser?._id
        ) {
          setNotifications((prev) => [{ ...data?.payload }, ...prev]);
          addNotification({
            title: 'Tile assigned to you',
            // subtitle: 'This is a subtitle',
            message: `Card ${data?.payload?.data?.taskId} assigned to you`,
            // theme: 'darkblue',
            native: true, // when using native, your OS will handle theming.
            duration: 8000,
          });
        }
      });
      socket.on(realTimeUser?.profileId, (data) => {
        const payload = JSON.parse(data);

        if (payload?.userIds?.includes(realTimeUser?._id)) {
          if (refetchNewNotification) refetchNewNotification();
          if (realTimeUser?._id !== payload?.userId)
            toast?.[payload?.type || 'info'](
              <CustomNotificationToast
                title={payload?.title}
                desc={payload?.description}
              />,
              {
                toastId: payload?._id,
              }
            );
        }
      });
    }
  }, [socket, realTimeUser, refetchNewNotification]);

  useEffect(() => {
    if (notificationsData) {
      setNotifications((prev) => [...prev, ...notificationsData]);
    }
  }, [notificationsData]);

  useEffect(() => {
    if (notificationsReadData) {
      setReadNotifications((prev) => [...prev, ...notificationsReadData]);
    }
  }, [notificationsReadData]);

  // useEffect(() => {
  //   if (selectedTab === 0) {
  //     setNotifications([]);
  //     setPage(1);
  //     refetchUnread();
  //   } else {
  //     setReadNotifications([]);
  //     setReadPage(1);
  //     refetchRead();
  //   }
  // }, [selectedTab]);

  // const markAllAsReadHandler = async () => {
  //   await markAllAsRead({ data: { user: state?.user._id } }).unwrap();
  // };

  // const deleteAllNotificationsFunc = async () => {
  //   //Passing Current User Id
  //   const res = await deleteAllNotifications({ userId: state?.user._id });
  //   if (res) {
  //     toast.success('All Notification Deleted');
  //     setReadNotifications([]);
  //     setNotifications([]);
  //   }
  // };

  const updatePostDelete = (id) => {
    setNotifications((prev) => prev?.filter((notif) => notif?._id !== id));
    setReadNotifications((prev) => prev?.filter((notif) => notif?._id !== id));
  };

  return (
    <div>
      <Popover
        as="div"
        className={`relative z-[100] aspect-square w-fit flex justify-center ${
          showSideBar ? 'h-3/5' : 'h-1/5 max-h-[25px] mx-auto'
        }`}
      >
        <Popover.Button className="outline-none relative">
          <Badge count={unreadNotificationsLength}>
            <FiBell color={'white'} className="text-2xl cursor-pointer" />
          </Badge>
        </Popover.Button>
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Popover.Panel className="-left-[390px] p-4 border-[1px] top-10 absolute min-w-[500px] rounded-lg overflow-y-scroll shadow-2xl no-scrollbar bg-white h-[530px]">
            <div className="flex items-center justify-between">
              <h3>Notifications</h3>
              <div className="flex items-center gap-3">
                <a
                  className="text-[12px] text-blue-400 cursor-pointer"
                  onClick={markAllAsReadNewNotification}
                >
                  Mark All as Read
                </a>
                <a
                  className="text-[12px] text-blue-400 cursor-pointer"
                  onClick={clearAllNewNotification}
                >
                  Clear All
                </a>
              </div>
            </div>
            <Tab.Group>
              <Tab.List className="bg-slate-100 p-[6px] rounded-[5px] w-fit text-sm my-2">
                <Tab
                  style={{
                    boxShadow: `${selectedTab === 0 ? 'rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px' : 'none'}`,
                    backgroundColor: `${selectedTab === 0 ? 'white' : ''}`,
                    color: `${selectedTab === 0 ? 'rgb(96 165 250)' : 'rgb(100 116 139)'}`,
                  }}
                  className={`py-2 px-4 rounded-[5px] font-semibold`}
                  onClick={() => setSelectedTab(0)}
                >
                  Unread
                </Tab>
                <Tab
                  style={{
                    boxShadow: `${selectedTab === 1 ? 'rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px' : 'none'}`,
                    backgroundColor: `${selectedTab === 1 ? 'white' : ''}`,
                    color: `${selectedTab === 1 ? 'rgb(96 165 250)' : 'rgb(100 116 139)'}`,
                  }}
                  className={`py-2 px-4 rounded-[5px] font-semibold`}
                  onClick={() => setSelectedTab(1)}
                >
                  Read
                </Tab>
              </Tab.List>
              <Tab.Panels>
                <Tab.Panel className="mt-4">
                  {newNotificationsData?.results?.map((notif, idx) => {
                    return (
                      <NotificationItemV2
                        key={idx}
                        notif={notif}
                        user={state?.user}
                      />
                    );
                  })}
                  {notifications?.map((notif, idx) => {
                    return (
                      <NotificationItem
                        key={idx}
                        notif={notif}
                        user={state?.user}
                        updatePostDelete={updatePostDelete}
                      />
                    );
                  })}
                  <p
                    className="py-1 px-2 text-sm text-center my-2 mx-auto w-fit text-slate-300 border-[1px] rounded-[10px] border-solid border-slate-300 cursor-pointer hover:bg-slate-300 hover:text-white"
                    onClick={() => setPage((prev) => prev + 1)}
                  >
                    Load More
                  </p>
                </Tab.Panel>
                <Tab.Panel>
                  {newNotificationsData?.results?.map((notif, idx) => {
                    return (
                      <NotificationItemV2
                        key={idx}
                        notif={notif}
                        user={state?.user}
                      />
                    );
                  })}
                  {readNotifications?.map((notif, idx) => {
                    return (
                      <NotificationItem
                        key={idx}
                        notif={notif}
                        user={state?.user}
                        updatePostDelete={updatePostDelete}
                      />
                    );
                  })}
                  <p
                    className="py-1 px-2 text-sm text-center my-2 mx-auto w-fit text-slate-300 border-[1px] rounded-[10px] border-solid border-slate-300 cursor-pointer hover:bg-slate-300 hover:text-white"
                    onClick={() => setReadPage((prev) => prev + 1)}
                  >
                    Load More
                  </p>
                </Tab.Panel>
              </Tab.Panels>
            </Tab.Group>
          </Popover.Panel>
        </Transition>
      </Popover>
    </div>
  );
};

export default NotificationsV2;
