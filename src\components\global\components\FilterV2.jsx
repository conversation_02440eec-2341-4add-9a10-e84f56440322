import {
  CloseOutlined,
  FilterFilled,
  FilterOutlined,
  LeftOutlined,
  PlusOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { Button, DatePicker, Popover, Tooltip } from 'antd';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import MultiSelect from './MultiSelect';
import SelectV2 from './SelectV2';

const transformFilters = (filters, filterConfig) => {
  const transformed = [];

  filterConfig.forEach(({ key, path, isObjectId, type }) => {
    const val = filters[key];

    if (type === 'date' && Array.isArray(val) && val.length === 2) {
      transformed.push({
        path,
        value: [
          new Date(new Date(val[0]).setHours(0, 0, 0, 0)),
          new Date(new Date(val[1]).setHours(23, 59, 59, 999)),
        ],
        isDate: true,
      });
    } else if (Array.isArray(val)) {
      if (val.length > 0) {
        transformed.push({
          path,
          value: val.map((item) => item.value),
          ...(isObjectId ? { isObjectId: true } : {}),
        });
      }
    } else if (val) {
      transformed.push({
        path,
        value: val,
        ...(isObjectId ? { isObjectId: true } : {}),
      });
    }
  });

  return transformed;
};

export const FilterV2 = ({ config, showFilters, setFilters }) => {
  const [activeFilterKey, setActiveFilterKey] = useState(null);
  const scrollContainerRef = useRef(null);
  const [showScrollArrows, setShowScrollArrows] = useState(false);
  const [showLeftScroll, setShowLeftScroll] = useState(false);
  const [showRightScroll, setShowRightScroll] = useState(false);
  const [localFilter, setLocalFilters] = useState({});

  const checkScroll = () => {
    if (!scrollContainerRef.current || !showScrollArrows) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setShowLeftScroll(scrollLeft > 0);
    setShowRightScroll(scrollLeft < scrollWidth - clientWidth - 10);
  };

  useEffect(() => {
    // Only check scroll when arrows are visible
    if (showScrollArrows) {
      checkScroll();
    }

    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', checkScroll);
      window.addEventListener('resize', checkScroll);
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', checkScroll);
        window.removeEventListener('resize', checkScroll);
      }
    };
  }, [showScrollArrows, activeFilterKey]); // eslint-disable-line

  const scroll = (direction) => {
    if (!scrollContainerRef.current) return;
    const scrollAmount = direction === 'right' ? 150 : -150;
    scrollContainerRef.current.scrollBy({
      left: scrollAmount,
      behavior: 'smooth',
    });
  };

  const getValueLabel = (field, value) => {
    if (!value) return null;

    if (field.type === 'select') {
      const option = field.options.find((opt) => opt.value === value);
      return option ? option.label : value;
    }

    if (field.type === 'multiSelect') {
      if (!Array.isArray(value) || value.length === 0) return null;
      return `${value.length} selected`;
    }

    if (field.type === 'date' && Array.isArray(value) && value.length === 2) {
      return 'Date range';
    }

    return String(value);
  };

  const setFilterFunc = (key, value) => {
    setLocalFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
    const transformedFilters = transformFilters(
      { ...localFilter, [key]: value },
      config
    );
    setFilters(encodeURIComponent(JSON.stringify(transformedFilters)));
  };

  const renderFilterContent = (field) => {
    const value = localFilter?.[field.key];

    if (field.type === 'select') {
      return (
        <SelectV2
          placeholder={field.label}
          style={{ width: 220 }}
          options={field.options}
          value={value}
          onChange={(e) => {
            setFilterFunc(field.key, e.target.value);
            setActiveFilterKey(null);
          }}
        />
      );
    }

    if (field.type === 'multiSelect') {
      return (
        <MultiSelect
          placeholder={field.label}
          style={{ width: 220 }}
          options={field.options}
          value={value}
          onChange={(e) => {
            setFilterFunc(field.key, e.target.value);
            setActiveFilterKey(null);
          }}
        />
      );
    }

    if (field.type === 'date') {
      return (
        <DatePicker.RangePicker
          onChange={(val) => {
            setFilterFunc(field.key, val);
            setActiveFilterKey(null);
          }}
          value={value}
          placeholder={[`${field.label} From`, `${field.label} To`]}
        />
      );
    }

    return null;
  };

  const isFilterActive = (key) => {
    const value = localFilter?.[key];
    return (
      value !== undefined &&
      value !== null &&
      (!Array.isArray(value) || value.length > 0) &&
      value !== ''
    );
  };

  const activeFilterCount = Object.keys(localFilter || {}).filter((key) =>
    isFilterActive(key)
  ).length;

  const handleClearFilters = () => {
    setLocalFilters({});
    setActiveFilterKey(null);
    setFilters([]);
    // Keep scroll arrows visible even after clearing filters
    // This ensures arrows remain visible when height is reduced
  };

  return (
    <AnimatePresence>
      {showFilters && (
        <motion.div
          key="filter-bar"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="flex flex-col gap-4 bg-zinc-50 px-4 shadow-sm"
        >
          <div className="relative flex justify-between items-center">
            <div className="w-3/4 relative">
              {showScrollArrows && showLeftScroll && (
                <div
                  className="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-6 h-6 flex items-center justify-center rounded-full bg-white shadow-md cursor-pointer"
                  onClick={() => scroll('left')}
                >
                  <LeftOutlined className="text-gray-600 text-xs" />
                </div>
              )}

              <div
                ref={scrollContainerRef}
                className="flex gap-2 items-center overflow-x-auto scrollbar-hide py-2 px-1"
                style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
              >
                {config.map((field) => {
                  const isActive = isFilterActive(field.key);
                  const isOpen = activeFilterKey === field.key;
                  const valueLabel = getValueLabel(
                    field,
                    localFilter?.[field.key]
                  );

                  return (
                    <Popover
                      key={field.key}
                      content={renderFilterContent(field)}
                      title={field.label}
                      trigger="click"
                      open={isOpen}
                      onOpenChange={(open) => {
                        setActiveFilterKey(open ? field.key : null);
                        // Show scroll arrows only when a filter is clicked
                        setShowScrollArrows(open);
                        if (open) {
                          setTimeout(checkScroll, 100);
                        }
                      }}
                      placement="bottom"
                    >
                      <div
                        className={`h-8 rounded-full px-2 py-1 flex items-center gap-1.5 cursor-pointer transition-all duration-200
                              ${
                                isActive
                                  ? isOpen
                                    ? 'bg-blue-100 border border-blue-300 shadow-sm'
                                    : 'bg-blue-50 border border-blue-200'
                                  : 'bg-white border border-gray-200 hover:border-gray-300'
                              }`}
                        style={{ maxWidth: isOpen ? '240px' : 'fit-content' }}
                      >
                        <div
                          className={`flex items-center justify-center w-4 h-4 rounded-full flex-shrink-0
                                    ${isOpen ? 'bg-blue-200' : isActive ? 'bg-blue-100' : 'bg-gray-200'}`}
                        >
                          {isOpen ? (
                            <CloseOutlined
                              style={{ fontSize: '10px' }}
                              className="text-blue-700"
                            />
                          ) : (
                            <PlusOutlined
                              style={{ fontSize: '10px' }}
                              className={
                                isActive ? 'text-blue-600' : 'text-gray-600'
                              }
                            />
                          )}
                        </div>

                        <div className="flex flex-nowrap items-center">
                          <span
                            className={`text-xs font-medium whitespace-nowrap
                                      ${isActive ? 'text-blue-800' : 'text-gray-700'}`}
                          >
                            {field.label}
                          </span>

                          {isActive && (
                            <>
                              <span className="mx-1 text-gray-400">:</span>
                              <span
                                className={`text-xs truncate max-w-40 ${
                                  isActive ? 'text-blue-600' : ''
                                }`}
                              >
                                {valueLabel}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </Popover>
                  );
                })}
              </div>

              {showScrollArrows && showRightScroll && (
                <div
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-6 h-6 flex items-center justify-center rounded-full bg-white shadow-md cursor-pointer"
                  onClick={() => scroll('right')}
                >
                  <RightOutlined className="text-gray-600 text-xs" />
                </div>
              )}
            </div>

            {activeFilterCount > 0 && (
              <Button
                onClick={handleClearFilters}
                type="text"
                size="small"
                className="text-xs h-7 flex items-center ml-3 flex-shrink-0 bg-red-500 text-white"
                icon={<CloseOutlined style={{ fontSize: '10px' }} />}
              >
                Clear
              </Button>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export const FilterIcon = ({ showFilters, setShowFilters }) => {
  return (
    <Tooltip title="Filters">
      <div
        className="flex items-center cursor-pointer"
        onClick={() => setShowFilters((prev) => !prev)}
      >
        {showFilters ? (
          <FilterFilled className="text-blue-600" />
        ) : (
          <FilterOutlined className="text-gray-600" />
        )}
      </div>
    </Tooltip>
  );
};
