import { Fragment } from 'react';

import { Popover, Transition } from '@headlessui/react';
import { CiRead } from 'react-icons/ci';
import { HiDotsVertical } from 'react-icons/hi';
import { MdDeleteOutline } from 'react-icons/md';

import { getLocalDate } from '../../../helperFunction';
import {
  useDeleteNotificationMutation,
  useMarkAsReadMutation,
} from '../../../slices/notificationApiSlice';

const NotificationItem = ({ notif, user, updatePostDelete }) => {
  const [markAsRead] = useMarkAsReadMutation();
  const [deleteNotification] = useDeleteNotificationMutation();

  const generateNotficationMessage = (notif) => {
    switch (notif?.type) {
      case 'stockRequest':
        return `${notif?.senderName} sent ${notif?.data?.quantity} requested for ${notif?.data?.itemName} for store ${notif?.data?.storeRecievedIn} `;
      case 'shiftKanbanColumnNotify':
        let page; //eslint-disable-line
        if (notif?.data?.order?.currentPage?.length > 1) {
          for (let pageLabel of notif?.data?.order?.currentPage) {
            page = page + `${pageLabel}, `;
          }
        } else {
          page = `${notif?.data?.order?.currentPage?.[0]}`;
        }
        return `${notif?.senderName} shifted order ${notif?.data?.order?.taskId} to ${page}`;
      case 'departmentalOrderReminder':
        return `${notif?.data?.title} reminder on ${notif?.data?.taskId}`;
      case 'departmentalOrderAssignUser':
        return `Card ${notif?.data?.taskId} assigned to you`;
      case 'taskAssigned':
        return `Task ${notif?.taskTitle || notif?.data?.taskTitle} Assigned to you`;
      case 'taskChatNoti':
        return `Task ${notif?.taskTitle || notif?.data?.taskTitle} has a new Message sent by ${notif?.senderName}`;
      case 'taskUpdate':
        if ((notif?.status || notif?.data?.status) === 'In Progress') {
          return `Task ${notif?.taskTitle || notif?.data?.taskTitle} Started by ${notif?.senderName}`;
        } else if ((notif?.status || notif?.data?.status) === 'Completed') {
          return `Task ${notif?.taskTitle || notif?.data?.taskTitle} Completed by ${notif?.senderName}`;
        }
        break;
      case 'assignTicket': {
        const customer = notif?.customer || notif?.data?.customer;
        const priority = notif?.priority || notif?.data?.priority;

        if (customer && priority) {
          return `A New Ticket Has Been Assigned to You of Customer ${customer} with Priority ${priority}`;
        } else if (customer) {
          return `A New Ticket Has Been Assigned to You of Customer ${customer}`;
        } else if (priority) {
          return `A New Ticket Has Been Assigned to You having Priority ${priority}`;
        } else {
          return `A New Ticket Has Been Assigned to You`;
        }
      }
      case 'equipmentWarranty':
        return `Equipment ${notif?.data?.name} is about to expire on  ${getLocalDate(notif?.data?.warrantyDate)} `;
      case 'serviceDate':
        return `Service is Should be done on ${getLocalDate(notif?.data?.serviceDate)} `;
      case 'leaveUpdate':
        return `${notif?.senderName} has ${notif?.data?.status} ${notif?.data?.totalDays} days of leave`;
      case 'assignLead':
        return `Lead named ${(notif?.firstName || notif?.data?.firstName) + (notif?.lastName || notif?.data?.lastName || '')}  have Id ${notif?.leadId || notif?.data?.leadId} Assigned To You at Stage ${notif?.stage || notif?.data?.stage || 'Leads'}`;
      default:
        return 'Error 404';
    }
  };

  function getDaysBetweenDates(creationDate) {
    const date1 = new Date(creationDate);
    const date2 = new Date();

    // Get the time difference in milliseconds
    const differenceInTime = date2.getTime() - date1.getTime();

    // Convert milliseconds to days
    const millisecondsInDay = 1000 * 60 * 60 * 24;
    const daysDifference = Math.floor(differenceInTime / millisecondsInDay);

    return daysDifference;
  }

  let msg = generateNotficationMessage(notif);
  let createdDate = new Date(notif?.createdAt);
  let daysDifference = getDaysBetweenDates(notif?.createdAt);

  let dayArray = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];
  let dateToShow;
  if (daysDifference < 7) {
    let day = dayArray[createdDate?.getDay()];
    let time = createdDate?.toLocaleTimeString('en-US');

    dateToShow = `${day}, ${time}`;
  } else {
    dateToShow = createdDate?.toDateString();
  }

  const markAsReadHandler = async (id) => {
    await markAsRead({ id, data: { user: user._id } }).unwrap();
  };

  const deleteNotificationHandler = async (id) => {
    const res = await deleteNotification({ id }).unwrap();
    if (res) {
      updatePostDelete(id);
    }
  };

  return (
    <div className="flex items-center gap-2 mb-4 relative">
      <div className="rounded-full py-2 px-4 bg-slate-200 text-slate-400">
        {notif?.senderName?.charAt(0)?.toUpperCase()}
      </div>
      <div className="mt-2 pb-2">
        <p className="text-sm">{msg}</p>
        <p className="text-[12px]">{dateToShow}</p>
      </div>
      <Popover
        as="div"
        className={`relative ml-auto aspect-square w-fit flex justify-center`}
      >
        <Popover.Button className="outline-none relative ml-auto">
          <HiDotsVertical className="ml-auto text-lg" />
        </Popover.Button>
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Popover.Panel className="-left-[130px] border-[1px] z-20 top-10 min-w-[170px] absolute rounded-lg overflow-y-scroll shadow-2xl no-scrollbar bg-white">
            <p
              className="hover:bg-slate-100 px-4 py-2 text-sm cursor-pointer flex items-center gap-2"
              onClick={() => {
                deleteNotificationHandler(notif?._id);
              }}
            >
              <span>
                <MdDeleteOutline className="text-base" />
              </span>
              <span>Clear</span>
            </p>
            <p
              className="hover:bg-slate-100 px-4 py-2 text-sm cursor-pointer flex items-center gap-2"
              onClick={() => {
                markAsReadHandler(notif?._id);
              }}
            >
              <span>
                <CiRead className="text-base" />
              </span>
              <span>Mark As Read</span>
            </p>
          </Popover.Panel>
        </Transition>
      </Popover>
      <div></div>
    </div>
  );
};

export default NotificationItem;
