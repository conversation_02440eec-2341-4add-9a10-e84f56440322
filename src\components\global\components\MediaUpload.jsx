import {
  DeleteOutlined,
  LoadingOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { Button, Image, Upload } from 'antd';
import { useState } from 'react';
import { toast } from 'react-toastify';
import {
  useDeleteMediaMutation,
  useUploadSingleMediaMutation,
} from '../../../slices/mediaSlice';

const MediaUpload = ({ value, onChange }) => {
  const [uploadMedia] = useUploadSingleMediaMutation();
  const [deleteMedia] = useDeleteMediaMutation();
  const [uploading, setUploading] = useState(false);

  const beforeUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      toast.error('You can only upload image files!');
      return false;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      toast.error('Image must be smaller than 2MB!');
      return false;
    }

    return false;
  };

  const handleChange = async (info) => {
    const file = info.file.originFileObj || info.file;
    if (file instanceof File || file instanceof Blob) {
      try {
        setUploading(true);

        const reader = new FileReader();
        reader.onloadend = async () => {
          const imageData = {
            data: reader.result,
            name: file.name,
            type: file.type,
          };

          try {
            const response = await uploadMedia({ data: imageData }).unwrap();

            // Expected format: { _id, data, name, type, etc. }
            onChange(response);

            toast.success('Image uploaded successfully!');
          } catch (error) {
            toast.error('Failed to upload image. Please try again.');
          } finally {
            setUploading(false);
          }
        };

        reader.readAsDataURL(file);
      } catch (error) {
        toast.error('Failed to process image. Please try again.');
        setUploading(false);
      }
    }
  };

  const handleRemove = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (value?._id) {
      try {
        await deleteMedia({ id: value._id }).unwrap();
        toast.success('Image deleted successfully!');
      } catch (error) {
        toast.error('Failed to delete image. Please try again.');
        return;
      }
    }

    onChange(null);
  };
  return (
    <div className="inline-block">
      {value?.data ? (
        <div className="relative">
          <div className="w-12 h-12 rounded-lg overflow-hidden border border-gray-200">
            <Image
              src={value?.data}
              alt={value?.name}
              className="w-full h-full object-cover"
            />
          </div>
          <Button
            type="text"
            size="small"
            icon={<DeleteOutlined />}
            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full shadow-md hover:bg-red-600"
            onClick={handleRemove}
            disabled={uploading}
          />
        </div>
      ) : (
        <Upload
          accept="image/*"
          showUploadList={false}
          beforeUpload={beforeUpload}
          onChange={handleChange}
          customRequest={() => {}}
          disabled={uploading}
        >
          <div className="w-12 h-12 rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-400 flex flex-col items-center justify-center bg-gray-50 hover:bg-blue-50 cursor-pointer">
            {uploading ? (
              <LoadingOutlined className="text-blue-500 text-lg" />
            ) : (
              <UploadOutlined className="text-gray-400 text-lg" />
            )}
            <span className="text-xs text-gray-500">
              {uploading ? 'Uploading...' : 'Add'}
            </span>
          </div>
        </Upload>
      )}
    </div>
  );
};

export default MediaUpload;
