import { useState } from 'react';
import { renderElemBasedOnFormatForGoalsTable } from '../../../helperFunction';
import { Row, Td } from '../../global/components/Table';

const MultiProcessRow = ({
  dataIndex,
  rowData,
  setGoalsTables,
  process,
  goalsData,
  inputHandlerForQuantity,
  workOrderId,
  modelName,
  inputScreen,
  jobId,
}) => {
  const [expandRow, setExpandRow] = useState(false);

  return (
    <>
      <Row>
        <Td
          className={`flex gap-x-1 ${
            rowData?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }`}
        >
          <div className="flex gap-x-1">
            {renderElemBasedOnFormatForGoalsTable(
              rowData?.batchNameFormat,
              dataIndex,
              workOrderId,
              modelName,
              inputScreen,
              jobId,
              setGoalsTables,
              process._id
            )}
          </div>
        </Td>
        <Td
          className={
            rowData?.status ? 'pointer-events-none !bg-gray-disabled' : ''
          }
        >
          <input
            type="number"
            value={rowData['Batch Size']}
            disabled={rowData?.hiddenMac?.length > 0}
            onChange={(e) => inputHandlerForQuantity(e, true)}
            onScroll={(e) => e.preventDefault()}
            min={1}
            max={goalsData?.maxBatch}
            name={`Batch Size$${rowData.batchNo}`}
            className={`w-full outline-none bg-transparent ${
              +rowData['Batch Size'] > +goalsData?.maxBatch
                ? 'bg-red-primary text-white'
                : ''
            }`}
          />
        </Td>
        <Td>{rowData?.newBatchSize || ''}</Td>
        <Td
          className={rowData?.status ? '!bg-gray-disabled' : ''}
          onClick={() => setExpandRow((prev) => !prev)}
        >
          + {rowData?.subProcessData?.length}
        </Td>
      </Row>

      {expandRow && (
        <>
          {rowData?.subProcessData?.map((data, dIdx) => {
            return (
              <Row key={dIdx}>
                <Td
                  className={
                    data?.status
                      ? 'pointer-events-none !bg-gray-disabled'
                      : '!bg-blue-50'
                  }
                ></Td>
                <Td
                  className={
                    data?.status
                      ? 'pointer-events-none !bg-gray-disabled'
                      : '!bg-blue-50'
                  }
                >
                  {data?.['Batch Size']}
                </Td>
                <Td
                  className={
                    data?.status
                      ? 'pointer-events-none !bg-gray-disabled'
                      : '!bg-blue-50'
                  }
                >
                  {data?.newBatchSize || ''}
                </Td>
                <Td
                  className={
                    data?.status
                      ? 'pointer-events-none !bg-gray-disabled'
                      : '!bg-blue-50'
                  }
                >
                  {data?.process}
                </Td>
              </Row>
            );
          })}
        </>
      )}
    </>
  );
};

export default MultiProcessRow;
