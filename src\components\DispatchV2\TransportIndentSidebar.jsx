import {
  Card,
  Descriptions,
  Tag,
  Space,
  Typography,
  Button,
  Empty,
  Tabs,
  Timeline,
  Avatar,
  Divider,
  Row,
  Col,
} from 'antd';
import {
  TruckOutlined,
  PhoneOutlined,
  LinkOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  ShopOutlined,
  TeamOutlined,
  EnvironmentOutlined,
  ArrowRightOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

// imports unchanged

const TransportIndentSidebar = ({ data }) => {
  const getTimeAgo = (dateString) => {
    const updatedDate = new Date(dateString);
    const now = new Date();
    const diffMs = now - updatedDate;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    if (diffMinutes < 60) {
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    }
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    }
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  };

  const getStatusConfig = (status) => {
    const configs = {
      Created: { color: '#1890ff', bgColor: '#e6f7ff', borderColor: '#91d5ff' },
      Dispatched: {
        color: '#fa541c',
        bgColor: '#fff2e8',
        borderColor: '#ffbb96',
      },
      'On the way': {
        color: '#13c2c2',
        bgColor: '#e6fffb',
        borderColor: '#87e8de',
      },
      Delivered: {
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f',
      },
      Completed: {
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f',
      },
      pending: { color: '#fa8c16', bgColor: '#fff7e6', borderColor: '#ffd591' },
      'in-transit': {
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#91d5ff',
      },
      delivered: {
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f',
      },
      cancelled: {
        color: '#ff4d4f',
        bgColor: '#fff2f0',
        borderColor: '#ffccc7',
      },
      delayed: { color: '#fa541c', bgColor: '#fff2e8', borderColor: '#ffbb96' },
      processing: {
        color: '#722ed1',
        bgColor: '#f9f0ff',
        borderColor: '#d3adf7',
      },
      Other: { color: '#8c8c8c', bgColor: '#f5f5f5', borderColor: '#d9d9d9' },
    };
    return configs[status] || configs.Other;
  };

  const getLocationInfo = (locationData) => {
    const { type, vendorName, customerName, address } = locationData || {};

    if (type === 'Vendor') {
      return {
        type: 'Vendor',
        name: vendorName || 'Unknown Vendor',
        icon: <ShopOutlined className="text-blue-600" />,
        color: '#1890ff',
        bgColor: '#e6f7ff',
      };
    }

    if (type === 'Customer') {
      return {
        type: 'Customer',
        name: customerName || 'Unknown Customer',
        icon: <TeamOutlined className="text-green-600" />,
        color: '#52c41a',
        bgColor: '#f6ffed',
      };
    }

    return {
      type: type || 'Unknown',
      name: address || 'Not specified',
      icon: <EnvironmentOutlined className="text-gray-500" />,
      color: '#8c8c8c',
      bgColor: '#f5f5f5',
    };
  };

  const StatusTag = ({ status }) => {
    const config = getStatusConfig(status);
    return (
      <Tag
        size="small"
        className="text-[11px]"
        style={{
          color: config.color,
          backgroundColor: config.bgColor,
          borderColor: config.borderColor,
          border: `1px solid ${config.borderColor}`,
        }}
      >
        {status?.toUpperCase() || 'UNKNOWN'}
      </Tag>
    );
  };

  const LocationCard = ({ locationData, label }) => {
    const locationInfo = getLocationInfo(locationData);

    return (
      <div className="bg-gray-50 rounded-md p-3 h-full">
        <div className="text-xs font-medium text-gray-500 mb-2 uppercase tracking-wide">
          {label}
        </div>
        <div className="flex items-center space-x-2">
          <div
            className="p-1.5 rounded-md flex-shrink-0"
            style={{ backgroundColor: locationInfo.bgColor }}
          >
            {locationInfo.icon}
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-xs text-gray-500 mb-0.5">
              {locationInfo.type}
            </div>
            <div className="text-sm font-semibold text-gray-900 break-words leading-tight">
              {locationInfo.name}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const handleTrackingClick = (url) => {
    if (url) {
      window.open(url, '_blank');
    }
  };

  if (!data) {
    return (
      <div className="p-3">
        <Card size="small">
          <Empty description="No transport data available" />
        </Card>
      </div>
    );
  }

  const mainInfo = (
    <div className="space-y-4">
      {/* Header with Transport Info */}
      <Card size="small" className="border-0 shadow-sm">
        <div className="flex items-center space-x-3 mb-4 px-4 py-4">
          <Avatar
            size={24}
            className="bg-blue-100 text-blue-600"
            icon={<TruckOutlined />}
          />
          <div className="flex-1 min-w-0">
            <Title level={5} className="!m-0 !text-base break-words">
              {data.name || 'Transport Details'}
            </Title>
          </div>
        </div>

        {/* Route Information */}
        <div className="space-y-3 px-4 pb-4">
          <div className="flex items-center space-x-2 mb-3">
            <EnvironmentOutlined className="text-blue-600 text-sm" />
            <Text strong className="text-sm">
              Route Information
            </Text>
          </div>
          <div className="block md:hidden space-y-3">
            <LocationCard locationData={data?.from} label="From Location" />
            <div className="flex justify-center py-1">
              <ArrowDownOutlined className="text-blue-600" />
            </div>
            <LocationCard locationData={data?.to} label="To Location" />
          </div>

          <div className="hidden md:block">
            <Row gutter={[12, 12]} align="middle">
              <Col span={10}>
                <LocationCard locationData={data?.from} label="From Location" />
              </Col>
              <Col span={4} className="text-center">
                <ArrowRightOutlined className="text-blue-600" />
              </Col>
              <Col span={10}>
                <LocationCard locationData={data?.to} label="To Location" />
              </Col>
            </Row>
          </div>
        </div>

        <Divider className="my-4" />

        {/* Basic Details */}
        <Descriptions
          column={1}
          size="small"
          labelStyle={{ fontSize: '12px', color: '#666' }}
          contentStyle={{ fontSize: '13px' }}
          className="px-4 pb-4"
        >
          <Descriptions.Item label="Status">
            <StatusTag status={data?.status} />
          </Descriptions.Item>
          <Descriptions.Item label="Quantity">
            <Text className="break-words text-sm">
              {data?.quantity || 'Not specified'}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Created">
            <Text type="secondary" className="text-xs">
              {data?.createdAt
                ? new Date(data.createdAt).toLocaleDateString()
                : '-'}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Updated">
            <Text type="secondary" className="text-xs">
              {data?.updatedAt ? getTimeAgo(data.updatedAt) : '-'}
            </Text>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* Transport Mode Information */}
      {data?.recommendedModeOfTransport && (
        <Card
          title={
            <Space size="small">
              <TruckOutlined className="text-blue-600 text-sm" />
              <Text strong className="text-sm">
                Transport Info
              </Text>
            </Space>
          }
          size="small"
          className="border-0 shadow-sm"
          headStyle={{ padding: 0 }} // Can't do with tailwind - override below
        >
          <div className="px-4 pt-3 pb-4">
            <Descriptions
              column={1}
              size="small"
              labelStyle={{ fontSize: '12px', color: '#666' }}
              contentStyle={{ fontSize: '13px' }}
            >
              <Descriptions.Item label="Mode">
                <Tag icon={<TruckOutlined />} color="blue" size="small">
                  {data?.recommendedModeOfTransport?.mode || 'Not specified'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Vehicle">
                <Text code className="break-all text-xs">
                  {data?.recommendedModeOfTransport?.vehicleNumber ||
                    'Not assigned'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Contact">
                {data?.recommendedModeOfTransport?.contactDetails ? (
                  <div className="flex items-center space-x-2">
                    <PhoneOutlined className="text-green-600 text-xs" />
                    <Text
                      copyable={{
                        text: data?.recommendedModeOfTransport?.contactDetails,
                        tooltips: ['Copy', 'Copied!'],
                      }}
                      className="break-all text-xs"
                    >
                      {data?.recommendedModeOfTransport?.contactDetails}
                    </Text>
                  </div>
                ) : (
                  <Text type="secondary" className="text-xs">
                    Not available
                  </Text>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="Tracking">
                {data?.recommendedModeOfTransport?.trackingUrl ? (
                  <Button
                    type="link"
                    size="small"
                    icon={<LinkOutlined />}
                    onClick={() =>
                      handleTrackingClick(
                        data?.recommendedModeOfTransport.trackingUrl
                      )
                    }
                    className="!p-0 !h-auto text-left break-all text-xs whitespace-normal"
                  >
                    Track Shipment
                  </Button>
                ) : (
                  <Text type="secondary" className="text-xs">
                    Not available
                  </Text>
                )}
              </Descriptions.Item>
            </Descriptions>
          </div>
        </Card>
      )}

      {/* Remarks */}
      {data?.remarks && (
        <Card
          title={
            <Space size="small">
              <FileTextOutlined className="text-orange-600 text-sm" />
              <Text strong className="text-sm">
                Remarks
              </Text>
            </Space>
          }
          size="small"
          className="border-0 shadow-sm"
        >
          <div className="bg-orange-50 p-3 rounded-md px-4 pb-4 pt-3">
            <Text className="break-words whitespace-pre-wrap text-sm">
              {data?.remarks}
            </Text>
          </div>
        </Card>
      )}

      {/* Created By Information */}
      {data?.createdBy && (
        <Card
          title={
            <Space size="small">
              <UserOutlined className="text-purple-600 text-sm" />
              <Text strong className="text-sm">
                Created By
              </Text>
            </Space>
          }
          size="small"
          className="border-0 shadow-sm"
        >
          <div className="flex items-center space-x-3 px-4 py-4">
            <Avatar
              size={24}
              className="bg-purple-100 text-purple-600"
              icon={<UserOutlined />}
            />
            <div>
              <Text strong className="block text-sm">
                {data?.createdBy?.name || 'Unknown User'}
              </Text>
              <Text type="secondary" className="text-xs">
                {data?.createdBy?.email || 'No email provided'}
              </Text>
            </div>
          </div>
        </Card>
      )}
    </div>
  );

  const progressHistory =
    data?.progressHistory && data?.progressHistory?.length > 0 ? (
      <Card className="border-0 shadow-sm" size="small">
        <div className="flex items-center space-x-2 mb-4 px-4 py-4">
          <ClockCircleOutlined className="text-blue-600 text-sm" />
          <Text strong className="text-sm">
            Progress Timeline
          </Text>
        </div>
        <Timeline
          mode="left"
          size="small"
          items={data?.progressHistory?.map((item, index) => {
            const config = getStatusConfig(item.status);
            return {
              key: index,
              color: config.color,
              dot: (
                <ClockCircleOutlined
                  style={{ color: config.color, fontSize: '12px' }}
                />
              ),
              label: (
                <div className="text-right">
                  <div className="text-xs text-gray-500 mb-0.5">
                    {new Date(item?.updatedAt).toLocaleDateString()}
                  </div>
                  <div className="text-xs text-gray-400">
                    {new Date(item?.updatedAt).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </div>
                  {item.updatedBy?.name && (
                    <div className="text-xs text-gray-500 mt-1 flex items-center justify-end space-x-1">
                      <UserOutlined style={{ fontSize: '10px' }} />
                      <span>{item?.updatedBy?.name}</span>
                    </div>
                  )}
                </div>
              ),
              children: (
                <div className="space-y-2 px-4 pb-4">
                  <Tag
                    size="small"
                    className="text-[11px]"
                    style={{
                      color: config.color,
                      backgroundColor: config.bgColor,
                      borderColor: config.borderColor,
                      border: `1px solid ${config.borderColor}`,
                    }}
                  >
                    {item.status?.toUpperCase() || 'UPDATE'}
                  </Tag>
                  {item?.remarks && (
                    <div className="bg-gray-50 p-2 rounded text-xs">
                      <Text
                        type="secondary"
                        className="break-words whitespace-pre-wrap"
                        style={{ fontSize: '12px' }}
                      >
                        {item?.remarks}
                      </Text>
                    </div>
                  )}
                </div>
              ),
            };
          })}
        />
      </Card>
    ) : (
      <Card className="border-0 shadow-sm" size="small">
        <div className="p-4">
          <Empty
            description="No progress history available"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            imageStyle={{ height: 40 }}
          />
        </div>
      </Card>
    );

  const items = [
    {
      key: 'main',
      label: (
        <Space size="small">
          <FileTextOutlined style={{ fontSize: '14px' }} />
          <span style={{ fontSize: '13px' }}>Details</span>
        </Space>
      ),
      children: mainInfo,
    },
    {
      key: 'progress',
      label: (
        <Space size="small">
          <ClockCircleOutlined style={{ fontSize: '14px' }} />
          <span style={{ fontSize: '13px' }}>Timeline</span>
        </Space>
      ),
      children: progressHistory,
    },
  ];

  return (
    <div className="h-full bg-gray-50">
      <div className="p-3 bg-white border-b">
        <Title level={5} className="!m-0 !text-gray-800 text-sm">
          Transport Details
        </Title>
      </div>
      <div className="p-3 h-full overflow-y-auto">
        <Tabs
          defaultActiveKey="main"
          items={items}
          size="small"
          tabBarStyle={{ marginBottom: '12px' }}
        />
      </div>
    </div>
  );
};

export default TransportIndentSidebar;
