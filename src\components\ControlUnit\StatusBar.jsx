import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>se, Modal } from 'antd';
import { useEffect, useState } from 'react';
import { convertMinsToHrs, getLocalDateTime } from '../../helperFunction';
import { useGetSingleStatusMutation } from '../../slices/planningStatusApiSlice';

function returnType(type) {
  switch (type) {
    case 'job':
      return 'Job';
    case 'wo':
      return 'Work Order';
    case 'cu':
      return 'Process';
    case 'mac':
      return 'Machine';
    default:
      return 'NA';
  }
}

function Message({ data }) {
  const [openModal, setOpenModal] = useState(false);

  if (!data?.status) return <p className="font-semibold">NA</p>;

  const time =
    data?.timeDifference > 0
      ? convertMinsToHrs(Math.round(data?.timeDifference / 60000))?.split(':')
      : ['0', '0'];

  return (
    <>
      <Modal
        open={openModal}
        title={`${returnType(data?.type)} Details`}
        onCancel={() => setOpenModal(false)}
        footer={[
          <Button key={'back'} onClick={() => setOpenModal(false)}>
            Ok
          </Button>,
        ]}
      >
        <p
          className={`font-medium text-lg mb-5 ${data?.status === 'leading' ? 'text-green-500' : 'text-red-500'}`}
        >
          {returnType(data?.type)} {data?.predictedStop ? 'is ' : 'was '}
          {data?.status === 'leading' ? 'early ' : 'late '}by {time?.[0]}h{' '}
          {time?.[1]}m
          {data?.predictedStop
            ? ' and is expected to complete by '
            : ' and was completed on '}
          {getLocalDateTime(data?.stop || data?.predictedStop)}
        </p>
        <Collapse
          items={[
            {
              label: 'View more details',
              children: (
                <div className="grid grid-cols-2">
                  <p className="font-semibold">Status:</p>
                  <p
                    className={
                      data?.status === 'leading'
                        ? 'text-green-500'
                        : 'text-red-500'
                    }
                  >
                    {data?.status} by {time?.[0]}h {time?.[1]}m
                  </p>
                  <p className="font-semibold">Quantity:</p>
                  <p>
                    {data?.quantity}/{data?.plannedQuantity}
                  </p>
                  <p className="font-semibold">Planned Start Time:</p>
                  <p>{getLocalDateTime(data?.plannedStart)}</p>
                  <p className="font-semibold">Actual Start Time:</p>
                  <p>{getLocalDateTime(data?.start)}</p>
                  <p className="font-semibold">Planned Stop Time:</p>
                  <p>{getLocalDateTime(data?.plannedStop)}</p>
                  <p className="font-semibold">Actual Stop Time:</p>
                  <p>{getLocalDateTime(data?.stop)}</p>
                </div>
              ),
            },
          ]}
        />
      </Modal>
      <div
        className="flex w-full justify-between cursor-pointer"
        onClick={() => setOpenModal(true)}
      >
        <p className="font-semibold">
          {data?.status === 'leading' ? 'Early ' : 'Late '}by {time?.[0]}h{' '}
          {time?.[1]}m
        </p>
      </div>
    </>
  );
}

function StatusBar({ id, type, macId }) {
  const [getSingleStatus, { data }] = useGetSingleStatusMutation();

  useEffect(() => {
    if (!id || !type) return;

    const filter = {
      type,
    };

    if (type === 'wo') {
      filter.createPoId = id;
    } else if (type === 'job') {
      filter.createInputId = id;
    } else if (type === 'cu') {
      filter.cuProjectId = id;
    } else if (type === 'mac') {
      filter.cuProjectId = id;
      filter.machineId = macId;
    } else {
      return;
    }

    getSingleStatus({
      data: filter,
    });
  }, [id, type, getSingleStatus, macId]);

  return (
    <div className="my-2">
      <Alert
        type={
          data?.status === 'lagging'
            ? 'error'
            : data?.status === 'leading'
              ? 'success'
              : 'warning'
        }
        message={<Message data={data} />}
      />
    </div>
  );
}

export default StatusBar;
