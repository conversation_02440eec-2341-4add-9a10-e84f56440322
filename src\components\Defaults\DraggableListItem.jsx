import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { RxDragHandleDots2 } from 'react-icons/rx';

const TYPE = 'PDF_ORDER';

export default function DraggableListItem({ children, index, moveItem }) {
  const ref = useRef(null);

  const [{ isDragging }, dragRef] = useDrag({
    type: TYPE,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const opacity = isDragging ? 0.2 : 1;

  const [_, dropRef] = useDrop({
    accept: TYPE,
    hover: (item, monitor) => {
      const dragIndex = item.index;
      const hoverIndex = index;

      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const hoverActualY = monitor.getClientOffset().y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverActualY < hoverMiddleY) return;
      if (dragIndex > hoverIndex && hoverActualY > hoverMiddleY) return;

      moveItem(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  const dragDropRef = dragRef(dropRef(ref));

  return (
    <li
      className="text-sm p-4 bg-gray-200 rounded-md font-semibold flex items-center gap-x-2 hover:cursor-move"
      ref={dragDropRef}
      style={{ opacity }}
    >
      <RxDragHandleDots2 size={20} className="text-gray-500" />
      {children}
    </li>
  );
}
