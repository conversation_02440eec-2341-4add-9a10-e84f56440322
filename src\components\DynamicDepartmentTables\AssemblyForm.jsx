import { TrashIcon } from '@heroicons/react/24/outline';
import Input from '../global/components/Input';
import Select from '../global/components/Select';
import DragAndDrop from '../global/components/DragAndDrop';
import Table from '../global/components/Table';

const AssemblyForm = ({
  addAssemblyFormData,
  setAddAssemblyFormData,
  assemblyFormSteps,
  setAssembyFormSteps,
  stepCount,
  disabled = false,
}) => {
  const assemblyFileChangeHandler = (e, type, isAdded, index) => {
    for (let i in e) {
      let fileName = e[i].name;
      let fileType = e[i].type;

      const fileReader = new FileReader();
      if (i === 'length') return;
      fileReader.readAsDataURL(e[i]);
      fileReader.addEventListener('load', () => {
        const url = fileReader.result;
        let data = {
          name: fileName,
          type: fileType,
          data: url,
        };

        if (!isAdded) {
          setAssembyFormSteps((prev) => ({
            ...prev,
            attachments: [...prev.attachments, data],
          }));
        } else {
          setAddAssemblyFormData((prev) => ({
            ...prev,
            steps: prev?.steps?.map((step, i) => {
              if (i === index) {
                return {
                  ...step,
                  attachments: [...step.attachments, data],
                };
              }
              return step;
            }),
          }));
        }
      });
    }
  };

  const handleRemovePdf = (file, isAdded, index) => {
    if (!isAdded) {
      setAssembyFormSteps((prev) => ({
        ...prev,
        attachments: prev?.attachments?.filter(
          (assembly) => assembly?.name !== file?.name
        ),
      }));
    } else {
      setAddAssemblyFormData((prev) => ({
        ...prev,
        steps: prev?.steps?.map((step, i) => {
          if (i === index) {
            return {
              ...step,
              attachments: step?.attachments?.filter(
                (assembly) => assembly?.name !== file?.name
              ),
            };
          }
          return step;
        }),
      }));
    }
  };

  const handleStepDelete = (item) => {
    setAddAssemblyFormData((prev) => ({
      ...prev,
      steps: prev?.steps?.filter(
        (step) => step?.stepNumber !== item?.stepNumber
      ),
    }));
  };

  return (
    <div className={`${disabled ? 'cursor-not-allowed' : ''}`}>
      <section>
        <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
          <div className="flex flex-col my-5">
            <label className="mb-1 font-semibold text-[#667085]">
              Assembly Form Name
            </label>
            <Input
              type="text"
              id="assemblyFormName"
              name="assemblyFormName"
              placeholder="Assembly Form Name"
              value={addAssemblyFormData?.formName}
              onChange={(e) =>
                setAddAssemblyFormData((prev) => ({
                  ...prev,
                  formName: e.target.value,
                }))
              }
            />
          </div>
          <div className="flex flex-col my-5">
            <label className="mb-1 font-semibold text-[#667085]">
              Category
            </label>
            <Select
              placeholder="Select SOP"
              id="SOP"
              name="SOP"
              menuPlacement="auto"
              onChange={(e) =>
                setAddAssemblyFormData((prev) => ({
                  ...prev,
                  category: e.target.value,
                }))
              }
              value={addAssemblyFormData?.category}
              closeMenuOnSelect={true}
              options={['WO SOP', 'Job Sop'].map((el) => ({
                value: el,
                label: el,
              }))}
            />
          </div>
        </div>
      </section>
      <section>
        {addAssemblyFormData?.steps?.length > 0 &&
          addAssemblyFormData?.steps?.map((item, index) => {
            return (
              <div key={index}>
                <div className="outline-[#4085ed80] rounded-lg border p-2 mb-5">
                  <div className="flex flex-row justify-between px-2">
                    <label className="font-bold text-[#667085] text-lg">
                      Step {item?.stepNumber}:
                    </label>
                    <div
                      className="hover:cursor-pointer"
                      onClick={() => handleStepDelete(item)}
                    >
                      <TrashIcon className="w-6 h-6 text-black my-auto" />
                    </div>
                  </div>
                  <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2 mt-2">
                    <div className="flex flex-col">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Step Name
                      </label>
                      <Input
                        type="text"
                        id="stepName"
                        name="stepName"
                        placeholder="Step Name"
                        value={item?.stepName}
                        onChange={(e) =>
                          setAddAssemblyFormData((prev) => ({
                            ...prev,
                            steps: prev?.steps?.map((prevItem, i) => {
                              if (i === index) {
                                return {
                                  ...prevItem,
                                  stepName: e.target.value,
                                };
                              }
                              return prevItem;
                            }),
                          }))
                        }
                      />
                    </div>
                    <div className="flex flex-row justify-between">
                      <div></div>
                      <div className="flex flex-row space-x-2 mt-8 items-center ">
                        <Input
                          type="checkbox"
                          id="isMediaMandatory"
                          name="isMediaMandatory"
                          value={item?.isMediaMandatory}
                          checked={item?.isMediaMandatory}
                          onChange={(e) =>
                            setAddAssemblyFormData((prev) => ({
                              ...prev,
                              steps: prev?.steps?.map((prevItem, i) => {
                                if (i === index) {
                                  return {
                                    ...prevItem,
                                    isMediaMandatory: e?.target?.checked,
                                  };
                                }
                                return prevItem;
                              }),
                            }))
                          }
                        />
                        <label className="mb-1 font-semibold text-[#667085]">
                          Media Mandatory?
                        </label>
                      </div>
                    </div>
                    <div className="mt-2 w-full col-span-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Select SOP File
                      </label>
                      <DragAndDrop
                        accept="application/pdf"
                        fileType="JPG/PNG"
                        // svg={<JpgPng className="h-10" />}
                        onChange={(e) => {
                          assemblyFileChangeHandler(e, 'project', true, index);
                        }}
                        multiple
                        className={`text-[#667085]`}
                      />
                    </div>
                    {item?.attachments?.length > 0 && (
                      <div className="mt-2 col-span-2">
                        <Table className="mt-2">
                          <Table.Head>
                            <Table.Row>
                              <Table.Th>S.No.</Table.Th>
                              <Table.Th>File Name</Table.Th>
                              <Table.Th></Table.Th>
                            </Table.Row>
                          </Table.Head>
                          <Table.Body>
                            {item?.attachments?.map((file, idx) => {
                              return (
                                <Table.Row key={idx}>
                                  <Table.Td>{idx + 1}</Table.Td>
                                  <Table.Td>{file?.name}</Table.Td>
                                  <Table.Td
                                    onClick={() =>
                                      handleRemovePdf(file, true, index)
                                    }
                                  >
                                    <div className="hover:cursor-pointer">
                                      x
                                    </div>
                                  </Table.Td>
                                </Table.Row>
                              );
                            })}
                          </Table.Body>
                        </Table>
                      </div>
                    )}
                    <div className="flex flex-col mt-2 col-span-2">
                      <label className="mb-1 font-semibold text-[#667085]">
                        Description
                      </label>
                      <textarea
                        type="text"
                        id="description"
                        name="description"
                        className="py-1 pl-4 pr-2 bg-transparent relative outline-[#4085ed80] rounded-lg flex justify-between items-center border text-black"
                        placeholder="Description"
                        value={item?.description}
                        onChange={(e) =>
                          setAddAssemblyFormData((prev) => ({
                            ...prev,
                            steps: prev?.steps?.map((prevItem, i) => {
                              if (i === index) {
                                return {
                                  ...prevItem,
                                  description: e.target.value,
                                };
                              }
                              return prevItem;
                            }),
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
      </section>
      <section>
        <label className="font-bold text-[#667085] text-lg">
          Step {stepCount}:
        </label>
        <div className="outline-[#4085ed80] rounded-lg border p-2">
          <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2 mt-2">
            <div className="flex flex-col">
              <label className="mb-1 font-semibold text-[#667085]">
                Step Name
              </label>
              <Input
                type="text"
                id="stepName"
                name="stepName"
                placeholder="Step Name"
                value={assemblyFormSteps?.stepName}
                onChange={(e) =>
                  setAssembyFormSteps((prev) => ({
                    ...prev,
                    stepName: e.target.value,
                  }))
                }
              />
            </div>
            <div className="flex flex-row justify-between">
              <div></div>
              <div className="flex flex-row space-x-2 mt-8 items-center ">
                <Input
                  type="checkbox"
                  id="isMediaMandatory"
                  name="isMediaMandatory"
                  checked={assemblyFormSteps?.isMediaMandatory}
                  onChange={(e) => {
                    setAssembyFormSteps((prev) => ({
                      ...prev,
                      isMediaMandatory: e.target.checked,
                    }));
                  }}
                />
                <label className="mb-1 font-semibold text-[#667085]">
                  Media Mandatory?
                </label>
              </div>
            </div>
            <div className="mt-2 w-full col-span-2">
              <label className="mb-1 font-semibold text-[#667085]">
                Select SOP File
              </label>
              <DragAndDrop
                accept="application/pdf"
                fileType="JPG/PNG"
                // svg={<JpgPng className="h-10" />}
                onChange={(e) => {
                  assemblyFileChangeHandler(e, 'project', false);
                }}
                multiple
                className={`text-[#667085]`}
              />
            </div>
            {assemblyFormSteps?.attachments?.length > 0 && (
              <div className="mt-2 col-span-2">
                <Table>
                  <Table.Head>
                    <Table.Row>
                      <Table.Th>S.No.</Table.Th>
                      <Table.Th>File Name</Table.Th>
                      <Table.Th></Table.Th>
                    </Table.Row>
                  </Table.Head>
                  <Table.Body>
                    {assemblyFormSteps?.attachments?.map((file, index) => {
                      return (
                        <Table.Row key={index}>
                          <Table.Td>{index + 1}</Table.Td>
                          <Table.Td>{file?.name}</Table.Td>
                          <Table.Td
                            onClick={() => handleRemovePdf(file, false)}
                          >
                            <div className="hover:cursor-pointer">x</div>
                          </Table.Td>
                        </Table.Row>
                      );
                    })}
                  </Table.Body>
                </Table>
              </div>
            )}
            <div className="flex flex-col mt-2 col-span-2">
              <label className="mb-1 font-semibold text-[#667085]">
                Description
              </label>
              <textarea
                type="text"
                id="description"
                name="description"
                className="py-1 pl-4 pr-2 bg-transparent relative outline-[#4085ed80] rounded-lg flex justify-between items-center border text-black"
                placeholder="Description"
                value={assemblyFormSteps?.description}
                onChange={(e) =>
                  setAssembyFormSteps((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
              />
            </div>
          </div>
        </div>
      </section>
      <div className="flex flex-row justify-between">
        <div></div>
        <div className="flex flex-row space-x-2 mt-8 items-center ">
          <Input
            type="checkbox"
            id="sequenceFollow"
            name="sequenceFollow"
            value={addAssemblyFormData?.isSequential}
            checked={addAssemblyFormData?.isSequential}
            onChange={(e) => {
              setAddAssemblyFormData((prev) => ({
                ...prev,
                isSequential: e.target.checked,
              }));
            }}
          />
          <label className="mb-1 font-semibold text-[#667085]">
            Sequential Steps
          </label>
        </div>
      </div>
    </div>
  );
};

export default AssemblyForm;
