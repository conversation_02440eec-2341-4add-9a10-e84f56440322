const AdvanceOptions = ({ setShowAdvanceOptions }) => {
  return (
    <div className="w-full border-b relative my-5 col-span-full">
      <p
        onClick={() => setShowAdvanceOptions((prev) => !prev)}
        className="absolute left-1/2 -translate-x-1/2 -top-2 text-xs text-gray-600 bg-white px-1 cursor-pointer"
      >
        Advance Options
      </p>
    </div>
  );
};

export default AdvanceOptions;
