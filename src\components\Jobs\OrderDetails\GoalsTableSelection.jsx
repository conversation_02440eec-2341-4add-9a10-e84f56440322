import { useContext, useState } from 'react';
import { toast } from 'react-toastify';
import { calculate } from '../../../calculateString';
import { useUpdateMacineMutation } from '../../../slices/machineApiSlice';
import { Store } from '../../../store/Store';
import { DEFAULT_MULTIPLIER_VALUE } from '../../../utils/Constant';
import Input from '../../global/components/Input';
import Modal from '../../global/components/Modal';
import MultiSelect from '../../global/components/MultiSelect';
import Select from '../../global/components/Select';

const initialAddOptionFor = { optFor: '', optVal: 0 };

const GoalsTableSelection = ({
  process = {},
  machines = [],
  goalsTable = {},
  setGoalsTables,
  selectedMachines = [],
  projectStatus,
  isEdit,
  templateDetails,
  createTemplate,
  setTemplateDetails,
}) => {
  const [selectedMacId, setSelectedMacId] = useState('');
  const [addOptionFor, setAddOptionFor] = useState(initialAddOptionFor);

  const { defaults: { defaultParam = {} } = {} } = useContext(Store);

  const { prefixIds } = defaultParam;

  const { isMultiProcess } = process;

  const { tableData = [] } = goalsTable;

  const [updateMachine] = useUpdateMacineMutation();

  const selectedMac =
    selectedMachines?.find((mac) => mac._id === selectedMacId) || {};

  const selectChangeHandler = (e, selectFor) => {
    const { value } = e.target;

    if (typeof value === 'string' && value?.startsWith('+')) {
      setAddOptionFor({ optFor: value, optVal: 0 });
      return;
    }

    // set goals tables
    setGoalsTables((prev) =>
      prev.map((gt) => {
        if (gt.flowId === process._id) {
          return {
            ...gt,
            compatibleMachines: gt?.compatibleMachines?.map((cMac) => {
              if (cMac.value === selectedMac?._id) {
                return {
                  ...cMac,
                  [selectFor]: value,
                };
              }
              return cMac;
            }),
          };
        }
        return gt;
      })
    );
  };

  const addOptionSubmit = async () => {
    const data = { _id: selectedMac._id };
    if (addOptionFor?.optFor === '+iph') {
      const exists = selectedMac?.itemsPerHour?.find(
        (i) => i.value === addOptionFor?.optVal
      );
      if (exists) {
        toast.error('Cannot and same value again', {
          toastId: 'duplicate iph',
        });

        return;
      } else {
        data.itemsPerHour = [
          ...(selectedMac?.itemsPerHour || []),
          {
            label: `${Object.values(prefixIds?.itemsPerHour || {})?.[0]}-${
              selectedMac?.itemsPerHour?.length + 1
            }`,
            value: addOptionFor?.optVal,
            isDefault: false,
          },
        ];

        const res = await updateMachine({ data }).unwrap();
        if (res) {
          selectChangeHandler(
            { target: { value: addOptionFor?.optVal } },
            'itemsPerHour'
          );

          setAddOptionFor(initialAddOptionFor);
        }
      }
    } else {
      const exists = selectedMac?.changeOverTime?.find(
        (i) => i.value === addOptionFor?.optVal
      );
      if (exists) {
        toast.error('Cannot and same value again', {
          toastId: 'duplicate cot',
        });
        return;
      } else {
        data.changeOverTime = [
          ...(selectedMac?.changeOverTime || []),
          {
            label: `${Object.values(prefixIds?.changeOverTime || {})?.[0]}-${
              selectedMac?.changeOverTime?.length + 1
            }`,
            value: addOptionFor?.optVal,
            isDefault: false,
          },
        ];

        const res = await updateMachine({ data }).unwrap();
        if (res) {
          selectChangeHandler(
            { target: { value: addOptionFor?.optVal } },
            'changeOverTime'
          );

          setAddOptionFor(initialAddOptionFor);
        }
      }
    }
    toast.success('Value added successfully', { toastId: 'sucess' });
  };

  const inputHandler = (e, inputFor) => {
    setGoalsTables((prev) =>
      prev.map((gt) => {
        if (gt.flowId === process._id) {
          if (isMultiProcess) {
            return {
              ...gt,
              tableData: gt?.tableData?.map((tdt) => ({
                ...tdt,
                subProcessData: tdt?.subProcessData?.map((stdt) => ({
                  ...stdt,
                  [inputFor]: +e.target.value,
                })),
              })),
            };
          }
          return {
            ...gt,
            tableData: gt?.tableData?.map((tdt) => ({
              ...tdt,
              [inputFor]: +e.target.value,
            })),
          };
        }
        return gt;
      })
    );
  };

  const handleMultiplierChange = (e) => {
    const regex = new RegExp(/^[*/]\d+/);
    const pass = regex.test(e.target.value);
    if (pass) {
      document
        .getElementById(`input${process._id}`)
        ?.classList.remove('!bg-red-200');
    } else {
      document
        .getElementById(`input${process._id}`)
        ?.classList.add('!bg-red-200');
    }

    setGoalsTables((prev) =>
      prev.map((item) => {
        if (item.flowId === process._id) {
          return {
            ...item,
            multiplier: e.target.value || DEFAULT_MULTIPLIER_VALUE,
            tableData: pass
              ? item.tableData.map((elem) => {
                  const isMulti = !!elem?.subProcessData;

                  if (isMulti) {
                    return {
                      ...elem,
                      newBatchSize: calculate(
                        `${elem?.['Batch Size']}${
                          e.target.value || DEFAULT_MULTIPLIER_VALUE
                        }`
                      ),
                      subProcessData: elem?.subProcessData?.map((sPro) => ({
                        ...sPro,
                        newBatchSize: calculate(
                          `${sPro?.['Batch Size']}${
                            e.target.value || DEFAULT_MULTIPLIER_VALUE
                          }`
                        ),
                      })),
                    };
                  }

                  return {
                    ...elem,
                    newBatchSize: calculate(
                      `${elem?.['Batch Size']}${
                        e.target.value || DEFAULT_MULTIPLIER_VALUE
                      }`
                    ),
                  };
                })
              : item.tableData,
          };
        }
        return item;
      })
    );
  };

  const macVals =
    goalsTable?.compatibleMachines?.find((i) => i.value === selectedMac._id) ||
    {};
  const iphVal = macVals?.itemsPerHour || '';
  const cotVal = macVals?.changeOverTime || '';

  const setupVal =
    (isMultiProcess
      ? tableData?.[0]?.subProcessData?.[0]?.setupTime
      : tableData?.[0]?.setupTime) || '';
  const bufferVal =
    (isMultiProcess
      ? tableData?.[0]?.subProcessData?.[0]?.bufferTime
      : tableData?.[0]?.bufferTime) || '';

  return (
    <>
      {addOptionFor?.optFor ? (
        <Modal
          title={`Add\xA0${
            addOptionFor?.optFor === '+iph'
              ? 'Items\xA0Per\xA0Hour'
              : 'Change\xA0Over\xA0Time'
          }`}
          description={`Add ${
            addOptionFor?.optFor === '+iph'
              ? 'Items Per Hour'
              : 'Change Over Time'
          } for ${selectedMac?.machineName}`}
          canSubmit={false}
          onSubmit={addOptionSubmit}
          onCloseModal={() => setAddOptionFor(initialAddOptionFor)}
        >
          {() => (
            <>
              <label htmlFor="addItem">
                {addOptionFor?.optFor === '+iph'
                  ? 'Items Per Hour:'
                  : 'Change Over Time:'}
              </label>
              <Input
                className="mt-3"
                id="addItem"
                type="number"
                value={addOptionFor?.optVal}
                onChange={(e) =>
                  setAddOptionFor((prev) => ({
                    ...prev,
                    optVal: +e.target.value,
                  }))
                }
              />
            </>
          )}
        </Modal>
      ) : null}
      {process?.mqtt?.category === 'Inhouse' && !createTemplate && (
        <div className="w-full my-5 px-5">
          <p>Select Machines</p>
          <MultiSelect
            placeholder="Select Machines"
            value={goalsTable?.compatibleMachines || []}
            onChange={(e) =>
              setGoalsTables((prev) =>
                prev.map((gt) => {
                  if (gt.flowId === process._id) {
                    return {
                      ...gt,
                      compatibleMachines: e.target.value?.map((i) => {
                        const exists =
                          gt?.compatibleMachines?.find(
                            (el) => el.value === i.value
                          ) || {};

                        const mac = machines?.find(
                          (mac) => mac._id === i.value
                        );

                        return {
                          ...i,
                          itemsPerHour: exists?.itemsPerHour || 0,
                          changeOverTime: exists?.changeOverTime || 0,
                          capacity: mac?.maxCapacity,
                          cost: mac?.costPerHour,
                        };
                      }),
                    };
                  }
                  return gt;
                })
              )
            }
            options={machines?.map((mac) => ({
              label: mac.machineName,
              value: mac._id,
            }))}
          />

          <div className="w-full">
            <section className="inline-flex rounded-lg shadow-sm overflow-hidden border border-[#d0d5dd] gap-x-px bg-[#d0d5dd] mt-4 w-fit">
              {selectedMachines?.map((mac) => (
                <p
                  key={mac._id}
                  className={`px-8 py-2 text-sm font-medium text-gray-900  outline-none cursor-pointer w-auto ${
                    selectedMac._id === mac._id ? 'bg-white' : 'bg-gray-100'
                  }`}
                  onClick={() => setSelectedMacId(mac._id)}
                >
                  {mac.machineName}
                </p>
              ))}
            </section>
            {selectedMac?._id ? (
              <section className="grid grid-cols-6 items-center mt-4 gap-x-5">
                <label>Items Per Hour</label>
                <Select
                  className="col-span-2"
                  value={iphVal}
                  onChange={(e) => {
                    selectChangeHandler(e, 'itemsPerHour');
                  }}
                  options={[
                    { label: '+ Add Option', value: '+iph' },
                    ...(selectedMac?.itemsPerHour?.map((i) => ({
                      ...i,
                      label: i.label + ' : ' + i.value,
                    })) || []),
                  ]}
                />
                <label>Change Over Time</label>
                <Select
                  className="col-span-2"
                  value={cotVal}
                  onChange={(e) => {
                    selectChangeHandler(e, 'changeOverTime');
                  }}
                  options={[
                    { label: '+ Add Option', value: '+cot' },
                    ...(selectedMac?.changeOverTime?.map((i) => ({
                      ...i,
                      label: i.label + ' : ' + i.value,
                    })) || []),
                  ]}
                />
              </section>
            ) : (
              <p>Please Select Machine</p>
            )}
          </div>
        </div>
      )}

      {process?.mqtt?.category !== 'Outsource' && (
        <div className="w-full px-5 my-3">
          <p>Select Applicable Downtimes</p>
          <MultiSelect
            placeholder="Select Applicable Downtimes"
            value={
              createTemplate
                ? templateDetails?.allApplicableDownTimes?.[process._id]
                : goalsTable?.applicableDowntime || []
            }
            onChange={(e) => {
              if (!createTemplate) {
                setGoalsTables((prev) =>
                  prev.map((gt) => {
                    if (gt.flowId === process._id) {
                      return {
                        ...gt,
                        applicableDowntime: e.target.value,
                      };
                    }
                    return gt;
                  })
                );
              } else {
                setTemplateDetails((prev) => ({
                  ...prev,
                  allApplicableDownTimes: {
                    ...prev.allApplicableDownTimes,
                    [process._id]: e.target.value,
                  },
                }));
              }
            }}
            options={process?.applicableDowntime}
          />
        </div>
      )}

      {!createTemplate && (
        <div className="w-full grid grid-cols-6 px-5 my-3 justify-items-start place-items-center gap-x-4">
          <label htmlFor="setup">Setup Time:</label>
          <Input
            id="setup"
            type="number"
            value={setupVal}
            onChange={(e) => inputHandler(e, 'setupTime')}
          />
          <label htmlFor="buffer">Buffer Time:</label>
          <Input
            id="buffer"
            type="number"
            value={bufferVal}
            onChange={(e) => inputHandler(e, 'bufferTime')}
          />
          <label htmlFor={`input${process._id}`}>Multilpier</label>
          <Input
            onChange={(e) => {
              handleMultiplierChange(e);
            }}
            value={goalsTable?.multiplier}
            id={`input${process._id}`}
            disabled={isEdit && projectStatus !== 'notStarted'}
          />
        </div>
      )}
    </>
  );
};

export default GoalsTableSelection;
