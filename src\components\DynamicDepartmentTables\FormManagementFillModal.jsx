import { useEffect, useState } from 'react';

import { Modal, Radio } from 'antd';
import MultiSelect from '../global/components/MultiSelect';

import { useGetAllFormQuery } from '../../slices/createFormapiSlice';
import Table from '../global/components/Table';
import FillForm from './FillForm';

const FormManagementFillModal = ({
  open,
  setOpen,
  changeHandler,
  value,
  form,
}) => {
  const [selectedForms, setSelectedForms] = useState([]);
  const [selectedFormIds, setSelectedFormIds] = useState([]);
  const [activeForm, setActiveForm] = useState();
  const [activeFormDetails, setActiveFormDetails] = useState({});
  const [initialFormData, setInitialFormData] = useState(value); //eslint-disable-line

  const { data: forms } = useGetAllFormQuery();

  const closeModal = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (form?._id) {
      setSelectedFormIds([form?._id]);
      setSelectedForms([form]);
      setActiveForm(form?._id);
      setActiveFormDetails(form);
    }
  }, [form]);

  useEffect(() => {
    if (initialFormData && forms && form === undefined) {
      let transformedFormData = [];
      for (let i of initialFormData) {
        let form = forms?.find((elem) => elem?._id === i?._id);
        let temp = [];
        for (let j of form?.formData) {
          let isPushed = false;
          for (let k of i?.formData) {
            if (k?.fieldName === j?.fieldName) {
              temp?.push(k);
              isPushed = true;
              break;
            }
          }
          if (!isPushed) {
            temp?.push(j);
          }
        }
        transformedFormData?.push({ ...i, formData: temp });
      }
      setSelectedForms(transformedFormData);
      setSelectedFormIds(transformedFormData?.map((elem) => elem?._id));
    }
  }, [initialFormData, forms, form]);

  const handleFormSelect = (e) => {
    let val = [];
    let ids = [];
    let temp = e.target.value?.map((elem) => elem?.value);
    for (let i of forms) {
      if (temp?.includes(i?._id)) {
        val?.push(i);
        ids?.push(i?._id);
      }
    }
    setSelectedForms(val);
    setSelectedFormIds(ids);
  };

  const handleModeChange = (e) => {
    setActiveForm(e.target.value);
    setActiveFormDetails(
      selectedForms?.find((elem) => elem?._id === e.target.value)
    );
  };

  const handleSubmit = () => {
    changeHandler(selectedForms);
    closeModal();
  };

  return (
    <Modal
      title="Fill Forms"
      width={700}
      centered
      open={open}
      onOk={handleSubmit}
      onCancel={closeModal}
    >
      <MultiSelect
        disabled={form?._id ? true : false}
        options={forms?.map((elem) => ({
          label: elem?.formName,
          value: elem?._id,
        }))}
        onChange={handleFormSelect}
        value={selectedFormIds}
      />
      <div className="mt-4">
        <Radio.Group
          onChange={handleModeChange}
          value={activeForm}
          style={{
            marginBottom: 8,
          }}
        >
          {selectedForms?.map((elem) => {
            return (
              <Radio.Button value={elem?._id} key={elem?._id}>
                {elem?.formName}
              </Radio.Button>
            );
          })}
        </Radio.Group>
        {activeFormDetails?._id && (
          <Table>
            <Table.Head>
              <Table.Th>Field Name</Table.Th>
              <Table.Th>Field Value</Table.Th>
            </Table.Head>
            <Table.Body>
              <FillForm
                key={activeFormDetails?._id}
                activeFormDetails={activeFormDetails}
                selectedForms={selectedForms}
                setSelectedForms={setSelectedForms}
              />
            </Table.Body>
          </Table>
        )}
      </div>
    </Modal>
  );
};

export default FormManagementFillModal;
