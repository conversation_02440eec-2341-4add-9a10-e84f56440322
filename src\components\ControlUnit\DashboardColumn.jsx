import { IoMdArrowDropleftCircle } from 'react-icons/io';

const DashboardColumn = ({ className, children, setOpenRightSideBar }) => {
  return (
    <div
      className={`${className} flex  flex-col p-3 gap-y-3 w-full md:min-w-[70%] md:px-[14px] md:pt-5 md:pb-12  mb-2 rounded-md md:bg-[#f6f6f6] h-[72vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100`}
    >
      <div className=" flex justify-end">
        <IoMdArrowDropleftCircle
          className="w-8 h-8 text-orange-600 cursor-pointer hover:text-orange-700 transition"
          onClick={() => setOpenRightSideBar(false)}
        />
      </div>
      <div className={`h-[71vh] `}>{children}</div>
    </div>
  );
};

export default DashboardColumn;
