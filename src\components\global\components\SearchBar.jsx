import { useState } from 'react';
import { ReactComponent as Search } from '../../../assets/svgs/search.svg';

const SearchBar = ({ value = { search: '' }, setValue, options }) => {
  const [showOptions, setShowOptions] = useState(false);

  return (
    <div
      className="h-6 rounded-md w-full bg-white mb-4 flex justify-between items-center pl-3"
      tabIndex="0"
      onBlur={() =>
        setTimeout(() => {
          setShowOptions(false);
        }, 100)
      }
    >
      <Search className="w-4 h-4" />
      <input
        type="text"
        value={value?.search}
        onChange={(e) => {
          if (setValue) {
            setValue((prev) => ({ ...prev, search: e.target.value }));
          }
        }}
        className="h-hull w-full outline-none px-2 text-[.94rem]"
        placeholder="Search"
      />

      <span
        className="px-3 hover:text-red-primary cursor-pointer text-sm"
        onClick={() =>
          setValue({
            search: '',
            option: options?.length === 1 ? options[0] : '',
          })
        }
      >
        x
      </span>

      <div
        className="relative w-1/3 h-full border-l pl-2 text-[.94rem]"
        onClick={() => setShowOptions((prev) => !prev)}
      >
        <span
          className={`capitalize ${!value?.option ? 'text-gray-primary' : ''}`}
        >
          {value?.option || 'Category'}
        </span>

        {showOptions ? (
          <div className="absolute right-0 top-[110%] w-full bg-white rounded-new text-[0.88rem] shadow-low">
            <ul className="px-3">
              {options?.map((option) => (
                <li
                  key={option}
                  className={`mt-1 cursor-pointer capitalize ${
                    option === value?.option ? 'underline' : 'hover:underline'
                  }`}
                  onClick={() => {
                    setValue((prev) => ({ ...prev, option }));
                    setShowOptions(false);
                  }}
                >
                  {option}
                </li>
              ))}
            </ul>
          </div>
        ) : null}
      </div>
    </div>
  );
};
export default SearchBar;
