import { Modal } from 'antd';
import Input from '../global/components/Input';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import Textarea from '../global/components/Textarea';
import { Label } from '../v2';

const CopyIndent = ({
  openModal,
  setOpenModal,
  setSearchParams,
  handleCopyIndent,
  isCreateIndentLoading,
  copyData,
  setCopyData,
  DropdownValue,
  handelpartChange,
  setSearchQuery,
  Suggestedparts,
  allparts,
  getCorrespondingConversionFactor,
  latestindent,
  dateConfig,
  FormatDate,
}) => {
  const handleCancel = () => {
    setSearchParams(
      (prev) => {
        prev.set('id', ''); // Remove the id parameter
        prev.delete('isCopy'); // Remove the isCopy parameter
        return prev;
      },
      { replace: true }
    );
    setOpenModal(false);
  };

  let conversion;
  allparts?.map((part) => {
    if (copyData?.product_name == part?.name) {
      conversion = getCorrespondingConversionFactor(copyData?.uom, part);
    }
  });
  return (
    <Modal
      title="Copy Indent"
      open={openModal}
      onCancel={handleCancel}
      onOk={handleCopyIndent}
      confirmLoading={isCreateIndentLoading}
    >
      <section>
        <div className="product-preview">
          <div className="ml-2">Indent Id :{latestindent?.indent_no + 1}</div>
          <Table>
            <Table.Head>
              <Table.Row>
                <Table.Th>Product name</Table.Th>
                <Table.Th>uom</Table.Th>
                <Table.Th>quantity</Table.Th>
                <Table.Th>Delivery date</Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              <Table.Row>
                <Table.Td>
                  <div className="max-w-[40ch] break-words">
                    {copyData.product_name}
                  </div>
                </Table.Td>
                <Table.Td>
                  <div style={{ whiteSpace: 'nowrap' }}>{copyData?.uom}</div>
                  {conversion && (
                    <span
                      style={{ whiteSpace: 'nowrap' }}
                      className=" mt-2 text-gray-500"
                    >
                      {conversion.conversionValue +
                        '-' +
                        conversion.conversionUnit}
                    </span>
                  )}
                </Table.Td>
                <Table.Td>{copyData.quantity}</Table.Td>
                <Table.Td>
                  {FormatDate(copyData.delivery_date, dateConfig)}
                </Table.Td>
              </Table.Row>
            </Table.Body>
          </Table>
        </div>
        <Label>
          Edit Products
          <Select
            value={DropdownValue}
            onInputChange={(e) => setSearchQuery(e)}
            options={Suggestedparts?.map((part) => {
              return {
                name: part?.name,
                value: part?.name,
              };
            })}
            onChange={handelpartChange}
            id="search-input"
          />
        </Label>
        <Label>
          Delivery Date
          <Input
            type="date"
            onChange={(e) => {
              setCopyData((prev) => {
                return {
                  ...prev,
                  delivery_date: e.target.value,
                };
              });
            }}
          />
        </Label>
        <Label>
          Quantity
          <Input
            type="number"
            value={copyData.quantity}
            onChange={(e) => {
              setCopyData((prev) => {
                return {
                  ...prev,
                  quantity: e.target.value,
                };
              });
            }}
          />
        </Label>
        <Label>
          Remark
          <Textarea
            value={copyData.remark}
            placeholder="Add Remark"
            onChange={(e) => {
              setCopyData((prev) => {
                return {
                  ...prev,
                  remark: e.target.value,
                };
              });
            }}
          />
        </Label>
      </section>
    </Modal>
  );
};

export default CopyIndent;
