import { Avatar, Popover } from 'antd';
import DOMPurify from 'dompurify';
import moment from 'moment/moment';
import { useEffect, useState } from 'react';
import { AiOutlineUser } from 'react-icons/ai';
import {
  MdLabelImportantOutline,
  MdOutlineArrowDropDown,
  MdOutlineDeleteOutline,
  MdOutlineMarkEmailUnread,
  MdOutlineStar,
  MdOutlineStarBorder,
  MdReply,
} from 'react-icons/md';
import { TiArrowForward } from 'react-icons/ti';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import DownloadIcon from '../../assets/images/download.png';
import {
  useAddFlagMutation,
  useMoveToFoldersMutation,
} from '../../slices/EmailApiSlice';
import Button from '../global/components/Button';
import Spinner from '../global/components/Spinner';
import ForwardMailModal from './ForwardMailModal';

const EmailDetail = ({
  mail,
  showActionButtons = true,
  setReplyMail,
  setIsReply,
  setMailUid,
}) => {
  const [SearchParams] = useSearchParams({
    tab: '',
  });
  const [Attachments, setAttachments] = useState([]);
  const [ShowForwardModal, setShowForwardModal] = useState(false);
  const [SelectedEmailToForward, setSelectedEmailToForward] = useState(null);
  const [Loading, setIsLoading] = useState(false);
  const [StarredLoading, setIsStarredLoadingLoading] = useState(false);
  const [MarkUnreadLoading, setMarkUnreadLoading] = useState(false);

  const [addFlag] = useAddFlagMutation();
  const [moveToBin] = useMoveToFoldersMutation();

  const BufferToBase64 = () => {
    mail?.attachments?.map((attachment) => {
      const buffer = new Uint8Array(attachment?.content?.data);
      const blob = new Blob([buffer], { type: attachment?.contentType });
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = (e) => {
        const dataurl = e.target.result;
        setAttachments((prev) => {
          return [
            ...prev,
            {
              type: attachment?.contentType,
              data: dataurl,
              name: attachment?.filename,
            },
          ];
        });
      };
    });
  };

  const handleAddStarred = async () => {
    setIsStarredLoadingLoading(true);
    const flag = '\\Flagged';
    const data = await addFlag({
      emailId: mail?.uid,
      flag,
      mailbox: SearchParams.get('tab'),
    }).unwrap();
    setIsStarredLoadingLoading(false);
    if (data?.data[0]?.flags?.includes(flag)) {
      toast.success('Message Starred');
    } else {
      toast.success('Message UnStarred');
    }
  };

  const handleMarkAsUnread = async () => {
    setMarkUnreadLoading(true);
    const flag = '\\Seen';
    const data = await addFlag({
      emailId: mail?.uid,
      flag,
      mailbox: SearchParams.get('tab'),
    }).unwrap();
    setMarkUnreadLoading(false);

    if (data?.data[0]) {
      toast.success('Message Marked As Unread');
    }
  };

  const handleAddToBin = async () => {
    setIsLoading(true);
    const data = await moveToBin({
      emailId: mail?.uid,
      mailbox: SearchParams.get('tab'),
    }).unwrap();

    if (data?.data) {
      setIsLoading(false);
      setMailUid(mail?.uid);
      toast.success('Message Sent To Bin');
    }
  };
  const handleReplyMail = () => {
    setIsReply(true);
    setReplyMail(mail);
  };
  useEffect(() => {
    setAttachments([]);
    BufferToBase64();
    // eslint-disable-next-line
  }, [mail]);
  const sanitizedHtml = DOMPurify.sanitize(mail?.html);
  return (
    <>
      {ShowForwardModal && (
        <ForwardMailModal
          mail={SelectedEmailToForward}
          setShowModal={setShowForwardModal}
        />
      )}

      {mail && (
        <div className="email-details-container">
          <div className="flex justify-between items-center">
            <div className="sender-info px-2  flex items-center gap-x-2">
              <div className="profile-pic">
                {mail && (
                  <Avatar
                    size={{ xs: 24, sm: 32, md: 32, lg: 32, xl: 48, xxl: 48 }}
                    icon={<AiOutlineUser />}
                  />
                )}
              </div>
              <div className="sender-receiver-container text-[14px]">
                <p className="flex items-center text-[#424242]">
                  <span className="mr-1">
                    {mail?.from?.value?.map((name) => name?.name)}
                  </span>{' '}
                  <span className="text-xl">{'<'}</span>
                  {mail?.from?.value?.map((name) => name?.address)}
                  <span className="text-xl">{'>'}</span>
                </p>
                <p className="flex items-center gap-1">
                  <span>To: </span>
                  me{' '}
                  <Popover
                    trigger="click"
                    placement="bottomLeft"
                    content={
                      <>
                        <div className="flex gap-4">
                          <div className="flex flex-col justify-end text-right text-gray-500">
                            <p>from:</p>
                            <p>to:</p>
                            <p>date:</p>
                            <p>subject:</p>
                            <p>mailed-by:</p>
                            <p>signed-by:</p>
                          </div>
                          <div className="flex flex-col justify-start">
                            <p>
                              {mail?.from?.value?.map((name) => name?.address)}
                            </p>
                            <p>
                              {mail?.to?.value?.map((name) => name?.address)}
                            </p>
                            <p>
                              {moment(mail?.date).format(
                                'ddd DD/MM/YYYY h:mm a'
                              )}
                            </p>
                            <p className="whitespace-nowrap">{mail?.subject}</p>
                            <p>
                              {mail?.from?.value?.map((name) => name?.address)}
                            </p>
                            <p>
                              {mail?.from?.value?.map((name) => name?.address)}
                            </p>
                          </div>
                        </div>
                      </>
                    }
                  >
                    <span className="cursor-pointer">
                      <MdOutlineArrowDropDown />
                    </span>
                  </Popover>
                </p>
              </div>
            </div>
            <div className="flex flex-col items-end subject">
              {showActionButtons && (
                <div className="flex items-center gap-[1vmax] p-2">
                  <span
                    className="w-fit cursor-pointer hover:text-[#4B84B8] text-gray-400"
                    title="Label"
                  >
                    <MdLabelImportantOutline />
                  </span>
                  <span
                    className="w-fit text-[#499EE0] cursor-pointer"
                    title="Forward"
                    onClick={() => {
                      setShowForwardModal(true);
                      setSelectedEmailToForward(mail);
                    }}
                  >
                    <TiArrowForward />
                  </span>
                  <span
                    className="w-fit text-[#871797] cursor-pointer"
                    title="Reply"
                    onClick={handleReplyMail}
                  >
                    <MdReply />
                  </span>
                  {Loading ? (
                    <span className="">
                      <Spinner size={4} />
                    </span>
                  ) : (
                    <span
                      className="w-fit text-gray-400 hover:text-[#E81127] cursor-pointer"
                      title="Delete"
                      onClick={handleAddToBin}
                    >
                      <MdOutlineDeleteOutline />
                    </span>
                  )}
                  {StarredLoading ? (
                    <span className="">
                      <Spinner size={4} />
                    </span>
                  ) : (
                    <span
                      className="w-fit cursor-pointer text-yellow-400 hover:text-yellow-500"
                      title="Starred"
                      onClick={handleAddStarred}
                    >
                      {mail?.flags?.includes('\\Flagged') ? (
                        <MdOutlineStar />
                      ) : (
                        <MdOutlineStarBorder />
                      )}
                    </span>
                  )}
                  {MarkUnreadLoading ? (
                    <span className="">
                      <Spinner size={4} />
                    </span>
                  ) : (
                    <span
                      className="w-fit text-[#4B84B8] cursor-pointer hover:text-[#67aae9]"
                      onClick={handleMarkAsUnread}
                      title="Unread"
                    >
                      <MdOutlineMarkEmailUnread />
                    </span>
                  )}
                </div>
              )}

              <span className="date text-[12px] font-medium pr-2 text-[#424242]">
                {moment(mail?.date).format('ddd DD/MM/YYYY h:mm a')}
              </span>
            </div>
          </div>

          <div className="px-2 py-1 border-b-[1px]"></div>
          <div
            className="mail-body px-2 py-1 mt-3 border-b-[1px]"
            dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
          ></div>
          {Attachments?.length !== 0 && (
            <>
              <div className="attachment-container mt-3 p-3">
                <div className="heading mb-2">
                  <h4 className="text-blue-500">Attachments</h4>
                </div>
                <div className="attachments flex gap-3 flex-wrap">
                  {Attachments?.map((attachment) => {
                    return (
                      <div
                        className="embed-wrapper relative"
                        key={attachment?.data}
                      >
                        <a
                          href={attachment?.data}
                          className="!absolute"
                          download={attachment?.name}
                        >
                          <Button className=" !p-1 !rounded-[2px]">
                            <img
                              src={DownloadIcon}
                              className="block w-[12px] h-[12px]"
                              alt=""
                            />
                          </Button>
                        </a>
                        <embed
                          className="border-[1px] p-5"
                          src={attachment?.data}
                          width="80px"
                          height="80px"
                          type={attachment?.type}
                        />
                      </div>
                    );
                  })}
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </>
  );
};

export default EmailDetail;
