import { useEffect, useState } from 'react';

import { useAddCustomTagsMutation } from '../../slices/orderApiSlice';
import { handleAssignTags } from './kanbanFunctions';

import { toast } from 'react-toastify';

import Button from '../global/components/Button';
import MultiSelect from '../global/components/MultiSelect';

const Tags = ({
  setInitialScrollIndex,
  index,
  setShowSelectTags,
  selectedTags,
  setSelectedTags,
  defaultParam,
  column,
  card,
}) => {
  const [tags, setTags] = useState([]);

  const [addCustomTags] = useAddCustomTagsMutation();

  useEffect(() => {
    const nodes = defaultParam?.defaultParam
      ? defaultParam?.defaultParam?.departmentFlow?.nodeStructure?.nodes
      : defaultParam?.departmentFlow?.nodeStructure?.nodes;
    if (nodes) {
      nodes.forEach((node) => {
        const { data } = node;
        if (data && data?.selectedPage) {
          if (data?.selectedPage[0]?.label === column?.label) {
            let tagsTemp = [];
            for (let i of data?.tags || []) {
              if (i?.name?.name) {
                tagsTemp?.push({
                  ...i,
                  name: i?.name?.name,
                });
              } else {
                tagsTemp?.push(i);
              }
            }
            setTags(tagsTemp);
          }
        }
      });
    }
  }, [defaultParam, column?.label]);

  return (
    <div
      onClick={() => setShowSelectTags(false)}
      className="fixed inset-0 flex items-center justify-center z-20 bg-gray-900 bg-opacity-50"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="flex flex-col justify-around elative w-[420px] h-64 bg-white border border-gray-200 shadow-md rounded-lg p-2"
      >
        <section className="w-full flex flex-col">
          <div className="w-full flex justify-between py-2">
            <label
              className="mb-1"
              style={{ fontSize: '13px', fontWeight: 'bold' }}
              htmlFor="projectId"
            >
              Select Tags
            </label>
            <label
              onClick={() => setShowSelectTags(false)}
              className="cursor-pointer"
            >
              ❌
            </label>
          </div>
          <MultiSelect
            closeMenuOnSelect={false}
            className="!w-full"
            placeholder="Select Tags"
            onChange={(e) => {
              setSelectedTags(e.target.value);
            }}
            value={selectedTags}
            options={tags?.map((tag) => ({
              value: tag,
              label: tag?.name || tag?.name?.name,
            }))}
          />
        </section>
        <div className="mt-4 overflow-y-scroll h-full">
          {selectedTags?.map((tag, idx) => (
            <span
              key={idx}
              className="flex flex-col py-2 text-md text-gray-400 font-semibold border-y-[1px] mx-4"
            >
              {tag?.label}
            </span>
          ))}
        </div>
        <div className="flex items-center justify-end">
          <Button
            className="mt-4 !py-1"
            onClick={() => {
              handleAssignTags({
                addCustomTags,
                setInitialScrollIndex,
                setShowSelectTags,
                toast,
                index,
                card,
                selectedTags,
              });
            }}
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Tags;
