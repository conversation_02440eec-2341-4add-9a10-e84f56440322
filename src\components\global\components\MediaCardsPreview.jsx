import {
  DeleteOutlined,
  DownloadOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { Badge, Card, Col, Image, Row, Tooltip, Typography } from 'antd';
import { useState } from 'react';
import { downloadMedia } from '../../../helperFunction';
import PreviewImgPdfFullscreen from '../../salesOrder/PreviewImgPdfFullscreen';
const { Text } = Typography;

const MediaCardsPreview = ({ medias = [], onDelete }) => {
  const [previewMedia, setPreviewMedia] = useState(false);
  const [expandedMedia, setExpandedMedia] = useState(null);

  const handlePreviewFile = (file) => {
    setExpandedMedia(file);
    setPreviewMedia(true);
  };

  const getFileIcon = (fileType) => {
    if (fileType?.includes('pdf')) {
      return <FilePdfOutlined style={{ fontSize: 36, color: '#f5222d' }} />;
    } else if (fileType?.includes('image')) {
      return <FileImageOutlined style={{ fontSize: 36, color: '#1890ff' }} />;
    } else {
      return <FileTextOutlined style={{ fontSize: 36, color: '#52c41a' }} />;
    }
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div>
      {/* Optional Preview Component */}
      {previewMedia && (
        <PreviewImgPdfFullscreen
          media={expandedMedia}
          showPreview={previewMedia}
          setShowPreview={setPreviewMedia}
        />
      )}

      {medias?.length > 0 && (
        <Row gutter={[12, 12]} className="mt-2">
          {medias.map((file, idx) => (
            <Col span={6} key={idx}>
              <Badge.Ribbon
                text={file.type?.split('/')[1]?.toUpperCase() || 'FILE'}
                color={
                  file.type?.includes('image')
                    ? 'blue'
                    : file.type?.includes('pdf')
                      ? 'red'
                      : 'orange'
                }
                style={{ fontSize: '10px' }}
              >
                <Card
                  hoverable
                  size="small"
                  styles={{ body: { padding: '8px', height: '64px' } }}
                  style={{ width: '100%' }}
                  cover={
                    <div
                      className="h-24 flex items-center justify-center bg-gray-50 cursor-pointer"
                      onClick={() => handlePreviewFile(file)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: '8px',
                        backgroundColor: '#fafafa',
                        borderBottom: '1px solid #f0f0f0',
                      }}
                    >
                      {file.type?.includes('image') ? (
                        <Image
                          alt={file.name}
                          src={file.data}
                          preview={false}
                          style={{
                            maxHeight: '80px',
                            maxWidth: '100%',
                            objectFit: 'contain',
                          }}
                        />
                      ) : (
                        getFileIcon(file.type)
                      )}
                    </div>
                  }
                  actions={[
                    <Tooltip title="Preview" key="preview">
                      <FileTextOutlined
                        onClick={() => handlePreviewFile(file)}
                      />
                    </Tooltip>,
                    <Tooltip title="Download" key="download">
                      <DownloadOutlined onClick={() => downloadMedia(file)} />
                    </Tooltip>,
                    onDelete && (
                      <Tooltip title="Delete" key="delete">
                        <DeleteOutlined onClick={() => onDelete(file)} />
                      </Tooltip>
                    ),
                  ]}
                >
                  <Card.Meta
                    title={
                      <Tooltip title={file.name}>
                        <div
                          style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            fontSize: '11px',
                            fontWeight: 500,
                            lineHeight: 1.2,
                          }}
                        >
                          {file.name}
                        </div>
                      </Tooltip>
                    }
                    description={
                      <Text
                        type="secondary"
                        style={{
                          fontSize: '10px',
                          lineHeight: 1,
                          marginTop: '4px',
                          display: 'block',
                        }}
                      >
                        {formatFileSize(file.size)}
                      </Text>
                    }
                  />
                </Card>
              </Badge.Ribbon>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

export default MediaCardsPreview;
