import { DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, Card, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { downloadMedia } from '../../helperFunction';
import { useGetMediaByIdArrayMutation } from '../../slices/mediaSlice';
import Table from '../global/components/Table';
import PreviewImgPdfFullscreen from '../salesOrder/PreviewImgPdfFullscreen';
import Spinner from '../global/components/Spinner';

const ViewDepFields = ({ type, data }) => {
  const [getMediaByIdArray, { isLoading: isMediaLoading }] =
    useGetMediaByIdArrayMutation();
  const [mediaToPreview, setMediaToPreview] = useState(null);
  const [previewMedia, setPreviewMedia] = useState(false);
  const [mediaData, setMediaData] = useState([]);

  useEffect(() => {
    const fetchMedia = async () => {
      const response = await getMediaByIdArray({ data: { ids: data } });
      const media = response?.data?.media || [];
      setMediaData(media);
    };

    if (type === 'media' || type === 'audio') {
      fetchMedia();
    }
  }, [data, type, getMediaByIdArray]);

  const renderAudio = () => {
    return (
      <div className="space-y-4">
        {mediaData?.map((item, idx) => (
          <div
            key={`${item.name}-${idx}`}
            className="p-4 border rounded-lg shadow-sm"
          >
            <div className="flex items-center justify-between mb-2">
              <Tooltip title={item.name}>
                <span className="text-sm text-gray-500 truncate max-w-[200px]">
                  {item.name.length > 30
                    ? `${item.name.slice(0, 30)}...`
                    : item.name}
                </span>
              </Tooltip>
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={() => downloadMedia(item)}
                className="text-green-500 hover:text-green-700"
              >
                Download
              </Button>
            </div>
            <audio
              controls
              className="w-full"
              src={item?.data}
              controlsList="nodownload"
            >
              Your browser does not support the audio element.
            </audio>
          </div>
        ))}
      </div>
    );
  };

  const renderTable = () => {
    return (
      <Table>
        <Table.Head>
          <Table.Row>
            {Object.keys(data?.[0] || {})?.map((column) => (
              <Table.Th key={column}>{column}</Table.Th>
            ))}
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {data?.map((row, rowIndex) => (
            <Table.Row key={rowIndex}>
              {Object.keys(data?.[0] || {})?.map((column) => (
                <Table.Td key={column}>
                  {row?.[column]?.value || row?.[column]}
                </Table.Td>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    );
  };

  const renderMedia = () => {
    if (isMediaLoading) return <Spinner />;
    return (
      <div>
        {mediaData?.map((item, idx) => (
          <div
            key={`${item.name}-${idx}`}
            className="flex items-center justify-between py-2 border-b last:border-b-0"
          >
            <Tooltip title={item.name}>
              <span className="text-sm text-gray-500 truncate max-w-[200px]">
                {item.name.length > 30
                  ? `${item.name.slice(0, 30)}...`
                  : item.name}
              </span>
            </Tooltip>
            <div className="flex space-x-2">
              {(item.type?.includes('image') || item.type?.includes('pdf')) && (
                <Button
                  type="text"
                  icon={<EyeOutlined />}
                  onClick={() => {
                    setMediaToPreview(item);
                    setPreviewMedia(true);
                  }}
                  className="text-blue-500 hover:text-blue-700"
                >
                  Preview
                </Button>
              )}
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={() => downloadMedia(item)}
                className="text-green-500 hover:text-green-700"
              >
                Download
              </Button>
            </div>
          </div>
        ))}

        {/* Media Preview Modal */}
        {previewMedia && (
          <PreviewImgPdfFullscreen
            media={mediaToPreview}
            showPreview={previewMedia}
            setShowPreview={setPreviewMedia}
          />
        )}
      </div>
    );
  };

  const renderFormsCreate = () => {
    if (!data?.length)
      return <p className="text-gray-500">No form data available</p>;
    return (
      <div className="space-y-4">
        {data?.map((formSection, sectionIdx) => (
          <Card key={sectionIdx} className="p-4">
            {formSection?.formData?.map((field, fieldIdx) => (
              <div
                key={fieldIdx}
                className="grid grid-cols-1 md:grid-cols-2 gap-4 p-3 border-b last:border-b-0"
              >
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-700">
                      {field.fieldName}
                    </span>
                    {field.isMandatory && (
                      <Badge variant="destructive" className="text-xs">
                        Required
                      </Badge>
                    )}
                  </div>
                  <span className="text-sm text-gray-500">
                    Type: {field.fieldType}
                  </span>
                </div>
                <div className="p-2 bg-gray-50 rounded-md">
                  <span className="text-gray-700">
                    {field.fieldType === 'Range'
                      ? `${field?.fieldValue?.min || 0}-${field?.fieldValue?.max || 0}`
                      : typeof field.fieldValue === 'object'
                        ? JSON.stringify(field.fieldValue)
                        : field.fieldValue || 'No value provided'}
                  </span>
                </div>
              </div>
            ))}
          </Card>
        ))}
      </div>
    );
  };

  if (type === 'table') {
    return renderTable();
  } else if (type === 'media') {
    return renderMedia();
  } else if (type === 'audio') {
    return renderAudio();
  } else if (type === 'forms-create') {
    return renderFormsCreate();
  } else if (Array.isArray(data)) {
    return (
      <ul className="list-disc pl-6">
        {data?.map((item, index) => (
          <li key={index} className="mb-2">
            {item.label || item}
          </li>
        ))}
      </ul>
    );
  } else {
    return null; // Default fallback
  }
};

export default ViewDepFields;
