import { Fragment, useContext, useEffect, useState } from 'react';
import {
  // Link,
  useLocation,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { toast } from 'react-toastify';
import { ReactComponent as Briefcase } from '../../assets/svgs/briefcase.svg';
import {
  calculateIndexForPagination,
  downloadMedia,
  getPartVariantName,
  getProductVariantName,
  isValidUrl,
  mobileWidth,
  tabletWidth,
} from '../../helperFunction';
import { useGetBomsMutation } from '../../slices/assemblyBomApiSlice';
import { Store } from '../../store/Store';
import HistorySidebar from '../Kanban/HistorySidebar';
// import { useCreateBomCommentMutation } from '../../slices/assemblyBomCommentApiSlice';
import { useLazyGetAssetsQuery } from '../../slices/assetApiSlice';
import { useCreateFormMutation } from '../../slices/createFormapiSlice';
import { useGetDepartmentsQuery } from '../../slices/departmentApiSlice';
// import { useAddColumnToChildMutation } from '../../slices/departmentColumnApiSlice';
import { useMediaQuery } from 'react-responsive';
import { useAddAssemblyFormMutation } from '../../slices/assemblyFormApiSlice';
import { useGetPosForOptionsQuery } from '../../slices/createPoApiSlice';
import {
  useAddDepartmentRowMutation,
  useDeleteDepartmentRowMutation,
  useEditDepartmentRowMutation,
  useQueryDepartmentRowsQuery,
} from '../../slices/departmentRowApiSlice';
import {
  useGetMediaMetaQuery,
  useLazyGetMediaByIdQuery,
} from '../../slices/mediaSlice';
import { PAGINATION_LIMIT } from '../../utils/Constant';
import { customConfirm } from '../../utils/customConfirm';
import Button from '../global/components/Button';
import CaptureCamera from '../global/components/CaptureCamera';
import CreateFormFullScreenModal from '../global/components/CreateFormFullScreenModal';
import Header from '../global/components/Header';
import Input from '../global/components/Input';
import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import PdfViewer from '../global/components/PdfViewer';
import RightSidebar from '../global/components/RightSidebar';
import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import AssemblyForm from './AssemblyForm';
import BomCommentsModal from './BomCommentsModal';
import DepartmentAttachments from './DepartmentAttachments';
import DepartmentForm from './DepartmentForm';
import DynamicTable from './DynamicTable';
import ManageDepartmentRowModal from './ManageDepartmentRowModal';
import MediaElement from './MediaElement';
import MultiDepartmentForm from './MultiDepartmentForm';

const defaultAssemblyFormData = {
  formName: '',
  category: '',
  steps: [],
  isSequential: false,
};

const defaultAssemblyStepsData = {
  stepNumber: '',
  stepName: '',
  description: '',
  attachments: [],
  isMediaMandatory: false,
};

const TrComponent = ({ setlabel, i, label }) => {
  return (
    <>
      <div className="flex flex-col  mt-2">
        <label className="mb-1 font-semibold text-[#667085]">Label Name </label>
        <Input
          // value={label[i] || ''}
          onChange={(e) => {
            let temp = label;
            temp[i] = e.target.value;
            setlabel(temp);
          }}
          placeholder="Label"
        />
      </div>
    </>
  );
};

const DynamicDepartmentTables = ({ data }) => {
  const { dispatch, state } = useContext(Store);
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const [searchParams, setSearchParams] = useSearchParams({
    page: 1,
    limit: PAGINATION_LIMIT,
  });
  const location = useLocation();
  const path = location.pathname;
  // const [addColumnToChild] = useAddColumnToChildMutation();
  const navigate = useNavigate();
  const { requiresApproval = false } = data?.departmentChildNav;

  const [openModal, setOpenModal] = useState(
    searchParams.get('openModal')
      ? searchParams.get('openModal') === 'true'
        ? true
        : false
      : false
  );
  const [openFileModal, setOpenFileModal] = useState(false);
  const [sharedFiles, setSharedFiles] = useState([]);
  const [showRightSideBar, setShowRightSideBar] = useState(false);
  const [rowMediaData, setRowMediaData] = useState([]);
  const [preview, setPreview] = useState({
    openModal: false,
    file: {},
  });
  const [historySidebar, setHistorySidebar] = useState({
    open: false,
    steps: [],
  });

  const [DepartmentData, setDepartmentData] = useState([]);
  const [show, setShow] = useState(false); //eslint-disable-line
  const [shown, setShown] = useState(false); //eslint-disable-line
  const [editData, setEditData] = useState({});
  const [ShowModal, setShowModal] = useState(false);
  const [SelectedRow, setSelectedRow] = useState(null);
  const [ShowFormModal, setShowFormModal] = useState();
  const [Form, setForm] = useState(null);
  const [filledFormData, setFilledFormData] = useState({});
  const [selectedIndx, setSelectedIdx] = useState();
  const [bomData, setBomData] = useState(null);
  const [showBom, setShowBom] = useState(searchParams.get('showBom') ?? false);
  const [formManagementModal, setFormManagementModal] = useState(false);

  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [getAssets, { data: assets }] = useLazyGetAssetsQuery();
  const [getBoms, { data: boms }] = useGetBomsMutation();
  const [editDepartmentRow, { isLoading: isLoadingEditDep }] =
    useEditDepartmentRowMutation();
  const [addDepartmentRow, { isLoading: isLoadingDepAdd }] =
    useAddDepartmentRowMutation();
  const { data: media } = useGetMediaMetaQuery();
  const { data: department } = useGetDepartmentsQuery();
  // const [addBomComments] = useCreateBomCommentMutation();
  const { data: allPos = [] } = useGetPosForOptionsQuery();

  const [bomComment, setBomComment] = useState(null); //eslint-disable-line
  const [ShowCommentModal, setShowCommentModal] = useState(false);
  const [SelectedBom, setSelectedBom] = useState(null);
  const [HighLight, setHightLight] = useState(false);

  const page = +searchParams.get('page');
  const limit = +searchParams.get('limit');

  const { _id: depColId, departmentChildNav: childNav, columns } = data;

  const {
    data: depColData = {},
    isLoading: isLoadingGet,
    isFetching: isFetchingGet,
    refetch,
  } = useQueryDepartmentRowsQuery(
    {
      page,
      limit,
      depColId,
    },
    // { skip: !page || !limit || !depColId, refetchOnMountOrArgChange: true }
    { refetchOnMountOrArgChange: true }
  );
  const { results: departmentRows = [], totalPages, totalResults } = depColData;

  const [deleteDepartmentRow, { isLoading: isDeleteRowLoading }] =
    useDeleteDepartmentRowMutation();

  const handleRowDelete = async (id, idx) => {
    const dataToBeDeleted = DepartmentData[idx];
    if (dataToBeDeleted.new === true) {
      setDepartmentData(
        DepartmentData?.filter((_, index) => {
          return idx !== index;
        })
      );
      return;
    }
    if (!id && idx) {
      const nonDeletedRow = DepartmentData?.filter((_, index) => {
        return idx !== index;
      });
      setDepartmentData(nonDeletedRow);
      return;
    }
    const confirm = await customConfirm(
      'Are you sure you want to delete Row?',
      'delete'
    );
    if (!confirm) return;

    const res = await deleteDepartmentRow({ id }).unwrap();

    if (res) {
      toast.success('Deleted row successfully', { toastId: 'success' });
    }
  };

  useEffect(() => {
    getAssets().unwrap();
  }, [getAssets]);

  useEffect(() => {
    getBoms().unwrap();
  }, [getBoms]);

  useEffect(() => {
    if (searchParams.get('kanban') === 'true' && departmentRows && columns) {
      // const order = searchParams.get('orderId')
      const newRow = {};
      columns?.map((col) => {
        newRow[col?.type] = null;
      });
      setDepartmentData(() => {
        return [{ data: newRow, new: true }];
      });
    } else if (departmentRows && searchParams.get('addMore') === 'true') {
      const newRow = {};
      columns?.map((col) => {
        newRow[col?.type] = null;
      });
      setDepartmentData([
        { data: newRow, _id: Date.now(), new: true },
        ...[...departmentRows].reverse(),
      ]);
    } else {
      setDepartmentData([...departmentRows].reverse());
    }
  }, [departmentRows, columns]); //eslint-disable-line

  useEffect(() => {
    if (media) {
      let temp = [];
      for (let i of media.media) {
        for (let j of i.departments) {
          if (j?.value?._id === data?.department?._id) {
            temp.push(i);
            break;
          }
        }
      }
      setSharedFiles(temp);
    }
  }, [media, data?.department?._id]);

  const getMedia = async (id) => {
    const fileInfo = await getMediaById({ id });
    return fileInfo;
  };

  const handleStatus = async (rowdata, status) => {
    if (!rowdata?._id || !status) return;
    const isApproved = status === 'approved';

    const confirm = await customConfirm(
      `Are you sure you want to ${isApproved ? 'Approve' : 'Reject'}`,
      isApproved ? 'success' : 'fail'
    );

    if (!confirm) return;

    const Editdata = {
      data: { ...rowdata?.data, forms: rowdata?.forms },
      attachments: rowdata?.attachments,
      status,
    };

    const updated = await editDepartmentRow({
      id: rowdata?._id,
      data: Editdata,
    }).unwrap();

    if (updated) {
      toast.success(`Succesfully ${isApproved ? 'Approved' : 'Rejected'}`, {
        toastId: 'status',
      });
    }
  };

  const handleSaveData = async (rowdata) => {
    // console.log(rowdata)
    // return
    let arr = [];
    columns?.map((col) => {
      if (
        col?.isMandatory &&
        (!rowdata?.data?.[col?.name] ||
          rowdata?.data?.[col?.name]?.value === null ||
          rowdata?.data?.[col?.name]?.value === '')
      ) {
        arr.push(col?.name);
      }
    });
    if (arr?.length > 0) {
      toast.error(
        `${arr?.map((item) => ' ' + item)} ${arr?.length === 1 ? ' is' : ' are'} mandatory`
      );
      return;
    }
    if (rowdata?._id && !rowdata.new) {
      // edit data
      const Editdata = {
        data: { ...rowdata?.data, forms: rowdata?.forms },
        attachments: rowdata?.attachments,
      };

      const updated = await editDepartmentRow({
        id: rowdata?._id,
        data: Editdata,
      }).unwrap();
      if (updated) {
        toast.success('Department Row Updated');
        setShowModal(false);
        setShowFormModal(false);
      }
    } else {
      let emailCheck = true;
      const keys = Object.keys(rowdata?.data);
      for (let i of keys) {
        if (rowdata?.data?.[`${i}`]?.type === 'email') {
          if (
            !rowdata?.data?.[`${i}`]?.value
              .toLowerCase()
              .match(
                /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
              )
          ) {
            emailCheck = false;
            break;
          }
        }
      }
      if (emailCheck) {
        const addData = {
          ...rowdata,
          bomComment: bomComment,
          data: {
            ...rowdata.data,
            forms: rowdata?.forms,
            createForm: rowdata?.data?.forms,
          },
          department: data?.department?._id,
          departmentNav: data?.departmentNav?._id,
          departmentChildNav: data?.departmentChildNav?._id,
          departmentColumn: data?._id,
        };
        delete addData._id;
        const newRow = await addDepartmentRow({ data: addData }).unwrap();
        if (newRow) {
          toast.success('Department Row Added');
          dispatch({
            type: 'REFETCH_KANBAN_TILES',
          });
          const kanban = searchParams.get('kanban') === 'true';
          const orderId = searchParams.get('orderId');
          const navigateParams = {
            department: searchParams.get('department'),
            id: newRow?._id,
            refType: searchParams.get('refType'),
            page: searchParams.get('page'),
            taskId: searchParams.get('taskId'),
            orderId,
            index: searchParams.get('index'),
          };

          if (kanban && !orderId) {
            let time = new Date();
            dispatch({
              type: 'ADD_CARD',
              payload: {
                data: {
                  taskId: searchParams.get('taskID'),
                  // firstStepId: salesInquiryId,
                  stepPage: searchParams.get('page'),
                  updatedAt: time?.toDateString(),
                  obj: newRow,
                  refKey: searchParams.get('refType'),
                  currentDepartment: searchParams.get('department'),
                },
                currentColumn: searchParams.get('page'),
              },
            });
          }

          const filteredParams = Object.fromEntries(
            Object.entries(navigateParams).filter(
              ([_, value]) => value !== null
            )
          );

          const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;

          if (kanban) {
            navigate(navigateStr);
          }

          setShowFormModal(false);
          refetch();
        }
      } else {
        toast.error('Invalid Email', {
          theme: 'colored',
          position: 'top-right',
          toastId: 'INVALID_EMAIL',
        });
      }
    }
  };

  const handleMediaAdd = (e) => {
    for (let i in e) {
      const fr = new FileReader();
      if (e[i] instanceof File) {
        const name = e[i].name;
        const type = e[i].type;
        fr.onload = (e) => {
          setRowMediaData((prev) => {
            return [...(prev || []), { name, type, data: e.target.result }];
          });
        };
        fr.readAsDataURL(e[i]);
      }
    }
  };

  const setDepartments = (e, mediaName) => {
    let temp = [...rowMediaData];
    for (let i in temp) {
      if (temp[i]?.name === mediaName) {
        temp[i] = {
          ...temp[i],
          departments: e.target.value,
        };
      }
    }
    setRowMediaData(temp);
  };

  const handelAddMedia = () => {
    const Updated = DepartmentData.map((rowdata, idx) => {
      if (idx === SelectedRow) {
        return {
          ...rowdata,
          attachments: rowMediaData,
          data: {
            ...rowdata?.data,
            [selectedCol]: {
              type: rowdata?.data[selectedCol]?.type,
              value: rowMediaData,
            },
          },
        };
      } else {
        return rowdata;
      }
    });
    setDepartmentData(Updated);
    // const data = Updated[SelectedRow];
    // handleSaveData(data);
    setShowModal(false);
  };
  const hanldeSubmitForm = async () => {
    const fieldNameToKeyMap = Form.formData.reduce((map, field) => {
      const key = Object.keys(filledFormData).find((k) =>
        k.startsWith(field.fieldType.toLowerCase())
      );
      if (key) {
        map[field.fieldName] = key;
      }
      return map;
    }, {});

    // Check if all mandatory fields are filled and collect unfilled fields
    const unfilledMandatoryFields = Form.formData
      .filter((field) => field.isMandatory)
      .filter((field) => {
        const key = fieldNameToKeyMap[field.fieldName];
        const value = filledFormData[key];
        return (
          !key ||
          value === undefined ||
          value === null ||
          value === '' ||
          (Array.isArray(value) && value.length === 0) ||
          (typeof value === 'object' && Object.keys(value).length === 0)
        );
      })
      .map((field) => field.fieldName);

    if (unfilledMandatoryFields.length > 0) {
      const fieldList = unfilledMandatoryFields.join(', ');
      toast.error(`Please fill all mandatory fields: ${fieldList}`);
      return;
    }

    const Updated = DepartmentData.map((rowdata, idx) => {
      if (idx === SelectedRow) {
        return {
          ...rowdata,
          data: {
            ...rowdata.data,
            [selectedCol]: {
              type: rowdata?.data[selectedCol]?.type,
              value: {
                formName: Form?.formName,
                formData: filledFormData,
              },
            },
          },
        };
      } else {
        return rowdata;
      }
    });
    setDepartmentData(Updated);
    // const data = Updated[SelectedRow];
    // await handleSaveData(data);
    setFilledFormData({});
    setShowFormModal(false);
    toast.success('Department Row created');
  };

  const handleFillFormSubmit = async () => {
    const Updated = DepartmentData.map((rowdata, idx) => {
      if (idx === SelectedRow) {
        return {
          ...rowdata,
          data: {
            ...rowdata.data,
            [selectedCol]: {
              type: rowdata.data[selectedCol].type + '/fill',
              value: fillForms,
            },
          },
        };
      } else {
        return rowdata;
      }
    });
    setDepartmentData(Updated);
    setFillForms([]);
    setShowFillForm(false);
  };

  const handleAddComment = (item, value) => {
    const updatedItems = bomData?.[displayedBom]?.children?.map((product) => {
      if (product?._id === item?._id) {
        return {
          ...product,
          comment: value,
        };
      }

      const updatedSubItemL1 = product?.children?.map((subItem) => {
        if (subItem?._id === item?._id) {
          return {
            ...subItem,
            comment: value,
          };
        }

        const updatedSubItemL2 = subItem?.children?.map((subItem) => {
          if (subItem?._id === item?._id) {
            return {
              ...subItem,
              comment: value,
            };
          }
        });

        return {
          ...subItem,
          children: updatedSubItemL2,
        };
      });

      return {
        ...product,
        children: updatedSubItemL1,
      };
    });

    setBomData((prev) => {
      const updated = [...prev];
      updated[displayedBom] = {
        ...updated[displayedBom],
        children: updatedItems,
      };
      return updated;
    });
  };

  // const handleAddComment = async (item, value) => {
  //   const updatedProductList = bomData[displayedBom]?.children?.map(
  //     (product) => {
  //       if (product._id === item._id) {
  //         return {
  //           ...product,
  //           comment: value,
  //           // Add other properties to update as needed
  //         };
  //       }

  //       // Check in subItems
  //       const updatedSubItems = product.children?.map((subItem) => {
  //         if (subItem._id === item._id) {
  //           return {
  //             ...subItem,
  //             comment: value,
  //             // Add other properties to update as needed
  //           };
  //         }
  //         return subItem;
  //       });

  //       return {
  //         ...product,
  //         subItems: updatedSubItems,
  //       };
  //     }
  //   );
  //   console.log("UPDATED PRODUCT LIST", updatedProductList);
  //   setBomData((prev) => {
  //     const updated = [...prev];
  //     updated[displayedBom] = {
  //       ...updated[displayedBom],
  //       productList: updatedProductList,
  //     };
  //     return updated;
  //   });
  // };

  const handleBomCommentSubmit = async () => {
    let allComments = [];

    for (let index of bomData) {
      let comments = [];
      for (let i of index?.children) {
        for (let j of i?.children) {
          comments?.push({
            id:
              j?.part?._id ||
              j?.product?._id ||
              j?.partVariant?._id ||
              j?.productVariant?._id,
            comment: j?.comment,
          });
          for (let k of j?.children) {
            comments?.push({
              id:
                k?.part?._id ||
                k?.product?._id ||
                k?.partVariant?._id ||
                k?.productVariant?._id,
              comment: k?.comment,
            });
          }
        }
        comments?.push({
          id:
            i?.part?._id ||
            i?.product?._id ||
            i?.manualEntry ||
            i?.partVariant?._id ||
            i?.productVariant?._id,
          comment: i?.comment,
        });
      }
      allComments.push({ _id: index?._id, comments });
    }
    const Updated = DepartmentData.map((rowdata, idx) => {
      if (idx === SelectedRow) {
        return {
          ...rowdata,
          data: {
            ...rowdata.data,
            [selectedCol]: {
              type: rowdata.data[selectedCol].type,
              value: bomData.map((bom) => bom._id),
              comments: allComments,
            },
          },
        };
      } else {
        return rowdata;
      }
    });
    setDepartmentData(Updated);

    // setBomComment(response);
    setShowBom(false);
    toast.success('Bom Comment Saved');
  };

  const onCloseFxn = (bool) => {
    setFormManagementModal(bool);
    setFormData([]);
    setFormType(null);
    setForms([]);
  };

  // const [formId, setFormId] = useState({
  //   form: null,
  //   name: '',
  //   type: 'forms',
  // });

  const handleSave = async () => {
    const hasEmptyFormName = !!forms.find((form) => form.FormName === '');
    const hasEmptyField = !!forms.find((form) => form.FormData.length === 0);
    if (hasEmptyFormName) {
      toast.error('One or more form name is empty');
      return;
    }
    if (hasEmptyField) {
      toast.error('One or more forms has no fields');
      return;
    }
    let res;
    forms.map(async (form) => {
      let data = {};
      data = {
        formName: form.FormName,
        formData: form.FormData,
        formFor: form.FormType,
        inspectionData: form.inspectionData,
        requiresInspection: form.requiresInspection,
        isUsed: false,
      };
      res = await createForm({ data }).unwrap();
      if (res) {
        setFormManagementModal(false);
      }
    });

    const Updated = DepartmentData.map((rowdata, idx) => {
      if (idx === SelectedRow) {
        return {
          ...rowdata,
          data: {
            ...rowdata.data,
            [selectedCol]: {
              type: rowdata.data[selectedCol].type,
              value: forms,
            },
          },
        };
      } else {
        return rowdata;
      }
    });
    setDepartmentData(Updated);

    if (res) {
      setShowFormModal(false);
      setCreateFormType(null);
      setFormManagementModal(false);
      setForm({});
      setForms([]);
      setFilledFormData({});
      setSelectedRow(null);
      setSelectedCol(null);
      toast.success('Form Added Succesfully', { toastId: 'Form creation' });
    }
  };

  const handleFieldAdd = (idx) => {
    if (forms[idx].FieldName && forms[idx].FieldType) {
      let data = {};
      if (FieldType === 'MultiCheckbox') {
        data = {
          fieldName: forms[idx].FieldName,
          fieldType: forms[idx].FieldType,
          labelArray: label,
        };
      } else {
        data = {
          fieldName: forms[idx].FieldName,
          fieldType: forms[idx].FieldType,
        };
      }
      setFormData((curr) => [...(curr || []), data]);
      const updatedForms = [...forms];
      updatedForms[idx]['FormData'] = [
        ...(updatedForms[idx]['FormData'] || []),
        data,
      ];
      updatedForms[idx]['FieldName'] = '';
      updatedForms[idx]['FieldType'] = '';
      setForms(updatedForms);
      setFieldName('');
      setFieldType('');
      setlabel([]);
      setnoOption(null);
      setFieldType('');
    } else {
      toast.error('Cannot add empty field');
    }
  };

  const [addAssemblyFormData, setAddAssemblyFormData] = useState(
    defaultAssemblyFormData
  );

  const [assemblyFormSteps, setAssembyFormSteps] = useState(
    defaultAssemblyStepsData
  );
  const [stepCount, setStepCount] = useState(1);

  const handleAssemblyStepAdd = () => {
    if (assemblyFormSteps.stepName !== '') {
      setAddAssemblyFormData((prev) => ({
        ...prev,
        steps: [
          ...prev.steps,
          {
            ...assemblyFormSteps,
            stepNumber: stepCount,
          },
        ],
      }));
      toast.success('Step Added Successfully');
      setAssembyFormSteps(defaultAssemblyStepsData);
      setStepCount((prev) => prev + 1);
    } else {
      toast.error('Cannot add empty field');
    }
  };

  const [addAssemlyForm] = useAddAssemblyFormMutation();

  const handleAssemblySave = async () => {
    if (addAssemblyFormData.formName !== '') {
      const res = await addAssemlyForm(addAssemblyFormData).unwrap();
      if (res?.status !== 400) {
        toast.success('Form Added Successfully');
        const Updated = DepartmentData.map((rowdata, idx) => {
          if (idx === SelectedRow) {
            return {
              ...rowdata,
              data: {
                ...rowdata.data,
                [selectedCol]: {
                  type: rowdata.data[selectedCol].type,
                  value: addAssemblyFormData,
                },
              },
            };
          } else {
            return rowdata;
          }
        });
        setDepartmentData(Updated);
        setAddAssemblyFormData(defaultAssemblyFormData);
        setAssembyFormSteps(defaultAssemblyStepsData);
        setStepCount(1);
        setFormManagementModal(false);
        setFormData([]);
        setFormType(null);
        setForms([]);
      } else {
        toast.error('Error in adding form');
      }
    } else {
      toast.error('Cannot save without form name or empty data');
    }
  };

  const [createForm, { isLoading: isLoadingCreate }] = useCreateFormMutation();
  // const [editForm] = useEditFormMutation();
  // const [queryform] = useLazyQueryFormQuery();
  const [_FormName, setFormName] = useState([]);
  const [_FieldName, setFieldName] = useState('');
  const [FieldType, setFieldType] = useState('');
  const [_createFormType, setCreateFormType] = useState(null);
  const [formData, setFormData] = useState([]);
  const [selectedCol, setSelectedCol] = useState(null);
  const [mediaType, setMediaType] = useState('media');
  const [showHyperLink, setShowHyperLink] = useState(false);
  const [hyperLinks, setHyperLinks] = useState([]);
  const [formType, setFormType] = useState(null);
  const ctg = [
    'Date',
    'Range',
    'Range Threshold',
    'Check',
    'MultiCheckbox',
    'String',
    'QR',
  ];
  const [noOption, setnoOption] = useState(null);
  const [label, setlabel] = useState([]);

  const removeHandler = (idx, formIndex) => {
    setFormData(formData.filter((pro, pIdx) => pIdx !== idx));
    const updated = [...forms];
    updated[formIndex]['FormData'] = updated[formIndex]['FormData'].filter(
      (_, pIdx) => pIdx !== idx
    );
    setForms(updated);
  };
  useEffect(() => {
    setFilledFormData(
      DepartmentData[SelectedRow]?.data[selectedCol]?.value?.formData || []
    );
    setFormName(
      DepartmentData[SelectedRow]?.data[selectedCol]?.value?.formName || []
    );
    setFormData(
      DepartmentData[SelectedRow]?.data[selectedCol]?.value?.formData || []
    );
    if (
      DepartmentData[SelectedRow]?.data[selectedCol]?.type === 'form management'
    ) {
      setForms(
        DepartmentData[SelectedRow]?.data[selectedCol]?.value || [
          {
            FormName: '',
            FieldName: '',
            FieldType: '',
            FormType: '',
            FormData: [],
            noOption: 0,
            inspectionData: [],
            requiresInspection: false,
          },
        ]
      );
    }
    setFillForms(DepartmentData[SelectedRow]?.data[selectedCol]?.value || []);
    if (mediaType === 'audio') {
      setRowMediaData(
        DepartmentData[SelectedRow]?.data[selectedCol]?.value || []
      );
    }
    setCreateFormType(
      DepartmentData[SelectedRow]?.data[selectedCol]?.value?.formType || []
    );
    setHyperLinks(DepartmentData[SelectedRow]?.data[selectedCol]?.value || []);
    if (formType === 'assembly') {
      setAddAssemblyFormData(
        DepartmentData[SelectedRow]?.data[selectedCol]?.value ||
          defaultAssemblyFormData
      );
      setAssembyFormSteps(
        DepartmentData[SelectedRow]?.data[selectedCol]?.value?.steps ||
          defaultAssemblyStepsData
      );
    }
  }, [DepartmentData, SelectedRow, formType, mediaType, selectedCol]);

  useEffect(() => {
    if (
      boms &&
      DepartmentData &&
      selectedCol !== null &&
      SelectedRow !== null &&
      showBom
    ) {
      let selectedBom = boms
        ?.map((bom) => {
          if (
            DepartmentData[SelectedRow]?.data[selectedCol]?.value?.includes(
              bom._id
            )
          ) {
            return bom;
          }
        })
        .filter((bom) => bom !== undefined);
      if (
        DepartmentData[SelectedRow]?.data[selectedCol]?.comments &&
        selectedBom
      ) {
        let allComments = selectedBom.map((index) => {
          let productListAfterComments = index?.children?.flatMap((j) => {
            let subItems = j?.children?.map((k) => {
              let subItemsL2 = k?.children?.map((k) => {
                let foundComment = DepartmentData[SelectedRow]?.data[
                  selectedCol
                ]?.comments?.find((comment) => comment._id === index._id);
                let val = k;
                if (foundComment) {
                  for (let comment of foundComment?.comments) {
                    if (
                      comment?.id === k?.part?._id ||
                      comment?.id === k?.product?._id ||
                      comment?.id === k?.partVariant?._id ||
                      comment?.id === k?.productVariant?._id
                    ) {
                      return {
                        ...val,
                        comment: comment?.comment,
                      };
                    }
                  }
                }
                return val;
              });
              let foundComment = DepartmentData[SelectedRow]?.data[
                selectedCol
              ]?.comments?.find((comment) => comment._id === index._id);

              let val = { ...k, children: subItemsL2 };
              if (foundComment) {
                for (let comment of foundComment?.comments) {
                  if (
                    comment?.id === k?.part?._id ||
                    comment?.id === k?.product?._id ||
                    comment?.id === k?.partVariant?._id ||
                    comment?.id === k?.productVariant?._id
                  ) {
                    return {
                      ...val,
                      comment: comment?.comment,
                    };
                  }
                }
              }
              return val;
            });
            let foundComment = DepartmentData[SelectedRow]?.data[
              selectedCol
            ]?.comments?.find((comment) => comment._id === index._id);
            let val = { ...j, children: subItems };
            if (foundComment) {
              for (let comment of foundComment?.comments) {
                if (
                  comment?.id === j?.part?._id ||
                  comment?.id === j?.product?._id ||
                  comment?.id === j?.partVariant?._id ||
                  comment?.id === j?.productVariant?._id
                ) {
                  return {
                    ...val,
                    comment: comment?.comment,
                  };
                }
              }
            }
            return val;
          });

          return { ...index, children: [...productListAfterComments] };
        });
        setBomData(allComments);
      } else {
        setBomData(selectedBom);
      }
    }
  }, [boms, DepartmentData, SelectedRow, selectedCol, showBom]);

  const handleSubmitLinks = () => {
    const allValid = hyperLinks.every((link) => isValidUrl(link));

    if (!allValid) {
      toast.error('One or more links are invalid. Please check and try again.');
      return;
    }
    setShowHyperLink(false);
    const Updated = DepartmentData.map((rowdata, idx) => {
      if (idx === SelectedRow) {
        return {
          ...rowdata,
          data: {
            ...rowdata.data,
            [selectedCol]: {
              type: rowdata.data[selectedCol].type,
              value: hyperLinks,
            },
          },
        };
      } else {
        return rowdata;
      }
    });
    setDepartmentData(Updated);
    toast.success('Added Links Successfully');
  };

  useEffect(() => {
    if (searchParams?.get('kanban') === 'true') {
      setHightLight(true);
    }
  }, [searchParams]);

  useEffect(() => {
    if (!HighLight) return;
    let timer = null;
    if (timer) clearTimeout(timer);
    // setting the highlight query to false after 1500ms because we only want to highlight the last row for a moment
    timer = setTimeout(() => {
      setHightLight(false);
    }, 2000);
    return () => {
      clearTimeout(timer);
    };
  }, [searchParams, HighLight]);

  const handleAdd = () => {
    const newForm = {
      FormName: '',
      FieldName: '',
      FieldType: '',
      FormType: '',
      FormData: [],
      noOption: 0,
      inspectionData: [],
      requiresInspection: false,
    };
    setForms([...forms, newForm]);
    const visible = visibleForms.map(() => false);
    setVisibleForms([...visible, true]);
  };

  const handleChange = (index, key, value) => {
    const updatedForms = [...forms];
    updatedForms[index][key] = value;
    setForms(updatedForms);
  };

  const [forms, setForms] = useState([]);
  const [OpenCamera, setOpenCamera] = useState(false);
  const [capturedImage, setCapturedImage] = useState([]);
  const [fillForms, setFillForms] = useState([]);
  const [showFillForm, setShowFillForm] = useState(false);
  const [visibleForms, setVisibleForms] = useState(forms?.map(() => true));
  // const [createFormStep, setCreateFormStep] = useState(1)

  const toggleFormVisibility = (index) => {
    const updatedVisibility = [...visibleForms];
    updatedVisibility[index] = !updatedVisibility[index];
    setVisibleForms(updatedVisibility);
  };

  const handleNext = (setStep) => {
    const hasEmptyFormName = !!forms.find(
      (form) => form.FormName === '' && form.requiresInspection
    );
    const hasEmptyField = !!forms.find(
      (form) => form.FormData.length === 0 && form.requiresInspection
    );
    if (hasEmptyFormName) {
      toast.error('Inspection Step needs form name to be filled');
      return;
    }
    if (hasEmptyField) {
      toast.error('Inspection Step needs form fields to be filled');
      return;
    }
    setStep((prev) => prev + 1);
  };

  const [displayedBom, setDisplayedBom] = useState(-1);

  const setSteps = (taskId) => {
    let tasks = state?.allTiles;
    let chosenTask = tasks?.find((elem) => elem?.taskId === taskId);
    if (chosenTask) {
      setHistorySidebar({
        open: true,
        steps: chosenTask?.steps,
        orderId: chosenTask?._id,
      });
    }
  };

  return (
    <>
      <HistorySidebar sidebar={historySidebar} setSidebar={setHistorySidebar} />
      {OpenCamera ? (
        <CaptureCamera
          setpdf={setRowMediaData}
          capturedImage={capturedImage}
          setImageInfo={setCapturedImage}
          setOpenCamera={setOpenCamera}
        />
      ) : (
        <>
          {ShowCommentModal && (
            <BomCommentsModal
              setshowModal={setShowCommentModal}
              bom={SelectedBom}
              handleAddComment={handleAddComment}
              bomData={bomData}
              setBomData={setBomData}
            />
          )}
          {formManagementModal && (
            <Modal
              isMobile={isMobile}
              isTablet={isTablet}
              title={'Create Forms'}
              svg={<Briefcase className="h-8 w-8" />}
              description={'Add Field Name and associated type'}
              onCloseModal={onCloseFxn}
              onSubmit={
                formType === 'assembly' ? handleAssemblySave : handleSave
              }
              onNextClick={({ setStep }) => handleNext(setStep)}
              // onBackClick={}
              pages={
                forms.some((form) => form?.requiresInspection === true)
                  ? ['Create Form', 'Inspection Data']
                  : ['Create Form']
              }
              onAdd={
                formType !== 'assembly'
                  ? {
                      label: 'Add Form',
                      func: [handleAdd],
                      step: [0],
                    }
                  : {
                      label: 'Add',
                      func: [handleAssemblyStepAdd],
                      step: [0],
                    }
              }
              btnIsLoading={isLoadingCreate}
            >
              {({ step: tempStep }) => {
                const createFormStep = tempStep + 1;
                return formType === 'assembly' ? (
                  <AssemblyForm
                    addAssemblyFormData={addAssemblyFormData}
                    assemblyFormSteps={assemblyFormSteps}
                    setAddAssemblyFormData={setAddAssemblyFormData}
                    setAssembyFormSteps={setAssembyFormSteps}
                    stepCount={stepCount}
                  />
                ) : createFormStep === 1 ? (
                  <div className="flex flex-col gap-3">
                    {forms.map((form, idx) => (
                      <div
                        key={idx}
                        className={`border-2 rounded-lg ${visibleForms[idx] && 'pb-10'}`}
                      >
                        <div className="flex justify-around items-center py-2">
                          <h2 className={`text-xl font-semibold`}>
                            {visibleForms[idx]
                              ? ''
                              : `Form: ${form?.FormName || idx + 1}`}
                          </h2>
                          <div className="flex gap-2 justify-end">
                            <Button
                              className="px-3 py-1 text-sm"
                              onClick={() => toggleFormVisibility(idx)}
                            >
                              {visibleForms[idx] ? 'Hide' : 'Show'}
                            </Button>
                            <Button
                              className="px-3 py-1 text-sm bg-red-500 text-white hover:bg-red-600"
                              onClick={() => {
                                setForms((prev) => {
                                  return [
                                    ...prev.slice(0, idx),
                                    ...prev.slice(idx + 1),
                                  ];
                                });
                              }}
                            >
                              Remove Form
                            </Button>
                          </div>
                        </div>
                        {visibleForms[idx] && (
                          <>
                            <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
                              <div className="flex mt-5 col-span-3 gap-10 w-full items-center">
                                <div className="col-span-2">
                                  <label className=" font-semibold text-[#667085]">
                                    Form Name{' '}
                                  </label>
                                  <Input
                                    value={form.FormName}
                                    onChange={(e) =>
                                      handleChange(
                                        idx,
                                        'FormName',
                                        e.target.value
                                      )
                                    }
                                    placeholder="Form Name"
                                  />
                                </div>
                                <div className="flex col-span-1 items-center gap-3">
                                  <Input
                                    type="checkbox"
                                    checked={form.requiresInspection}
                                    onChange={(e) =>
                                      handleChange(
                                        idx,
                                        'requiresInspection',
                                        e.target.checked
                                      )
                                    }
                                    placeholder="requiresInspection"
                                  />
                                  <label className="mb-1 text-sm font-semibold text-[#667085]">
                                    Requires Inspection{' '}
                                  </label>
                                </div>
                              </div>

                              <div className="flex flex-col col-span-2 mt-5">
                                <label className="mb-1 font-semibold text-[#667085] flex items-center gap-[5px]">
                                  Form Type
                                </label>
                                <Select
                                  value={form.FormType}
                                  onChange={(e) =>
                                    handleChange(
                                      idx,
                                      'FormType',
                                      e.target.value
                                    )
                                  }
                                  options={[
                                    { label: 'QC', value: 'QC' },
                                    {
                                      label: 'Inspection',
                                      value: 'Inspection',
                                    },
                                    {
                                      label: 'Department',
                                      value: 'Department',
                                    },
                                  ]}
                                  placeholder="Select Type"
                                />
                              </div>

                              <div className="flex w-full col-span-3 gap-2 items-center mt-5">
                                <div className="flex flex-col col-span-1">
                                  <label className="mb-1 font-semibold text-[#667085]">
                                    Field Name{' '}
                                  </label>
                                  <Input
                                    value={form.FieldName}
                                    onChange={(e) =>
                                      handleChange(
                                        idx,
                                        'FieldName',
                                        e.target.value
                                      )
                                    }
                                    placeholder="Field Name"
                                  />
                                </div>

                                <div className="flex flex-col col-span-1">
                                  <label className="font-semibold text-[#667085] flex items-center gap-[5px]">
                                    Field Type
                                  </label>
                                  <Select
                                    value={form.FieldType}
                                    onChange={(e) =>
                                      handleChange(
                                        idx,
                                        'FieldType',
                                        e.target.value
                                      )
                                    }
                                    options={ctg.map((option) => ({
                                      name: option,
                                      value: option,
                                    }))}
                                    placeholder="Select Type"
                                  />
                                </div>

                                <Button
                                  className={'text-sm mt-5 col-span-1'}
                                  onClick={() => handleFieldAdd(idx)}
                                >
                                  +Add
                                </Button>
                              </div>

                              {form.FieldType === 'MultiCheckbox' && (
                                <div className="flex flex-col  mt-5 ml-4">
                                  <label className="mb-1 font-semibold text-[#667085]">
                                    No of Options{' '}
                                  </label>
                                  <Input
                                    type="number"
                                    value={noOption}
                                    onChange={(e) => {
                                      handleChange(
                                        idx,
                                        'noOption',
                                        +e.target.value
                                      );
                                    }}
                                    placeholder="Number of Options"
                                  />
                                </div>
                              )}
                            </div>

                            {form.noOption !== 0 && (
                              <div className="flex flex-row flex-wrap justify-start mt-4 px-8 gap-x-3">
                                {[...Array(+noOption)].map((e, i) => (
                                  <TrComponent
                                    setlabel={setlabel}
                                    i={i}
                                    label={label}
                                    key={i}
                                  />
                                ))}
                              </div>
                            )}

                            {form.FormData?.length > 0 ? (
                              <div className="w-full  overflow-scroll mt-8">
                                <Table className="w-full">
                                  <Table.Head>
                                    <Table.Row>
                                      <Table.Th>#</Table.Th>
                                      <Table.Th>FieldName</Table.Th>
                                      <Table.Th>FieldType</Table.Th>
                                      <Table.Th>Action</Table.Th>
                                    </Table.Row>
                                  </Table.Head>

                                  <Table.Body>
                                    {form.FormData?.map((pro, pIdx) => {
                                      return (
                                        <Table.Row key={pIdx}>
                                          <Table.Td>{pIdx + 1}</Table.Td>

                                          <Table.Td>{pro?.fieldName}</Table.Td>
                                          <Table.Td>{pro?.fieldType}</Table.Td>
                                          <Table.Td className="px-3 py-0.5">
                                            <span
                                              onClick={() =>
                                                removeHandler(pIdx, idx)
                                              }
                                              className="outline-none p-4 rounded text-red-primary"
                                            >
                                              Remove
                                            </span>
                                          </Table.Td>
                                        </Table.Row>
                                      );
                                    })}
                                  </Table.Body>
                                </Table>
                              </div>
                            ) : null}
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col gap-3">
                    {forms.map((form, idx) => (
                      <div
                        key={idx}
                        className={`border-2 rounded-lg pb-10 ${form.requiresInspection ? '' : 'hidden'}`}
                      >
                        <div className="flex justify-between items-center max-h-4">
                          <h2 className={`text-xl font-semibold pl-10 mt-10`}>
                            {visibleForms[idx]
                              ? ''
                              : `Form ${form?.FormName || idx + 1}`}
                          </h2>
                          <Button
                            onClick={() => toggleFormVisibility(idx)}
                            className={'mr-10 mt-10'}
                          >
                            {visibleForms[idx] ? 'Hide' : 'Show'}
                          </Button>
                        </div>
                        {visibleForms[idx] && (
                          <>
                            <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
                              <div className="flex mt-5 col-span-3 gap-10 w-full items-center">
                                <div className="col-span-2">
                                  <label className="mb-1 font-semibold text-[#667085]">
                                    Form Name : {form.FormName}
                                  </label>
                                </div>
                              </div>

                              <div className="mt-5 col-span-3 gap-10 w-full items-center">
                                <Table>
                                  <Table.Head>
                                    <Table.Row>
                                      <Table.Th>Field Name</Table.Th>
                                      <Table.Th>Check Condition</Table.Th>
                                      <Table.Th>Value</Table.Th>
                                    </Table.Row>
                                  </Table.Head>
                                  <Table.Body>
                                    {form.FormData?.map((field, fIdx) => {
                                      if (field?.fieldType === 'Date') {
                                        return (
                                          <Table.Row key={fIdx}>
                                            <Table.Td>
                                              {field.fieldName}
                                            </Table.Td>
                                            <Table.Td>
                                              <Select
                                                options={[
                                                  {
                                                    value: 'gt',
                                                    label: 'Greater Than',
                                                  },
                                                  {
                                                    value: 'lt',
                                                    label: 'Lesser Than',
                                                  },
                                                ]}
                                                value={
                                                  form.inspectionData[fIdx]
                                                    ?.condition
                                                }
                                                onChange={(e) =>
                                                  setForms((prev) => {
                                                    const updated = [...prev];
                                                    updated[idx].inspectionData[
                                                      fIdx
                                                    ] = {
                                                      ...updated[idx]
                                                        .inspectionData[fIdx],
                                                      condition: e.target.value,
                                                    };
                                                    return updated;
                                                  })
                                                }
                                              />
                                            </Table.Td>
                                            <Table.Td>
                                              <Input
                                                type="date"
                                                value={
                                                  form.inspectionData[fIdx]
                                                    ?.value
                                                }
                                                onChange={(e) =>
                                                  setForms((prev) => {
                                                    const updated = [...prev];
                                                    updated[idx].inspectionData[
                                                      fIdx
                                                    ] = {
                                                      ...updated[idx]
                                                        .inspectionData[fIdx],
                                                      value: e.target.value,
                                                    };
                                                    return updated;
                                                  })
                                                }
                                              />
                                            </Table.Td>
                                          </Table.Row>
                                        );
                                      }
                                      if (
                                        field?.fieldType === 'Range' ||
                                        field?.fieldType === 'Range Threshold'
                                      ) {
                                        return (
                                          <Table.Row key={fIdx}>
                                            <Table.Td>
                                              {field.fieldName}
                                            </Table.Td>
                                            <Table.Td>
                                              <div className="flex gap-3">
                                                <Input
                                                  type="number"
                                                  value={
                                                    form.inspectionData[fIdx]
                                                      ?.min
                                                  }
                                                  onChange={(e) =>
                                                    setForms((prev) => {
                                                      const updated = [...prev];
                                                      updated[
                                                        idx
                                                      ].inspectionData[fIdx] = {
                                                        ...updated[idx]
                                                          .inspectionData[fIdx],
                                                        min: +e.target.value,
                                                      };
                                                      return updated;
                                                    })
                                                  }
                                                  placeholder="Min"
                                                />
                                                <Input
                                                  type="number"
                                                  value={
                                                    form.inspectionData[fIdx]
                                                      ?.max
                                                  }
                                                  onChange={(e) =>
                                                    setForms((prev) => {
                                                      const updated = [...prev];
                                                      updated[
                                                        idx
                                                      ].inspectionData[fIdx] = {
                                                        ...updated[idx]
                                                          .inspectionData[fIdx],
                                                        max: +e.target.value,
                                                      };
                                                      return updated;
                                                    })
                                                  }
                                                  placeholder="Threshold"
                                                />
                                              </div>
                                            </Table.Td>
                                          </Table.Row>
                                        );
                                      }
                                    })}
                                  </Table.Body>
                                </Table>
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                );
              }}
            </Modal>
          )}
          {/* {ShowFormModal && (
            <Modal
              isMobile={isMobile}
              isTablet={isTablet}
              title="Form"
              onCloseModal={() => {
                setShowFormModal(false);
                setFormName('');
                setFormData([]);
                setForm({});
                setFilledFormData({});
                setSelectedRow(null);
                setSelectedCol(null);
              }}
              onSubmit={hanldeSubmitForm}
            >
              {() => {
                return (
                  <DepartmentForm
                    setForm={setForm}
                    initialData={
                      DepartmentData?.[SelectedRow]?.data[selectedCol]?.value
                        ?.formData || {}
                    }
                    Form={Form}
                    filledFormData={filledFormData}
                    setFilledFormData={setFilledFormData}
                    setShowFormModal={setShowFormModal}
                  />
                );
              }}
            </Modal>
          )} */}
          {ShowFormModal && (
            <CreateFormFullScreenModal
              title="Form"
              onClose={() => {
                setShowFormModal(false);
                setFormName('');
                setFormData([]);
                setForm({});
                setFilledFormData({});
                setSelectedRow(null);
                setSelectedCol(null);
              }}
              onSubmit={hanldeSubmitForm}
            >
              <DepartmentForm
                setForm={setForm}
                initialData={
                  DepartmentData?.[SelectedRow]?.data[selectedCol]?.value
                    ?.formData || {}
                }
                Form={Form}
                filledFormData={filledFormData}
                setFilledFormData={setFilledFormData}
                setShowFormModal={setShowFormModal}
              />
            </CreateFormFullScreenModal>
          )}

          {showFillForm && (
            <CreateFormFullScreenModal
              title="Form"
              onClose={() => {
                setShowFillForm(false);
                setFillForms([]);
                setSelectedRow(null);
                setSelectedCol(null);
              }}
              onSubmit={handleFillFormSubmit}
            >
              <MultiDepartmentForm
                fillForms={fillForms}
                setFillForms={setFillForms}
              />
            </CreateFormFullScreenModal>
          )}

          {/* {showFillForm && (
            <Modal
              isMobile={isMobile}
              isTablet={isTablet}
              title="Form"
              onCloseModal={() => {
                setShowFillForm(false);
                setFillForms([]);
                setSelectedRow(null);
                setSelectedCol(null);
              }}
              onSubmit={handleFillFormSubmit}
            >
              {() => {
                return (
                  <MultiDepartmentForm
                    fillForms={fillForms}
                    setFillForms={setFillForms}
                  />
                );
              }}
            </Modal>
          )} */}
          {ShowModal && (
            <Modal
              isMobile={isMobile}
              isTablet={isTablet}
              title="Attachments"
              onCloseModal={() => {
                setShowModal(false);
              }}
              onSubmit={handelAddMedia}
            >
              {() => {
                return (
                  <DepartmentAttachments
                    capturedImage={capturedImage}
                    setCapturedImage={setCapturedImage}
                    setOpenCamera={setOpenCamera}
                    isMobile={isMobile}
                    isTablet={isTablet}
                    rowMediaData={rowMediaData}
                    setDepartments={setDepartments}
                    setRowMediaData={setRowMediaData}
                    department={department}
                    handleMediaAdd={handleMediaAdd}
                    data={data}
                    mediaType={mediaType}
                  />
                );
              }}
            </Modal>
          )}
          {preview?.openModal && (
            <div
              className="fixed top-0 left-0 flex justify-between items-center w-screen h-screen bg-black/10 z-[999999]"
              onClick={() => {
                if (media?.type !== 'application/pdf')
                  setPreview((prev) => ({
                    ...prev,
                    openModal: false,
                  }));
              }}
            >
              <>
                {preview?.file?.type === 'application/pdf' ? (
                  <PdfViewer
                    file={preview?.file?.data}
                    name={preview?.file?.name}
                    closeClick={(e) => {
                      e.preventDefault();
                      setPreview((prev) => ({
                        ...prev,
                        openModal: false,
                      }));
                    }}
                  />
                ) : preview?.file?.type === 'audio' ? (
                  <div className="flex flex-col items-center justify-center ml-[40%]">
                    <audio
                      src={preview?.file?.data}
                      controls
                      className="h-full aspect-video "
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <img
                      className="h-[90%] aspect-video object-contain"
                      src={preview?.file?.data}
                      alt=""
                    />
                  </div>
                )}
              </>
            </div>
          )}
          {openFileModal && (
            <Modal
              isMobile={isMobile}
              isTablet={isTablet}
              title={'Shared Files'}
              onCloseModal={() => {
                setOpenFileModal(false);
              }}
            >
              {() => (
                <>
                  <div className="flex gap-3 items-center flex-wrap justify-center">
                    {sharedFiles?.map((file) => {
                      return (
                        <MediaElement
                          item={file}
                          getMedia={getMedia}
                          setPreview={setPreview}
                          key={file?._id}
                        />
                      );
                    })}
                  </div>
                </>
              )}
            </Modal>
          )}
          {showBom && (
            <Modal
              isMobile={isMobile}
              isTablet={isTablet}
              title={'Bom Comments'}
              onCloseModal={() => setShowBom(false)}
              onSubmit={() => handleBomCommentSubmit()}
            >
              {() => (
                <>
                  <div className="mb-2">
                    Select BOM:
                    <MultiSelect
                      value={bomData?.map((bom) => bom._id)}
                      AddOption={'+ Add BOM'}
                      handleAddFunction={() => {
                        if (
                          !searchParams.get('kanban') ||
                          searchParams.get('kanban') !== true
                        ) {
                          navigate(
                            `/jobs/workorder?selectedTab=bom&modalOpen=true&path=${path}&showBom=${showBom}`
                          );
                        }
                        if (searchParams.get('orderId')) {
                          navigate(
                            `/jobs/workorder?selectedTab=bom&modalOpen=true&path=${path}&showBom=${showBom}&kanban=${searchParams.get('kanban')}&department=${searchParams.get('department')}&refType=${searchParams.get('refType')}&page=${searchParams.get('page')}&orderId=${searchParams.get('orderId')}&index=${searchParams.get('index')}`
                          );
                        }
                        if (!searchParams.get('orderId')) {
                          navigate(
                            `/jobs/workorder?selectedTab=bom&modalOpen=true&path=${path}&showBom=${showBom}&kanban=${searchParams.get('kanban')}&department=${searchParams.get('department')}&refType=${searchParams.get('refType')}&page=${searchParams.get('page')}&index=${searchParams.get('index')}`
                          );
                        }
                      }}
                      options={boms?.map((bom) => ({
                        label: bom?.name,
                        value: bom?._id,
                      }))}
                      onChange={(e) => {
                        setShow(false);
                        let selectedBom = e.target.value.map(
                          (bom) => bom.value
                        );
                        setBomData(
                          boms.filter((bom) => selectedBom.includes(bom._id))
                        );
                        setShowBom(true);
                      }}
                    />
                  </div>
                  <div className="flex gap-3 overflow-x-scroll">
                    {bomData?.map((bom, i) => (
                      <Button
                        key={i}
                        onClick={() => setDisplayedBom(i)}
                        className={`${displayedBom === i ? '' : '!bg-blue-300'}`}
                      >
                        {bom?.name}
                      </Button>
                    ))}
                  </div>
                  {displayedBom !== -1 && (
                    <Table key={bomData[displayedBom]?._id}>
                      <Table.Head>
                        <Table.Row>
                          <Table.Th>SR NO.</Table.Th>
                          <Table.Th>category</Table.Th>
                          <Table.Th>Item Name</Table.Th>
                          <Table.Th>Units</Table.Th>
                          <Table.Th>Subitem</Table.Th>
                          <Table.Th>Comment</Table.Th>
                        </Table.Row>
                      </Table.Head>
                      <Table.Body>
                        {bomData[displayedBom]?.children?.map((item, idx) => (
                          <>
                            <Table.Row key={idx}>
                              <Table.Td>{idx}</Table.Td>
                              <Table.Td>{item?.category}</Table.Td>
                              <Table.Td>
                                {item?.part?.name ||
                                  item?.product?.name ||
                                  item?.manualEntry ||
                                  (item?.partVariant
                                    ? getPartVariantName(item?.partVariant)
                                    : item?.productVariant
                                      ? getProductVariantName(
                                          item?.productVariant
                                        )
                                      : '-')}
                              </Table.Td>
                              <Table.Td>{item?.units}</Table.Td>
                              <Table.Td>No</Table.Td>
                              <Table.Td>
                                <Input
                                  type="text"
                                  placeholder="Enter comments"
                                  onChange={(e) =>
                                    handleAddComment(item, e.target.value)
                                  }
                                  value={item?.comment}
                                />
                              </Table.Td>
                            </Table.Row>
                            {item?.children?.map((s, i) => (
                              <Table.Row key={i} className={`!bg-slate-200`}>
                                <Table.Td>{i + 1}</Table.Td>
                                <Table.Td>{s?.category}</Table.Td>
                                <Table.Td>
                                  {s?.part?.name ||
                                    s?.product?.name ||
                                    s?.manualEntry ||
                                    (s?.partVariant
                                      ? getPartVariantName(s?.partVariant)
                                      : s?.productVariant
                                        ? getProductVariantName(
                                            s?.productVariant
                                          )
                                        : '-')}
                                </Table.Td>
                                <Table.Td>{s?.units}</Table.Td>
                                <Table.Td>Yes</Table.Td>
                                <Table.Td>
                                  <Input
                                    type="text"
                                    placeholder="Enter comments"
                                    onChange={(e) =>
                                      handleAddComment(s, e.target.value)
                                    }
                                    value={s?.comment}
                                  />
                                </Table.Td>
                              </Table.Row>
                            ))}
                          </>
                        ))}
                      </Table.Body>
                    </Table>
                  )}
                </>
              )}
            </Modal>
          )}
          {showHyperLink && (
            <Modal
              isMobile={isMobile}
              isTablet={isTablet}
              title={'Add Hyperlinks'}
              onSubmit={() => handleSubmitLinks()}
              onCloseModal={() => setShowHyperLink(false)}
            >
              {() => {
                return (
                  <div>
                    <Table>
                      <Table.Head>
                        <Table.Row>
                          <Table.Th>#</Table.Th>
                          <Table.Th>Links</Table.Th>
                          <Table.Th>Visit</Table.Th>
                          <Table.Th>Delete</Table.Th>{' '}
                          {/* Added delete header */}
                        </Table.Row>
                      </Table.Head>
                      <Table.Body>
                        {Array.isArray(hyperLinks) &&
                          hyperLinks?.map((link, idx) => (
                            <Table.Row key={idx}>
                              <Table.Td>{idx + 1}</Table.Td>
                              <Table.Td>
                                <Input
                                  value={link}
                                  onChange={(e) =>
                                    setHyperLinks((prev) => {
                                      const updatedLinks = [...prev];
                                      updatedLinks[idx] = e.target.value;
                                      return updatedLinks;
                                    })
                                  }
                                />
                              </Table.Td>
                              <Table.Td>
                                <Button
                                  className="text-blue-500"
                                  onClick={() => {
                                    if (isValidUrl(link)) {
                                      window.open(link, '_blank');
                                    } else {
                                      toast.error(
                                        'Invalid URL. Please enter a valid link.'
                                      );
                                    }
                                  }}
                                >
                                  Visit
                                </Button>
                              </Table.Td>
                              <Table.Td>
                                <Button
                                  className="bg-red-500 text-white px-3 py-1 rounded"
                                  onClick={() => {
                                    setHyperLinks((prev) =>
                                      prev.filter((_, index) => index !== idx)
                                    );
                                  }}
                                >
                                  Delete
                                </Button>
                              </Table.Td>
                            </Table.Row>
                          ))}
                      </Table.Body>
                    </Table>
                    <div>
                      <Button
                        className={'mt-5'}
                        onClick={() => {
                          setHyperLinks((prev) => [...prev, '']);
                        }}
                      >
                        + Add Links
                      </Button>
                    </div>
                  </div>
                );
              }}
            </Modal>
          )}
          <RightSidebar
            openSideBar={showRightSideBar}
            setOpenSideBar={setShowRightSideBar}
          >
            <h3>Attachments</h3>

            <Table>
              <Table.Body>
                {Array.isArray(rowMediaData) &&
                  rowMediaData?.map((file, index) => (
                    <Fragment key={index}>
                      <Table.Row>
                        <Table.Td className="!w-fit">
                          {file?.fname ?? file?.name}
                        </Table.Td>
                        <Table.Td className="text-right">
                          <Button onClick={() => downloadMedia(file)}>
                            Download
                          </Button>
                        </Table.Td>
                      </Table.Row>
                    </Fragment>
                  ))}
              </Table.Body>
            </Table>
          </RightSidebar>
          <div className="w-full">
            {openModal && (
              <ManageDepartmentRowModal
                setOpenModal={setOpenModal}
                colData={data}
                editData={editData}
                setEditData={setEditData}
                currentDepartmentId={data?.department?._id}
              />
            )}
            <div className="w-full flex justify-between">
              <Header
                title={childNav?.cname}
                description={`Manage ${childNav?.cname} here`}
                hasInfoPopup={false}
                classNames="w-full"
              />
              <div className="flex gap-3">
                <Button
                  onClick={() => {
                    setOpenFileModal(true);
                  }}
                  // disabled={!sharedFiles?.length}
                  className="w-full !h-7"
                  color="green"
                >
                  Files
                </Button>
              </div>
            </div>
            {columns?.length > 0 && (
              <div className="flex justify-end">
                <Button
                  onClick={() => {
                    const newRow = {};
                    columns?.map((col) => {
                      newRow[col?.type] = null;
                    });

                    setDepartmentData((prev) => {
                      return [
                        { data: newRow, _id: Date.now(), new: true },
                        ...prev,
                      ];
                    });
                  }}
                  className="!py-1 mb-1"
                >
                  + Add More
                </Button>
              </div>
            )}
            {isLoadingGet ? (
              <Spinner />
            ) : (
              <>
                {columns?.length === 0 ? (
                  <>
                    <p>
                      You have not added any columns to this page, Please add
                      column first.
                    </p>
                  </>
                ) : (
                  <DynamicTable
                    columns={columns}
                    DepartmentData={DepartmentData}
                    isFetchingGet={isFetchingGet}
                    HighLight={HighLight}
                    calculateIndexForPagination={calculateIndexForPagination}
                    page={page}
                    limit={limit}
                    requiresApproval={requiresApproval}
                    setSteps={setSteps}
                    searchParams={searchParams}
                    setShowRightSideBar={setShowRightSideBar}
                    setRowMediaData={setRowMediaData}
                    setDepartmentData={setDepartmentData}
                    assets={assets}
                    boms={boms}
                    setShowModal={setShowModal}
                    setSelectedRow={setSelectedRow}
                    setShowFormModal={setShowFormModal}
                    setForm={setForm}
                    setBomData={setBomData}
                    setShowBom={setShowBom}
                    setFormManagementModal={setFormManagementModal}
                    setShowCommentModal={setShowCommentModal}
                    setSelectedBom={setSelectedBom}
                    setSelectedCol={setSelectedCol}
                    setMediaType={setMediaType}
                    setShowHyperLink={setShowHyperLink}
                    setFormType={setFormType}
                    allPos={allPos}
                    setShowFillForm={setShowFillForm}
                    handleStatus={handleStatus}
                    handleSaveData={handleSaveData}
                    handleRowDelete={handleRowDelete}
                    isLoadingDepAdd={isLoadingDepAdd}
                    isLoadingEditDep={isLoadingEditDep}
                    selectedIndx={selectedIndx}
                    isDeleteRowLoading={isDeleteRowLoading}
                    totalPages={totalPages}
                    totalResults={totalResults}
                    setSearchParams={setSearchParams}
                    setSelectedIdx={setSelectedIdx}
                    setShown={setShown}
                    setShow={setShow}
                  />
                )}
              </>
            )}
          </div>
        </>
      )}
    </>
  );
};

export default DynamicDepartmentTables;
