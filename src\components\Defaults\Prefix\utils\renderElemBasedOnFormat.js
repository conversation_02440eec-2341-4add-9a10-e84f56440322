import { MdEdit } from 'react-icons/md';
import NewInput from '../../../global/components/NewInput';
import Select from '../../../global/components/Select';

const renderElemBasedOnFormat = (
  format,
  idIndex,
  idType,
  setFormat,
  setShowEditOptions,
  setDropdown
) => {
  let component;
  let tempFormat = format;
  if (Object.keys(tempFormat).length == 0) {
    // setFormatComponent({});
    return {};
  } else {
    Object.keys(tempFormat).forEach((type) => {
      let elem = type.substring(0, type.indexOf('_'));
      switch (elem) {
        case 'String': {
          const StringInput = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 flex items-center relative mr-2">
              <NewInput
                type="text"
                value={tempFormat[type]}
                className="w-max"
                onChange={(e) => {
                  setFormat((prev) => {
                    return {
                      ...prev,
                      [idType]: [
                        ...prev[idType].slice(0, idIndex),
                        {
                          ...prev[idType][idIndex],
                          [type]: e.target.value,
                        },
                        ...prev[idType].slice(idIndex + 1),
                      ],
                    };
                  });
                }}
              />
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: StringInput,
          };
          break;
        }
        case 'MM-YY': {
          const MonthYear = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 flex border items-center rounded-lg relative mr-2">
              <p className="w-max">MM-YY</p>
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: MonthYear,
          };
          break;
        }
        case 'DD-MM-YY': {
          const DayMonthYear = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 flex border items-center rounded-lg relative mr-2">
              <p className="w-max">DD-MM-YY</p>
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: DayMonthYear,
          };
          break;
        }
        case 'Increment': {
          const Increment = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 flex items-center relative mr-2">
              <NewInput
                type="number"
                className="w-max"
                value={tempFormat[type]}
                onChange={(e) => {
                  setFormat((prev) => {
                    return {
                      ...prev,
                      [idType]: [
                        ...prev[idType].slice(0, idIndex),
                        {
                          ...prev[idType][idIndex],
                          [type]: e.target.value,
                        },
                        ...prev[idType].slice(idIndex + 1),
                      ],
                    };
                  });
                }}
              />
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: Increment,
          };
          // setFormatComponent((prev) => {
          //   return {
          //     ...prev,
          //     [type]: Increment,
          //   };
          // });
          break;
        }
        case 'UserEntry': {
          const UserEntry = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 pt-1 pr-1 flex items-center relative mr-2">
              <NewInput
                type="text"
                placeholder="User Entry"
                className="w-max"
                disabled
              />
              <MdEdit
                className="absolute end-0 top-0 text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: UserEntry,
          };
          break;
        }
        case 'WorkOrderId': {
          const WorkOrderId = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <div className="w-max">Work Order ID</div>
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: WorkOrderId,
          };
          break;
        }
        case 'JobId': {
          const JobId = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <p className="w-max">Job ID</p>
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
            // <></>
          );
          component = {
            ...(component || {}),
            [type]: JobId,
          };
          break;
        }
        case 'ModelName': {
          const ModelName = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <p className="w-max">Model Name</p>
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: ModelName,
          };
          break;
        }
        case 'Process': {
          const Process = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <p className="w-max">Process</p>
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: Process,
          };
          break;
        }
        case 'BatchNo': {
          const BatchNo = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <p className="w-max">Batch No.</p>
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: BatchNo,
          };
          break;
        }
        case 'InputScreen': {
          const InputScreen = (
            <div className="lg:w-40 xl:w-44 2xl:w-48 2xl:h-9 mt-auto flex border items-center rounded-lg relative py-1 px-3 mr-3">
              <p className="w-max">Input Screen</p>
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={() => {
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: InputScreen,
          };
          break;
        }
        case 'Dropdown': {
          const Dropdown = (
            <div
              className="flex pt-1 pr-1 relative"
              onClick={() =>
                setDropdown(() => ({
                  open: true,
                  idType: idType,
                  type: type,
                  options: tempFormat?.[type] || [],
                  idIndex: idIndex,
                }))
              }
            >
              <Select className="w-max" disabled placeholder="Dropdown">
                Dropdown
              </Select>
              <MdEdit
                className="absolute end-[-0.25rem] top-[-0.25rem] text-white bg-blue-400 rounded-full w-[20px] h-[20px] p-1 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowEditOptions({
                    open: true,
                    idType: idType,
                    type: type,
                    selectedIndex: idIndex,
                  });
                }}
              />
            </div>
          );
          component = {
            ...(component || {}),
            [type]: Dropdown,
          };
          break;
        }
      }
    });
    return component;
  }
};

export default renderElemBasedOnFormat;
