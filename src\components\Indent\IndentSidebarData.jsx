import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  FormatDate,
  checkApprovalAccess,
  getCorrespondingConversionFactor,
  handlePdf,
} from '../../helperFunction';
// import useDebounceValue from '../hooks/useDebounceValue';
import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  FilePdfOutlined,
} from '@ant-design/icons';
import { Button } from 'antd';
import {
  useGetAllPartsForOptionsQuery,
  useGetPartsQuery,
  useLazyGetPartsBySpecficVendorQuery,
  useUpdatePartMutation,
} from '../../slices/partApiSlice';
import { useLazyGetPdfQuery } from '../../slices/pdfApiSlice';
import { useGetAllProductsForOptionsQuery } from '../../slices/productApiSlice';
import { useGetAllVendorsForOptionsQuery } from '../../slices/vendorApiSlice';
import { Store } from '../../store/Store';
import { toCapitalize } from '../../utils/toCapitalize';
import MultiSelect from '../global/components/MultiSelect';
import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import Tooltip from '../global/components/ToolTip';

const statusColors = {
  pending: 'bg-yellow-400/90 text-white',
  rejected: 'bg-red-500/90 text-white',
  approved: 'bg-emerald-500/90 text-white',
};

const poColors = {
  'not created': 'bg-red-500/90 text-white',
  created: 'bg-emerald-500/90 text-white',
};

const IndentSidebarData = ({
  indent,
  approveindent,
  rejectindent,
  deleteindent,
  edidindent,
  setshowEditModal,
  setshowSidebar,
  handelVendorChange,
  copyIndent,
  setShowCopyModal,
  setOpenVendorAddModal,
  fromKanban = false,
}) => {
  const {
    state: { user },
  } = useContext(Store);
  const { data: allparts } = useGetAllPartsForOptionsQuery();
  const { data: allProducts } = useGetAllProductsForOptionsQuery();
  const [assignedVendorDropdown, setAssignedVendorDropdown] = useState(false);
  const [SpecficVendorsAsPerPart, setSpecficVendorsAsPerPart] = useState([]);
  const [
    getPartsBySpecficVendor,
    { data: partBySpecficVendor, isLoading: partBySpecficVendorLoading },
  ] = useLazyGetPartsBySpecficVendorQuery();

  const { data: allVendors } = useGetAllVendorsForOptionsQuery();
  const [updatePart] = useUpdatePartMutation();
  const { data: allParts } = useGetPartsQuery();
  const [setpartInfo, setSetpartInfo] = useState([]);
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();

  const hasApprovalAccess = checkApprovalAccess('/purchase/indent');
  const navigate = useNavigate();
  const [data, setData] = useState({
    vendors: [],
    vendor_details: [],
  });

  useEffect(() => {
    setSetpartInfo(allParts?.parts?.items);
  }, [allParts?.parts?.items]);

  const handleSubmitForm = async (e) => {
    e.preventDefault();
    let editData = allparts?.find(
      (el) => el?.name.trim() === indent?.product_name.trim()
    );
    if (indent?.type === 'PartVariant') {
      editData = editData?.part;
    }
    const dataInfo = {
      name: editData?.name,
      description: editData?.description,
      media: editData?.media,
      id: indent?.type === 'PartVariant' ? editData?._id : editData?.value,
      vendors: data?.vendors,
      stores: editData?.stores,
      storeArea: editData?.storeArea,
      quantityThreshold: editData?.quantityThreshold,
      requiresInspections: editData?.requiresInspections,
      createForm: editData?.createForm,
      category: editData?.category,
      vendor_details: data?.vendor_details,
      hsn_sacCode: editData?.hsn_sacCode,
      thumbNails: editData?.thumbNails,
      defaultFormData: editData?.defaultFormData,
    };
    const res = await updatePart({
      part: dataInfo,
      id: dataInfo?.id,
    }).unwrap();
    if (res) {
      setData({});
      setAssignedVendorDropdown(false);
      toast.success('Part updated successfully', {
        theme: 'colored',
        position: 'top-right',
      });
    }
  };

  useEffect(() => {
    if (!indent) return;
    //fetching all vendors as per specfic part
    const getVendorsDetailsAsperPart = async (type, id) => {
      if (type === 'PartVariant') {
        id = id.split('-')[0];
      }
      if (type === 'Part' || type === 'PartVariant') {
        const res = await getPartsBySpecficVendor({ id }).unwrap();
        if (res) {
          setSpecficVendorsAsPerPart(partBySpecficVendor);
        }
      }
    };
    const productName = indent?.product_name;
    getVendorsDetailsAsperPart(indent?.type, productName);
  }, [
    indent,
    getPartsBySpecficVendor,
    assignedVendorDropdown,
    setpartInfo,
    partBySpecficVendor,
  ]);
  // Check if edit operations are allowed
  const isEditAllowed = () => {
    if (!user?.canEditIndent) {
      return false;
    }
    if (indent?.status?.toLowerCase() === 'approved') {
      if (user?.canEditApprovedIndent) {
        return true;
      }
      return false;
    }
    return true;
  };

  const isDeleteAllowed = () => {
    if (!user?.canDeleteIndent) {
      return false;
    }
    if (indent?.status?.toLowerCase() === 'approved') {
      return false;
    }
    return true;
  };

  useEffect(() => {
    setData((prev) => {
      return {
        ...prev,
        vendors:
          SpecficVendorsAsPerPart?.vendors?.map((vendor) => vendor?._id) || [],
      };
    });
  }, [SpecficVendorsAsPerPart?.vendors]);
  let conversion;
  [...(allparts || []), ...(allProducts || [])]?.map((part) => {
    if (indent?.product_name == part?.name) {
      conversion = getCorrespondingConversionFactor(indent?.uom, part);
    }
  });
  return (
    <section>
      {/* Action Buttons Row */}
      <div className="flex justify-end items-center gap-2 mb-2">
        {!fromKanban && (
          <>
            {isFetchingPdf ? (
              <div>
                <Spinner size={4} />
              </div>
            ) : (
              <Tooltip title="Download PDF">
                <FilePdfOutlined
                  onClick={() => handlePdf(getPdf, indent?._id, 'indent', null)}
                />
              </Tooltip>
            )}
            {isEditAllowed() && (
              <Tooltip text="Edit">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => {
                    edidindent(indent?._id);
                    setshowEditModal(true);
                    setshowSidebar(false);
                  }}
                  className="!p-2 hover:!bg-gray-100 rounded-full"
                />
              </Tooltip>
            )}
            {isDeleteAllowed() && (
              <Tooltip text="Delete">
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => deleteindent(indent?._id)}
                  className="!p-2 hover:!bg-gray-100 rounded-full"
                />
              </Tooltip>
            )}
            <Tooltip text="Copy">
              <Button
                type="text"
                icon={<CopyOutlined />}
                onClick={() => {
                  copyIndent(indent?._id);
                  setShowCopyModal(true);
                  setshowSidebar(false);
                }}
                className="!p-2 hover:!bg-gray-100 rounded-full"
              />
            </Tooltip>
          </>
        )}
      </div>

      {/* Details Table */}
      <div className="bg-white rounded-lg overflow-hidden border border-gray-100">
        <div className="divide-y divide-gray-100">
          {[
            ['Indent No', indent?.indent_no],
            ['Indent Date', FormatDate(indent?.createdAt)],
            ['Requested By', indent?.request_by],
            [
              'Product Name',
              <div key="product-name" className="max-w-[60ch] break-words">
                {indent?.product_name}
              </div>,
            ],
            [
              'UOM',
              <div key="uom">
                <div className="whitespace-nowrap">{indent?.uom}</div>
                {conversion && (
                  <span className="mt-2 text-sm text-gray-500">
                    {conversion.conversionValue +
                      '-' +
                      conversion.conversionUnit}
                  </span>
                )}
              </div>,
            ],
            ['Quantity', indent?.quantity],
            ['Remarks', indent?.remark],
            ['Delivery Date', FormatDate(indent?.delivery_date)],
            [
              'Vendor',
              indent?.type === 'Part' || indent?.type === 'PartVariant' ? (
                <Select
                  menuPosition="fixed"
                  className="!min-w-[190px]"
                  value={indent?.vendor_name}
                  isLoading={partBySpecficVendorLoading}
                  options={[
                    { label: '+Create Vendor', value: '+addVendor' },
                    { label: '+Assign Vendor', value: '+assignVendor' },
                    ...(SpecficVendorsAsPerPart?.vendors?.map((vendor) => ({
                      label: vendor?.name,
                      value: vendor?.name,
                    })) || []),
                  ]}
                  onChange={(e) => {
                    if (e.target.value === '+addVendor') {
                      setOpenVendorAddModal(true);
                    } else if (e.target.value === '+assignVendor') {
                      setAssignedVendorDropdown(true);
                    } else {
                      handelVendorChange(e, indent._id);
                    }
                  }}
                />
              ) : (
                <div className="ml-1">-</div>
              ),
            ],
            [
              'Indent Status',
              <span
                key="indent-status"
                className={`${
                  statusColors[indent?.status]
                } px-3 py-[0.3rem] rounded-full text-sm font-medium`}
              >
                {toCapitalize(indent?.status)}
              </span>,
            ],
            [
              'PO',
              <span
                key="po"
                className={`${
                  poColors[indent?.po]
                } px-3 py-[0.3rem] rounded-full text-sm font-medium`}
              >
                {toCapitalize(indent?.po)}
              </span>,
            ],
          ].map(([label, value], idx) => (
            <div
              key={idx}
              className="px-6 py-4 flex justify-between items-center hover:bg-gray-50"
            >
              <div className="text-sm font-medium text-gray-500">{label}</div>
              <div className="text-sm text-gray-900">{value}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Vendor Assignment Section */}
      {assignedVendorDropdown && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-100">
          <div className="flex gap-3 items-center">
            <div className="flex-1">
              <MultiSelect
                placeholder="Select Vendors"
                onChange={(e) => {
                  const tmp = e.target.value?.map((el) => ({
                    name: el?.label,
                    vendor: el?.value,
                    rate:
                      data?.vendor_details?.find(
                        (vendor) => vendor.name === el?.label
                      )?.rate || 0,
                    discount:
                      data?.vendor_details?.find(
                        (vendor) => vendor.name === el?.label
                      )?.discount || 0,
                  }));
                  setData((prev) => ({
                    ...prev,
                    vendors: e.target.value?.map((el) => el.value),
                    vendor_details: tmp,
                  }));
                }}
                value={data.vendors}
                name="vendors"
                closeMenuOnSelect={false}
                options={allVendors?.map((e) => ({
                  value: e.value,
                  label: e.label,
                }))}
              />
            </div>
            <Button
              onClick={handleSubmitForm}
              className="!px-4 !py-2 !bg-blue-600 hover:!bg-blue-700"
            >
              Submit
            </Button>
            <button
              onClick={() => setAssignedVendorDropdown(false)}
              className="p-2 hover:bg-gray-200 rounded-full transition-colors"
            >
              <svg
                className="w-5 h-5 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {!fromKanban && (
        <div className="flex flex-wrap gap-3 mt-2">
          <Button
            className="!px-4 !py-2 !bg-blue-600 hover:!bg-blue-700 !rounded-lg !text-sm text-white"
            onClick={() =>
              navigate(`/purchase/indent/viewindent/${indent?._id}`)
            }
          >
            View
          </Button>
          {indent?.status?.toLowerCase() !== 'approved' && (
            <Button
              disabled={!hasApprovalAccess}
              className="bg-green-500 hover:bg-green-600 text-white"
              onClick={() => approveindent(indent?._id)}
            >
              Approve
            </Button>
          )}
          {indent?.status?.toLowerCase() !== 'approved' && (
            <Button
              disabled={!hasApprovalAccess}
              danger
              type="primary"
              onClick={() => rejectindent(indent?._id)}
            >
              Reject
            </Button>
          )}
        </div>
      )}
    </section>
  );
};

export default IndentSidebarData;
