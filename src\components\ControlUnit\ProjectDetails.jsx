// import { CheckIcon, XIcon } from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';

const ProjectDetails = ({ alldata = [], cuData }) => {
  const [keys, setKeys] = useState([]);

  useEffect(() => {
    if (alldata && cuData) {
      setKeys(
        alldata?.map((tab) =>
          tab?.data?.map((sheet) => {
            const keys = Object.keys(sheet?.inputData);

            if (sheet?.inputData) {
              return keys.filter((key) => {
                return sheet?.inputData?.[key]?.selectOptions?.find((check) => {
                  return (
                    check?.machineid?.id ||
                    check?.machineid === cuData?.machineId?.id ||
                    check?._id === cuData?.mqtt?._id
                  );
                });
              });
            }
            return null;
          })
        )
      );
    }
  }, [cuData]); // eslint-disable-line

  return (
    <div className="w-full h-full overflow-y-scroll no-scrollbar">
      {alldata &&
        alldata?.map((tab, tabIdx) => (
          <div
            className="bg-white/20 w-full text-black rounded-lg p-2 mb-4 last:mb-0"
            key={tab?.name + tabIdx.toString()}
          >
            <h3 className="text-center capitalize mb-2 font-extrabold">
              {tab.name}
            </h3>
            {tab?.data.map((sheet, sheetIdx) => (
              <div
                className="bg-black/10 p-2 rounded-lg mb-3 last:mb-0"
                key={sheet.sheetData.name + sheetIdx.toString()}
              >
                <h4 className="mb-2 font-bold">{sheet.sheetData.name}</h4>
                <div className="grid grid-cols-3 gap-4 w-full text-base xl:text-xl">
                  {keys[tabIdx] &&
                    keys[tabIdx][sheetIdx] &&
                    keys[tabIdx][sheetIdx].map((key, keyIdx) => {
                      const paramData = sheet.sheetData.data.find(
                        (item) => item.Parameter === key
                      );

                      const horizontalFields = [
                        ...Array(+paramData['Horizontal Repetition']),
                      ];
                      const verticalFields = [
                        ...Array(+paramData['Vertical Repetition']),
                      ];
                      const title = paramData.Title.split('; ');

                      return (
                        <div
                          className={`col-span-${paramData.Span} bg-[#d9d9d9] p-2 rounded h-fit w-full`}
                          key={key + keyIdx.toString()}
                        >
                          <p>{key}</p>
                          {horizontalFields?.length > 1 &&
                          verticalFields?.length > 1 &&
                          paramData?.Type !== 'CHECKBOX' ? (
                            <div className="w-full overflow-x-scroll">
                              <table className="w-full text-base border-separate border-spacing-x-2">
                                <thead>
                                  <tr className="relative">
                                    {title?.map((t, tIdx) => (
                                      <th
                                        key={tIdx}
                                        className={
                                          tIdx === 0
                                            ? 'sticky left-0 bg-[#d9d9d9]'
                                            : ''
                                        }
                                      >
                                        {t}
                                      </th>
                                    ))}
                                  </tr>
                                </thead>

                                <tbody>
                                  {verticalFields?.map((v, vIdx) => (
                                    <tr key={vIdx} className="relative">
                                      {horizontalFields?.map((h, hIdx) => (
                                        <td
                                          key={hIdx}
                                          className={`text-center ${
                                            hIdx === 0
                                              ? 'sticky left-0 bg-[#d9d9d9]'
                                              : ''
                                          } `}
                                        >
                                          {sheet.inputData[key].value[
                                            title[hIdx] + vIdx.toString()
                                          ]
                                            ? sheet.inputData[key].value[
                                                title[hIdx] + vIdx.toString()
                                              ]
                                            : 'N/A'}
                                        </td>
                                      ))}
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          ) : (
                            <div
                              className={`flex flex-row ${
                                horizontalFields.length > 5
                                  ? 'overflow-x-scroll'
                                  : 'ml-2'
                              }`}
                            >
                              {horizontalFields.map((h, hIdx) => {
                                if (
                                  !sheet?.inputData[
                                    paramData?.Parameter
                                  ]?.visibility?.[
                                    cuData?.machineId?.mqtt?._id
                                  ].find((item) => item === title?.[hIdx]) &&
                                  sheet?.inputData[paramData?.Parameter]
                                    ?.visibility
                                ) {
                                  return null;
                                }

                                return (
                                  <div key={hIdx}>
                                    {horizontalFields.length > 1 && (
                                      <h6 className="text-center">
                                        {title[hIdx]}
                                      </h6>
                                    )}
                                    {verticalFields.map((v, vIdx) => {
                                      if (
                                        sheet?.inputData?.[key]?.rows &&
                                        sheet?.inputData?.[key]?.rows < vIdx + 1
                                      ) {
                                        return null;
                                      }

                                      return (
                                        <div key={vIdx}>
                                          <p>
                                            {paramData.Type === 'CHECKBOX' ? (
                                              sheet.inputData[
                                                key
                                              ].value.includes(title[hIdx]) ? (
                                                <></>
                                              ) : (
                                                // <CheckIcon className="w-6 h-6 mx-auto mt-2" />
                                                <></>
                                                // <XIcon className="w-6 h-6 mx-auto mt-2" />
                                              )
                                            ) : horizontalFields.length > 1 &&
                                              verticalFields.length > 1 ? (
                                              sheet.inputData[key].value[
                                                title[hIdx] + vIdx.toString()
                                              ] ? (
                                                sheet.inputData[key].value[
                                                  title[hIdx] + vIdx.toString()
                                                ]
                                              ) : (
                                                'N/A'
                                              )
                                            ) : horizontalFields.length > 1 ? (
                                              sheet.inputData[key].value[
                                                title[hIdx]
                                              ] ? (
                                                sheet.inputData[key].value[
                                                  title[hIdx]
                                                ]
                                              ) : (
                                                'N/A'
                                              )
                                            ) : verticalFields.length > 1 ? (
                                              sheet.inputData[key].value[
                                                vIdx
                                              ] ? (
                                                sheet.inputData[key].value[vIdx]
                                              ) : (
                                                'N/A'
                                              )
                                            ) : (
                                              sheet?.inputData?.[key]?.value ||
                                              'NA'
                                            )}
                                          </p>
                                        </div>
                                      );
                                    })}
                                  </div>
                                );
                              })}
                            </div>
                          )}
                        </div>
                      );
                    })}
                </div>
              </div>
            ))}
          </div>
        ))}
    </div>
  );
};

export default ProjectDetails;
