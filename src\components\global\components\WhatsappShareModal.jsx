import { <PERSON><PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import {
  useGetTemplatesQuery,
  useSendPoOrQuotationMessageMutation,
} from '../../../slices/whatsappConfigurationApiSlice';
import { DEFAULT_PO_PRODUCT_DETAILS_HEADER } from '../../../utils/Constant';
import Input from './Input';
import Select from './Select';

const defaultText = {
  _id: '001',
  isDefault: false,
  name: 'welcome_message',
  language: 'en',
  category: 'QUOTATION',
  body: {
    text: "Welcome to our service! We're glad to have you with us, {{company_name}}. Feel free to reach out if you need any assistance.",
    variables: [{ name: '{{company_name}}' }],
  },
  constant: true,
};

const formatWhatsAppText = (text) => {
  const toSnakeCase = (str) => {
    return str
      .replace(/([A-Z])/g, '_$1')
      .replace(/^_/, '')
      .toLowerCase();
  };
  const variables = [];
  let tempText = text.replace(/{{([^}]+)}}/g, (match, contents) => {
    const snakeCaseVar = `{{${toSnakeCase(contents)}}}`;
    variables.push(snakeCaseVar);
    return `###VAR${variables.length - 1}###`;
  });

  // Apply formatting
  tempText = tempText
    .replace(/\*(.*?)\*/g, '<strong>$1</strong>')
    .replace(/_(.*?)_/g, '<em>$1</em>')
    .replace(/~(.*?)~/g, '<del>$1</del>')
    .replace(/\n/g, '<br />');

  variables.forEach((variable, index) => {
    tempText = tempText.replace(`###VAR${index}###`, variable);
  });

  return tempText;
};

function WhatsappShareModal({
  whatsappShare,
  setWhatsappShare,
  hideManual = false,
  type,
  id,
  additionalOptions,
}) {
  const [data, setData] = useState({});
  useEffect(() => {
    if (additionalOptions?.[0]?.name) {
      setData({
        shareBy: additionalOptions?.[0]?.name || (hideManual ? '' : 'manual'),
        number: additionalOptions?.[0]?.options?.[0]?.value || '',
        options: additionalOptions?.[0]?.options || [],
      });
    }
  }, [additionalOptions, hideManual]);

  const { data: templateData } = useGetTemplatesQuery();
  const [sendToWhatsApp, { isLoading }] = useSendPoOrQuotationMessageMutation();
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    if (!data.number || !selectedTemplate) {
      toast.error('Please select a template and enter a phone number.');
      return;
    }
    try {
      const information = {
        phoneNumber: data.number,
        templateId: selectedTemplate._id,
        type: type,
        id: id,
        activeHeader:
          localStorage.getItem('activePOProductDetailsHeader') ||
          JSON.stringify(DEFAULT_PO_PRODUCT_DETAILS_HEADER),
      };
      await sendToWhatsApp(information).unwrap();
      toast.success('Message sent successfully!');
      setWhatsappShare(false);
    } catch (error) {
      toast.error('An error occurred while sending the message.');
    }
  };
  useEffect(() => {
    if (templateData) {
      const defaultTemplate = templateData.find(
        (template) => template.category === 'QUOTATION' && template.isDefault
      );
      if (defaultTemplate) {
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, [templateData]);

  return (
    <Modal
      title={
        <h2 className="text-2xl font-semibold text-gray-800">
          Share on WhatsApp
        </h2>
      }
      onCancel={() => setWhatsappShare(false)}
      open={whatsappShare}
      width={600}
      className="modern-modal"
      footer={[
        <Button
          key="cancel"
          onClick={() => setWhatsappShare(false)}
          className="px-6 hover:bg-gray-100"
        >
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={isLoading}
          onClick={handleSubmit}
          className="px-6 bg-green-600 hover:bg-green-700 border-0"
        >
          Send Message
        </Button>,
      ]}
    >
      <Spin spinning={isLoading}>
        <div className="space-y-6 py-4">
          {/* Share Options */}
          <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
            {additionalOptions?.map((opt) => (
              <div key={opt?.name} className="flex items-center gap-3">
                <input
                  type="radio"
                  name="shareBy"
                  id={opt?.name}
                  value={opt?.name}
                  checked={data?.shareBy === opt?.name}
                  onChange={handleInputChange}
                  className="w-5 h-5 text-green-600 cursor-pointer"
                />
                <label
                  htmlFor={opt?.name}
                  className="text-gray-700 font-medium cursor-pointer"
                >
                  {opt?.name.charAt(0).toUpperCase() + opt?.name.slice(1)}
                </label>
              </div>
            ))}
            {!hideManual && (
              <div className="flex items-center gap-3">
                <input
                  type="radio"
                  name="shareBy"
                  id="manual"
                  value="manual"
                  checked={data?.shareBy === 'manual'}
                  onChange={handleInputChange}
                  className="w-5 h-5 text-green-600 cursor-pointer"
                />
                <label
                  htmlFor="manual"
                  className="text-gray-700 font-medium cursor-pointer"
                >
                  Manual Entry
                </label>
              </div>
            )}
          </div>

          {/* Phone Number Input */}
          <div className="space-y-2">
            <label
              htmlFor="number"
              className="block text-sm font-medium text-gray-700"
            >
              {`${data?.shareBy === 'manual' ? 'Enter' : 'Select'} Phone Number`}
            </label>
            {data?.shareBy === 'manual' ? (
              <Input
                name="number"
                id="number"
                value={data?.number}
                onChange={handleInputChange}
                type="number"
                className="w-full rounded-lg"
              />
            ) : (
              <Select
                name="number"
                id="number"
                value={data?.number}
                options={data?.options}
                onChange={handleInputChange}
                className="w-full rounded-lg"
              />
            )}
          </div>

          {/* Template Selection */}
          <div className="space-y-4">
            <Select
              options={[
                defaultText,
                ...(templateData?.filter((t) => t?.category === 'QUOTATION') ||
                  []),
              ]?.map((template) => ({
                value: template._id,
                label: template.name,
              }))}
              placeholder="Select message template"
              value={selectedTemplate?._id}
              onChange={(e) => {
                const selectedId = e.target.value;
                if (selectedId === '001') {
                  setSelectedTemplate(defaultText);
                } else {
                  const template = templateData.find(
                    (t) => t._id === selectedId
                  );
                  setSelectedTemplate(template);
                }
              }}
              className="w-full rounded-lg"
            />

            {selectedTemplate && (
              <div className="rounded-lg border border-gray-200 overflow-hidden">
                <div className="bg-gray-50 px-4 py-2 border-b">
                  <h3 className="text-sm font-medium text-gray-700">
                    Template Preview
                  </h3>
                </div>
                <div className="p-4 bg-white">
                  <p
                    className="text-gray-600 leading-relaxed"
                    dangerouslySetInnerHTML={{
                      __html: formatWhatsAppText(selectedTemplate.body.text),
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </Spin>
    </Modal>
  );
}

export default WhatsappShareModal;
