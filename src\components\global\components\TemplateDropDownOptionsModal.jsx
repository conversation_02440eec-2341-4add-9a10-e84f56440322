import { TrashIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { ReactComponent as Doc } from '../../../assets/svgs/documentprevious.svg';
import { useEditDashboardTemplateMutation } from '../../../slices/dsahboardTemplateApiSlice.js';
import NewInput from './Input';
import Modal from './Modal';
import Table from './Table.jsx';

const TemplateDropDownOptionsModal = ({
  modalOpen,
  setModalOpen,
  additionalFields,
  setAdditionalFields,
  newOptionStatus,
  setNewOptionStatus,
  index,
  setDropdownModalIdx,
  Searchparams,
}) => {
  const [count, setCount] = useState(1);
  const [editTemplate, { isLoading: isUpdateLoading }] =
    useEditDashboardTemplateMutation();
  Searchparams?.set('templateId', additionalFields?._id);

  const handleSubmit = async () => {
    const res = await editTemplate({
      data: additionalFields,
      id: additionalFields?._id,
    }).unwrap();
    setDropdownModalIdx(-1);
    if (res.data) {
      toast.success('New Option added Successfully');
    }
    setModalOpen(false);
  };

  return (
    <>
      {modalOpen && (
        <Modal
          title={'Update Drop Down Options'}
          svg={<Doc className="h-8 w-8" />}
          onCloseModal={setModalOpen}
          onSubmit={handleSubmit}
          btnIsLoading={isUpdateLoading}
        >
          {() => {
            return (
              <>
                <div className="flex flex-col">
                  <div>
                    <Table>
                      <Table.Head>
                        <Table.Row>
                          <Table.Th>#</Table.Th>
                          <Table.Th>Options</Table.Th>
                          <Table.Th>Delete</Table.Th>
                        </Table.Row>
                      </Table.Head>
                      <Table.Body>
                        {additionalFields?.templateData[
                          index
                        ]?.fieldOptions?.map((item, i) => {
                          return (
                            <Table.Row key={i}>
                              <Table.Td>{i + 1}</Table.Td>
                              <Table.Td>
                                <NewInput
                                  type="text"
                                  id={`editTermsAndConditions-${index}`}
                                  name={`editTermsAndConditions-${index}`}
                                  value={item?.value}
                                  placeholder="Enter Value"
                                  onChange={(e) =>
                                    setAdditionalFields((prev) => ({
                                      ...prev,
                                      templateData: prev.templateData.map(
                                        (data, dataIndex) => {
                                          if (dataIndex === index) {
                                            return {
                                              ...data,
                                              fieldOptions:
                                                data.fieldOptions.map(
                                                  (option, optionIndex) => {
                                                    if (optionIndex === i) {
                                                      return {
                                                        ...option,
                                                        value: e.target.value,
                                                        label: e.target.value,
                                                      };
                                                    }
                                                    return option;
                                                  }
                                                ),
                                            };
                                          }
                                          return data;
                                        }
                                      ),
                                    }))
                                  }
                                />
                              </Table.Td>
                              <Table.Td>
                                <div
                                  className="hover:cursor-pointer"
                                  onClick={() =>
                                    setAdditionalFields((prev) => {
                                      const allTemplates = [
                                        ...prev.templateData,
                                      ];

                                      const updatedTemplates = allTemplates.map(
                                        (temp, ind) => {
                                          if (ind === index) {
                                            return {
                                              ...temp,
                                              fieldOptions:
                                                temp?.fieldOptions?.filter(
                                                  (el) => {
                                                    if (item?._id) {
                                                      return (
                                                        el?._id !== item?._id
                                                      );
                                                    } else {
                                                      return (
                                                        el?.id !== item?.id
                                                      );
                                                    }
                                                  }
                                                ),
                                            };
                                          }
                                          return temp;
                                        }
                                      );
                                      return {
                                        ...prev,
                                        templateData: updatedTemplates,
                                      };
                                    })
                                  }
                                >
                                  <TrashIcon className="w-6 h-6 text-black my-auto" />
                                </div>
                              </Table.Td>
                            </Table.Row>
                          );
                        })}
                      </Table.Body>
                    </Table>
                    <div
                      className="cursor-pointer text-blue-400 flex items-start ml-4 gap-2"
                      onClick={() => {
                        if (newOptionStatus) {
                          const latestOption =
                            additionalFields?.templateData[index]?.fieldOptions
                              ?.length > 0
                              ? additionalFields.templateData[index]
                                  .fieldOptions[
                                  additionalFields.templateData[index]
                                    .fieldOptions.length - 1
                                ]
                              : null;
                          if (latestOption && !latestOption.value) {
                            toast.error('Enter Value of new option');
                            return;
                          }
                          setAdditionalFields((prev) => ({
                            ...prev,
                            templateData: prev.templateData.map((el, idx) => {
                              if (idx === index) {
                                return {
                                  ...el,
                                  fieldOptions: [
                                    ...el?.fieldOptions,
                                    { id: count, value: '', label: '' },
                                  ],
                                };
                              }
                              return el;
                            }),
                          }));
                          setCount((prev) => prev + 1);
                        } else {
                          setAdditionalFields((prev) => ({
                            ...prev,
                            templateData: prev.templateData.map((el, idx) => {
                              if (idx === index) {
                                return {
                                  ...el,
                                  fieldOptions: [
                                    ...el?.fieldOptions,
                                    { id: count, value: '', label: '' },
                                  ],
                                };
                              }
                              return el;
                            }),
                          }));
                          setNewOptionStatus(true);
                          setCount((prev) => prev + 1);
                        }
                      }}
                    >
                      <span>Add More +</span>
                    </div>
                  </div>
                </div>
              </>
            );
          }}
        </Modal>
      )}
    </>
  );
};

export default TemplateDropDownOptionsModal;
