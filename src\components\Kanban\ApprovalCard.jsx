import {
  IoCalendarOutline,
  IoCheckmarkCircle,
  IoCloseCircle,
  IoCreateOutline,
  IoEyeOutline,
  IoMailOutline,
  IoPrintOutline,
} from 'react-icons/io5';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  getLocalDateTime,
  handlePdf,
  unCamelCaseString,
} from '../../helperFunction';
import { useUpdateDepRowStatusMutation } from '../../slices/departmentRowApiSlice';
import { useEditIndentMutation } from '../../slices/indentApiSlice';
import { useLazyGetPdfQuery } from '../../slices/pdfApiSlice';
import { useUpdatePurchaseOrderStatusMutation } from '../../slices/purchaseOrderApiSlice';
import { useUpdateQuotationsMutation } from '../../slices/quotationApiSlice';
import { useUpdateSalesOrderMutation } from '../../slices/salesOrderSlices';
import { DEFAULT_PO_PRODUCT_DETAILS_HEADER } from '../../utils/Constant';
import { customConfirm } from '../../utils/customConfirm';

const renderFields = (selectedTab, data) => {
  const fieldConfig = {
    purchaseOrders: [
      { label: 'Vendor', value: data?.vendor?.name, important: false },
      { label: 'Amount', value: data?.amount, important: true },
      {
        label: 'Delivery',
        value: data?.deliveryDate ? getLocalDateTime(data?.deliveryDate) : '-',
        important: false,
      },
    ],
    purchaseIndents: [
      { label: 'Product', value: data?.product_name || '-', important: false },
      { label: 'Quantity', value: data?.quantity || '-', important: true },
      {
        label: 'Delivery',
        value: data?.delivery_date
          ? getLocalDateTime(data?.delivery_date)
          : '-',
        important: false,
      },
    ],
    salesQuotations: [
      {
        label: 'Company',
        value: data?.vendorDetails?.companyName || '-',
        important: false,
      },
      {
        label: 'Customer',
        value: data?.vendorDetails?.name || '-',
        important: false,
      },
      {
        label: 'Expiry',
        value: data?.date?.expiryDate
          ? getLocalDateTime(data?.date?.expiryDate)
          : '-',
        important: true,
      },
    ],
    salesOrders: [
      { label: 'Customer', value: data?.customer || '-', important: false },
      { label: 'Total', value: data?.charges?.total || '-', important: true },
      {
        label: 'Delivery',
        value: data?.deliveryDate ? getLocalDateTime(data?.deliveryDate) : '-',
        important: false,
      },
    ],
  };

  const fields = fieldConfig[selectedTab] || [];

  return (
    <div className="space-y-3">
      {fields.map((field, index) => (
        <div key={index} className="flex items-center justify-between">
          <span className="text-slate-500 text-sm font-medium min-w-fit">
            {field.label}
          </span>
          <span
            className={`text-right text-sm truncate ml-4 ${field.important ? 'font-semibold text-slate-900' : 'text-slate-700'}`}
          >
            {field.value}
          </span>
        </div>
      ))}
    </div>
  );
};

function ApprovalCard({
  selectedTab,
  data,
  setShowSidebar,
  setSidebarData,
  setSidebarDataType,
  setSelectedTabForPrint,
  setShowEmailModal,
  setDataForMail,
  isSelected = false,
  onSelect,
}) {
  const navigate = useNavigate();
  const [updatePurchaseOrderStatus, { isLoading: isLoading1 }] =
    useUpdatePurchaseOrderStatusMutation();
  const [editIndentdatas, { isLoading: isLoading2 }] = useEditIndentMutation();
  const [updateQuotations, { isLoading: isLoading3 }] =
    useUpdateQuotationsMutation();
  const [updateSalesOrder, { isLoading: isLoading4 }] =
    useUpdateSalesOrderMutation();
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const [updateDepRowStatus, { isLoading: isLoading5 }] =
    useUpdateDepRowStatusMutation();

  const isLoading =
    isLoading1 || isLoading2 || isLoading3 || isLoading4 || isLoading5;

  const handleAction = async (id, isApproved, fullData) => {
    const confirm = await customConfirm(
      `Are you sure you want to ${isApproved ? 'approve' : 'reject'} ${unCamelCaseString(selectedTab)}?`,
      isApproved ? 'success' : ''
    );

    if (!confirm) return;

    let data = null;
    if (selectedTab === 'purchaseOrders') {
      data = await updatePurchaseOrderStatus({
        poStatus: isApproved ? 'Approved' : 'Rejected',
        id,
      }).unwrap();
    } else if (selectedTab === 'purchaseIndents') {
      data = await editIndentdatas({
        editdata: { status: isApproved ? 'approved' : 'rejected' },
        id,
      });
    } else if (selectedTab === 'salesQuotations') {
      data = await updateQuotations({
        data: {
          ...fullData,
          quoteStatus: isApproved ? 'Approved' : 'Rejected',
        },
        id,
      }).unwrap();
    } else if (selectedTab === 'salesOrders') {
      data = await updateSalesOrder({
        data: {
          salesOrderStatus: isApproved ? 'approved' : 'rejected',
        },
        id,
      }).unwrap();
    } else {
      data = await updateDepRowStatus({
        data: {
          status: isApproved ? 'approved' : 'rejected',
        },
        id,
      }).unwrap();
    }

    if (data) {
      if (isApproved) {
        toast.success(
          `${unCamelCaseString(selectedTab)} approved successfully`,
          { toastId: selectedTab + 'approve' }
        );
      } else {
        toast.success(
          `${unCamelCaseString(selectedTab)} rejected successfully`,
          {
            toastId: selectedTab + 'reject',
          }
        );
      }
    }
  };

  const handleEditNavigate = () => {
    switch (selectedTab) {
      case 'purchaseOrders':
        navigate(
          `/purchase/po/createpurchaseorderv2/${data?._id}?navigateTo=/primary/kanban?tab=approval`
        );
        break;
      case 'purchaseIndents':
        navigate(
          `/purchase/indent?id=${data?._id}&filter_heading=&filter_value=&navigateTo=/primary/kanban?tab=approval`
        );
        break;
      case 'salesQuotations':
        navigate(
          `/salesordermanagement/quotation?editid=${data?._id}&navigateTo=/primary/kanban?tab=approval`
        );
        break;
      case 'salesOrders':
        navigate(
          `/salesordermanagement/orders?editid=${data?._id}&navigateTo=/primary/kanban?tab=approval`
        );
        break;
      default:
        null;
    }
  };

  const renderCardName = () => {
    switch (selectedTab) {
      case 'purchaseOrders':
        return 'Purchase Order';
      case 'purchaseIndents':
        return 'Indent';
      case 'salesQuotations':
        return 'Quotation';
      case 'salesOrders':
        return 'Sales Order';
      default:
        return (
          unCamelCaseString(selectedTab).replace(' ', '\xA0')?.trim() || ''
        );
    }
  };

  const getStatusColor = (type) => {
    const colors = {
      purchaseOrders: 'bg-blue-50 text-blue-700 border-blue-200',
      purchaseIndents: 'bg-purple-50 text-purple-700 border-purple-200',
      salesQuotations: 'bg-emerald-50 text-emerald-700 border-emerald-200',
      salesOrders: 'bg-amber-50 text-amber-700 border-amber-200',
      default: 'bg-slate-50 text-slate-700 border-slate-200',
    };
    return colors[type] || colors.default;
  };

  const isCustomDepartment = ![
    'purchaseOrders',
    'purchaseIndents',
    'salesQuotations',
    'salesOrders',
  ].includes(selectedTab);

  const pdfMap = {
    purchaseIndents: 'indent',
    purchaseOrders: 'purchaseOrder',
    salesQuotations: 'quotation',
    salesOrders: 'salesOrder',
  };

  const cardId =
    data?.poID ||
    data?.indent_no ||
    data?.SalesInquiryLink ||
    data?.salesOrderID ||
    data?.quoteID ||
    data?.taskIdDetails?.taskId;

  return (
    <div className={`bg-white border rounded-xl overflow-hidden mb-4 transition-all duration-200 hover:translate-y-[-1px] ${isSelected
        ? 'border-blue-500 bg-blue-50 shadow-md'
        : 'border-slate-200 hover:border-slate-300'
      }`}>
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onSelect?.(data._id, e.target.checked)}
              className="mt-1 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
            <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-bold text-slate-900 truncate">
                {cardId}
              </h3>
              <span
                className={`inline-flex items-center px-2.5 py-1 rounded-lg text-xs font-medium border ${getStatusColor(selectedTab)}`}
              >
                {renderCardName()}
              </span>
            </div>
            <div className="flex items-center gap-2 text-slate-500">
              <IoCalendarOutline className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm">
                Created {getLocalDateTime(data?.createdAt)}
              </span>
            </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 pb-6">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
          {/* Details */}
          <div className="lg:col-span-3">
            <div className="bg-slate-50 rounded-lg p-4">
              {renderFields(selectedTab, data)}
            </div>
          </div>

          {/* Actions */}
          <div className="lg:col-span-2">
            <div className="space-y-3">
              {/* Primary Actions */}
              <div className="grid grid-cols-2 gap-2">
                <button
                  disabled={isLoading}
                  onClick={() => handleAction(data?._id, true, data)}
                  className="inline-flex items-center justify-center gap-2 px-4 py-2.5 bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-400 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                >
                  <IoCheckmarkCircle className="w-4 h-4" />
                  {isLoading ? 'Loading...' : 'Approve'}
                </button>
                <button
                  disabled={isLoading}
                  onClick={() => handleAction(data?._id, false)}
                  className="inline-flex items-center justify-center gap-2 px-4 py-2.5 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                >
                  <IoCloseCircle className="w-4 h-4" />
                  {isLoading ? 'Loading...' : 'Reject'}
                </button>
              </div>

              {/* Secondary Actions */}
              {!isCustomDepartment && (
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => {
                        setSidebarData(data);
                        setShowSidebar(true);
                        setSidebarDataType(selectedTab);
                      }}
                      className="inline-flex items-center justify-center gap-2 px-3 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 text-sm font-medium rounded-lg transition-colors duration-200"
                    >
                      <IoEyeOutline className="w-4 h-4" />
                      View
                    </button>

                    {selectedTab !== 'purchaseIndents' ? (
                      <button
                        disabled={isFetchingPdf}
                        onClick={() => {
                          if (selectedTab === 'purchaseOrders') {
                            handlePdf(
                              getPdf,
                              data?._id,
                              pdfMap?.[selectedTab],
                              null,
                              localStorage.getItem(
                                'activePOProductDetailsHeader'
                              ) ||
                                JSON.stringify(
                                  DEFAULT_PO_PRODUCT_DETAILS_HEADER
                                )
                            );
                          } else {
                            handlePdf(getPdf, data?._id, pdfMap?.[selectedTab]);
                          }
                        }}
                        className="inline-flex items-center justify-center gap-2 px-3 py-2 bg-slate-100 hover:bg-slate-200 disabled:bg-slate-50 text-slate-700 text-sm font-medium rounded-lg transition-colors duration-200"
                      >
                        <IoPrintOutline className="w-4 h-4" />
                        {isFetchingPdf ? 'Loading...' : 'Print'}
                      </button>
                    ) : (
                      <button
                        onClick={handleEditNavigate}
                        className="inline-flex items-center justify-center gap-2 px-3 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 text-sm font-medium rounded-lg transition-colors duration-200"
                      >
                        <IoCreateOutline className="w-4 h-4" />
                        Edit
                      </button>
                    )}
                  </div>

                  {selectedTab !== 'purchaseIndents' && (
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={handleEditNavigate}
                        className="inline-flex items-center justify-center gap-2 px-3 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 text-sm font-medium rounded-lg transition-colors duration-200"
                      >
                        <IoCreateOutline className="w-4 h-4" />
                        Edit
                      </button>
                      <button
                        onClick={() => {
                          setSelectedTabForPrint(selectedTab);
                          setDataForMail(data);
                          setShowEmailModal(true);
                        }}
                        className="inline-flex items-center justify-center gap-2 px-3 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 text-sm font-medium rounded-lg transition-colors duration-200"
                      >
                        <IoMailOutline className="w-4 h-4" />
                        Send Mail
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ApprovalCard;
