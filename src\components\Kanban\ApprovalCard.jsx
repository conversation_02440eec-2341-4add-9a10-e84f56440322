import { memo, useCallback, useMemo } from 'react';
import {
  IoCalendarOutline,
  IoCheckmarkCircle,
  IoCloseCircle,
  IoEyeOutline,
  IoMailOutline,
  IoPrintOutline,
} from 'react-icons/io5';

import { toast } from 'react-toastify';
import {
  getLocalDateTime,
  unCamelCaseString,
} from '../../helperFunction';
import { useUpdateDepRowStatusMutation } from '../../slices/departmentRowApiSlice';
import { useEditIndentMutation } from '../../slices/indentApiSlice';
import { useLazyGetPdfQuery } from '../../slices/pdfApiSlice';
import { useUpdatePurchaseOrderStatusMutation } from '../../slices/purchaseOrderApiSlice';
import { useUpdateQuotationsMutation } from '../../slices/quotationApiSlice';
import { useUpdateSalesOrderMutation } from '../../slices/salesOrderSlices';

const FieldRenderer = memo(({ selectedTab, data }) => {
  const fieldConfig = useMemo(() => ({
    purchaseOrders: [
      { label: 'PO Number', value: data?.poID || 'N/A', important: true },
      { label: 'Vendor', value: data?.vendor?.name || 'N/A' },
      { label: 'Total Amount', value: `₹${data?.total || 0}`, important: true },
    ],
    purchaseIndents: [
      { label: 'Indent Number', value: data?.indent_no || 'N/A', important: true },
      { label: 'Status', value: data?.status || 'N/A' },
      { label: 'Item', value: data?.product_name || 'N/A' },
    ],
    salesQuotations: [
      { label: 'Quote Number', value: data?.quoteID || 'N/A', important: true },
      { label: 'Customer', value: data?.vendorDetails?.name || 'N/A' },
      { label: 'Valid Until', value: data?.date?.expiryDate ? getLocalDateTime(data.date.expiryDate) : 'N/A' },
    ],
    salesOrders: [
      { label: 'Order Number', value: data?.salesOrderID || 'N/A', important: true },
      { label: 'Customer', value: data?.customer || 'N/A' },
      { label: 'Delivery Date', value: data?.deliveryDate ? getLocalDateTime(data.deliveryDate) : 'N/A' },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }), [data, selectedTab]);

  const fields = fieldConfig[selectedTab] || [
    { label: 'ID', value: data?._id || 'N/A', important: true },
    { label: 'Status', value: data?.status || 'Pending' },
    { label: 'Created', value: getLocalDateTime(data?.createdAt) },
  ];

  return (
    <div className="space-y-2">
      {fields.map((field, index) => (
        <div key={index} className="flex items-center justify-between">
          <span className="text-slate-500 text-xs font-medium min-w-fit">
            {field.label}
          </span>
          <span
            className={`text-right text-xs truncate ml-2 ${field.important ? 'font-semibold text-slate-900' : 'text-slate-700'
              }`}
          >
            {field.value}
          </span>
        </div>
      ))}
    </div>
  );
});

FieldRenderer.displayName = 'FieldRenderer';

// Memoized status color function
const getStatusColor = (selectedTab) => {
  const colors = {
    purchaseOrders: 'bg-blue-100 text-blue-800 border-blue-200',
    purchaseIndents: 'bg-purple-100 text-purple-800 border-purple-200',
    salesQuotations: 'bg-green-100 text-green-800 border-green-200',
    salesOrders: 'bg-orange-100 text-orange-800 border-orange-200',
  };
  return colors[selectedTab] || 'bg-gray-100 text-gray-800 border-gray-200';
};

const ApprovalCard = memo(({
  selectedTab,
  data,
  setShowSidebar,
  setSidebarData,
  setSidebarDataType,
  setSelectedTabForPrint,
  setDataToPrint,
  setShowEmailModal,
  setDataForMail,
  isSelected = false,
  onSelect,
}) => {
  // Mutation hooks
  const [updatePurchaseOrderStatus, { isLoading: isLoading1 }] = useUpdatePurchaseOrderStatusMutation();
  const [editIndentdatas, { isLoading: isLoading2 }] = useEditIndentMutation();
  const [updateQuotations, { isLoading: isLoading3 }] = useUpdateQuotationsMutation();
  const [updateSalesOrder, { isLoading: isLoading4 }] = useUpdateSalesOrderMutation();
  const [_getPdf, { isFetching: _isFetchingPdf }] = useLazyGetPdfQuery();
  const [updateDepRowStatus, { isLoading: isLoading5 }] = useUpdateDepRowStatusMutation();

  const isLoading = isLoading1 || isLoading2 || isLoading3 || isLoading4 || isLoading5;

  const { cardId, cardName } = useMemo(() => {
    const idMap = {
      purchaseOrders: data?.poID || data?._id,
      purchaseIndents: data?.indent_no || data?._id,
      salesQuotations: data?.quoteID || data?._id,
      salesOrders: data?.salesOrderID || data?._id,
    };

    const nameMap = {
      purchaseOrders: 'Purchase Order',
      purchaseIndents: 'Purchase Indent',
      salesQuotations: 'Sales Quotation',
      salesOrders: 'Sales Order',
    };

    return {
      cardId: idMap[selectedTab] || data?.customTaskId || data?._id,
      cardName: nameMap[selectedTab] || unCamelCaseString(selectedTab)
    };
  }, [selectedTab, data]);

  // Memoized action handler
  const handleAction = useCallback(async (id, isApproved, fullData = null) => {
    try {
      if (selectedTab === 'purchaseOrders') {
        await updatePurchaseOrderStatus({
          poStatus: isApproved ? 'Approved' : 'Rejected',
          id,
        }).unwrap();
      } else if (selectedTab === 'purchaseIndents') {
        await editIndentdatas({
          editdata: { status: isApproved ? 'approved' : 'rejected' },
          id,
        });
      } else if (selectedTab === 'salesQuotations') {
        await updateQuotations({
          data: {
            ...fullData,
            quoteStatus: isApproved ? 'Approved' : 'Rejected',
          },
          id,
        }).unwrap();
      } else if (selectedTab === 'salesOrders') {
        await updateSalesOrder({
          data: {
            salesOrderStatus: isApproved ? 'approved' : 'rejected',
          },
          id,
        }).unwrap();
      } else {
        await updateDepRowStatus({
          data: {
            status: isApproved ? 'approved' : 'rejected',
          },
          id,
        }).unwrap();
      }

      toast.success(
        `Successfully ${isApproved ? 'approved' : 'rejected'} ${cardName}`,
        { toastId: `${id}-${isApproved ? 'approve' : 'reject'}` }
      );
    } catch (error) {
      toast.error(
        `Failed to ${isApproved ? 'approve' : 'reject'} ${cardName}. Please try again.`,
        { toastId: `${id}-error` }
      );
    }
  }, [selectedTab, cardName, updatePurchaseOrderStatus, editIndentdatas, updateQuotations, updateSalesOrder, updateDepRowStatus]);

  // Memoized checkbox handler
  const handleCheckboxChange = useCallback((e) => {
    onSelect?.(data._id, e.target.checked);
  }, [data._id, onSelect]);

  // Memoized view handler
  const handleView = useCallback(() => {
    setSidebarData(data);
    setSidebarDataType(selectedTab);
    setShowSidebar(true);
  }, [data, selectedTab, setSidebarData, setSidebarDataType, setShowSidebar]);

  // Memoized print handler
  const handlePrint = useCallback(() => {
    setSelectedTabForPrint(selectedTab);
    setDataToPrint(data);
  }, [selectedTab, data, setSelectedTabForPrint, setDataToPrint]);

  // Memoized email handler
  const handleEmail = useCallback(() => {
    setDataForMail(data);
    setShowEmailModal(true);
  }, [data, setDataForMail, setShowEmailModal]);

  return (
    <div className={`bg-white border rounded-xl overflow-hidden transition-all duration-200 hover:translate-y-[-1px] h-64 ${isSelected
      ? 'border-blue-500 bg-blue-50 shadow-md'
      : 'border-slate-200 hover:border-slate-300'
      }`}>
      {/* Header */}
      <div className="p-4 pb-2">
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={handleCheckboxChange}
              className="mt-1 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="text-base font-bold text-slate-900 truncate">
                  {cardId}
                </h3>
                <span
                  className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium border ${getStatusColor(selectedTab)}`}
                >
                  {cardName}
                </span>
              </div>
              <div className="flex items-center gap-1 text-slate-500">
                <IoCalendarOutline className="w-3 h-3 flex-shrink-0" />
                <span className="text-xs">
                  Created {getLocalDateTime(data?.createdAt)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 pb-4 flex-1 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 h-full">
          {/* Details */}
          <div className="lg:col-span-3">
            <div className="bg-slate-50 rounded-lg p-3 h-full overflow-y-auto">
              <FieldRenderer selectedTab={selectedTab} data={data} />
            </div>
          </div>

          {/* Actions */}
          <div className="lg:col-span-2">
            <div className="space-y-2 h-full flex flex-col">
              {/* Primary Actions */}
              <div className="grid grid-cols-2 gap-2">
                <button
                  disabled={isLoading}
                  onClick={() => handleAction(data?._id, true, data)}
                  className="inline-flex items-center justify-center gap-1 px-3 py-2 bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-400 text-white text-xs font-medium rounded-lg transition-colors duration-200"
                >
                  <IoCheckmarkCircle className="w-3 h-3" />
                  {isLoading ? 'Loading...' : 'Approve'}
                </button>
                <button
                  disabled={isLoading}
                  onClick={() => handleAction(data?._id, false)}
                  className="inline-flex items-center justify-center gap-1 px-3 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white text-xs font-medium rounded-lg transition-colors duration-200"
                >
                  <IoCloseCircle className="w-3 h-3" />
                  {isLoading ? 'Loading...' : 'Reject'}
                </button>
              </div>

              {/* Secondary Actions */}
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={handleView}
                  className="inline-flex items-center justify-center gap-1 px-2 py-1.5 bg-slate-100 hover:bg-slate-200 text-slate-700 text-xs font-medium rounded-lg transition-colors duration-200"
                >
                  <IoEyeOutline className="w-3 h-3" />
                  View
                </button>
                <button
                  onClick={handlePrint}
                  className="inline-flex items-center justify-center gap-1 px-2 py-1.5 bg-slate-100 hover:bg-slate-200 text-slate-700 text-xs font-medium rounded-lg transition-colors duration-200"
                >
                  <IoPrintOutline className="w-3 h-3" />
                  Print
                </button>
              </div>

              <button
                onClick={handleEmail}
                className="w-full inline-flex items-center justify-center gap-1 px-2 py-1.5 bg-blue-100 hover:bg-blue-200 text-blue-700 text-xs font-medium rounded-lg transition-colors duration-200"
              >
                <IoMailOutline className="w-3 h-3" />
                Send Email
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

ApprovalCard.displayName = 'ApprovalCard';

export default ApprovalCard;
