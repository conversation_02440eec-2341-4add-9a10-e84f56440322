import { PlusIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { IoMailUnreadOutline, IoTrashBinOutline } from 'react-icons/io5';
import { LuMailOpen, LuSendHorizonal } from 'react-icons/lu';
import {
  MdOutlineCreate,
  MdOutlineDrafts,
  MdOutlineInbox,
  MdOutlineNewLabel,
  // MdOutlineOutbox,
  MdOutlineStarOutline,
} from 'react-icons/md';
import { Link } from 'react-router-dom';

const FolderButton = ({ children, onClick }) => {
  return (
    <div
      className={`transition-all duration-300 hover:bg-gray-100 hover:scale-105 `}
    >
      <button
        onClick={onClick}
        disabled={true} //disabled beacuse of this features is implemented yet
        className={`flex items-center justify-start text-[14px] py-3 px-2 w-full gap-x-2 font-semibold text-gray-500`}
      >
        {children}
        {true && <PlusIcon className="ml-auto w-4 h-4" />}
      </button>
    </div>
  );
};

const EmailButton = ({ children, onClick, isActive }) => {
  return (
    <div
      className={`rounded-md transition-all duration-300 hover:bg-gray-100 hover:rounded-md hover:scale-105 ${
        isActive ? 'bg-gray-200 text-blue-500' : 'bg-[#fcfcfd] text-gray-700'
      }`}
    >
      <button
        className={`flex items-center justify-start text-[14px] py-3 px-2 w-full gap-x-2 font-semibold`}
        onClick={onClick}
      >
        {children}
      </button>
    </div>
  );
};

const EmailSidebar = ({
  setShowComposeModal,
  setSearchParams,
  SearchParams,
  mailLength,
}) => {
  // const [showFolders, setShowFolders] = useState(false);
  const ChangeTabs = (e) => {
    setSearchParams(
      (prev) => {
        prev.set('tab', e.target.innerText?.toLowerCase());
        prev.set('page', 1);
        return prev;
      },
      {
        replace: true,
      }
    );
  };
  const handleFolderClick = () => {
    setIsFoldersOpen(!isFoldersOpen);
  };
  const [isFoldersOpen, setIsFoldersOpen] = useState(false);

  return (
    <>
      <aside className="bg-[#fcfcfd] pl-4 h-full overflow-x-hidden overflow-y-scroll">
        <div className="flex flex-col w-full py-0">
          <EmailButton
            onClick={() => {
              setShowComposeModal((prev) => {
                return !prev;
              });
            }}
          >
            <MdOutlineCreate size={18} className="text-gray-700" />
            New Mail
          </EmailButton>
          <EmailButton
            onClick={ChangeTabs}
            isActive={SearchParams?.get('tab') === 'all mail'}
          >
            <LuMailOpen size={18} className="text-gray-700" />
            All Mail{' '}
            {mailLength > 0 && (
              <span className="ml-auto text-xs text-gray">{mailLength}</span>
            )}
          </EmailButton>
          <EmailButton
            className="rounded-[12px] flex items-center !p-3 !min-w-[8rem]"
            isActive={SearchParams?.get('tab') === 'inbox'}
            onClick={ChangeTabs}
          >
            <MdOutlineInbox size={18} className="text-gray-700" />
            Inbox
          </EmailButton>
          <EmailButton
            className="rounded-[12px] flex items-center !p-3 !min-w-[8rem]"
            isActive={SearchParams?.get('tab') === 'drafts'}
            onClick={ChangeTabs}
          >
            <MdOutlineDrafts size={18} className="text-gray-700" />
            Drafts
          </EmailButton>
          <EmailButton
            className="rounded-[12px] flex items-center !p-3 !min-w-[8rem]"
            isActive={SearchParams?.get('tab') === 'sent'}
            onClick={ChangeTabs}
          >
            <LuSendHorizonal size={18} className="text-gray-700" />
            Sent
          </EmailButton>
          <EmailButton
            className="rounded-[12px] flex items-center !p-3 !min-w-[8rem]"
            isActive={SearchParams?.get('tab') === 'bin'}
            onClick={ChangeTabs}
          >
            <IoTrashBinOutline size={18} className="text-gray-700" />
            Bin
          </EmailButton>
          {/* <EmailButton
            className="rounded-[12px] flex items-center !p-3 !min-w-[8rem]"
            isActive={SearchParams?.get('tab') === 'outbox'}
            onClick={ChangeTabs}
          >
            <MdOutlineOutbox size={18} className="text-gray-700" />
            OutBox
          </EmailButton> */}
          <EmailButton
            className="rounded-[12px] flex items-center !p-3 !min-w-[8rem]"
            isActive={SearchParams?.get('tab') === 'unread'}
            onClick={ChangeTabs}
          >
            <IoMailUnreadOutline size={18} className="text-gray-700" />
            Unread
          </EmailButton>
          <EmailButton
            className="rounded-[12px] flex items-center !p-3 !min-w-[8rem]"
            isActive={SearchParams?.get('tab') === 'starred'}
            onClick={ChangeTabs}
          >
            <MdOutlineStarOutline size={18} className="text-gray-700" />
            Starred
          </EmailButton>

          <>
            <FolderButton onClick={handleFolderClick} isOpen={isFoldersOpen}>
              <MdOutlineNewLabel size={18} className="text-gray-700" />
              Labels
            </FolderButton>
            {isFoldersOpen && (
              <div className="pl-8">
                <div className="flex flex-col">
                  <Link
                    to="/folder1"
                    className="text-xs py-2 hover:text-blue-primary"
                  >
                    Folder 1
                  </Link>
                  <Link
                    to="/folder2"
                    className="text-xs py-2 hover:text-blue-primary"
                  >
                    Folder 2
                  </Link>
                </div>
                <div className="pl-4">
                  <div className="flex flex-col">
                    <Link
                      to="/folder1/allmails"
                      className="text-xs py-2 hover:text-blue-primary"
                    >
                      All Mails
                    </Link>
                    <Link
                      to="/folder1/drafts"
                      className="text-xs py-2 hover:text-blue-primary"
                    >
                      Drafts
                    </Link>
                    <Link
                      to="/folder1/sent"
                      className="text-xs py-2 hover:text-blue-primary"
                    >
                      Sent
                    </Link>
                    <Link
                      to="/folder1/bin"
                      className="text-xs py-2 hover:text-blue-primary"
                    >
                      Bin
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </>
        </div>
      </aside>
    </>
  );
};

export default EmailSidebar;
