import { APIProvider, Map, Marker } from '@vis.gl/react-google-maps';
import { useContext, useState } from 'react';
import { Store } from '../../../store/Store';
import Button from './Button';

function MapLocationSelector({ value, setOpen, onSelect }) {
  const [marker, setMarker] = useState(value || {});

  const { defaults: { googleMapApiKey = '' } = {} } = useContext(Store);

  return (
    <div className="fixed top-0 left-0 z-[99999] w-screen h-screen flex justify-center items-center bg-black/20">
      <div className="w-4/5 h-4/5 p-1 bg-white rounded">
        <div className="flex w-full justify-between items-center p-3">
          <Button onClick={() => setOpen(false)}>Cancel</Button>
          <h3>Select Location</h3>
          <Button
            disabled={!marker?.lat}
            onClick={() => {
              onSelect(marker);
              setOpen(false);
            }}
          >
            Select
          </Button>
        </div>

        <div className="h-full">
          <APIProvider apiKey={googleMapApiKey}>
            <Map
              style={{ width: '100%', height: '88%' }}
              defaultCenter={
                marker?.lat
                  ? marker
                  : { lat: 22.977378795847805, lng: 79.26377078903572 }
              }
              defaultZoom={marker?.lat ? 16 : 4}
              gestureHandling={'greedy'}
              onClick={(e) => {
                setMarker(e?.detail?.latLng);
              }}
            >
              {marker?.lat && <Marker position={marker} />}
            </Map>
          </APIProvider>
        </div>
      </div>
    </div>
  );
}

export default MapLocationSelector;
