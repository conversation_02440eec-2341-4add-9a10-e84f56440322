import { useContext, useEffect, useMemo, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import {
  generateDateString,
  mobileWidth,
  printWithSpecialFileName,
  tabletWidth,
  unCamelCaseString,
} from '../../helperFunction';
import PurchasePdf from '../../pdfGeneratorFiles/purchaseOrderPdf';
import { useBulkApproveCardsMutation, useGetAllPendingStatusDataQuery } from '../../slices/kanbanApiSlice';
import { Store } from '../../store/Store';
import { customConfirm } from '../../utils/customConfirm';
import QuotationPDF from '../SalesOrderManagement/Quotation/QuotationPDF';
import RightSidebar from '../global/components/RightSidebar';
import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import { TabButton, TabContainer } from '../global/components/TabContainer';
import SalesReport from '../salesOrder/salesReport';
import { Label } from '../v2';
import MediaModal from '../v3/global/components/MediaModal';
import ApprovalCard from './ApprovalCard';
import ApprovalSidebarData from './ApprovalSidebarData';
import SendApprovalMail from './SendApprovalMail';

function ApprovalPage() {
  const { data: pendingData, isFetching } = useGetAllPendingStatusDataQuery();
  const [selectedTab, setSelectedTab] = useState('');
  const [ShowSidebar, setShowSidebar] = useState(false);
  const [SidebarData, setSidebarData] = useState({});
  const [SidebarDataType, setSidebarDataType] = useState('');
  const [SelectedTabForPrint, setSelectedTabForPrint] = useState('');
  const [Total, setTotal] = useState(0);
  const [DataToPrint, setDataToPrint] = useState(null);
  const [ShowEmailModal, setShowEmailModal] = useState(false);
  const [DataForMail, setDataForMail] = useState(null);
  const [Media, setMedia] = useState([]);
  const [ReadMore, setReadMore] = useState(false);
  const [allCount, setAllCount] = useState(0);
  const [selectedCards, setSelectedCards] = useState(new Set());
  const [bulkApproveCards, { isLoading: isBulkLoading }] = useBulkApproveCardsMutation();
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const disableMap = {
    purchaseIndents: 'indent',
    purchaseOrders: 'purchaseOrder',
    salesQuotations: 'quotation',
    salesInvoices: 'salesInvoice',
    salesOrders: 'salesOrder',
  };

  const disabledPages = (
    defaultParam?.projectDefaults?.disabledApprovalFor || []
  )?.map(
    (key) =>
      Object.keys(disableMap).find((mapKey) => disableMap[mapKey] === key) ||
      key
  );

  const options = useMemo(
    () =>
      Object.keys(pendingData || {})
        .filter(
          (key) =>
            key !== 'noAccess' &&
            !pendingData?.noAccess?.includes(key) &&
            !disabledPages.includes(key)
        )
        .map((key) => ({
          label: unCamelCaseString(key).replace(' ', '\xA0').trim(),
          value: key,
        })),
    [pendingData, disabledPages]
  );

  useEffect(() => {
    let timer = null;
    let sum = 0;
    if (SelectedTabForPrint && DataToPrint) {
      DataToPrint?.productDetails?.map((item) => {
        sum += item?.totalAmount || 0;
      });
      setTotal(sum);
      clearTimeout(timer);
      timer = setTimeout(() => {
        printWithSpecialFileName();
      }, 400);
    }
    return () => {
      clearTimeout(timer);
    };
  }, [SelectedTabForPrint, DataToPrint]);

  window.onafterprint = () => {
    setSelectedTabForPrint('');
    setDataToPrint(null);
  };

  const [reversedSelectedTabOrders, setReversedSelectedTabOrders] = useState(
    []
  );

  useEffect(() => {
    if (pendingData && selectedTab) {
      const reversedOrders = [...pendingData[selectedTab]].reverse();
      setReversedSelectedTabOrders(reversedOrders);
    }
    let val = 0;
    for (let key in pendingData) {
      if (key !== 'noAccess') val += pendingData?.[key]?.length || 0;
    }
    setAllCount(val);
    setSelectedCards(new Set());
  }, [pendingData, selectedTab]);

  const handleCardSelect = (cardId, isSelected) => {
    const newSelected = new Set(selectedCards);
    if (isSelected) {
      newSelected.add(cardId);
    } else {
      newSelected.delete(cardId);
    }
    setSelectedCards(newSelected);
  };

  const handleSelectAll = () => {
    if (!selectedTab) {
      const allCardIds = new Set();
      Object.keys(pendingData || {}).forEach(key => {
        if (key !== 'noAccess' && !disabledPages.includes(key)) {
          pendingData[key]?.forEach(item => allCardIds.add(item._id));
        }
      });
      setSelectedCards(selectedCards.size === allCardIds.size ? new Set() : allCardIds);
    } else {
      const tabCardIds = new Set(reversedSelectedTabOrders.map(item => item._id));
      setSelectedCards(selectedCards.size === tabCardIds.size ? new Set() : tabCardIds);
    }
  };

  const handleBulkApproval = async (action) => {
    if (selectedCards.size === 0) return;

    const confirm = await customConfirm(
      `Are you sure you want to ${action} ${selectedCards.size} selected items?`,
      action === 'approve' ? 'success' : ''
    );

    if (!confirm) return;

    const cardsToProcess = [];

    if (!selectedTab) {
      Object.keys(pendingData || {}).forEach(key => {
        if (key !== 'noAccess' && !disabledPages.includes(key)) {
          pendingData[key]?.forEach(item => {
            if (selectedCards.has(item._id)) {
              cardsToProcess.push({ id: item._id, type: key, data: item });
            }
          });
        }
      });
    } else {
      reversedSelectedTabOrders.forEach(item => {
        if (selectedCards.has(item._id)) {
          cardsToProcess.push({ id: item._id, type: selectedTab, data: item });
        }
      });
    }

    try {
      await bulkApproveCards({ cards: cardsToProcess, action }).unwrap();
      toast.success(
        `Successfully ${action === 'approve' ? 'approved' : 'rejected'} ${cardsToProcess.length} items`,
        { toastId: `bulk-${action}` }
      );
      setSelectedCards(new Set());
    } catch (error) {
      toast.error(
        `Failed to ${action} selected items. Please try again.`,
        { toastId: `bulk-${action}-error` }
      );
    }
  };

  if (isFetching) return <Spinner />;

  return (
    <>
      {ShowEmailModal && (
        <SendApprovalMail
          isMobile={isMobile}
          isTablet={isTablet}
          selectedtab={SelectedTabForPrint}
          setShowEmailModal={setShowEmailModal}
          data={DataForMail}
        />
      )}
      {SelectedTabForPrint === 'purchaseOrders' && DataToPrint && (
        <PurchasePdf data={DataToPrint} />
      )}
      {SelectedTabForPrint === 'salesQuotations' && DataToPrint && (
        <QuotationPDF
          data={DataToPrint}
          total={Total}
          generateDateString={generateDateString}
        />
      )}
      {SelectedTabForPrint === 'salesOrders' && DataToPrint && (
        <SalesReport data={DataToPrint} />
      )}
      <div>
        <RightSidebar
          openSideBar={ShowSidebar}
          setOpenSideBar={setShowSidebar}
          className={'w-full md:w-1/3'}
        >
          <ApprovalSidebarData
            data={SidebarData}
            tab={SidebarDataType}
            setReadMore={setReadMore}
            setShowSidebar={setShowSidebar}
            setMedia={setMedia}
            showSidebar={ShowSidebar}
          />
        </RightSidebar>
        {ReadMore && (
          <MediaModal
            ShowModal={ReadMore}
            FormData={Media}
            setShowModal={setReadMore}
            isView={true}
          />
        )}
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-3 md:mx-[-1rem] lg:mx-[6rem]">
          <div className="flex items-center justify-start gap-x-3">
            <TabContainer className="!m-0">
              <TabButton
                isactive={!selectedTab ? true : false}
                onClick={() => {
                  setSelectedTab('');
                }}
                className={'flex items-center gap-x-2'}
              >
                Pending Approvals
                <p className="flex items-center justify-center text-[13px] bg-blue-500 text-white rounded-full px-2">
                  {!selectedTab ? allCount : pendingData?.[selectedTab].length}
                </p>
              </TabButton>
            </TabContainer>
          </div>
          <div className="w-full md:w-[14rem] mt-4 md:mt-0 lg:mt-0">
            <Label>Select Approval</Label>
            <Select
              options={[{ label: 'All Options', value: '' }, ...options]}
              value={selectedTab}
              onChange={(e) => {
                setSelectedTab(e.target.value);
              }}
            />
          </div>
        </div>

        {selectedCards.size > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4 md:mx-[-1rem] lg:mx-[6rem]">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleSelectAll}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  {selectedCards.size > 0 ? 'Deselect All' : 'Select All'}
                </button>
                <span className="text-sm text-gray-600">
                  {selectedCards.size} item(s) selected
                </span>
              </div>
              {selectedCards.size > 0 && (
                <div className="flex gap-2">
                  <button
                    onClick={() => handleBulkApproval('approve')}
                    disabled={isBulkLoading}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white text-sm font-medium rounded-lg transition-colors"
                  >
                    {isBulkLoading ? 'Processing...' : `Approve ${selectedCards.size}`}
                  </button>
                  <button
                    onClick={() => handleBulkApproval('reject')}
                    disabled={isBulkLoading}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white text-sm font-medium rounded-lg transition-colors"
                  >
                    {isBulkLoading ? 'Processing...' : `Reject ${selectedCards.size}`}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="md:mx-[-14px] lg:mx-[6rem]">
          {!selectedTab ? (
            <>
              {pendingData &&
                !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                  'indent'
                ) &&
                pendingData?.purchaseIndents?.map((item) => (
                  <ApprovalCard
                    key={item._id}
                    selectedTab={'purchaseIndents'}
                    data={item}
                    setShowSidebar={setShowSidebar}
                    setSidebarData={setSidebarData}
                    setSidebarDataType={setSidebarDataType}
                    setSelectedTabForPrint={setSelectedTabForPrint}
                    setDataToPrint={setDataToPrint}
                    setShowEmailModal={setShowEmailModal}
                    setDataForMail={setDataForMail}
                    isSelected={selectedCards.has(item._id)}
                    onSelect={handleCardSelect}
                  />
                ))}

              {pendingData &&
                !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                  'purchaseOrder'
                ) &&
                pendingData?.purchaseOrders.map((item) => (
                  <ApprovalCard
                    key={item._id}
                    selectedTab={'purchaseOrders'}
                    data={item}
                    setShowSidebar={setShowSidebar}
                    setSidebarData={setSidebarData}
                    setSidebarDataType={setSidebarDataType}
                    setSelectedTabForPrint={setSelectedTabForPrint}
                    setDataToPrint={setDataToPrint}
                    setShowEmailModal={setShowEmailModal}
                    setDataForMail={setDataForMail}
                    isSelected={selectedCards.has(item._id)}
                    onSelect={handleCardSelect}
                  />
                ))}

              {pendingData &&
                !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                  'salesOrder'
                ) &&
                pendingData?.salesOrders?.map((item) => (
                  <ApprovalCard
                    key={item._id}
                    selectedTab={'salesOrders'}
                    data={item}
                    setShowSidebar={setShowSidebar}
                    setSidebarData={setSidebarData}
                    setSidebarDataType={setSidebarDataType}
                    setSelectedTabForPrint={setSelectedTabForPrint}
                    setDataToPrint={setDataToPrint}
                    setShowEmailModal={setShowEmailModal}
                    setDataForMail={setDataForMail}
                    isSelected={selectedCards.has(item._id)}
                    onSelect={handleCardSelect}
                  />
                ))}

              {pendingData &&
                !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                  'quotation'
                ) &&
                pendingData?.salesQuotations?.map((item) => (
                  <ApprovalCard
                    key={item._id}
                    selectedTab={'salesQuotations'}
                    data={item}
                    setShowSidebar={setShowSidebar}
                    setSidebarData={setSidebarData}
                    setSidebarDataType={setSidebarDataType}
                    setSelectedTabForPrint={setSelectedTabForPrint}
                    setDataToPrint={setDataToPrint}
                    setShowEmailModal={setShowEmailModal}
                    setDataForMail={setDataForMail}
                    isSelected={selectedCards.has(item._id)}
                    onSelect={handleCardSelect}
                  />
                ))}

              {pendingData &&
                Object.keys(pendingData || {})?.map((key) => {
                  if (
                    [
                      'purchaseOrders',
                      'purchaseIndents',
                      'salesQuotations',
                      'salesOrders',
                      'noAccess',
                    ].includes(key)
                  )
                    return null;

                  return (
                    <>
                      {pendingData?.[key].map((item) => (
                        <ApprovalCard
                          key={item._id}
                          selectedTab={key}
                          data={item}
                          setShowSidebar={setShowSidebar}
                          setSidebarData={setSidebarData}
                          setSidebarDataType={setSidebarDataType}
                          setSelectedTabForPrint={setSelectedTabForPrint}
                          setDataToPrint={setDataToPrint}
                          setShowEmailModal={setShowEmailModal}
                          setDataForMail={setDataForMail}
                          isSelected={selectedCards.has(item._id)}
                          onSelect={handleCardSelect}
                        />
                      ))}
                    </>
                  );
                })}
            </>
          ) : (
            <>
              {pendingData?.[selectedTab]?.length === 0 ? (
                <p className="text-center">
                  No pending {unCamelCaseString(selectedTab)}
                </p>
              ) : (
                <>
                  {reversedSelectedTabOrders.map((item) => (
                    <ApprovalCard
                      key={item._id}
                      selectedTab={selectedTab}
                      data={item}
                      setShowSidebar={setShowSidebar}
                      setSidebarData={setSidebarData}
                      setSidebarDataType={setSidebarDataType}
                      setSelectedTabForPrint={setSelectedTabForPrint}
                      setDataToPrint={setDataToPrint}
                      setShowEmailModal={setShowEmailModal}
                      setDataForMail={setDataForMail}
                      isSelected={selectedCards.has(item._id)}
                      onSelect={handleCardSelect}
                    />
                  ))}
                </>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
}

export default ApprovalPage;
