import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import {
  generateDateString,
  mobileWidth,
  printWithSpecialFileName,
  tabletWidth,
  unCamelCaseString,
} from '../../helperFunction';
import PurchasePdf from '../../pdfGeneratorFiles/purchaseOrderPdf';
import {
  useBulkApproveCardsMutation,
  useGetPendingStatusCountsQuery,
  useLazyGetAllPendingStatusDataPaginatedQuery
} from '../../slices/kanbanApiSlice';
import { Store } from '../../store/Store';
import { customConfirm } from '../../utils/customConfirm';
import QuotationPDF from '../SalesOrderManagement/Quotation/QuotationPDF';
import RightSidebar from '../global/components/RightSidebar';
import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import { TabButton, TabContainer } from '../global/components/TabContainer';
import SalesReport from '../salesOrder/salesReport';
import { Label } from '../v2';
import MediaModal from '../v3/global/components/MediaModal';
import ApprovalList from './ApprovalList';
import ApprovalSidebarData from './ApprovalSidebarData';
import SendApprovalMail from './SendApprovalMail';

function ApprovalPage() {
  // State management
  const [selectedTab, setSelectedTab] = useState('all');
  const [pendingItems, setPendingItems] = useState([]);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Get counts for initial load
  const { data: countsData } = useGetPendingStatusCountsQuery();

  // Lazy query for paginated data
  const [fetchPendingData, { isFetching }] = useLazyGetAllPendingStatusDataPaginatedQuery();

  // Other state
  const [ShowSidebar, setShowSidebar] = useState(false);
  const [SidebarData, setSidebarData] = useState({});
  const [SidebarDataType, setSidebarDataType] = useState('');
  const [SelectedTabForPrint, setSelectedTabForPrint] = useState('');
  const [Total, setTotal] = useState(0);
  const [DataToPrint, setDataToPrint] = useState(null);
  const [ShowEmailModal, setShowEmailModal] = useState(false);
  const [DataForMail, setDataForMail] = useState(null);
  const [Media, setMedia] = useState([]);
  const [ReadMore, setReadMore] = useState(false);
  const [selectedCards, setSelectedCards] = useState(new Set());
  const [bulkApproveCards, { isLoading: isBulkLoading }] = useBulkApproveCardsMutation();

  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const {
    defaults: { defaultParam },
  } = useContext(Store);
  const [allCount, setAllCount] = useState(0);

  const disableMap = {
    purchaseIndents: 'indent',
    purchaseOrders: 'purchaseOrder',
    salesQuotations: 'quotation',
    salesInvoices: 'salesInvoice',
    salesOrders: 'salesOrder',
  };

  const disabledPages = (
    defaultParam?.projectDefaults?.disabledApprovalFor || []
  )?.map(
    (key) =>
      Object.keys(disableMap).find((mapKey) => disableMap[mapKey] === key) ||
      key
  );

  // Create options from counts data
  const options = useMemo(() => {
    if (!countsData) return [];

    return Object.keys(countsData)
      .filter(key => !disabledPages.includes(key))
      .map((key) => ({
        label: unCamelCaseString(key).replace(' ', '\xA0').trim(),
        value: key,
      }));
  }, [countsData, disabledPages]);

  // Load initial data
  const loadData = useCallback(async (category = 'all', page = 1, reset = false) => {
    try {
      setIsLoadingMore(true);
      const result = await fetchPendingData({
        page,
        limit: 20,
        category
      }).unwrap();

      if (reset || page === 1) {
        setPendingItems(result.data || []);
      } else {
        setPendingItems(prev => [...prev, ...(result.data || [])]);
      }

      setHasNextPage(result.hasMore || false);
      setCurrentPage(page);
    } catch (error) {
      toast.error('Failed to load pending approvals');
    } finally {
      setIsLoadingMore(false);
    }
  }, [fetchPendingData]);

  // Load more data for infinite scroll
  const loadNextPage = useCallback(() => {
    if (!isLoadingMore && hasNextPage) {
      loadData(selectedTab, currentPage + 1, false);
    }
  }, [loadData, selectedTab, currentPage, isLoadingMore, hasNextPage]);

  // Handle tab change
  const handleTabChange = useCallback((newTab) => {
    setSelectedTab(newTab);
    setSelectedCards(new Set());
    loadData(newTab, 1, true);
  }, [loadData]);

  // Initial load
  useEffect(() => {
    loadData(selectedTab, 1, true);
  }, [loadData, selectedTab]);

  // Calculate total count
  useEffect(() => {
    if (countsData) {
      const total = Object.values(countsData).reduce((sum, count) => sum + count, 0);
      setAllCount(total);
    }
  }, [countsData]);

  // Print functionality
  useEffect(() => {
    let timer = null;
    let sum = 0;
    if (SelectedTabForPrint && DataToPrint) {
      DataToPrint?.productDetails?.map((item) => {
        sum += item?.totalAmount || 0;
      });
      setTotal(sum);
      clearTimeout(timer);
      timer = setTimeout(() => {
        printWithSpecialFileName();
      }, 400);
    }
    return () => {
      clearTimeout(timer);
    };
  }, [SelectedTabForPrint, DataToPrint]);

  window.onafterprint = () => {
    setSelectedTabForPrint('');
    setDataToPrint(null);
  };

  // Card selection handlers
  const handleCardSelect = useCallback((cardId, isSelected) => {
    const newSelected = new Set(selectedCards);
    if (isSelected) {
      newSelected.add(cardId);
    } else {
      newSelected.delete(cardId);
    }
    setSelectedCards(newSelected);
  }, [selectedCards]);

  const handleSelectAll = useCallback(() => {
    const allCardIds = new Set(pendingItems.map(item => item._id));
    setSelectedCards(selectedCards.size === allCardIds.size ? new Set() : allCardIds);
  }, [pendingItems, selectedCards]);

  // Bulk approval handler
  const handleBulkApproval = useCallback(async (action) => {
    if (selectedCards.size === 0) return;

    const confirm = await customConfirm(
      `Are you sure you want to ${action} ${selectedCards.size} selected items?`,
      action === 'approve' ? 'success' : ''
    );

    if (!confirm) return;

    const cardsToProcess = pendingItems
      .filter(item => selectedCards.has(item._id))
      .map(item => ({
        id: item._id,
        type: item._category || selectedTab,
        data: item
      }));

    try {
      await bulkApproveCards({ cards: cardsToProcess, action }).unwrap();
      toast.success(
        `Successfully ${action === 'approve' ? 'approved' : 'rejected'} ${cardsToProcess.length} items`,
        { toastId: `bulk-${action}` }
      );
      setSelectedCards(new Set());
      // Reload data after bulk operation
      loadData(selectedTab, 1, true);
    } catch (error) {
      toast.error(
        `Failed to ${action} selected items. Please try again.`,
        { toastId: `bulk-${action}-error` }
      );
    }
  }, [selectedCards, pendingItems, selectedTab, bulkApproveCards, loadData]);

  if (isFetching && pendingItems.length === 0) return <Spinner />;

  return (
    <>
      {ShowEmailModal && (
        <SendApprovalMail
          isMobile={isMobile}
          isTablet={isTablet}
          selectedtab={SelectedTabForPrint}
          setShowEmailModal={setShowEmailModal}
          data={DataForMail}
        />
      )}
      {SelectedTabForPrint === 'purchaseOrders' && DataToPrint && (
        <PurchasePdf data={DataToPrint} />
      )}
      {SelectedTabForPrint === 'salesQuotations' && DataToPrint && (
        <QuotationPDF
          data={DataToPrint}
          total={Total}
          generateDateString={generateDateString}
        />
      )}
      {SelectedTabForPrint === 'salesOrders' && DataToPrint && (
        <SalesReport data={DataToPrint} />
      )}
      <div>
        <RightSidebar
          openSideBar={ShowSidebar}
          setOpenSideBar={setShowSidebar}
          className={'w-full md:w-1/3'}
        >
          <ApprovalSidebarData
            data={SidebarData}
            tab={SidebarDataType}
            setReadMore={setReadMore}
            setShowSidebar={setShowSidebar}
            setMedia={setMedia}
            showSidebar={ShowSidebar}
          />
        </RightSidebar>
        {ReadMore && (
          <MediaModal
            ShowModal={ReadMore}
            FormData={Media}
            setShowModal={setReadMore}
            isView={true}
          />
        )}
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-3 md:mx-[-1rem] lg:mx-[6rem]">
          <div className="flex items-center justify-start gap-x-3">
            <TabContainer className="!m-0">
              <TabButton
                isactive={selectedTab === 'all'}
                onClick={() => handleTabChange('all')}
                className={'flex items-center gap-x-2'}
              >
                Pending Approvals
                <p className="flex items-center justify-center text-[13px] bg-blue-500 text-white rounded-full px-2">
                  {selectedTab === 'all' ? allCount : (countsData?.[selectedTab] || 0)}
                </p>
              </TabButton>
            </TabContainer>
          </div>
          <div className="w-full md:w-[14rem] mt-4 md:mt-0 lg:mt-0">
            <Label>Select Approval</Label>
            <Select
              options={[{ label: 'All Options', value: 'all' }, ...options]}
              value={selectedTab}
              onChange={(e) => {
                handleTabChange(e.target.value);
              }}
            />
          </div>
        </div>

        {selectedCards.size > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4 md:mx-[-1rem] lg:mx-[6rem]">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleSelectAll}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  {selectedCards.size > 0 ? 'Deselect All' : 'Select All'}
                </button>
                <span className="text-sm text-gray-600">
                  {selectedCards.size} item(s) selected
                </span>
              </div>
              {selectedCards.size > 0 && (
                <div className="flex gap-2">
                  <button
                    onClick={() => handleBulkApproval('approve')}
                    disabled={isBulkLoading}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white text-sm font-medium rounded-lg transition-colors"
                  >
                    {isBulkLoading ? 'Processing...' : `Approve ${selectedCards.size}`}
                  </button>
                  <button
                    onClick={() => handleBulkApproval('reject')}
                    disabled={isBulkLoading}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white text-sm font-medium rounded-lg transition-colors"
                  >
                    {isBulkLoading ? 'Processing...' : `Reject ${selectedCards.size}`}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="md:mx-[-14px] lg:mx-[6rem]">
          {pendingItems.length === 0 && !isFetching ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No pending approvals found</p>
            </div>
          ) : (
              <ApprovalList
                items={pendingItems}
                hasNextPage={hasNextPage}
                isNextPageLoading={isLoadingMore}
                loadNextPage={loadNextPage}
                selectedCards={selectedCards}
                onCardSelect={handleCardSelect}
                setShowSidebar={setShowSidebar}
                setSidebarData={setSidebarData}
                setSidebarDataType={setSidebarDataType}
                setSelectedTabForPrint={setSelectedTabForPrint}
                setDataToPrint={setDataToPrint}
                setShowEmailModal={setShowEmailModal}
                setDataForMail={setDataForMail}
                height={600}
              />
          )}
        </div>
      </div>
    </>
  );
}

export default ApprovalPage;
