import { useContext, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import useDebounceValue from '../../hooks/useDebounceValue';
import {
  useGetBaseItemsMutation,
  useGetVariantItemsMutation,
} from '../../slices/variantApiSlice';
import { Store } from '../../store/Store';
import Button from './components/Button';
import Select from './components/Select';

function SelectComp({
  hierarchyIdx = 0,
  options = [],
  value = [],
  onItemAdd,
  checkQuantity,
  setParentSearch,
  onLastItemSelect,
  disableAddButton,
  storeId,
}) {
  const [selectedOption, setSelectedOption] = useState({});
  const [search, setSearch] = useState('');
  const [optionsForNextSelect, setOptionsForNextSelect] = useState();

  // const {
  //   defaults: {
  //     defaultParam: { projectDefaults: { variantLabels = [] } = {} } = {},
  //   } = {},
  // } = useContext(Store);

  const debouncedSearch = useDebounceValue(search);

  const [getVariantItems, { isLoading }] = useGetVariantItemsMutation();

  useEffect(() => {
    if (selectedOption?.childrenCount > 0) {
      getVariantItems({
        data: {
          type: selectedOption?.type,
          parentId: selectedOption?.value,
          variantIndex: (selectedOption?.variantIndex || 0) + 1,
          ids: value,
          search: debouncedSearch,
          checkQuantity,
          storeId,
        },
      })
        .unwrap()
        .then((res) => {
          if (checkQuantity && res?.length === 0) {
            toast.error(
              'No items with sufficient quantity to make transaction'
            );
          }
          setOptionsForNextSelect(res);
        });
    } else {
      setOptionsForNextSelect(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    selectedOption?.childrenCount,
    selectedOption?.type,
    selectedOption?.value,
    selectedOption?.variantIndex,
    getVariantItems,
    debouncedSearch,
    checkQuantity,
  ]);

  const handleSingleAdd = () => {
    const exists = value?.includes(selectedOption?.value);

    if (exists) {
      toast.error('Item already selected');
      return;
    }

    if (onItemAdd) onItemAdd([...value, selectedOption?.value]);
    setSelectedOption({});
    if (hierarchyIdx === 0) {
      setParentSearch('');
    } else {
      setSearch('');
    }
  };

  return (
    <>
      <div>
        {' '}
        <label>{hierarchyIdx ? `Variant ${hierarchyIdx}` : 'Parent'}</label>
        <Select
          className="mb-3"
          value={selectedOption?.value}
          onChange={(e) => {
            if (onLastItemSelect) onLastItemSelect(null);
            const item = options?.find((i) => i?.value === e?.target?.value);

            setSelectedOption(item);
            if (onLastItemSelect && item?.childrenCount === 0) {
              onLastItemSelect(item);
            }
          }}
          onInputChange={(e) => {
            if (!e) return;
            if (hierarchyIdx === 0) {
              setParentSearch(e);
            } else {
              setSearch(e);
            }
          }}
          options={options}
        />
      </div>

      {selectedOption?.value && optionsForNextSelect?.length > 0 && (
        <SelectComp
          key={selectedOption?.value}
          hierarchyIdx={hierarchyIdx + 1}
          options={optionsForNextSelect}
          value={value}
          onItemAdd={onItemAdd}
          checkQuantity={checkQuantity}
          onLastItemSelect={onLastItemSelect}
          disableAddButton={disableAddButton}
        />
      )}
      {selectedOption?.value &&
        !optionsForNextSelect &&
        !isLoading &&
        !disableAddButton && (
          <div className="mt-6">
            <Button onClick={handleSingleAdd} size="sm" className="">
              Add
            </Button>
          </div>
        )}
    </>
  );
}

function VariantSelector({
  type = '',
  value = [],
  onItemAdd,
  checkQuantity = false,
  onLastItemSelect,
  disableAddButton = false,
  storeId = null,
}) {
  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounceValue(search);

  const {
    defaults: {
      defaultParam: { projectDefaults: { enableHorizontalSelector } = {} } = {},
    } = {},
  } = useContext(Store);

  const [getBaseItems, { data }] = useGetBaseItemsMutation();

  useEffect(() => {
    getBaseItems({
      data: {
        type,
        ids: value,
        search: debouncedSearch || '',
        checkQuantity,
        storeId,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, debouncedSearch, checkQuantity, storeId]);

  return (
    <div
      className={`w-full ${
        enableHorizontalSelector
          ? 'flex flex-wrap gap-3 items-start [&>div]:min-w-[250px] [&>div]:flex-1'
          : ''
      }`}
    >
      <SelectComp
        hierarchyIdx={0}
        options={data}
        value={value}
        onItemAdd={onItemAdd}
        checkQuantity={checkQuantity}
        setParentSearch={setSearch}
        onLastItemSelect={onLastItemSelect}
        disableAddButton={disableAddButton}
        storeId={storeId}
      />
    </div>
  );
}

export default VariantSelector;
