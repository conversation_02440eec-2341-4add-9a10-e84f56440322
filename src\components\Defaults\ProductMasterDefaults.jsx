import { InfoTooltip } from '../global/components/InfoTooltip';

const ProductMasterDefaults = ({ defaults, setDefaults }) => {
  return (
    <div className="py-2 border-b-2 border-gray-300/70">
      <h3 className="text-gray-subHeading mb-4">Product Master Defaults :</h3>
      <div className="flex gap-x-4 items-center">
        <input
          type="checkbox"
          checked={defaults?.projectDefaults?.showProductMasterModal || false}
          onChange={(e) => {
            setDefaults((prev) => ({
              ...prev,
              projectDefaults: {
                ...prev.projectDefaults,
                showProductMasterModal: e.target.checked,
              },
            }));
          }}
        />
        <div className="flex gap-x-2 items-center">
          <label className=" text-sm font-medium text-gray-700">
            Show Product Master Modal
          </label>
          <InfoTooltip
            position="top"
            width="200px"
            id="downtimeType"
            isHtml={true}
            content="This Checkbox used to show the Product Master Modal"
          />
        </div>
      </div>
    </div>
  );
};

export default ProductMasterDefaults;
