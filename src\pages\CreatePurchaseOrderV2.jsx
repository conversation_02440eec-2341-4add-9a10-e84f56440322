import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMediaQuery } from 'react-responsive';
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom';
import { toast } from 'react-toastify';
import { default as AdditionalInfo } from '../components/PurchaseOrder/AdditionalInfo';
import {
  generatePrefixId,
  getLocalDate,
  handleFormula,
  isObjectEmpty,
  mobileWidth,
  renderFieldsBasedOnType,
  tabletWidth,
} from '../helperFunction';
// import CreatePOPreviewPDF from '../components/PurchaseOrder/CreatePOPreviewPDF';
import GeneralInfo from '../components/PurchaseOrder/GeneralInfo';
import ProductsInfo from '../components/PurchaseOrder/ProductsInfo';
import PurchaseAddItemModal from '../components/PurchaseOrder/PurchaseAddItemModal.jsx';
import ProductMasterSelectorModal from '../components/global/ProductMasterSelectorModal.jsx';
import Button from '../components/global/components/Button';
import Header from '../components/global/components/Header';
import AddPaymentTerm from '../components/v3/InventoryMasters/AddPaymentTerm.jsx';
import AddVendorModal from '../components/v3/global/components/AddVendorModal.jsx';
import { default as useCompanyDetailsSelector } from '../hooks/useCompanyDetailsSelector';
import useHeaderAndFooter from '../hooks/useHeaderAndFooter';
import { useUpdateDefaultsMutation } from '../slices/defaultsApiSlice';
import { useGetDropdownsQuery } from '../slices/dropdownApiSlice.js';
import {
  useEditIndentMutation,
  useLazyGetSpecifcIndentQuery,
} from '../slices/indentApiSlice';
import { useCreateOrderMutation } from '../slices/orderApiSlice';
import { clearOutSourceToPoData } from '../slices/outSoureToPoSlice.js';
import { useGetAllPartsForOptionsQuery } from '../slices/partApiSlice';
import { useGetAllProductsForOptionsQuery } from '../slices/productApiSlice';
import {
  useCreatePurchaseOrderMutation,
  useGetLatestPoQuery,
  useLazyGetPurchaseOrderByIdQuery,
  useUpdatePurchaseOrderMutation,
} from '../slices/purchaseOrderApiSlice';
import { useGetSelectedVariantsMutation } from '../slices/variantApiSlice.js';
import {
  useGetAllVendorsForOptionsQuery,
  useLazyGetVendorsQuery,
} from '../slices/vendorApiSlice';
import { Store } from '../store/Store';
import useNaigationBlocker from '../hooks/useNaigationBlocker.js';

const CreatePurchaseOrderV2 = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const [SearchParams, setSearchParams] = useSearchParams({
    with_indent: '',
  });
  const queryParams = new URLSearchParams(location.search);
  const isCopy = queryParams.get('isCopy');
  const { defaults: defaultParam, state, dispatch } = useContext(Store);
  const [indent, setIndent] = useState([]);
  const [getPo] = useLazyGetPurchaseOrderByIdQuery();
  const [getIndent] = useLazyGetSpecifcIndentQuery();
  const [getVendors, vendorResults] = useLazyGetVendorsQuery();
  const { data: allParts = [] } = useGetAllPartsForOptionsQuery();
  const { data: allProducts = [] } = useGetAllProductsForOptionsQuery();
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [Searchparams] = useSearchParams();
  const [createPO, { isLoading: isCreatePoLoading }] =
    useCreatePurchaseOrderMutation();
  const [updatePO, { isLoading: isUpdatePoLoading }] =
    useUpdatePurchaseOrderMutation();
  const [editIndentData] = useEditIndentMutation();
  const [createDepOrder] = useCreateOrderMutation();
  const [updateDefaults] = useUpdateDefaultsMutation();
  const [additionalFields, setAdditionalFields] = useState(null);
  const [subtotalFormulaPrice, setSubtotalFormulaPrice] = useState(0);
  const [type, setType] = useState(
    defaultParam?.defaultParam?.poLastProductType || ''
  );
  const dispatchOut = useDispatch();

  const itemsFromOutSource = useSelector(
    (state) => state?.outSourceToPo?.items
  );
  const checkFromOutSource = useSelector(
    (state) => state?.outSourceToPo?.check
  );
  const vendorId = useSelector((state) => state?.outSourceToPo?.vendorId);
  const [dataDefault, setDefaults] = useState({
    purchaseOrder: {
      billingAddress: '',
      comments: '',
      deliveryAddress: '',
      paymentTerm: '',
      terms: '',
    },
  });
  const [isEdit, setIsEdit] = useState(false);
  const [formData, setFormData] = useState({
    indentLink: [],
    items: [],
    poID: '',
    deliveryDate: '',
    poStatus: 'pending',
    amount: 0,
    total: 0,
    deliveryAddress: '',
    billingAddress: '',
    vendorSelectedAddress: '',
    vendorSelectedContact: '',
    paymentTerm: '',
    terms: [],
    comments: [],
    vendor: {},
    files: '',
    approvedBy: '',
    productColumnHideStatus: {},
    customColumnsHideStatus: {},
  });
  const [AddNewVendor, setAddNewVendor] = useState(false);

  const [itemTypes, setItemTypes] = useState([]);
  const [pdf, setpdf] = useState([]);
  const [deletedMedia, setDeletedMedia] = useState([]);
  const [items, setItems] = useState([]);
  const [vendor, setVendor] = useState(formData?.vendor || {});
  const [columnInputs, setColumnInputs] = useState(['']);
  const [companySelectedDetails, setCompanySelectedDetails] = useState();
  const [selectedTermAndCondition, setSelectedTermAndCondition] = useState([]);
  const [selectedItem, setSelectedItem] = useState([]);
  const [selectedParts, setSelectedParts] = useState([]);
  const [fromOtherPage, setFromOtherPage] = useState(false);
  const { data: latestpo } = useGetLatestPoQuery();
  const [showIgst, setShowIgst] = useState(false);
  const { data: dropdowns } = useGetDropdownsQuery();
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const [ShowAddNewModal, setShowAddNewModal] = useState(false);
  const [paymentTermOptions, setPaymentTermOptions] = useState([]);
  const [newAddedVendorId, setNewAddedVendorId] = useState();
  const { data: vendorData } = useGetAllVendorsForOptionsQuery();
  const [isAdded, setIsAdded] = useState(false);
  const [showAddItemModal, setShowAddItemModal] = useState(false);
  const [showMasterModal, setShowMasterModal] = useState(false);

  const [getSelectedVariants] = useGetSelectedVariantsMutation();

  const {
    companyDetails,
    addressSelector,
    contactSelector,
    emailSelector,
    profileSelector,
  } = useCompanyDetailsSelector(
    companySelectedDetails,
    setCompanySelectedDetails
  );
  const { header, footer } = useHeaderAndFooter(
    {},
    companySelectedDetails?.selectedDetails
  );

  const { allowNavigation } = useNaigationBlocker();

  const changeHandler = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  useEffect(() => {
    if (checkFromOutSource && itemsFromOutSource && vendorId) {
      const updatedItems = itemsFromOutSource?.map((item) => {
        if (item?.part) {
          return {
            ...item.part,
            quantity: item?.outQuantity ? parseFloat(item.outQuantity) : 0,
          };
        } else if (item?.product) {
          return {
            ...item.product,
            quantity: item?.outQuantity ? parseFloat(item.outQuantity) : 0,
          };
        } else {
          return {
            name: item?.manualEntry,
            quantity: item?.outQuantity ? parseFloat(item.outQuantity) : 0,
          };
        }
      });

      setFromOtherPage(true);
      setItems(updatedItems);
      setVendor(
        vendorResults?.data?.vendors?.items?.find((v) => v._id === vendorId)
      );
    }
  }, [itemsFromOutSource, checkFromOutSource, vendorResults]); // eslint-disable-line

  useEffect(() => {
    const compAddress = companyDetails?.address?.find(
      (i) => i._id === companySelectedDetails?.selectedDetails?.address
    );
    const compContact = companyDetails?.contactNumber?.find(
      (i) => i._id === companySelectedDetails?.selectedDetails?.contact
    );

    if (companyDetails?.name) {
      const res = `${companyDetails.name}
${compAddress?.street || ''}, ${compAddress?.city || ''},  ${compAddress?.state || ''} - ${compAddress?.postalCode || ''}, ${compAddress?.country || ''}
${compContact?.number || ''}
${companyDetails?.gstNumber}  `;
      setFormData((prev) => ({
        ...prev,
        billingAddress: res,
      }));
    }
  }, [
    companyDetails?.name,
    companyDetails?.address,
    companyDetails?.contactNumber,
    companyDetails?.gstNumber,
    companySelectedDetails?.selectedDetails?.address,
    companySelectedDetails?.selectedDetails?.contact,
  ]);

  useEffect(() => {
    const handleSelectAddedVendor = async () => {
      const newAddedVendor = vendorData?.find(
        (el) => el._id === newAddedVendorId
      );
      setVendor(newAddedVendor);
    };
    if (isAdded) {
      handleSelectAddedVendor();
    }
  }, [isAdded]); // eslint-disable-line

  useEffect(() => {
    if (!dropdowns) return;
    const PaymentTerm = dropdowns?.dropdowns?.find((dropdown) => {
      return dropdown?.name === 'Payment Term';
    });

    setPaymentTermOptions(PaymentTerm?.values);
  }, [dropdowns]);

  useEffect(() => {
    (async () => {
      const indentIds = JSON.parse(localStorage.getItem('storedIds'));
      if (indentIds?.length > 0) {
        let indentItems = [];
        for (let i = 0; i < indentIds?.length; i++) {
          const indent = await getIndent({ id: indentIds[i] }).unwrap();
          indentItems = [...indentItems, indent];
        }

        const allVendors = await getVendors(
          indentItems[0]?.vendor_name
        ).unwrap();

        const selectedVendor = allVendors?.vendors?.items?.find(
          (el) => el?.name === indentItems[0]?.vendor_name
        );
        setVendor(selectedVendor);

        const tmpitems = [];
        const itemsType = [];
        for (let i = 0; i < indentItems.length; i++) {
          const item = indentItems[i];
          if (SearchParams?.get('with_indent') === 'true') {
            let temp = [...(allParts || []), ...(allProducts || [])];
            temp.forEach((part) => {
              if (part?.name === indentItems[i]?.product_name) {
                if (part?.type === 'ProductVariant') {
                  tmpitems.push({
                    ...part,
                    rate: part?.itemDetails?.rate,
                    discount: part?.itemDetails?.discount,
                    quantity: indentItems[i]?.quantity,
                    cgst: part?.itemDetails?.cgst,
                    sgst: part?.itemDetails?.sgst,
                    igst: part?.itemDetails?.igst,
                    hsn_sacCode: part?.itemDetails?.hsn,
                  });
                  itemsType.push(item.type + ': ' + item?.product_name);
                } else {
                  const vendorDetails = part?.vendor_details?.find(
                    (ven) => ven?.vendor?._id === selectedVendor?._id
                  );
                  if (!isObjectEmpty(vendorDetails)) {
                    part = {
                      ...part,
                      rate: vendorDetails?.rate,
                      discount: vendorDetails?.discount,
                      quantity: indentItems[i]?.quantity,
                    };
                  } else {
                    part = {
                      ...part,
                      quantity: indentItems[i]?.quantity,
                    };
                  }
                  tmpitems.push(part);
                  itemsType.push(item.type + ': ' + item?.product_name);
                }

                // if (!isObjectEmpty(vendorDetails)) {
                //   setDiscount((prev) => [...prev, vendorDetails?.discount]);
                //   setRate((prev) => [...prev, vendorDetails?.rate]);
                // }
              }
            });
          }
        }
        const filteredTmpItems = tmpitems.filter((item) => item !== undefined);
        setItems(filteredTmpItems);
        setItemTypes(itemsType);
        setSelectedItem(filteredTmpItems);
        setSelectedParts(filteredTmpItems?.map((elem) => elem?.value));
        localStorage.removeItem('storedIds');
        if (id === 'new') {
          setIndent(indentItems);
        }
      } else {
        getVendors('');
      }
    })();
  }, [getIndent, getVendors, id, SearchParams, allParts, allProducts]);

  useEffect(() => {
    (async () => {
      setDefaults(defaultParam);
      let obj = {};
      if (latestpo) {
        const { productColumnHideStatus, customColumnsHideStatus } = latestpo;
        if (id === 'new') {
          obj = {
            ...formData,
            deliveryAddress: defaultParam?.purchaseOrder?.deliveryAddress,
            paymentTerm: defaultParam?.purchaseOrder?.paymentTerm,
            terms: defaultParam?.purchaseOrder?.terms,
            comments: defaultParam?.purchaseOrder?.comments,
            productColumnHideStatus: productColumnHideStatus || {},
            customColumnsHideStatus: customColumnsHideStatus || {},
          };
          setIsEdit(false);
        } else {
          const poData = await getPo({ id }).unwrap();
          setIndent(
            poData?.viewPurchaseOrder?.indentLink?.map((indent) => indent)
          );
          obj = {
            ...formData,
            pdf: poData?.viewPurchaseOrder?.files,
            indentLink: poData?.viewPurchaseOrder?.indentLink?.map(
              (indent) => indent
            ),
            productColumnHideStatus:
              poData?.viewPurchaseOrder?.productColumnHideStatus,
            customColumnsHideStatus:
              poData?.viewPurchaseOrder?.customColumnsHideStatus,
            deliveryDate: poData?.viewPurchaseOrder?.deliveryDate
              ? getLocalDate(poData?.viewPurchaseOrder?.deliveryDate)
                  .split('/')
                  .reverse()
                  .join('-')
              : '',
            deliveryAddress: poData?.viewPurchaseOrder?.deliveryAddress,
            paymentTerm: poData?.viewPurchaseOrder?.paymentTerm,
            terms: poData?.viewPurchaseOrder?.terms,
            comments: poData?.viewPurchaseOrder?.comments,
            vendor: poData?.viewPurchaseOrder?.vendor,
            termsAndConditionsHide:
              poData?.viewPurchaseOrder?.termsAndConditionsHide,
          };
          setVendor(poData?.viewPurchaseOrder?.vendor);
          setItemTypes(
            poData?.viewPurchaseOrder?.items?.map((item) => item?.item?.name)
          );
          if (!isCopy) {
            obj = {
              ...obj,
              poID: poData?.viewPurchaseOrder?.poID,
            };
          }
          setIsEdit(true);
          setItems(poData?.viewPurchaseOrder?.items?.map((item) => item.item));
          let selected = poData?.viewPurchaseOrder?.items?.map(
            (item) => item.item?.value
          );
          setSelectedItem(poData?.viewPurchaseOrder?.items);
          setSelectedParts(selected);
        }
      }

      setFormData(obj);
    })();
  }, [defaultParam, getPo, id, isCopy, setFormData, getPo, latestpo]); //eslint-disable-line

  const itemsFormulaHandler = () => {
    let transformedItems = [];

    for (let item in items) {
      let temp = { ...items?.[item] };

      if (items?.[item]?.customColumns) {
        for (let columnInput of columnInputs) {
          if (columnInput?.formula) {
            let val = {};
            let formula = columnInput?.formula
              ?.substring(
                columnInput?.formula?.indexOf('{') + 1,
                columnInput?.formula?.indexOf('}')
              )
              .trim();

            formula += ' ';

            while (formula?.indexOf('$') !== -1) {
              let key = formula?.substring(
                formula?.indexOf('$') + 1,
                formula?.indexOf(' ')
              );
              let stringRegex = /^[A-Za-z]+$/;
              if (stringRegex.test(key)) {
                val = {
                  ...val,
                  [key]:
                    items?.[item]?.[key] ||
                    parseInt(items?.[item]?.customColumns?.[key]) ||
                    0,
                };
              }
              formula = formula?.substring(formula?.indexOf(' ') + 1);
            }
            const allFields = [
              {
                name: 'cgst',
                percentOf: 'amount',
              },
              {
                name: 'sgst',
                percentOf: 'amount',
              },
              {
                name: 'igst',
                percentOf: 'amount',
              },
              {
                name: 'discount',
                percentOf: 'amount',
              },
            ];
            let res = handleFormula(columnInput, val, allFields);

            // Directly update the customColumns object instead of creating an array
            temp.customColumns = {
              ...temp.customColumns,
              [columnInput?.columnName]: res,
            };
          }
        }
      }
      let itemObj = {
        item: temp,
        itemType: itemTypes[item] || 'Part',
        quantity: temp?.quantity,
        rate: temp?.rate,
        cgst: temp?.cgst,
        sgst: temp?.sgst,
        igst: temp?.igst,
        hsn: temp?.hsn_sacCode,
        discount: temp?.discount,
      };

      transformedItems?.push(itemObj);
    }

    return transformedItems;
  };

  const handleInputChangefunck = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }
    if (fieldValue === '+') {
      setTemplateDropDownModal(true);
      setDropdownIdx(idx);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field, index) => {
          if (field?.fieldName === fieldName && index === idx) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );

      setAdditionalFields((prev) => {
        return {
          ...prev,
          templateData: updatedAdditionalFields,
        };
      });
    }
  };
  const save = async () => {
    try {
      for (let i = 0; i < items?.length; i++) {
        if (items?.[i]?.quantity < 0) {
          toast.error('Quantity can not ne negative');
          return;
        }
        if (items?.[i]?.rate < 0) {
          toast.error('Rate can not ne negative');
          return;
        }
        if (items?.[i]?.discount < 0) {
          toast.error('Discount can not ne negative');
          return;
        }
      }
      const updatedTerms = selectedTermAndCondition?.map((term) => term?.value);
      let transformedItems = itemsFormulaHandler();
      const data = {
        ...formData,
        deliveryDate: formData?.deliveryDate
          ? new Date(formData?.deliveryDate)
          : '',
        indentLink: indent || [],
        items: transformedItems,
        poStatus: 'pending',
        terms: updatedTerms,
        files: pdf,
        vendor: vendor,
        approvedBy: state?.user?.name || state?.user?.username,
        additionalFields: additionalFields,
        showIgst,
        subTotal: subtotalFormulaPrice,
        vendorSelectedAddress: vendor?.address?.[0],
        vendorSelectedContact: vendor?.contact?.[0],
      };

      if (items?.length <= 0) {
        toast.error('Enter the Product Details');
      } else {
        let res;
        if (id === 'new' || isCopy) {
          res = await createPO({ data }).unwrap();
          if (res?.created) {
            // Clearing the OutSOurce Data if Submit Properly
            dispatchOut(clearOutSourceToPoData());
            for (let i = 0; i < res?.created?.indentLink?.length; i++) {
              await editIndentData({
                editdata: { po: 'created' },
                id: res?.created?.indentLink[i],
              });
            }
          }
        } else {
          data.deletedMedia = deletedMedia;
          updatePO({ data, id });
        }
        let obj = {
          objRef: res?.created?._id,
          currentDepartment: 'purchase',
          refKey: 'PurchaseOrder',
          currentPage: 'PO Dashboard',
          userId: state?.user?._id,
          taskId: generatePrefixId(
            defaultParam?.defaultParam?.prefixIds?.['taskId']
          ),
        };

        await createDepOrder({
          data: obj,
        });

        let temp = { poLastProductType: type };
        if (temp) {
          await updateDefaults(temp);
        }

        const kanban = SearchParams.get('kanban') === 'true';
        const orderId = SearchParams.get('orderId');
        const navigateParams = {
          department: SearchParams.get('department'),
          id: res?.created?._id,
          refType: SearchParams.get('refType'),
          page: SearchParams.get('page'),
          taskId: SearchParams.get('taskId'),
          index: SearchParams.get('index'),
          orderId,
        };

        if (kanban) {
          let time = new Date();
          dispatch({
            type: 'ADD_CARD',
            payload: {
              data: {
                taskId: SearchParams.get('taskId'),
                stepPage: 'PO Dashboard',
                updatedAt: time?.toDateString(),
              },
              currentColumn: 'PO Dashboard',
            },
          });
        }

        const filteredParams = Object.fromEntries(
          Object.entries(navigateParams).filter(([_, value]) => value !== null)
        );

        const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;

        allowNavigation();

        if (kanban) {
          navigate(navigateStr);
        } else if (SearchParams?.get('navigateTo')) {
          navigate(SearchParams.get('navigateTo'));
        } else {
          navigate('/purchase/po');
        }

        if (id === 'new') {
          toast.success('Purchase Order Created Successfully');
        } else if (isCopy) {
          toast.success('Purchase Order Copied Successfully');
        } else {
          toast.success('Purchase Order Edited Successfully');
        }
      }
    } catch (error) {
      console.log('Error in Purchase Order', error); // eslint-disable-line
    }
  };

  const handleModalSubmit = async (ids) => {
    const res = await getSelectedVariants({ data: { ids } }).unwrap();
    if (res) {
      setItems((prevItems) => {
        const newItems = res?.map((pro) => ({
          ...pro,
          value: pro?._id,
          type: pro?.type,
          itemTypes: pro?.category?.name,
          customColumns: {},
          discount: pro?.itemDetails?.discount || 0,
          cgst: pro?.itemDetails?.cgst || 0,
          sgst: pro?.itemDetails?.sgst || 0,
          igst: pro?.itemDetails?.igst || 0,
        }));

        // Filter out duplicates based on _id
        const uniqueItems = newItems.filter(
          (newItem) =>
            !prevItems.some((existingItem) => existingItem._id === newItem._id)
        );

        return [...prevItems, ...uniqueItems];
      });
      setItemTypes((prev) => [
        ...prev,
        ...res?.map((pro) => pro?.category?.name || pro?.category),
      ]);
      toast.success('Items Added');
      setShowAddItemModal(false);
      setShowMasterModal(false);
    }
  };

  return (
    <>
      {showAddItemModal && (
        <PurchaseAddItemModal
          allItems={items}
          onCloseModal={() => setShowAddItemModal(false)}
          onSubmit={handleModalSubmit}
          type={type}
          setType={setType}
        />
      )}
      {showMasterModal && (
        <ProductMasterSelectorModal
          onSubmit={handleModalSubmit}
          onCloseModal={setShowMasterModal}
        />
      )}
      {ShowAddNewModal && (
        <AddPaymentTerm
          isMobile={isMobile}
          isTablet={isTablet}
          setShowModal={setShowAddNewModal}
          dropdowns={dropdowns}
          PaymentTermOptions={paymentTermOptions}
          setPaymentTermOptions={setPaymentTermOptions}
        />
      )}

      {AddNewVendor && (
        <AddVendorModal
          setNewAddedVendorId={setNewAddedVendorId}
          setIsAdded={setIsAdded}
          isMobile={isMobile}
          isTablet={isTablet}
          openVendorAddModal={AddNewVendor}
          setOpenVendorAddModal={setAddNewVendor}
        />
      )}

      <div
        className={`${!isMobile && !isTablet && 'mx-48'} !bg-white border-x-2 px-6 py-4`}
      >
        <div className="flex justify-between  w-full" id="ignore">
          <Button
            textColor=""
            color=""
            className="bg-gray-100 border-2 border-gray-500 text-gray-900 mb-12 !mt-3 "
            onClick={() => {
              dispatchOut(clearOutSourceToPoData());
              navigate(-1);
            }}
          >
            Back
          </Button>
        </div>
        <div className="flex cursor-pointer" id="ignore">
          <Header
            title="Create New PO"
            // description="Effortlessly generate purchase orders with our user-friendly Create New Purchase Order page. by selecting the vendor and specifying item details, such as quantity and unit price. Input billing and shipping information, set payment terms, and preview before saving or submitting."
            description=""
            infoTitle="Welcomes to Create PO page"
            infoDesc=""
            paras={[
              'The Purchase order page simplifies procurement processes.',
            ]}
            classNames=""
          />
        </div>
        {header()}
        <section>
          <GeneralInfo
            key={JSON.stringify(defaultParam?.defaultParam?.prefixIds?.poId)}
            isMobile={isMobile}
            isTablet={isTablet}
            paymentTermOptions={paymentTermOptions}
            setPaymentTermOptions={setPaymentTermOptions}
            ShowAddNewModal={ShowAddNewModal}
            setShowAddNewModal={setShowAddNewModal}
            AddNewVendor={AddNewVendor}
            setAddNewVendor={setAddNewVendor}
            changeHandler={changeHandler}
            formData={formData}
            addressSelector={addressSelector}
            contactSelector={contactSelector}
            emailSelector={emailSelector}
            profileSelector={profileSelector}
            dataDefault={dataDefault}
            setDefaults={setDefaults}
            companyDetails={companyDetails}
            SearchParams={SearchParams}
            setSearchParams={setSearchParams}
            vendor={vendor}
            setVendor={setVendor}
            setFormData={setFormData}
            companySelectedDetails={companySelectedDetails}
            isCopy={isCopy}
            indent={indent}
            additionalFields={additionalFields}
            defaultParam={defaultParam}
            setAdditionalFields={setAdditionalFields}
            Searchparams={Searchparams}
            isEdit={isEdit}
            setItems={setItems}
          />
        </section>
        <section>
          <ProductsInfo
            formData={formData}
            setFormData={setFormData}
            setItems={setItems}
            SearchParams={SearchParams}
            setSearchParams={setSearchParams}
            items={items}
            vendor={vendor}
            columnInputs={columnInputs}
            setColumnInputs={setColumnInputs}
            itemTypes={itemTypes}
            setItemTypes={setItemTypes}
            partsData={allParts}
            productsData={allProducts}
            selectedItem={selectedItem}
            setSelectedItem={setSelectedItem}
            selectedParts={selectedParts}
            setSelectedParts={setSelectedParts}
            id={id}
            fromOtherPage={fromOtherPage}
            setAdditionalFields={setAdditionalFields}
            showIgst={showIgst}
            setShowIgst={setShowIgst}
            subtotalFormulaPrice={subtotalFormulaPrice}
            setSubtotalFormulaPrice={setSubtotalFormulaPrice}
            setShowAddItemModal={setShowAddItemModal}
            setShowMasterModal={setShowMasterModal}
          />
        </section>
        <section>
          <AdditionalInfo
            isMobile={isMobile}
            isTablet={isTablet}
            formData={formData}
            setFormData={setFormData}
            isEdit={isEdit}
            dataDefault={dataDefault}
            setDefaults={setDefaults}
            pdf={pdf}
            setpdf={setpdf}
            setDeletedMedia={setDeletedMedia}
            selectedTermAndCondition={selectedTermAndCondition}
            setSelectedTermAndCondition={setSelectedTermAndCondition}
          />
        </section>
        {/* <section>
          <CreatePOPreviewPDF
            items={items}
            Vendor={vendor}
            poPrefixId={poPrefixId}
            header={header}
          />
        </section> */}
        <div>
          {additionalFields && (
            <div className="mt-5 w-full gap-3">
              {additionalFields.templateData.length > 0 && (
                <h3>Template Details</h3>
              )}
              <div className="w-full">
                {renderFieldsBasedOnType(
                  additionalFields,
                  handleInputChangefunck,
                  templateDropDownModal,
                  setTemplateDropDownModal,
                  setAdditionalFields,
                  newOptionStatus,
                  setNewOptionStatus,
                  dropdownIdx,
                  setDropdownIdx,
                  Searchparams
                )}
              </div>
            </div>
          )}
        </div>
        <div className="justify-end flex gap-3" id="ignore">
          {id === 'new' || isCopy ? (
            <Button
              isLoading={isCreatePoLoading}
              withLoading
              onClick={() => save()}
            >
              Save
            </Button>
          ) : (
            <Button
              isLoading={isUpdatePoLoading}
              withLoading
              onClick={() => save()}
            >
              Save Update
            </Button>
          )}
        </div>
        {footer()}
      </div>
    </>
  );
};

export default CreatePurchaseOrderV2;
