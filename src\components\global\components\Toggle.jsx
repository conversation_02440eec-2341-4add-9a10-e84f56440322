import { Switch } from '@headlessui/react';

const Toggle = ({
  className,
  disabled = false,
  value = false,
  checked = false,
  onChange,
  name = '',
  ...rest
}) => {
  const val = checked || value;
  return (
    <Switch
      onClick={() => {
        onChange({
          target: { checked: !val, value: !val, name, type: 'toggle' },
        });
      }}
      disabled={disabled}
      className={`bg-transparent relative inline-flex h-6 w-11 items-center shadow-[0_0_1px_1px_#AAAAAA] rounded-full ${className}`}
      {...rest}
    >
      <span className="sr-only">Enable notifications</span>
      <span
        className={`${
          val ? 'translate-x-6 bg-blue-primary' : 'translate-x-1 bg-gray-400'
        } inline-block h-4 w-4 transform rounded-full transition`}
      />
    </Switch>
  );
};

export default Toggle;
