import { CheckIcon } from '@heroicons/react/24/outline';
import { useEffect, useRef } from 'react';

const BreadCrumbElement = ({
  page,
  pIdx,
  step,
  setStep,
  pages,
  clickToNavigate,
  gap,
}) => {
  const tempRef = useRef(null);

  useEffect(() => {
    if (tempRef?.current) {
      if (step === pIdx) {
        tempRef?.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center',
        });
      }
    }
  }, [tempRef, step, pIdx]);

  return (
    <section
      className={`relative flex flex-col items-center gap-y-2 ${
        clickToNavigate ? 'cursor-pointer' : ''
      }`}
      onClick={() => {
        if (clickToNavigate) setStep(pIdx);
      }}
      ref={tempRef}
      title={page}
    >
      <section
        className={`relative h-8 w-8 aspect-square text-[12px] flex justify-center items-center rounded-full ${
          step > pIdx
            ? 'bg-[#87f3ab]'
            : step === pIdx
              ? 'bg-blue-primary text-white'
              : 'bg-gray-200 text-black/80'
        }`}
      >
        {step > pIdx ? (
          <CheckIcon className="h-4 w-4 text-[#14BA6D]" />
        ) : (
          pIdx + 1
        )}
        {pIdx + 1 === pages?.length ? null : (
          <section
            className={`absolute left-full top-1/2 border-t-2 pointer-events-none ${
              gap === 'normal' ? 'w-[250%]' : 'w-[350%]'
            } ${step > pIdx ? 'border-[#87f3ab]' : 'border-black/20'}`}
          />
        )}
      </section>
      <section
        className={`absolute flex items-center gap-[5px] top-[105%] text-[12px] leading-snug text-center ${
          step > pIdx
            ? 'text-[#14BA6D]'
            : step === pIdx
              ? 'text-blue-primary'
              : 'text-black/80'
        }`}
      >
        {page}
      </section>
    </section>
  );
};

const BreadCrumbs = ({
  pages,
  step,
  setStep,
  clickToNavigate = false,
  gap = 'normal',
}) => {
  return (
    <section
      className={`flex w-full items-center mt-2 pb-9 overflow-y-hidden no-scrollbar px-6 ${
        gap === 'normal' ? 'gap-x-14' : 'gap-x-16'
      }`}
    >
      {pages?.map((page, pIdx) => (
        <BreadCrumbElement
          key={pIdx}
          page={page}
          pIdx={pIdx}
          step={step}
          setStep={setStep}
          pages={pages}
          clickToNavigate={clickToNavigate}
          gap={gap}
        />
      ))}
    </section>
  );
};

export default BreadCrumbs;
