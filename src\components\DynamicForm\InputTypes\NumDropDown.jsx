import { useEffect, useState } from 'react';
import useFieldsAndTitle from '../../../hooks/useFieldsAndTitle';

const NumDropDown = ({ data, state, horIdx, verIdx, type, isDisabled }) => {
  const [dropdownData, setDropdownData] = useState([]);
  const [error, setError] = useState({ status: false, message: '' });
  const [horizontalFields, verticalFields, title] = useFieldsAndTitle(data);
  const { inputData, handleChange, renderState, dropdownSheet } = state;

  useEffect(() => {
    if (dropdownSheet && dropdownSheet.data) {
      // if master contains sheet with name DROPDOWN

      setError({ status: false, message: '' });
      const entry = dropdownSheet.data.find(
        (entry) =>
          entry.Parameter === data.Title.split('; ')[horIdx] ||
          entry.Parameter === data.Parameter
      );

      if (entry) {
        // if sheet parameter name and parameter type is same

        setDropdownData([]);
        const values = Object.values(entry);
        const filteredValues = values.filter((val, idx) => idx > 0 && val);
        if (filteredValues.every((val) => +val)) {
          setDropdownData(filteredValues);
        } else {
          // if value in array is not a number

          setError({
            status: true,
            message: `${data.Parameter} consists a value which is not a number`,
          });
        }
      } else {
        // if sheet parameter name and parameter type is not same

        setError({
          status: true,
          message: `No entry with name ${data.Parameter} found in the DROPDOWN sheet`,
        });
      }
    } else {
      // if master does not contains sheet with name DROPDOWN

      setError({
        status: true,
        message: `No sheet with name "${type}" found in the master`,
      });
    }
  }, [dropdownSheet, data, horIdx, type]);

  return (
    <>
      {renderState && (
        <>
          {error.status ? (
            <p className="text-red-600 text-[10px]">{error.message}</p>
          ) : (
            <div
              className={`flex border rounded items-center text-gray-200 leading-nomal w-full ${
                verticalFields.length > 1 &&
                verticalFields.length - 1 !== verIdx
                  ? ' mb-2'
                  : ''
              }`}
            >
              <select
                key={data.Parameter + title[horIdx] + verIdx.toString()}
                // name={data.Parameter}
                onChange={(e) => handleChange(e, 'NUMDROPDOWN')}
                className={`bg-white appearance-none rounded py-0.5 px-3 text-sm text-gray-200 leading-nomal focus:outline-none focus:ring-2 ${
                  data.Units !== '' ? ' w-4/5' : ' w-full border'
                } `}
                // value={
                // 	dropdownData.includes(inputData[data.Parameter].value)
                // 		? inputData[data.Parameter].value
                // 		: 'none'
                // }
                name={
                  horizontalFields.length > 1 && verticalFields.length > 1
                    ? `${data.Parameter}-${
                        title[horIdx]
                      }${verIdx.toString()}-hv`
                    : horizontalFields.length > 1
                    ? `${data.Parameter}-${title[horIdx]}-h`
                    : verticalFields.length > 1
                    ? `${data.Parameter}-${verIdx.toString()}-v`
                    : data.Parameter
                }
                value={
                  (horizontalFields.length > 1 && verticalFields.length > 1
                    ? inputData?.[data.Parameter]?.value?.[
                        title[horIdx] + verIdx.toString()
                      ]
                    : horizontalFields?.length > 1
                    ? inputData?.[data.Parameter]?.value?.[title[horIdx]]
                    : verticalFields?.length > 1
                    ? inputData?.[data.Parameter]?.value?.[verIdx]
                    : inputData?.[data.Parameter]?.value) || 'none'
                }
                disabled={isDisabled}
              >
                <option value="none" disabled hidden>
                  Please select an option
                </option>
                {dropdownData.map((option, idx) => (
                  <option
                    key={option + idx.toString()}
                    value={option}
                    className="text-md"
                  >
                    {option}
                  </option>
                ))}
              </select>
              {data.Units && (
                <span className="text-md w-1/5 h-fit font-normal text-sm text-center">
                  {data.Units}
                </span>
              )}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default NumDropDown;
