import { Button, Input, Modal } from 'antd';
import { Settings, Table as TableIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';
import Select from '../global/components/Select';
import Table from '../global/components/Table';
import DepartmentOptions from './DepartmentOptions';
import DropDownOptions from './DropDownOptions';

const DepartmentTableConfig = ({ tableConfig, setTableConfig }) => {
  const [previewTableModal, setPreviewTableModal] = useState(false);
  const [dropDownOptionModal, setDropDownOptionModal] = useState(false);
  const [DepartmentOptionsModal, setDepartmentOptionsModal] = useState(false);
  const [dropDownOptions, setDropDownOptions] = useState([]);
  const [departmentCheckedCols, setDepartmentCheckedCols] = useState([]);
  const [columnIndex, setColumnIndex] = useState(-1);

  const handleOk = () => {
    setPreviewTableModal(false);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Number of Columns
            </label>
            <div className="relative">
              <Input
                className="w-full"
                value={tableConfig?.columns?.length}
                onChange={(e) => {
                  if (e.target.value > 10) {
                    toast.error('Table Columns should be less than 10');
                    return;
                  }
                  const allColumns = [];
                  for (let i = 0; i < e.target.value; i++) {
                    allColumns.push({
                      name: 'Column ' + (i + 1),
                      value: 'text',
                    });
                  }
                  setTableConfig((prev) => ({
                    ...prev,
                    columns: allColumns,
                  }));
                }}
              />
              <span className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400">
                max 10
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Number of Rows
            </label>
            <Input
              className="w-full"
              value={tableConfig?.rows}
              onChange={(e) =>
                setTableConfig((prev) => ({
                  ...prev,
                  rows: e.target.value,
                }))
              }
            />
          </div>

          <div className="flex items-end">
            <Button
              type="primary"
              className="flex items-center gap-2"
              onClick={() => setPreviewTableModal(true)}
              icon={<TableIcon className="w-4 h-4" />}
            >
              Preview Table
            </Button>
          </div>
        </div>
      </div>

      <Modal
        title={
          <div className="flex items-center gap-2">
            <TableIcon className="w-5 h-5" />
            <span>Table Preview</span>
          </div>
        }
        open={previewTableModal}
        onCancel={() => setPreviewTableModal(false)}
        onOk={() => handleOk()}
        width={1000}
        className="preview-modal"
      >
        <div className="mt-4 border rounded-lg overflow-auto h-[25rem]">
          {tableConfig?.columns?.length > 0 && (
            <Table className={'mb-44'}>
              <Table.Head>
                <Table.Th>Column Name</Table.Th>
                <Table.Th>Data Type</Table.Th>
              </Table.Head>
              <Table.Body>
                {tableConfig?.columns?.map((item, idx) => (
                  <Table.Row key={idx}>
                    <Table.Td>
                      <Input
                        value={item?.name}
                        placeholder="Enter column name"
                        className="w-full"
                        onChange={(e) => {
                          const hasSpecialCharacters = /[^a-zA-Z0-9\s]/.test(
                            e.target.value
                          );
                          if (hasSpecialCharacters) {
                            toast.error('Special characters are not allowed');
                            return;
                          }
                          setTableConfig((prev) => ({
                            ...prev,
                            columns: prev?.columns?.map((elem, i) => {
                              if (i === idx) {
                                return {
                                  ...elem,
                                  name: e.target.value,
                                };
                              }
                              return elem;
                            }),
                          }));
                        }}
                      />
                    </Table.Td>
                    <Table.Td>
                      <div className="flex gap-2 items-center">
                        <Select
                          value={item?.value}
                          className="w-full"
                          onChange={(e) => {
                            if (e.target.value === 'dropdown') {
                              setDropDownOptionModal(true);
                              setColumnIndex(idx);
                            }
                            if (e.target.value === 'department') {
                              const alreadySelected =
                                tableConfig?.columns?.find(
                                  (c) => c.value === 'department'
                                );
                              if (alreadySelected) {
                                toast.error(
                                  'Department column already Selected , First Deselect it'
                                );
                                return;
                              }
                              setDepartmentOptionsModal(true);
                              setColumnIndex(idx);
                            }
                            setTableConfig((prev) => ({
                              ...prev,
                              columns: prev?.columns?.map((elem, i) => {
                                if (i === idx) {
                                  return {
                                    ...elem,
                                    value: e.target.value,
                                  };
                                }
                                return elem;
                              }),
                            }));
                          }}
                          options={[
                            { label: 'Text', value: 'text' },
                            { label: 'Number', value: 'number' },
                            { label: 'Date', value: 'date' },
                            { label: 'Dropdown', value: 'dropdown' },
                            { label: 'Department', value: 'department' },
                          ]}
                        />
                        {item.value === 'dropdown' && (
                          <Button
                            type="default"
                            size="small"
                            className="flex items-center gap-1"
                            onClick={() => {
                              setDropDownOptionModal(true);
                              setDropDownOptions(item?.options || []);
                              setColumnIndex(idx);
                            }}
                            icon={<Settings className="w-4 h-4" />}
                          >
                            Options
                          </Button>
                        )}
                        {item.value === 'department' && (
                          <Button
                            type="default"
                            size="small"
                            className="flex items-center gap-1"
                            onClick={() => {
                              setDepartmentOptionsModal(true);
                              setDepartmentCheckedCols(item?.options || []);
                              setColumnIndex(idx);
                            }}
                            icon={<Settings className="w-4 h-4" />}
                          >
                            Options
                          </Button>
                        )}
                      </div>
                    </Table.Td>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          )}
        </div>
      </Modal>

      <DropDownOptions
        dropDownOptionModal={dropDownOptionModal}
        setDropDownOptionModal={setDropDownOptionModal}
        tableConfig={tableConfig}
        setTableConfig={setTableConfig}
        columnIndex={columnIndex}
        dropDownOptions={dropDownOptions}
        setDropDownOptions={setDropDownOptions}
      />

      <DepartmentOptions
        openModal={DepartmentOptionsModal}
        setOpenModal={setDepartmentOptionsModal}
        tableConfig={tableConfig}
        setTableConfig={setTableConfig}
        columnIndex={columnIndex}
        departmentCheckedCols={departmentCheckedCols}
        setDepartmentCheckedCols={setDepartmentCheckedCols}
      />
    </div>
  );
};

export default DepartmentTableConfig;
