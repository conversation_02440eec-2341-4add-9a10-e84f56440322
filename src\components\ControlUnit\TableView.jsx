import Select from '../global/components/Select';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import Card from './Card';
import Column from './Column';
// import Button from '../global/components/Button';
import { Button, Modal, Tag } from 'antd';
import { Fragment, useContext, useState } from 'react';
import { IoMdArrowDropleftCircle } from 'react-icons/io';
import { toast } from 'react-toastify';
import { getCreateInputs } from '../../helperFunction';
import { Store } from '../../store/Store';
import Tooltip from '../global/components/ToolTip';
import CuSidebar from './CuSidebar';

const TableView = ({
  selectedTabJobStatus = 'ongoing',
  cusForStatus,
  activeTab,
  isMobile,
  isLoadingPo,
  selectedWo,
  getWO,
  selectedData,
  setSelectedData,
  inhouseItems,
  subAssemItems,
  bomItms,
  setShowOutSrc,
  setIsOpenSidebar,
  setOpenRightSideBar,
  openRightSideBar,
  itemForJob,
  setItemForJob,
  setSideBarData,
  getPosWithoutPopulate,
  activeIdx,
  setActiveIdx,
}) => {
  const [showDashboard, setShowDashboard] = useState(false);
  const { defaults: { defaultParam = {} } = {} } = useContext(Store);
  const { projectDefaults = {} } = defaultParam;
  const { isSingleProcessJob = false } = projectDefaults;

  return (
    <div className="w-full  h-[72vh] pb-2">
      {!openRightSideBar && (
        <div className="w-full flex  h-[72vh] overflow-x-auto px-2">
          {!isLoadingPo ? (
            <>
              <div className="w-full h-[72vh] bg-white/75 rounded-lg">
                <Table>
                  <Table.Head>
                    <Table.Row></Table.Row>
                    <Table.Th>#</Table.Th>
                    <Table.Th>Job Name</Table.Th>
                    <Table.Th>Item</Table.Th>
                    <Table.Th className={'text-center'}>Status</Table.Th>
                    {selectedTabJobStatus !== 'completed' &&
                      isSingleProcessJob && (
                        <>
                          <Table.Th className={'text-center'}>
                            Select Batch
                          </Table.Th>
                          <Table.Th className={'text-center'}>Action</Table.Th>
                        </>
                      )}
                  </Table.Head>
                  <Table.Body>
                    {
                      <>
                        {getCreateInputs(selectedWo?.items)?.map((ci, idx) => {
                          // if (ci?.status !== selectedTabJobStatus) return null;
                          if (
                            selectedTabJobStatus === 'ongoing' &&
                            ci?.status === 'completed'
                          )
                            return null;

                          if (
                            selectedTabJobStatus === 'completed' &&
                            ci?.status !== 'completed'
                          )
                            return null;

                          // comment for now for calculation of unit sent, for multi procees table view it will view unit sent of selected one for all process

                          // let currCuproject = {};

                          // currCuproject = ci?.cuProjects?.find(
                          //   (cu) =>
                          //     cu?.flowId === selectedData?.flow?._id &&
                          //     +cu?.batchInfo?.batchNo ===
                          //       +selectedData?.batchInfo?.batchNo
                          // );

                          // console.log(currCuproject, 'currCuproject');

                          const itemExists = (items) =>
                            items?.some((elem) => elem?._id === ci?.bomItemId);

                          if (
                            (activeTab === 'subAssembly' &&
                              itemExists(inhouseItems)) ||
                            (activeTab === 'inhouse' &&
                              itemExists(subAssemItems))
                          ) {
                            return null;
                          }

                          const jobItem = bomItms?.find(
                            (bom) => bom?._id === ci?.bomItemId
                          );
                          const itemName =
                            jobItem?.itemId?.name || jobItem?.itemName;

                          const tableData = ci?.goalsTable?.[0]?.tableData;
                          return (
                            <Table.Row
                              key={ci._id}
                              onClick={() => {
                                if (!isSingleProcessJob) {
                                  setActiveIdx(idx);
                                  setOpenRightSideBar(true);
                                }
                              }}
                              className={
                                'hover:cursor-pointer hover:!bg-slate-200 hover:text-blue-400'
                              }
                            >
                              <Table.Td
                                className={
                                  'hover:cursor-pointer hover:!bg-slate-200 hover:text-blue-400'
                                }
                              >
                                {idx + 1}
                              </Table.Td>
                              <Table.Td
                                className={
                                  'hover:cursor-pointer hover:!bg-slate-200 hover:!text-blue-400'
                                }
                              >{`${ci.modelName}(${ci?.id})`}</Table.Td>
                              <Table.Td
                                className={
                                  'hover:cursor-pointer hover:!bg-slate-200 hover:!text-blue-400'
                                }
                              >
                                {/* {itemName} */}

                                <span className="max-w-[5ch] break-words overflow-wrap">
                                  {itemName?.length > 45 ? (
                                    <Tooltip
                                      text={itemName}
                                      maxWidth={'!max-w-[500px]'}
                                      minWidth={'!min-w-[250px]'}
                                    >
                                      {itemName?.slice(0, 45) + '...'}
                                    </Tooltip>
                                  ) : (
                                    itemName || '-'
                                  )}
                                </span>
                              </Table.Td>
                              <Table.Td
                                className={
                                  'text-center hover:cursor-pointer hover:!bg-slate-200 hover:text-blue-600 '
                                }
                              >
                                {
                                  <Tag
                                    color={`${ci?.status?.toLowerCase() === 'completed' ? 'green' : ci?.status?.toLowerCase() === 'ongoing' ? 'cyan' : 'red'}`}
                                    className="capitalize w-24 text-center"
                                  >
                                    {ci?.status}
                                  </Tag>
                                }
                              </Table.Td>
                              {selectedTabJobStatus !== 'completed' &&
                                isSingleProcessJob && (
                                  <>
                                    <Table.Td
                                      className={
                                        'flex items-center justify-center'
                                      }
                                    >
                                      <Select
                                        className="text-center !w-36  "
                                        placeholder="Select Batch"
                                        value={selectedData?.batchNo?.[ci._id]}
                                        options={tableData?.map((bt, i) => ({
                                          label: bt.batchName,
                                          value: i + 1,
                                        }))}
                                        onChange={(e) =>
                                          setSelectedData((prev) => ({
                                            ...prev,
                                            model: ci,
                                            productionFlow: ci?.productionFlow,
                                            batchNo: {
                                              ...(prev?.batchNo || {}),
                                              [ci._id]: e.target.value,
                                            },
                                          }))
                                        }
                                      />
                                    </Table.Td>
                                    <Table.Td className={'text-center'}>
                                      <Button
                                        classNames={
                                          'text-center flex items-center justify-center  '
                                        }
                                        color={
                                          ci?.status === 'ongoing'
                                            ? 'green'
                                            : 'danger'
                                        }
                                        onClick={() => {
                                          if (
                                            !selectedData?.batchNo?.[ci._id]
                                          ) {
                                            toast.warn(
                                              'Please select batch first',
                                              {
                                                toastId: 'warn',
                                              }
                                            );
                                            return;
                                          }

                                          if (
                                            ci?.productionFlow?.processes
                                              ?.length > 1
                                          ) {
                                            toast.warn(
                                              'This job has multiple processes, Please switch to Multiprocess mode',
                                              {
                                                toastId: 'warn',
                                              }
                                            );
                                            return;
                                          }

                                          let flow =
                                            ci?.productionFlow?.processes?.[0];
                                          setItemForJob(jobItem);
                                          setSelectedData((prev) => ({
                                            ...prev,
                                            model: ci,
                                            flow: flow,
                                            productionFlow: ci?.productionFlow,
                                            batchInfo: ci?.goalsTable
                                              ?.find(
                                                (gt) => gt.flowId === flow._id
                                              )
                                              ?.tableData?.find(
                                                (d) =>
                                                  d.batchNo ===
                                                  prev?.batchNo?.[ci._id]
                                              ),
                                          }));
                                          setShowDashboard(true);
                                          setOpenRightSideBar(true);
                                          setActiveIdx(() => idx);
                                        }}
                                      >
                                        {tableData?.[
                                          selectedData?.batchNo?.[ci._id] - 1
                                        ]?.status === 'start'
                                          ? 'View'
                                          : 'Start'}
                                      </Button>
                                    </Table.Td>
                                  </>
                                )}
                            </Table.Row>
                          );
                        })}
                      </>
                    }
                  </Table.Body>
                </Table>
              </div>
            </>
          ) : (
            <Spinner />
          )}
        </div>
      )}

      {/* show only when some job is selected  */}
      {openRightSideBar && (
        <section className="w-full">
          {!isSingleProcessJob ? (
            <>
              {!showDashboard && (
                <section className="flex">
                  <section className={`${isMobile ? 'w-full' : 'w-1/2'}`}>
                    <>
                      {getCreateInputs(selectedWo?.items).map((ci, idx) => {
                        if (activeIdx !== idx) {
                          return null;
                        }
                        if (activeTab === 'subAssembly') {
                          if (
                            inhouseItems?.find(
                              (elem) => elem?._id === ci?.bomItemId
                            )
                          ) {
                            return null;
                          }
                        } else if (activeTab === 'inhouse') {
                          if (
                            subAssemItems?.find(
                              (elem) => elem?._id === ci?.bomItemId
                            )
                          ) {
                            return null;
                          }
                        }

                        const jobItem = bomItms?.find(
                          (bom) =>
                            bom?._id === ci?.bomItemId ||
                            bom?.manualEntry === ci?.assemblyManualEntry
                        );

                        const itemName =
                          jobItem?.itemId?.name || jobItem?.itemName;

                        const tableData = ci?.goalsTable?.[0]?.tableData;

                        return (
                          <>
                            <div className="block md:hidden">
                              <Select
                                placeholder="Select Batch"
                                value={selectedData?.batchNo?.[ci._id]}
                                options={tableData?.map((bt, i) => ({
                                  label: bt.batchName,
                                  value: i + 1,
                                }))}
                                onChange={(e) =>
                                  setSelectedData((prev) => ({
                                    ...prev,
                                    batchNo: {
                                      ...(prev?.batchNo || {}),
                                      [ci._id]: e.target.value,
                                    },
                                  }))
                                }
                              />
                            </div>
                            <div className="min-w-[30%] md:px-2 flex flex-col md:flex-row gap-x-3 ">
                              <Column
                                key={ci._id}
                                title={`${ci.modelName}(${ci?.id})`}
                                selectedData={selectedData}
                                tableData={tableData}
                                setSelectedData={setSelectedData}
                                mediaIds={ci?.imageURLs?.project}
                                index={idx}
                                itemId={ci?.bomItemId}
                                item={jobItem}
                                setShowOutSrc={setShowOutSrc}
                                ci={ci}
                                jobItem={itemName}
                                selectedWo={selectedWo}
                                setIsOpenSidebar={setIsOpenSidebar}
                                setSideBarData={setSideBarData}
                                getWO={getWO}
                              >
                                <Fragment>
                                  <Select
                                    className="hidden md:block w-full  sticky top-0"
                                    menuPlacement="bottom"
                                    // disabled={ci?.isForceStopped}
                                    placeholder="Select Batch"
                                    value={selectedData?.batchNo?.[ci._id]}
                                    options={tableData?.map((bt, i) => ({
                                      label: bt.batchName,
                                      value: i + 1,
                                    }))}
                                    onChange={(e) =>
                                      setSelectedData((prev) => ({
                                        ...prev,
                                        model: ci,
                                        // flow: flow,
                                        productionFlow: ci?.productionFlow,
                                        batchNo: {
                                          ...(prev?.batchNo || {}),
                                          [ci._id]: e.target.value,
                                        },
                                      }))
                                    }
                                  />
                                  <div className="overflow-y-scroll w-full">
                                    {ci?.productionFlow?.processes
                                      ?.slice(
                                        0,
                                        activeIdx === idx
                                          ? ci?.productionFlow?.processes
                                              ?.length
                                          : 10
                                      )
                                      ?.map((flow, i) => {
                                        const cuStatus = cusForStatus?.find(
                                          (cu) =>
                                            cu?.project === ci._id &&
                                            cu?.flowId === flow._id &&
                                            cu?.batchInfo?.batchNo ===
                                              selectedData?.batchNo?.[ci._id]
                                        );

                                        const jobFetchQuery = {
                                          query: {
                                            project: ci._id,
                                            batchNo:
                                              selectedData?.batchNo?.[ci._id],
                                            flowId:
                                              ci?.productionFlow?.processes[
                                                i - 1
                                              ]?._id || null,
                                            productionFlow:
                                              ci?.productionFlow?._id,
                                          },
                                        };

                                        return (
                                          <Card
                                            borderColors
                                            key={flow._id}
                                            processName={flow.processName}
                                            processCategory={
                                              flow?.processCategory
                                            }
                                            jobFetchQuery={
                                              i - 1 >= 0
                                                ? jobFetchQuery
                                                : 'NoPrevProcess'
                                            }
                                            cuStatus={cuStatus}
                                            onClick={() => {
                                              if (
                                                !selectedData?.batchNo?.[ci._id]
                                              ) {
                                                toast.warn(
                                                  'Please select batch first',
                                                  {
                                                    toastId: 'warn',
                                                  }
                                                );
                                                return;
                                              }

                                              setItemForJob(jobItem);
                                              setSelectedData((prev) => ({
                                                ...prev,
                                                model: ci,
                                                flow: flow,
                                                productionFlow:
                                                  ci?.productionFlow,
                                                batchInfo: ci?.goalsTable
                                                  ?.find(
                                                    (gt) =>
                                                      gt.flowId === flow._id
                                                  )
                                                  ?.tableData?.find(
                                                    (d) =>
                                                      d.batchNo ===
                                                      prev?.batchNo?.[ci._id]
                                                  ),
                                              }));
                                              setShowDashboard(true);
                                              setOpenRightSideBar(true);
                                              setActiveIdx(() => idx);
                                            }}
                                            elementid={idx}
                                          />
                                        );
                                      })}
                                  </div>
                                </Fragment>

                                {ci?.productionFlow?.processes?.length > 10 &&
                                  activeIdx !== idx && (
                                    <span
                                      onClick={() => setActiveIdx(idx)}
                                      className="text-[12px] px-[8px]  py-[3px] text-blue-600"
                                    >
                                      Load more...
                                    </span>
                                  )}
                              </Column>
                            </div>
                          </>
                        );
                      })}
                    </>
                  </section>
                  {!isMobile && (
                    <section className="w-1/2">
                      <div className=" flex justify-end mt-3 mr-3">
                        <IoMdArrowDropleftCircle
                          className="w-8 h-8 text-orange-600 cursor-pointer hover:text-orange-700 transition"
                          onClick={() => {
                            if (!isMobile) setOpenRightSideBar(false);
                            setShowDashboard(false);
                          }}
                        />
                      </div>
                    </section>
                  )}
                </section>
              )}
              <section>
                {showDashboard && openRightSideBar && (
                  <div
                    className={` flex  flex-col p-3 gap-y-3 w-full md:min-w-[70%] md:px-[14px] md:pt-5 md:pb-12  mb-2 rounded-md md:bg-[#f6f6f6] h-[72vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100`}
                  >
                    <div className=" flex justify-end">
                      <IoMdArrowDropleftCircle
                        className="w-8 h-8 text-orange-600 cursor-pointer hover:text-orange-700 transition"
                        onClick={() => {
                          if (!isMobile) setOpenRightSideBar(false);
                          setShowDashboard(false);
                        }}
                      />
                    </div>
                    <div className={`h-[71vh] `}>
                      <CuSidebar
                        itemForJob={itemForJob}
                        selectedData={selectedData}
                        getAllPo={getPosWithoutPopulate}
                        selectedWo={selectedWo}
                        inhouseItems={inhouseItems}
                      />
                    </div>
                  </div>
                )}
              </section>
            </>
          ) : (
            <>
              <Modal
                open={showDashboard}
                footer={null}
                centered={true}
                focusTriggerAfterClose={true}
                onCancel={() => {
                  setShowDashboard(false);
                  setOpenRightSideBar(false);
                }}
                width={isMobile ? '100%' : '70%'}
              >
                <div className="mt-7 p-3 border rounded-lg ">
                  <CuSidebar
                    itemForJob={itemForJob}
                    selectedData={selectedData}
                    getAllPo={getPosWithoutPopulate}
                    selectedWo={selectedWo}
                    inhouseItems={inhouseItems}
                  />
                </div>
              </Modal>
            </>
          )}
        </section>
      )}
    </div>
  );
};

export default TableView;
