import { useState, useEffect } from 'react';
import Select from '../../global/components/Select';
import { Button, Tabs } from 'antd';
import { CheckSquareOutlined, FormOutlined } from '@ant-design/icons';
import AddIcon from '../../../assets/images/add.png';
import CreateFormFullScreenModal from '../../global/components/CreateFormFullScreenModal';
import DynamicForm from './DynamicForm';
import ShortUniqueId from 'short-unique-id';

const JobQcFormTab = ({
  jobQCData,
  setJobQCData,
  allQCForms = [],
  isStarted,
  disableButtons,
  formExists,
  activeKey,
}) => {
  const [fName, setfName] = useState('');
  const [currForm, setCurrForm] = useState(null);
  const [saved, setSaved] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({});
  const [showRecording, setShowRecording] = useState(false);

  useEffect(() => {
    if (formExists?.formId?._id || formExists?.formId) {
      setSaved(true);
      setFormData(formExists?.refData || {});

      setCurrForm(
        allQCForms.find(
          (item) => item._id === (formExists?.formId?._id || formExists?.formId)
        )
      );
      setfName(formExists?.formId?._id || formExists?.formId);
    } else {
      setSaved(false);
      setFormData({});
      setCurrForm(null);
      setfName('');
    }
  }, [jobQCData, formExists, allQCForms, activeKey]);

  const handleSubmit = () => {
    setJobQCData((prev) => {
      return prev?.map((data) => {
        if (data?.uid === formExists?.uid) {
          return { ...data, formId: fName, refData: formData };
        }
        return data;
      });
    });

    setSaved(true);
    setIsOpen(false);
  };

  const changeHandler = (e) => {
    const form = allQCForms.find((item) => item._id === e.target.value);
    setCurrForm(form);
    setfName(e.target.value);
  };

  return (
    <div className="my-10">
      {isOpen && (
        <CreateFormFullScreenModal
          title={'Assign Form'}
          description={'Assign form to this project'}
          onClose={setIsOpen}
          onSubmit={handleSubmit}
          canSubmit={false}
        >
          <>
            <Select
              // disabled={disable}
              value={fName}
              onChange={(e) => changeHandler(e)}
              options={allQCForms?.map((option) => ({
                name: option.formName,
                value: option._id,
              }))}
              placeholder="Select Form"
            />
            {/* eslit-disable-next-line */}
            {currForm && (
              <DynamicForm
                isJobForm={true}
                formFields={currForm?.formData}
                onSubmit={handleSubmit}
                // processId={pro.mqtt._id}
                setSaved={setSaved}
                formData={formData}
                setFormData={setFormData}
                // isQrDisabled={qr !== pro._id && qr}
                // percent={percent}
                // setPercent={setPercent}
                // individualBatchSize={individualBatchSize}
                showRecording={showRecording}
                setShowRecording={setShowRecording}
              />
            )}
          </>
        </CreateFormFullScreenModal>
      )}

      <div className="w-full flex  justify-between px-10">
        <label className="block text-md mt-2 mb-1 text-black font-bold">
          Add form
        </label>

        <div className="flex">
          <Button
            type="button"
            disabled={disableButtons && (formExists?._id || isStarted)}
            onClick={() => setIsOpen((prev) => !prev)}
            className="bg-blue-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mt-2"
          >
            <img
              src={AddIcon}
              alt="Add Icon"
              className="w-5 h-5 object-contain relative"
            />
            {saved ? 'Edit' : 'Add'}
          </Button>
          {saved && (
            <p className="font-normal text-green-400 w-fit px-4 py-1 mt-3  ml-4">
              Saved
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

const JobQcFrom = ({ jobQCData, setJobQCData, allQCForms = [] }) => {
  const [activeKey, setActiveKey] = useState('');

  const uid = new ShortUniqueId();

  const editHandler = (targetKey, action) => {
    if (action === 'add') {
      let id = uid.rnd(10);
      setJobQCData((prev) => {
        return [
          ...prev,
          { formId: '', refData: {}, uid: id, isSubmitted: false },
        ];
      });
      setActiveKey(id);
    } else if (action === 'remove') {
      setJobQCData((prev) => {
        return prev.filter((item) => item.uid !== targetKey);
      });
    }
  };

  return (
    <div className="px-2 h-[30vh] border rounded-md no-scrollbar py-2 m-2">
      <Tabs
        style={{
          '.ant-tabs-tab.ant-tabs-tab-active': {
            backgroundColor: jobQCData.find((item) => item.uid === activeKey)
              ?.formId
              ? '#34C759'
              : '',
          },
        }}
        type="editable-card"
        moreIcon={<CheckSquareOutlined />}
        activeKey={activeKey}
        animated={true}
        centered={true}
        onChange={setActiveKey}
        items={Array.from({ length: jobQCData.length }).map((_, idx) => ({
          label: `Form ${idx + 1}`,
          key: jobQCData[idx]?.uid,

          icon: jobQCData[idx]?.formId ? (
            <CheckSquareOutlined style={{ color: '#34C759' }} />
          ) : (
            <FormOutlined />
          ),
          children: (
            <JobQcFormTab
              jobQCData={jobQCData}
              setJobQCData={setJobQCData}
              allQCForms={allQCForms}
              formExists={jobQCData?.find((itm) => itm?.uid === activeKey)}
              activeKey={activeKey}
            />
          ),
        }))}
        onEdit={editHandler}
      />
    </div>
  );
};

export default JobQcFrom;
