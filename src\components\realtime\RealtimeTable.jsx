import {
  CheckCircleFilled,
  CloseCircleFilled,
  HistoryOutlined,
  WarningOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { Badge, Button, Image, Input, Spin, Table, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import * as XLSX from 'xlsx';
import noPartImage from '../../assets/images/no_partImage.jpg';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice';
import {
  useExportRealtimeDataMutation,
  useGetRealTimePosMutation,
} from '../../slices/transactionsApiSlice';
import ExportButton from '../global/components/ExportButton';
import { FilterIcon, FilterV2 } from '../global/components/FilterV2';
import Select from '../global/components/Select';
import MisMatchedModal from './MisMatchedModal';
import PoInfoModal from './PoInfoModal';

const RealtimeTable = ({
  isLoading,
  setsearchTerm,
  totalLowStock,
  tableData,
  isMobile,
  setClickedRow,
  handlePart,
  setRightBar,
  setRightBarData,
  setIndentModal,
  setIndentData,
  data,
  allParts,
  realTimeData,
  setPrintType,
  setShowSelectableExport,
  globalUOM,
  setGlobalUOM,
  selectedTab,
  realtimeFilterOptions,
  setFilters,
  searchterm,
  showLowQuantity,
  setShowLowQuantity,
}) => {
  const navigate = useNavigate();
  const { data: dropdownsData } = useGetDropdownsQuery();
  const [getRealTimePos, { data: allPos, isLoading: isLoadingPos }] =
    useGetRealTimePosMutation();
  const [selectedItemId, setSelectedItemId] = useState('');
  const [openPOInfoModal, setOpenPOInfoModal] = useState(false);
  const [openMismatchModal, setOpenMismatchModal] = useState(false);
  const [exportRealtimeData, { isLoading: isCsvLoading }] =
    useExportRealtimeDataMutation();

  useEffect(() => {
    if (realTimeData && realTimeData?.length > 0) {
      getRealTimePos({ data: { ids: realTimeData?.map((i) => i?._id) } });
    }
  }, [realTimeData, getRealTimePos]);

  let filterConfig;
  if (selectedTab === 'part') {
    filterConfig = [
      {
        key: 'part',
        path: '_id',
        label: 'Part',
        type: 'multiSelect',
        isObjectId: true,
        options: realtimeFilterOptions?.part || [],
      },
      {
        key: 'quantity',
        path: 'quantity',
        label: 'Net Quantity',
        type: 'multiSelect',
        options: realtimeFilterOptions?.quantity || [],
      },
      {
        key: 'uom',
        path: 'uom',
        label: 'UOM',
        type: 'multiSelect',
        options: realtimeFilterOptions?.uom || [],
      },
      {
        key: 'category',
        path: 'category',
        label: 'Category',
        type: 'multiSelect',
        isObjectId: true,
        options: realtimeFilterOptions?.category || [],
      },
      {
        key: 'status',
        path: 'status',
        label: 'Status',
        type: 'multiSelect',
        options: realtimeFilterOptions?.status || [],
      },
      // {
      //   key: 'poExists',
      //   path: 'poExists',
      //   label: 'OutStanding PO',
      //   type: 'select',
      //   options: [
      //     {
      //       label: 'Exists',
      //       value: 'true',
      //     },
      //     {
      //       label: 'Not Exists',
      //       value: 'false',
      //     },
      //   ],
      // },
    ];
  } else if (selectedTab === 'product') {
    filterConfig = [
      {
        key: 'product',
        path: '_id',
        label: 'Product',
        type: 'multiSelect',
        isObjectId: true,
        options: realtimeFilterOptions?.product || [],
      },
      {
        key: 'quantity',
        path: 'quantity',
        label: 'Net Quantity',
        type: 'multiSelect',
        options: realtimeFilterOptions?.quantity || [],
      },
      {
        key: 'uom',
        path: 'uom',
        label: 'UOM',
        type: 'multiSelect',
        options: realtimeFilterOptions?.uom || [],
      },
      {
        key: 'category',
        path: 'category',
        label: 'Category',
        type: 'multiSelect',
        options: realtimeFilterOptions?.category || [],
      },
      {
        key: 'status',
        path: 'status',
        label: 'Status',
        type: 'multiSelect',
        options: realtimeFilterOptions?.status || [],
      },

      // {
      //   key: 'poExists',
      //   path: 'poExists',
      //   label: 'OutStanding PO',
      //   type: 'select',
      //   options: [
      //     {
      //       label: 'Exists',
      //       value: 'true',
      //     },
      //     {
      //       label: 'Not Exists',
      //       value: 'false',
      //     },
      //   ],
      // },
    ];
  }

  const handleRealtimeExport = async (isAll = true) => {
    const options = { type: selectedTab };
    if (!isAll) {
      options.ids = realTimeData.map((i) => i?._id);
    }
    const res = await exportRealtimeData({ data: options }).unwrap();

    const headers = ['Name', 'UOM', 'Quantity', 'Scrap', 'Category'];

    const ws = XLSX.utils.json_to_sheet(res, {
      header: ['name', 'uom', 'quantity', 'scrap', 'category'],
    });

    XLSX.utils.sheet_add_aoa(ws, [headers], { origin: 'A1' });
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, `Real Time - ${selectedTab}`);
    XLSX.writeFile(wb, `realTime-${selectedTab}.csv`, { bookType: 'csv' });
  };

  const getStockStatus = (quantity, threshold, othersThreshold) => {
    if (!othersThreshold?.length) {
      const isLow = quantity <= threshold;
      const outOfStock = quantity === 0;
      return {
        status: outOfStock ? 'Out of Stock' : isLow ? 'Low Stock' : 'In Stock',
        color: outOfStock || isLow ? '#d40000' : '#00d42e',
      };
    }

    const sortedThresholds = [
      { value: threshold, color: '#d40000', tag: 'Low Stock' },
      ...[...othersThreshold].sort((a, b) => a.value - b.value),
    ];
    for (let i = 0; i < sortedThresholds.length - 1; i++) {
      const current = sortedThresholds?.[i];
      const next = sortedThresholds?.[i + 1];
      if (quantity <= 0) {
        return { status: 'Out of Stock', color: '#d40000' };
      }
      if (quantity < current?.value) {
        return { status: current?.tag, color: current?.color };
      }
      if (quantity === current?.value) {
        return { status: current?.tag, color: current?.color };
      }

      if (quantity > current?.value && quantity <= next?.value) {
        return { status: next?.tag, color: next?.color };
      }
    }

    const lastThreshold = sortedThresholds?.[sortedThresholds?.length - 1];
    if (quantity > lastThreshold?.value) {
      return { status: 'In Stock', color: '#00d42e' };
    }
  };

  const StatusBadge = ({ status, color }) => {
    return (
      <Badge color={color} text={<span style={{ color }}>{status}</span>} />
    );
  };

  const getNameColumn = (name) => {
    if (!name) return '';

    return name.length <= 50 ? (
      name
    ) : (
      <Tooltip title={name}>{`${name.substring(0, 50)}...`}</Tooltip>
    );
  };
  // const getLowQuantityCount = () => {
  //   return tableData?.filter((record) => {
  //     const type = Object.values(record.batches)[0][0];
  //     const Quan =
  //       type?.part?.quantity ||
  //       type?.product?.quantity ||
  //       type?.partVariant?.quantity ||
  //       type?.subAssembly?.quantity ||
  //       type?.productVariant?.quantity;
  //     const ThresQuan =
  //       type?.part?.quantityThreshold ||
  //       type?.product?.quantityThreshold ||
  //       type?.partVariant?.part?.quantityThreshold ||
  //       type?.subAssembly?.quantityThreshold ||
  //       type?.productVariant?.product?.quantity;

  //     return Quan < ThresQuan;
  //   }).length;
  // };
  //
  //

  const poItemsArray =
    allPos?.flatMap((i) => i.items)?.map((i) => i.item?._id || i.item?.value) ||
    [];

  const columns = [
    {
      title: 'S.No',
      dataIndex: 'index',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Thumbnail',
      key: 'thumbnail',
      render: (_, record) => {
        if (record?.thumbNails?.[0]?.data) {
          return (
            <Image
              src={record.thumbNails[0].data}
              alt={record.thumbNails[0].name}
              width={40}
              height={40}
              className="object-cover rounded-lg cursor-pointer"
              preview={{
                mask: null,
              }}
            />
          );
        } else {
          return (
            <img
              src={noPartImage}
              alt="No thumbnail"
              className="w-10 h-10 object-cover rounded-lg bg-contain"
            />
          );
        }
      },
    },
    {
      title: 'Name',
      key: 'name',
      fixed: 'left',
      width: 400,
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (_, record) => {
        return (
          <span
            className="text-blue-600 cursor-pointer hover:text-blue-800"
            onClick={() => {
              if (isMobile) {
                setClickedRow(record);
              } else {
                handlePart(record?.name);
                setRightBar(true);
                setRightBarData({
                  title: record?.name,
                  itemId: record?._id,
                });
              }
            }}
          >
            {getNameColumn(record.name)}
          </span>
        );
      },
    },
    {
      title: 'Status',
      key: 'status',
      width: 200,
      render: (_, record) => {
        const Quan = Number(record?.quantity || 0);
        const ThresQuan = Number(record?.quantityThreshold || 0);
        const othersThreshold = record?.othersThreshold || [];
        const stockStatus = getStockStatus(Quan, ThresQuan, othersThreshold);
        const status = stockStatus?.status || '-';
        const color = stockStatus?.color || '0000FF';
        return <StatusBadge status={status} color={color} />;
      },
      // filters: [
      //   { text: 'In Stock', value: 'In Stock' },
      //   { text: 'Out of Stock', value: 'Out of Stock' },
      //   { text: 'Low Stock', value: 'Low Stock' },
      //   ...(Array.isArray(tableData)
      //     ? tableData
      //         .map((record) => record?.othersThreshold || [])
      //         .reduce((acc, threshold) => {
      //           if (threshold && !acc.some((f) => f.value === threshold.tag)) {
      //             acc.push({ text: threshold.tag, value: threshold.tag });
      //           }
      //           return acc;
      //         }, [])
      //     : []),
      // ],
      // onFilter: (value, record) => {
      //   if (!record?.batches) return false;
      //
      //   const batchValues = Object.values(record.batches);
      //   if (!batchValues.length || !batchValues[0]?.length) return false;
      //
      //   const Quan = Number(record?.quantity || 0);
      //   const ThresQuan = Number(record?.quantityThreshold || 0);
      //   const othersThreshold = record?.othersThreshold || [];
      //
      //   const { status } = getStockStatus(Quan, ThresQuan, othersThreshold);
      //   return value === status;
      // },
    },
    {
      title: 'Category',
      key: 'category',
      render: (_, record) => record?.category?.name || record?.category || '-',
      // filters: [
      //   ...new Set(realTimeData?.results?.map((item) => item?.category)),
      // ]
      //   .filter(Boolean)
      //   .map((name) => ({
      //     text: name,
      //     value: realTimeData?.results?.find((item) => item?.category === name)
      //       ?.category?._id,
      //   })),
      // onFilter: (value, record) => record?.category?._id === value,
    },
    {
      title: 'Net Quantity',
      key: 'quantity',
      render: (_, record) => {
        const additionalUoms = record?.additionalUoms || [];
        const conversion = additionalUoms?.find(
          (conversion) => conversion?.additionalUnit === globalUOM
        );
        let quantity = record.quantity;
        if (conversion && conversion.conversionValue) {
          quantity = (quantity / conversion.conversionValue).toFixed(2);
        }

        return quantity;
      },
      sorter: (a, b) => a?.quantity - b?.quantity,
    },
    {
      title: 'Scrap',
      key: 'scrap',
      render: (_, record) => record?.scrap || '-',
    },
    {
      title: 'UOM',
      key: 'uom',
      render: (_, record) => {
        const additionalUoms = record?.additionalUoms || [];
        const uom = record?.uom || '-';

        const conversion = additionalUoms?.find(
          (conversion) => conversion?.additionalUnit === globalUOM
        );

        return (
          <div className="flex flex-col">
            <span>{conversion ? conversion?.additionalUnit : uom}</span>
            {conversion && (
              <span className="text-gray-500 text-xs">
                {`${conversion.conversionValue}-${conversion.conversionUnit}`}
              </span>
            )}
          </div>
        );
      },
      // filters: Array.from(
      //   new Set(
      //     realTimeData?.results.map((record) => {
      //       const uom = record.uom;
      //       return uom ? [uom] : [];
      //     })
      //   )
      // ).map((uom) => ({
      //   text: uom,
      //   value: uom,
      // })),
      // onFilter: (value, record) => {
      //   const type = Object.values(record.batches)[0][0];
      //   const uom =
      //     type?.part?.uom ||
      //     type?.product?.uom ||
      //     type?.partVariant?.uom ||
      //     type?.productVariant?.uom;
      //   return uom === value;
      // },
    },
    {
      title: 'Batches',
      key: 'batches',
      render: (_, record) => (
        <span
          className="text-blue-600 cursor-pointer hover:text-blue-800"
          onClick={() => {
            setRightBar(true);
            setRightBarData({
              itemId: record._id,
              title: record?.name,
            });
          }}
        >
          View Batches
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => {
        return (
          <Button
            type="primary"
            // disabled={Quan <= ThresQuan ? false : !Quan ? false : true}
            onClick={() => {
              setIndentModal(record);
              const vendorName = allParts
                .filter((item) => record?.name === item?.name)
                .map((item) => item?.vendors?.[0]?.name);
              setIndentData((prev) => ({
                ...prev,
                indent_no: data?.results[0]?.indent_no + 1,
                products: [
                  {
                    ...prev.products[0],
                    name: record?.name,
                    uom: record?.uom || '',
                    quantity: record?.quantity,
                    vendor: vendorName[0],
                  },
                ],
              }));
            }}
          >
            + Create Indent
          </Button>
        );
      },
    },
    {
      title: 'Outstanding PO',
      key: 'outstanding_po',
      render: (_, record) => {
        const poExists = poItemsArray.includes(record._id);

        return (
          <Tooltip
            title={poExists ? 'View Purchase Orders' : 'No Purchase Orders'}
          >
            <Button
              className={isLoadingPos ? 'animate-pulse' : ''}
              icon={
                isLoadingPos ? (
                  <LoadingOutlined className="animate-spin text-blue-600" />
                ) : poExists ? (
                  <CheckCircleFilled style={{ color: '#52c41a' }} />
                ) : (
                  <CloseCircleFilled style={{ color: '#bfbfbf' }} />
                )
              }
              onClick={() => {
                if (poExists) {
                  setSelectedItemId(record?._id);
                  setOpenPOInfoModal(true);
                }
              }}
              disabled={!poExists}
            />
          </Tooltip>
        );
      },
    },
    {
      title: 'History',
      key: 'history',
      render: (_, record) => {
        const itemType = record.itemType;

        return (
          <Tooltip title="History">
            <HistoryOutlined
              className="text-gray-600 cursor-pointer hover:text-blue-600"
              onClick={() =>
                navigate(
                  `/inventory/transactions?type=${itemType}&id=${record?._id}`
                )
              }
            />
          </Tooltip>
        );
      },
    },
  ];
  const [showFilters, setShowFilters] = useState(true);
  return (
    <div className="">
      {selectedItemId && openPOInfoModal && (
        <PoInfoModal
          itemId={selectedItemId}
          openModal={openPOInfoModal}
          setOpenModal={setOpenPOInfoModal}
        />
      )}

      {openMismatchModal && (
        <MisMatchedModal
          openModal={openMismatchModal}
          setOpenModal={setOpenMismatchModal}
        />
      )}
      <div className="bg-white rounded-lg shadow-sm p-6">
        {/* Search and Controls Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          {/* Search Bar */}
          <div className="w-full md:w-auto">
            <Input.Search
              onChange={(e) => setsearchTerm(e.target.value)}
              placeholder="Search by Name, Category, or UOM"
              value={searchterm}
            />
          </div>
          {!isMobile && (
            <div className="flex items-center gap-3 w-full md:w-auto">
              <FilterIcon
                showFilters={showFilters}
                setShowFilters={setShowFilters}
              />
              <Select
                className="min-w-[10rem]"
                placeholder="Select UOM"
                value={globalUOM}
                onChange={(e) => setGlobalUOM(e.target.value)}
                options={[
                  { value: null, label: 'Not Applicable' },
                  ...(dropdownsData?.dropdowns
                    ?.find((e) => e.name === 'uom')
                    ?.values?.map((e) => ({
                      value: e,
                      label: e,
                    })) || []),
                ]}
                styles={{
                  control: (base) => ({
                    ...base,
                    fontSize: '14px',
                    minHeight: '33px',
                    height: '22px',
                  }),
                }}
              />

              {/* Low Quantity Toggle */}
              <Button
                type={showLowQuantity ? 'primary' : 'default'}
                icon={<WarningOutlined />}
                onClick={() => {
                  setShowLowQuantity(!showLowQuantity);
                }}
                className="flex items-center hover:scale-105 transition-transform"
              >
                {showLowQuantity ? 'Show All' : 'Low Quantity'}
                <Badge
                  count={totalLowStock || 'NA'}
                  className="ml-2"
                  style={{
                    backgroundColor: showLowQuantity ? '#fff' : '#ff4d4f',
                    color: showLowQuantity ? '#1890ff' : '#fff',
                  }}
                />
              </Button>

              {/* Export Button */}
              <ExportButton
                csvClick={() => handleRealtimeExport(false)}
                setPrintType={setPrintType}
                setShowSelectableExport={setShowSelectableExport}
                className="!h-9 !px-4"
                fetchAndExport={handleRealtimeExport}
                isCsvLoading={isCsvLoading}
                hidePdf={true}
              />

              <Button type="primary" onClick={() => setOpenMismatchModal(true)}>
                <span className="text-sm font-semibold">MisMatch</span>
              </Button>
            </div>
          )}
        </div>

        <FilterV2
          config={filterConfig}
          showFilters={showFilters}
          setFilters={setFilters}
        />

        {/* Table */}
        <Table
          columns={columns}
          dataSource={tableData}
          loading={{
            indicator: <Spin size="large" />,
            spinning: isLoading,
          }}
          pagination={false}
          rowKey={(record) => record._id}
          scroll={{ x: true }}
          className="border border-gray-200 rounded-lg overflow-hidden"
        />
      </div>
    </div>
  );
};

export default RealtimeTable;
