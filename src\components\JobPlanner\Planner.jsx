import { LeftOutlined } from '@ant-design/icons';
import { But<PERSON> } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { padZero } from '../../helperFunction';
import { useLazyGetCreateInputByIDQuery } from '../../slices/createInputApiSlice';
import { useEditManyGoalsTableMutation } from '../../slices/goalsTableapiSlice';
import { useGetAllMachineScheduleByCiQuery } from '../../slices/jobScheduleApiSlice';
import { useGetAllLocationsQuery } from '../../slices/locationApiSlice';
import { useGetMachinesByMqttMutation } from '../../slices/machineApiSlice';
import { useGetShiftsForMachinesMutation } from '../../slices/machineShiftApiSlice';
import { useGetAllUserByProcessAccessMutation } from '../../slices/userApiSlice';
import Header from '../global/components/Header';
import { InfoTooltip } from '../global/components/InfoTooltip';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import MachineSelection from './MachineSelection';
import PlannerGoalsTable from './PlannerGoalsTable';

const Planner = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [goalsTable, setGoalsTable] = useState([]);
  const [machineSchedules, setMachineSchedules] = useState([]);
  const [planningStatus, setPlanningStatus] = useState('unScheduled');
  const [step, setStep] = useState(0);

  const [getCreateInputByID, { isFetching: isFetchingGet, data: createInput }] =
    useLazyGetCreateInputByIDQuery();

  const [editManyGoalsTable, { isLoading: isLoadingEdit }] =
    useEditManyGoalsTableMutation();

  const { productionFlow, jobPlanningDetails = {} } = createInput || {};
  const { isSequential } = jobPlanningDetails;

  const [getMachinesByMqtt, { data: allMachines }] =
    useGetMachinesByMqttMutation();

  const [getShiftsForMachines, { data: customMachineTimes }] =
    useGetShiftsForMachinesMutation();

  const { data: machineSchedulesForJob } = useGetAllMachineScheduleByCiQuery(
    { id },
    { skip: !id, refetchOnMountOrArgChange: true }
  );

  const { data: allLocations } = useGetAllLocationsQuery();

  const [getAllUserByProcessAccess, { data: usersForAssigning = [] }] =
    useGetAllUserByProcessAccessMutation();

  useEffect(() => {
    setMachineSchedules(machineSchedulesForJob || []);
  }, [machineSchedulesForJob]);

  useEffect(() => {
    const uniqueIds = new Set();

    const ids = productionFlow?.processes
      ?.filter((pro) => {
        uniqueIds.add(pro?.mqtt?._id);
        return pro?.processCategory === 'Inhouse';
      })
      ?.map((i) => i?.mqtt?._id || i?.mqtt);

    getAllUserByProcessAccess({ ids: [...uniqueIds] });

    getMachinesByMqtt({ data: { ids } })
      .unwrap()
      .then((res) => {
        if (res?.length > 0) {
          const macIds = res.map((i) => i._id);

          getShiftsForMachines({ data: { macIds } });
        }
      });
  }, [
    getMachinesByMqtt,
    productionFlow,
    getShiftsForMachines,
    getAllUserByProcessAccess,
  ]);

  useEffect(() => {
    if (id)
      getCreateInputByID({ id })
        .unwrap()
        .then((res) => setGoalsTable(res.goalsTable));
  }, [id, getCreateInputByID]);

  useEffect(() => {
    if (goalsTable) {
      let tempArr = [];
      goalsTable?.forEach((gt) => {
        gt.tableData?.forEach((tdt) => {
          tempArr.push(tdt?.startDate && tdt?.stopDate ? 'p' : 'u');
        });
      });

      const isUnPlanned = tempArr.every((i) => i === 'u');
      const isPlanned = tempArr.every((i) => i === 'p');

      setPlanningStatus(
        isUnPlanned ? 'unScheduled' : isPlanned ? 'scheduled' : 'partial'
      );
    }
  }, [goalsTable]);

  const editHandler = () => {
    if (step === 0) {
      setStep(1);
      return;
    }

    const filtered = machineSchedules
      ?.filter((i) =>
        i?.isRemoved
          ? true
          : i?.start && i?.stop && (i?._id ? i?.isEdited : true)
      )
      .map((i) => {
        const [hh = 0, mm = 0] = i.hour?.split(':');
        return { ...i, hour: `${padZero(+hh)}:${padZero(+mm)}` };
      });

    editManyGoalsTable({
      data: {
        goalsTable,
        planningStatus,
        createInputId: id,
        machineSchedules: filtered,
      },
    })
      .unwrap()
      .then(() => {
        toast.success('Job planning sucessful', { toastId: 'sucess' });
        navigate('/jobs/jobplanner');
      });
  };

  const allBatches =
    createInput?.goalsTable?.[0]?.tableData?.map((_i, idx) => idx) || [];

  return (
    <div className="">
      <div className="px-2 sm:px-4 lg:px-6 pb-16">
        <Header
          title="Planner"
          description="Schedule your jobs here"
          hasInfoPopup={false}
        />
        <Button
          onClick={() => navigate(-1)}
          type="primary"
          icon={<LeftOutlined />}
        >
          Back
        </Button>
      </div>

      {/* Progress Steps */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <nav className="flex items-center justify-center" aria-label="Progress">
          <ol className="flex items-center space-x-5">
            {['Machines', 'Planning'].map((page, index) => (
              <li key={page} className="flex items-center">
                <button
                  onClick={() => setStep(index)}
                  className={`${
                    index <= step
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-500'
                  } rounded-full h-8 w-8 flex items-center justify-center text-sm font-medium`}
                >
                  {index + 1}
                </button>
                <span
                  className={`ml-4 text-sm font-medium ${
                    index <= step ? 'text-blue-500' : 'text-gray-500'
                  }`}
                >
                  {page}
                </span>
                {index < 1 && <div className="ml-4 h-0.5 w-16 bg-gray-200" />}
              </li>
            ))}
          </ol>
        </nav>
      </div>

      {/* Main Content */}
      <div className="px-2 sm:px-4 lg:px-6 pb-16">
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">
              {step === 0 ? 'Select Machines' : 'Batch Planning'}
            </h2>
            <div className="flex space-x-4">
              {step > 0 && (
                <Button
                  onClick={() => setStep(0)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Back
                </Button>
              )}
              <Button
                isLoading={isLoadingEdit}
                onClick={() => editHandler()}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
              >
                {step === 0 ? 'Save and Proceed' : 'Submit'}
              </Button>
            </div>
          </div>

          <div className="px-6 py-4">
            {isFetchingGet ? (
              <div className="flex justify-center py-12">
                <Spinner className="h-8 w-8 text-blue-600" />
              </div>
            ) : (
              <div className="overflow-x-auto">
                {step === 0 ? (
                  <MachineSelection
                    productionFlow={productionFlow}
                    goalsTable={goalsTable}
                    setGoalsTable={setGoalsTable}
                    allMachines={allMachines}
                    machineSchedules={machineSchedules}
                  />
                ) : (
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        <Table.Th>
                          <div className="flex items-center gap-x-3">
                            <p>Batch</p>
                            <InfoTooltip
                              position="right"
                              width="400px"
                              id="downtimeType"
                              isHtml={true}
                              content="Click on batch number to toggle advance table"
                            />
                          </div>
                        </Table.Th>
                        <Table.Th>
                          <div className="flex items-center gap-x-3">
                            <p>Process/Name</p>
                            <InfoTooltip
                              position="right"
                              width="400px"
                              id="downtimeType"
                              isHtml={true}
                              content="Click on process name to toggle machines table"
                            />
                          </div>
                        </Table.Th>
                        <Table.Th>Batch&nbsp;Size/Target</Table.Th>
                        <Table.Th>New&nbsp;Batch&nbsp;Size</Table.Th>
                        <Table.Th>Sub&nbsp;Process</Table.Th>
                        <Table.Th>Speed</Table.Th>
                        <Table.Th>Time&nbsp;(HH:MM)</Table.Th>
                        <Table.Th>Buffer</Table.Th>
                        <Table.Th>Start&nbsp;Date</Table.Th>
                        <Table.Th>Stop&nbsp;Date</Table.Th>
                        <Table.Th>User</Table.Th>
                        <Table.Th>Duration</Table.Th>
                        <Table.Th>Location</Table.Th>
                        <Table.Th>Action</Table.Th>
                        {/* <Table.Th>Start&nbsp;Date</Table.Th>
                      <Table.Th>Stop&nbsp;Date</Table.Th> */}
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {allBatches?.map((batch) => {
                        const batchData = goalsTable?.map((gt) => {
                          return { ...gt?.tableData?.[batch] };
                        });
                        return (
                          <PlannerGoalsTable
                            key={batch}
                            batchData={batchData}
                            productionFlow={productionFlow}
                            goalsTable={goalsTable}
                            setGoalsTable={setGoalsTable}
                            machineSchedules={machineSchedules}
                            setMachineSchedules={setMachineSchedules}
                            allMachines={allMachines}
                            isSequential={isSequential}
                            customMachineTimes={customMachineTimes}
                            allLocations={allLocations}
                            usersForAssigning={usersForAssigning}
                          />
                        );
                      })}
                    </Table.Body>
                  </Table>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Planner;
