import { useEffect, useRef, useState } from 'react';
import { findLargestLengthItem } from '../../helperFunction';

function DropDownButton({ allFilters }) {
  const [selectedFilter, setSelectedFilter] = useState(allFilters[0].value);
  // console.log(allFilters[0].name);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownref = useRef('');
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownref.current && !dropdownref.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };
  return (
    <div className="flex items-center z-10" ref={dropdownref}>
      <div>
        <button
          type="button"
          className="flex justify-center items-center gap-2 rounded-md shadow-sm px-4 py-2 bg-[#0070ff] text-sm font-medium text-white  hover:brightness-125 hover:contrast-125  focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          id="filter"
          onClick={toggleDropdown}
          style={{
            width: `${findLargestLengthItem(allFilters)?.name?.length * 15}px`,
          }}
        >
          <span className={isOpen ? 'rotate-90' : '-rotate-90'}>&lt;</span>
          {allFilters.find((el) => el.value === selectedFilter)?.name}
        </button>
      </div>

      {isOpen && (
        <div className="origin-top-right absolute right-0 mt-2 w-30 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
          <div
            className="py-1 text-left-0"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="dropdown-button"
          >
            {allFilters.map((el, index) => {
              return (
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-[#0070ff] hover:bg-gray-100 hover:text-gray-900"
                  role="menuitem"
                  key={index}
                  onClick={() => {
                    setSelectedFilter(el.value);
                    setIsOpen(false);
                  }}
                >
                  {el?.name}
                </a>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

export default DropDownButton;
