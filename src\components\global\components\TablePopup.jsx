import Button from './Button';

export default function TablePopup({
  onBack,
  children,
  onEdit,
  onDownload,
  isEdit = true,
  isDownload = true,
  downloading,
}) {
  const isButtonDivVisible = isEdit || isDownload;

  return (
    <>
      <div className="fixed top-0 bottom-0 left-0 bg-white w-screen h-screen z-50">
        <div className="p-4">
          <Button
            textColor="text-gray-600"
            className="bg-white border-2 text-gray-600 !text-sm !h-10"
            onClick={onBack}
          >
            Back
          </Button>
        </div>
        <div className="flex flex-col h-full">
          <div className="w-full h-[82%] overflow-y-auto px-4 py-2">
            {children}
          </div>
          {isButtonDivVisible && (
            <div className="bg-gray-50 px-4 py-2 flex gap-2 w-full">
              {isEdit && (
                <Button type="button" onClick={onEdit} className="w-full">
                  Edit
                </Button>
              )}
              {isDownload && (
                <Button
                  isLoading={downloading}
                  type="button"
                  onClick={onDownload}
                  className="w-full"
                >
                  Download
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
