import { Dropdown } from 'antd';
import { useEffect, useState } from 'react';
import { IoMdAdd, IoMdRefresh } from 'react-icons/io';
import { toast } from 'react-toastify';
import { PREFIX_ID_LABELS } from '../../../utils/Constant';
import { customConfirm } from '../../../utils/customConfirm';
import initializeWithOptions from '../Prefix/utils/initializeWithOptions';
import RenderFields from './RenderPrefixIdFields';

const HAS_SINGLE_IDS = ['itemsPerHour', 'changeOverTime'];

function PrefixIdFormat({ idType, format, setFormat, setEditData }) {
  const [options, setOptions] = useState([]);

  const hasMultipleIds = !HAS_SINGLE_IDS?.includes(idType);

  useEffect(() => {
    initializeWithOptions(idType, setOptions);
  }, [idType]);

  const handleAddType = (opt, idIndex, item) => {
    const keys = Object.keys(item);

    let val = '';

    if (opt === 'Increment') val = 1;
    else if (opt === 'Dropdown') val = [];

    const newVal = keys?.length - (keys?.includes('isUsed') ? 1 : 0) || 0;
    const key = `${opt}_${newVal}`;

    setFormat((prev) => {
      const data = prev?.[idType]?.map((i, idx) => {
        if (idx === idIndex) {
          const isUsed = i?.isUsed || false;
          delete i.isUsed;
          return { ...i, [key]: val, isUsed };
        }
        return i;
      });
      setEditData((prev) => ({ ...(prev || {}), [idType]: data }));
      return {
        ...prev,
        [idType]: data,
      };
    });
  };

  const handleReset = async (item, idIndex) => {
    if (item?.isUsed) {
      toast.error('Cannot reset id once used in template');
      return;
    }

    const confirm = await customConfirm(
      `Are you certain you want to reset the ID for "${PREFIX_ID_LABELS?.[idType]}"? If it has already been used, this could lead to conflicts when creating the document.`,
      'delete'
    );

    if (!confirm) return;

    setFormat((prev) => {
      const data = { ...prev };
      data[idType][idIndex] = {};
      setEditData((prev) => ({ ...(prev || {}), [idType]: data?.[idType] }));
      return data;
    });
  };

  return (
    <div className="mb-10 mt-6 border-b pb-5 p-2 overflow-x-auto grid gap-2 lg:grid-cols-6 hover:bg-gray-100/50">
      <div className="self-start flex items-center gap-2 mb-1">
        <p className="flex gap-5">{PREFIX_ID_LABELS[idType]}:</p>
        {hasMultipleIds && (
          <button
            className="flex aspect-square bg-blue-primary rounded-full p-1.5 items-center justify-center text-white cursor-pointer hover:brightness-125"
            onClick={() =>
              setFormat((prev) => {
                const data = [...(prev?.[idType] || []), {}];
                setEditData((prev) => ({ ...(prev || {}), [idType]: data }));
                return { ...prev, [idType]: data };
              })
            }
            // className="flex h-[20px] aspect-square bg-blue-primary rounded-full items-center justify-center text-xl text-white"
          >
            <IoMdAdd />
          </button>
        )}
      </div>
      <div className="col-span-5">
        {format?.map((item, idIndex) => (
          <div key={idIndex} className="flex items-center gap-3 mb-2">
            {Object?.entries(item)?.map(([type, tVal]) => {
              if (type === 'isUsed') return null;
              return (
                <RenderFields
                  key={type}
                  type={type}
                  tVal={tVal}
                  idType={idType}
                  idIndex={idIndex}
                  setFormat={setFormat}
                  options={options}
                  setEditData={setEditData}
                  hasMultipleIds={hasMultipleIds}
                />
              );
            })}
            {hasMultipleIds && (
              <>
                <Dropdown
                  trigger={['click']}
                  menu={{
                    items: options?.map((opt) => ({
                      label: (
                        <p onClick={() => handleAddType(opt, idIndex, item)}>
                          {opt}
                        </p>
                      ),
                      key: opt,
                    })),
                  }}
                  className="rounded-full"
                >
                  <span className="flex aspect-square bg-blue-primary p-1.5 items-center justify-center text-white cursor-pointer rounder-full hover:brightness-125">
                    <IoMdAdd />
                  </span>
                </Dropdown>
                <button
                  className="flex aspect-square bg-gray-200 p-1.5 items-center justify-center cursor-pointer rounded-full hover:brightness-75"
                  onClick={() => handleReset(item, idIndex)}
                >
                  <IoMdRefresh />
                </button>
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

export default PrefixIdFormat;
