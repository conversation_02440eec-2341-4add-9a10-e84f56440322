import {
  CategoryScale,
  Chart as ChartJS,
  Colors,
  LineElement,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js';
import { useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { useLazyGetDeviceDataByMachineWithTypeQuery } from '../../slices/deviceDataApiSlice';
import { useLazyGetMachineByIdQuery } from '../../slices/machineApiSlice';

ChartJS.register(
  Colors,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip
);

const CreateAnaGraph = ({ mid }) => {
  const [getMachineById, { data: macData = {} }] = useLazyGetMachineByIdQuery();
  const { machine = {} } = macData;
  const { machineId: macId } = machine;

  const [getDeviceDataByMachineWithType, { data: divData = {} }] =
    useLazyGetDeviceDataByMachineWithTypeQuery();
  const { deviceDatas: deviceData = [] } = divData;

  useEffect(() => {
    if (mid) {
      getMachineById({ id: mid }, false).unwrap();
      getDeviceDataByMachineWithType({ id: mid }, false).unwrap();
    }
  }, [mid, getMachineById, getDeviceDataByMachineWithType]);

  var labelArray = [];
  var dataArray = [];

  deviceData.forEach((item) => {
    dataArray.push(item.data.COUNT);

    var temp = item.data.displayDate;
    var ar = temp.split(', ');
    labelArray.push(ar[1]);
  });

  const labels = labelArray;

  const data = {
    labels,
    datasets: [
      {
        label: macId,
        data: dataArray,
      },
    ],
  };

  const options = {
    responsive: true,
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          display: false,
        },
      },
    },
  };

  return <Line options={options} data={data} />;
};

export default CreateAnaGraph;
