import {
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { useContext, useEffect, useState } from 'react';
import {
  useManageHolidayMutation,
  useManageShiftMutation,
} from '../../slices/defaultsApiSlice';
import { Store } from '../../store/Store';
import Button from '../global/components/Button';
import Input from '../global/components/Input';
import Select from '../global/components/Select';

const DatePopup = ({
  fullDate,
  isTodayDate,
  isHoliday,
  isWeekHoliday,
  date,
  handleDaysHoliday,
  handleCustomShift,
  customShift,
}) => {
  const [showPopup, setShowPopup] = useState(false);
  const [shiftData, setShiftData] = useState({
    start: '',
    stop: '',
    date: fullDate,
  });

  useEffect(() => {
    if (customShift) {
      setShiftData({ start: '', stop: '', date: fullDate, ...customShift });
    }
  }, [customShift, fullDate]);

  const inputHandler = (e) =>
    setShiftData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));

  return (
    <div
      className={`text-center hover:cursor-pointer font-medium hover:bg-blue-light border-[1px] border-gray-100
       ${
         isHoliday
           ? 'bg-[#ffb3af]'
           : customShift?._id
             ? 'text-white bg-orange-primary'
             : ''
       }`}
    >
      <p
        className={`w-full py-3 ${isTodayDate ? 'border-2 border-black' : ''}`}
        onClick={() => setShowPopup(true)}
      >
        {date.getDate()}
      </p>
      {showPopup && (
        <div className="fixed z-[110] w-screen h-screen top-0 left-0 bg-black/10 flex justify-center items-center text-black">
          <div className=" w-2/5 min-w-[250px] bg-white rounded-2xl">
            <section className="px-8 py-2 border-b flex justify-between items-center">
              <h4>{fullDate}</h4>
              {/* <Button className="w-1/4" onClick={() => setShowPopup(false)}>
                Back
              </Button> */}
              <XMarkIcon
                className="w-4 h-4 cursor-pointer"
                onClick={() => setShowPopup(false)}
              />
            </section>
            <section className="w-full grid grid-cols-2 gap-x-1 gap-y-2 px-14 mt-5">
              <label
                htmlFor="shiftStart"
                className="flex w-full h-full items-center"
              >
                Shift Start
              </label>
              <Input
                type="time"
                name="start"
                id="shiftStart"
                disabled={isHoliday}
                value={shiftData.start}
                onChange={inputHandler}
              />
              <label
                htmlFor="shiftStop"
                className="flex w-full h-full items-center"
              >
                Shift Stop
              </label>
              <Input
                type="time"
                name="stop"
                id="shiftStop"
                disabled={isHoliday}
                value={shiftData.stop}
                onChange={inputHandler}
              />
            </section>
            <section className="flex py-5 justify-evenly">
              <Button
                color="red"
                className="w-1/4"
                onClick={() => {
                  handleDaysHoliday(fullDate, isWeekHoliday);
                  setShowPopup(!showPopup);
                }}
              >
                {`Set ${isHoliday ? 'Not ' : ''}Holiday`}
              </Button>
              <Button
                className="w-1/4"
                disabled={!shiftData?.start || !shiftData?.stop}
                onClick={() => {
                  handleCustomShift('add', shiftData);
                  setShowPopup(!showPopup);
                }}
              >
                Set Shift
              </Button>
              <Button
                color="orange"
                className="w-1/4"
                disabled={!customShift?.start || !customShift?.stop}
                onClick={() => {
                  handleCustomShift('remove', shiftData);
                  setShowPopup(!showPopup);
                }}
              >
                Remove Shift
              </Button>
            </section>
          </div>
        </div>
      )}
    </div>
  );
};

const Calendar = () => {
  const todayDate = new Date();

  const [year, setYear] = useState(todayDate.getFullYear());
  const [month, setMonth] = useState(todayDate.getMonth());
  const [calendar, setCalendar] = useState([]);
  const [holidaysData, setHolidaysData] = useState({});
  const [customShiftsData, setCustomShiftsData] = useState([]);

  const [manageHoliday] = useManageHolidayMutation();
  const [manageShift] = useManageShiftMutation();

  const isLeapYear = new Date(`${year}`).getFullYear() % 4 === 0;

  const {
    defaults: { defaultParam },
  } = useContext(Store);

  const months = [
    { name: 'January', days: 31 },
    { name: 'February', days: isLeapYear ? 29 : 28 },
    { name: 'March', days: 31 },
    { name: 'April', days: 30 },
    { name: 'May', days: 31 },
    { name: 'June', days: 30 },
    { name: 'July', days: 31 },
    { name: 'August', days: 31 },
    { name: 'September', days: 30 },
    { name: 'October', days: 31 },
    { name: 'November', days: 30 },
    { name: 'December', days: 31 },
  ];

  const weeks = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];

  const years = [];

  for (let i = 2020; i <= todayDate.getFullYear(); i++) {
    years.push(i);
  }

  useEffect(() => {
    if (defaultParam?.holidays) {
      setHolidaysData(defaultParam?.holidays);
    }
    if (defaultParam?.customShiftTimings?.length > 0) {
      setCustomShiftsData(defaultParam?.customShiftTimings);
    } else {
      setCustomShiftsData([]);
    }
  }, [defaultParam]);

  useEffect(() => {
    const newMonth = months[month];

    if (newMonth && year) {
      const temp = [...Array(newMonth.days)].map((t, day) => {
        return new Date(`${year}-${newMonth.name}-${day + 1}`);
      });

      let tempArr = [];

      const startDay = temp[0].getDay();

      weeks.forEach((week, wIdx) => {
        if (wIdx < startDay) {
          tempArr.push(null);
        }
      });

      temp.forEach((date) => {
        tempArr.push(date);
      });

      setCalendar(tempArr);
    }
    // eslint-disable-next-line
  }, [month, year]);

  const handleDaysHoliday = async (day, isWeekHoliday) => {
    // check is it is a week holiday
    if (isWeekHoliday) {
      if (holidaysData?.excluded?.includes(day)) {
        // if exists remove from excluded
        await manageHoliday({
          query: 'remove',
          data: {
            exclude: day,
          },
        }).unwrap();
      } else {
        // else add to excluded
        await manageHoliday({
          query: 'add',
          data: {
            exclude: day,
          },
        }).unwrap();
      }
      return;
    }
    // if not week holiday check if it is day holiday
    if (holidaysData?.days?.includes(day)) {
      // if exists remove from days
      await manageHoliday({ query: 'remove', data: { day } }).unwrap();
    } else {
      // else add to days
      await manageHoliday({ query: 'add', data: { day } }).unwrap();
    }
  };

  const handleWeeksHoliday = async (week) => {
    if (holidaysData?.weeks?.includes(week)) {
      // if exists remove from weeks
      await manageHoliday({ query: 'remove', data: { week } }).unwrap();
    } else {
      // else add to weeks
      await manageHoliday({ query: 'add', data: { week } }).unwrap();
    }
  };

  const handleCustomShift = async (condition, shiftData) => {
    await manageShift({ query: condition, data: shiftData }).unwrap();
  };

  return (
    <div className="w-full">
      <div className="w-full rounded-new overflow- ">
        <div className="w-full flex items-center gap-x-10 px-10 py-3 bg-[#EBF3FF] border-b rounded-t-new">
          <span
            onClick={() =>
              setYear((prev) => {
                if (prev === years[0]) return years[0];
                return +prev - 1;
              })
            }
          >
            <ChevronDoubleLeftIcon className="h-5 w-5 hover:cursor-pointer" />
          </span>
          <span
            onClick={() =>
              setMonth((prev) => {
                if (prev === 0) return 0;
                return +prev - 1;
              })
            }
          >
            <ChevronLeftIcon className="h-5 w-5 hover:cursor-pointer" />
          </span>
          <Select
            value={month}
            onChange={(e) => setMonth(+e.target.value)}
            options={months.map((month, mIdx) => ({
              name: month.name,
              value: mIdx,
            }))}
            className="shadow-none"
          />
          <Select
            value={year}
            onChange={(e) => setYear(+e.target.value)}
            options={years.map((year) => ({ name: year, value: year }))}
            className="shadow-none"
          />
          <span
            onClick={() =>
              setMonth((prev) => {
                if (prev === 11) return 11;
                return +prev + 1;
              })
            }
          >
            <ChevronRightIcon className="h-5 w-5 hover:cursor-pointer" />
          </span>
          <span
            onClick={() =>
              setYear((prev) => {
                if (prev === years[years?.length - 1])
                  return years[years?.length - 1];
                return +prev + 1;
              })
            }
          >
            <ChevronDoubleRightIcon className="h-5 w-5 hover:cursor-pointer" />
          </span>
        </div>

        <div className="w-full grid grid-cols-7 bg-white rounded-b-new">
          {weeks.map((week) => {
            const isWeekHoliday = holidaysData?.weeks?.includes(week);

            return (
              <p
                key={week}
                className={`text-center py-3 font-bold hover:cursor-pointer hover:bg-blue-light  ${
                  isWeekHoliday ? 'text-[#ED4040] bg-[#EAECF0]' : 'bg-[#EAECF0]'
                }`}
                onClick={() => handleWeeksHoliday(week)}
              >
                {week.slice(0, 3)}
              </p>
            );
          })}

          {calendar.map((date, dIdx) => {
            if (!date) {
              return <p key={dIdx}></p>;
            }
            const isTodayDate =
              date.getDate() === todayDate.getDate() &&
              date.getMonth() === todayDate.getMonth() &&
              date.getFullYear() === todayDate.getFullYear();

            const fullDate = `${year}-${month + 1 < 10 ? 0 : ''}${month + 1}-${
              date.getDate() < 10 ? 0 : ''
            }${date.getDate()}`;

            const isExcluded = holidaysData?.excluded?.includes(fullDate);

            const week = weeks[date.getDay()];

            const isWeekHoliday = holidaysData?.weeks?.includes(week);

            const isHoliday =
              holidaysData?.days?.includes(fullDate) ||
              (isWeekHoliday && !isExcluded);

            const customShift =
              customShiftsData?.find((item) => item.date === fullDate) || {};

            return (
              <DatePopup
                key={dIdx}
                date={date}
                fullDate={fullDate}
                isHoliday={isHoliday}
                isTodayDate={isTodayDate}
                isWeekHoliday={isWeekHoliday}
                handleDaysHoliday={handleDaysHoliday}
                handleCustomShift={handleCustomShift}
                customShift={customShift}
                isSunday={week === 'Sunday'} // Pass the isSunday prop
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Calendar;
