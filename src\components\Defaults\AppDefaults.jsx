import { useEffect, useState } from 'react';
import {
  <PERSON>Bar<PERSON>hart,
  <PERSON>Bell,
  FiCheckSquare,
  FiClock,
  FiDollarSign,
  FiFileText,
  FiFolder,
  FiGitBranch,
  FiLink,
  FiMenu,
  FiPackage,
  FiSave,
  FiSend,
  FiSettings,
  FiTag,
  FiX,
} from 'react-icons/fi';
import { useSearchParams } from 'react-router-dom';
import Button from '../global/components/Button';
import DepartmentFlow from './DepartmentFlow';
import HeatMapThresholds from './HeatMapThresholds';
import IdealProductionTimes from './IdealProductionTime';
import InventoryDefaults from './InventoryDefaults';
import MandatoryFields from './MandatoryFields';
import NotificationDefaults from './NotificationDefaults';
import OutsourceDefaults from './OutsourceDefaults';
import PartDefaults from './PartDefaults';
import PDFOrderDefaults from './PDFOrderDefaults';
import PrefixIds from './PrefixIds/PrefixIds';
import ProjectDefaults from './ProjectDefaults';
import RoundOffValues from './RoundOffValues';
import ThridPartIntegration from './ThridPartIntegration';
import WhatsAppConfiguration from './WhatsAppConfiguration';

const AppDefaults = ({
  isLoading,
  saveHandler,
  defaults,
  setDefaults,
  tabObjects,
  setTabObjects,
  selectedTab,
  setSelectedTab,
  allDashboard,
  defaultParam,
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    const tabId = searchParams.get('tab');
    if (tabId) {
      const tab = tabObjects.find((t) => t.id === parseInt(tabId));
      if (tab) {
        handleTabChange(tab);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getTabIcon = (id) => {
    const icons = {
      1: FiBarChart,
      2: FiClock,
      3: FiFolder,
      4: FiTag,
      5: FiTag,
      7: FiBell,
      8: FiGitBranch,
      9: FiPackage,
      10: FiFileText,
      11: FiLink,
      12: FiSettings,
      13: FiSend,
      14: FiCheckSquare,
      15: FiDollarSign,
    };
    return icons[id] || FiSettings;
  };

  const handleTabChange = (tab) => {
    const updatedTabs = tabObjects.map((obj) => ({
      ...obj,
      active: obj.id === tab.id,
    }));
    setTabObjects(updatedTabs);
    setSelectedTab(tab);
    setSidebarOpen(false);
    setSearchParams({ tab: tab.id.toString() });
  };

  const shouldShowSaveButton = ![4, 8, 10, 11, 13, 14, 15].includes(
    selectedTab.id
  );

  return (
    <div className="h-[80vh] flex bg-gray-50 rounded-lg overflow-hidden">
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <div
        className={`
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 fixed lg:relative inset-y-0 left-0 w-64 bg-white border-r border-gray-200 transition-transform duration-300 ease-in-out flex flex-col
      `}
      >
        <button
          onClick={() => setSidebarOpen(false)}
          className="lg:hidden p-1 rounded-md hover:bg-gray-100"
        >
          <FiX className="w-5 h-5 text-gray-500" />
        </button>

        <nav className="flex-1 overflow-y-auto py-4">
          <ul className="space-y-1 px-3">
            {tabObjects.map((tab) => {
              const IconComponent = getTabIcon(tab.id);
              return (
                <li key={tab.id}>
                  <button
                    onClick={() => handleTabChange(tab)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 text-left
                      ${
                        tab.active
                          ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                          : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                      }
                    `}
                  >
                    <IconComponent
                      className={`w-5 h-5 flex-shrink-0 ${
                        tab.active ? 'text-blue-600' : 'text-gray-400'
                      }`}
                    />
                    <span className="truncate">
                      {tab.name.replace(/\xA0/g, ' ')}
                    </span>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        {shouldShowSaveButton && (
          <div className="p-4 border-t border-gray-200 lg:hidden">
            <Button
              isLoading={isLoading}
              onClick={saveHandler}
              size="sm"
              className="w-full flex items-center justify-center gap-2 px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
            >
              <FiSave className="w-4 h-4" />
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        )}
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col min-w-0">
        <header className="bg-white border-b border-gray-200 px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-md hover:bg-gray-100"
            >
              <FiMenu className="w-5 h-5 text-gray-500" />
            </button>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {selectedTab.name}
              </h1>
            </div>
          </div>

          {/* Save button (desktop) */}
          {shouldShowSaveButton && (
            <div className="hidden lg:block">
              <Button
                isLoading={isLoading}
                onClick={saveHandler}
                size="sm"
                className="flex items-center gap-2 px-6 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
              >
                <FiSave className="w-4 h-4" />
                <span className="hidden sm:inline">Save Changes</span>
                <span className="sm:hidden">Save</span>
              </Button>
            </div>
          )}
        </header>

        {/* Content */}
        <main className="flex-1 overflow-y-auto p-4 bg-white">
          {selectedTab.id === 1 && (
            <HeatMapThresholds defaults={defaults} setDefaults={setDefaults} />
          )}
          {selectedTab.id === 2 && (
            <IdealProductionTimes
              defaults={defaults}
              setDefaults={setDefaults}
            />
          )}
          {selectedTab.id === 3 && (
            <ProjectDefaults
              defaults={defaults}
              setDefaults={setDefaults}
              allDashboard={allDashboard}
              defaultParam={defaultParam}
            />
          )}
          {selectedTab.id === 4 && <PrefixIds />}
          {selectedTab.id === 5 && (
            <PartDefaults defaults={defaults} setDefaults={setDefaults} />
          )}
          {selectedTab.id === 7 && (
            <NotificationDefaults
              defaults={defaults}
              setDefaults={setDefaults}
            />
          )}
          {selectedTab.id === 8 && (
            <DepartmentFlow defaults={defaults} setDefaults={setDefaults} />
          )}
          {selectedTab.id === 9 && (
            <InventoryDefaults defaults={defaults} setDefaults={setDefaults} />
          )}
          {selectedTab.id === 10 && (
            <PDFOrderDefaults defaults={defaults} setDefaults={setDefaults} />
          )}
          {selectedTab.id === 11 && <ThridPartIntegration />}
          {selectedTab.id === 12 && (
            <OutsourceDefaults defaults={defaults} setDefaults={setDefaults} />
          )}
          {selectedTab.id === 13 && <WhatsAppConfiguration />}
          {selectedTab.id === 14 && <MandatoryFields />}
          {selectedTab.id === 15 && <RoundOffValues />}
        </main>
      </div>
    </div>
  );
};

export default AppDefaults;
