import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useLazyGetAllPartsForOptionsQuery } from '../../../slices/partApiSlice';
import Input from '../../global/components/Input';
import Modal from '../../global/components/Modal';
import MultiSelect from '../../global/components/MultiSelect';
import Table from '../../global/components/Table';

export default function RMAddModal({
  isRMModalOpen,
  setIsRMModalOpen,
  setRmTableData,
  rmTableData,
}) {
  const [allInputs, setAllInputs] = useState({
    selectedInventoryType: '',
    dropDownValue: [],
    data: [],
  });

  const [getAllParts] = useLazyGetAllPartsForOptionsQuery();

  const handleAllInputsChange = (name, value) => {
    setAllInputs((prev) => ({ ...prev, [name]: value }));
  };

  const handlePartChange = (e) => {
    const tmp = e.target.value?.map((data) => {
      return { ...data, qty: null };
    });

    handleAllInputsChange('dropDownValue', tmp);
  };

  const handelQtyChange = (qty, idx) => {
    const tmp = allInputs.dropDownValue.map((data, index) => {
      if (index === idx) {
        return { ...data, qty };
      } else {
        return data;
      }
    });
    handleAllInputsChange('dropDownValue', tmp);
  };

  const handleProductAdd = () => {
    for (let i = 0; i < allInputs.dropDownValue.length; i++) {
      if (!allInputs.dropDownValue[i]?.qty) {
        return toast.error('Quantity is required for all selected items');
      }
    }

    if (allInputs.dropDownValue.length === 0) {
      return toast.error('At least one item must be selected');
    }

    for (let i = 0; i < allInputs.dropDownValue.length; i++) {
      const selectedItem = allInputs.dropDownValue[i];

      const itemExists = rmTableData.some((existingItem) => {
        return existingItem.name === selectedItem.label;
      });

      if (itemExists) {
        return toast.error(
          `Item '${selectedItem.label}' already exists in the list`
        );
      }
    }

    const updatedTableData = [...rmTableData];

    for (let i = 0; i < allInputs.dropDownValue.length; i++) {
      const selectedItem = allInputs.dropDownValue[i];

      const newItem = {
        name: selectedItem.label,
        _id: selectedItem.value,
        qty: parseInt(selectedItem.qty),
      };

      updatedTableData.push(newItem);
    }

    setRmTableData(updatedTableData);

    setIsRMModalOpen(false);
    setAllInputs({
      selectedInventoryType: '',
      dropDownValue: [],
      data: [],
    });
  };

  useEffect(() => {
    if (isRMModalOpen) {
      (async () => {
        const res = await getAllParts().unwrap();
        const tempAllParts = res.filter(
          (el) => el?.category?.name === 'Raw Materials'
        );

        handleAllInputsChange('data', tempAllParts);
      })();
    }
  }, [getAllParts]); //eslint-disable-line

  return (
    isRMModalOpen && (
      <Modal
        onCloseModal={() => setIsRMModalOpen(false)}
        title="Add Raw Material"
        onSubmit={handleProductAdd}
      >
        {() => {
          return (
            <div className="w-full">
              <div className="flex items-center gap-2">
                <div className="w-full">
                  <label className="block font-normal mb-1 text-sm">
                    Select Raw Material
                  </label>
                  <MultiSelect
                    placeholder={`Select Raw Material`}
                    onChange={(e) => {
                      handlePartChange(e);
                    }}
                    value={allInputs.dropDownValue}
                    closeMenuOnSelect
                    className="w-60 text-ellipsis"
                    options={[
                      ...(allInputs.data?.map((e) => {
                        return {
                          value: e?._id,
                          label: e?.name,
                        };
                      }) || []),
                    ]}
                  />
                </div>
              </div>
              <Table className="!mt-3">
                <Table.Head>
                  <Table.Row>
                    <Table.Th>Item</Table.Th>
                    <Table.Th>Quantity</Table.Th>
                  </Table.Row>
                </Table.Head>
                <Table.Body>
                  {allInputs.dropDownValue?.map((item, idx) => {
                    return (
                      <Table.Row key={item?.label}>
                        <Table.Td>{item?.label}</Table.Td>
                        <Table.Td className="!max-w-[6rem]">
                          <Input
                            type="number"
                            placeholder="Enter Quantity"
                            value={item?.qty}
                            onChange={(e) => {
                              handelQtyChange(e.target.value, idx);
                            }}
                          />
                        </Table.Td>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </div>
          );
        }}
      </Modal>
    )
  );
}
