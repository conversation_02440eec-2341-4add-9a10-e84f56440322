import { Checkbox, Typography } from 'antd';
import { useGetAllStoresForOptionsQuery } from '../../../../slices/storeApiSlice';
const { Title, Text } = Typography;

const StoreAccess = ({ setInputData, inputData }) => {
  const { data: stores } = useGetAllStoresForOptionsQuery({
    userAccess: false,
  });

  // Compute selectAll based on inputData and stores
  const allStoreIds = stores?.stores?.map((store) => store._id) || [];
  const selectAll =
    allStoreIds.length > 0 &&
    allStoreIds.every((id) => inputData?.storeAccess?.includes(id));

  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setInputData((prev) => ({
        ...prev,
        storeAccess: allStoreIds,
      }));
    } else {
      setInputData((prev) => ({
        ...prev,
        storeAccess: [],
      }));
    }
  };

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <Title level={4} className="!mb-2">
          Access Permissions
        </Title>
        <Text type="secondary">
          {' '}
          Select the stores that you wish to grant access to.
        </Text>
      </div>

      {/* Select All Checkbox */}
      <div className="mb-4 flex items-center justify-end">
        <Checkbox
          id="selectAll"
          checked={selectAll}
          onChange={handleSelectAll}
          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
        <label
          htmlFor="selectAll"
          className="ml-3 text-sm font-medium text-gray-700 cursor-pointer"
        >
          Select All Stores
        </label>
      </div>

      {/* Stores List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {stores?.stores?.map((store) => (
          <div
            key={store._id}
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Checkbox
              id={store._id}
              checked={inputData?.storeAccess?.includes(store._id)}
              onChange={(e) => {
                setInputData((prev) => {
                  const newAccess = e.target.checked
                    ? [...(prev?.storeAccess || []), store._id]
                    : prev?.storeAccess?.filter((id) => id !== store._id);
                  return {
                    ...prev,
                    storeAccess: newAccess,
                  };
                });
              }}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label
              htmlFor={store._id}
              className="ml-3 text-sm font-medium text-gray-700 cursor-pointer"
            >
              {store.name}
            </label>
          </div>
        ))}
      </div>

      {/* Selected Count */}
      <div className="mt-6 text-sm text-gray-600">
        Selected Stores: {inputData?.storeAccess?.length || 0} of{' '}
        {stores?.stores?.length || 0}
      </div>
    </div>
  );
};

export default StoreAccess;
