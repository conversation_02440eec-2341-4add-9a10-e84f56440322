import { useRef, useState } from 'react';

const DragAndDrop = ({ onChange, accept, multiple, className }) => {
  const [dragActive, setDragActive] = useState(false);

  const inputRef = useRef();

  // handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // triggers when file is dropped
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0] && !multiple) {
      onChange(e.dataTransfer.files[0]);
    } else if (e.dataTransfer.files && multiple) {
      onChange(e.dataTransfer.files);
    }
  };

  // triggers when file is selected with click
  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0] && !multiple) {
      onChange(e.target.files[0]);
    } else if (e.target.files && multiple) {
      onChange(e.target.files);
    }
    e.target.value = '';
  };

  // triggers the input when the button is clicked
  const onClick = () => {
    inputRef.current.click();
  };

  return (
    <div
      onDragEnter={handleDrag}
      onClick={onClick}
      className={`relative min-w-[100px] h-20 rounded-xl border-2 hover:cursor-pointer border-black/30 border-dashed flex justify-start px-4 items-center py-8 ${className}`}
    >
      <input
        ref={inputRef}
        type="file"
        className="hidden"
        accept={accept}
        onChange={handleChange}
        multiple={multiple}
      />
      <div className="flex  justify-center w-full">
        <div className="flex gap-x-2 items-center ">
          {/* <div> {svg && svg}</div> */}
          <div className="flex flex-col justify-center items-center text-blue-600 text-decoration-line: underline">
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-6 h-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M18.375 12.739l-7.693 7.693a4.5 4.5 0 01-6.364-6.364l10.94-10.94A3 3 0 1119.5 7.372L8.552 18.32m.009-.01l-.01.01m5.699-9.941l-7.81 7.81a1.5 1.5 0 002.112 2.13"
                />
              </svg>
            </div>
          </div>

          <div className=" text-blue-600 font-semibold">Attach File</div>
        </div>
      </div>
      {dragActive && (
        <div
          className="absolute h-full w-full top-0 left-0"
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        ></div>
      )}
    </div>
  );
};

export default DragAndDrop;
