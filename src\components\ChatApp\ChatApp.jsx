import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import { format } from 'date-fns';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';

import { Document, Page } from 'react-pdf';
import { toast } from 'react-toastify';
import {
  useDeleteChatHistoryMutation,
  useGetChatHistoryQuery,
  useSendMessagesMutation,
  useGetAllChatsHistoryForUserQuery,
} from '../../slices/chatApiSlice';
import { useCreateNotificationMutation } from '../../slices/notificationApiSlice';
import { useLazyGetAllUsersQuery } from '../../slices/userApiSlice';
import { DepartmentStore } from '../../store/DepartmentStore';
import MediaViewer from '../global/components/MediaViewer';
import { FaFilePdf } from 'react-icons/fa';

//const socket = io('http://localhost:5000');
import { Store } from '../../store/Store';
import { FiSend } from 'react-icons/fi';

const ChatApp = ({ visible, onClose, setOpenChatInfo, openchatInfo }) => {
  const { state: { user } = {}, socket } = useContext(Store);
  const [getAllUsers, { data }] = useLazyGetAllUsersQuery();

  // below is my changes
  const [usersInfo, setUsersInfo] = useState([]);
  const [openChat, setOpenChat] = useState(''); //eslint-disable-line
  const { data: userAllChats } = useGetAllChatsHistoryForUserQuery();

  const inputRef = useRef();
  // above is my changes

  const [selectedUser, setSelectedUser] = useState(null);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [sendMessagesMutation] = useSendMessagesMutation();
  const [file, setFile] = useState([]);
  const [showDragAndDrop, setShowDragAndDrop] = useState(false);

  const [viewMediaData, setViewMediaData] = useState();
  const chatContainerRef = useRef(null);
  const [createNotification] = useCreateNotificationMutation();
  const { senderId } = useContext(DepartmentStore);
  const [deleteChatHistoryMutation] = useDeleteChatHistoryMutation();
  const { data: chatHistory, refetch } = useGetChatHistoryQuery(
    selectedUser ? { senderId: user._id, receiverId: selectedUser._id } : null,
    { skip: !selectedUser }
  );

  const filteredData = useMemo(() => {
    return data ? data.filter((userData) => userData.name !== user.name) : [];
  }, [data, user.name]);

  const onClick = () => {
    inputRef?.current?.click();
  };

  useEffect(() => {
    if (senderId && filteredData) {
      const x = filteredData.find((user) => senderId === user._id);
      setSelectedUser(x);
    }
  }, [filteredData, senderId]);

  useEffect(() => {
    if (chatHistory) {
      setMessages(chatHistory);
    }
    scrollToBottom();
  }, [chatHistory]);

  useEffect(() => {
    if (!socket) return;
    getAllUsers({}, false);
    socket.emit('join', { userId: user._id });
    socket.on('receiveMessage', (newMessage) => {
      setMessages((prevMessages) => [...prevMessages, newMessage]);
      // Display a notification
      /*   if (newMessage.senderId !== user._id) {
        const sender = data?.find((user) => user._id === newMessage.senderId);
        console.log(sender);
        if (sender) {
          toast.info(`New message from ${sender.name}: ${newMessage.message}`);
        }
      } */
      scrollToBottom();
    });

    return () => {
      socket.off('receiveMessage');
    };
  }, [getAllUsers, user._id, socket]);

  const sendMessage = async (message) => {
    socket.emit('sendMessage', message);
  };

  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0] && !true) {
      changeHandler(e.target.files[0]);
    } else if (e.target.files && true) {
      changeHandler(e.target.files);
    }
    e.target.value = '';
  };

  const handleSendMessage = async () => {
    if (selectedUser && (message.trim() !== '' || file.length > 0)) {
      if (messages.length >= 10000) {
        // Display a warning to the user
        toast.warning(
          'Chat history has reached the limit. Export and clear the chat history to continue.'
        );
        return;
      }
      const newMessage = {
        senderId: user._id,
        receiverId: selectedUser._id,
        message,
        media: file,
        createdAt: new Date().toISOString(),
      };

      try {
        await sendMessage(newMessage);
        await sendMessagesMutation(newMessage);
        setMessage('');
        setFile([]);
        await createNotification({
          data: {
            createdAt: new Date(),
            forUser: selectedUser._id, // User to whom the message is sent
            data: {
              messageNotification: `${user.name} sends a message`,
              senderId: user._id,
            }, // Notification message
            type: 'chatnotification',
          },
        });
      } catch (error) {
        toast.error('Error sending message');
      }
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
      toggleDragAndDrop();
    }
  };

  const handleMessageChange = (e) => {
    setMessage(e.target.value);
  };

  const handleUserChange = (e) => {
    const selectedUserId = e;
    const selectedUserObj = data?.find((user) => user?._id === selectedUserId);
    setSelectedUser(selectedUserObj);
    setMessages([]);

    if (selectedUserObj) {
      refetch().catch(() => {
        toast.error('Error refetching');
      });
    }
    setOpenChatInfo(!openchatInfo);
  };

  const changeHandler = (e) => {
    const newFiles = [];
    const filesArray = Array.from(e); // Convert FileList to array
    const totalFiles = file.length + filesArray.length; // Current files + new files

    if (totalFiles > 4) {
      toast.warning('You can only upload up to 4 files.');
      return;
    }

    for (let i = 0; i < filesArray.length; i++) {
      if (filesArray[i].size > 10 * 1024 * 1024) {
        toast.warning('File size exceeds 10 MB. Please upload a smaller file.');
        continue;
      }
      let fname = filesArray[i].name;
      let ftype = filesArray[i].type;

      const fr = new FileReader();
      fr.readAsDataURL(filesArray[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;

        let data = {
          name: fname,
          type: ftype,
          data: url,
        };
        newFiles.push(data);

        setFile((prevFile) => [...prevFile, ...newFiles]);
      });
    }
  };

  const handleExportChat = () => {
    if (messages.length === 0) {
      toast.warning('No messages to export');
      return;
    }

    const formattedMessages = messages
      .map((msg) => {
        const date = new Date(msg.createdAt);
        const formattedDate = format(date, 'yyyy-MM-dd hh:mm a');
        return `${msg.senderId === user._id ? 'You' : selectedUser.name} [${formattedDate}]: ${msg.message}`;
      })
      .join('\n');

    const blob = new Blob([formattedMessages], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat_with_${selectedUser.name}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleDeleteChatHistory = async () => {
    if (messages.length === 0) {
      toast.warning('No messages to delete');
      return;
    }

    if (selectedUser) {
      // Show confirmation dialog
      const confirmed = window.confirm(
        'Are you sure you want to delete the chat history?'
      );

      // If user confirms deletion
      if (confirmed) {
        try {
          await deleteChatHistoryMutation({
            senderId: user._id,
            receiverId: selectedUser._id,
          }).unwrap();
          setMessages([]);
          toast.success('Chat history deleted successfully');
        } catch (error) {
          toast.error('Error deleting chat history');
        }
      }
    }
  };

  const handleRemoveFile = (name) => {
    setFile((prevFiles) => prevFiles.filter((file) => file.name !== name));
  };

  const toggleDragAndDrop = () => {
    setShowDragAndDrop(!showDragAndDrop);
  };

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  };
  const [numPages, setNumPages] = useState(1); //eslint-disable-line

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
  };

  useEffect(() => {
    setUsersInfo(filteredData);
  }, [data, filteredData]);

  const handleSearchValue = (searchValue) => {
    if (!searchValue) {
      setUsersInfo(filteredData); // Reset to full list if search is cleared
      return;
    }

    const searchedUser = filteredData?.filter((item) =>
      item?.name?.toLowerCase().startsWith(searchValue.toLowerCase())
    );

    setUsersInfo(searchedUser);
  };

  return (
    <>
      {/* {visible && (
        <div className="fixed pb-4 top-0 right-0 w-[450px] h-screen border border-gray-300 rounded-t-lg bg-white shadow-lg flex flex-col z-50">
          <div className="bg-[#483285] text-white p-3 rounded-t-lg font-bold text-center flex justify-between items-center">
            <div className="flex items-center">
              <div className="mr-2">
                <IoMdChatboxes /> */}

      {visible && openchatInfo && (
        <div className="fixed pb-4 right-2 top-[60px] w-[460px] h-[90vh] border transition-opacity duration-500 border-gray-300 rounded-3xl bg-gray-50 shadow-lg flex flex-col z-50">
          <div className="flex items-center justify-end p-4   bg-gray-50 rounded-t-3xl">
            <button
              onClick={() => {
                onClose();
                setOpenChatInfo(!openchatInfo);
              }}
              title="Close"
              className="focus:outline-none"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-6 h-6 text-gray-700"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="relative px-5 mt-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-8 h-8 text-gray-400 absolute top-1/2 transform -translate-y-1/2 left-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
              />
            </svg>

            <input
              type="text"
              placeholder="Search message..."
              className="w-full py-5 pl-12 p-2 rounded-xl   focus:outline-none focus:ring-0 focus:ring-blue-500 focus:border-transparent"
              onChange={(e) => handleSearchValue(e.target.value)}
            />
          </div>

          <div className="flex-1 overflow-y-auto mt-4 px-5">
            {usersInfo?.map((userInfo) => {
              const allChatsUser = userAllChats?.find((chat) =>
                chat?.participants?.includes(userInfo._id)
              );

              const userLastMessage =
                allChatsUser?.messages[allChatsUser?.messages?.length - 1];

              let formatedDate = '';
              allChatsUser?.messages?.forEach((messageInfo) => {
                if (messageInfo?.createdAt)
                  if (!isNaN(new Date(messageInfo?.createdAt).getDate())) {
                    formatedDate = format(
                      new Date(messageInfo?.createdAt),
                      'hh:mm a'
                    );
                  }
              });

              return (
                <div
                  key={userInfo._id}
                  className="mt-5 flex items-center p-3 mb-2 bg-white rounded-xl shadow-sm hover:bg-gray-100 cursor-pointer"
                  onClick={() => handleUserChange(userInfo._id)}
                >
                  <div className="flex justify-between w-full">
                    <div className="flex">
                      <div className="flex items-center justify-center h-10 w-10 rounded-full bg-indigo-500 text-white flex-shrink-0">
                        {userInfo.name.substring(0, 1)}
                      </div>
                      <div className="ml-3 mb-3">
                        <div className="text-md font-semibold text-black">
                          {userInfo.name}
                        </div>
                        <div className="text-xs font-normal text-gray-500">
                          {userLastMessage?.message &&
                          userLastMessage?.message?.length > 100
                            ? userLastMessage?.message?.substring(0, 150) +
                              '...'
                            : userLastMessage?.message}
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">{formatedDate}</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
      {visible && !openchatInfo && (
        <div className="fixed   right-2 top-[60px] w-[460px] h-[90vh] border transition-opacity duration-500  border-gray-300 !rounded-3xl bg-gray-100 shadow-lg flex flex-col z-50">
          <div className=" text-black h-[5rem] bg-white p-3 !rounded-3xl font-bold text-center flex justify-between items-center">
            <div className="flex">
              {/* <div className="mr-2 mt-2">
                <IoMdChatboxes />
              </div> */}
              <div className="flex gap-3 items-center">
                <div
                  className="relative  group !font-normal"
                  onClick={() => {
                    setUsersInfo(filteredData);
                    setOpenChatInfo(!openchatInfo);
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="size-6 mt-1 cursor-pointer"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
                    />
                  </svg>

                  <span className="absolute top-full !left-[25px] transform -translate-x-1/2 mt-2 w-max px-2 py-1 text-xs text-white bg-black rounded opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                    Go Back
                  </span>
                </div>

                <div className="flex flex-col items-center">
                  <div className="flex gap-2 mt-2">
                    <div className="flex text-1 items-center justify-center  h-8 w-8 rounded-full bg-indigo-300 flex-shrink-0">
                      {selectedUser?.name?.substring(0, 1)}
                    </div>
                    <div className="mr-2 mt-1">{selectedUser?.name}</div>
                  </div>
                </div>
              </div>
              {/* <div className="mr-2">Chat App - {user.name}</div> */}
            </div>

            {/* <div className="flex items-center space-x-7">
              <button
                onClick={handleExportChat}
                className="text-white"
                title="Export Chat"
              >
                <IoIosShare />
              </button>
              <button
                onClick={handleDeleteChatHistory}
                className=" text-white"
                title="Delete Chat History"
              >
                <IoMdTrash />
              </button>
              <button onClick={onClose} className="text-white" title="Close">
                <IoMdClose />
              </button> */}

            <div className="flex items-center space-x-4">
              <div className="relative flex items-center group !font-normal">
                <button onClick={handleExportChat} title="Export Chat">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="size-5 cursor-pointer -mt-1 !text-gray-500"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                    />
                  </svg>
                </button>
                <span className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-max px-2 py-1 text-xs text-white bg-black rounded opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                  Export Chat
                </span>
              </div>
              <div className="relative group !font-normal">
                <button
                  onClick={handleDeleteChatHistory}
                  title="Delete Chat History"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="size-5 cursor-pointer  !text-gray-500"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                    />
                  </svg>
                </button>
                <span className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-max px-2 py-1 text-xs text-white bg-black rounded opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                  Delete Chat
                </span>
              </div>
              <div className="relative group !font-normal">
                <button onClick={onClose} title="Close">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="size-5 cursor-pointer  !text-gray-500"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M6 18 18 6M6 6l12 12"
                    />
                  </svg>
                </button>
                <span className="absolute top-full left-1/4 transform -translate-x-1/2 mt-2 w-max px-2 py-1 text-xs text-white bg-black rounded opacity-0 transition-opacity duration-300 group-hover:opacity-100 !mr-2">
                  Close
                </span>
              </div>
            </div>
          </div>

          {/* <select
            className="p-2 border border-gray-300 w-full rounded-l-lg bg-white text-gray-700 appearance-none"
            onChange={handleUserChange}
            value={selectedUser ? selectedUser._id : ''}
          >
            <option disabled value="">
              Select User
            </option>
            {filteredData?.map((userData) => (
              <option key={userData._id} value={userData._id}>
                {userData.name}
              </option>
            ))}
          </select> */}

          <div
            // className="flex-1 p-3 overflow-y-auto break-words break-all"
            className="flex-1 p-3 bg-gray-100 overflow-y-auto break-words break-all"
            ref={chatContainerRef}
          >
            {selectedUser?.name &&
              (messages?.length > 0 ? (
                messages.map((msg, index) => {
                  let formattedDate = '';
                  if (msg.createdAt) {
                    const date = new Date(msg.createdAt);
                    if (!isNaN(date.getTime())) {
                      formattedDate = format(date, 'hh:mm a');
                    }
                  }

                  return (
                    // <div
                    //   key={index}
                    //   className={`mb-2 ${msg.senderId === user?._id ? 'flex justify-end' : 'flex justify-start'}`}
                    // >
                    <>
                      <div
                        // className={`rounded-lg p-2 max-w-[70%] ${msg.senderId === user?._id ? 'bg-[#5c51ff] text-white' : 'bg-[#f5f4fe] text-gray-900'}`}
                        key={index}
                        className={`mb-2 ${msg.senderId === user?._id ? 'flex justify-end' : 'flex justify-start'}`}
                      >
                        {/* <div className="flex flex-col items-center mb-1">
                          <span>{msg.message}</span>
                          {formattedDate && (
                            <span className="text-xs text-black-500 mr-1">
                              {formattedDate}
                            </span> */}

                        <div
                          className={`mt-7 p-2 max-w-[90%] ${
                            msg.senderId === user?._id
                              ? 'bg-[#5c51ff] text-white rounded-tl-3xl rounded-bl-3xl rounded-tr-3xl rounded-br-sm'
                              : 'bg-white text-black rounded-tl-sm rounded-bl-3xl rounded-tr-3xl rounded-br-3xl'
                          }`}
                        >
                          <div className="flex flex-col mb-1 px-2">
                            <span className="text-sm">{msg?.message}</span>
                          </div>

                          {viewMediaData && (
                            <MediaViewer
                              media={viewMediaData}
                              setMedia={setViewMediaData}
                              onClose={() => setViewMediaData(null)}
                            />
                          )}
                          {/* </div> */}

                          {msg.media &&
                            msg.media.map((mediaItem, i) => (
                              <div key={i} className="mb-2">
                                <div className="media-container">
                                  {mediaItem.type === 'application/pdf' ? (
                                    <div
                                      className="flex flex-col hover:cursor-pointer w-[200px]"
                                      onClick={() =>
                                        setViewMediaData(mediaItem)
                                      }
                                    >
                                      <Document
                                        file={mediaItem.data}
                                        onLoadSuccess={onDocumentLoadSuccess}
                                      >
                                        <Page pageNumber={1} width={200} />
                                      </Document>
                                      <div className="ml-2 text-xs flex flex-row text-gray-500">
                                        <FaFilePdf className="w-7 h-7 mr-2 text-red-500" />
                                        {mediaItem.name}
                                      </div>
                                    </div>
                                  ) : (
                                    <img
                                      src={mediaItem.data}
                                      alt={
                                        mediaItem.name + ' ' + mediaItem.type
                                      }
                                      className="hover:cursor-pointer w-[200px]"
                                      onClick={() =>
                                        setViewMediaData(mediaItem)
                                      }
                                    />
                                  )}
                                </div>
                              </div>
                            ))}
                        </div>
                      </div>
                      {formattedDate && (
                        <div className="flex  flex-col mb-1 px-2">
                          <span
                            className={`text-gray-500 text-xs ${msg.senderId === user?._id ? ' flex justify-end' : ' flex justify-start'} mr-1`}
                          >
                            {formattedDate}
                          </span>
                        </div>
                      )}
                    </>
                  );
                })
              ) : (
                <div className="flex justify-center font-bold">
                  No chats found
                </div>
              ))}
          </div>

          <div>
            {file?.map((img, indx) => {
              return (
                <section key={indx} className="p-2 border rounded-md w-fit">
                  <img
                    src={img.data}
                    className="hover:cursor-pointer w-[50px] "
                  />
                  <p className="text-xs">{img.name}</p>
                  <section className="flex justify-between items-center text-sm mt-2">
                    <span className="px-2">{`Image${indx + 1}`}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveFile(img.name)}
                      className="outline-none text-red-primary hover:text-white hover:bg-red-primary rounded px-2 py-1"
                    >
                      Remove
                    </button>
                  </section>
                </section>
              );
            })}
          </div>

          <div className="!relative flex items-center !rounded-3xl  px-4 py-8 mt-2 bg-white ">
            {/* Message Input Field */}
            <input
              type="text"
              placeholder="Type here..."
              value={message}
              onChange={handleMessageChange}
              onKeyDown={handleKeyDown}
              className="!min-w-[100px] !w-[150px] rounded-full bg-gray-50 flex-1 text-sm border-none p-5 shadow-sm focus:outline-none !focus:ring-0 !focus:border-transparent duration-150 ease-in-out"
            />
            {/* Button to toggle DragAndDrop */}
            {/* <button
              className="p-2  bg-[#483285] text-white rounded-r-lg mr-1"
              onClick={toggleDragAndDrop}
              title="Attach File"
            >
              <IoMdAttach size={20} />
            </button> */}

            <div className="!reative group !font-normal">
              <button
                className="absolute top-[35px] right-14 ml-2 p-3  text-white rounded-full  transition-colors duration-150 ease-in-out"
                // onClick={toggleDragAndDrop}
                onClick={onClick}
                title="Attach File"
              >
                <div
                  className={`w-2 h-2 px-2.5 py-2.5 rounded-xl hover:cursor-pointer flex justify-start items-center`}
                >
                  <input
                    ref={inputRef}
                    type="file"
                    className="hidden outline-none"
                    accept={'image/png, image/jpeg , application/pdf'}
                    onChange={handleChange}
                    multiple={true}
                  />
                  <div className="flex  justify-center w-full">
                    <div className="flex gap-x-2 items-center ">
                      <div className="flex flex-col justify-center items-center text-blue-600 text-decoration-line: underline">
                        <div>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="w-5 h-5 !text-gray-400"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M18.375 12.739l-7.693 7.693a4.5 4.5 0 01-6.364-6.364l10.94-10.94A3 3 0 1119.5 7.372L8.552 18.32m.009-.01l-.01.01m5.699-9.941l-7.81 7.81a1.5 1.5 0 002.112 2.13"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                {/* <IoMdAttach
                className="text-black"
                size={20}
                onClick={changeHandler}
              /> */}
              </button>
              <span className="!absolute top-[80px] right-[25px] transform -translate-x-1/2 mt-2 w-max px-2 py-1 text-xs text-white bg-black rounded opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                Attach File
              </span>
            </div>

            {/* Conditional rendering of DragAndDrop */}
            {/* {showDragAndDrop && (
              <DragAndDrop
                className="h-[50px] w-[100px] text-xs"
                accept="image/png, image/jpeg , application/pdf"
                onChange={(e) => changeHandler(e)}
                multiple={true}
              />
            )} */}

            {/* {showDragAndDrop && (
              <div className="ml-2">
                <DragAndDrop
                  className="h-12 w-24 text-xs"
                  accept="image/png, image/jpeg, application/pdf"
                  onChange={changeHandler}
                  multiple
                />
              </div>
            )} */}

            {/* <button
              title="Send message"
              onClick={() => {
                handleSendMessage();
                toggleDragAndDrop();
              }}
              className="p-2 bg-[#483285] text-white rounded-r-lg"
            >
              <IoIosSend size={'20px'} />
            </button> */}

            {/* Send Button */}
            <div className=" group !font-normal">
              <button
                title="Send message"
                onClick={() => {
                  handleSendMessage();
                  toggleDragAndDrop();
                }}
                className="absolute top-[35px] right-6 ml-2 p-3  duration-150 ease-in-out"
              >
                <FiSend size={20} className="!text-gray-400" />
              </button>
              <span className="!absolute top-[80px] right-[2px] transform -translate-x-1/2 mt-2 w-max px-2 py-1 text-xs text-white bg-black rounded opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                Send Message
              </span>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChatApp;
